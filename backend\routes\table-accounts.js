// Backend route for tables management
// مسارات إدارة الطاولات
const express = require('express');
const router = express.Router();
const { authenticateToken } = require('../middleware/auth');
const Table = require('../models/Table');
const Order = require('../models/Order');
const User = require('../models/User');

/**
 * @route   GET /api/table-accounts
 * @desc    Get all table accounts with real-time data
 * @access  Private
 */
router.get('/', authenticateToken, async (req, res) => {
  try {
    console.log('🔄 GET /api/table-accounts - بدء جلب حسابات الطاولات');
    
    // جلب جميع الطاولات مع معلومات الطلبات النشطة
    const tables = await Table.find({ isActive: true })
      .populate('assignedWaiter', 'name username')
      .populate({
        path: 'currentOrder',
        populate: {
          path: 'items.product',
          select: 'name price'
        }
      })
      .sort({ section: 1, number: 1 });

    console.log(`📊 تم العثور على ${tables.length} طاولة نشطة`);

    // جلب جميع الطلبات لحساب إجمالي المبيعات وعدد الطلبات للطاولات
    const allOrders = await Order.find({})
    .populate('table', 'number')
    .select('table tableNumber waiterName items totalPrice pricing totals orderNumber createdAt status');

    console.log(`📋 تم العثور على ${allOrders.length} طلب إجمالي`);

    // تحويل البيانات لتتناسب مع واجهة المستخدم الحالية
    const tableAccounts = [];

    for (const table of tables) {
      let totalAmount = 0;
      let orders = [];
      let customer = null;
      let openTime = null;
      let closeTime = null;

      // جلب جميع الطلبات الخاصة بهذه الطاولة (للإحصائيات)
      const allTableOrders = allOrders.filter(order => {
        // محاولة مطابقة بطرق مختلفة
        return (order.table && order.table.number === table.number) || 
               (order.tableNumber === table.number) ||
               (order.tableNumber === table.number.toString());
      });

      console.log(`📊 طاولة ${table.number}: وجد ${allTableOrders.length} طلب`);

      // جلب الطلبات النشطة فقط للعرض
      const activeTableOrders = allTableOrders.filter(order =>
        ['pending', 'preparing', 'ready'].includes(order.status)
      );

      // حساب إجمالي المبيعات من جميع الطلبات (المكتملة والنشطة)
      const totalSales = allTableOrders.reduce((sum, order) => {
        const orderTotal = order.totals?.total || order.pricing?.total || order.totalPrice || 0;
        return sum + orderTotal;
      }, 0);

      console.log(`💰 طاولة ${table.number}: إجمالي المبيعات ${totalSales} ج.م`);

      // حساب المبلغ الحالي من الطلبات النشطة فقط
      totalAmount = activeTableOrders.reduce((sum, order) => {
        const orderTotal = order.totals?.total || order.pricing?.total || order.totalPrice || 0;
        return sum + orderTotal;
      }, 0);

      // إذا كانت الطاولة مشغولة، جلب تفاصيل الطلب
      if (table.status === 'occupied' && table.currentOrder) {
        const order = table.currentOrder;
        openTime = order.timing.orderTime;
        
        // تفاصيل العميل
        customer = {
          name: order.customer.name,
          phone: order.customer.phone || 'غير محدد'
        };

        // تفاصيل الطلبات النشطة فقط للعرض
        orders = activeTableOrders.map(order => ({
          _id: order._id,
          orderNumber: order.orderNumber,
          items: order.items,
          totalPrice: order.pricing?.total || order.totalPrice || 0,
          createdAt: order.createdAt,
          status: order.status
        }));
      }      // إنشاء كائن حساب الطاولة
      const tableAccount = {
        _id: table._id || `table-${table.number}`,
        id: `table-${table.number}`,
        tableNumber: table.number,
        section: table.section,
        status: table.status === 'occupied' ? 'active' : 'closed',
        isOpen: table.status === 'occupied',
        customer: customer || {
          name: 'غير محدد',
          phone: 'غير محدد'
        },
        customerName: customer?.name || 'غير محدد',
        orders: orders,
        ordersCount: allTableOrders.length, // العدد الإجمالي لجميع الطلبات
        activeOrdersCount: activeTableOrders.length, // عدد الطلبات النشطة
        totalAmount: totalAmount, // المبلغ الحالي للطلبات النشطة
        totalSales: totalSales, // إجمالي المبيعات لجميع الطلبات
        openTime: openTime,
        closeTime: table.status !== 'occupied' ? new Date() : closeTime,
        waiter: {
          id: table.assignedWaiter?._id || null,
          name: table.assignedWaiter?.name || 'غير محدد',
          username: table.assignedWaiter?.username || 'غير محدد'
        },
        waiterId: table.assignedWaiter?._id || null, // إضافة waiterId
        waiterName: table.assignedWaiter?.username || 'غير محدد',
        capacity: table.capacity,
        features: table.features,
        // إضافة معلومات إضافية
        occupationTime: table.occupationTime,
        isAvailable: table.isAvailable,
        displayName: table.displayName,
        createdAt: openTime || table.createdAt,
        updatedAt: table.updatedAt
      };

      tableAccounts.push(tableAccount);
    }

    console.log(`✅ تم إنشاء ${tableAccounts.length} حساب طاولة للإرسال`);
    
    // إرسال الاستجابة بصيغة منسقة
    res.json({
      success: true,
      message: 'تم جلب حسابات الطاولات بنجاح',
      data: tableAccounts,
      count: tableAccounts.length
    });
  } catch (error) {
    console.error('خطأ في جلب حسابات الطاولات:', error);
    res.status(500).json({
      success: false,
      message: 'خطأ في الخادم أثناء جلب حسابات الطاولات',
      error: error.message
    });
  }
});

/**
 * @route   GET /api/table-accounts/long-running
 * @desc    Get tables that have been occupied for more than 1 hour
 * @access  Private
 */
router.get('/long-running', authenticateToken, async (req, res) => {
  try {
    // جلب الطاولات المشغولة لأكثر من ساعة
    const oneHourAgo = new Date(Date.now() - 60 * 60 * 1000);
    
    const longRunningTables = await Table.find({
      status: 'occupied',
      'stats.lastUsed': { $lte: oneHourAgo },
      isActive: true
    })
    .populate('assignedWaiter', 'name username')
    .populate({
      path: 'currentOrder',
      populate: {
        path: 'items.product',
        select: 'name price'
      }
    });

    // تحويل البيانات
    const formattedTables = longRunningTables.map(table => {
      const order = table.currentOrder;
      const occupationMinutes = Math.round((Date.now() - table.stats.lastUsed.getTime()) / (1000 * 60));

      return {
        id: `table-${table.number}`,
        tableNumber: table.number,
        section: table.section,
        status: 'active',
        customer: {
          name: order?.customer.name || 'غير محدد',
          phone: order?.customer.phone || 'غير محدد'
        },
        orders: order?.items || [],
        totalAmount: order?.total || 0,
        openTime: table.stats.lastUsed,
        closeTime: null,
        waiter: {
          id: table.assignedWaiter?._id || null,
          name: table.assignedWaiter?.name || 'غير محدد',
          username: table.assignedWaiter?.username || 'غير محدد' // إضافة اسم المستخدم هنا
        },
        duration: occupationMinutes,
        waiterName: table.assignedWaiter?.name || 'غير محدد'
      };
    });

    res.json(formattedTables);
  } catch (error) {
    console.error('خطأ في جلب الطاولات طويلة المدى:', error);
    res.status(500).json({
      success: false,
      message: 'خطأ في الخادم أثناء جلب الطاولات طويلة المدى',
      error: error.message
    });
  }
});

/**
 * @route   POST /api/table-accounts
 * @desc    Create a new table account (occupy a table)
 * @access  Private
 */
router.post('/', authenticateToken, async (req, res) => {
  try {
    console.log('📱 Table account request from:', req.get('User-Agent'));
    console.log('📋 Request body:', JSON.stringify(req.body, null, 2));
    
    const { tableNumber, section, customer } = req.body;

    // التحقق من البيانات المطلوبة
    if (!tableNumber || !section) {
      console.log('❌ Missing required fields:', { tableNumber, section });
      return res.status(400).json({
        success: false,
        message: 'رقم الطاولة والقسم مطلوبان',
        missingFields: {
          tableNumber: !tableNumber,
          section: !section
        }
      });
    }

    // البحث عن الطاولة
    console.log('🔍 Searching for table:', { number: tableNumber, section });
    const table = await Table.findOne({ 
      number: tableNumber, 
      section: section,
      isActive: true 
    });

    if (!table) {
      console.log('❌ Table not found:', { tableNumber, section });
      return res.status(404).json({
        success: false,
        message: 'الطاولة غير موجودة',
        searchCriteria: { tableNumber, section }
      });
    }

    console.log('✅ Table found:', table.number, 'Status:', table.status);

    if (table.status !== 'available') {
      console.log('❌ Table not available:', table.status);
      return res.status(400).json({
        success: false,
        message: 'الطاولة غير متاحة حالياً',
        currentStatus: table.status
      });
    }

    // إنشاء طلب جديد للطاولة
    const newOrder = new Order({
      customer: {
        name: customer.name,
        phone: customer.phone
      },
      orderType: 'dine-in',
      table: {
        number: tableNumber,
        section: section
      },
      items: [],
      total: 0,
      status: 'pending'
    });

    await newOrder.save();

    // ربط الطاولة بالطلب
    await table.occupy(newOrder._id, req.user.userId);    // تحضير الرد
    const tableAccount = {
      _id: table._id,
      id: `table-${table.number}`,
      tableNumber: table.number,
      section: table.section,
      status: 'active',
      isOpen: true,
      customer: customer,
      customerName: customer.name,
      orders: [],
      ordersCount: 0,
      totalAmount: 0,
      openTime: new Date(),
      closeTime: null,
      waiter: {
        id: req.user.userId,
        name: req.user.name || 'النادل',
        username: req.user.username || 'النادل'
      },
      waiterId: req.user.userId,
      waiterName: req.user.username || req.user.name || 'النادل',
      createdAt: new Date(),
      updatedAt: new Date()
    };res.status(201).json({
      success: true,
      message: 'تم إنشاء حساب الطاولة بنجاح',
      data: tableAccount
    });

    // Send Socket notifications for table opening
    if (global.socketHandlers) {
      try {
        const waiterUser = await User.findById(req.user.userId);
        const waiterDisplayName = waiterUser ? (waiterUser.name || waiterUser.username) : 'نادل';

        // Emit table status change event
        global.socketHandlers.io.emit('table-status-change', {
          tableNumber: table.number,
          isOpen: true,
          waiterName: waiterDisplayName,
          action: 'opened',
          customer: customer,
          timestamp: new Date().toISOString()
        });

        console.log(`📡 تم إرسال إشعار فتح الطاولة ${table.number} بواسطة ${waiterDisplayName}`);
      } catch (socketError) {
        console.error('خطأ في إرسال إشعار Socket:', socketError);
      }
    }  } catch (error) {
    console.error('❌ خطأ في إنشاء حساب الطاولة:', error);
    console.error('📱 User-Agent:', req.get('User-Agent'));
    console.error('🌐 Origin:', req.get('Origin'));
    console.error('📋 Request body:', JSON.stringify(req.body, null, 2));
    
    // تحديد نوع الخطأ
    let statusCode = 500;
    let message = 'خطأ في الخادم أثناء إنشاء حساب الطاولة';
    
    if (error.name === 'ValidationError') {
      statusCode = 400;
      message = 'بيانات غير صحيحة';
    } else if (error.name === 'CastError') {
      statusCode = 400;
      message = 'تنسيق البيانات غير صحيح';
    }
    
    res.status(statusCode).json({
      success: false,
      message: message,
      error: error.message,
      errorType: error.name,
      // إضافة معلومات إضافية للتشخيص
      debug: {
        userAgent: req.get('User-Agent'),
        origin: req.get('Origin'),
        contentType: req.get('Content-Type'),
        body: req.body
      }
    });
  }
});

/**
 * @route   PUT /api/table-accounts/:tableId/close
 * @desc    Close a table account
 * @access  Private
 */
router.put('/:tableId/close', authenticateToken, async (req, res) => {
  try {
    const tableId = req.params.tableId;
    console.log('🔄 محاولة إغلاق الطاولة:', tableId);

    let table = null;

    // محاولة البحث بـ ObjectId أولاً
    if (tableId.match(/^[0-9a-fA-F]{24}$/)) {
      table = await Table.findById(tableId).populate('currentOrder');
      console.log('🔍 البحث بـ ObjectId:', tableId, table ? 'موجود' : 'غير موجود');
    }

    // إذا لم يوجد، حاول البحث برقم الطاولة
    if (!table) {
      const tableNumber = parseInt(tableId.replace('table-', ''));
      if (!isNaN(tableNumber)) {
        table = await Table.findOne({ number: tableNumber }).populate('currentOrder');
        console.log('🔍 البحث برقم الطاولة:', tableNumber, table ? 'موجود' : 'غير موجود');
      }
    }

    // إذا لم يوجد، حاول البحث بـ tableId كما هو
    if (!table) {
      table = await Table.findOne({ 
        $or: [
          { _id: tableId },
          { number: tableId },
          { id: tableId }
        ]
      }).populate('currentOrder');
      console.log('🔍 البحث المتعدد:', tableId, table ? 'موجود' : 'غير موجود');
    }

    if (!table) {
      console.log('❌ لم يتم العثور على الطاولة:', tableId);
      return res.status(404).json({
        success: false,
        message: 'الطاولة غير موجودة',
        tableId: tableId
      });
    }

    console.log('✅ تم العثور على الطاولة:', table.number, 'الحالة:', table.status);

    // تحديث حالة الطلب إلى مكتمل
    if (table.currentOrder) {
      const order = table.currentOrder;
      order.status = 'completed';
      order.timing.completedAt = new Date();
      await order.save();

      // تحديث إحصائيات الطاولة
      await table.updateStats(order.total || 0);
    }

    // تحرير الطاولة
    await table.release();    res.json({
      success: true,
      message: 'تم إغلاق حساب الطاولة بنجاح'
    });

    // Send Socket notifications for table closing
    if (global.socketHandlers) {
      try {
        const waiterUser = await User.findById(table.assignedWaiter);
        const waiterDisplayName = waiterUser ? (waiterUser.name || waiterUser.username) : 'نادل';

        // Emit table status change event
        global.socketHandlers.io.emit('table-status-change', {
          tableNumber: table.number,
          isOpen: false,
          waiterName: waiterDisplayName,
          action: 'closed',
          totalAmount: table.currentOrder?.total || 0,
          timestamp: new Date().toISOString()
        });

        console.log(`📡 تم إرسال إشعار إغلاق الطاولة ${table.number} بواسطة ${waiterDisplayName}`);
      } catch (socketError) {
        console.error('خطأ في إرسال إشعار Socket:', socketError);
      }
    }
  } catch (error) {
    console.error('خطأ في إغلاق حساب الطاولة:', error);
    res.status(500).json({
      success: false,
      message: 'خطأ في الخادم أثناء إغلاق حساب الطاولة',
      error: error.message
    });
  }
});

/**
 * @route   GET /api/table-accounts/stats
 * @desc    Get tables statistics
 * @access  Private
 */
router.get('/stats', authenticateToken, async (req, res) => {
  try {
    const stats = {
      total: await Table.countDocuments({ isActive: true }),
      available: await Table.countDocuments({ status: 'available', isActive: true }),
      occupied: await Table.countDocuments({ status: 'occupied', isActive: true }),
      reserved: await Table.countDocuments({ status: 'reserved', isActive: true }),
      cleaning: await Table.countDocuments({ status: 'cleaning', isActive: true }),
      maintenance: await Table.countDocuments({ status: 'maintenance', isActive: true }),
      sections: {}
    };

    // إحصائيات حسب القسم
    const sections = ['أ', 'ب', 'ج', 'VIP'];
    for (const section of sections) {
      stats.sections[section] = {
        total: await Table.countDocuments({ section, isActive: true }),
        available: await Table.countDocuments({ section, status: 'available', isActive: true }),
        occupied: await Table.countDocuments({ section, status: 'occupied', isActive: true })
      };
    }

    res.json({
      success: true,
      data: stats
    });
  } catch (error) {
    console.error('خطأ في جلب إحصائيات الطاولات:', error);
    res.status(500).json({
      success: false,
      message: 'خطأ في الخادم أثناء جلب الإحصائيات',
      error: error.message
    });
  }
});

/**
 * @route   GET /api/table-accounts/check
 * @desc    Check if table account exists for specific table and waiter
 * @access  Private
 */
router.get('/check', authenticateToken, async (req, res) => {
  try {
    const { tableNumber, waiterName } = req.query;

    console.log('🔍 فحص الطاولة - المعاملات المستلمة:', { tableNumber, waiterName });

    if (!tableNumber) {
      return res.status(400).json({
        success: false,
        message: 'رقم الطاولة مطلوب'
      });
    }

    // تحويل رقم الطاولة إلى رقم صحيح مع معالجة الأخطاء المحسنة
    let tableNum;
    try {
      tableNum = parseInt(tableNumber, 10);
      if (isNaN(tableNum) || tableNum <= 0) {
        throw new Error('رقم طاولة غير صالح');
      }
    } catch (parseError) {
      console.error('❌ خطأ في تحويل رقم الطاولة:', parseError);
      return res.status(400).json({
        success: false,
        message: 'رقم طاولة غير صالح - يجب أن يكون رقم صحيح أكبر من 0'
      });
    }

    // البحث عن الطاولة مع معالجة أفضل للأخطاء
    let table;
    try {
      table = await Table.findOne({ 
        number: tableNum,
        isActive: true 
      })
      .populate('assignedWaiter', 'name username')
      .populate({
        path: 'currentOrder',
        populate: {
          path: 'items.product',
          select: 'name price'
        }
      })
      .exec();
    } catch (dbError) {
      console.error('❌ خطأ في قاعدة البيانات عند البحث عن الطاولة:', dbError);
      return res.status(500).json({
        success: false,
        message: 'خطأ في الخادم - فشل في البحث عن الطاولة',
        error: process.env.NODE_ENV === 'development' ? dbError.message : 'Database error'
      });
    }

    console.log('📋 نتيجة البحث عن الطاولة:', table ? {
      number: table.number,
      isOccupied: table.isOccupied,
      status: table.status,
      hasCurrentOrder: !!table.currentOrder,
      assignedWaiter: table.assignedWaiter?.name
    } : 'لا توجد');

    // إذا لم توجد الطاولة أو لم تكن مشغولة
    if (!table || !table.isOccupied || !table.currentOrder) {
      return res.json({
        success: true,
        exists: false,
        account: null,
        tableStatus: 'available',
        waiterName: null,
        waiterUsername: null,
        isCurrentWaiter: false
      });
    }

    // تحضير معلومات الحساب بشكل متوافق مع الحاسوب والهاتف
    const order = table.currentOrder;
    const tableAccount = {
      _id: table._id,
      tableNumber: table.number.toString(), // تحويل إلى string للتوافق
      section: table.section || 'القسم الرئيسي',
      status: 'active',
      isOpen: true,
      customer: {
        name: order.customer?.name || 'غير محدد',
        phone: order.customer?.phone || 'غير محدد'
      },
      orders: order.items ? order.items.map(item => ({
        productName: item.productName || 'غير محدد',
        quantity: item.quantity || 1,
        price: item.price || 0,
        subtotal: item.subtotal || 0
      })) : [],
      totalAmount: order.totals?.total || 0,
      openTime: order.timing?.orderTime || new Date(),
      closeTime: null,
      waiter: {
        id: table.assignedWaiter?._id || null,
        name: table.assignedWaiter?.name || 'غير محدد',
        username: table.assignedWaiter?.username || 'غير محدد'
      },
      waiterName: table.assignedWaiter?.name || 'غير محدد',
      waiterUsername: table.assignedWaiter?.username || 'غير محدد',
      createdAt: order.timing?.orderTime || new Date()
    };

    // فحص ما إذا كان النادل الحالي هو نفسه
    const currentWaiterUsername = req.user?.username;
    const isCurrentWaiter = table.assignedWaiter?.username === currentWaiterUsername;

    console.log('✅ إعداد استجابة فحص الطاولة:', {
      exists: true,
      waiterMatch: isCurrentWaiter,
      currentWaiter: currentWaiterUsername,
      tableWaiter: table.assignedWaiter?.username
    });

    res.json({
      success: true,
      exists: true,
      account: tableAccount,
      tableStatus: 'occupied',
      waiterName: table.assignedWaiter?.name || 'غير محدد',      waiterUsername: table.assignedWaiter?.username || 'غير محدد',
      isCurrentWaiter: isCurrentWaiter
    });

  } catch (error) {
    console.error('❌ خطأ في التحقق من حساب الطاولة:', error);
    res.status(500).json({
      success: false,
      message: 'خطأ في الخادم أثناء التحقق من حساب الطاولة',
      error: error.message,
      stack: process.env.NODE_ENV === 'development' ? error.stack : undefined
    });
  }
});

/**
 * @route   PUT /api/table-accounts/:id
 * @desc    Update or close a table account by ID
 * @access  Private
 */
router.put('/:id', authenticateToken, async (req, res) => {
  try {
    const tableId = req.params.id;
    const { status, action } = req.body;

    // If this is a close action
    if (action === 'close' || status === 'closed') {
      // Try to find table by MongoDB _id first
      let table = await Table.findById(tableId).populate('currentOrder');
      
      // If not found, try to extract table number from the ID format
      if (!table && tableId.startsWith('table-')) {
        const tableNumber = parseInt(tableId.replace('table-', ''));
        table = await Table.findOne({ number: tableNumber }).populate('currentOrder');
      }

      // If still not found, try to find by table number directly
      if (!table) {
        const tableNumber = parseInt(tableId);
        if (!isNaN(tableNumber)) {
          table = await Table.findOne({ number: tableNumber }).populate('currentOrder');
        }
      }

      if (!table) {
        return res.status(404).json({
          success: false,
          message: 'الطاولة غير موجودة'
        });
      }

      // تحديث حالة الطلب إلى مكتمل
      if (table.currentOrder) {
        const order = table.currentOrder;
        order.status = 'completed';
        order.timing.completedAt = new Date();
        await order.save();

        // تحديث إحصائيات الطاولة
        await table.updateStats(order.total || 0);
      }

      // تحرير الطاولة
      await table.release();

      res.json({
        success: true,
        message: 'تم إغلاق حساب الطاولة بنجاح',
        tableId: tableId,
        tableNumber: table.number
      });

      // Send Socket notifications
      if (global.socketHandlers) {
        try {
          const waiterUser = await User.findById(table.assignedWaiter);
          const waiterDisplayName = waiterUser ? (waiterUser.name || waiterUser.username) : 'نادل';

          global.socketHandlers.io.emit('table-status-change', {
            tableNumber: table.number,
            isOpen: false,
            waiterName: waiterDisplayName,
            action: 'closed',
            totalAmount: table.currentOrder?.total || 0,
            timestamp: new Date().toISOString()
          });

          console.log(`📡 تم إرسال إشعار إغلاق الطاولة ${table.number} بواسطة ${waiterDisplayName}`);
        } catch (socketError) {
          console.error('خطأ في إرسال إشعار Socket:', socketError);
        }
      }

      return;
    }

    // For other update operations
    res.status(400).json({
      success: false,
      message: 'عملية غير مدعومة'
    });

  } catch (error) {
    console.error('خطأ في تحديث حساب الطاولة:', error);
    res.status(500).json({
      success: false,
      message: 'خطأ في الخادم أثناء تحديث حساب الطاولة',
      error: error.message
    });
  }
});

/**
 * @route   POST /api/table-accounts/:id/close
 * @desc    Close a table account (alternative endpoint)
 * @access  Private
 */
router.post('/:id/close', authenticateToken, async (req, res) => {
  try {
    const tableId = req.params.id;

    // Use the same logic as PUT endpoint
    // Try to find table by MongoDB _id first
    let table = await Table.findById(tableId).populate('currentOrder');
    
    // If not found, try to extract table number from the ID format
    if (!table && tableId.startsWith('table-')) {
      const tableNumber = parseInt(tableId.replace('table-', ''));
      table = await Table.findOne({ number: tableNumber }).populate('currentOrder');
    }

    // If still not found, try to find by table number directly
    if (!table) {
      const tableNumber = parseInt(tableId);
      if (!isNaN(tableNumber)) {
        table = await Table.findOne({ number: tableNumber }).populate('currentOrder');
      }
    }

    if (!table) {
      return res.status(404).json({
        success: false,
        message: 'الطاولة غير موجودة'
      });
    }

    // تحديث حالة الطلب إلى مكتمل
    if (table.currentOrder) {
      const order = table.currentOrder;
      order.status = 'completed';
      order.timing.completedAt = new Date();
      await order.save();

      // تحديث إحصائيات الطاولة
      await table.updateStats(order.total || 0);
    }

    // تحرير الطاولة
    await table.release();

    res.json({
      success: true,
      message: 'تم إغلاق حساب الطاولة بنجاح',
      tableId: tableId,
      tableNumber: table.number
    });

    // Send Socket notifications
    if (global.socketHandlers) {
      try {
        const waiterUser = await User.findById(table.assignedWaiter);
        const waiterDisplayName = waiterUser ? (waiterUser.name || waiterUser.username) : 'نادل';

        global.socketHandlers.io.emit('table-status-change', {
          tableNumber: table.number,
          isOpen: false,
          waiterName: waiterDisplayName,
          action: 'closed',
          totalAmount: table.currentOrder?.total || 0,
          timestamp: new Date().toISOString()
        });

        console.log(`📡 تم إرسال إشعار إغلاق الطاولة ${table.number} بواسطة ${waiterDisplayName}`);
      } catch (socketError) {
        console.error('خطأ في إرسال إشعار Socket:', socketError);
      }
    }

  } catch (error) {    console.error('خطأ في إغلاق حساب الطاولة:', error);
    res.status(500).json({
      success: false,
      message: 'خطأ في الخادم أثناء إغلاق حساب الطاولة',
      error: error.message
    });
  }
});

// Reset all table accounts - للمدير فقط
router.delete('/reset-all', authenticateToken, async (req, res) => {
  try {
    console.log('🔄 طلب إعادة تهيئة جميع الطاولات من المدير:', req.user);
    console.log('🔍 دور المستخدم:', req.user.role);
    
    // التحقق من أن المستخدم مدير
    if (req.user.role !== 'manager') {
      console.log('❌ رفض الطلب - المستخدم ليس مدير:', req.user.role);
      return res.status(403).json({
        success: false,
        message: 'غير مسموح - المديرون فقط يمكنهم إعادة تهيئة الطاولات'
      });
    }

    console.log('✅ التحقق من دور المدير نجح');
    console.log('🗄️ محاولة الاتصال بقاعدة البيانات لإعادة تهيئة الطاولات...');

    // إعادة تعيين جميع الطاولات لحالة فارغة
    const updateResult = await Table.updateMany(
      {},
      {
        $unset: {
          assignedWaiter: 1,
          currentOrder: 1,
          currentCustomer: 1,
          openedAt: 1,
          lastActivity: 1
        },
        $set: {
          isOccupied: false,
          status: 'available'
        }
      }
    );

    console.log('✅ تم إعادة تهيئة الطاولات:', updateResult.modifiedCount);

    // إرسال إشعار عبر Socket للجميع
    if (global.socketHandlers && global.socketHandlers.io) {
      global.socketHandlers.io.emit('tables-reset', {
        message: 'تم إعادة تهيئة جميع الطاولات',
        modifiedCount: updateResult.modifiedCount,
        timestamp: new Date().toISOString()
      });
      console.log('📡 تم إرسال إشعار Socket لإعادة تهيئة الطاولات');
    }

    res.json({
      success: true,
      message: `تم إعادة تهيئة ${updateResult.modifiedCount} طاولة بنجاح`,
      modifiedCount: updateResult.modifiedCount
    });

  } catch (error) {
    console.error('❌ خطأ في إعادة تهيئة الطاولات:', error);
    console.error('❌ تفاصيل الخطأ:', error.stack);
    res.status(500).json({
      success: false,
      message: 'خطأ في الخادم أثناء إعادة تهيئة الطاولات',
      error: error.message,
      details: process.env.NODE_ENV === 'development' ? error.stack : undefined
    });
  }
});

/**
 * @route   GET /api/table-accounts/:tableId/orders
 * @desc    Get all orders for a specific table
 * @access  Private
 */
router.get('/:tableId/orders', authenticateToken, async (req, res) => {
  try {
    const { tableId } = req.params;
    
    // البحث عن الطاولة
    const table = await Table.findById(tableId);
    if (!table) {
      return res.status(404).json({
        success: false,
        message: 'الطاولة غير موجودة'
      });
    }

    // جلب جميع الطلبات الخاصة بهذه الطاولة
    const orders = await Order.find({ 
      table: tableId 
    })
    .populate('table', 'number section')
    .populate('waiter', 'name username')
    .sort({ createdAt: -1 }) // الأحدث أولاً
    .select('orderNumber items totalPrice pricing createdAt status waiterName customerName updatedAt');

    // إذا لم تكن هناك طلبات، جرب البحث بـ table number
    let fallbackOrders = [];
    if (orders.length === 0) {
      fallbackOrders = await Order.find({
        $or: [
          { tableNumber: table.number },
          { 'table.number': table.number }
        ]
      })
      .sort({ createdAt: -1 })
      .select('orderNumber items totalPrice pricing createdAt status waiterName customerName updatedAt');
    }

    const finalOrders = orders.length > 0 ? orders : fallbackOrders;

    // تنسيق البيانات
    const formattedOrders = finalOrders.map(order => {
      let totalAmount = 0;
      
      // حساب الإجمالي من مصادر مختلفة
      if (order.totalPrice && typeof order.totalPrice === 'number') {
        totalAmount = order.totalPrice;
      } else if (order.pricing?.total && typeof order.pricing.total === 'number') {
        totalAmount = order.pricing.total;
      } else if (order.items && Array.isArray(order.items)) {
        totalAmount = order.items.reduce((sum, item) => {
          const itemPrice = item.price || 0;
          const itemQuantity = item.quantity || 1;
          return sum + (itemPrice * itemQuantity);
        }, 0);
      }

      return {
        _id: order._id,
        orderNumber: order.orderNumber,
        items: order.items || [],
        totalAmount: totalAmount,
        totalPrice: totalAmount,
        status: order.status,
        tableNumber: table.number,
        customerName: order.customerName || 'غير محدد',
        waiterName: order.waiterName || (order.waiter?.name) || 'غير محدد',
        createdAt: order.createdAt,
        updatedAt: order.updatedAt
      };
    });

    console.log(`📋 تم جلب ${formattedOrders.length} طلب للطاولة ${table.number}`);

    res.json({
      success: true,
      data: formattedOrders,
      table: {
        _id: table._id,
        number: table.number,
        section: table.section
      },
      total: formattedOrders.length
    });

  } catch (error) {
    console.error('❌ خطأ في جلب طلبات الطاولة:', error);
    res.status(500).json({
      success: false,
      message: 'خطأ في جلب طلبات الطاولة',
      error: error.message,
      details: process.env.NODE_ENV === 'development' ? error.stack : undefined
    });
  }
});

module.exports = router;

﻿/* Orders Screen Isolated Styles */
@import '../variables/orders-variables.css';

/* Ø<PERSON>Ø®Ø·ÙŠØ· Ø§Ù„Ø·Ù„Ø¨Ø§Øª */
.orders-screen {
  padding: var(--orders-spacing-lg);
  max-width: 1400px;
  margin: 0 auto;
  overflow-x: hidden;
}

/* Ø±Ø£Ø³ Ø§Ù„Ø´Ø§Ø´Ø© */
.orders-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: var(--orders-spacing-lg);
  flex-wrap: wrap;
  gap: var(--orders-spacing-md);
}

.orders-title {
  font-size: var(--orders-font-size-xl);
  font-weight: 700;
  color: var(--orders-text-primary);
  margin: 0;
}

/* Ø£Ø¯ÙˆØ§Øª Ø§Ù„ØªØ­ÙƒÙ… ÙÙŠ Ø§Ù„Ø·Ù„Ø¨Ø§Øª */
.orders-controls {
  display: flex;
  gap: var(--orders-spacing-md);
  align-items: center;
  flex-wrap: wrap;
}

.orders-search {
  padding: var(--orders-spacing-sm) var(--orders-spacing-md);
  border: 1px solid var(--orders-border-color);
  border-radius: var(--orders-border-radius);
  font-size: var(--orders-font-size-sm);
  background: var(--orders-bg-primary);
  color: var(--orders-text-primary);
  min-width: 200px;
}

.orders-filter {
  padding: var(--orders-spacing-sm) var(--orders-spacing-md);
  border: 1px solid var(--orders-border-color);
  border-radius: var(--orders-border-radius);
  font-size: var(--orders-font-size-sm);
  background: var(--orders-bg-primary);
  color: var(--orders-text-primary);
  min-width: 120px;
}

/* Ø§Ù„Ø¥Ø­ØµØ§Ø¦ÙŠØ§Øª Ø§Ù„Ø³Ø±ÙŠØ¹Ø© */
.orders-stats {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: var(--orders-spacing-md);
  margin-bottom: var(--orders-spacing-lg);
}

.order-stat-card {
  background: var(--orders-bg-primary);
  border: 1px solid var(--orders-border-color);
  border-radius: var(--orders-border-radius);
  padding: var(--orders-spacing-md);
  text-align: center;
}

.order-stat-number {
  font-size: var(--orders-font-size-xl);
  font-weight: 700;
  margin-bottom: var(--orders-spacing-xs);
}

.order-stat-number.pending {
  color: var(--orders-warning-color);
}

.order-stat-number.preparing {
  color: var(--orders-info-color);
}

.order-stat-number.ready {
  color: var(--orders-success-color);
}

.order-stat-number.completed {
  color: var(--orders-primary-color);
}

.order-stat-label {
  font-size: var(--orders-font-size-sm);
  color: var(--orders-text-secondary);
}

/* Ø¨Ø·Ø§Ù‚Ø§Øª Ø§Ù„Ø·Ù„Ø¨Ø§Øª */
.orders-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
  gap: var(--orders-spacing-lg);
  margin-top: var(--orders-spacing-lg);
}

.order-card {
  background: var(--orders-bg-primary);
  border: 1px solid var(--orders-border-color);
  border-radius: var(--orders-border-radius-lg);
  padding: var(--orders-spacing-lg);
  position: relative;
  transition: all 0.3s ease;
  box-shadow: var(--orders-shadow-sm);
  word-break: break-word;
  overflow: visible;
}

.order-card:hover {
  transform: translateY(-2px);
  box-shadow: var(--orders-shadow-md);
}

.order-card.pending {
  border-left: 4px solid var(--orders-warning-color);
}

.order-card.preparing {
  border-left: 4px solid var(--orders-info-color);
}

.order-card.ready {
  border-left: 4px solid var(--orders-success-color);
}

.order-card.completed {
  border-left: 4px solid var(--orders-primary-color);
}

/* Ø±Ø£Ø³ Ø¨Ø·Ø§Ù‚Ø© Ø§Ù„Ø·Ù„Ø¨ */
.order-card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: var(--orders-spacing-md);
}

.order-number {
  font-size: var(--orders-font-size-lg);
  font-weight: 700;
  color: var(--orders-text-primary);
}

.order-status {
  padding: 4px 8px;
  border-radius: 12px;
  font-size: var(--orders-font-size-xs);
  font-weight: 500;
  text-transform: uppercase;
}

.order-status.pending {
  background: var(--orders-warning-light);
  color: var(--orders-warning-color);
}

.order-status.preparing {
  background: var(--orders-info-light);
  color: var(--orders-info-color);
}

.order-status.ready {
  background: var(--orders-success-light);
  color: var(--orders-success-color);
}

.order-status.completed {
  background: var(--orders-primary-light);
  color: var(--orders-primary-color);
}

/* Ù…Ø¹Ù„ÙˆÙ…Ø§Øª Ø§Ù„Ø·Ù„Ø¨ */
.order-info {
  margin-bottom: var(--orders-spacing-md);
}

.order-info-item {
  display: flex;
  justify-content: space-between;
  margin-bottom: var(--orders-spacing-xs);
  font-size: var(--orders-font-size-sm);
}

.order-info-label {
  color: var(--orders-text-secondary);
}

.order-info-value {
  color: var(--orders-text-primary);
  font-weight: 500;
}

.order-total-amount {
  color: var(--orders-primary-color);
  font-weight: 700;
  font-size: var(--orders-font-size-md);
}

/* Ø£ØµÙ†Ø§Ù Ø§Ù„Ø·Ù„Ø¨ */
.order-items {
  margin-bottom: var(--orders-spacing-md);
}

.order-items-title {
  font-size: var(--orders-font-size-sm);
  font-weight: 600;
  color: var(--orders-text-primary);
  margin-bottom: var(--orders-spacing-xs);
}

.order-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: var(--orders-spacing-xs) 0;
  border-bottom: 1px solid var(--orders-border-color);
  font-size: var(--orders-font-size-xs);
}

.order-item:last-child {
  border-bottom: none;
}

.order-item-name {
  color: var(--orders-text-primary);
  flex: 1;
}

.order-item-quantity {
  color: var(--orders-text-secondary);
  margin: 0 var(--orders-spacing-sm);
}

.order-item-price {
  color: var(--orders-primary-color);
  font-weight: 500;
}

/* Ø£Ø²Ø±Ø§Ø± Ø§Ù„ØªØ­ÙƒÙ… ÙÙŠ Ø§Ù„Ø·Ù„Ø¨ */
.order-actions {
  display: flex;
  gap: var(--orders-spacing-sm);
  margin-top: var(--orders-spacing-md);
  flex-wrap: wrap;
}

.order-action-btn {
  padding: var(--orders-spacing-xs) var(--orders-spacing-sm);
  border: none;
  border-radius: var(--orders-border-radius);
  cursor: pointer;
  font-size: var(--orders-font-size-xs);
  font-weight: 500;
  transition: all 0.3s ease;
  flex: 1;
  min-width: 70px;
}

.order-action-btn.edit {
  background: var(--orders-warning-light);
  color: var(--orders-warning-color);
}

.order-action-btn.edit:hover {
  background: var(--orders-warning-color);
  color: white;
}

.order-action-btn.delete {
  background: var(--orders-error-light);
  color: var(--orders-error-color);
}

.order-action-btn.delete:hover {
  background: var(--orders-error-color);
  color: white;
}

.order-action-btn.status {
  background: var(--orders-info-light);
  color: var(--orders-info-color);
}

.order-action-btn.status:hover {
  background: var(--orders-info-color);
  color: white;
}

/* Ù‚Ø§Ø¦Ù…Ø© Ù…Ù†Ø³Ø¯Ù„Ø© Ù„ØªØ­Ø¯ÙŠØ« Ø§Ù„Ø­Ø§Ù„Ø© */
.status-dropdown {
  padding: var(--orders-spacing-xs) var(--orders-spacing-sm);
  border: 1px solid var(--orders-border-color);
  border-radius: var(--orders-border-radius);
  font-size: var(--orders-font-size-xs);
  background: var(--orders-bg-primary);
  color: var(--orders-text-primary);
  cursor: pointer;
}

/* Ø­Ø§Ù„Ø© Ø§Ù„ØªØ­Ù…ÙŠÙ„ */
.orders-loading {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 300px;
  font-size: var(--orders-font-size-lg);
  color: var(--orders-text-secondary);
}

/* Ø±Ø³Ø§Ù„Ø© Ø¹Ø¯Ù… ÙˆØ¬ÙˆØ¯ Ø·Ù„Ø¨Ø§Øª */
.no-orders {
  text-align: center;
  padding: var(--orders-spacing-xl);
  color: var(--orders-text-secondary);
}

.no-orders h3 {
  margin-bottom: var(--orders-spacing-md);
  color: var(--orders-text-primary);
}

/* ØªØ¬Ø§ÙˆØ¨ Ø§Ù„Ø´Ø§Ø´Ø© */
@media (max-width: 768px) {
  .orders-screen {
    padding: var(--orders-spacing-md);
  }
  
  .orders-header {
    flex-direction: column;
    align-items: stretch;
  }
  
  .orders-controls {
    flex-direction: column;
  }
  
  .orders-search,
  .orders-filter {
    width: 100%;
  }
  
  .orders-grid {
    grid-template-columns: 1fr;
    gap: var(--orders-spacing-md);
  }
  
  .orders-stats {
    grid-template-columns: repeat(2, 1fr);
  }
}

@media (max-width: 480px) {
  .order-actions {
    flex-direction: column;
  }
  
  .order-action-btn {
    flex: none;
  }
  
  .order-info-item {
    flex-direction: column;
    gap: var(--orders-spacing-xs);
  }
  
  .orders-stats {
    grid-template-columns: 1fr;
  }
}



<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار حالة الاتصال - Desha Coffee</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            direction: rtl;
            text-align: right;
            margin: 20px;
            background: #f5f5f5;
        }
        
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .status-card {
            border: 2px solid #ddd;
            border-radius: 8px;
            padding: 15px;
            margin: 10px 0;
            transition: all 0.3s ease;
        }
        
        .status-card.success {
            border-color: #22c55e;
            background: #f0fdf4;
        }
        
        .status-card.error {
            border-color: #ef4444;
            background: #fef2f2;
        }
        
        .status-card.loading {
            border-color: #3b82f6;
            background: #eff6ff;
        }
        
        button {
            background: #3b82f6;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        
        button:hover {
            background: #2563eb;
        }
        
        button:disabled {
            background: #9ca3af;
            cursor: not-allowed;
        }
        
        .log {
            background: #1f2937;
            color: #10b981;
            padding: 15px;
            border-radius: 5px;
            font-family: 'Courier New', monospace;
            font-size: 12px;
            white-space: pre-wrap;
            max-height: 400px;
            overflow-y: auto;
            margin: 10px 0;
        }
        
        .indicator {
            display: inline-block;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            margin-left: 8px;
        }
        
        .indicator.success { background: #22c55e; }
        .indicator.error { background: #ef4444; }
        .indicator.loading { background: #3b82f6; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔍 اختبار حالة الاتصال - Desha Coffee</h1>
        
        <div class="status-card" id="status-card">
            <h3><span class="indicator loading" id="indicator"></span>حالة الاتصال</h3>
            <p><strong>الخادم:</strong> <span id="server-status">جاري الفحص...</span></p>
            <p><strong>قاعدة البيانات:</strong> <span id="db-status">جاري الفحص...</span></p>
            <p><strong>آخر فحص:</strong> <span id="last-check">لم يتم الفحص بعد</span></p>
            <p id="error-message" style="color: red; display: none;"></p>
        </div>
        
        <div>
            <button onclick="checkHealth()" id="check-btn">فحص الاتصال</button>
            <button onclick="clearLog()">مسح السجل</button>
            <button onclick="autoCheck()" id="auto-btn">فحص تلقائي</button>
        </div>
        
        <h3>📊 سجل التفاصيل</h3>
        <div class="log" id="log"></div>
    </div>

    <script>
        let autoCheckInterval = null;
        
        function log(message) {
            const logEl = document.getElementById('log');
            const timestamp = new Date().toLocaleTimeString();
            logEl.textContent += `[${timestamp}] ${message}\n`;
            logEl.scrollTop = logEl.scrollHeight;
        }
        
        function updateUI(isConnected, serverStatus, dbStatus, error = null) {
            const card = document.getElementById('status-card');
            const indicator = document.getElementById('indicator');
            const serverEl = document.getElementById('server-status');
            const dbEl = document.getElementById('db-status');
            const lastCheckEl = document.getElementById('last-check');
            const errorEl = document.getElementById('error-message');
            
            // تحديث الحالة
            if (isConnected) {
                card.className = 'status-card success';
                indicator.className = 'indicator success';
            } else {
                card.className = 'status-card error';
                indicator.className = 'indicator error';
            }
            
            serverEl.textContent = serverStatus;
            dbEl.textContent = dbStatus;
            lastCheckEl.textContent = new Date().toLocaleTimeString();
            
            if (error) {
                errorEl.textContent = error;
                errorEl.style.display = 'block';
            } else {
                errorEl.style.display = 'none';
            }
        }
        
        function setLoading(loading) {
            const card = document.getElementById('status-card');
            const indicator = document.getElementById('indicator');
            const btn = document.getElementById('check-btn');
            
            if (loading) {
                card.className = 'status-card loading';
                indicator.className = 'indicator loading';
                btn.disabled = true;
                btn.textContent = 'جاري الفحص...';
            } else {
                btn.disabled = false;
                btn.textContent = 'فحص الاتصال';
            }
        }
        
        async function checkHealth() {
            setLoading(true);
            log('🔍 بدء فحص الاتصال...');
            
            try {
                const apiUrl = 'http://localhost:5001/api/health';
                log(`📡 إرسال طلب إلى: ${apiUrl}`);
                
                const response = await fetch(apiUrl, {
                    method: 'GET',
                    headers: {
                        'Content-Type': 'application/json',
                        'Accept': 'application/json',
                    }
                });
                
                log(`📊 Status: ${response.status} ${response.statusText}`);
                log(`📊 Headers: ${JSON.stringify(Object.fromEntries(response.headers.entries()), null, 2)}`);
                
                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }
                
                const data = await response.json();
                log(`📊 Response Data: ${JSON.stringify(data, null, 2)}`);
                
                // محاكاة منطق makeRequest
                const apiResponse = { success: true, data: data };
                const healthData = apiResponse.data;
                
                // محاكاة منطق ConnectionStatus
                const isServerHealthy = apiResponse.success === true && healthData?.status === 'healthy';
                const isDatabaseConnected = apiResponse.success === true && healthData?.database?.connected === true;
                
                log(`✅ Server healthy: ${isServerHealthy}`);
                log(`✅ Database connected: ${isDatabaseConnected}`);
                log(`✅ Overall connection: ${isServerHealthy && isDatabaseConnected}`);
                
                const serverStatus = isServerHealthy ? 'متصل' : 'غير متصل';
                const databaseStatus = isDatabaseConnected ? 'متصل' : 'غير متصل';
                
                updateUI(isServerHealthy && isDatabaseConnected, serverStatus, databaseStatus);
                log(`✅ تم الفحص بنجاح`);
                
            } catch (error) {
                log(`❌ خطأ في الاتصال: ${error.message}`);
                updateUI(false, 'غير متصل', 'غير متصل', error.message);
            } finally {
                setLoading(false);
            }
        }
        
        function clearLog() {
            document.getElementById('log').textContent = '';
        }
        
        function autoCheck() {
            const btn = document.getElementById('auto-btn');
            
            if (autoCheckInterval) {
                clearInterval(autoCheckInterval);
                autoCheckInterval = null;
                btn.textContent = 'فحص تلقائي';
                log('⏹️ تم إيقاف الفحص التلقائي');
            } else {
                autoCheckInterval = setInterval(checkHealth, 10000);
                btn.textContent = 'إيقاف التلقائي';
                log('▶️ تم بدء الفحص التلقائي كل 10 ثوان');
                checkHealth(); // فحص فوري
            }
        }
        
        // فحص تلقائي عند تحميل الصفحة
        window.onload = function() {
            log('🚀 تم تحميل صفحة الاختبار');
            checkHealth();
        };
    </script>
</body>
</html>

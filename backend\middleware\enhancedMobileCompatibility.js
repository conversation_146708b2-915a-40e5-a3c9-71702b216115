// Enhanced Mobile Compatibility Middleware
// ميدلوير محسن للتوافق مع الأجهزة المحمولة

const enhancedMobileCompatibility = (req, res, next) => {
  // Get user agent and detect device type
  const userAgent = req.headers['user-agent'] || '';
  const origin = req.headers.origin || req.headers.referer || 'unknown';
  
  // Enhanced device detection
  const isMobile = /Mobile|Android|iPhone|iPad|BlackBerry|IEMobile|Opera Mini/i.test(userAgent);
  const isTablet = /iPad|Android(?!.*Mobile)/i.test(userAgent);
  const isIOS = /iPhone|iPad|iPod/i.test(userAgent);
  const isAndroid = /Android/i.test(userAgent);
  const isChrome = /Chrome/i.test(userAgent);
  const isSafari = /Safari/i.test(userAgent) && !/Chrome/i.test(userAgent);
  const isFirefox = /Firefox/i.test(userAgent);
  
  // Device info object
  const deviceInfo = {
    userAgent,
    origin,
    isMobile,
    isTablet,
    isDesktop: !isMobile && !isTablet,
    isIOS,
    isAndroid,
    isChrome,
    isSafari,
    isFirefox,
    type: isMobile ? 'mobile' : isTablet ? 'tablet' : 'desktop',
    platform: isIOS ? 'ios' : isAndroid ? 'android' : 'web'
  };
  
  // Add device info to request
  req.deviceInfo = deviceInfo;
  req.isMobile = isMobile; // Keep for backward compatibility
  
  // Set mobile-optimized headers
  if (isMobile || isTablet) {
    // Prevent caching for mobile devices to ensure fresh data
    res.header('Cache-Control', 'no-cache, no-store, must-revalidate, private');
    res.header('Pragma', 'no-cache');
    res.header('Expires', '0');
    
    // Optimize for mobile networks
    res.header('X-Content-Type-Options', 'nosniff');
    res.header('X-Frame-Options', 'SAMEORIGIN');
    
    // Mobile-specific headers
    res.header('X-UA-Compatible', 'IE=edge');
    res.header('X-Mobile-Optimized', 'true');
    
    // Compression hints for mobile
    res.header('Vary', 'Accept-Encoding, User-Agent');
  }
    // Enhanced CORS headers for mobile compatibility
  const allowedOrigins = [
    'https://desha-coffee.vercel.app',
    'https://coffee-eta-murex.vercel.app', 
    'http://localhost:3000',
    'http://localhost:5173',
    'http://localhost:5190',
    'capacitor://localhost',
    'ionic://localhost',
    'http://localhost',
    'https://localhost',
    process.env.FRONTEND_URL,
    process.env.CORS_ORIGIN
  ].filter(Boolean);
  
  // More permissive origin handling for mobile apps
  if (isMobile && (!origin || origin === 'null' || origin === 'undefined')) {
    res.header('Access-Control-Allow-Origin', '*');
  } else if (allowedOrigins.includes(origin) || origin.includes('localhost')) {
    res.header('Access-Control-Allow-Origin', origin);
  } else {
    res.header('Access-Control-Allow-Origin', '*'); // More permissive for mobile
  }
  
  // Enhanced headers for mobile compatibility
  const allowedHeaders = [
    'Origin',
    'X-Requested-With',
    'Content-Type',
    'Accept', 
    'Authorization',
    'Cache-Control',
    'Pragma',
    'Expires',
    'X-Request-ID',
    'X-Correlation-ID',
    'X-Session-ID',
    'X-Device-Type',
    'X-App-Version',
    'X-Platform',
    'User-Agent',
    'Referer',
    'Accept-Language',
    'Accept-Encoding'
  ];
  
  res.header('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE, OPTIONS, PATCH, HEAD');
  res.header('Access-Control-Allow-Headers', allowedHeaders.join(', '));
  res.header('Access-Control-Allow-Credentials', 'true');
  res.header('Access-Control-Max-Age', '86400'); // 24 hours
  
  // Expose useful headers to mobile clients
  res.header('Access-Control-Expose-Headers', [
    'X-Total-Count',
    'X-Page-Count', 
    'X-Current-Page',
    'X-API-Version',
    'X-Request-ID',
    'X-Response-Time'
  ].join(', '));
  
  // Handle preflight requests efficiently
  if (req.method === 'OPTIONS') {
    res.header('Content-Length', '0');
    return res.status(204).end();
  }
    // Add request ID for tracking
  req.requestId = req.headers['x-request-id'] || 
                  req.headers['x-railway-request-id'] ||
                  req.headers['x-vercel-id'] ||
                  `req_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  res.header('X-Request-ID', req.requestId);
  
  // Add Railway/Vercel specific headers
  if (process.env.RAILWAY_PROJECT_ID) {
    res.header('X-Powered-By-Railway', 'true');
  }
  
  if (process.env.VERCEL) {
    res.header('X-Frontend-Vercel', 'true');
  }
  
  // Detailed logging for mobile requests
  if (isMobile || process.env.NODE_ENV === 'development') {
    const logData = {
      requestId: req.requestId,
      timestamp: new Date().toISOString(),
      method: req.method,
      path: req.path,
      query: Object.keys(req.query).length > 0 ? req.query : null,
      deviceType: deviceInfo.type,
      platform: deviceInfo.platform,
      userAgent: userAgent.substring(0, 100) + (userAgent.length > 100 ? '...' : ''),
      origin: origin,
      contentType: req.headers['content-type'],
      contentLength: req.headers['content-length'],
      hasBody: req.method !== 'GET' && req.method !== 'HEAD' && req.method !== 'OPTIONS'
    };
    
    console.log(`📱 ${deviceInfo.type.toUpperCase()} Request:`, logData);
  }
  
  // Add response time tracking
  const startTime = Date.now();
  
  // Override res.json to add mobile-specific optimizations
  const originalJson = res.json;
  res.json = function(data) {
    const responseTime = Date.now() - startTime;
    res.header('X-Response-Time', `${responseTime}ms`);
    
    // Add device info to response in development
    if (process.env.NODE_ENV === 'development' && isMobile) {
      if (typeof data === 'object' && data !== null) {
        data._deviceInfo = {
          type: deviceInfo.type,
          platform: deviceInfo.platform,
          responseTime: `${responseTime}ms`
        };
      }
    }
    
    return originalJson.call(this, data);
  };
  
  // Handle mobile-specific timeouts
  if (isMobile) {
    // Set a longer timeout for mobile devices due to potentially slower connections
    req.setTimeout(30000, () => {
      console.warn(`⏰ Mobile request timeout: ${req.method} ${req.path}`);
      if (!res.headersSent) {
        res.status(408).json({
          success: false,
          message: 'انتهت مهلة الطلب',
          error: 'REQUEST_TIMEOUT',
          timestamp: new Date().toISOString()
        });
      }
    });
  }
  
  next();
};

// Middleware to handle mobile-specific errors
const mobileErrorHandler = (error, req, res, next) => {
  const deviceInfo = req.deviceInfo || {};
  
  console.error(`🚨 Mobile Error Handler:`, {
    requestId: req.requestId,
    deviceType: deviceInfo.type,
    platform: deviceInfo.platform,
    error: error.message,
    path: req.path,
    method: req.method
  });
  
  // Mobile-friendly error responses
  if (deviceInfo.isMobile || deviceInfo.isTablet) {
    // Simplified error messages for mobile
    const mobileErrorMap = {
      'ECONNREFUSED': 'خطأ في الاتصال',
      'ENOTFOUND': 'خدمة غير متاحة',
      'ETIMEDOUT': 'انتهت مهلة الطلب',
      'ECONNRESET': 'انقطع الاتصال',
      'ValidationError': 'بيانات غير صحيحة',
      'CastError': 'معرف غير صحيح',
      'MongoError': 'خطأ في قاعدة البيانات'
    };
    
    const friendlyMessage = mobileErrorMap[error.name] || mobileErrorMap[error.code] || 'خطأ غير متوقع';
    
    return res.status(error.status || 500).json({
      success: false,
      message: friendlyMessage,
      error: error.name || 'UNKNOWN_ERROR',
      requestId: req.requestId,
      timestamp: new Date().toISOString(),
      // Include more details in development
      ...(process.env.NODE_ENV === 'development' && {
        details: error.message,
        path: req.path,
        method: req.method
      })
    });
  }
  
  // Pass to next error handler for non-mobile devices
  next(error);
};

module.exports = {
  enhancedMobileCompatibility,
  mobileErrorHandler
};

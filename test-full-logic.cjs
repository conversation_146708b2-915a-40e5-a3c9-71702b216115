// اختبار مباشر لـ checkServerHealth function
// سنستخدم نفس الكود الذي يستخدمه المكون

const fetch = require('node-fetch');

// محاكاة makeRequest function
const makeRequest = async (endpoint) => {
  const API_BASE_URL = 'http://localhost:5001';
  const REQUEST_TIMEOUT = 30000;
  
  const controller = new AbortController();
  const timeoutId = setTimeout(() => controller.abort(), REQUEST_TIMEOUT);

  try {
    const response = await fetch(`${API_BASE_URL}${endpoint}`, {
      signal: controller.signal,
      headers: {
        'Content-Type': 'application/json',
        'Accept': 'application/json',
      },
    });

    clearTimeout(timeoutId);

    if (!response.ok) {
      throw new Error(`HTTP ${response.status}`);
    }

    const data = await response.json();
    return { success: true, data };
    
  } catch (error) {
    clearTimeout(timeoutId);
    console.error('❌ makeRequest error:', error.message);
    return { success: false, error: error.message };
  }
};

// محاكاة checkServerHealth
const checkServerHealth = async () => {
  return makeRequest('/api/health');
};

// اختبار المنطق الكامل
async function testFullLogic() {
  console.log('🔍 اختبار منطق ConnectionStatus كاملاً...\n');
  
  try {
    console.log('1️⃣ استدعاء checkServerHealth...');
    const response = await checkServerHealth();
    console.log('📊 Response:', JSON.stringify(response, null, 2));
    
    console.log('\n2️⃣ استخراج البيانات...');
    const healthData = response.data;
    console.log('📊 Health data:', JSON.stringify(healthData, null, 2));
    console.log('📊 Response success:', response.success);
    console.log('📊 Health status:', healthData?.status);
    console.log('📊 Database connected:', healthData?.database?.connected);
    
    console.log('\n3️⃣ التحقق من الحالة...');
    const isServerHealthy = response.success === true && healthData?.status === 'healthy';
    const isDatabaseConnected = response.success === true && healthData?.database?.connected === true;
    
    console.log('✅ Server healthy:', isServerHealthy);
    console.log('✅ Database connected:', isDatabaseConnected);
    console.log('✅ Overall connection:', isServerHealthy && isDatabaseConnected);
    
    console.log('\n4️⃣ النتيجة النهائية:');
    const finalState = {
      isConnected: isServerHealthy && isDatabaseConnected,
      serverStatus: isServerHealthy ? 'متصل' : 'غير متصل',
      databaseStatus: isDatabaseConnected ? 'متصل' : 'غير متصل',
      lastChecked: new Date(),
      error: null
    };
    
    console.log('📱 Final State:', JSON.stringify(finalState, null, 2));
    
  } catch (error) {
    console.error('❌ Test failed:', error.message);
  }
}

testFullLogic();

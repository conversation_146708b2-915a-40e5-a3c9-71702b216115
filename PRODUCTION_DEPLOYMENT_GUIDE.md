# 🚀 دليل النشر للإنتاج - نظام إدارة المقهى

## 📋 متطلبات النشر

### **متطلبات الخادم**
- Node.js v16+ 
- NPM v8+
- MongoDB v4.4+
- ذاكرة RAM: 1GB كحد أدنى
- مساحة التخزين: 5GB كحد أدنى

### **متطلبات البيئة**
- HTTPS مطلوب للـ Service Worker والإشعارات
- دعم WebSockets للـ Real-time updates
- CORS مضبوط بشكل صحيح

---

## 🛠️ خطوات النشر

### **1. تحضير الملفات**
```bash
# استنساخ المشروع
git clone <repository-url>
cd Coffee

# تثبيت التبعيات
npm install
cd backend && npm install && cd ..

# بناء المشروع للإنتاج
npm run build
```

### **2. إعد<PERSON> قاعدة البيانات**
```bash
# إنشاء قاعدة بيانات MongoDB
mongosh
use coffee_shop_production

# تشغيل scripts الإعداد
node backend/scripts/setup-database.js
```

### **3. ضبط متغيرات البيئة**
```bash
# إنشاء ملف .env في backend
NODE_ENV=production
PORT=3001
MONGODB_URI=mongodb://localhost:27017/coffee_shop_production
JWT_SECRET=your-super-secret-jwt-key-here
```

### **4. إعداد NGINX (اختياري)**
```nginx
server {
    listen 80;
    server_name your-domain.com;
    
    # إعادة توجيه HTTP إلى HTTPS
    return 301 https://$server_name$request_uri;
}

server {
    listen 443 ssl http2;
    server_name your-domain.com;
    
    # شهادات SSL
    ssl_certificate /path/to/certificate.crt;
    ssl_certificate_key /path/to/private.key;
    
    # ملفات الـ Frontend (Static)
    location / {
        root /path/to/coffee/dist;
        try_files $uri $uri/ /index.html;
    }
    
    # API Backend
    location /api/ {
        proxy_pass http://localhost:3001;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host $host;
        proxy_cache_bypass $http_upgrade;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }
    
    # WebSocket للـ Real-time
    location /socket.io/ {
        proxy_pass http://localhost:3001;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection "upgrade";
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }
}
```

---

## 🐳 نشر باستخدام Docker

### **1. إنشاء Dockerfile**
```dockerfile
# Multi-stage build
FROM node:18-alpine AS builder

WORKDIR /app
COPY package*.json ./
RUN npm ci --only=production

COPY . .
RUN npm run build

# Production stage  
FROM node:18-alpine AS production

WORKDIR /app

# إنشاء مستخدم غير مميز
RUN addgroup -g 1001 -S nodejs
RUN adduser -S nextjs -u 1001

# نسخ Backend
COPY backend/package*.json ./backend/
RUN cd backend && npm ci --only=production

COPY backend/ ./backend/
COPY --from=builder /app/dist ./dist

# تعيين الإذونات
RUN chown -R nextjs:nodejs /app
USER nextjs

EXPOSE 3001

CMD ["node", "backend/server.js"]
```

### **2. إنشاء docker-compose.yml**
```yaml
version: '3.8'

services:
  app:
    build: .
    ports:
      - "3001:3001"
    environment:
      - NODE_ENV=production
      - MONGODB_URI=mongodb://mongo:27017/coffee_shop
      - JWT_SECRET=${JWT_SECRET}
    depends_on:
      - mongo
    restart: unless-stopped

  mongo:
    image: mongo:5.0
    ports:
      - "27017:27017"
    volumes:
      - mongo_data:/data/db
    restart: unless-stopped

  nginx:
    image: nginx:alpine
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx.conf:/etc/nginx/conf.d/default.conf
      - ./ssl:/etc/nginx/ssl
    depends_on:
      - app
    restart: unless-stopped

volumes:
  mongo_data:
```

### **3. تشغيل Docker**
```bash
# بناء وتشغيل الحاويات
docker-compose up -d

# مراقبة اللوجز
docker-compose logs -f
```

---

## ☁️ نشر على الخدمات السحابية

### **Railway.app**
```toml
# railway.toml
[build]
builder = "NIXPACKS"

[deploy]
healthcheckPath = "/api/health"
healthcheckTimeout = 300
restartPolicyType = "NEVER"

[[services]]
serviceName = "web"
source = "."
```

### **Vercel (Frontend فقط)**
```json
{
  "version": 2,
  "builds": [
    {
      "src": "package.json",
      "use": "@vercel/static-build",
      "config": {
        "distDir": "dist"
      }
    }
  ],
  "routes": [
    {
      "src": "/api/(.*)",
      "dest": "https://your-backend-url.com/api/$1"
    },
    {
      "src": "/(.*)",
      "dest": "/index.html"
    }
  ]
}
```

### **Heroku**
```json
{
  "name": "coffee-shop-system",
  "description": "نظام إدارة مقهى متطور",
  "stack": "heroku-22",
  "buildpacks": [
    {
      "url": "heroku/nodejs"
    }
  ],
  "addons": [
    {
      "plan": "mongolab:sandbox"
    }
  ],
  "env": {
    "NODE_ENV": "production"
  }
}
```

---

## 🔧 إعدادات الإنتاج

### **1. تحسين الأداء**
```javascript
// backend/config/production.js
module.exports = {
  // ضغط الردود
  compression: true,
  
  // Rate limiting
  rateLimit: {
    windowMs: 15 * 60 * 1000, // 15 دقيقة
    max: 100 // حد أقصى 100 طلب لكل IP
  },
  
  // CORS للإنتاج
  cors: {
    origin: process.env.FRONTEND_URL,
    credentials: true
  },
  
  // أمان إضافي
  security: {
    helmet: true,
    contentSecurityPolicy: true
  }
};
```

### **2. لوجز ومراقبة**
```javascript
// backend/middleware/logging.js
const winston = require('winston');

const logger = winston.createLogger({
  level: 'info',
  format: winston.format.combine(
    winston.format.timestamp(),
    winston.format.json()
  ),
  transports: [
    new winston.transports.File({ filename: 'error.log', level: 'error' }),
    new winston.transports.File({ filename: 'combined.log' })
  ]
});

if (process.env.NODE_ENV !== 'production') {
  logger.add(new winston.transports.Console({
    format: winston.format.simple()
  }));
}
```

---

## 📊 مراقبة النظام

### **Health Check Endpoint**
```javascript
// backend/routes/health.js
router.get('/health', async (req, res) => {
  try {
    // فحص قاعدة البيانات
    await mongoose.connection.db.admin().ping();
    
    // فحص الذاكرة
    const memoryUsage = process.memoryUsage();
    
    res.json({
      status: 'healthy',
      timestamp: new Date().toISOString(),
      uptime: process.uptime(),
      memory: {
        used: Math.round(memoryUsage.heapUsed / 1024 / 1024) + 'MB',
        total: Math.round(memoryUsage.heapTotal / 1024 / 1024) + 'MB'
      },
      database: 'connected'
    });
  } catch (error) {
    res.status(500).json({
      status: 'unhealthy',
      error: error.message
    });
  }
});
```

### **Monitoring Script**
```javascript
// scripts/monitor.js
const http = require('http');

function checkHealth() {
  const options = {
    hostname: 'localhost',
    port: 3001,
    path: '/api/health',
    method: 'GET',
    timeout: 5000
  };

  const req = http.request(options, (res) => {
    console.log(`✅ Health Check: ${res.statusCode}`);
  });

  req.on('error', (error) => {
    console.error(`❌ Health Check Failed: ${error.message}`);
  });

  req.end();
}

// فحص كل 30 ثانية
setInterval(checkHealth, 30000);
```

---

## 🔐 الأمان

### **1. HTTPS إجباري**
```javascript
// إجبار HTTPS
app.use((req, res, next) => {
  if (req.header('x-forwarded-proto') !== 'https') {
    res.redirect(`https://${req.header('host')}${req.url}`);
  } else {
    next();
  }
});
```

### **2. إعدادات الأمان**
```javascript
const helmet = require('helmet');

app.use(helmet({
  contentSecurityPolicy: {
    directives: {
      defaultSrc: ["'self'"],
      styleSrc: ["'self'", "'unsafe-inline'"],
      scriptSrc: ["'self'"],
      imgSrc: ["'self'", "data:", "https:"],
      connectSrc: ["'self'", "wss:", "ws:"]
    }
  }
}));
```

---

## 📱 إعداد PWA للإنتاج

### **1. Service Worker للإنتاج**
```javascript
// تحديد ملفات للـ Cache
const CACHE_NAME = 'coffee-shop-v2';
const urlsToCache = [
  '/',
  '/coffee-logo.svg',
  '/manifest.json',
  '/notification.wav',
  // إضافة ملفات CSS/JS المُجمعة
  '/assets/index-[hash].js',
  '/assets/index-[hash].css'
];
```

### **2. Manifest محدث**
```json
{
  "name": "نظام إدارة المقهى",
  "short_name": "Coffee System",
  "description": "نظام شامل لإدارة المقاهي والمطاعم",
  "start_url": "/",
  "display": "standalone",
  "background_color": "#ffffff",
  "theme_color": "#007bff",
  "icons": [
    {
      "src": "/coffee-logo-192.png",
      "sizes": "192x192",
      "type": "image/png"
    },
    {
      "src": "/coffee-logo-512.png", 
      "sizes": "512x512",
      "type": "image/png"
    }
  ]
}
```

---

## 🧪 اختبار الإنتاج

### **1. سكريبت اختبار شامل**
```javascript
// scripts/production-test.js
const axios = require('axios');

const tests = [
  {
    name: 'Health Check',
    url: '/api/health',
    method: 'GET'
  },
  {
    name: 'Auth Login',
    url: '/api/auth/login',
    method: 'POST',
    data: { username: 'admin', password: 'admin123' }
  },
  {
    name: 'Get Tables',
    url: '/api/table-accounts',
    method: 'GET'
  }
];

async function runTests() {
  for (const test of tests) {
    try {
      const response = await axios({
        method: test.method,
        url: `${process.env.API_URL}${test.url}`,
        data: test.data
      });
      
      console.log(`✅ ${test.name}: ${response.status}`);
    } catch (error) {
      console.error(`❌ ${test.name}: ${error.message}`);
    }
  }
}

runTests();
```

---

## 🚀 إطلاق النظام

### **الخطوات النهائية:**
1. ✅ رفع الملفات للخادم
2. ✅ ضبط قاعدة البيانات
3. ✅ تشغيل النظام
4. ✅ اختبار جميع الوظائف
5. ✅ إعداد النسخ الاحتياطي
6. ✅ تدريب المستخدمين

### **فحص نهائي:**
- [ ] النظام يعمل على HTTPS
- [ ] الإشعارات تعمل بشكل صحيح
- [ ] قاعدة البيانات متصلة
- [ ] جميع Routes تعمل
- [ ] Service Worker نشط
- [ ] PWA قابل للتثبيت

---

**🎉 مبروك! نظامك جاهز للإنتاج**

لأي استفسارات أو مشاكل، يرجى مراجعة logs الخادم أو استخدام أدوات المراقبة المدمجة.

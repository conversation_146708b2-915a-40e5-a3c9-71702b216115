/* تنسيقات شاشة إدارة القائمة - تصميم محسن ومعاصر */

/* الشاشة الأساسية */
.menu-screen {
  padding: 2rem;
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
  min-height: 100vh;
  direction: rtl;
}

/* رأس القسم */
.menu-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 2rem;
  background: linear-gradient(135deg, #27ae60 0%, #229954 100%);
  color: white;
  padding: 2rem;
  border-radius: 20px;
  box-shadow: 0 10px 30px rgba(39, 174, 96, 0.3);
  border: none;
  position: relative;
  overflow: hidden;
}

.menu-header::before {
  content: '';
  position: absolute;
  top: -50%;
  left: -50%;
  width: 200%;
  height: 200%;
  background: linear-gradient(45deg, transparent, rgba(255,255,255,0.1), transparent);
  transform: rotate(45deg);
  animation: shimmer 3s infinite;
}

.menu-header h1 {
  color: white;
  margin: 0;
  font-size: 2.2rem;
  display: flex;
  align-items: center;
  gap: 1rem;
  font-weight: 700;
  text-shadow: 0 2px 4px rgba(0,0,0,0.3);
  position: relative;
  z-index: 1;
}

.menu-header h1 i {
  font-size: 2.5rem;
  background: linear-gradient(45deg, #ffffff, #ecf0f1);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  filter: drop-shadow(0 2px 4px rgba(0,0,0,0.3));
}

/* أزرار الإجراءات */
.menu-actions {
  display: flex;
  gap: 1rem;
  align-items: center;
}

.add-product-btn {
  background: linear-gradient(135deg, #27ae60 0%, #2ecc71 100%);
  color: white;
  border: none;
  padding: 1rem 1.5rem;
  border-radius: 10px;
  cursor: pointer;
  font-size: 1rem;
  font-weight: 600;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  transition: all 0.3s ease;
  box-shadow: 0 4px 15px rgba(39, 174, 96, 0.2);
}

.add-product-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(39, 174, 96, 0.3);
  background: linear-gradient(135deg, #229954 0%, #27ae60 100%);
}

/* فلاتر القائمة */
.menu-filters {
  background: white;
  padding: 1.5rem;
  border-radius: 12px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  margin-bottom: 2rem;
}

.filters-row {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 1rem;
  align-items: end;
}

.filter-group {
  display: flex;
  flex-direction: column;
}

.filter-group label {
  color: #495057;
  font-weight: 600;
  margin-bottom: 0.5rem;
  font-size: 0.9rem;
}

.filter-group input,
.filter-group select {
  padding: 0.8rem;
  border: 2px solid #e9ecef;
  border-radius: 8px;
  font-size: 0.9rem;
  transition: border-color 0.3s ease;
}

.filter-group input:focus,
.filter-group select:focus {
  outline: none;
  border-color: #27ae60;
  box-shadow: 0 0 0 3px rgba(39, 174, 96, 0.1);
}

.search-input {
  position: relative;
}

.search-input i {
  position: absolute;
  left: 1rem;
  top: 50%;
  transform: translateY(-50%);
  color: #6c757d;
}

.search-input input {
  padding-left: 3rem;
}

/* شبكة المنتجات */
.products-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
  gap: 1.5rem;
  margin-bottom: 2rem;
}

/* شبكة عناصر القائمة */
.menu-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
  gap: 2rem;
  margin-bottom: 2rem;
}

/* بطاقة المنتج */
.product-card {
  background: white;
  border-radius: 12px;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
  border: 1px solid #e9ecef;
  overflow: hidden;
  position: relative;
}

.product-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

/* بطاقة عنصر القائمة المحسنة */
.menu-item-card {
  background: linear-gradient(145deg, #ffffff 0%, #f8f9fa 100%);
  border-radius: 20px;
  padding: 0;
  box-shadow: 0 10px 30px rgba(39, 174, 96, 0.15);
  transition: all 0.4s ease;
  border: none;
  position: relative;
  overflow: hidden;
  cursor: pointer;
}

.menu-item-card::before {
  content: '';
  position: absolute;
  top: 0;
  right: 0;
  left: 0;
  height: 5px;
  background: linear-gradient(90deg, #27ae60 0%, #2ecc71 50%, #16a085 100%);
  border-radius: 20px 20px 0 0;
}

.menu-item-card:hover {
  transform: translateY(-8px) scale(1.02);
  box-shadow: 0 20px 50px rgba(39, 174, 96, 0.3);
}

/* صورة المنتج */
.product-image {
  height: 200px;
  background: linear-gradient(135deg, #f8f9fa, #e9ecef);
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
  overflow: hidden;
}

.product-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.product-image .no-image {
  color: #6c757d;
  font-size: 3rem;
}

/* صورة عنصر القائمة */
.menu-item-image {
  width: 100%;
  height: 200px;
  background: linear-gradient(135deg, #27ae60, #2ecc71);
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 3rem;
  border-radius: 20px 20px 0 0;
  position: relative;
  overflow: hidden;
}

.menu-item-image::before {
  content: '';
  position: absolute;
  top: -50%;
  left: -50%;
  width: 200%;
  height: 200%;
  background: linear-gradient(45deg, transparent, rgba(255,255,255,0.2), transparent);
  transform: rotate(45deg);
  animation: imageShimmer 3s infinite;
}

@keyframes imageShimmer {
  0% { transform: translateX(-100%) translateY(-100%) rotate(45deg); }
  100% { transform: translateX(100%) translateY(100%) rotate(45deg); }
}

/* حالة المنتج */
.product-status {
  position: absolute;
  top: 1rem;
  right: 1rem;
  padding: 0.3rem 0.6rem;
  border-radius: 12px;
  font-size: 0.7rem;
  font-weight: 600;
}

.product-status.available {
  background: #d4edda;
  color: #155724;
  border: 1px solid #c3e6cb;
}

.product-status.unavailable {
  background: #f8d7da;
  color: #721c24;
  border: 1px solid #f5c6cb;
}

/* محتوى المنتج */
.product-content {
  padding: 1.5rem;
}

.product-name {
  color: #2c3e50;
  font-size: 1.2rem;
  font-weight: 600;
  margin-bottom: 0.5rem;
  line-height: 1.3;
}

.product-description {
  color: #6c757d;
  font-size: 0.9rem;
  line-height: 1.4;
  margin-bottom: 1rem;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.product-category {
  display: inline-block;
  background: #f8f9fa;
  color: #495057;
  padding: 0.25rem 0.5rem;
  border-radius: 12px;
  font-size: 0.8rem;
  margin-bottom: 1rem;
}

/* محتوى البطاقة */
.menu-item-content {
  padding: 1.5rem;
}

.menu-item-title {
  font-size: 1.3rem;
  font-weight: 700;
  color: #2c3e50;
  margin: 0 0 0.5rem 0;
}

.menu-item-description {
  color: #6c757d;
  font-size: 0.9rem;
  margin: 0 0 1rem 0;
  line-height: 1.5;
}

.menu-item-price {
  background: linear-gradient(135deg, #27ae60, #2ecc71);
  color: white;
  padding: 0.5rem 1rem;
  border-radius: 15px;
  font-weight: 700;
  font-size: 1.1rem;
  display: inline-block;
}

/* معلومات المنتج */
.product-details {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1rem;
}

.product-price {
  color: #27ae60;
  font-size: 1.3rem;
  font-weight: bold;
}

.product-stock {
  color: #6c757d;
  font-size: 0.9rem;
}

.product-stock.low {
  color: #e74c3c;
  font-weight: 600;
}

/* أزرار إجراءات المنتج */
.product-actions {
  display: flex;
  gap: 0.5rem;
}

.product-action-btn {
  flex: 1;
  padding: 0.6rem;
  border: none;
  border-radius: 6px;
  cursor: pointer;
  font-size: 0.8rem;
  font-weight: 600;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.3rem;
}

.edit-btn {
  background: #3498db;
  color: white;
}

.edit-btn:hover {
  background: #2980b9;
}

.delete-btn {
  background: #e74c3c;
  color: white;
}

.delete-btn:hover {
  background: #c0392b;
}

.toggle-btn {
  background: #f39c12;
  color: white;
}

.toggle-btn:hover {
  background: #e67e22;
}

/* إحصائيات القائمة */
.menu-stats {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 1rem;
  margin-bottom: 2rem;
}

.stat-card {
  background: white;
  padding: 1.5rem;
  border-radius: 12px;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
  text-align: center;
  border-left: 4px solid #27ae60;
}

.stat-card.total-products {
  border-left-color: #3498db;
}

.stat-card.available-products {
  border-left-color: #27ae60;
}

.stat-card.low-stock {
  border-left-color: #e74c3c;
}

.stat-card.categories {
  border-left-color: #f39c12;
}

.stat-card .stat-icon {
  font-size: 2.5rem;
  margin-bottom: 1rem;
  color: #27ae60;
}

.stat-card.total-products .stat-icon {
  color: #3498db;
}

.stat-card.available-products .stat-icon {
  color: #27ae60;
}

.stat-card.low-stock .stat-icon {
  color: #e74c3c;
}

.stat-card.categories .stat-icon {
  color: #f39c12;
}

.stat-card .stat-number {
  font-size: 2rem;
  font-weight: bold;
  color: #2c3e50;
  margin-bottom: 0.5rem;
}

.stat-card .stat-label {
  color: #6c757d;
  font-size: 0.9rem;
}

/* مودال إضافة/تعديل منتج */
.product-modal .modal-header {
  background: linear-gradient(135deg, #27ae60, #2ecc71);
}

.product-modal .modal-body {
  max-height: 70vh;
  overflow-y: auto;
}

.product-modal .form-group {
  margin-bottom: 1.5rem;
}

.product-modal label {
  display: block;
  margin-bottom: 0.5rem;
  color: #495057;
  font-weight: 600;
}

.product-modal input,
.product-modal select,
.product-modal textarea {
  width: 100%;
  padding: 0.8rem;
  border: 2px solid #e9ecef;
  border-radius: 8px;
  font-size: 1rem;
  transition: border-color 0.3s ease;
  font-family: inherit;
}

.product-modal textarea {
  resize: vertical;
  min-height: 100px;
}

.product-modal input:focus,
.product-modal select:focus,
.product-modal textarea:focus {
  outline: none;
  border-color: #27ae60;
  box-shadow: 0 0 0 3px rgba(39, 174, 96, 0.1);
}

.form-row {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 1rem;
}

.image-upload {
  border: 2px dashed #dee2e6;
  border-radius: 8px;
  padding: 2rem;
  text-align: center;
  transition: all 0.3s ease;
  cursor: pointer;
}

.image-upload:hover {
  border-color: #27ae60;
  background: #f8fff8;
}

.image-upload.dragover {
  border-color: #27ae60;
  background: #f8fff8;
}

.image-preview {
  max-width: 200px;
  max-height: 150px;
  border-radius: 8px;
  margin: 1rem auto;
}

/* رسالة فارغة */
.empty-menu {
  text-align: center;
  padding: 4rem 2rem;
  color: #6c757d;
}

.empty-menu i {
  font-size: 4rem;
  margin-bottom: 1rem;
  color: #dee2e6;
}

.empty-menu h3 {
  color: #495057;
  margin-bottom: 1rem;
}

/* تنسيقات الإحصائيات العامة للقائمة */
.menu-screen .stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 1.5rem;
  margin-bottom: 3rem;
  width: 100%;
  max-width: 100%;
  overflow-x: auto;
}

/* تنسيقات البحث للقائمة */
.menu-screen .search-container {
  position: relative;
  display: flex;
  align-items: center;
  background: white;
  border: 2px solid #e9ecef;
  border-radius: 12px;
  overflow: hidden;
  transition: all 0.3s ease;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

.menu-screen .search-container:hover {
  border-color: #27ae60;
  box-shadow: 0 4px 15px rgba(39, 174, 96, 0.15);
}

.menu-screen .search-container:focus-within {
  border-color: #27ae60;
  box-shadow: 0 0 0 3px rgba(39, 174, 96, 0.1);
}

.menu-screen .search-icon {
  padding: 0.7rem 1rem;
  color: #6c757d;
  background: #f8f9fa;
  border-right: 1px solid #e9ecef;
}

.menu-screen .search-input {
  border: none;
  outline: none;
  padding: 0.7rem 1rem;
  font-size: 0.9rem;
  min-width: 200px;
  background: transparent;
}

.menu-screen .search-input::placeholder {
  color: #adb5bd;
  font-style: italic;
}

.menu-screen .clear-search {
  padding: 0.5rem;
  background: none;
  border: none;
  color: #6c757d;
  cursor: pointer;
  transition: color 0.2s ease;
}

.menu-screen .clear-search:hover {
  color: #e74c3c;
}

/* تنسيقات المرشحات للقائمة */
.menu-screen .filter-select {
  padding: 0.7rem 1.2rem;
  border: 2px solid #e9ecef;
  border-radius: 12px;
  background: white;
  font-size: 0.9rem;
  font-weight: 500;
  min-width: 150px;
  color: #495057;
  transition: all 0.3s ease;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

.menu-screen .filter-select:hover {
  border-color: #27ae60;
  box-shadow: 0 4px 15px rgba(39, 174, 96, 0.15);
  transform: translateY(-1px);
}

.menu-screen .filter-select:focus {
  outline: none;
  border-color: #27ae60;
  box-shadow: 0 0 0 3px rgba(39, 174, 96, 0.1);
}

/* التصميم المتجاوب */
@media (max-width: 768px) {
  .menu-screen {
    padding: 1rem;
  }
  
  .menu-header {
    flex-direction: column;
    gap: 1rem;
    text-align: center;
  }
  
  .menu-header h1 {
    font-size: 1.5rem;
  }
  
  .menu-actions {
    flex-direction: column;
    width: 100%;
  }
  
  .add-product-btn {
    width: 100%;
    justify-content: center;
  }
  
  .filters-row {
    grid-template-columns: 1fr;
  }
  
  .products-grid {
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 1rem;
  }
  
  .menu-stats {
    grid-template-columns: repeat(2, 1fr);
  }
  
  .form-row {
    grid-template-columns: 1fr;
  }
}

@media (max-width: 480px) {
  .products-grid {
    grid-template-columns: 1fr;
  }
  
  .menu-stats {
    grid-template-columns: 1fr;
  }
  
  .product-actions {
    flex-direction: column;
    gap: 0.3rem;
  }
  
  .product-action-btn {
    width: 100%;
  }
}

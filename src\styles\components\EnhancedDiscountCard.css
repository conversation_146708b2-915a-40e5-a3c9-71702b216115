/* ??????? ????????? ??????? */
@import '../variables/discount-variables.css';

/* Enhanced Discount Card Styles */

.discount-card-premium {
  background: linear-gradient(145deg, #ffffff 0%, #f8f9fa 100%);
  border-radius: 16px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  transition: all 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
  border: none;
  position: relative;
  overflow: hidden;
  min-height: 280px;
  cursor: pointer;
}

.discount-card-premium::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.1), rgba(255, 255, 255, 0.05));
  pointer-events: none;
  transition: opacity 0.3s ease;
}

.discount-card-premium:hover {
  transform: translateY(-12px) scale(1.02);
  box-shadow: 0 25px 80px rgba(0, 0, 0, 0.25);
}

.discount-card-premium:hover::before {
  opacity: 0.8;
}

/* Status Bar */
.discount-status-bar {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  border-radius: 20px 20px 0 0;
}

.discount-status-bar.pending {
  background: linear-gradient(90deg, #f39c12, #e67e22);
}

.discount-status-bar.approved {
  background: linear-gradient(90deg, #27ae60, #2ecc71);
}

.discount-status-bar.rejected {
  background: linear-gradient(90deg, #e74c3c, #c0392b);
}

/* Floating Discount Indicator */
.floating-discount-indicator {
  position: absolute;
  top: 12px;
  right: 12px;
  width: 50px;
  height: 50px;
  border-radius: 50%;
  background: linear-gradient(135deg, #667eea, #764ba2);
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  color: white;
  font-weight: 700;
  z-index: 3;
  box-shadow: 0 6px 20px rgba(102, 126, 234, 0.3);
  transition: all 0.3s ease;
}

.discount-card-premium:hover .floating-discount-indicator {
  transform: scale(1.1) rotate(5deg);
  box-shadow: 0 8px 25px rgba(102, 126, 234, 0.4);
}

.floating-discount-indicator i {
  font-size: 1rem;
  margin-bottom: 2px;
}

.floating-discount-indicator span {
  font-size: 0.7rem;
  font-weight: 800;
}

/* Card Content */
.discount-card-content {
  padding: 1rem;
  height: 100%;
  display: flex;
  flex-direction: column;
}

/* Header */
.discount-card-header {
  text-align: center;
  margin-bottom: 1rem;
}

.discount-order-icon {
  width: 50px;
  height: 50px;
  margin: 0 auto 0.75rem;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, #667eea, #764ba2);
  font-size: 1.5rem;
  color: white;
  box-shadow: 0 6px 20px rgba(102, 126, 234, 0.3);
  transition: all 0.3s ease;
}

.discount-card-premium:hover .discount-order-icon {
  transform: scale(1.1) rotate(-5deg);
  box-shadow: 0 8px 25px rgba(102, 126, 234, 0.4);
}

.discount-order-number {
  font-size: 1.1rem;
  font-weight: 700;
  color: #2c3e50;
  margin-bottom: 0.5rem;
}

.discount-status-badge {
  display: inline-flex;
  align-items: center;
  gap: 0.4rem;
  padding: 0.4rem 0.8rem;
  border-radius: 16px;
  font-weight: 600;
  font-size: 0.8rem;
  margin-bottom: 0.4rem;
}

.discount-status-badge.pending {
  background: linear-gradient(135deg, #fff3cd, #ffeaa7);
  color: #856404;
  border: 1px solid #ffeaa7;
}

.discount-status-badge.approved {
  background: linear-gradient(135deg, #d4edda, #c3e6cb);
  color: #155724;
  border: 1px solid #c3e6cb;
}

.discount-status-badge.rejected {
  background: linear-gradient(135deg, #f8d7da, #f5c6cb);
  color: #721c24;
  border: 1px solid #f5c6cb;
}

.discount-timestamp {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
  color: #6c757d;
  font-size: 0.85rem;
}

/* Amount Section */
.discount-amount-section {
  background: linear-gradient(135deg, rgba(102, 126, 234, 0.1), rgba(118, 75, 162, 0.05));
  border-radius: 12px;
  padding: 0.75rem;
  margin-bottom: 0.75rem;
  border: 1px solid rgba(102, 126, 234, 0.2);
}

.amount-display {
  display: flex;
  align-items: center;
  gap: 0.6rem;
  margin-bottom: 0.6rem;
}

.amount-icon {
  width: 35px;
  height: 35px;
  border-radius: 50%;
  background: linear-gradient(135deg, #667eea, #764ba2);
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 0.9rem;
}

.amount-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
}

.amount-label {
  font-size: 0.8rem;
  color: #6c757d;
  font-weight: 500;
}

.amount-value {
  font-size: 1.1rem;
  font-weight: 700;
  color: #667eea;
}

.amount-breakdown {
  display: flex;
  flex-direction: column;
  gap: 0.4rem;
}

.breakdown-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0.4rem;
  background: rgba(255, 255, 255, 0.7);
  border-radius: 6px;
}

.breakdown-label {
  font-size: 0.85rem;
  color: #6c757d;
  font-weight: 500;
}

.breakdown-value {
  font-size: 0.9rem;
  font-weight: 600;
  color: #2c3e50;
}

.breakdown-value.final-amount {
  color: #27ae60;
  font-weight: 700;
  font-size: 1rem;
}

/* Waiter Section */
.discount-waiter-section {
  margin-bottom: 1rem;
}

.waiter-info {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  padding: 0.75rem;
  background: rgba(248, 249, 250, 0.8);
  border-radius: 12px;
  border: 1px solid #e9ecef;
}

.waiter-icon {
  width: 35px;
  height: 35px;
  border-radius: 50%;
  background: linear-gradient(135deg, #3498db, #2980b9);
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 0.9rem;
}

.waiter-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
}

.waiter-label {
  font-size: 0.75rem;
  color: #6c757d;
  font-weight: 500;
}

.waiter-name {
  font-size: 0.9rem;
  font-weight: 600;
  color: #2c3e50;
}

/* Reason Section */
.discount-reason-section {
  margin-bottom: 1rem;
}

.reason-header {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  margin-bottom: 0.5rem;
  color: #6c757d;
  font-weight: 600;
  font-size: 0.85rem;
}

.reason-content {
  background: rgba(248, 249, 250, 0.8);
  border-radius: 10px;
  padding: 0.75rem;
  border: 1px solid #e9ecef;
}

.reason-content p {
  margin: 0;
  font-size: 0.9rem;
  color: #2c3e50;
  line-height: 1.4;
  font-style: italic;
}

/* Action Buttons */
.discount-actions {
  display: flex;
  gap: 0.5rem;
  margin-top: auto;
}

.discount-action-btn {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 0.25rem;
  padding: 0.75rem 0.5rem;
  border: 2px solid transparent;
  border-radius: 12px;
  background: transparent;
  transition: all 0.3s ease;
  cursor: pointer;
  font-weight: 600;
  font-size: 0.8rem;
}

.discount-action-btn:hover:not(:disabled) {
  transform: translateY(-2px);
}

.discount-action-btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  transform: none !important;
}

.discount-action-btn.details {
  color: #3498db;
  border-color: rgba(52, 152, 219, 0.3);
  background: rgba(52, 152, 219, 0.05);
}

.discount-action-btn.details:hover {
  background: rgba(52, 152, 219, 0.1);
  border-color: #3498db;
}

.discount-action-btn.approve {
  color: #27ae60;
  border-color: rgba(39, 174, 96, 0.3);
  background: rgba(39, 174, 96, 0.05);
}

.discount-action-btn.approve:hover {
  background: rgba(39, 174, 96, 0.1);
  border-color: #27ae60;
}

.discount-action-btn.reject {
  color: #e74c3c;
  border-color: rgba(231, 76, 60, 0.3);
  background: rgba(231, 76, 60, 0.05);
}

.discount-action-btn.reject:hover {
  background: rgba(231, 76, 60, 0.1);
  border-color: #e74c3c;
}

.discount-action-btn i {
  font-size: 1rem;
}

.discount-action-btn span {
  font-size: 0.75rem;
  font-weight: 600;
}

/* Status Message */
.discount-status-message {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  padding: 0.75rem;
  border-radius: 12px;
  margin-top: 1rem;
}

.discount-status-message.approved {
  background: linear-gradient(135deg, rgba(39, 174, 96, 0.1), rgba(46, 204, 113, 0.05));
  border: 1px solid rgba(39, 174, 96, 0.3);
}

.discount-status-message.rejected {
  background: linear-gradient(135deg, rgba(231, 76, 60, 0.1), rgba(192, 57, 43, 0.05));
  border: 1px solid rgba(231, 76, 60, 0.3);
}

.status-icon {
  width: 30px;
  height: 30px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 0.9rem;
}

.discount-status-message.approved .status-icon {
  background: linear-gradient(135deg, #27ae60, #2ecc71);
}

.discount-status-message.rejected .status-icon {
  background: linear-gradient(135deg, #e74c3c, #c0392b);
}

.status-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
}

.status-text {
  font-size: 0.9rem;
  font-weight: 600;
  color: #2c3e50;
}

.status-date {
  font-size: 0.75rem;
  color: #6c757d;
}

/* Responsive Design */
@media (max-width: 768px) {
  .discount-card-premium {
    min-height: 320px;
  }

  .discount-card-content {
    padding: 1rem;
  }

  .floating-discount-indicator {
    width: 50px;
    height: 50px;
    top: 12px;
    right: 12px;
  }

  .floating-discount-indicator i {
    font-size: 1rem;
  }

  .floating-discount-indicator span {
    font-size: 0.7rem;
  }

  .discount-order-icon {
    width: 50px;
    height: 50px;
    font-size: 1.5rem;
  }

  .discount-order-number {
    font-size: 1.2rem;
  }

  .discount-status-badge {
    padding: 0.4rem 0.8rem;
    font-size: 0.8rem;
  }

  .amount-value {
    font-size: 1.2rem;
  }

  .discount-actions {
    flex-direction: column;
    gap: 0.75rem;
  }

  .discount-action-btn {
    flex-direction: row;
    justify-content: center;
    gap: 0.5rem;
    padding: 0.6rem 1rem;
  }
}

@media (max-width: 480px) {
  .discount-card-premium {
    min-height: 300px;
  }

  .discount-card-content {
    padding: 0.75rem;
  }

  .floating-discount-indicator {
    width: 45px;
    height: 45px;
    top: 10px;
    right: 10px;
  }

  .floating-discount-indicator i {
    font-size: 0.9rem;
  }

  .floating-discount-indicator span {
    font-size: 0.65rem;
  }

  .discount-order-icon {
    width: 45px;
    height: 45px;
    font-size: 1.3rem;
  }

  .discount-order-number {
    font-size: 1.1rem;
  }

  .discount-card-header {
    margin-bottom: 1rem;
  }

  .discount-amount-section {
    padding: 0.75rem;
    margin-bottom: 0.75rem;
  }

  .amount-icon {
    width: 35px;
    height: 35px;
    font-size: 0.9rem;
  }

  .amount-value {
    font-size: 1.1rem;
  }

  .waiter-info {
    padding: 0.6rem;
  }

  .waiter-icon {
    width: 30px;
    height: 30px;
    font-size: 0.8rem;
  }

  .reason-content {
    padding: 0.6rem;
  }

  .discount-action-btn {
    padding: 0.5rem 0.8rem;
    font-size: 0.75rem;
  }

  .discount-action-btn i {
    font-size: 0.9rem;
  }

  .discount-status-message {
    padding: 0.6rem;
  }

  .status-icon {
    width: 25px;
    height: 25px;
    font-size: 0.8rem;
  }
}


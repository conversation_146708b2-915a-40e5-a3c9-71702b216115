import React from 'react';

interface BootstrapHeaderProps {
  user?: any;
  onLogout?: () => void;
  onToggleSidebar?: () => void;
  sidebarOpen?: boolean;
}

const BootstrapHeader: React.FC<BootstrapHeaderProps> = ({
  user,
  onLogout,
  onToggleSidebar,
  sidebarOpen
}) => {
  return (
    <header className="app-header">
      <div className="container-fluid">
        <div className="row align-items-center header-content">
          {/* عمود الأزرار الجانبية */}
          <div className="col-auto">
            <button
              className="sidebar-toggle btn"
              onClick={onToggleSidebar}
              type="button"
              aria-label="تبديل القائمة الجانبية"
            >
              <i className={`fas ${sidebarOpen ? 'fa-times' : 'fa-bars'}`}></i>
            </button>
          </div>

          {/* عمود العنوان */}
          <div className="col">
            <h1 className="app-title mb-0">
              <i className="fas fa-coffee"></i>
              نظام إدارة المقهى
            </h1>
          </div>

          {/* عمود معلومات المستخدم */}
          <div className="col-auto">
            <div className="d-flex align-items-center gap-3">
              {user && (
                <div className="text-end d-none d-sm-block">
                  <div className="fw-bold">{user.name || user.username}</div>
                  <small className="text-light opacity-75">
                    {user.role === 'manager' ? 'مدير' : user.role}
                  </small>
                </div>
              )}
              
              <button
                className="btn btn-outline-light btn-sm"
                onClick={onLogout}
                type="button"
              >
                <i className="fas fa-sign-out-alt me-2"></i>
                <span className="d-none d-sm-inline">تسجيل الخروج</span>
              </button>
            </div>
          </div>
        </div>
      </div>
    </header>
  );
};

export default BootstrapHeader;

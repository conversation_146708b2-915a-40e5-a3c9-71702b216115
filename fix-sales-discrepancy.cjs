// إصلاح التباين في مبيعات النُدُل - Fix Sales Discrepancy
const { MongoClient, ObjectId } = require('mongodb');
require('dotenv').config();

const uri = process.env.MONGODB_URI;

async function fixSalesDiscrepancy() {
    const client = new MongoClient(uri);
    
    try {
        await client.connect();
        console.log('🔧 بدء إصلاح التباين في مبيعات النُدُل...');
        console.log('🔗 متصل بقاعدة البيانات');
        
        const db = client.db('coffee_shop');
        const ordersCollection = db.collection('orders');
        const usersCollection = db.collection('users');
        
        let fixedCount = 0;
        let totalFixed = 0;
        
        // الخطوة 1: إصلاح الطلبات بدون نادل
        console.log('\n📋 الخطوة 1: إصلاح الطلبات بدون نادل...');
        
        const ordersWithoutWaiter = await ordersCollection.find({
            status: 'completed',
            $or: [
                { waiter: { $exists: false } },
                { waiter: null },
                { waiter: '' }
            ]
        }).toArray();
        
        console.log(`🔍 وُجد ${ordersWithoutWaiter.length} طلب بدون نادل`);
        
        // الحصول على نادل افتراضي (أول نادل نشط)
        const defaultWaiter = await usersCollection.findOne({ 
            role: 'waiter', 
            status: 'active' 
        });
        
        if (defaultWaiter && ordersWithoutWaiter.length > 0) {
            console.log(`👤 سيتم تعيين النادل الافتراضي: ${defaultWaiter.name} (${defaultWaiter.username})`);
            
            for (const order of ordersWithoutWaiter) {
                await ordersCollection.updateOne(
                    { _id: order._id },
                    { 
                        $set: { 
                            waiter: defaultWaiter.username,
                            updatedAt: new Date(),
                            fixedBy: 'system_repair',
                            fixedAt: new Date()
                        } 
                    }
                );
                
                fixedCount++;
                totalFixed += order.total || 0;
                console.log(`  ✅ طلب ${order._id}: ${order.total} جنيه - تم تعيين ${defaultWaiter.username}`);
            }
        }
        
        // الخطوة 2: إصلاح الطلبات بنُدُل غير موجودين
        console.log('\n📋 الخطوة 2: إصلاح الطلبات بنُدُل غير موجودين...');
        
        const waiters = await usersCollection.find({ role: 'waiter' }).toArray();
        const waiterUsernames = waiters.map(w => w.username);
        
        const ordersWithInvalidWaiter = await ordersCollection.find({
            status: 'completed',
            waiter: { $exists: true, $ne: null, $ne: '' },
            waiter: { $nin: waiterUsernames }
        }).toArray();
        
        console.log(`🔍 وُجد ${ordersWithInvalidWaiter.length} طلب بنُدُل غير موجودين`);
        
        if (defaultWaiter && ordersWithInvalidWaiter.length > 0) {
            for (const order of ordersWithInvalidWaiter) {
                const oldWaiter = order.waiter;
                
                await ordersCollection.updateOne(
                    { _id: order._id },
                    { 
                        $set: { 
                            waiter: defaultWaiter.username,
                            oldWaiter: oldWaiter,
                            updatedAt: new Date(),
                            fixedBy: 'system_repair',
                            fixedAt: new Date()
                        } 
                    }
                );
                
                fixedCount++;
                totalFixed += order.total || 0;
                console.log(`  ✅ طلب ${order._id}: ${order.total} جنيه - تم تغيير من ${oldWaiter} إلى ${defaultWaiter.username}`);
            }
        }
        
        // الخطوة 3: التحقق من النتائج بعد الإصلاح
        console.log('\n📊 التحقق من النتائج بعد الإصلاح...');
        
        // حساب إجمالي المبيعات مرة أخرى
        const totalSalesResult = await ordersCollection.aggregate([
            {
                $match: {
                    status: 'completed',
                    total: { $exists: true, $ne: null }
                }
            },
            {
                $group: {
                    _id: null,
                    totalSales: { $sum: '$total' },
                    orderCount: { $sum: 1 }
                }
            }
        ]).toArray();
        
        const totalSales = totalSalesResult[0]?.totalSales || 0;
        
        // حساب مجموع مبيعات النُدُل بعد الإصلاح
        let waiterSalesTotal = 0;
        
        for (const waiter of waiters) {
            const waiterSales = await ordersCollection.aggregate([
                {
                    $match: {
                        waiter: waiter.username,
                        status: 'completed',
                        total: { $exists: true, $ne: null }
                    }
                },
                {
                    $group: {
                        _id: null,
                        sales: { $sum: '$total' }
                    }
                }
            ]).toArray();
            
            waiterSalesTotal += waiterSales[0]?.sales || 0;
        }
        
        const newDiscrepancy = totalSales - waiterSalesTotal;
        
        console.log('\n📈 النتائج النهائية:');
        console.log('=' .repeat(50));
        console.log(`💰 إجمالي المبيعات: ${totalSales.toFixed(2)} جنيه`);
        console.log(`👥 مجموع مبيعات النُدُل: ${waiterSalesTotal.toFixed(2)} جنيه`);
        console.log(`📊 التباين الجديد: ${newDiscrepancy.toFixed(2)} جنيه`);
        console.log(`🔧 عدد الطلبات المُصلحة: ${fixedCount}`);
        console.log(`💵 قيمة المبيعات المُصلحة: ${totalFixed.toFixed(2)} جنيه`);
        
        if (Math.abs(newDiscrepancy) <= 1) {
            console.log('\n✅ تم إصلاح التباين بنجاح!');
        } else {
            console.log('\n⚠️  لا يزال هناك تباين يحتاج إلى مراجعة إضافية');
        }
        
        // الخطوة 4: إنشاء تقرير الإصلاح
        const repairReport = {
            timestamp: new Date(),
            fixedOrders: fixedCount,
            totalValueFixed: totalFixed,
            discrepancyBefore: 'يحتاج حساب',
            discrepancyAfter: newDiscrepancy,
            defaultWaiterUsed: defaultWaiter?.username || 'none',
            status: Math.abs(newDiscrepancy) <= 1 ? 'success' : 'partial'
        };
        
        // حفظ تقرير الإصلاح
        await db.collection('repair_reports').insertOne(repairReport);
        console.log('\n📋 تم حفظ تقرير الإصلاح في قاعدة البيانات');
        
    } catch (error) {
        console.error('❌ خطأ في إصلاح التباين:', error);
    } finally {
        await client.close();
    }
}

fixSalesDiscrepancy();

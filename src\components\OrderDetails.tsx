import React from 'react';
import './OrderDetails.css';
import Button from './Button';
import type { Order, OrderItem } from '../types/Order';
import { getOrderTotal, formatOrderPrice } from '../types/Order';

// تم نقل interfaces إلى src/types/Order.ts

interface OrderDetailsProps {
  order: Order;
  onStatusChange?: (orderId: string, newStatus: Order['status']) => void;
  userRole?: 'waiter' | 'chef' | 'manager';
}

const OrderDetails: React.FC<OrderDetailsProps> = ({
  order,
  onStatusChange,
  userRole = 'manager'
}) => {
  const getStatusColor = (status: Order['status']) => {
    switch (status) {
      case 'pending':
        return '#ff9800';
      case 'preparing':
        return '#2196f3';
      case 'ready':
        return '#4caf50';
      case 'delivered':
        return '#8bc34a';
      case 'cancelled':
        return '#f44336';
      default:
        return '#757575';
    }
  };

  const getStatusText = (status: Order['status']) => {
    switch (status) {
      case 'pending':
        return 'في الانتظار';
      case 'preparing':
        return 'قيد التحضير';
      case 'ready':
        return 'جاهز';
      case 'delivered':
        return 'تم التسليم';
      case 'cancelled':
        return 'ملغي';
      default:
        return status;
    }
  };

  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleString('ar-SA', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  const handleStatusChange = (newStatus: Order['status']) => {
    if (onStatusChange && order._id) {
      onStatusChange(order._id, newStatus);
    }
  };

  return (
    <div className="order-details">
      {/* معلومات الطلب الأساسية */}
      <div className="order-header">
        <div className="order-info-grid">
          <div className="info-item">
            <span className="info-label">رقم الطلب:</span>
            <span className="info-value order-number">#{order.orderNumber}</span>
          </div>

          <div className="info-item">
            <span className="info-label">اسم النادل:</span>
            <span className="info-value">{'waiterName' in order ? order.waiterName : '-'}</span>
          </div>

          <div className="info-item">
            <span className="info-label">حالة الطلب:</span>
            <span
              className="info-value status-badge"
              style={{ backgroundColor: getStatusColor(order.status) }}
            >
              {getStatusText(order.status)}
            </span>
          </div>

          <div className="info-item">
            <span className="info-label">المجموع الكلي:</span>
            <span className="info-value total-price">{formatOrderPrice(order, 'جنيه')}</span>
          </div>

          { 'tableNumber' in order && order.tableNumber && (
            <div className="info-item">
              <span className="info-label">رقم الطاولة:</span>
              <span className="info-value">{order.tableNumber}</span>
            </div>
          )}

          { 'customerName' in order && order.customerName && (
            <div className="info-item">
              <span className="info-label">اسم العميل:</span>
              <span className="info-value">{order.customerName}</span>
            </div>
          )}

          { 'preparationTime' in order && order.preparationTime && (
            <div className="info-item">
              <span className="info-label">وقت التحضير:</span>
              <span className="info-value">{order.preparationTime} دقيقة</span>
            </div>
          )}

          <div className="info-item">
            <span className="info-label">وقت الطلب:</span>
            <span className="info-value">{order.createdAt ? formatDate(order.createdAt) : '-'}</span>
          </div>

          {order.updatedAt && order.updatedAt !== order.createdAt && (
            <div className="info-item">
              <span className="info-label">آخر تحديث:</span>
              <span className="info-value">{formatDate(order.updatedAt)}</span>
            </div>
          )}
        </div>
      </div>

      {/* تفاصيل الأصناف */}
      <div className="order-items">
        <h3>تفاصيل الطلب</h3>
        <div className="items-list">
          {order.items.map((item, index) => (
            <div key={item.id || index} className="item-card">
              <div className="item-info">
                <div className="item-name">{item.name}</div>
                <div className="item-details">
                  <span className="item-quantity">الكمية: {item.quantity}</span>
                  <span className="item-price">السعر: {item.price} جنيه</span>
                  <span className="item-total">المجموع: {item.quantity * item.price} جنيه</span>
                </div>
                {item.notes && (
                  <div className="item-notes">
                    <i className="fas fa-sticky-note"></i>
                    ملاحظات: {item.notes}
                  </div>
                )}
              </div>
            </div>
          ))}
        </div>
      </div>

      {/* ملاحظات الطلب */}
      { 'notes' in order && order.notes && (
        <div className="order-notes">
          <h3>ملاحظات الطلب</h3>
          <div className="notes-content">
            <i className="fas fa-comment-alt"></i>
            {typeof order.notes === 'string' ? order.notes : '-'}
          </div>
        </div>
      )}

      {/* أزرار تغيير الحالة */}
      {onStatusChange && (
        <div className="status-actions">
          <h3>تغيير حالة الطلب</h3>
          <div className="status-buttons">
            {userRole === 'chef' && order.status === 'pending' && (
              <Button
                variant="info"
                onClick={() => handleStatusChange('preparing')}
                icon="fas fa-play"
              >
                بدء التحضير
              </Button>
            )}

            {userRole === 'chef' && order.status === 'preparing' && (
              <Button
                variant="success"
                onClick={() => handleStatusChange('ready')}
                icon="fas fa-check"
              >
                جاهز للتسليم
              </Button>
            )}

            {userRole === 'waiter' && order.status === 'ready' && (
              <Button
                variant="success"
                onClick={() => handleStatusChange('delivered')}
                icon="fas fa-truck"
              >
                تم التسليم
              </Button>
            )}

            {userRole === 'manager' && order.status !== 'cancelled' && order.status !== 'delivered' && (
              <Button
                variant="error"
                onClick={() => handleStatusChange('cancelled')}
                icon="fas fa-times"
              >
                إلغاء الطلب
              </Button>
            )}
          </div>
        </div>
      )}
    </div>
  );
};

export default OrderDetails;

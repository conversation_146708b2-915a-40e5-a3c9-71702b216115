const mongoose = require('mongoose');
require('dotenv').config();

// تعريف نموذج المنتج مباشرة
const productSchema = new mongoose.Schema({
  name: String,
  price: Number,
  available: Boolean,
  stock: {
    quantity: { type: Number, default: 100 },
    unit: { type: String, default: 'قطعة' },
    lowStockAlert: { type: Number, default: 5 }
  }
}, { collection: 'products' });

const Product = mongoose.model('Product', productSchema);

// دالة الاتصال بقاعدة البيانات
const connectDB = async () => {
  try {
    const mongoURI = process.env.MONGODB_URI;
    console.log('🔗 الاتصال بقاعدة البيانات...');
    
    await mongoose.connect(mongoURI, {
      serverSelectionTimeoutMS: 10000,
      socketTimeoutMS: 20000
    });
    
    console.log('✅ تم الاتصال بنجاح');
  } catch (error) {
    console.error('❌ خطأ في الاتصال:', error.message);
    throw error;
  }
};

// دالة إصلاح المخزون
const fixStock = async () => {
  try {
    console.log('🔧 بدء إصلاح المخزون...');
    
    // البحث عن المنتجات التي لديها مشاكل في المخزون
    const products = await Product.find({}).limit(10);
    console.log(`📦 تم العثور على ${products.length} منتج`);
    
    if (products.length === 0) {
      console.log('❌ لا توجد منتجات');
      return;
    }
    
    let fixedCount = 0;
    
    for (const product of products) {
      try {
        console.log(`\n🔍 فحص المنتج: ${product.name}`);
        
        // التحقق من بنية المخزون
        let needsUpdate = false;
        let newStock = {
          quantity: 100,
          unit: 'قطعة',
          lowStockAlert: 5
        };
        
        if (!product.stock) {
          console.log('  ❌ المخزون غير موجود - سيتم إنشاؤه');
          needsUpdate = true;
        } else if (typeof product.stock === 'number') {
          console.log(`  🔄 المخزون رقم (${product.stock}) - سيتم تحويله لكائن`);
          newStock.quantity = product.stock;
          needsUpdate = true;
        } else if (typeof product.stock === 'object') {
          if (product.stock.quantity === undefined || product.stock.quantity === null) {
            console.log('  ❌ quantity غير موجود - سيتم إضافته');
            needsUpdate = true;
          } else {
            console.log(`  ✅ المخزون سليم: ${product.stock.quantity} ${product.stock.unit || 'قطعة'}`);
            newStock = {
              quantity: product.stock.quantity,
              unit: product.stock.unit || 'قطعة',
              lowStockAlert: product.stock.lowStockAlert || 5
            };
          }
        }
        
        if (needsUpdate) {
          await Product.updateOne(
            { _id: product._id },
            { $set: { stock: newStock } }
          );
          console.log(`  ✅ تم إصلاح المخزون: ${newStock.quantity} ${newStock.unit}`);
          fixedCount++;
        }
        
      } catch (error) {
        console.error(`  ❌ خطأ في إصلاح ${product.name}:`, error.message);
      }
    }
    
    console.log(`\n🎉 تم إصلاح ${fixedCount} منتج من أصل ${products.length}`);
    
    // عرض عينة من المنتجات بعد الإصلاح
    console.log('\n📊 عينة من المنتجات بعد الإصلاح:');
    const updatedProducts = await Product.find({}).limit(5).select('name stock');
    
    updatedProducts.forEach((product, index) => {
      console.log(`${index + 1}. ${product.name}:`);
      if (product.stock && typeof product.stock === 'object') {
        console.log(`   المخزون: ${product.stock.quantity} ${product.stock.unit}`);
      } else {
        console.log(`   المخزون: ${product.stock} (نوع: ${typeof product.stock})`);
      }
    });
    
  } catch (error) {
    console.error('❌ خطأ في إصلاح المخزون:', error.message);
  }
};

// دالة تحديث مخزون منتج معين
const updateSpecificProduct = async (productName, newQuantity) => {
  try {
    console.log(`\n🎯 تحديث مخزون "${productName}" إلى ${newQuantity}...`);
    
    const product = await Product.findOne({ 
      name: { $regex: new RegExp(productName, 'i') } 
    });
    
    if (!product) {
      console.log('❌ المنتج غير موجود');
      return;
    }
    
    await Product.updateOne(
      { _id: product._id },
      { 
        $set: { 
          'stock.quantity': parseInt(newQuantity),
          'stock.unit': 'قطعة',
          'stock.lowStockAlert': 5
        } 
      }
    );
    
    console.log('✅ تم التحديث بنجاح');
    
    // التحقق من النتيجة
    const updatedProduct = await Product.findById(product._id);
    console.log(`📊 المخزون الجديد: ${updatedProduct.stock.quantity} ${updatedProduct.stock.unit}`);
    
  } catch (error) {
    console.error('❌ خطأ في التحديث:', error.message);
  }
};

// الدالة الرئيسية
const main = async () => {
  try {
    await connectDB();
    
    console.log('\n🛠️  أداة إصلاح المخزون السريعة');
    console.log('=' .repeat(40));
    
    // إصلاح جميع المنتجات
    await fixStock();
    
    // مثال على تحديث منتج معين (اختياري)
    // await updateSpecificProduct('قهوة', 50);
    
    console.log('\n✅ تم الانتهاء من جميع العمليات');
    
  } catch (error) {
    console.error('❌ خطأ عام:', error.message);
  } finally {
    await mongoose.disconnect();
    console.log('🔌 تم قطع الاتصال بقاعدة البيانات');
    process.exit(0);
  }
};

// تشغيل الأداة
if (require.main === module) {
  main();
}

module.exports = { fixStock, updateSpecificProduct };
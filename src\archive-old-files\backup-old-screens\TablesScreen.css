/* تنسيقات شاشة إدارة الطاولات - تصميم محسن ومعاصر */

/* الشاشة الأساسية */
.tables-screen {
  padding: 2rem;
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
  min-height: 100vh;
  direction: rtl;
}

/* رأس القسم */
.tables-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 2rem;
  background: linear-gradient(135deg, #e67e22 0%, #d35400 100%);
  color: white;
  padding: 2rem;
  border-radius: 20px;
  box-shadow: 0 10px 30px rgba(230, 126, 34, 0.3);
  border: none;
  position: relative;
  overflow: hidden;
}

.tables-header::before {
  content: '';
  position: absolute;
  top: -50%;
  left: -50%;
  width: 200%;
  height: 200%;
  background: linear-gradient(45deg, transparent, rgba(255,255,255,0.1), transparent);
  transform: rotate(45deg);
  animation: shimmer 3s infinite;
}

.tables-header h1 {
  color: white;
  margin: 0;
  font-size: 2.2rem;
  display: flex;
  align-items: center;
  gap: 1rem;
  font-weight: 700;
  text-shadow: 0 2px 4px rgba(0,0,0,0.3);
  position: relative;
  z-index: 1;
}

.tables-header h1 i {
  font-size: 2.5rem;
  background: linear-gradient(45deg, #ffffff, #ecf0f1);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  filter: drop-shadow(0 2px 4px rgba(0,0,0,0.3));
}

/* أزرار الإجراءات */
.tables-actions {
  display: flex;
  gap: 1rem;
  align-items: center;
}

.add-table-btn {
  background: linear-gradient(135deg, #e67e22 0%, #f39c12 100%);
  color: white;
  border: none;
  padding: 1rem 1.5rem;
  border-radius: 10px;
  cursor: pointer;
  font-size: 1rem;
  font-weight: 600;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  transition: all 0.3s ease;
  box-shadow: 0 4px 15px rgba(230, 126, 34, 0.2);
}

.add-table-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(230, 126, 34, 0.3);
  background: linear-gradient(135deg, #d35400 0%, #e67e22 100%);
}

.reset-tables-btn {
  background: linear-gradient(135deg, #e74c3c 0%, #c0392b 100%);
  color: white;
  border: none;
  padding: 1rem 1.5rem;
  border-radius: 10px;
  cursor: pointer;
  font-size: 1rem;
  font-weight: 600;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  transition: all 0.3s ease;
  box-shadow: 0 4px 15px rgba(231, 76, 60, 0.2);
}

.reset-tables-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(231, 76, 60, 0.3);
  background: linear-gradient(135deg, #c0392b 0%, #a93226 100%);
}

/* شبكة الطاولات */
.tables-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
  gap: 1.5rem;
  margin-bottom: 2rem;
}

/* بطاقة الطاولة المحسنة */
.table-card {
  background: linear-gradient(145deg, #ffffff 0%, #f8f9fa 100%);
  border-radius: 20px;
  padding: 2rem;
  box-shadow: 0 10px 30px rgba(230, 126, 34, 0.15);
  transition: all 0.4s ease;
  border: none;
  position: relative;
  text-align: center;
  cursor: pointer;
  overflow: hidden;
}

.table-card::before {
  content: '';
  position: absolute;
  top: 0;
  right: 0;
  left: 0;
  height: 5px;
  background: linear-gradient(90deg, #e67e22 0%, #d35400 50%, #b7461e 100%);
  border-radius: 20px 20px 0 0;
}

.table-card:hover {
  transform: translateY(-8px) scale(1.02);
  box-shadow: 0 20px 50px rgba(230, 126, 34, 0.3);
}

/* حالات الطاولة المحسنة */
.table-card.available::before {
  background: linear-gradient(90deg, #27ae60 0%, #2ecc71 50%, #16a085 100%);
}

.table-card.occupied::before {
  background: linear-gradient(90deg, #e74c3c 0%, #c0392b 50%, #a93226 100%);
}

.table-card.reserved::before {
  background: linear-gradient(90deg, #f39c12 0%, #e67e22 50%, #d35400 100%);
}

.table-card.maintenance::before {
  background: linear-gradient(90deg, #95a5a6 0%, #7f8c8d 50%, #566573 100%);
}

/* أيقونة الطاولة */
.table-icon {
  font-size: 3rem;
  margin-bottom: 1rem;
}

.table-card.available .table-icon {
  color: #27ae60;
}

.table-card.occupied .table-icon {
  color: #e74c3c;
}

.table-card.reserved .table-icon {
  color: #f39c12;
}

.table-card.maintenance .table-icon {
  color: #95a5a6;
}

/* رقم الطاولة */
.table-number {
  font-size: 1.5rem;
  font-weight: bold;
  color: #2c3e50;
  margin-bottom: 0.5rem;
}

/* حالة الطاولة */
.table-status {
  padding: 0.4rem 0.8rem;
  border-radius: 20px;
  font-size: 0.8rem;
  font-weight: 600;
  margin-bottom: 1rem;
  display: inline-block;
}

.table-status.available {
  background: #d4edda;
  color: #155724;
  border: 1px solid #c3e6cb;
}

.table-status.occupied {
  background: #f8d7da;
  color: #721c24;
  border: 1px solid #f5c6cb;
}

.table-status.reserved {
  background: #fff3cd;
  color: #856404;
  border: 1px solid #ffeaa7;
}

.table-status.maintenance {
  background: #d1ecf1;
  color: #0c5460;
  border: 1px solid #bee5eb;
}

/* معلومات الطاولة */
.table-info {
  margin-bottom: 1rem;
}

.table-detail {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0.3rem 0;
  font-size: 0.85rem;
}

.table-detail .label {
  color: #6c757d;
  font-weight: 600;
}

.table-detail .value {
  color: #495057;
}

/* أزرار إجراءات الطاولة */
.table-actions {
  display: flex;
  gap: 0.5rem;
  justify-content: center;
}

.table-action-btn {
  padding: 0.5rem 1rem;
  border: none;
  border-radius: 6px;
  cursor: pointer;
  font-size: 0.8rem;
  font-weight: 600;
  transition: all 0.3s ease;
  flex: 1;
}

.occupy-btn {
  background: #e74c3c;
  color: white;
}

.occupy-btn:hover {
  background: #c0392b;
}

.free-btn {
  background: #27ae60;
  color: white;
}

.free-btn:hover {
  background: #229954;
}

.reserve-btn {
  background: #f39c12;
  color: white;
}

.reserve-btn:hover {
  background: #e67e22;
}

.maintenance-btn {
  background: #95a5a6;
  color: white;
}

.maintenance-btn:hover {
  background: #7f8c8d;
}

/* إحصائيات الطاولات */
.tables-stats {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(180px, 1fr));
  gap: 1rem;
  margin-bottom: 2rem;
}

.stat-card {
  background: white;
  padding: 1.5rem;
  border-radius: 12px;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
  text-align: center;
  border-left: 4px solid #e67e22;
}

.stat-card.total-tables {
  border-left-color: #3498db;
}

.stat-card.available-tables {
  border-left-color: #27ae60;
}

.stat-card.occupied-tables {
  border-left-color: #e74c3c;
}

.stat-card.reserved-tables {
  border-left-color: #f39c12;
}

.stat-card .stat-icon {
  font-size: 2.5rem;
  margin-bottom: 1rem;
  color: #e67e22;
}

.stat-card.total-tables .stat-icon {
  color: #3498db;
}

.stat-card.available-tables .stat-icon {
  color: #27ae60;
}

.stat-card.occupied-tables .stat-icon {
  color: #e74c3c;
}

.stat-card.reserved-tables .stat-icon {
  color: #f39c12;
}

.stat-card .stat-number {
  font-size: 2rem;
  font-weight: bold;
  color: #2c3e50;
  margin-bottom: 0.5rem;
}

.stat-card .stat-label {
  color: #6c757d;
  font-size: 0.9rem;
}

/* مودال إضافة طاولة */
.add-table-modal .modal-header {
  background: linear-gradient(135deg, #e67e22, #f39c12);
}

.add-table-modal .form-group {
  margin-bottom: 1.5rem;
}

.add-table-modal label {
  display: block;
  margin-bottom: 0.5rem;
  color: #495057;
  font-weight: 600;
}

.add-table-modal input,
.add-table-modal select {
  width: 100%;
  padding: 0.8rem;
  border: 2px solid #e9ecef;
  border-radius: 8px;
  font-size: 1rem;
  transition: border-color 0.3s ease;
}

.add-table-modal input:focus,
.add-table-modal select:focus {
  outline: none;
  border-color: #e67e22;
  box-shadow: 0 0 0 3px rgba(230, 126, 34, 0.1);
}

/* تنسيقات الإحصائيات العامة للطاولات */
.tables-screen .stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 1.5rem;
  margin-bottom: 3rem;
  width: 100%;
  max-width: 100%;
  overflow-x: auto;
}

/* تنسيقات البحث للطاولات */
.tables-screen .search-container {
  position: relative;
  display: flex;
  align-items: center;
  background: white;
  border: 2px solid #e9ecef;
  border-radius: 12px;
  overflow: hidden;
  transition: all 0.3s ease;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

.tables-screen .search-container:hover {
  border-color: #e67e22;
  box-shadow: 0 4px 15px rgba(230, 126, 34, 0.15);
}

.tables-screen .search-container:focus-within {
  border-color: #e67e22;
  box-shadow: 0 0 0 3px rgba(230, 126, 34, 0.1);
}

.tables-screen .search-icon {
  padding: 0.7rem 1rem;
  color: #6c757d;
  background: #f8f9fa;
  border-right: 1px solid #e9ecef;
}

.tables-screen .search-input {
  border: none;
  outline: none;
  padding: 0.7rem 1rem;
  font-size: 0.9rem;
  min-width: 200px;
  background: transparent;
}

.tables-screen .search-input::placeholder {
  color: #adb5bd;
  font-style: italic;
}

.tables-screen .clear-search {
  padding: 0.5rem;
  background: none;
  border: none;
  color: #6c757d;
  cursor: pointer;
  transition: color 0.2s ease;
}

.tables-screen .clear-search:hover {
  color: #e74c3c;
}

/* تنسيقات المرشحات للطاولات */
.tables-screen .filter-select {
  padding: 0.7rem 1.2rem;
  border: 2px solid #e9ecef;
  border-radius: 12px;
  background: white;
  font-size: 0.9rem;
  font-weight: 500;
  min-width: 150px;
  color: #495057;
  transition: all 0.3s ease;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

.tables-screen .filter-select:hover {
  border-color: #e67e22;
  box-shadow: 0 4px 15px rgba(230, 126, 34, 0.15);
  transform: translateY(-1px);
}

.tables-screen .filter-select:focus {
  outline: none;
  border-color: #e67e22;
  box-shadow: 0 0 0 3px rgba(230, 126, 34, 0.1);
}

/* التصميم المتجاوب */
@media (max-width: 768px) {
  .tables-screen {
    padding: 1rem;
  }
  
  .tables-header {
    flex-direction: column;
    gap: 1rem;
    text-align: center;
  }
  
  .tables-header h1 {
    font-size: 1.5rem;
  }
  
  .tables-actions {
    flex-direction: column;
    width: 100%;
  }
  
  .add-table-btn,
  .reset-tables-btn {
    width: 100%;
    justify-content: center;
  }
  
  .tables-grid {
    grid-template-columns: repeat(2, 1fr);
    gap: 1rem;
  }
  
  .table-card {
    padding: 1rem;
  }
  
  .tables-stats {
    grid-template-columns: repeat(2, 1fr);
  }
}

@media (max-width: 480px) {
  .tables-grid {
    grid-template-columns: 1fr;
  }
  
  .tables-stats {
    grid-template-columns: 1fr;
  }
  
  .table-actions {
    flex-direction: column;
    gap: 0.3rem;
  }
  
  .table-action-btn {
    width: 100%;
  }
}

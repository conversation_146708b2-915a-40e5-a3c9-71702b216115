/* ============================== */
/* DiscountRequestsManagerScreen - Clean Bootstrap Design */
/* ============================== */

/* استيراد المتغيرات المميزة لشاشة طلبات الخصم */
@import '../variables/discount-variables.css';

/* Container for discount requests - scoped to avoid conflicts */

/* ================ */
/* Bootstrap Container Enhancements */
/* ================ */

.discount-requests-bootstrap-container {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  min-height: 100vh;
  padding: 2rem 1rem;
  width: 100%;
  max-width: none;
}

/* Remove Bootstrap Grid Constraints */
.discount-requests-bootstrap-container .row {
  margin-left: 0;
  margin-right: 0;
}

.discount-requests-bootstrap-container .col,
.discount-requests-bootstrap-container [class*="col-"] {
  padding-left: 0;
  padding-right: 0;
}

/* Ensure ResponsiveGrid uses full available space */
.discount-requests-bootstrap-container .responsive-grid {
  width: 100%;
  max-width: none;
}

/* ================ */
/* Statistics Cards */
/* ================ */

.discount-requests-stat-card {
  border: none;
  border-radius: 12px;
  overflow: hidden;
  transition: all 0.3s ease;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  background: #ffffff;
}

.discount-requests-stat-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

.discount-requests-stat-icon {
  width: 60px;
  height: 60px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.5rem;
  flex-shrink: 0;
}

/* ================ */
/* Filters Card */
/* ================ */

.discount-requests-filters-card {
  border: none;
  border-radius: 12px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  background: #ffffff;
}

.discount-requests-filters-card .card-header {
  background: linear-gradient(135deg, #007bff, #0056b3);
  color: #ffffff;
  border: none;
  border-radius: 12px 12px 0 0;
  padding: 1rem 1.5rem;
}

.discount-requests-filters-card .form-control,
.discount-requests-filters-card .form-select {
  border: 2px solid #e9ecef;
  border-radius: 8px;
  transition: all 0.3s ease;
  font-size: 0.95rem;
}

.discount-requests-filters-card .form-control:focus,
.discount-requests-filters-card .form-select:focus {
  border-color: #007bff;
  box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
}

.discount-requests-filters-card .input-group-text {
  background: #f8f9fa;
  border: 2px solid #e9ecef;
  color: #6c757d;
}

/* ================ */
/* Request Cards */
/* ================ */

.discount-requests-item-card {
  border: none;
  border-radius: 12px;
  overflow: hidden;
  transition: all 0.3s ease;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  background: #ffffff;
  position: relative;
  width: 100%;
  height: auto;
  max-width: none;
}

.discount-requests-item-card:hover {
  transform: translateY(-3px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

/* Status-based border colors */
.discount-requests-item-card.discount-requests-status-pending {
  border-left: 5px solid #ffc107;
}

.discount-requests-item-card.discount-requests-status-approved {
  border-left: 5px solid #28a745;
}

.discount-requests-item-card.discount-requests-status-rejected {
  border-left: 5px solid #dc3545;
}

/* ================ */
/* Info Items */
/* ================ */

.discount-info-item {
  display: flex;
  align-items: center;
  padding: 0.5rem 0;
  border-bottom: 1px solid #f8f9fa;
}

.discount-info-item:last-child {
  border-bottom: none;
}

.discount-info-item i {
  width: 20px;
  text-align: center;
}

/* ================ */
/* Amount Breakdown */
/* ================ */

.discount-amount-breakdown {
  background: linear-gradient(135deg, #f8f9fa, #e9ecef) !important;
  border: 1px solid #dee2e6;
  border-radius: 8px;
}

.discount-amount-breakdown .text-sm {
  font-size: 0.875rem;
}

/* ================ */
/* Empty State */
/* ================ */

.discount-requests-empty-card {
  max-width: 500px;
  border: none;
  border-radius: 12px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  background: #ffffff;
}

/* ================ */
/* Action Buttons */
/* ================ */

.discount-requests-details-btn {
  border: 2px solid #17a2b8;
  color: #17a2b8;
  transition: all 0.3s ease;
}

.discount-requests-details-btn:hover {
  background: #17a2b8;
  color: #ffffff;
  transform: scale(1.02);
}

.discount-requests-approve-btn {
  transition: all 0.3s ease;
}

.discount-requests-approve-btn:hover {
  transform: scale(1.02);
}

.discount-requests-reject-btn {
  transition: all 0.3s ease;
}

.discount-requests-reject-btn:hover {
  transform: scale(1.02);
}

/* ================ */
/* Status Indicators */
/* ================ */

.discount-requests-card-border {
  position: absolute;
  top: 0;
  left: 0;
  bottom: 0;
  width: 4px;
}

.discount-requests-card-border.pending {
  background: #ffc107;
}

.discount-requests-card-border.approved {
  background: #28a745;
}

.discount-requests-card-border.rejected {
  background: #dc3545;
}

/* ================ */
/* Grid Layout */
/* ================ */

.discount-requests-grid {
  display: grid;
  gap: 1.5rem;
  grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
}

/* ================ */
/* Responsive Design */
/* ================ */

@media (max-width: 576px) {
  .discount-requests-bootstrap-container {
    padding: 1rem 0.5rem;
  }
  
  .discount-requests-grid {
    grid-template-columns: 1fr;
    gap: 1rem;
  }
  
  .discount-requests-filters-card .row {
    flex-direction: column;
  }
  
  .discount-requests-filters-card .col-md-3,
  .discount-requests-filters-card .col-md-4 {
    margin-bottom: 1rem;
  }
  
  .discount-amount-breakdown {
    font-size: 0.8rem;
  }
}

.discountRequestsManagerScreen__stat-card--approved::before {
  background: linear-gradient(90deg, #28a745, #1e7e34);
}

.discountRequestsManagerScreen__stat-card--rejected::before {
  background: linear-gradient(90deg, #dc3545, #bd2130);
}

.discountRequestsManagerScreen__stat-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.discountRequestsManagerScreen__stat-icon {
  width: 50px;
  height: 50px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.5rem;
  color: white;
}

.discountRequestsManagerScreen__stat-icon--total {
  background: #007bff;
}

.discountRequestsManagerScreen__stat-icon--pending {
  background: #ffc107;
}

.discountRequestsManagerScreen__stat-icon--approved {
  background: #28a745;
}

.discountRequestsManagerScreen__stat-icon--rejected {
  background: #dc3545;
}

.discountRequestsManagerScreen__stat-content {
  flex: 1;
}

.discountRequestsManagerScreen__stat-value {
  font-size: 2rem;
  font-weight: 700;
  color: #2c3e50;
  margin: 0 0 0.25rem 0;
}

.discountRequestsManagerScreen__stat-label {
  font-size: 1rem;
  color: #6c757d;
  margin: 0;
}

/* Filters section */
.discountRequestsManagerScreen__filters {
  background: #ffffff;
  border-radius: 12px;
  padding: 1.5rem;
  margin-bottom: 2rem;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  border: 1px solid #e9ecef;
}

.discountRequestsManagerScreen__filters-row {
  display: flex;
  gap: 1rem;
  flex-wrap: wrap;
  align-items: center;
}

.discountRequestsManagerScreen__filter-group {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
  min-width: 150px;
}

.discountRequestsManagerScreen__filter-label {
  font-size: 0.9rem;
  font-weight: 600;
  color: #2c3e50;
}

.discountRequestsManagerScreen__filter-select,
.discountRequestsManagerScreen__filter-input {
  padding: 0.5rem 0.75rem;
  border: 2px solid #e9ecef;
  border-radius: 8px;
  font-size: 0.95rem;
  transition: border-color 0.2s ease;
}

.discountRequestsManagerScreen__filter-select:focus,
.discountRequestsManagerScreen__filter-input:focus {
  outline: none;
  border-color: #007bff;
  box-shadow: 0 0 0 3px rgba(0, 123, 255, 0.1);
}

/* Request cards grid */
.discountRequestsManagerScreen__requests-grid {
  background: #ffffff;
  border-radius: 12px;
  border: 1px solid #e9ecef;
  padding: 1.5rem;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.discountRequestsManagerScreen__request-item {
  border-bottom: 1px solid #e9ecef;
  padding: 1rem 0;
}

.discountRequestsManagerScreen__request-item:last-child {
  border-bottom: none;
}

.discountRequestsManagerScreen__request-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 0.75rem;
}

.discountRequestsManagerScreen__order-number {
  font-size: 1.1rem;
  font-weight: 600;
  color: #2c3e50;
}

.discountRequestsManagerScreen__status-badge {
  padding: 0.25rem 0.75rem;
  border-radius: 20px;
  font-size: 0.8rem;
  font-weight: 600;
  text-transform: uppercase;
}

.discountRequestsManagerScreen__status-badge--pending {
  background: #fff3cd;
  color: #856404;
  border: 1px solid #ffeaa7;
}

.discountRequestsManagerScreen__status-badge--approved {
  background: #d4edda;
  color: #155724;
  border: 1px solid #c3e6cb;
}

.discountRequestsManagerScreen__status-badge--rejected {
  background: #f8d7da;
  color: #721c24;
  border: 1px solid #f5c6cb;
}

.discountRequestsManagerScreen__request-details {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 0.75rem;
  margin-bottom: 1rem;
}

.discountRequestsManagerScreen__detail-item {
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
}

.discountRequestsManagerScreen__detail-label {
  font-size: 0.85rem;
  font-weight: 600;
  color: #6c757d;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.discountRequestsManagerScreen__detail-value {
  font-size: 0.95rem;
  color: #2c3e50;
}

.discountRequestsManagerScreen__amount-breakdown {
  background: #f8f9fa;
  border: 1px solid #e9ecef;
  border-radius: 8px;
  padding: 1rem;
  margin: 1rem 0;
}

.discountRequestsManagerScreen__amount-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 0.5rem;
}

.discountRequestsManagerScreen__amount-row:last-child {
  margin-bottom: 0;
  padding-top: 0.5rem;
  border-top: 1px solid #dee2e6;
  font-weight: 600;
}

.discountRequestsManagerScreen__amount-label {
  color: #6c757d;
}

.discountRequestsManagerScreen__amount-value {
  font-weight: 600;
  color: #2c3e50;
}

.discountRequestsManagerScreen__amount-value--discount {
  color: #dc3545;
}

.discountRequestsManagerScreen__amount-value--final {
  color: #28a745;
  font-size: 1.1rem;
}

.discountRequestsManagerScreen__actions {
  display: flex;
  gap: 0.5rem;
  flex-wrap: wrap;
}

.discountRequestsManagerScreen__btn {
  padding: 0.5rem 1rem;
  border: none;
  border-radius: 6px;
  font-size: 0.9rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s ease;
  text-decoration: none;
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
}

.discountRequestsManagerScreen__btn--view {
  background: #17a2b8;
  color: white;
}

.discountRequestsManagerScreen__btn--view:hover {
  background: #138496;
  transform: translateY(-1px);
}

.discountRequestsManagerScreen__btn--approve {
  background: #28a745;
  color: white;
}

.discountRequestsManagerScreen__btn--approve:hover {
  background: #218838;
  transform: translateY(-1px);
}

.discountRequestsManagerScreen__btn--reject {
  background: #dc3545;
  color: white;
}

.discountRequestsManagerScreen__btn--reject:hover {
  background: #c82333;
  transform: translateY(-1px);
}

.discountRequestsManagerScreen__btn--secondary {
  background: #6c757d;
  color: white;
}

.discountRequestsManagerScreen__btn--secondary:hover {
  background: #5a6268;
  transform: translateY(-1px);
}

/* Empty state */
.discountRequestsManagerScreen__empty-state {
  text-align: center;
  padding: 3rem 1rem;
  border-left: 4px solid #007bff;
}

.discountRequestsManagerScreen__empty-icon {
  font-size: 3rem;
  color: #6c757d;
  margin-bottom: 1rem;
}

.discountRequestsManagerScreen__empty-title {
  font-size: 1.5rem;
  font-weight: 600;
  color: #2c3e50;
  margin-bottom: 0.5rem;
}

.discountRequestsManagerScreen__empty-message {
  color: #6c757d;
}

/* Loading spinner */
.discountRequestsManagerScreen__loading {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 3rem;
}

.discountRequestsManagerScreen__spinner {
  width: 3rem;
  height: 3rem;
  border: 3px solid #e9ecef;
  border-top: 3px solid #007bff;
  border-radius: 50%;
  animation: discountRequestsManagerScreen-spin 1s linear infinite;
}

@keyframes discountRequestsManagerScreen-spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* ================ */
/* Enhanced Discount Card Layout */
/* ================ */

/* Icon for discount request */
.discount-request-icon {
  width: 40px;
  height: 40px;
  background: linear-gradient(135deg, #f39c12, #e67e22);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 1.1rem;
  box-shadow: 0 2px 8px rgba(243, 156, 18, 0.3);
}

/* Detail Groups */
.detail-group {
  background: #f8f9fa;
  border-radius: 8px;
  border: 1px solid #e9ecef;
  overflow: hidden;
  height: 100%;
}

.detail-group.discount-group {
  background: linear-gradient(135deg, #fff3cd, #ffeaa7);
  border-color: #f1c40f;
}

.detail-header {
  background: #ffffff;
  padding: 0.5rem 0.75rem;
  border-bottom: 1px solid #e9ecef;
  font-size: 0.9rem;
  display: flex;
  align-items: center;
}

.detail-content {
  padding: 0.75rem;
}

.detail-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 0.5rem;
}

.detail-item:last-child {
  margin-bottom: 0;
}

.detail-label {
  font-size: 0.85rem;
  color: #6c757d;
  font-weight: 500;
}

.detail-value {
  font-size: 0.9rem;
  font-weight: 600;
  color: #2c3e50;
}

.detail-value.discount-amount {
  color: #f39c12;
  font-weight: 700;
}

/* Amounts Summary */
.amounts-summary {
  background: linear-gradient(135deg, #ffffff, #f8f9fa);
  border: 2px solid #e9ecef;
  border-radius: 10px;
  overflow: hidden;
}

.amounts-header {
  background: linear-gradient(135deg, #17a2b8, #138496);
  color: white;
  padding: 0.5rem 0.75rem;
  font-size: 0.9rem;
  display: flex;
  align-items: center;
}

.amounts-body {
  padding: 0.75rem;
}

.amount-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 0.5rem;
  font-size: 0.9rem;
}

.amount-row.total {
  font-weight: 700;
  font-size: 1rem;
  padding-top: 0.5rem;
  border-top: 2px solid #dee2e6;
  margin-top: 0.5rem;
}

.amount-label {
  color: #6c757d;
  font-weight: 500;
}

.amount-value {
  font-weight: 600;
  color: #2c3e50;
}

.amount-value.original {
  color: #495057;
}

.amount-value.discount {
  color: #e74c3c;
}

.amount-value.final {
  color: #27ae60;
  font-weight: 700;
}

.amount-divider {
  height: 1px;
  background: linear-gradient(90deg, transparent, #dee2e6, transparent);
  margin: 0.5rem 0;
}

/* Reason Section */
.reason-section {
  background: linear-gradient(135deg, #e8f4f8, #d1ecf1);
  border: 1px solid #bee5eb;
  border-radius: 8px;
  overflow: hidden;
}

.reason-header {
  background: linear-gradient(135deg, #e3f2fd, #bbdefb);
  padding: 0.5rem 0.75rem;
  border-bottom: 1px solid #90caf9;
  display: flex;
  align-items: center;
  gap: 0.4rem;
  font-weight: 600;
  color: #1976d2;
  font-size: 0.85rem;
  margin: 0;
}

.reason-header i {
  color: #2196f3;
}

.reason-body {
  padding: 0.75rem;
  background: #fafafa;
  margin: 0;
}

.reason-text {
  margin: 0;
  color: #455a64;
  line-height: 1.4;
  font-size: 0.85rem;
  font-style: italic;
  padding: 0.4rem;
  background: #ffffff;
  border-radius: 6px;
  border-left: 3px solid #2196f3;
}

/* Enhanced badges */
.badge.rounded-pill {
  font-size: 0.8rem;
  padding: 0.4rem 1rem;
}

/* ============================== */
/* Enhanced Discount Details */
/* ============================== */

/* Enhanced Discount Details Container */
.enhanced-discount-details {
  position: relative;
}

/* Discount Visual Summary */
.discount-visual-summary {
  margin-bottom: 1.5rem;
}

.discount-highlight-card {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 15px;
  padding: 1rem;
  color: white;
  box-shadow: 0 8px 25px rgba(102, 126, 234, 0.3);
  position: relative;
  overflow: hidden;
  margin-bottom: 0.75rem;
}

.discount-highlight-card::before {
  content: '';
  position: absolute;
  top: -50%;
  right: -50%;
  width: 100%;
  height: 100%;
  background: radial-gradient(circle, rgba(255,255,255,0.1) 0%, transparent 70%);
  transform: rotate(45deg);
}

.discount-main-info {
  position: relative;
  z-index: 2;
}

.discount-amount-display {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.discount-icon {
  width: 50px;
  height: 50px;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.5rem;
  -webkit-backdrop-filter: blur(10px);
  backdrop-filter: blur(10px);
  border: 2px solid rgba(255, 255, 255, 0.3);
}

.discount-values {
  flex: 1;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.primary-amount {
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
}

.primary-amount .amount-label {
  font-size: 0.9rem;
  opacity: 0.9;
  font-weight: 500;
}

.primary-amount .amount-value {
  font-size: 1.75rem;
  font-weight: 700;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

.percentage-badge {
  background: rgba(255, 255, 255, 0.25);
  padding: 0.5rem 1rem;
  border-radius: 25px;
  font-size: 1.1rem;
  font-weight: 700;
  -webkit-backdrop-filter: blur(10px);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.3);
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
}

/* Details Grid */
.details-grid {
  display: grid;
  grid-template-columns: 1fr;
  gap: 0.75rem;
  width: 100%;
}

/* On larger screens, use two columns */
@media (min-width: 992px) {
  .details-grid {
    grid-template-columns: 1fr 1fr;
    gap: 1rem;
  }
}

.detail-card {
  background: #ffffff;
  border-radius: 12px;
  overflow: hidden;
  border: 1px solid #e9ecef;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
  transition: all 0.3s ease;
  width: 100%;
  margin: 0;
}

/* ============================== */
/* Optimized Space Usage for Cards - Enhanced */
/* ============================== */

/* Card Body Enhanced Padding - Maximum Space Utilization */
.discount-requests-item-card .card-body {
  padding: 0.5rem;
  width: 100%;
  box-sizing: border-box;
}

/* Enhanced Discount Details - Full Width */
.enhanced-discount-details {
  width: 100%;
  margin: 0;
  padding: 0;
}

/* Details Grid - Optimized for Maximum Space */
.details-grid {
  display: grid;
  grid-template-columns: 1fr;
  gap: 0.5rem;
  width: 100%;
  margin: 0;
}

/* On larger screens, use two columns with reduced gap */
@media (min-width: 992px) {
  .details-grid {
    grid-template-columns: 1fr 1fr;
    gap: 0.75rem;
  }
}

/* Detail Cards - Full Width Usage */
.detail-card {
  background: #ffffff;
  border-radius: 12px;
  overflow: hidden;
  border: 1px solid #e9ecef;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
  transition: all 0.3s ease;
  width: 100%;
  margin: 0;
}

/* Remove Any Width Constraints */
.discount-visual-summary,
.discount-highlight-card,
.discount-reason-card {
  width: 100%;
  max-width: none;
  margin-left: 0;
  margin-right: 0;
}

/* Ensure Full Width for All Elements */
.discount-amount-display,
.discount-values,
.amount-breakdown,
.info-item {
  width: 100%;
  max-width: none;
}

/* Responsive Adjustments for Maximum Space Usage */
@media (max-width: 1199px) {
  .details-grid {
    grid-template-columns: 1fr;
    gap: 0.5rem;
  }
}

@media (max-width: 991px) {
  .discount-requests-item-card .card-body {
    padding: 0.625rem;
  }
}

@media (max-width: 767px) {
  .discount-requests-item-card .card-body {
    padding: 0.5rem;
  }

  .details-grid {
    gap: 0.375rem;
  }
}

@media (max-width: 575px) {
  .discount-requests-item-card .card-body {
    padding: 0.5rem;
  }

  .details-grid {
    gap: 0.25rem;
  }
}

/* ============================== */
/* Optimized Detail Card Components */
/* ============================== */

.detail-card-header {
  background: linear-gradient(135deg, #f8f9fa, #e9ecef);
  padding: 0.25rem 0.375rem;
  border-bottom: 1px solid #dee2e6;
  display: flex;
  align-items: center;
  gap: 0.25rem;
  font-weight: 600;
  color: #495057;
  font-size: 0.75rem;
  margin: 0;
}

.detail-card-header i {
  color: #007bff;
  font-size: 0.8rem;
}

.detail-card-body {
  padding: 0.25rem;
  margin: 0;
}

/* Order Info Card */
.detail-card.order-info .detail-card-header i {
  color: #28a745;
}

.info-item {
  display: flex;
  align-items: center;
  gap: 0.25rem;
  margin-bottom: 0.25rem;
  padding-bottom: 0.125rem;
  border-bottom: 1px solid #f8f9fa;
}

.info-item:last-child {
  margin-bottom: 0;
  border-bottom: none;
  padding-bottom: 0;
}

.info-item .icon {
  width: 14px;
  color: #6c757d;
  text-align: center;
  font-size: 0.75rem;
  flex-shrink: 0;
}

.info-item .label {
  color: #6c757d;
  font-size: 0.75rem;
  font-weight: 500;
  min-width: 50px;
  flex-shrink: 0;
}

.info-item .value {
  color: #2c3e50;
  font-weight: 600;
  font-size: 0.8rem;
  flex: 1;
  text-align: left;
}

/* Amounts Info Card */
.detail-card.amounts-info .detail-card-header i {
  color: #fd7e14;
}

.amount-breakdown {
  display: flex;
  flex-direction: column;
  gap: 0.125rem;
  margin: 0;
}

.amount-line {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 0.85rem;
  margin: 0;
  padding: 0;
}

.amount-line .label {
  color: #6c757d;
  font-weight: 500;
}

.amount-line .value {
  font-weight: 600;
  color: #2c3e50;
}

.amount-line.original .value {
  color: #495057;
}

.amount-line.discount .value.negative {
  color: #dc3545;
  font-weight: 700;
}

.amount-line.final {
  padding-top: 0.4rem;
  border-top: 2px solid #dee2e6;
  margin-top: 0.2rem;
  font-size: 0.9rem;
}

.amount-line.final .label {
  color: #495057;
  font-weight: 600;
}

.amount-line.final .value.total {
  color: #28a745;
  font-weight: 700;
  font-size: 1rem;
}

.amount-separator {
  height: 1px;
  background: linear-gradient(90deg, transparent, #dee2e6, transparent);
  margin: 0.2rem 0;
}

/* Discount Reason Card - Optimized */
.discount-reason-card {
  background: #ffffff;
  border-radius: 12px;
  border: 1px solid #e3f2fd;
  overflow: hidden;
  box-shadow: 0 2px 8px rgba(33, 150, 243, 0.1);
  margin: 0;
}

.reason-header {
  background: linear-gradient(135deg, #e3f2fd, #bbdefb);
  padding: 0.5rem 0.75rem;
  border-bottom: 1px solid #90caf9;
  display: flex;
  align-items: center;
  gap: 0.4rem;
  font-weight: 600;
  color: #1976d2;
  font-size: 0.85rem;
  margin: 0;
}

.reason-header i {
  color: #2196f3;
}

.reason-body {
  padding: 0.75rem;
  background: #fafafa;
  margin: 0;
}

.reason-text {
  margin: 0;
  color: #455a64;
  line-height: 1.4;
  font-size: 0.85rem;
  font-style: italic;
  padding: 0.4rem;
  background: #ffffff;
  border-radius: 6px;
  border-left: 3px solid #2196f3;
}

/* ============================== */
/* Remove Bootstrap Grid Constraints */
/* ============================== */

/* Override any Bootstrap constraints */
.discount-requests-bootstrap-container .row {
  margin-left: 0;
  margin-right: 0;
}

.discount-requests-bootstrap-container .col,
.discount-requests-bootstrap-container [class*="col-"] {
  padding-left: 0;
  padding-right: 0;
}

/* Ensure ResponsiveGrid uses full available space */
.discount-requests-bootstrap-container .responsive-grid {
  width: 100%;
  max-width: none;
}

/* Enhanced container for better space utilization */
.discount-requests-bootstrap-container {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  min-height: 100vh;
  padding: 2rem 1rem;
  width: 100%;
  max-width: none;
}

/* Remove any max-width constraints from cards */
.discount-requests-item-card,
.discount-requests-item-card * {
  box-sizing: border-box;
}

/* Action buttons - full width on mobile */
@media (max-width: 767px) {
  .discount-requests-item-card .d-flex.gap-2.flex-wrap {
    flex-direction: column;
  }
  
  .discount-requests-item-card .btn {
    width: 100%;
    margin-bottom: 0.5rem;
  }
  
  .discount-requests-item-card .btn:last-child {
    margin-bottom: 0;
  }
}

/* Ensure status alerts use full width */
.discount-requests-item-card .alert {
  width: 100%;
  margin: 0;
}

/* Header section optimization */
.discount-requests-item-card .d-flex.justify-content-between.align-items-start {
  flex-wrap: wrap;
  gap: 0.75rem;
}

@media (max-width: 575px) {
  .discount-requests-item-card .d-flex.justify-content-between.align-items-start {
    flex-direction: column;
    align-items: flex-start;
  }
  
  .discount-requests-item-card .badge {
    align-self: flex-end;
  }
}

/* ============================== */
/* Large Screen Optimizations - Enhanced Space Usage */
/* ============================== */

/* For extra large screens - maximize card space usage */
@media (min-width: 1400px) {
  .discount-requests-bootstrap-container {
    padding: 2rem 1.5rem;
  }

  .discount-requests-item-card .card-body {
    padding: 1rem;
  }

  .details-grid {
    grid-template-columns: 1fr 1fr;
    gap: 0.75rem;
  }

  .discount-highlight-card {
    padding: 1rem;
  }

  .primary-amount .amount-value {
    font-size: 1.6rem;
  }

  .percentage-badge {
    font-size: 1rem;
    padding: 0.5rem 0.875rem;
  }
}

/* ============================== */
/* Additional Space Optimizations */
/* ============================== */

/* Reduce margins and padding for better space utilization */
.discount-visual-summary {
  margin-bottom: 0.5rem !important;
}

.discount-visual-summary-compact {
  margin-bottom: 0.25rem !important;
}

.details-grid-compact {
  margin-bottom: 0.25rem !important;
}

.discount-reason-card-compact {
  margin-bottom: 0.25rem !important;
}

.discount-highlight-card {
  padding: 0.75rem;
  margin: 0;
}

.discount-reason-card {
  margin-bottom: 0.5rem !important;
}

.reason-header {
  padding: 0.375rem 0.5rem;
  font-size: 0.8rem;
}

.reason-body {
  padding: 0.5rem;
}

.reason-text {
  margin: 0;
  padding: 0.375rem;
  font-size: 0.8rem;
  line-height: 1.3;
}

/* Optimize button spacing */
.discount-requests-item-card .d-flex.gap-2 {
  gap: 0.375rem !important;
  margin-top: 0.5rem;
}

.discount-requests-item-card .btn-sm {
  padding: 0.375rem 0.75rem;
  font-size: 0.8rem;
}

/* ============================== */
/* Enhanced Visual Elements Space Optimization */
/* ============================== */

/* Discount Amount Display - Compact */
.discount-amount-display {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  width: 100%;
}

.discount-icon {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  background: linear-gradient(135deg, #f39c12, #e67e22);
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 0.9rem;
  flex-shrink: 0;
}

.discount-values {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
}

.primary-amount {
  display: flex;
  flex-direction: column;
  gap: 0.125rem;
}

.amount-label {
  font-size: 0.7rem;
  color: #6c757d;
  font-weight: 500;
}

.amount-value {
  font-size: 1.1rem;
  font-weight: 700;
  color: #2c3e50;
}

.percentage-badge {
  background: linear-gradient(135deg, #28a745, #20c997);
  color: white;
  padding: 0.25rem 0.5rem;
  border-radius: 12px;
  font-size: 0.75rem;
  font-weight: 600;
  flex-shrink: 0;
}

/* Header optimizations */
.discount-requests-item-card h6 {
  font-size: 0.9rem;
  line-height: 1.2;
}

.discount-requests-item-card small {
  font-size: 0.7rem;
  line-height: 1.1;
}

/* Badge optimizations */
.discount-requests-item-card .badge {
  font-size: 0.7rem;
  padding: 0.25rem 0.5rem;
}

/* ============================== */
/* Maximum Space Utilization - Detail Cards */
/* ============================== */

/* Remove all unnecessary margins and padding from detail cards */
.detail-card {
  background: #ffffff;
  border-radius: 8px;
  overflow: hidden;
  border: 1px solid #e9ecef;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
  transition: all 0.3s ease;
  width: 100%;
  margin: 0;
}

/* Maximize content area in detail cards */
.detail-card-header {
  padding: 0.2rem 0.3rem;
  font-size: 0.7rem;
  gap: 0.2rem;
}

.detail-card-header i {
  font-size: 0.75rem;
}

.detail-card-body {
  padding: 0.2rem;
}

/* Compress info items further */
.info-item {
  gap: 0.2rem;
  margin-bottom: 0.2rem;
  padding-bottom: 0.1rem;
}

.info-item .icon {
  width: 12px;
  font-size: 0.7rem;
}

.info-item .label {
  font-size: 0.7rem;
  min-width: 45px;
}

.info-item .value {
  font-size: 0.75rem;
}

/* Compress amount breakdown */
.amount-breakdown {
  gap: 0.1rem;
}

.amount-line {
  font-size: 0.8rem;
  padding: 0.1rem 0;
}

/* Make detail cards use full width */
.details-grid {
  gap: 0.375rem;
}

/* Remove extra spacing from discount highlight card */
.discount-highlight-card {
  padding: 0.5rem;
  margin: 0;
}

.discount-amount-display {
  gap: 0.375rem;
}

.primary-amount {
  gap: 0.1rem;
}

.amount-label {
  font-size: 0.65rem;
}

.amount-value {
  font-size: 1rem;
  line-height: 1.1;
}

/* ============================== */
/* Mobile and Small Screen Optimizations */
/* ============================== */

@media (max-width: 576px) {
  .discount-requests-item-card .card-body {
    padding: 0.25rem;
  }

  .discount-amount-display {
    gap: 0.25rem;
  }

  .discount-icon {
    width: 26px;
    height: 26px;
    font-size: 0.75rem;
  }

  .amount-value {
    font-size: 0.95rem;
  }

  .percentage-badge {
    font-size: 0.65rem;
    padding: 0.15rem 0.3rem;
  }

  .discount-requests-item-card h6 {
    font-size: 0.8rem;
  }

  .discount-requests-item-card small {
    font-size: 0.6rem;
  }

  .detail-card-header {
    padding: 0.15rem 0.25rem;
    font-size: 0.65rem;
  }

  .detail-card-body {
    padding: 0.15rem;
  }

  .info-item {
    gap: 0.15rem;
    margin-bottom: 0.15rem;
  }

  .info-item .icon {
    width: 10px;
    font-size: 0.65rem;
  }

  .info-item .label {
    min-width: 40px;
    font-size: 0.65rem;
  }

  .info-item .value {
    font-size: 0.7rem;
  }

  .amount-breakdown {
    gap: 0.05rem;
  }

  .amount-line {
    font-size: 0.75rem;
    padding: 0.05rem 0;
  }

  .reason-header {
    padding: 0.15rem 0.25rem;
    font-size: 0.65rem;
  }

  .reason-body {
    padding: 0.15rem;
  }

  .reason-text {
    padding: 0.15rem;
    font-size: 0.7rem;
  }

  .details-grid {
    gap: 0.25rem;
  }

  .discount-highlight-card {
    padding: 0.3rem;
  }
}

/* ============================== */
/* Medium and Large Screen Maximum Space Usage */
/* ============================== */

@media (min-width: 577px) and (max-width: 991px) {
  .discount-requests-item-card .card-body {
    padding: 0.375rem;
  }

  .detail-card-header {
    padding: 0.2rem 0.3rem;
    font-size: 0.7rem;
  }

  .detail-card-body {
    padding: 0.2rem;
  }

  .info-item {
    gap: 0.2rem;
    margin-bottom: 0.2rem;
  }

  .details-grid {
    gap: 0.3rem;
  }
}

@media (min-width: 992px) and (max-width: 1199px) {
  .discount-requests-item-card .card-body {
    padding: 0.4rem;
  }

  .detail-card-header {
    padding: 0.25rem 0.35rem;
    font-size: 0.75rem;
  }

  .detail-card-body {
    padding: 0.25rem;
  }

  .details-grid {
    grid-template-columns: 1fr 1fr;
    gap: 0.4rem;
  }
}

@media (min-width: 1200px) {
  .discount-requests-item-card .card-body {
    padding: 0.5rem;
  }

  .details-grid {
    grid-template-columns: 1fr 1fr;
    gap: 0.5rem;
  }

  .detail-card-header {
    padding: 0.3rem 0.4rem;
    font-size: 0.75rem;
  }

  .detail-card-body {
    padding: 0.3rem;
  }
}

/* For large screens - optimize layout */
@media (min-width: 1200px) and (max-width: 1399px) {
  .discount-requests-item-card .card-body {
    padding: 1.125rem;
  }
  
  .details-grid {
    grid-template-columns: 1fr 1fr;
    gap: 0.875rem;
  }
}

/* Ensure no content is cut off */
.discount-requests-item-card {
  overflow: visible;
}

.discount-requests-item-card .card-body {
  overflow: visible;
}

/* Make sure text doesn't overflow */
.info-item .value,
.amount-line .value,
.reason-text {
  word-wrap: break-word;
  overflow-wrap: break-word;
  max-width: 100%;
}

/* Optimize button spacing */
.discount-requests-item-card .d-flex.gap-2.flex-wrap .btn {
  flex: 1;
  min-width: auto;
}

@media (min-width: 768px) {
  .discount-requests-item-card .d-flex.gap-2.flex-wrap .btn {
    flex: 1;
    max-width: none;
  }
}


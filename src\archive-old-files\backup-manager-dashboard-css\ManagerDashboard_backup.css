﻿/* Manager Dashboar  font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
  direction: rtl;
  overflow-x: hidden; /* منع التمرير الأفقي */
}

/* Header *//

/* تحسينات عامة للتخطيط */
.manager-dashboard {
  display: flex;
  flex-direction: column;
   overflow: hidden !important;
  transition: all 0.3s ease;
  /* Webkit scrollbar styling for better browser support */
}ight: 100vh;
  background: var(--light-bg, #f8f9fa);
  font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
  direction: rtl;
  overflow-x: hidden; /* منع التمرير الأفقي */
}d Styles */

/* ØªØ­Ø³ÙŠÙ†Ø§Øª Ø¹Ø§Ù…Ø© Ù„Ù„ØªØ®Ø·ÙŠØ· */
.manager-dashboard {
  display: flex;
  flex-direction: column;
  min-height: 100vh;
  background: var(--light-bg, #f8f9fa);
  font-family: 'Segoe UI', Tahoma, Gene  overflow: hidden !important;
  transition: all 0.3s ease;
  
  /* Firefox scrollbar styling - limited browser support */
}dana, sans-serif;
  direction: rtl;
  overflow-x: hidden; /* Ù…Ù†Ø¹ Ø§Ù„ØªÙ…Ø±ÙŠØ± Ø§Ù„Ø£ÙÙ‚ÙŠ */
}

/* Header */
.manager-header {
  background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
  color: white;
  padding: 1rem 2rem;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  position: sticky;
  top: 0;
  z-index: 1000; /* Ø£Ø¹Ù„Ù‰ Ù…Ù† Ø§Ù„Ù‚Ø§Ø¦Ù…Ø© Ø§Ù„Ø¬Ø§Ù†Ø¨ÙŠØ© */
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.header-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  max-width: 1400px;
  margin: 0 auto;
}

.header-left {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.sidebar-toggle {
  background: none;
  border: none;
  color: white;
  font-size: 1.5rem;
  cursor: pointer;
  padding: 0.5rem;
  border-radius: 8px;
  transition: background-color 0.3s ease;
  display: none; /* Ù…Ø®ÙÙŠ ÙÙŠ Ø§Ù„Ø´Ø§Ø´Ø§Øª Ø§Ù„ÙƒØ¨ÙŠØ±Ø© */
}

.sidebar-toggle:hover {
  background-color: rgba(255, 255, 255, 0.1);
}

/* Ø¥Ø¸Ù‡Ø§Ø± Ø²Ø± Ø§Ù„Ù€ toggle ÙÙŠ Ø§Ù„Ù‡Ø§ØªÙ ÙÙ‚Ø· */
@media (max-width: 768px) {
  .sidebar-toggle {
    display: block !important;
  }
  
  .dashboard-content {
    position: relative;
  }
  
  .manager-sidebar {
    position: fixed;
    top: 80px;
    right: 0;
    transform: translateX(100%);
    transition: transform 0.3s ease-in-out;
    z-index: 999;
  }
  
  .manager-sidebar.open {
    transform: translateX(0);
  }
  
  .manager-main {
    padding: 1rem;
    width: 100%;
  }
  
  .sidebar-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.5);
    z-index: 998;
    -webkit-backdrop-filter: blur(2px);
    backdrop-filter: blur(2px);
    display: block;
  }
}

/* ÙÙŠ Ø§Ù„Ø´Ø§Ø´Ø§Øª Ø§Ù„ÙƒØ¨ÙŠØ±Ø©: Ø§Ù„Ù‚Ø§Ø¦Ù…Ø© Ø§Ù„Ø¬Ø§Ù†Ø¨ÙŠØ© Ù…ÙØªÙˆØ­Ø© Ø¯Ø§Ø¦Ù…Ø§Ù‹ */

/* Ø²Ø± Ø¥ØµÙ„Ø§Ø­ Ø§Ù„Ù…Ø¨ÙŠØ¹Ø§Øª */
.sales-fix-btn {
  background: linear-gradient(135deg, #e74c3c 0%, #c0392b 100%) !important;
  color: white !important;
  border: 2px solid #c0392b !important;
  position: relative;
  overflow: hidden;
}

.sales-fix-btn:hover {
  background: linear-gradient(135deg, #c0392b 0%, #a93226 100%) !important;
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(231, 76, 60, 0.3);
}

.sales-fix-btn:before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
  transition: left 0.5s;
}

.sales-fix-btn:hover:before {
  left: 100%;
}

.sales-fix-btn i {
  margin-left: 8px;
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0% { transform: scale(1); }
  50% { transform: scale(1.1); }
  100% { transform: scale(1); }
}
@media (min-width: 769px) {
  .manager-sidebar {
    position: relative;
    transform: translateX(0);
    transition: none;
  }
  
  .sidebar-overlay {
    display: none;
  }
  
  .sidebar-toggle {
    display: none;
  }
}

.header-left h1 {
  margin: 0;
  font-size: 1.5rem;
  font-weight: 600;
}

.header-right {
  display: flex;
  align-items: center;
  gap: 1rem;
}

/* Reset Buttons */
.reset-buttons {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  margin-right: 1rem;
  flex-wrap: wrap;
}

.reset-btn {
  background: rgba(255, 255, 255, 0.1);
  color: white;
  border: 1px solid rgba(255, 255, 255, 0.2);
  padding: 0.4rem 0.8rem;
  border-radius: 6px;
  cursor: pointer;
  font-size: 0.85rem;
  display: flex;
  align-items: center;
  gap: 0.3rem;
  transition: all 0.3s ease;
  white-space: nowrap;
}

.reset-btn:hover {
  background: rgba(255, 255, 255, 0.2);
  border-color: rgba(255, 255, 255, 0.4);
  transform: translateY(-1px);
}

.reset-btn.reset-orders {
  background: rgba(52, 152, 219, 0.8);
  border-color: #3498db;
}

.reset-btn.reset-orders:hover {
  background: rgba(52, 152, 219, 1);
}

.reset-btn.reset-discounts {
  background: rgba(241, 196, 15, 0.8);
  border-color: #f1c40f;
}

.reset-btn.reset-discounts:hover {
  background: rgba(241, 196, 15, 1);
}

.reset-btn.reset-all {
  background: rgba(231, 76, 60, 0.8);
  border-color: #e74c3c;
}

.reset-btn.reset-all:hover {
  background: rgba(231, 76, 60, 1);
}

.reset-btn i {
  font-size: 0.8rem;
}

/* Dashboard Content */
.dashboard-content {
  display: flex;
  flex: 1;
  max-width: 1400px;
  margin: 0 auto;
  width: 100%;
  position: relative;
  min-height: calc(100vh - 80px);
  gap: 0; /* Ø¥Ø²Ø§Ù„Ø© Ø£ÙŠ Ù…Ø³Ø§ÙØ§Øª ØºÙŠØ± Ù…Ø±ØºÙˆØ¨Ø© */
}

/* Sidebar */
.manager-sidebar {
  width: 280px;
  background: white;
  box-shadow: 2px 0 10px rgba(0, 0, 0, 0.1);
  height: calc(100vh - 80px);
  z-index: 500;
  overflow: hidden !important;
  transition: all 0.3s ease;
  /* Modern scrollbar for Firefox - fallback for older browsers */
  scrollbar-width: thin; /* ØºÙŠØ± Ù…Ø¯Ø¹ÙˆÙ… ÙÙŠ Chrome < 121, Safari */
  scrollbar-color: #cbd5e0 #f7fafc; /* ØºÙŠØ± Ù…Ø¯Ø¹ÙˆÙ… ÙÙŠ Chrome < 121, Safari */
  /* Webkit fallback for Chrome, Edge, Safari */
}

.manager-sidebar::-webkit-scrollbar {
  width: 6px;
}

.manager-sidebar::-webkit-scrollbar-thumb {
  background: #cbd5e0;
  border-radius: 4px;
}

.manager-sidebar::-webkit-scrollbar-track {
  background: #f7fafc;
}

/* ØªØ­Ø³ÙŠÙ† Ø´ÙƒÙ„ Ø§Ù„Ù€ scrollbar */
.manager-sidebar::-webkit-scrollbar {
  width: 6px;
}

.manager-sidebar::-webkit-scrollbar-track {
  background: #f7fafc;
}

.manager-sidebar::-webkit-scrollbar-thumb {
  background: #cbd5e0;
  border-radius: 3px;
}

.manager-sidebar::-webkit-scrollbar-thumb:hover {
  background: #a0aec0;
}

/* ÙÙŠ Ø§Ù„Ø­Ø§Ù„Ø© Ø§Ù„Ù…ØºÙ„Ù‚Ø© (Ù„Ù„Ù‡Ø§ØªÙ ÙÙ‚Ø·) */
@media (max-width: 768px) {
  .manager-sidebar:not(.open) {
    transform: translateX(100%);
  }
}

/* ÙÙŠ Ø§Ù„Ø´Ø§Ø´Ø§Øª Ø§Ù„ÙƒØ¨ÙŠØ±Ø© */
@media (min-width: 769px) {
  .manager-sidebar:not(.open) {
    transform: translateX(0);
  }
}

.sidebar-content {
  padding: 2rem 1rem;
}

.manager-profile {
  text-align: center;
  margin-bottom: 2rem;
  padding-bottom: 2rem;
  border-bottom: 1px solid #eee;
}

.manager-avatar {
  width: 80px;
  height: 80px;
  border-radius: 50%;
  background: linear-gradient(135deg, #3498db, #2980b9);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 2rem;
  margin: 0 auto 1rem;
  box-shadow: 0 4px 15px rgba(52, 152, 219, 0.3);
}

.manager-profile h3 {
  margin: 0 0 0.5rem 0;
  color: #2c3e50;
  font-size: 1.2rem;
}

.manager-profile p {
  margin: 0;
  color: #7f8c8d;
  font-size: 0.9rem;
}

/* Navigation */
.manager-nav {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.nav-btn {
  display: flex;
  align-items: center;
  gap: 1rem;
  padding: 1rem;
  background: none;
  border: none;
  border-radius: 12px;
  cursor: pointer;
  transition: all 0.3s ease;
  text-align: right;
  font-size: 1rem;
  color: #2c3e50;
}

.nav-btn:hover {
  background: #f8f9fa;
  transform: translateX(-5px);
}

.nav-btn.active {
  background: linear-gradient(135deg, #3498db, #2980b9);
  color: white;
  box-shadow: 0 4px 15px rgba(52, 152, 219, 0.3);
}

.nav-btn i {
  font-size: 1.2rem;
  width: 20px;
  text-align: center;
}

/* Main Content */
.manager-main {
  flex: 1;
  padding: 2rem;
  max-width: 100%;
  overflow-x: hidden;
}

/* تنسيقات إضافية للشاشة الرئيسية */
.manager-main {
  flex: 1;
  padding: 2rem;
  max-width: 100%;
  overflow-x: hidden;
}

/* =========================== */
/* البطاقات الرئيسية والشبكة */
/* =========================== */

/* شبكة البطاقات الرئيسية */
.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 1.5rem;
  margin-bottom: 2rem;
}

@media (max-width: 768px) {
  .stats-grid {
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 1rem;
  }
}

@media (max-width: 480px) {
  .stats-grid {
    grid-template-columns: 1fr;
  }
}

/* البطاقات الإحصائية */
.stat-card {
  background: linear-gradient(145deg, #ffffff 0%, #f8f9fa 100%);
  border-radius: 20px;
  padding: 2rem;
  box-shadow: 
    0 10px 30px rgba(0, 0, 0, 0.1),
    0 4px 8px rgba(0, 0, 0, 0.05);
  transition: all 0.4s ease;
  display: flex;
  align-items: center;
  gap: 1.5rem;
  position: relative;
  overflow: hidden;
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.stat-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: linear-gradient(90deg, var(--primary-color, #3498db), var(--secondary-color, #2980b9));
  transition: height 0.3s ease;
}

.stat-card:hover {
  transform: translateY(-8px);
  box-shadow: 
    0 20px 40px rgba(0, 0, 0, 0.15),
    0 8px 16px rgba(0, 0, 0, 0.1);
}

.stat-card:hover::before {
  height: 6px;
}

/* أيقونات البطاقات */
.stat-icon {
  width: 70px;
  height: 70px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 2rem;
  color: white;
  position: relative;
  overflow: hidden;
}

.stat-icon::before {
  content: '';
  position: absolute;
  top: -50%;
  left: -50%;
  width: 200%;
  height: 200%;
  background: linear-gradient(45deg, transparent, rgba(255,255,255,0.1), transparent);
  transform: rotate(45deg);
  transition: all 0.6s ease;
  opacity: 0;
}

.stat-card:hover .stat-icon::before {
  opacity: 1;
  animation: shimmer 1.5s ease-in-out;
}

@keyframes shimmer {
  0% { transform: translateX(-100%) translateY(-100%) rotate(45deg); }
  100% { transform: translateX(100%) translateY(100%) rotate(45deg); }
}

/* ألوان البطاقات المختلفة */
.stat-card.orders .stat-icon {
  background: linear-gradient(135deg, #3498db 0%, #2980b9 100%);
}

.stat-card.sales .stat-icon {
  background: linear-gradient(135deg, #27ae60 0%, #229954 100%);
}

.stat-card.employees .stat-icon {
  background: linear-gradient(135deg, #e67e22 0%, #d35400 100%);
}

.stat-card.tables .stat-icon {
  background: linear-gradient(135deg, #9b59b6 0%, #8e44ad 100%);
}

/* محتوى البطاقات */
.stat-content {
  flex: 1;
}

.stat-content h3 {
  font-size: 2.5rem;
  font-weight: 700;
  margin: 0 0 0.5rem 0;
  background: linear-gradient(135deg, #2c3e50, #34495e);
  -webkit-background-clip: text;
  background-clip: text;
  -webkit-text-fill-color: transparent;
  text-align: right;
}

.stat-content p {
  font-size: 1rem;
  color: #7f8c8d;
  margin: 0;
  font-weight: 500;
  text-align: right;
}

/* =========================== */
/* إحصائيات الطلبات */
/* =========================== */

.orders-stats {
  margin-bottom: 2rem;
}

.orders-stats h2 {
  color: #2c3e50;
  margin-bottom: 1.5rem;
  font-size: 1.5rem;
  font-weight: 600;
  text-align: right;
}

.orders-stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 1.5rem;
}

@media (max-width: 768px) {
  .orders-stats-grid {
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1rem;
  }
}

@media (max-width: 480px) {
  .orders-stats-grid {
    grid-template-columns: 1fr 1fr;
  }
}

/* بطاقات حالة الطلبات */
.order-stat {
  background: linear-gradient(145deg, #ffffff 0%, #f8f9fa 100%);
  border-radius: 16px;
  padding: 1.5rem;
  text-align: center;
  box-shadow: 
    0 8px 25px rgba(0, 0, 0, 0.1),
    0 4px 8px rgba(0, 0, 0, 0.05);
  transition: all 0.4s ease;
  position: relative;
  overflow: hidden;
  border: 2px solid transparent;
}

.order-stat::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  transition: height 0.3s ease;
}

.order-stat:hover {
  transform: translateY(-5px);
  box-shadow: 
    0 15px 35px rgba(0, 0, 0, 0.15),
    0 6px 12px rgba(0, 0, 0, 0.1);
}

.order-stat:hover::before {
  height: 6px;
}

.order-stat i {
  font-size: 2.5rem;
  margin-bottom: 1rem;
  display: block;
}

.order-stat h3 {
  font-size: 2rem;
  font-weight: 700;
  margin: 0.5rem 0;
}

.order-stat p {
  font-size: 0.9rem;
  margin: 0;
  font-weight: 500;
  opacity: 0.8;
}

/* حالات الطلبات - الألوان */
.order-stat.pending {
  border-color: rgba(241, 196, 15, 0.3);
}

.order-stat.pending::before {
  background: linear-gradient(90deg, #f1c40f 0%, #f39c12 100%);
}

.order-stat.pending i {
  color: #f39c12;
}

.order-stat.pending h3 {
  color: #e67e22;
}

.order-stat.preparing {
  border-color: rgba(52, 152, 219, 0.3);
}

.order-stat.preparing::before {
  background: linear-gradient(90deg, #3498db 0%, #2980b9 100%);
}

.order-stat.preparing i {
  color: #3498db;
}

.order-stat.preparing h3 {
  color: #2980b9;
}

.order-stat.ready {
  border-color: rgba(46, 204, 113, 0.3);
}

.order-stat.ready::before {
  background: linear-gradient(90deg, #2ecc71 0%, #27ae60 100%);
}

.order-stat.ready i {
  color: #27ae60;
}

.order-stat.ready h3 {
  color: #229954;
}

.order-stat.completed {
  border-color: rgba(155, 89, 182, 0.3);
}

.order-stat.completed::before {
  background: linear-gradient(90deg, #9b59b6 0%, #8e44ad 100%);
}

.order-stat.completed i {
  color: #8e44ad;
}

.order-stat.completed h3 {
  color: #7d3c98;
}

/* تأكيد عرض البطاقات جانب بعض */
.summary-cards,
.data-table {
  width: 100%;
  max-width: 100%;
  overflow-x: auto;
}

/* ============================== */
/* تنسيقات العناصر الديناميكية */
/* ============================== */

/* أشرطة الكمية */
.quantity-bar {
  background: #ecf0f1;
  border-radius: 6px;
  height: 8px;
  overflow: hidden;
  position: relative;
}

.quantity-bar [data-width] {
  height: 100%;
  background: linear-gradient(90deg, #3498db, #2980b9);
  border-radius: 6px;
  transition: width 0.6s ease;
  width: 0%; /* القيمة الافتراضية */
}

.quantity-fill[data-width] {
  height: 100%;
  border-radius: 6px;
  transition: width 0.6s ease;
  width: 0%; /* القيمة الافتراضية */
}

/* رؤوس الفئات */
.category-header[data-color] {
  background-color: #8B4513; /* اللون الافتراضي */
  color: white;
  padding: 1.5rem;
  border-radius: 12px 12px 0 0;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

/* رموز الألوان */
.color-code[data-color] {
  background-color: #8B4513; /* اللون الافتراضي */
  color: white;
  padding: 0.2rem 0.5rem;
  border-radius: 4px;
  font-size: 0.8rem;
  font-family: monospace;
}

.color-preview[data-color] {
  background-color: #8B4513; /* اللون الافتراضي */
  width: 30px;
  height: 30px;
  border-radius: 50%;
  border: 2px solid #ecf0f1;
  display: inline-block;
  vertical-align: middle;
}
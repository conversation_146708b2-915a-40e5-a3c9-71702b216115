/* ============================== */
/* MenuManagerScreen - Menu Management Interface */
/* ============================== */

/* استيراد المتغيرات المميزة لشاشة القائمة */
@import '../variables/menu-variables.css';

/* ================ */
/* Screen Container */
/* ================ */

.menuManagerScreen {
  padding: 2rem;
  max-width: 1400px;
  margin: 0 auto;
  direction: rtl;
  background: var(--menu-bg-secondary);
  min-height: 100vh;
}

/* ================ */
/* Screen Header */
/* ================ */

.menuManagerScreen__header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 2rem;
  padding-bottom: 1rem;
  border-bottom: 2px solid #f1f2f6;
  flex-wrap: wrap;
  gap: 1rem;
}

.menuManagerScreen__title-section {
  flex: 1;
}

.menuManagerScreen__title {
  color: var(--menu-primary);
  font-size: 2rem;
  font-weight: 700;
  margin: 0 0 0.5rem 0;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.menuManagerScreen__title-icon {
  color: var(--menu-warning);
  font-size: 1.8rem;
}

.menuManagerScreen__subtitle {
  color: #7f8c8d;
  font-size: 1rem;
  margin: 0;
}

.menuManagerScreen__add-btn {
  background: linear-gradient(135deg, var(--menu-success) 0%, #229954 100%);
  color: var(--menu-text-light);
  border: none;
  padding: 1rem 2rem;
  border-radius: var(--menu-border-radius);
  font-size: 1rem;
  font-weight: 600;
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  transition: var(--menu-transition-base);
  box-shadow: var(--menu-shadow-md);
}

.menuManagerScreen__add-btn:hover {
  background: linear-gradient(135deg, #229954 0%, #1e8449 100%);
  transform: translateY(-2px);
  box-shadow: 0 4px 15px rgba(39, 174, 96, 0.3);
}

/* ================ */
/* Filters and Search */
/* ================ */

.menuManagerScreen__filters {
  background: var(--menu-text-light);
  padding: 1.5rem;
  border-radius: var(--menu-border-radius);
  box-shadow: var(--menu-shadow-md);
  margin-bottom: 2rem;
  border: 1px solid #f1f2f6;
}

.menuManagerScreen__filters-header {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  margin-bottom: 1rem;
  color: var(--menu-primary);
  font-size: 1.1rem;
  font-weight: 600;
}

.menuManagerScreen__filters-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 1rem;
  align-items: end;
}

.menuManagerScreen__filter-group {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.menuManagerScreen__filter-label {
  font-size: 0.9rem;
  font-weight: 600;
  color: var(--menu-primary);
}

.menuManagerScreen__search-input,
.menuManagerScreen__filter-select {
  width: 100%;
  padding: 0.75rem;
  border: 2px solid #e1e8ed;
  border-radius: 8px;
  font-size: 0.95rem;
  background: var(--menu-text-light);
  transition: var(--menu-transition-base);
  text-align: right;
  direction: rtl;
}

.menuManagerScreen__search-input:focus,
.menuManagerScreen__filter-select:focus {
  outline: none;
  border-color: var(--menu-secondary);
  box-shadow: 0 0 0 3px rgba(52, 152, 219, 0.1);
}

/* ================ */
/* Menu Items Grid */
/* ================ */

.menuManagerScreen__menu-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
  gap: 1.5rem;
}

.menuManagerScreen__menu-item {
  background: var(--menu-text-light);
  border-radius: var(--menu-border-radius);
  box-shadow: var(--menu-shadow-md);
  overflow: hidden;
  transition: var(--menu-transition-base);
  border: 1px solid #f1f2f6;
  position: relative;
}

.menuManagerScreen__menu-item:hover {
  transform: translateY(-5px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

.menuManagerScreen__menu-item--available {
  border-left: 4px solid var(--menu-success);
}

.menuManagerScreen__menu-item--unavailable {
  border-left: 4px solid var(--menu-danger);
  opacity: 0.8;
}

/* ================ */
/* Menu Item Header */
/* ================ */

.menuManagerScreen__item-header {
  padding: 1.5rem;
  background: linear-gradient(135deg, var(--menu-bg-secondary), #ffffff);
  border-bottom: 1px solid #f1f2f6;
  position: relative;
}

.menuManagerScreen__item-image {
  width: 80px;
  height: 80px;
  border-radius: 50%;
  background: linear-gradient(135deg, var(--menu-warning), #d68910);
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto 1rem auto;
  color: var(--menu-text-light);
  font-size: 2rem;
  box-shadow: 0 4px 15px rgba(243, 156, 18, 0.3);
}

.menuManagerScreen__item-name {
  font-size: 1.3rem;
  font-weight: 700;
  color: var(--menu-primary);
  text-align: center;
  margin: 0 0 0.5rem 0;
}

.menuManagerScreen__item-category {
  text-align: center;
  display: inline-block;
  background: var(--menu-secondary);
  color: var(--menu-text-light);
  padding: 0.25rem 0.75rem;
  border-radius: 15px;
  font-size: 0.8rem;
  font-weight: 500;
  margin: 0 auto;
}

.menuManagerScreen__item-status {
  position: absolute;
  top: 1rem;
  left: 1rem;
  padding: 0.25rem 0.5rem;
  border-radius: 15px;
  font-size: 0.75rem;
  font-weight: 600;
  text-transform: uppercase;
}

.menuManagerScreen__item-status--available {
  background: var(--menu-success);
  color: var(--menu-text-light);
}

.menuManagerScreen__item-status--unavailable {
  background: var(--menu-danger);
  color: var(--menu-text-light);
}

/* ================ */
/* Menu Item Body */
/* ================ */

.menuManagerScreen__item-body {
  padding: 1.5rem;
}

.menuManagerScreen__item-description {
  color: #7f8c8d;
  font-size: 0.95rem;
  line-height: 1.5;
  margin-bottom: 1.5rem;
  text-align: center;
}

.menuManagerScreen__item-sizes {
  margin-bottom: 1.5rem;
}

.menuManagerScreen__sizes-title {
  font-size: 1rem;
  font-weight: 600;
  color: var(--menu-primary);
  margin-bottom: 0.75rem;
  text-align: center;
}

.menuManagerScreen__sizes-list {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.menuManagerScreen__size-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0.75rem;
  background: var(--menu-bg-secondary);
  border-radius: 8px;
  border: 2px solid transparent;
  transition: var(--menu-transition-base);
}

.menuManagerScreen__size-item:hover {
  background: #e9ecef;
  border-color: var(--menu-secondary);
}

.menuManagerScreen__size-name {
  font-weight: 600;
  color: var(--menu-primary);
  font-size: 0.95rem;
}

.menuManagerScreen__size-price {
  font-weight: 700;
  color: var(--menu-success);
  font-size: 1rem;
}

/* ================ */
/* Item Info */
/* ================ */

.menuManagerScreen__item-info {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 0.75rem;
  margin-bottom: 1.5rem;
}

.menuManagerScreen__info-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 0.75rem;
  background: var(--menu-bg-secondary);
  border-radius: 8px;
}

.menuManagerScreen__info-label {
  font-size: 0.8rem;
  color: #7f8c8d;
  margin-bottom: 0.25rem;
}

.menuManagerScreen__info-value {
  font-weight: 600;
  color: var(--menu-primary);
  font-size: 0.95rem;
}

.menuManagerScreen__info-value--stock {
  color: var(--menu-warning);
}

.menuManagerScreen__info-value--sales {
  color: var(--menu-success);
}

/* ================ */
/* Item Actions */
/* ================ */

.menuManagerScreen__item-actions {
  display: flex;
  gap: 0.5rem;
  justify-content: center;
  flex-wrap: wrap;
}

.menuManagerScreen__action-btn {
  padding: 0.75rem 1rem;
  border: none;
  border-radius: 8px;
  font-size: 0.85rem;
  font-weight: 600;
  cursor: pointer;
  transition: var(--menu-transition-base);
  display: flex;
  align-items: center;
  gap: 0.5rem;
  flex: 1;
  justify-content: center;
  min-width: 90px;
}

.menuManagerScreen__action-btn--edit {
  background: var(--menu-secondary);
  color: var(--menu-text-light);
}

.menuManagerScreen__action-btn--edit:hover {
  background: #2980b9;
  transform: translateY(-1px);
}

.menuManagerScreen__action-btn--delete {
  background: var(--menu-danger);
  color: var(--menu-text-light);
}

.menuManagerScreen__action-btn--delete:hover {
  background: #c0392b;
  transform: translateY(-1px);
}

.menuManagerScreen__action-btn--toggle {
  background: var(--menu-warning);
  color: var(--menu-text-light);
}

.menuManagerScreen__action-btn--toggle:hover {
  background: #d68910;
  transform: translateY(-1px);
}

/* ================ */
/* Statistics Cards */
/* ================ */

.menuManagerScreen__stats {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 1rem;
  margin-bottom: 2rem;
}

.menuManagerScreen__stat-card {
  background: var(--menu-text-light);
  padding: 1.5rem;
  border-radius: var(--menu-border-radius);
  box-shadow: var(--menu-shadow-md);
  text-align: center;
  border: 1px solid #f1f2f6;
  transition: var(--menu-transition-base);
  position: relative;
  overflow: hidden;
}

.menuManagerScreen__stat-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
}

.menuManagerScreen__stat-card--total::before {
  background: var(--menu-secondary);
}

.menuManagerScreen__stat-card--available::before {
  background: var(--menu-success);
}

.menuManagerScreen__stat-card--unavailable::before {
  background: var(--menu-danger);
}

.menuManagerScreen__stat-card--categories::before {
  background: var(--menu-warning);
}

.menuManagerScreen__stat-card:hover {
  transform: translateY(-3px);
  box-shadow: 0 6px 20px rgba(0, 0, 0, 0.1);
}

.menuManagerScreen__stat-icon {
  font-size: 2rem;
  margin-bottom: 0.5rem;
}

.menuManagerScreen__stat-card--total .menuManagerScreen__stat-icon {
  color: var(--menu-secondary);
}

.menuManagerScreen__stat-card--available .menuManagerScreen__stat-icon {
  color: var(--menu-success);
}

.menuManagerScreen__stat-card--unavailable .menuManagerScreen__stat-icon {
  color: var(--menu-danger);
}

.menuManagerScreen__stat-card--categories .menuManagerScreen__stat-icon {
  color: var(--menu-warning);
}

.menuManagerScreen__stat-number {
  font-size: 2rem;
  font-weight: 700;
  color: var(--menu-primary);
  margin: 0;
}

.menuManagerScreen__stat-label {
  color: #7f8c8d;
  font-size: 0.9rem;
  margin: 0.5rem 0 0 0;
  font-weight: 500;
}

/* ================ */
/* Empty State */
/* ================ */

.menuManagerScreen__empty-state {
  text-align: center;
  padding: 3rem;
  color: #7f8c8d;
  background: var(--menu-text-light);
  border-radius: var(--menu-border-radius);
  box-shadow: var(--menu-shadow-md);
  grid-column: 1 / -1;
}

.menuManagerScreen__empty-icon {
  font-size: 4rem;
  color: var(--menu-warning);
  margin-bottom: 1rem;
}

.menuManagerScreen__empty-title {
  font-size: 1.5rem;
  font-weight: 600;
  color: var(--menu-primary);
  margin-bottom: 0.5rem;
}

.menuManagerScreen__empty-message {
  font-size: 1rem;
  margin-bottom: 2rem;
}

/* ================ */
/* Loading State */
/* ================ */

.menuManagerScreen__loading {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 3rem;
  color: var(--menu-secondary);
  grid-column: 1 / -1;
}

.menuManagerScreen__loading-spinner {
  font-size: 2rem;
  margin-bottom: 1rem;
  animation: menuManagerScreen-spin 1s linear infinite;
}

@keyframes menuManagerScreen-spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.menuManagerScreen__loading-text {
  font-size: 1.1rem;
  font-weight: 500;
}

/* ================ */
/* Responsive Design */
/* ================ */

@media (max-width: 768px) {
  .menuManagerScreen {
    padding: 1rem;
  }
  
  .menuManagerScreen__header {
    flex-direction: column;
    align-items: stretch;
  }
  
  .menuManagerScreen__title {
    font-size: 1.5rem;
    text-align: center;
  }
  
  .menuManagerScreen__add-btn {
    width: 100%;
    justify-content: center;
  }
  
  .menuManagerScreen__filters-grid {
    grid-template-columns: 1fr;
  }
  
  .menuManagerScreen__stats {
    grid-template-columns: repeat(2, 1fr);
  }
  
  .menuManagerScreen__menu-grid {
    grid-template-columns: 1fr;
  }
  
  .menuManagerScreen__item-actions {
    flex-direction: column;
  }
  
  .menuManagerScreen__action-btn {
    flex: none;
  }
}

@media (max-width: 480px) {
  .menuManagerScreen__stats {
    grid-template-columns: 1fr;
  }
  
  .menuManagerScreen__item-info {
    grid-template-columns: 1fr;
  }
  
  .menuManagerScreen__item-image {
    width: 60px;
    height: 60px;
    font-size: 1.5rem;
  }
  
  .menuManagerScreen__item-name {
    font-size: 1.1rem;
  }
}

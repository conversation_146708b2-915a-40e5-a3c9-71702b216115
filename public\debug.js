// إضافة debugging للـ browser console
console.log('🔧 Debug script loaded');
console.log('🔧 Current URL:', window.location.href);
console.log('🔧 Environment variables:', {
  VITE_API_URL: import.meta?.env?.VITE_API_URL || 'undefined',
  MODE: import.meta?.env?.MODE || 'undefined'
});

// Test direct API call
async function testDirectConnection() {
  console.log('🧪 Testing direct API connection...');
  try {
    const response = await fetch('http://localhost:5000/health', {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
        'Accept': 'application/json'
      }
    });
    
    console.log('🧪 Direct API response:', {
      status: response.status,
      ok: response.ok,
      statusText: response.statusText
    });
    
    if (response.ok) {
      const data = await response.json();
      console.log('🧪 Direct API data:', data);
    }
  } catch (error) {
    console.error('🧪 Direct API error:', error);
  }
}

// تشغيل الاختبار بعد ثانيتين
setTimeout(testDirectConnection, 2000);

// إضافة إمكانية الوصول للدوال من Console
window.debugCoffeeApp = {
  testDirectConnection,
  clearCache: () => {
    localStorage.clear();
    sessionStorage.clear();
    console.log('🧹 Cache cleared');
  },
  hardRefresh: () => {
    window.location.reload(true);
  }
};

console.log('🔧 Debug functions available in window.debugCoffeeApp');

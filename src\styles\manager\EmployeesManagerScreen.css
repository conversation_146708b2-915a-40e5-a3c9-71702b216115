/* ============================== */
/* EmployeesManagerScreen - Employee Management Interface */
/* ============================== */

/* ??????? ????????? ??????? ????? ???????? */
@import '../variables/employees-variables.css';

/* ================ */
/* Screen Container */
/* ================ */

.employeesManagerScreen {
  padding: 2rem;
  max-width: 1400px;
  margin: 0 auto;
  direction: rtl;
  background: var(--employees-light-bg);
  min-height: 100vh;
}

/* ================ */
/* Screen Header */
/* ================ */

.employeesManagerScreen__header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 2rem;
  padding-bottom: 1rem;
  border-bottom: 2px solid #f1f2f6;
  flex-wrap: wrap;
  gap: 1rem;
}

.employeesManagerScreen__title-section {
  flex: 1;
}

.employeesManagerScreen__title {
  color: var(--employees-primary-color);
  font-size: 2rem;
  font-weight: 700;
  margin: 0 0 0.5rem 0;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.employeesManagerScreen__title-icon {
  color: var(--employees-secondary-color);
  font-size: 1.8rem;
}

.employeesManagerScreen__subtitle {
  color: #7f8c8d;
  font-size: 1rem;
  margin: 0;
}

.employeesManagerScreen__add-btn {
  background: linear-gradient(135deg, var(--employees-success-color) 0%, #229954 100%);
  color: var(--employees-white);
  border: none;
  padding: 1rem 2rem;
  border-radius: var(--employees-border-radius);
  font-size: 1rem;
  font-weight: 600;
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  transition: var(--employees-transition);
  box-shadow: var(--employees-box-shadow);
}

.employeesManagerScreen__add-btn:hover {
  background: linear-gradient(135deg, #229954 0%, #1e8449 100%);
  transform: translateY(-2px);
  box-shadow: 0 4px 15px rgba(39, 174, 96, 0.3);
}

.employeesManagerScreen__add-btn-icon {
  font-size: 1rem;
}

/* ================ */
/* Search and Filters */
/* ================ */

.employeesManagerScreen__filters {
  background: var(--employees-white);
  padding: 1.5rem;
  border-radius: var(--employees-border-radius);
  box-shadow: var(--employees-box-shadow);
  margin-bottom: 2rem;
  border: 1px solid #f1f2f6;
}

.employeesManagerScreen__filters-header {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  margin-bottom: 1rem;
  color: var(--employees-primary-color);
  font-size: 1.1rem;
  font-weight: 600;
}

.employeesManagerScreen__filters-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 1rem;
}

.employeesManagerScreen__search-input,
.employeesManagerScreen__filter-select {
  width: 100%;
  padding: 0.75rem;
  border: 2px solid #e1e8ed;
  border-radius: 8px;
  font-size: 0.95rem;
  background: var(--employees-white);
  transition: var(--employees-transition);
  text-align: right;
  direction: rtl;
}

.employeesManagerScreen__search-input:focus,
.employeesManagerScreen__filter-select:focus {
  outline: none;
  border-color: var(--employees-secondary-color);
  box-shadow: 0 0 0 3px rgba(52, 152, 219, 0.1);
}

/* ================ */
/* Statistics Cards */
/* ================ */

.employeesManagerScreen__stats {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 1rem;
  margin-bottom: 2rem;
}

.employeesManagerScreen__stat-card {
  background: var(--employees-white);
  padding: 1.5rem;
  border-radius: var(--employees-border-radius);
  box-shadow: var(--employees-box-shadow);
  text-align: center;
  border: 1px solid #f1f2f6;
  transition: var(--employees-transition);
  position: relative;
  overflow: hidden;
}

.employeesManagerScreen__stat-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: var(--employees-secondary-color);
}

.employeesManagerScreen__stat-card--active::before {
  background: var(--employees-success-color);
}

.employeesManagerScreen__stat-card--inactive::before {
  background: var(--employees-danger-color);
}

.employeesManagerScreen__stat-card--total::before {
  background: var(--employees-secondary-color);
}

.employeesManagerScreen__stat-card:hover {
  transform: translateY(-3px);
  box-shadow: 0 6px 20px rgba(0, 0, 0, 0.1);
}

.employeesManagerScreen__stat-icon {
  font-size: 2rem;
  margin-bottom: 0.5rem;
  color: var(--employees-secondary-color);
}

.employeesManagerScreen__stat-card--active .employeesManagerScreen__stat-icon {
  color: var(--employees-success-color);
}

.employeesManagerScreen__stat-card--inactive .employeesManagerScreen__stat-icon {
  color: var(--employees-danger-color);
}

.employeesManagerScreen__stat-number {
  font-size: 2rem;
  font-weight: 700;
  color: var(--employees-primary-color);
  margin: 0;
}

.employeesManagerScreen__stat-label {
  color: #7f8c8d;
  font-size: 0.9rem;
  margin: 0.5rem 0 0 0;
  font-weight: 500;
}

/* ================ */
/* Employees Grid */
/* ================ */

.employeesManagerScreen__employees-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
  gap: 1.5rem;
}

.employeesManagerScreen__employee-card {
  background: var(--employees-white);
  border-radius: var(--employees-border-radius);
  box-shadow: var(--employees-box-shadow);
  overflow: hidden;
  transition: var(--employees-transition);
  border: 1px solid #f1f2f6;
  position: relative;
}

.employeesManagerScreen__employee-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

.employeesManagerScreen__employee-card--active {
  border-left: 4px solid var(--employees-success-color);
}

.employeesManagerScreen__employee-card--inactive {
  border-left: 4px solid var(--employees-danger-color);
  opacity: 0.8;
}

/* ================ */
/* Employee Card Header */
/* ================ */

.employeesManagerScreen__employee-header {
  padding: 1.5rem;
  background: linear-gradient(135deg, var(--employees-light-bg), #ffffff);
  border-bottom: 1px solid #f1f2f6;
  position: relative;
}

.employeesManagerScreen__employee-avatar {
  width: 60px;
  height: 60px;
  border-radius: 50%;
  background: linear-gradient(135deg, var(--employees-secondary-color), #2980b9);
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto 1rem auto;
  color: var(--employees-white);
  font-size: 1.5rem;
  font-weight: 700;
  box-shadow: 0 4px 15px rgba(52, 152, 219, 0.3);
}

.employeesManagerScreen__employee-name {
  font-size: 1.2rem;
  font-weight: 700;
  color: var(--employees-primary-color);
  text-align: center;
  margin: 0 0 0.5rem 0;
}

.employeesManagerScreen__employee-role {
  text-align: center;
  display: inline-block;
  background: var(--employees-secondary-color);
  color: var(--employees-white);
  padding: 0.25rem 0.75rem;
  border-radius: 15px;
  font-size: 0.8rem;
  font-weight: 500;
  margin: 0 auto;
}

.employeesManagerScreen__employee-status {
  position: absolute;
  top: 1rem;
  left: 1rem;
  padding: 0.25rem 0.5rem;
  border-radius: 15px;
  font-size: 0.75rem;
  font-weight: 600;
  text-transform: uppercase;
}

.employeesManagerScreen__employee-status--active {
  background: var(--employees-success-color);
  color: var(--employees-white);
}

.employeesManagerScreen__employee-status--inactive {
  background: var(--employees-danger-color);
  color: var(--employees-white);
}

/* ================ */
/* Employee Card Body */
/* ================ */

.employeesManagerScreen__employee-body {
  padding: 1.5rem;
}

.employeesManagerScreen__employee-info {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
  margin-bottom: 1.5rem;
}

.employeesManagerScreen__info-item {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  color: var(--employees-primary-color);
  font-size: 0.9rem;
}

.employeesManagerScreen__info-icon {
  color: var(--employees-secondary-color);
  width: 16px;
  text-align: center;
}

.employeesManagerScreen__info-label {
  font-weight: 600;
  min-width: 80px;
}

.employeesManagerScreen__info-value {
  color: #7f8c8d;
}

/* ================ */
/* Employee Performance */
/* ================ */

.employeesManagerScreen__employee-performance {
  background: var(--employees-light-bg);
  padding: 1rem;
  border-radius: 8px;
  margin-bottom: 1rem;
}

.employeesManagerScreen__performance-title {
  font-size: 0.9rem;
  font-weight: 600;
  color: var(--employees-primary-color);
  margin-bottom: 0.75rem;
  text-align: center;
}

.employeesManagerScreen__performance-stats {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 0.5rem;
}

.employeesManagerScreen__performance-stat {
  text-align: center;
  padding: 0.5rem;
  background: var(--employees-white);
  border-radius: 6px;
}

.employeesManagerScreen__performance-number {
  font-size: 1.2rem;
  font-weight: 700;
  color: var(--employees-secondary-color);
  margin: 0;
}

.employeesManagerScreen__performance-label {
  font-size: 0.75rem;
  color: #7f8c8d;
  margin: 0;
}

/* ================ */
/* Employee Actions */
/* ================ */

.employeesManagerScreen__employee-actions {
  display: flex;
  gap: 0.5rem;
  justify-content: center;
  flex-wrap: wrap;
}

.employeesManagerScreen__action-btn {
  padding: 0.5rem 1rem;
  border: none;
  border-radius: 6px;
  font-size: 0.85rem;
  font-weight: 500;
  cursor: pointer;
  transition: var(--employees-transition);
  display: flex;
  align-items: center;
  gap: 0.25rem;
  flex: 1;
  justify-content: center;
  min-width: 70px;
}

.employeesManagerScreen__action-btn--edit {
  background: var(--employees-secondary-color);
  color: var(--employees-white);
}

.employeesManagerScreen__action-btn--edit:hover {
  background: #2980b9;
  transform: translateY(-1px);
}

.employeesManagerScreen__action-btn--delete {
  background: var(--employees-danger-color);
  color: var(--employees-white);
}

.employeesManagerScreen__action-btn--delete:hover {
  background: #c0392b;
  transform: translateY(-1px);
}

.employeesManagerScreen__action-btn--toggle {
  background: var(--employees-warning-color);
  color: var(--employees-white);
}

.employeesManagerScreen__action-btn--toggle:hover {
  background: #d68910;
  transform: translateY(-1px);
}

.employeesManagerScreen__action-btn--activate {
  background: var(--employees-success-color);
  color: var(--employees-white);
}

.employeesManagerScreen__action-btn--activate:hover {
  background: #229954;
  transform: translateY(-1px);
}

/* ================ */
/* Empty State */
/* ================ */

.employeesManagerScreen__empty-state {
  text-align: center;
  padding: 3rem;
  color: #7f8c8d;
  background: var(--employees-white);
  border-radius: var(--employees-border-radius);
  box-shadow: var(--employees-box-shadow);
}

.employeesManagerScreen__empty-icon {
  font-size: 4rem;
  color: var(--employees-secondary-color);
  margin-bottom: 1rem;
}

.employeesManagerScreen__empty-title {
  font-size: 1.5rem;
  font-weight: 600;
  color: var(--employees-primary-color);
  margin-bottom: 0.5rem;
}

.employeesManagerScreen__empty-message {
  font-size: 1rem;
  margin-bottom: 2rem;
}

/* ================ */
/* Loading State */
/* ================ */

.employeesManagerScreen__loading {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 3rem;
  color: var(--employees-secondary-color);
}

.employeesManagerScreen__loading-spinner {
  font-size: 2rem;
  margin-bottom: 1rem;
  animation: employeesManagerScreen-spin 1s linear infinite;
}

@keyframes employeesManagerScreen-spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.employeesManagerScreen__loading-text {
  font-size: 1.1rem;
  font-weight: 500;
}

/* ================ */
/* Modal Styles */
/* ================ */

.employeesManagerScreen__modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  padding: 1rem;
}

.employeesManagerScreen__modal {
  background: var(--employees-white);
  border-radius: var(--employees-border-radius);
  box-shadow: 0 10px 40px rgba(0, 0, 0, 0.2);
  max-width: 500px;
  width: 100%;
  max-height: 90vh;
  overflow-y: auto;
}

.employeesManagerScreen__modal-header {
  padding: 1.5rem;
  border-bottom: 1px solid #f1f2f6;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.employeesManagerScreen__modal-title {
  font-size: 1.3rem;
  font-weight: 700;
  color: var(--employees-primary-color);
  margin: 0;
}

.employeesManagerScreen__modal-close {
  background: none;
  border: none;
  font-size: 1.5rem;
  color: #7f8c8d;
  cursor: pointer;
  padding: 0.5rem;
  border-radius: 50%;
  transition: var(--employees-transition);
}

.employeesManagerScreen__modal-close:hover {
  background: var(--employees-light-bg);
  color: var(--employees-danger-color);
}

.employeesManagerScreen__modal-body {
  padding: 1.5rem;
}

/* ================ */
/* Responsive Design */
/* ================ */

@media (max-width: 768px) {
  .employeesManagerScreen {
    padding: 1rem;
  }
  
  .employeesManagerScreen__header {
    flex-direction: column;
    align-items: stretch;
  }
  
  .employeesManagerScreen__title {
    font-size: 1.5rem;
    text-align: center;
  }
  
  .employeesManagerScreen__add-btn {
    width: 100%;
    justify-content: center;
  }
  
  .employeesManagerScreen__filters-grid {
    grid-template-columns: 1fr;
  }
  
  .employeesManagerScreen__stats {
    grid-template-columns: repeat(2, 1fr);
  }
  
  .employeesManagerScreen__employees-grid {
    grid-template-columns: 1fr;
  }
  
  .employeesManagerScreen__employee-actions {
    flex-direction: column;
  }
  
  .employeesManagerScreen__action-btn {
    flex: none;
  }
}

@media (max-width: 480px) {
  .employeesManagerScreen__stats {
    grid-template-columns: 1fr;
  }
  
  .employeesManagerScreen__performance-stats {
    grid-template-columns: 1fr;
  }
  
  .employeesManagerScreen__modal {
    margin: 0.5rem;
    max-width: calc(100% - 1rem);
  }
}


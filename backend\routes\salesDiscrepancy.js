// API endpoints لتحليل وإصلاح التباين في مبيعات النُدُل
const express = require('express');
const router = express.Router();
const { ObjectId } = require('mongodb');

// تحليل التباين في المبيعات
router.get('/analyze-discrepancy', async (req, res) => {
    try {
        const { db } = req.app.locals;
        const ordersCollection = db.collection('orders');
        const usersCollection = db.collection('users');

        // حساب إجمالي المبيعات
        const totalSalesResult = await ordersCollection.aggregate([
            {
                $match: {
                    status: 'completed',
                    total: { $exists: true, $ne: null }
                }
            },
            {
                $group: {
                    _id: null,
                    totalSales: { $sum: '$total' },
                    orderCount: { $sum: 1 }
                }
            }
        ]).toArray();

        const totalSales = totalSalesResult[0]?.totalSales || 0;
        const totalOrders = totalSalesResult[0]?.orderCount || 0;

        // الحصول على جميع النُدُل
        const waiters = await usersCollection.find({ role: 'waiter' }).toArray();

        // حساب مبيعات كل نادل
        let waiterSalesTotal = 0;
        const waiterBreakdown = [];

        for (const waiter of waiters) {
            const waiterSales = await ordersCollection.aggregate([
                {
                    $match: {
                        waiter: waiter.username,
                        status: 'completed',
                        total: { $exists: true, $ne: null }
                    }
                },
                {
                    $group: {
                        _id: null,
                        sales: { $sum: '$total' },
                        orders: { $sum: 1 }
                    }
                }
            ]).toArray();

            const sales = waiterSales[0]?.sales || 0;
            const orders = waiterSales[0]?.orders || 0;

            waiterSalesTotal += sales;

            waiterBreakdown.push({
                name: waiter.name || waiter.username,
                username: waiter.username,
                sales: sales,
                orders: orders
            });
        }

        // حساب التباين
        const discrepancy = totalSales - waiterSalesTotal;

        // البحث عن الطلبات بدون نادل
        const orphanedOrders = await ordersCollection.countDocuments({
            status: 'completed',
            $or: [
                { waiter: { $exists: false } },
                { waiter: null },
                { waiter: '' }
            ]
        });

        // البحث عن طلبات بنُدُل غير موجودين
        const waiterUsernames = waiters.map(w => w.username);
        const invalidWaiterOrders = await ordersCollection.countDocuments({
            status: 'completed',
            waiter: { $exists: true, $ne: null, $ne: '' },
            waiter: { $nin: waiterUsernames }
        });

        res.json({
            success: true,
            data: {
                totalSales,
                waiterSalesTotal,
                discrepancy,
                orphanedOrders,
                invalidWaiterOrders,
                waiterBreakdown: waiterBreakdown.sort((a, b) => b.sales - a.sales)
            }
        });

    } catch (error) {
        console.error('خطأ في تحليل التباين:', error);
        res.status(500).json({
            success: false,
            message: 'فشل في تحليل التباين في المبيعات',
            error: error.message
        });
    }
});

// إصلاح التباين في المبيعات
router.post('/fix-discrepancy', async (req, res) => {
    try {
        const { db } = req.app.locals;
        const ordersCollection = db.collection('orders');
        const usersCollection = db.collection('users');

        let fixedCount = 0;
        let totalFixed = 0;

        // الحصول على نادل افتراضي
        const defaultWaiter = await usersCollection.findOne({
            role: 'waiter',
            status: 'active'
        });

        if (!defaultWaiter) {
            return res.status(400).json({
                success: false,
                message: 'لا يوجد نُدُل نشطين لتعيينهم كافتراضي'
            });
        }

        // إصلاح الطلبات بدون نادل
        const ordersWithoutWaiter = await ordersCollection.find({
            status: 'completed',
            $or: [
                { waiter: { $exists: false } },
                { waiter: null },
                { waiter: '' }
            ]
        }).toArray();

        for (const order of ordersWithoutWaiter) {
            await ordersCollection.updateOne(
                { _id: order._id },
                {
                    $set: {
                        waiter: defaultWaiter.username,
                        updatedAt: new Date(),
                        fixedBy: 'system_repair',
                        fixedAt: new Date()
                    }
                }
            );

            fixedCount++;
            totalFixed += order.total || 0;
        }

        // إصلاح الطلبات بنُدُل غير موجودين
        const waiters = await usersCollection.find({ role: 'waiter' }).toArray();
        const waiterUsernames = waiters.map(w => w.username);

        const ordersWithInvalidWaiter = await ordersCollection.find({
            status: 'completed',
            waiter: { $exists: true, $ne: null, $ne: '' },
            waiter: { $nin: waiterUsernames }
        }).toArray();

        for (const order of ordersWithInvalidWaiter) {
            await ordersCollection.updateOne(
                { _id: order._id },
                {
                    $set: {
                        waiter: defaultWaiter.username,
                        oldWaiter: order.waiter,
                        updatedAt: new Date(),
                        fixedBy: 'system_repair',
                        fixedAt: new Date()
                    }
                }
            );

            fixedCount++;
            totalFixed += order.total || 0;
        }

        // حساب التباين الجديد
        const totalSalesResult = await ordersCollection.aggregate([
            {
                $match: {
                    status: 'completed',
                    total: { $exists: true, $ne: null }
                }
            },
            {
                $group: {
                    _id: null,
                    totalSales: { $sum: '$total' }
                }
            }
        ]).toArray();

        const totalSales = totalSalesResult[0]?.totalSales || 0;

        let waiterSalesTotal = 0;
        for (const waiter of waiters) {
            const waiterSales = await ordersCollection.aggregate([
                {
                    $match: {
                        waiter: waiter.username,
                        status: 'completed',
                        total: { $exists: true, $ne: null }
                    }
                },
                {
                    $group: {
                        _id: null,
                        sales: { $sum: '$total' }
                    }
                }
            ]).toArray();

            waiterSalesTotal += waiterSales[0]?.sales || 0;
        }

        const newDiscrepancy = totalSales - waiterSalesTotal;

        // حفظ تقرير الإصلاح
        const repairReport = {
            timestamp: new Date(),
            fixedOrders: fixedCount,
            totalValueFixed: totalFixed,
            discrepancyAfter: newDiscrepancy,
            defaultWaiterUsed: defaultWaiter.username,
            status: Math.abs(newDiscrepancy) <= 1 ? 'success' : 'partial',
            userId: req.user?.id,
            username: req.user?.username
        };

        await db.collection('repair_reports').insertOne(repairReport);

        res.json({
            success: true,
            data: {
                fixedOrders: fixedCount,
                totalValueFixed: totalFixed,
                discrepancyAfter: newDiscrepancy,
                defaultWaiterUsed: defaultWaiter.username,
                status: Math.abs(newDiscrepancy) <= 1 ? 'success' : 'partial'
            }
        });

    } catch (error) {
        console.error('خطأ في إصلاح التباين:', error);
        res.status(500).json({
            success: false,
            message: 'فشل في إصلاح التباين في المبيعات',
            error: error.message
        });
    }
});

// تقرير المبيعات المُفصل للنُدُل
router.get('/detailed-waiter-report', async (req, res) => {
    try {
        const { db } = req.app.locals;
        const ordersCollection = db.collection('orders');
        const usersCollection = db.collection('users');

        const waiters = await usersCollection.find({ role: 'waiter' }).toArray();
        const detailedReport = [];

        for (const waiter of waiters) {
            // مبيعات اليوم
            const today = new Date();
            today.setHours(0, 0, 0, 0);
            const tomorrow = new Date(today);
            tomorrow.setDate(tomorrow.getDate() + 1);

            const todaySales = await ordersCollection.aggregate([
                {
                    $match: {
                        waiter: waiter.username,
                        status: 'completed',
                        createdAt: { $gte: today, $lt: tomorrow }
                    }
                },
                {
                    $group: {
                        _id: null,
                        sales: { $sum: '$total' },
                        orders: { $sum: 1 }
                    }
                }
            ]).toArray();

            // إجمالي المبيعات
            const totalSales = await ordersCollection.aggregate([
                {
                    $match: {
                        waiter: waiter.username,
                        status: 'completed'
                    }
                },
                {
                    $group: {
                        _id: null,
                        sales: { $sum: '$total' },
                        orders: { $sum: 1 }
                    }
                }
            ]).toArray();

            detailedReport.push({
                name: waiter.name || waiter.username,
                username: waiter.username,
                status: waiter.status || 'active',
                todaySales: todaySales[0]?.sales || 0,
                todayOrders: todaySales[0]?.orders || 0,
                totalSales: totalSales[0]?.sales || 0,
                totalOrders: totalSales[0]?.orders || 0
            });
        }

        res.json({
            success: true,
            data: detailedReport.sort((a, b) => b.totalSales - a.totalSales)
        });

    } catch (error) {
        console.error('خطأ في تقرير النُدُل:', error);
        res.status(500).json({
            success: false,
            message: 'فشل في إنشاء تقرير النُدُل',
            error: error.message
        });
    }
});

module.exports = router;

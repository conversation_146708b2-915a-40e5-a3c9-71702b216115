const fetch = require('node-fetch');

class OrdersDebugger {
  constructor() {
    this.baseURL = 'https://deshacoffee-production.up.railway.app';
    this.waiterId = '684c864e558dd1359d2380f7'; // Bosy
    this.waiterName = 'بوسي';
    this.targetTables = ['1', '2', '29'];
  }

  async debugTablesAndOrders() {
    console.log('🔍 فحص مشكلة الطلبات في الطاولات...\n');
    console.log('='.repeat(60));

    // 1. فحص الطاولات
    await this.checkTables();
    console.log('\n' + '-'.repeat(40) + '\n');
    
    // 2. فحص جميع الطلبات
    await this.checkAllOrders();
    console.log('\n' + '-'.repeat(40) + '\n');
    
    // 3. فحص طلبات النادلة بوسي
    await this.checkBosyOrders();
    console.log('\n' + '-'.repeat(40) + '\n');
    
    // 4. فحص طلبات الطاولات المستهدفة
    await this.checkTableSpecificOrders();
    console.log('\n' + '-'.repeat(40) + '\n');
    
    // 5. فحص API مختلف للطلبات
    await this.checkAlternativeOrdersAPI();
    console.log('\n' + '-'.repeat(40) + '\n');
    
    // 6. تحليل المشكلة وتقديم الحلول
    await this.analyzeAndSuggestSolutions();
  }

  async checkTables() {
    console.log('🏓 فحص بيانات الطاولات...');
    
    try {
      // جرب API الطاولات المختلف
      const endpoints = [
        `/api/v1/table-accounts?waiterId=${this.waiterId}`,
        `/api/v1/table-accounts`,
        `/api/v1/tables?waiterId=${this.waiterId}`,
        `/api/v1/tables`
      ];

      for (const endpoint of endpoints) {
        console.log(`\n📡 اختبار: ${endpoint}`);
        try {
          const response = await fetch(`${this.baseURL}${endpoint}`);
          const result = await response.json();
          
          console.log(`   📊 استجابة ${response.status}:`, JSON.stringify(result, null, 2));
          
          if (result.data && Array.isArray(result.data)) {
            const targetTables = result.data.filter(t => 
              this.targetTables.includes(String(t.tableNumber))
            );
            console.log(`   🎯 الطاولات المستهدفة (${targetTables.length}):`, 
              targetTables.map(t => `طاولة ${t.tableNumber}`));
          }
        } catch (error) {
          console.log(`   ❌ خطأ: ${error.message}`);
        }
      }
    } catch (error) {
      console.log(`❌ خطأ عام في فحص الطاولات: ${error.message}`);
    }
  }

  async checkAllOrders() {
    console.log('📋 فحص جميع الطلبات...');
    
    try {
      const endpoints = [
        '/api/v1/orders',
        '/api/v1/orders?status=active',
        '/api/v1/orders?status=pending',
        '/api/orders'
      ];

      for (const endpoint of endpoints) {
        console.log(`\n📡 اختبار: ${endpoint}`);
        try {
          const response = await fetch(`${this.baseURL}${endpoint}`);
          const result = await response.json();
          
          console.log(`   📊 استجابة ${response.status} - النوع: ${typeof result}`);
          
          let orders = [];
          if (Array.isArray(result)) {
            orders = result;
          } else if (result.data && Array.isArray(result.data)) {
            orders = result.data;
          } else if (result.orders && Array.isArray(result.orders)) {
            orders = result.orders;
          }
          
          console.log(`   📈 إجمالي الطلبات: ${orders.length}`);
          
          if (orders.length > 0) {
            // فحص طلبات بوسي
            const bosyOrders = orders.filter(order => {
              return order.waiterId === this.waiterId || 
                     order.waiterName === this.waiterName ||
                     order.waiterName === 'Bosy' ||
                     (order.waiter && order.waiter.includes('بوسي'));
            });
            
            console.log(`   👤 طلبات بوسي: ${bosyOrders.length}`);
            
            // فحص طلبات الطاولات المستهدفة
            const tableOrders = orders.filter(order => 
              this.targetTables.includes(String(order.tableNumber))
            );
            
            console.log(`   🏓 طلبات الطاولات 1,2,29: ${tableOrders.length}`);
            
            // عرض أمثلة من الطلبات
            if (orders.length > 0) {
              console.log('   📝 مثال على الطلبات:');
              orders.slice(0, 3).forEach((order, index) => {
                console.log(`     ${index + 1}. طاولة ${order.tableNumber}, نادل: ${order.waiterName || order.waiterId}, حالة: ${order.status}, مبلغ: ${order.totalPrice}`);
              });
            }
          }
        } catch (error) {
          console.log(`   ❌ خطأ: ${error.message}`);
        }
      }
    } catch (error) {
      console.log(`❌ خطأ عام في فحص الطلبات: ${error.message}`);
    }
  }

  async checkBosyOrders() {
    console.log('👤 فحص طلبات النادلة بوسي بالتفصيل...');
    
    try {
      const endpoints = [
        `/api/v1/orders?waiterId=${this.waiterId}`,
        `/api/v1/orders?waiterName=${encodeURIComponent(this.waiterName)}`,
        `/api/v1/orders?waiterName=Bosy`,
        `/api/v1/orders/waiter/${this.waiterId}`
      ];

      for (const endpoint of endpoints) {
        console.log(`\n📡 اختبار: ${endpoint}`);
        try {
          const response = await fetch(`${this.baseURL}${endpoint}`);
          const result = await response.json();
          
          console.log(`   📊 استجابة ${response.status}:`, JSON.stringify(result, null, 2));
        } catch (error) {
          console.log(`   ❌ خطأ: ${error.message}`);
        }
      }
    } catch (error) {
      console.log(`❌ خطأ عام في فحص طلبات بوسي: ${error.message}`);
    }
  }

  async checkTableSpecificOrders() {
    console.log('🏓 فحص طلبات كل طاولة على حدة...');
    
    for (const tableNumber of this.targetTables) {
      console.log(`\n📋 فحص طاولة ${tableNumber}:`);
      
      const endpoints = [
        `/api/v1/orders?tableNumber=${tableNumber}`,
        `/api/v1/orders/table/${tableNumber}`,
        `/api/v1/tables/${tableNumber}/orders`
      ];

      for (const endpoint of endpoints) {
        console.log(`   📡 اختبار: ${endpoint}`);
        try {
          const response = await fetch(`${this.baseURL}${endpoint}`);
          const result = await response.json();
          
          console.log(`     📊 استجابة ${response.status}:`, JSON.stringify(result, null, 2));
        } catch (error) {
          console.log(`     ❌ خطأ: ${error.message}`);
        }
      }
    }
  }

  async checkAlternativeOrdersAPI() {
    console.log('🔄 فحص APIs بديلة للطلبات...');
    
    try {
      const endpoints = [
        '/api/orders',
        '/api/order',
        '/api/v2/orders',
        '/api/waiter/orders',
        '/api/table/orders'
      ];

      for (const endpoint of endpoints) {
        console.log(`\n📡 اختبار: ${endpoint}`);
        try {
          const response = await fetch(`${this.baseURL}${endpoint}`);
          const result = await response.json();
          
          console.log(`   📊 استجابة ${response.status} - النوع: ${typeof result}`);
          console.log(`   📝 البيانات:`, JSON.stringify(result, null, 2));
        } catch (error) {
          console.log(`   ❌ خطأ: ${error.message}`);
        }
      }
    } catch (error) {
      console.log(`❌ خطأ عام في فحص APIs البديلة: ${error.message}`);
    }
  }

  async analyzeAndSuggestSolutions() {
    console.log('🔍 تحليل المشكلة واقتراح الحلول...');
    
    console.log('\n📊 المشاكل المحتملة:');
    console.log('1. ❌ API الطلبات لا يرجع البيانات الصحيحة');
    console.log('2. ❌ مشكلة في فلترة الطلبات حسب النادل');
    console.log('3. ❌ مشكلة في ربط الطلبات بالطاولات');
    console.log('4. ❌ مشكلة في Frontend في عرض البيانات');
    console.log('5. ❌ مشكلة في قاعدة البيانات');
    
    console.log('\n💡 الحلول المقترحة:');
    console.log('1. ✅ فحص API الطلبات في Backend');
    console.log('2. ✅ فحص منطق فلترة الطلبات');
    console.log('3. ✅ فحص كود Frontend لجلب الطلبات');
    console.log('4. ✅ فحص قاعدة البيانات مباشرة');
    console.log('5. ✅ إضافة logs للتتبع');
    
    console.log('\n🚀 الخطوات التالية:');
    console.log('1. فحص كود Frontend في WaiterDashboard');
    console.log('2. فحص Backend APIs للطلبات');
    console.log('3. اختبار قاعدة البيانات مباشرة');
    console.log('4. إضافة debugging إضافي');
  }
}

// تشغيل الاختبار
async function main() {
  const ordersDebugger = new OrdersDebugger();
  await ordersDebugger.debugTablesAndOrders();
}

main().catch(console.error);

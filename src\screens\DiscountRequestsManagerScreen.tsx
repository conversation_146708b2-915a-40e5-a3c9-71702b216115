import React, { useState } from 'react';
import { authenticatedPut } from '../utils/apiHelpers';
// استدعاء ملف CSS الجديد المنظم
import '../styles/manager/DiscountRequestsManagerScreen.css';

interface OrderItem {
  _id?: string;
  name?: string;
  productName?: string;
  quantity?: number;
  price?: number;
  size?: string;
  category?: string;
  notes?: string;
  product?: string;
}

interface Order {
  _id: string;
  orderNumber: string;
  items: OrderItem[];
  totalAmount?: number;
  totalPrice?: number;
  totals?: {
    subtotal: number;
    tax: number;
    discount: number;
    total: number;
  };
  status: 'pending' | 'preparing' | 'ready' | 'completed' | 'delivered';
  tableNumber?: number;
  customerName?: string;
  waiterName?: string;
  waiterId?: string;
  chefName?: string;
  staff?: {
    waiter?: string;
    chef?: string;
  };
  createdAt: string;
  updatedAt: string;
}

interface DiscountRequest {
  _id: string;
  orderId: string;
  orderNumber: string;
  waiterName: string;
  amount?: number;
  originalAmount?: number;
  requestedDiscount?: number;
  reason?: string;
  status: 'pending' | 'approved' | 'rejected';
  createdAt: string;
  order?: Order;
  formattedAmount?: string;
  formattedPercentage?: string;
  discountPercentage?: string;
  tableNumber?: number;
  approvedBy?: string;
  approvedByName?: string;
  rejectedBy?: string;
  rejectedByName?: string;
  waiterUsername?: string;
  approvedByUsername?: string;
  rejectedByUsername?: string;
}

interface DiscountRequestsManagerScreenProps {
  discountRequests: DiscountRequest[];
  managerId: string;
  selectedDiscountForDetails: DiscountRequest | null;
  showDiscountDetailsModal: boolean;
  setSelectedDiscountForDetails: (request: DiscountRequest | null) => void;
  setShowDiscountDetailsModal: (show: boolean) => void;
  fetchDiscountRequests: () => void;
  showSuccess: (message: string) => void;
  showError: (message: string) => void;
}

const DiscountRequestsManagerScreen: React.FC<DiscountRequestsManagerScreenProps> = ({
  discountRequests,
  managerId,
  selectedDiscountForDetails,
  showDiscountDetailsModal,
  setSelectedDiscountForDetails,
  setShowDiscountDetailsModal,
  fetchDiscountRequests,
  showSuccess,
  showError
}) => {
  
  const [loadingActions, setLoadingActions] = useState<{ [key: string]: boolean }>({});

  // إحصائيات طلبات الخصم
  const discountStats = {
    total: discountRequests.length,
    pending: discountRequests.filter(r => r.status === 'pending').length,
    approved: discountRequests.filter(r => r.status === 'approved').length,
    rejected: discountRequests.filter(r => r.status === 'rejected').length,
    totalAmount: discountRequests.reduce((sum, r) => {
      const amount = r.amount != null ? Number(r.amount) : 0;
      return sum + (isNaN(amount) ? 0 : amount);
    }, 0)
  };

  const handleApproveRequest = async (request: DiscountRequest) => {
    const confirmApproval = window.confirm(`هل أنت متأكد من قبول طلب الخصم للطلب #${request.orderNumber}؟`);
    if (!confirmApproval) return;

    setLoadingActions(prev => ({ ...prev, [request._id]: true }));

    try {
      console.log('Approving discount request:', request._id);
      const response = await authenticatedPut(`/api/v1/discount-requests/${request._id}`, {
        status: 'approved',
        approvedBy: managerId,
        approvedAt: new Date().toISOString()
      });
      
      if (response.success) {
        showSuccess('تم قبول طلب الخصم بنجاح');
        fetchDiscountRequests();
      } else {
        console.error('Failed to approve discount:', response);
        showError('فشل في قبول طلب الخصم');
      }
    } catch (error) {
      console.error('Error approving discount:', error);
      showError('حدث خطأ في قبول طلب الخصم');
    } finally {
      setLoadingActions(prev => ({ ...prev, [request._id]: false }));
    }
  };

  const handleRejectRequest = async (request: DiscountRequest) => {
    const confirmRejection = window.confirm(`هل أنت متأكد من رفض طلب الخصم للطلب #${request.orderNumber}؟`);
    if (!confirmRejection) return;

    setLoadingActions(prev => ({ ...prev, [request._id]: true }));

    try {
      console.log('Rejecting discount request:', request._id);
      const response = await authenticatedPut(`/api/v1/discount-requests/${request._id}`, {
        status: 'rejected',
        rejectedBy: managerId,
        rejectedAt: new Date().toISOString()
      });
      
      if (response.success) {
        showSuccess('تم رفض طلب الخصم بنجاح');
        fetchDiscountRequests();
      } else {
        console.error('Failed to reject discount:', response);
        showError('فشل في رفض طلب الخصم');
      }
    } catch (error) {
      console.error('Error rejecting discount:', error);
      showError('حدث خطأ في رفض طلب الخصم');
    } finally {
      setLoadingActions(prev => ({ ...prev, [request._id]: false }));
    }
  };

  return (
    <div className="discount-requests-screen">
      {/* إحصائيات طلبات الخصم */}
      <div className="discount-stats-section">
        <div className="stats-grid">
          <div className="stat-card total">
            <div className="stat-icon">
              <i className="fas fa-list"></i>
            </div>
            <div className="stat-content">
              <div className="stat-value">{discountStats.total}</div>
              <div className="stat-label">إجمالي الطلبات</div>
            </div>
          </div>

          <div className="stat-card pending">
            <div className="stat-icon">
              <i className="fas fa-clock"></i>
            </div>
            <div className="stat-content">
              <div className="stat-value">{discountStats.pending}</div>
              <div className="stat-label">قيد الانتظار</div>
            </div>
          </div>

          <div className="stat-card approved">
            <div className="stat-icon">
              <i className="fas fa-check-circle"></i>
            </div>
            <div className="stat-content">
              <div className="stat-value">{discountStats.approved}</div>
              <div className="stat-label">مقبول</div>
            </div>
          </div>

          <div className="stat-card rejected">
            <div className="stat-icon">
              <i className="fas fa-times-circle"></i>
            </div>
            <div className="stat-content">
              <div className="stat-value">{discountStats.rejected}</div>
              <div className="stat-label">مرفوض</div>
            </div>
          </div>
        </div>
      </div>

      {/* قائمة طلبات الخصم */}
      <div className="discount-requests-section">
        <div className="section-header">
          <h3>
            <i className="fas fa-percentage"></i>
            طلبات الخصم ({discountRequests.length})
          </h3>
        </div>

        {discountRequests.length === 0 ? (
          <div className="empty-state">
            <div className="empty-icon">
              <i className="fas fa-inbox"></i>
            </div>
            <h3>لا توجد طلبات خصم</h3>
            <p>لم يتم إنشاء أي طلبات خصم بعد</p>
          </div>
        ) : (
          <div className="discount-requests-grid">
            {discountRequests.map((request) => (
              <div key={request._id} className={`discount-request-card ${request.status}`}>
                <div className="request-header">
                  <div className="request-number">
                    <i className="fas fa-receipt"></i>
                    <span>طلب #{request.orderNumber}</span>
                  </div>
                  <div className={`status-badge ${request.status}`}>
                    {request.status === 'pending' && (
                      <>
                        <i className="fas fa-clock"></i>
                        قيد الانتظار
                      </>
                    )}
                    {request.status === 'approved' && (
                      <>
                        <i className="fas fa-check-circle"></i>
                        مقبول
                      </>
                    )}
                    {request.status === 'rejected' && (
                      <>
                        <i className="fas fa-times-circle"></i>
                        مرفوض
                      </>
                    )}
                  </div>
                </div>

                <div className="request-info">
                  <div className="info-row">
                    <div className="info-item">
                      <i className="fas fa-table"></i>
                      <span>الطاولة: {
                        request.order?.tableNumber || 
                        request.tableNumber || 
                        'غير محدد'
                      }</span>
                    </div>
                    <div className="info-item">
                      <i className="fas fa-user"></i>
                      <span>النادل: {request.waiterName || 'غير محدد'}</span>
                    </div>
                  </div>

                  <div className="info-row">
                    <div className="info-item">
                      <i className="fas fa-money-bill"></i>
                      <span>المبلغ: {
                        request.amount != null 
                          ? Number(request.amount).toFixed(2) 
                          : (request.requestedDiscount != null 
                            ? Number(request.requestedDiscount).toFixed(2)
                            : (request.formattedAmount || 
                              (request.order?.totals?.total && Number(request.order.totals.total).toFixed(2)) ||
                              (request.order?.totalAmount && Number(request.order.totalAmount).toFixed(2)) ||
                              '0.00'))
                      } ج.م</span>
                    </div>
                  </div>

                  {request.reason && (
                    <div className="request-reason">
                      <i className="fas fa-comment"></i>
                      <span>السبب: {request.reason}</span>
                    </div>
                  )}

                  <div className="request-time">
                    <i className="fas fa-calendar"></i>
                    <span>
                      {new Date(request.createdAt).toLocaleDateString('ar-SA', {
                        year: 'numeric',
                        month: 'long',
                        day: 'numeric',
                        hour: '2-digit',
                        minute: '2-digit'
                      })}
                    </span>
                  </div>
                </div>

                <div className="request-actions">
                  <button
                    className="btn btn-info details-btn"
                    onClick={() => {
                      console.log('🔍 Discount request details clicked:', request);
                      console.log('📄 Available fields:', Object.keys(request));
                      console.log('🏓 Table info:', {
                        orderTableNumber: request.order?.tableNumber,
                        requestTableNumber: request.tableNumber,
                        orderId: request.orderId,
                        orderData: request.order
                      });
                      console.log('💰 Amount info:', {
                        amount: request.amount,
                        requestedDiscount: request.requestedDiscount,
                        formattedAmount: request.formattedAmount,
                        orderTotal: request.order?.totals?.total,
                        orderTotalAmount: request.order?.totalAmount
                      });
                      
                      console.log('🔗 Modal functions available:', {
                        setSelectedDiscountForDetails: typeof setSelectedDiscountForDetails,
                        setShowDiscountDetailsModal: typeof setShowDiscountDetailsModal
                      });
                      
                      if (setSelectedDiscountForDetails && setShowDiscountDetailsModal) {
                        console.log('🚀 Setting modal data...');
                        setSelectedDiscountForDetails(request);
                        setShowDiscountDetailsModal(true);
                        console.log('✅ Modal should open now - state updated');
                        
                        // إضافة timeout للتحقق من حالة الـ Modal
                        setTimeout(() => {
                          console.log('⏰ Checking modal state after 100ms');
                          const modalElement = document.querySelector('.modal-overlay');
                          console.log('🔍 Modal element found:', !!modalElement);
                          if (modalElement) {
                            console.log('✅ Modal is in DOM');
                          } else {
                            console.error('❌ Modal is NOT in DOM');
                          }
                        }, 100);
                      } else {
                        console.error('❌ Modal functions not available:', {
                          setSelectedDiscountForDetails: !!setSelectedDiscountForDetails,
                          setShowDiscountDetailsModal: !!setShowDiscountDetailsModal
                        });
                      }
                    }}
                    title="عرض التفاصيل"
                  >
                    <i className="fas fa-eye"></i>
                    التفاصيل
                  </button>

                  {request.status === 'pending' && (
                    <>
                      <button
                        className="btn btn-success approve-btn"
                        onClick={() => handleApproveRequest(request)}
                        title="قبول طلب الخصم"
                        disabled={loadingActions[request._id]}
                      >
                        {loadingActions[request._id] ? (
                          <i className="fas fa-spinner fa-spin"></i>
                        ) : (
                          <i className="fas fa-check"></i>
                        )}
                        قبول
                      </button>

                      <button
                        className="btn btn-danger reject-btn"
                        onClick={() => handleRejectRequest(request)}
                        title="رفض طلب الخصم"
                        disabled={loadingActions[request._id]}
                      >
                        {loadingActions[request._id] ? (
                          <i className="fas fa-spinner fa-spin"></i>
                        ) : (
                          <i className="fas fa-times"></i>
                        )}
                        رفض
                      </button>
                    </>
                  )}
                </div>
              </div>
            ))}
          </div>
        )}
      </div>
    </div>
  );
};

export default DiscountRequestsManagerScreen;

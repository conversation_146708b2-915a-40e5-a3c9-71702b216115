/**
 * Order Compatibility Middleware - محدث للعمل مع Railway + Vercel + MongoDB
 * يحول البيانات المرسلة من Frontend (Vercel) إلى الصيغة المتوقعة في Backend (Railway)
 * موحد للعمل من الحاسوب والهاتف
 */
const orderCompatibilityMiddleware = (req, res, next) => {
  try {
    console.log('🔄 Order Compatibility Middleware - البيانات المستلمة:', JSON.stringify(req.body, null, 2));
    console.log('📱 User-Agent:', req.headers['user-agent']);
    
    const { 
      waiterName, 
      customerName, 
      tableNumber, 
      totalPrice,
      items,
      customer,
      table,
      orderType,
      status 
    } = req.body;

    // 1. تحويل بنية العميل - موحد للحاسوب والهاتف
    if (!req.body.customer && customerName) {
      req.body.customer = { 
        name: customerName.trim(),
        phone: req.body.customerPhone || '',
        email: req.body.customerEmail || ''
      };
      console.log('✅ تم تحويل customerName إلى customer object');
    }
    
    // إذا كان customer موجود ولكن name فارغ، استخدم customerName
    if (req.body.customer && !req.body.customer.name && customerName) {
      req.body.customer.name = customerName.trim();
      console.log('✅ تم تحديث customer.name من customerName');
    }

    // 2. تحويل بنية الطاولة - موحد للحاسوب والهاتف
    const processTableNumber = (tableNumberValue) => {
      if (tableNumberValue === undefined || tableNumberValue === null || tableNumberValue === '') {
        return null;
      }
      
      // تنظيف وتحويل رقم الطاولة
      const cleanTableNumber = String(tableNumberValue).trim();
      const tableNum = parseInt(cleanTableNumber, 10);
      
      if (isNaN(tableNum) || tableNum <= 0) {
        console.error(`رقم طاولة غير صالح: "${tableNumberValue}"`);
        return null;
      }
      
      return tableNum;
    };
    
    // معالجة رقم الطاولة من مصادر مختلفة
    let finalTableNumber = null;
    
    if (req.body.table && req.body.table.number) {
      finalTableNumber = processTableNumber(req.body.table.number);
    } else if (tableNumber) {
      finalTableNumber = processTableNumber(tableNumber);
    } else if (req.body.tableNumber) {
      finalTableNumber = processTableNumber(req.body.tableNumber);
    }
    
    // تحديد نوع الطلب
    const currentOrderType = req.body.orderType || 'dine-in';
    
    // للطلبات في الصالة، رقم الطاولة مطلوب
    if (currentOrderType === 'dine-in') {
      if (!finalTableNumber) {
        console.error('رقم الطاولة مطلوب لطلبات الصالة');
        return res.status(400).json({ 
          success: false, 
          message: 'رقم الطاولة مطلوب لطلبات الصالة' 
        });
      }
      
      req.body.table = {
        number: finalTableNumber,
        section: req.body.table?.section || req.body.tableSection || 'القسم الرئيسي'
      };
    } else {
      // للطلبات الأخرى (توصيل، تيك أواي)
      req.body.table = finalTableNumber ? {
        number: finalTableNumber,
        section: req.body.table?.section || req.body.tableSection || 'القسم الرئيسي'
      } : {};
    }
    
    console.log('✅ تم معالجة رقم الطاولة:', req.body.table);

    // 3. إضافة orderType الافتراضي
    if (!req.body.orderType) {
      req.body.orderType = 'dine-in';
      console.log('✅ تم إضافة orderType افتراضي: dine-in');
    }    // 4. إصلاح بنية المنتجات - موحد للحاسوب والهاتف
    if (req.body.items && Array.isArray(req.body.items)) {
      req.body.items = req.body.items.map(item => {
        const fixedItem = { ...item };
        
        // تحويل productId إلى product (للتوافق مع النظام القديم)
        if (item.productId && !item.product) {
          fixedItem.product = item.productId;
          delete fixedItem.productId;
          console.log('✅ تم تحويل productId إلى product');
        }
        
        // تحويل _id إلى product (للتوافق مع WaiterDashboard)
        if (item._id && !fixedItem.product) {
          fixedItem.product = item._id;
          console.log('✅ تم تحويل _id إلى product');
        }
        
        // إضافة productName إذا كان مفقود
        if (item.name && !fixedItem.productName) {
          fixedItem.productName = item.name;
        }
        
        // التأكد من وجود الكمية والسعر
        if (!fixedItem.quantity || fixedItem.quantity <= 0) {
          fixedItem.quantity = 1;
          console.log('⚠️ تم تعيين الكمية إلى 1 افتراضياً');
        }
        
        // حساب المجموع الفرعي
        if (typeof fixedItem.price === 'number' && typeof fixedItem.quantity === 'number') {
          fixedItem.subtotal = fixedItem.price * fixedItem.quantity;
        } else {
          fixedItem.subtotal = 0;
          console.log(`⚠️ تم تعيين subtotal إلى 0 للمنتج: ${fixedItem.productName || 'غير معروف'}`);
        }
        
        // إضافة الملاحظات إذا كانت مفقودة
        if (!fixedItem.notes) {
          fixedItem.notes = '';
        }
        
        return fixedItem;
      });
      console.log('✅ تم معالجة جميع العناصر بنجاح');
    } else {
      console.error('❌ لا توجد عناصر في الطلب أو البيانات غير صالحة');
      return res.status(400).json({
        success: false,
        message: 'يجب أن يحتوي الطلب على عناصر صالحة'
      });
    }    // 5. إصلاح بنية الإجماليات - موحد للحاسوب والهاتف
    if (!req.body.totals) {
      req.body.totals = {};
    }

    // حساب المجموع الفرعي من العناصر
    const calculatedSubtotal = req.body.items.reduce((sum, item) => {
      return sum + (item.subtotal || 0);
    }, 0);

    // إعداد الإجماليات
    req.body.totals.subtotal = calculatedSubtotal;
    req.body.totals.tax = parseFloat(req.body.totals.tax || 0);
    req.body.totals.discount = parseFloat(req.body.totals.discount || req.body.discountAmount || 0);
    
    // حساب المجموع النهائي
    let finalTotal = calculatedSubtotal + req.body.totals.tax - req.body.totals.discount;
    
    // إضافة رسوم التوصيل إذا كان طلب توصيل
    if (req.body.orderType === 'delivery' && req.body.delivery && req.body.delivery.fee) {
      finalTotal += parseFloat(req.body.delivery.fee || 0);
    }
    
    // استخدام totalPrice إذا كان موجود وصالح، وإلا استخدم المحسوب
    if (totalPrice && !isNaN(parseFloat(totalPrice))) {
      req.body.totals.total = parseFloat(totalPrice);
    } else {
      req.body.totals.total = Math.max(0, finalTotal);
    }
    
    console.log('✅ تم حساب الإجماليات:', req.body.totals);

    // 6. إضافة payment الافتراضي
    if (!req.body.payment) {
      req.body.payment = {
        method: 'cash',
        status: 'pending'
      };
      console.log('✅ تم إضافة payment object افتراضي');
    }

    // 7. معالجة بيانات النادل - موحد للحاسوب والهاتف
    if (waiterName && !req.body.staff) {
      req.body.waiterName = waiterName;
      console.log('✅ تم حفظ waiterName للمعالجة لاحقاً');
    }

    // 8. تنظيف البيانات لتجنب التعارض
    const fieldsToClean = ['customerName', 'tableNumber', 'discountAmount'];
    fieldsToClean.forEach(field => {
      if (req.body[field] !== undefined) {
        delete req.body[field];
      }
    });

    console.log('🎯 البيانات النهائية بعد التوحيد:', {
      customer: req.body.customer,
      table: req.body.table,
      orderType: req.body.orderType,
      items: req.body.items?.length || 0,
      totals: req.body.totals,
      payment: req.body.payment,
      waiterName: req.body.waiterName,
      userAgent: req.headers['user-agent']?.includes('Mobile') ? 'Mobile' : 'Desktop'
    });

    next();
  } catch (error) {
    console.error('❌ خطأ في Order Compatibility Middleware:', error);
    res.status(500).json({
      success: false,
      message: 'خطأ في معالجة بيانات الطلب',
      error: error.message,
      stack: process.env.NODE_ENV === 'development' ? error.stack : undefined
    });
  }
};

module.exports = orderCompatibilityMiddleware;

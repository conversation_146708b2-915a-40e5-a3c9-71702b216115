const express = require('express');
const User = require('../models/User');
const { authenticateToken } = require('../middleware/auth');
const router = express.Router();

// Get all users
router.get('/', authenticateToken, async (req, res) => {
  try {
    const { role, status } = req.query;
    let query = {};

    if (role) {
      query.role = role;
    }

    if (status) {
      query.status = status;
    }

    const users = await User.find(query).select('-password').sort({ createdAt: -1 });

    res.json({
      success: true,
      data: users,
      total: users.length
    });
  } catch (error) {
    console.error('Get users error:', error);
    res.status(500).json({
      success: false,
      message: 'خطأ في جلب المستخدمين'
    });
  }
});

// Get user by ID
router.get('/:id', authenticateToken, async (req, res) => {
  try {
    const user = await User.findById(req.params.id).select('-password');

    if (!user) {
      return res.status(404).json({
        success: false,
        message: 'المستخدم غير موجود'
      });
    }

    res.json({
      success: true,
      data: user
    });
  } catch (error) {
    console.error('Get user error:', error);
    res.status(500).json({
      success: false,
      message: 'خطأ في جلب المستخدم'
    });
  }
});

// Create new user
router.post('/', authenticateToken, async (req, res) => {
  try {
    const { username, name, email, role, password } = req.body;

    if (!username || !name || !email || !role || !password) {
      return res.status(400).json({
        success: false,
        message: 'جميع الحقول مطلوبة'
      });
    }

    // Check if user already exists
    const existingUser = await User.findOne({
      $or: [{ username }, { email }]
    });

    if (existingUser) {
      return res.status(400).json({
        success: false,
        message: 'اسم المستخدم أو البريد الإلكتروني موجود بالفعل'
      });
    }

    const newUser = new User({
      username,
      name,
      email,
      role,
      password,
      status: 'active'
    });

    await newUser.save();

    const { password: _, ...userWithoutPassword } = newUser.toObject();

    // Send Socket notifications for new user
    if (global.socketHandlers) {
      try {
        // Notify managers about new user
        global.socketHandlers.sendRoleNotification('manager', 
          `تم إضافة مستخدم جديد: ${newUser.name}`, {
          type: 'user-created',
          userId: newUser._id,
          userName: newUser.name,
          userRole: newUser.role,
          timestamp: new Date().toISOString()
        });

        console.log(`👤 تم إرسال إشعار إضافة مستخدم جديد: ${newUser.name}`);
      } catch (socketError) {
        console.error('خطأ في إرسال إشعار Socket:', socketError);
      }
    }

    res.status(201).json({
      success: true,
      message: 'تم إنشاء المستخدم بنجاح',
      data: userWithoutPassword
    });
  } catch (error) {
    console.error('Create user error:', error);
    res.status(500).json({
      success: false,
      message: 'خطأ في إنشاء المستخدم'
    });
  }
});

// Update user
router.put('/:id', authenticateToken, async (req, res) => {
  try {
    const { id } = req.params;
    const { username, name, email, role, password, active, status } = req.body;

    const user = await User.findById(id);

    if (!user) {
      return res.status(404).json({
        success: false,
        message: 'المستخدم غير موجود'
      });
    }

    // Check if username or email already exists for another user
    if (username || email) {
      const existingUser = await User.findOne({
        _id: { $ne: id },
        $or: [
          ...(username ? [{ username }] : []),
          ...(email ? [{ email }] : [])
        ]
      });

      if (existingUser) {
        return res.status(400).json({
          success: false,
          message: 'اسم المستخدم أو البريد الإلكتروني موجود بالفعل'
        });
      }
    }

    // Update user fields
    if (username !== undefined) user.username = username;
    if (name !== undefined) user.name = name;
    if (email !== undefined) user.email = email;
    if (role !== undefined) user.role = role;
    if (password !== undefined) user.password = password;
    if (active !== undefined) {
      // Convert boolean active to status string
      user.status = active ? 'active' : 'inactive';
    }
    if (status !== undefined) user.status = status;

    await user.save();

    const { password: _, ...userWithoutPassword } = user.toObject();

    // Send Socket notifications for updated user
    if (global.socketHandlers) {
      try {
        // Notify managers about updated user
        global.socketHandlers.sendRoleNotification('manager', 
          `تم تحديث مستخدم: ${user.name}`, {
          type: 'user-updated',
          userId: user._id,
          userName: user.name,
          userRole: user.role,
          status: user.status,
          timestamp: new Date().toISOString()
        });

        console.log(`🔄 تم إرسال إشعار تحديث مستخدم: ${user.name}`);
      } catch (socketError) {
        console.error('خطأ في إرسال إشعار Socket:', socketError);
      }
    }

    res.json({
      success: true,
      message: 'تم تحديث المستخدم بنجاح',
      data: userWithoutPassword
    });
  } catch (error) {
    console.error('Update user error:', error);
    res.status(500).json({
      success: false,
      message: 'خطأ في تحديث المستخدم'
    });
  }
});

// Delete user
router.delete('/:id', authenticateToken, async (req, res) => {
  try {
    const { id } = req.params;

    const user = await User.findById(id);

    if (!user) {
      return res.status(404).json({
        success: false,
        message: 'المستخدم غير موجود'
      });
    }

    await User.findByIdAndDelete(id);

    // Send Socket notifications for deleted user
    if (global.socketHandlers) {
      try {
        // Notify managers about deleted user
        global.socketHandlers.sendRoleNotification('manager', 
          `تم حذف مستخدم: ${user.name}`, {
          type: 'user-deleted',
          userId: user._id,
          userName: user.name,
          userRole: user.role,
          timestamp: new Date().toISOString()
        });

        console.log(`🗑️ تم إرسال إشعار حذف مستخدم: ${user.name}`);
      } catch (socketError) {
        console.error('خطأ في إرسال إشعار Socket:', socketError);
      }
    }

    res.json({
      success: true,
      message: 'تم حذف المستخدم بنجاح'
    });
  } catch (error) {
    console.error('Delete user error:', error);
    res.status(500).json({
      success: false,
      message: 'خطأ في حذف المستخدم'
    });
  }
});

module.exports = router;

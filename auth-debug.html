<!DOCTYPE html>
<html>
<head>
    <title>تحقق من بيانات المصادقة</title>
    <style>
        body { font-family: Arial, sans-serif; padding: 20px; direction: rtl; }
        .info-box { background: #f0f8ff; border: 1px solid #0066cc; padding: 15px; margin: 10px 0; border-radius: 5px; }
        .error-box { background: #ffe6e6; border: 1px solid #cc0000; padding: 15px; margin: 10px 0; border-radius: 5px; }
        .success-box { background: #e6ffe6; border: 1px solid #00cc00; padding: 15px; margin: 10px 0; border-radius: 5px; }
        button { padding: 10px 20px; margin: 5px; border: none; border-radius: 3px; cursor: pointer; }
        .test-btn { background: #007bff; color: white; }
        .clear-btn { background: #dc3545; color: white; }
    </style>
</head>
<body>
    <h1>🔍 فحص بيانات المصادقة</h1>
    
    <div class="info-box">
        <h3>📋 بيانات LocalStorage الحالية:</h3>
        <div id="localStorage-info"></div>
    </div>
    
    <div class="info-box">
        <h3>👤 معلومات المستخدم المُحلل:</h3>
        <div id="user-info"></div>
    </div>
    
    <div class="info-box">
        <h3>🔑 التوكن:</h3>
        <div id="token-info"></div>
    </div>
    
    <div>
        <button class="test-btn" onclick="testAuthenticatedRequest()">🧪 اختبار طلب مُصادق عليه</button>
        <button class="test-btn" onclick="testDeleteOrder()">🗑️ اختبار حذف طلب</button>
        <button class="clear-btn" onclick="clearAuth()">🗑️ مسح بيانات المصادقة</button>
        <button class="test-btn" onclick="refreshData()">🔄 تحديث البيانات</button>
    </div>
    
    <div class="info-box">
        <h3>📡 نتائج الاختبارات:</h3>
        <div id="test-results"></div>
    </div>

    <script>
        function refreshData() {
            // LocalStorage info
            const localStorageData = {};
            for (let i = 0; i < localStorage.length; i++) {
                const key = localStorage.key(i);
                localStorageData[key] = localStorage.getItem(key);
            }
            
            document.getElementById('localStorage-info').innerHTML = 
                '<pre>' + JSON.stringify(localStorageData, null, 2) + '</pre>';
            
            // User info
            let userInfo = 'لا يوجد مستخدم';
            try {
                const userStr = localStorage.getItem('user');
                if (userStr) {
                    const user = JSON.parse(userStr);
                    userInfo = `
                        <strong>الاسم:</strong> ${user.name || 'غير محدد'}<br>
                        <strong>اسم المستخدم:</strong> ${user.username || 'غير محدد'}<br>
                        <strong>الدور:</strong> ${user.role || 'غير محدد'}<br>
                        <strong>المعرف:</strong> ${user._id || user.id || 'غير محدد'}<br>
                        <strong>الحالة:</strong> ${user.status || 'غير محدد'}
                    `;
                }
            } catch (error) {
                userInfo = `خطأ في تحليل بيانات المستخدم: ${error.message}`;
            }
            document.getElementById('user-info').innerHTML = userInfo;
            
            // Token info
            const token = localStorage.getItem('token') || localStorage.getItem('authToken');
            document.getElementById('token-info').innerHTML = token ? 
                `موجود (الطول: ${token.length})` : 'غير موجود';
        }
        
        async function testAuthenticatedRequest() {
            const resultsDiv = document.getElementById('test-results');
            resultsDiv.innerHTML += '<div class="info-box">🔄 جاري اختبار طلب GET مُصادق عليه...</div>';
            
            try {
                const token = localStorage.getItem('token') || localStorage.getItem('authToken');
                const response = await fetch('http://localhost:5000/api/v1/orders', {
                    headers: {
                        'Authorization': `Bearer ${token}`,
                        'Content-Type': 'application/json'
                    }
                });
                
                const result = await response.json();
                
                if (response.ok) {
                    resultsDiv.innerHTML += '<div class="success-box">✅ طلب GET نجح: ' + JSON.stringify(result).substring(0, 200) + '...</div>';
                } else {
                    resultsDiv.innerHTML += '<div class="error-box">❌ طلب GET فشل: ' + JSON.stringify(result) + '</div>';
                }
            } catch (error) {
                resultsDiv.innerHTML += '<div class="error-box">❌ خطأ في طلب GET: ' + error.message + '</div>';
            }
        }
        
        async function testDeleteOrder() {
            const resultsDiv = document.getElementById('test-results');
            const orderId = prompt('أدخل معرف الطلب للحذف:');
            
            if (!orderId) {
                resultsDiv.innerHTML += '<div class="error-box">❌ لم يتم إدخال معرف الطلب</div>';
                return;
            }
            
            resultsDiv.innerHTML += '<div class="info-box">🔄 جاري اختبار حذف الطلب...</div>';
            
            try {
                const token = localStorage.getItem('token') || localStorage.getItem('authToken');
                const response = await fetch(`http://localhost:5000/api/v1/orders/${orderId}`, {
                    method: 'DELETE',
                    headers: {
                        'Authorization': `Bearer ${token}`,
                        'Content-Type': 'application/json'
                    }
                });
                
                const result = await response.json();
                
                if (response.ok) {
                    resultsDiv.innerHTML += '<div class="success-box">✅ حذف الطلب نجح: ' + JSON.stringify(result) + '</div>';
                } else {
                    resultsDiv.innerHTML += '<div class="error-box">❌ حذف الطلب فشل (' + response.status + '): ' + JSON.stringify(result) + '</div>';
                }
            } catch (error) {
                resultsDiv.innerHTML += '<div class="error-box">❌ خطأ في حذف الطلب: ' + error.message + '</div>';
            }
        }
        
        function clearAuth() {
            localStorage.removeItem('token');
            localStorage.removeItem('authToken');
            localStorage.removeItem('user');
            localStorage.removeItem('username');
            localStorage.removeItem('userRole');
            refreshData();
            document.getElementById('test-results').innerHTML += '<div class="success-box">✅ تم مسح بيانات المصادقة</div>';
        }
        
        // Initial load
        refreshData();
    </script>
</body>
</html>

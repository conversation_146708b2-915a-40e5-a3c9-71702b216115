const mongoose = require('mongoose');
const bcrypt = require('bcryptjs');

// نماذج قاعدة البيانات
const User = require('./backend/models/User');
const Product = require('./backend/models/Product');
const Category = require('./backend/models/Category');
const Table = require('./backend/models/Table');

/**
 * سكريپت تهيئة قاعدة البيانات المحلية
 * يقوم بإنشاء البيانات الأساسية للتطوير المحلي
 */
async function initLocalDatabase() {
  try {
    console.log('🔗 الاتصال بقاعدة البيانات السحابية...');
    
    // الاتصال بقاعدة البيانات السحابية (MongoDB Atlas)
    await mongoose.connect('mongodb+srv://besomustafa:<EMAIL>/deshacoffee?retryWrites=true&w=majority&appName=MyCoffeChop', {
      useNewUrlParser: true,
      useUnifiedTopology: true,
    });
    
    console.log('✅ تم الاتصال بقاعدة البيانات السحابية (MongoDB Atlas)');
    
    // مسح البيانات السابقة (اختياري)
    console.log('🧹 مسح البيانات السابقة...');
    await User.deleteMany({});
    await Product.deleteMany({});
    await Category.deleteMany({});
    await Table.deleteMany({});
    
    console.log('✅ تم مسح البيانات السابقة');
    
    // إنشاء المدير الافتراضي
    console.log('👤 إنشاء المدير الافتراضي...');
    const hashedPassword = await bcrypt.hash('admin123', 10);
    
    const adminUser = new User({
      username: 'admin',
      name: 'المدير العام',
      email: 'admin@localhost',
      password: hashedPassword,
      role: 'admin',
      isActive: true,
      permissions: [
        'view_dashboard',
        'manage_users',
        'manage_products',
        'manage_orders',
        'view_reports',
        'manage_settings'
      ]
    });
    
    await adminUser.save();
    console.log('✅ تم إنشاء المدير الافتراضي (admin / admin123)');
    
    // إنشاء نُدل للتجربة
    console.log('👥 إنشاء نُدل للتجربة...');
    const waiters = [
      {
        username: 'waiter1',
        name: 'أحمد محمد',
        email: 'ahmed@localhost',
        password: await bcrypt.hash('123456', 10),
        role: 'waiter',
        isActive: true,
        permissions: ['view_dashboard', 'manage_orders', 'view_tables']
      },
      {
        username: 'waiter2',
        name: 'فاطمة علي',
        email: 'fatma@localhost',
        password: await bcrypt.hash('123456', 10),
        role: 'waiter',
        isActive: true,
        permissions: ['view_dashboard', 'manage_orders', 'view_tables']
      },
      {
        username: 'waiter3',
        name: 'محمد حسن',
        email: 'mohamed@localhost',
        password: await bcrypt.hash('123456', 10),
        role: 'waiter',
        isActive: true,
        permissions: ['view_dashboard', 'manage_orders', 'view_tables']
      }
    ];
    
    await User.insertMany(waiters);
    console.log('✅ تم إنشاء 3 نُدل للتجربة');
    
    // إنشاء طاهي للتجربة
    console.log('👨‍🍳 إنشاء طاهي للتجربة...');
    const chef = new User({
      username: 'chef1',
      name: 'الشيف كريم',
      email: 'chef@localhost',
      password: await bcrypt.hash('123456', 10),
      role: 'chef',
      isActive: true,
      permissions: ['view_dashboard', 'manage_orders', 'view_kitchen']
    });
    
    await chef.save();
    console.log('✅ تم إنشاء طاهي للتجربة');
    
    // إنشاء فئات المنتجات
    console.log('📂 إنشاء فئات المنتجات...');
    const categories = [
      {
        name: 'القهوة الساخنة',
        description: 'جميع أنواع القهوة الساخنة',
        color: '#8B4513',
        icon: 'fa-coffee',
        isActive: true
      },
      {
        name: 'القهوة الباردة',
        description: 'المشروبات الباردة والآيس كريم',
        color: '#4A90E2',
        icon: 'fa-glass',
        isActive: true
      },
      {
        name: 'الشاي والأعشاب',
        description: 'الشاي وجميع أنواع الأعشاب',
        color: '#228B22',
        icon: 'fa-leaf',
        isActive: true
      },
      {
        name: 'العصائر الطبيعية',
        description: 'العصائر الطازجة والطبيعية',
        color: '#FF6347',
        icon: 'fa-apple',
        isActive: true
      },
      {
        name: 'الحلويات',
        description: 'الكيك والحلويات المختلفة',
        color: '#DDA0DD',
        icon: 'fa-birthday-cake',
        isActive: true
      },
      {
        name: 'الساندويتشات',
        description: 'الساندويتشات والوجبات الخفيفة',
        color: '#D2691E',
        icon: 'fa-hamburger',
        isActive: true
      }
    ];
    
    const savedCategories = await Category.insertMany(categories);
    console.log('✅ تم إنشاء 6 فئات للمنتجات');
    
    // إنشاء منتجات العينة
    console.log('☕ إنشاء منتجات العينة...');
    const products = [
      // القهوة الساخنة
      {
        name: 'أمريكانو',
        description: 'قهوة أمريكانو كلاسيكية',
        price: 25,
        category: savedCategories[0]._id,
        available: true,
        preparationTime: 5
      },
      {
        name: 'كابتشينو',
        description: 'كابتشينو بالحليب المبخر',
        price: 30,
        category: savedCategories[0]._id,
        available: true,
        preparationTime: 7
      },
      {
        name: 'لاتيه',
        description: 'لاتيه كريمي مع فن الحليب',
        price: 35,
        category: savedCategories[0]._id,
        available: true,
        preparationTime: 8
      },
      {
        name: 'إسبريسو',
        description: 'إسبريسو قوي وغني',
        price: 20,
        category: savedCategories[0]._id,
        available: true,
        preparationTime: 3
      },
      
      // القهوة الباردة
      {
        name: 'آيس لاتيه',
        description: 'لاتيه بارد منعش',
        price: 40,
        category: savedCategories[1]._id,
        available: true,
        preparationTime: 6
      },
      {
        name: 'فرابتشينو',
        description: 'مشروب القهوة المثلج',
        price: 45,
        category: savedCategories[1]._id,
        available: true,
        preparationTime: 8
      },
      
      // الشاي والأعشاب
      {
        name: 'شاي أحمر',
        description: 'شاي أحمر تقليدي',
        price: 15,
        category: savedCategories[2]._id,
        available: true,
        preparationTime: 5
      },
      {
        name: 'شاي أخضر',
        description: 'شاي أخضر صحي',
        price: 18,
        category: savedCategories[2]._id,
        available: true,
        preparationTime: 5
      },
      {
        name: 'نعناع',
        description: 'شاي النعناع المنعش',
        price: 12,
        category: savedCategories[2]._id,
        available: true,
        preparationTime: 4
      },
      
      // العصائر
      {
        name: 'عصير برتقال طازج',
        description: 'عصير برتقال طبيعي 100%',
        price: 20,
        category: savedCategories[3]._id,
        available: true,
        preparationTime: 3
      },
      {
        name: 'عصير مانجو',
        description: 'عصير مانجو طازج',
        price: 25,
        category: savedCategories[3]._id,
        available: true,
        preparationTime: 4
      },
      
      // الحلويات
      {
        name: 'تشيز كيك',
        description: 'تشيز كيك بالفراولة',
        price: 35,
        category: savedCategories[4]._id,
        available: true,
        preparationTime: 2
      },
      {
        name: 'براونيز',
        description: 'براونيز بالشوكولاتة',
        price: 28,
        category: savedCategories[4]._id,
        available: true,
        preparationTime: 2
      },
      
      // الساندويتشات
      {
        name: 'كلوب ساندويتش',
        description: 'كلوب ساندويتش بالدجاج',
        price: 45,
        category: savedCategories[5]._id,
        available: true,
        preparationTime: 10
      },
      {
        name: 'ساندويتش جبنة مشوية',
        description: 'ساندويتش جبنة مشوية كلاسيكي',
        price: 25,
        category: savedCategories[5]._id,
        available: true,
        preparationTime: 8
      }
    ];
    
    await Product.insertMany(products);
    console.log('✅ تم إنشاء 15 منتج للعينة');
    
    // إنشاء طاولات العينة
    console.log('🪑 إنشاء طاولات العينة...');
    const tables = [];
    for (let i = 1; i <= 20; i++) {
      tables.push({
        tableNumber: i.toString(),
        capacity: Math.floor(Math.random() * 6) + 2, // من 2 إلى 8 أشخاص
        status: 'available',
        location: i <= 10 ? 'داخلي' : 'خارجي',
        isActive: true
      });
    }
    
    await Table.insertMany(tables);
    console.log('✅ تم إنشاء 20 طاولة للعينة');
    
    console.log('\n' + '='.repeat(60));
    console.log('🎉 تمت تهيئة قاعدة البيانات المحلية بنجاح!');
    console.log('='.repeat(60));
    
    console.log('\n👤 بيانات تسجيل الدخول:');
    console.log('📋 المدير:');
    console.log('   المستخدم: admin');
    console.log('   كلمة المرور: admin123');
    console.log('   الدور: مدير');
    
    console.log('\n👥 النُدل:');
    console.log('   waiter1 / 123456 (أحمد محمد)');
    console.log('   waiter2 / 123456 (فاطمة علي)');
    console.log('   waiter3 / 123456 (محمد حسن)');
    
    console.log('\n👨‍🍳 الطاهي:');
    console.log('   chef1 / 123456 (الشيف كريم)');
    
    console.log('\n📊 البيانات المُنشأة:');
    console.log(`   ${categories.length} فئات منتجات`);
    console.log(`   ${products.length} منتج`);
    console.log(`   ${tables.length} طاولة`);
    console.log(`   ${waiters.length + 2} مستخدم`);
    
    console.log('\n🚀 يمكنك الآن تشغيل النظام باستخدام:');
    console.log('   npm run dev:all:local');
    
  } catch (error) {
    console.error('❌ خطأ في تهيئة قاعدة البيانات:', error);
  } finally {
    await mongoose.disconnect();
    console.log('\n🔌 تم قطع الاتصال بقاعدة البيانات');
  }
}

// تشغيل السكريپت
initLocalDatabase();

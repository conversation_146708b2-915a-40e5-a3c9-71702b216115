const mongoose = require('mongoose');
const Product = require('./backend/models/Product');
require('dotenv').config();

// إعداد الاتصال بقاعدة البيانات
const connectDB = async () => {
  try {
    // استخدام إعدادات قاعدة البيانات من ملف .env
    const mongoURI = process.env.MONGODB_URI || 'mongodb://localhost:27017/coffee-shop';
    
    console.log('🔗 محاولة الاتصال بقاعدة البيانات:', mongoURI.replace(/\/\/.*@/, '//***:***@'));
    
    await mongoose.connect(mongoURI, {
      serverSelectionTimeoutMS: 30000, // 30 seconds
      socketTimeoutMS: 45000, // 45 seconds
      bufferMaxEntries: 0,
      maxPoolSize: 10,
      minPoolSize: 1
    });
    
    console.log('✅ تم الاتصال بقاعدة البيانات بنجاح');
  } catch (error) {
    console.error('❌ خطأ في الاتصال بقاعدة البيانات:', error.message);
    process.exit(1);
  }
};

// دالة لعرض جميع المنتجات مع المخزون الحالي
const showAllProducts = async () => {
  try {
    console.log('🔍 البحث عن المنتجات في قاعدة البيانات...');
    const products = await Product.find({}, 'name stock price available').sort({ name: 1 }).maxTimeMS(30000);
    
    console.log('\n📦 قائمة المنتجات والمخزون الحالي:');
    console.log('=' .repeat(80));
    
    products.forEach((product, index) => {
      const stockQuantity = typeof product.stock === 'object' && product.stock.quantity !== undefined 
        ? product.stock.quantity 
        : (typeof product.stock === 'number' ? product.stock : 0);
      
      const stockUnit = typeof product.stock === 'object' && product.stock.unit 
        ? product.stock.unit 
        : 'قطعة';
      
      const availability = product.available ? '✅ متوفر' : '❌ غير متوفر';
      
      console.log(`${index + 1}. ${product.name}`);
      console.log(`   ID: ${product._id}`);
      console.log(`   المخزون: ${stockQuantity} ${stockUnit}`);
      console.log(`   السعر: ${product.price} جنيه`);
      console.log(`   الحالة: ${availability}`);
      console.log('-'.repeat(50));
    });
    
    return products;
  } catch (error) {
    console.error('❌ خطأ في عرض المنتجات:', error.message);
    return [];
  }
};

// دالة لتحديث مخزون منتج معين
const updateProductStock = async (productId, newQuantity, unit = 'قطعة') => {
  try {
    console.log(`\n🔄 تحديث مخزون المنتج ${productId} إلى ${newQuantity} ${unit}...`);
    
    const product = await Product.findById(productId);
    
    if (!product) {
      console.error('❌ المنتج غير موجود');
      return false;
    }
    
    console.log(`📝 المنتج الحالي: ${product.name}`);
    
    const oldStock = typeof product.stock === 'object' && product.stock.quantity !== undefined 
      ? product.stock.quantity 
      : (typeof product.stock === 'number' ? product.stock : 0);
    
    console.log(`📊 المخزون السابق: ${oldStock}`);
    
    // تحديث المخزون
    product.stock = {
      quantity: parseInt(newQuantity),
      unit: unit,
      lowStockAlert: product.stock?.lowStockAlert || 5
    };
    
    await product.save();
    
    console.log(`✅ تم تحديث المخزون بنجاح إلى: ${newQuantity} ${unit}`);
    return true;
    
  } catch (error) {
    console.error('❌ خطأ في تحديث المخزون:', error.message);
    return false;
  }
};

// دالة لتحديث مخزون عدة منتجات
const updateMultipleProducts = async (updates) => {
  console.log(`\n🔄 تحديث مخزون ${updates.length} منتج...`);
  
  let successCount = 0;
  let failCount = 0;
  
  for (const update of updates) {
    const success = await updateProductStock(update.id, update.quantity, update.unit || 'قطعة');
    if (success) {
      successCount++;
    } else {
      failCount++;
    }
  }
  
  console.log(`\n📊 نتائج التحديث:`);
  console.log(`✅ نجح: ${successCount} منتج`);
  console.log(`❌ فشل: ${failCount} منتج`);
};

// دالة لإعادة تعيين جميع المخزون إلى قيمة افتراضية
const resetAllStock = async (defaultQuantity = 100) => {
  try {
    console.log(`\n🔄 إعادة تعيين جميع المخزون إلى ${defaultQuantity}...`);
    
    const result = await Product.updateMany(
      {},
      {
        $set: {
          'stock.quantity': defaultQuantity,
          'stock.unit': 'قطعة',
          'stock.lowStockAlert': 5
        }
      }
    );
    
    console.log(`✅ تم تحديث ${result.modifiedCount} منتج بنجاح`);
    return true;
    
  } catch (error) {
    console.error('❌ خطأ في إعادة تعيين المخزون:', error.message);
    return false;
  }
};

// الدالة الرئيسية
const main = async () => {
  await connectDB();
  
  console.log('\n🎯 أداة تحديث المخزون المباشر');
  console.log('=' .repeat(50));
  
  // عرض جميع المنتجات أولاً
  const products = await showAllProducts();
  
  if (products.length === 0) {
    console.log('❌ لا توجد منتجات في قاعدة البيانات');
    process.exit(1);
  }
  
  // أمثلة على التحديث
  console.log('\n💡 أمثلة على كيفية استخدام الأداة:');
  console.log('1. لتحديث منتج واحد:');
  console.log('   await updateProductStock("PRODUCT_ID", 50, "قطعة");');
  console.log('\n2. لتحديث عدة منتجات:');
  console.log('   await updateMultipleProducts([');
  console.log('     { id: "PRODUCT_ID_1", quantity: 30, unit: "قطعة" },');
  console.log('     { id: "PRODUCT_ID_2", quantity: 25, unit: "كوب" }');
  console.log('   ]);');
  console.log('\n3. لإعادة تعيين جميع المخزون:');
  console.log('   await resetAllStock(100);');
  
  // مثال عملي - تحديث أول منتج
  if (products.length > 0) {
    console.log('\n🔧 مثال عملي: تحديث مخزون أول منتج إلى 75 قطعة...');
    await updateProductStock(products[0]._id, 75, 'قطعة');
    
    // عرض النتيجة
    console.log('\n📊 حالة المنتج بعد التحديث:');
    const updatedProduct = await Product.findById(products[0]._id);
    console.log(`المنتج: ${updatedProduct.name}`);
    console.log(`المخزون الجديد: ${updatedProduct.stock.quantity} ${updatedProduct.stock.unit}`);
  }
  
  console.log('\n✅ تم الانتهاء من العملية');
  process.exit(0);
};

// تشغيل الأداة
if (require.main === module) {
  main().catch(error => {
    console.error('❌ خطأ في تشغيل الأداة:', error);
    process.exit(1);
  });
}

// تصدير الدوال للاستخدام الخارجي
module.exports = {
  connectDB,
  showAllProducts,
  updateProductStock,
  updateMultipleProducts,
  resetAllStock
};
// سكريبت لحساب إجمالي المبيعات وإحصائيات النُدل
const fs = require('fs');
const path = require('path');

// استيراد ملفات النظام الموجودة
const backendPath = path.join(__dirname, 'backend');
process.chdir(backendPath);

// استيراد التكوين والنماذج
const config = require('./config/environment');
const mongoose = require('mongoose');

// نماذج البيانات
const orderSchema = new mongoose.Schema({
  total: Number,
  discount: { type: Number, default: 0 },
  status: String,
  assignedWaiter: { type: mongoose.Schema.Types.ObjectId, ref: 'User' },
  createdAt: { type: Date, default: Date.now },
  items: [{ 
    name: String, 
    price: Number, 
    quantity: Number 
  }],
  tableNumber: Number
});

const userSchema = new mongoose.Schema({
  username: String,
  role: String,
  email: String
});

const Order = mongoose.model('Order', orderSchema);
const User = mongoose.model('User', userSchema);

async function connectToDatabase() {
  try {
    await mongoose.connect(config.database.mongoUri, {
      maxPoolSize: config.database.maxPoolSize,
      minPoolSize: config.database.minPoolSize,
      maxIdleTimeMS: config.database.maxIdleTimeMS
    });
    console.log('✅ تم الاتصال بقاعدة البيانات بنجاح');
    return true;
  } catch (error) {
    console.error('❌ خطأ في الاتصال بقاعدة البيانات:', error.message);
    return false;
  }
}

async function getAllOrders() {
  try {
    const orders = await Order.find({}).populate('assignedWaiter', 'username email role');
    return orders;
  } catch (error) {
    console.error('خطأ في جلب الطلبات:', error.message);
    return [];
  }
}

async function getAllUsers() {
  try {
    const users = await User.find({ role: 'waiter' });
    return users;
  } catch (error) {
    console.error('خطأ في جلب المستخدمين:', error.message);
    return [];
  }
}

function calculateSalesStats(orders) {
  let totalSalesBeforeDiscount = 0;
  let totalDiscounts = 0;
  let totalNetSales = 0;
  let completedOrders = 0;
  
  const waiterStats = {};
  const statusCounts = {};
  
  orders.forEach(order => {
    const orderTotal = order.total || 0;
    const orderDiscount = order.discount || 0;
    const orderNet = orderTotal - orderDiscount;
    const status = order.status || 'غير محدد';
    
    // إحصائيات الحالة
    statusCounts[status] = (statusCounts[status] || 0) + 1;
    
    // حساب المبيعات للطلبات المكتملة فقط
    if (status === 'completed') {
      completedOrders++;
      totalSalesBeforeDiscount += orderTotal;
      totalDiscounts += orderDiscount;
      totalNetSales += orderNet;
    }
    
    // إحصائيات النُدل (جميع الطلبات)
    const waiterName = order.assignedWaiter?.username || 'غير محدد';
    
    if (!waiterStats[waiterName]) {
      waiterStats[waiterName] = {
        totalOrders: 0,
        completedOrders: 0,
        totalSales: 0,
        totalDiscounts: 0,
        netSales: 0,
        averageOrderValue: 0
      };
    }
    
    waiterStats[waiterName].totalOrders++;
    
    if (status === 'completed') {
      waiterStats[waiterName].completedOrders++;
      waiterStats[waiterName].totalSales += orderTotal;
      waiterStats[waiterName].totalDiscounts += orderDiscount;
      waiterStats[waiterName].netSales += orderNet;
    }
  });
  
  // حساب متوسط قيمة الطلب لكل نادل
  Object.keys(waiterStats).forEach(waiterName => {
    const stats = waiterStats[waiterName];
    if (stats.completedOrders > 0) {
      stats.averageOrderValue = stats.netSales / stats.completedOrders;
    }
  });
  
  return {
    totalOrders: orders.length,
    completedOrders,
    totalSalesBeforeDiscount,
    totalDiscounts,
    totalNetSales,
    waiterStats,
    statusCounts
  };
}

function displayResults(stats) {
  console.log('\n' + '='.repeat(80));
  console.log('🏪 تقرير شامل للمبيعات ونظام إدارة المقهى');
  console.log('='.repeat(80));
  
  console.log('\n📊 إحصائيات عامة:');
  console.log(`📦 إجمالي عدد الطلبات: ${stats.totalOrders}`);
  console.log(`✅ الطلبات المكتملة: ${stats.completedOrders}`);
  console.log(`⏳ الطلبات غير المكتملة: ${stats.totalOrders - stats.completedOrders}`);
  
  if (stats.completedOrders > 0) {
    console.log('\n💰 إجمالي المبيعات (الطلبات المكتملة فقط):');
    console.log(`💵 إجمالي المبيعات قبل الخصم: ${stats.totalSalesBeforeDiscount.toFixed(2)} ريال`);
    console.log(`🎯 إجمالي الخصومات: ${stats.totalDiscounts.toFixed(2)} ريال`);
    console.log(`✨ صافي المبيعات بعد الخصم: ${stats.totalNetSales.toFixed(2)} ريال`);
    
    if (stats.totalSalesBeforeDiscount > 0) {
      console.log(`📈 نسبة الخصومات: ${((stats.totalDiscounts / stats.totalSalesBeforeDiscount) * 100).toFixed(1)}%`);
    }
    console.log(`📊 متوسط قيمة الطلب: ${(stats.totalNetSales / stats.completedOrders).toFixed(2)} ريال`);
  }
  
  console.log('\n📋 توزيع الطلبات حسب الحالة:');
  Object.entries(stats.statusCounts).forEach(([status, count]) => {
    const percentage = ((count / stats.totalOrders) * 100).toFixed(1);
    console.log(`   ${status}: ${count} طلب (${percentage}%)`);
  });
  
  console.log('\n👥 إحصائيات النُدل:');
  console.log('='.repeat(80));
  
  // ترتيب النُدل حسب صافي المبيعات
  const sortedWaiters = Object.entries(stats.waiterStats)
    .filter(([_, stats]) => stats.completedOrders > 0)
    .sort((a, b) => b[1].netSales - a[1].netSales);
  
  if (sortedWaiters.length === 0) {
    console.log('⚠️  لا توجد بيانات مبيعات للنُدل (لا توجد طلبات مكتملة)');
  } else {
    sortedWaiters.forEach(([waiterName, waiterStats], index) => {
      console.log(`\n🏆 ${index + 1}. النادل: ${waiterName}`);
      console.log(`   📦 إجمالي الطلبات: ${waiterStats.totalOrders}`);
      console.log(`   ✅ الطلبات المكتملة: ${waiterStats.completedOrders}`);
      console.log(`   💰 إجمالي المبيعات: ${waiterStats.totalSales.toFixed(2)} ريال`);
      console.log(`   🎯 إجمالي الخصومات: ${waiterStats.totalDiscounts.toFixed(2)} ريال`);
      console.log(`   ✨ صافي المبيعات: ${waiterStats.netSales.toFixed(2)} ريال`);
      console.log(`   📈 متوسط قيمة الطلب: ${waiterStats.averageOrderValue.toFixed(2)} ريال`);
      
      if (stats.totalNetSales > 0) {
        console.log(`   📊 نسبة من إجمالي المبيعات: ${((waiterStats.netSales / stats.totalNetSales) * 100).toFixed(1)}%`);
      }
      console.log('   ' + '-'.repeat(50));
    });
  }
  
  // إحصائيات النُدل الذين لديهم طلبات غير مكتملة فقط
  const waitersWithIncompleteOnly = Object.entries(stats.waiterStats)
    .filter(([_, stats]) => stats.totalOrders > 0 && stats.completedOrders === 0);
  
  if (waitersWithIncompleteOnly.length > 0) {
    console.log('\n⏳ نُدل بطلبات غير مكتملة فقط:');
    waitersWithIncompleteOnly.forEach(([waiterName, waiterStats]) => {
      console.log(`   ${waiterName}: ${waiterStats.totalOrders} طلب غير مكتمل`);
    });
  }
}

async function main() {
  console.log('🚀 بدء تشغيل نظام حساب المبيعات...');
  
  const connected = await connectToDatabase();
  if (!connected) {
    console.log('❌ فشل الاتصال بقاعدة البيانات');
    process.exit(1);
  }
  
  console.log('📥 جاري جلب البيانات من قاعدة البيانات...');
  
  const orders = await getAllOrders();
  const users = await getAllUsers();
  
  console.log(`📊 تم جلب ${orders.length} طلب و ${users.length} نادل`);
  
  if (orders.length === 0) {
    console.log('⚠️  لا توجد طلبات في قاعدة البيانات');
    
    // إنشاء بيانات تجريبية
    console.log('\n🔧 إنشاء بيانات تجريبية للاختبار...');
    await createDemoData();
    
    const newOrders = await getAllOrders();
    const stats = calculateSalesStats(newOrders);
    displayResults(stats);
  } else {
    const stats = calculateSalesStats(orders);
    displayResults(stats);
  }
  
  await mongoose.disconnect();
  console.log('\n✅ تم إنهاء الاتصال بقاعدة البيانات');
}

async function createDemoData() {
  try {
    // إنشاء نُدل تجريبيين
    const waiters = [
      { username: 'أحمد محمد', role: 'waiter', email: '<EMAIL>' },
      { username: 'فاطمة علي', role: 'waiter', email: '<EMAIL>' },
      { username: 'محمد سعد', role: 'waiter', email: '<EMAIL>' }
    ];
    
    const createdWaiters = [];
    for (const waiter of waiters) {
      const existingWaiter = await User.findOne({ username: waiter.username });
      if (!existingWaiter) {
        const newWaiter = await User.create(waiter);
        createdWaiters.push(newWaiter);
      } else {
        createdWaiters.push(existingWaiter);
      }
    }
    
    // إنشاء طلبات تجريبية
    const demoOrders = [
      // طلبات أحمد محمد
      { total: 150, discount: 10, status: 'completed', assignedWaiter: createdWaiters[0]._id, tableNumber: 1 },
      { total: 200, discount: 0, status: 'completed', assignedWaiter: createdWaiters[0]._id, tableNumber: 2 },
      { total: 175, discount: 15, status: 'completed', assignedWaiter: createdWaiters[0]._id, tableNumber: 3 },
      { total: 120, discount: 5, status: 'completed', assignedWaiter: createdWaiters[0]._id, tableNumber: 4 },
      { total: 95, discount: 0, status: 'pending', assignedWaiter: createdWaiters[0]._id, tableNumber: 5 },
      
      // طلبات فاطمة علي
      { total: 250, discount: 25, status: 'completed', assignedWaiter: createdWaiters[1]._id, tableNumber: 6 },
      { total: 180, discount: 0, status: 'completed', assignedWaiter: createdWaiters[1]._id, tableNumber: 7 },
      { total: 220, discount: 30, status: 'completed', assignedWaiter: createdWaiters[1]._id, tableNumber: 8 },
      { total: 160, discount: 10, status: 'completed', assignedWaiter: createdWaiters[1]._id, tableNumber: 9 },
      { total: 140, discount: 0, status: 'preparing', assignedWaiter: createdWaiters[1]._id, tableNumber: 10 },
      
      // طلبات محمد سعد
      { total: 300, discount: 50, status: 'completed', assignedWaiter: createdWaiters[2]._id, tableNumber: 11 },
      { total: 275, discount: 25, status: 'completed', assignedWaiter: createdWaiters[2]._id, tableNumber: 12 },
      { total: 190, discount: 0, status: 'completed', assignedWaiter: createdWaiters[2]._id, tableNumber: 13 },
      { total: 210, discount: 15, status: 'completed', assignedWaiter: createdWaiters[2]._id, tableNumber: 14 },
      { total: 240, discount: 20, status: 'completed', assignedWaiter: createdWaiters[2]._id, tableNumber: 15 },
      { total: 165, discount: 5, status: 'completed', assignedWaiter: createdWaiters[2]._id, tableNumber: 16 },
      { total: 185, discount: 0, status: 'cancelled', assignedWaiter: createdWaiters[2]._id, tableNumber: 17 },
      
      // طلبات بدون نادل محدد
      { total: 100, discount: 0, status: 'completed', assignedWaiter: null, tableNumber: 18 },
      { total: 85, discount: 5, status: 'completed', assignedWaiter: null, tableNumber: 19 },
      { total: 110, discount: 0, status: 'pending', assignedWaiter: null, tableNumber: 20 }
    ];
    
    await Order.insertMany(demoOrders);
    console.log(`✅ تم إنشاء ${demoOrders.length} طلب تجريبي`);
    
  } catch (error) {
    console.error('خطأ في إنشاء البيانات التجريبية:', error.message);
  }
}

// تشغيل البرنامج
main().catch(error => {
  console.error('خطأ في تشغيل البرنامج:', error.message);
  process.exit(1);
});

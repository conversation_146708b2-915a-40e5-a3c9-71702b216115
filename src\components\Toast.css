.toast-container {
  position: fixed;
  top: 20px;
  left: 20px;
  z-index: 10000;
  display: flex;
  flex-direction: column;
  gap: var(--spacing-sm);
  max-width: 400px;
  width: 100%;
}

.toast {
  display: flex;
  align-items: flex-start;
  gap: var(--spacing-sm);
  padding: var(--spacing-md);
  border-radius: var(--border-radius-md);
  box-shadow: var(--shadow-lg);
  transform: translateX(-100%);
  opacity: 0;
  transition: all 0.3s ease;
  border-right: 4px solid;
  background: var(--surface);
}

.toast-visible {
  transform: translateX(0);
  opacity: 1;
}

.toast-icon {
  font-size: var(--font-size-lg);
  margin-top: 2px;
}

.toast-content {
  flex: 1;
}

.toast-content p {
  margin: 0;
  color: var(--text-primary);
  font-size: var(--font-size-md);
  line-height: 1.4;
}

.toast-close {
  background: transparent;
  border: none;
  color: var(--text-secondary);
  cursor: pointer;
  font-size: var(--font-size-sm);
  padding: var(--spacing-xs);
  border-radius: 50%;
  width: 24px;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s;
}

.toast-close:hover {
  background-color: rgba(0, 0, 0, 0.1);
  color: var(--text-primary);
}

/* أنواع التوست */
.toast-success {
  border-right-color: var(--success);
}

.toast-success .toast-icon {
  color: var(--success);
}

.toast-error {
  border-right-color: var(--error);
}

.toast-error .toast-icon {
  color: var(--error);
}

.toast-warning {
  border-right-color: var(--warning);
}

.toast-warning .toast-icon {
  color: var(--warning);
}

.toast-info {
  border-right-color: var(--info);
}

.toast-info .toast-icon {
  color: var(--info);
}

/* تنسيق خاص للوضع المظلم */
[data-theme="dark"] .toast {
  background: var(--surface);
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.4);
}

[data-theme="dark"] .toast-close:hover {
  background-color: rgba(255, 255, 255, 0.1);
}

/* تصميم متجاوب */
@media (max-width: 480px) {
  .toast-container {
    left: 10px;
    right: 10px;
    max-width: none;
  }
  
  .toast {
    padding: var(--spacing-sm) var(--spacing-md);
  }
  
  .toast-content p {
    font-size: var(--font-size-sm);
  }
}

.toast-copy-btn {
  background: transparent;
  border: none;
  color: var(--primary);
  cursor: pointer;
  font-size: 0.95em;
  margin-top: 4px;
  display: flex;
  align-items: center;
  gap: 4px;
  transition: color 0.2s;
}

.toast-copy-btn:hover {
  color: var(--primary-dark);
}

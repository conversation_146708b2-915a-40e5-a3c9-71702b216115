/* ====================================
   STOCK CONTROLS FIX - أزرار التحكم في المخزون
   إصلاح شامل لأزرار التحكم في المخزون
   ==================================== */

/* Container for stock controls */
.stock-controls {
  grid-row: 3 !important;
  margin-top: auto !important;
  padding: 0.8rem !important;
  background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%) !important;
  border-radius: 16px !important;
  border: 1px solid rgba(226, 232, 240, 0.8) !important;
}

.stock-controls-label {
  display: flex !important;
  align-items: center !important;
  gap: 0.5rem !important;
  margin-bottom: 1rem !important;
  font-weight: 600 !important;
  color: #475569 !important;
  font-size: 0.95rem !important;
}

.stock-controls-label i {
  color: #3b82f6 !important;
  font-size: 1rem !important;
}

/* Stock buttons grid */
.stock-buttons {
  display: grid !important;
  grid-template-columns: repeat(4, 1fr) !important;
  gap: 0.6rem !important;
  width: 100% !important;
}

/* Enhanced stock button styling */
.stock-btn {
  padding: 0.8rem 0.5rem !important;
  border: none !important;
  border-radius: 12px !important;
  font-size: 0.85rem !important;
  font-weight: 600 !important;
  cursor: pointer !important;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1) !important;
  display: flex !important;
  flex-direction: column !important;
  align-items: center !important;
  justify-content: center !important;
  gap: 0.3rem !important;
  min-height: 50px !important;
  max-height: 55px !important;
  position: relative !important;
  overflow: hidden !important;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1) !important;
}

.stock-btn i {
  font-size: 1rem !important;
  margin-bottom: 0.2rem !important;
}

.stock-btn span {
  font-size: 0.8rem !important;
  font-weight: 700 !important;
}

/* Shimmer effect */
.stock-btn::before {
  content: '' !important;
  position: absolute !important;
  top: 0 !important;
  left: -100% !important;
  width: 100% !important;
  height: 100% !important;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.4), transparent) !important;
  transition: left 0.5s !important;
}

.stock-btn:hover::before {
  left: 100% !important;
}

/* Decrease button (-1) */
.stock-btn.decrease {
  background: linear-gradient(135deg, #ef4444, #dc2626) !important;
  color: white !important;
  box-shadow: 0 4px 15px rgba(239, 68, 68, 0.3) !important;
}

.stock-btn.decrease:hover {
  background: linear-gradient(135deg, #dc2626, #b91c1c) !important;
  transform: translateY(-2px) !important;
  box-shadow: 0 8px 25px rgba(239, 68, 68, 0.4) !important;
}

.stock-btn.decrease:active {
  transform: translateY(0) scale(0.95) !important;
}

/* Decrease five button (-5) */
.stock-btn.decrease-five {
  background: linear-gradient(135deg, #f59e0b, #d97706) !important;
  color: white !important;
  box-shadow: 0 4px 15px rgba(245, 158, 11, 0.3) !important;
}

.stock-btn.decrease-five:hover {
  background: linear-gradient(135deg, #d97706, #b45309) !important;
  transform: translateY(-2px) !important;
  box-shadow: 0 8px 25px rgba(245, 158, 11, 0.4) !important;
}

.stock-btn.decrease-five:active {
  transform: translateY(0) scale(0.95) !important;
}

/* Increase five button (+5) */
.stock-btn.increase-five {
  background: linear-gradient(135deg, #3b82f6, #2563eb) !important;
  color: white !important;
  box-shadow: 0 4px 15px rgba(59, 130, 246, 0.3) !important;
}

.stock-btn.increase-five:hover {
  background: linear-gradient(135deg, #2563eb, #1d4ed8) !important;
  transform: translateY(-2px) !important;
  box-shadow: 0 8px 25px rgba(59, 130, 246, 0.4) !important;
}

.stock-btn.increase-five:active {
  transform: translateY(0) scale(0.95) !important;
}

/* Increase button (+1) */
.stock-btn.increase {
  background: linear-gradient(135deg, #10b981, #059669) !important;
  color: white !important;
  box-shadow: 0 4px 15px rgba(16, 185, 129, 0.3) !important;
}

.stock-btn.increase:hover {
  background: linear-gradient(135deg, #059669, #047857) !important;
  transform: translateY(-2px) !important;
  box-shadow: 0 8px 25px rgba(16, 185, 129, 0.4) !important;
}

.stock-btn.increase:active {
  transform: translateY(0) scale(0.95) !important;
}

/* Disabled state */
.stock-btn:disabled {
  opacity: 0.5 !important;
  cursor: not-allowed !important;
  transform: none !important;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1) !important;
  background: #9ca3af !important;
}

.stock-btn:disabled:hover {
  transform: none !important;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1) !important;
}

.stock-btn:disabled::before {
  display: none !important;
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .stock-buttons {
    grid-template-columns: repeat(2, 1fr) !important;
    gap: 0.8rem !important;
  }
  
  .stock-btn {
    min-height: 55px !important;
    max-height: 60px !important;
    padding: 1rem 0.6rem !important;
    font-size: 0.9rem !important;
  }
  
  .stock-btn i {
    font-size: 1.1rem !important;
  }
  
  .stock-btn span {
    font-size: 0.85rem !important;
  }
}

@media (max-width: 480px) {
  .stock-buttons {
    grid-template-columns: 1fr 1fr !important;
    gap: 1rem !important;
  }
  
  .stock-btn {
    min-height: 60px !important;
    font-size: 1rem !important;
  }
}

/* Additional fix for inventory controls wrapper */
.inventory-controls-wrapper {
  grid-row: 3 !important;
  margin-top: auto !important;
}

/* Ensure proper stacking and visibility */
.stock-controls,
.stock-buttons,
.stock-btn {
  z-index: 10 !important;
  position: relative !important;
}

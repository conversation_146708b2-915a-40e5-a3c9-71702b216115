const express = require('express');
const Category = require('../models/Category');
const Product = require('../models/Product');
const { authenticateToken } = require('../middleware/auth');
const { 
  applyUnifiedMiddleware, 
  asyncHandler, 
  formatResponse, 
  formatError 
} = require('../middleware/unifiedRouteHandler');
const { 
  sendSuccess, 
  sendError, 
  sendNotFound, 
  handleDatabaseError 
} = require('../middleware/unifiedResponse');

const router = express.Router();

// Apply unified middleware
applyUnifiedMiddleware(router);

// Get all categories
router.get('/', asyncHandler(async (req, res) => {
  const { includeProductCount, status = 'active' } = req.query;
  
  let query = {};
  if (status !== 'all') {
    // Convert status to active boolean or use direct boolean value
    if (status === 'active') {
      query.active = true;
    } else if (status === 'inactive') {
      query.active = false;
    }
    // For backward compatibility with status field if it exists
    else if (status) {
      query.$or = [
        { active: status === true || status === 'true' },
        { status: status }
      ];
    }
  }
  const categories = await Category.find(query)
    .sort({ sortOrder: 1, createdAt: -1 });
  // Include product count if requested
  if (includeProductCount === 'true') {
    for (let category of categories) {
      const productCount = await Product.countDocuments({
        category: category._id,
        status: 'active',
        available: true
      });
      category.productCount = productCount;
    }
  }

  console.log('📂 Categories Query Result:', {
    count: categories.length,
    includeProductCount,
    deviceType: req.deviceInfo?.type
  });

  return sendSuccess(res, categories, 'تم تحميل التصنيفات بنجاح');
}));

// Get category by ID
router.get('/:id', asyncHandler(async (req, res) => {
  const category = await Category.findById(req.params.id);

  if (!category) {
    return sendNotFound(res, 'التصنيف');
  }
  // Get products in this category
  const products = await Product.find({
    category: category._id,
    status: 'active',
    available: true
  }).sort({ createdAt: -1 });

  const categoryWithProducts = {
    ...category.toObject(),
    products,
    productCount: products.length
  };

  console.log('📂 Category Details Retrieved:', {
    categoryId: category._id,
    name: category.name,
    productCount: products.length,
    deviceType: req.deviceInfo?.type
  });

  return sendSuccess(res, categoryWithProducts, 'تم تحميل تفاصيل التصنيف بنجاح');
}));

// Create new category
router.post('/', authenticateToken, asyncHandler(async (req, res) => {
  console.log('📱 Category Creation Request:', {
    deviceType: req.deviceInfo?.type,
    userAgent: req.headers['user-agent'],
    bodyKeys: Object.keys(req.body || {}),
    user: req.user?.name || 'Unknown'
  });

  const { name, description, icon, color, order } = req.body;

  // Validate required fields
  const trimmedName = typeof name === 'string' ? name.trim() : '';
  if (!trimmedName) {
    return sendError(res, 'اسم التصنيف مطلوب', 400, 'MISSING_NAME');
  }

  // Check for duplicate category names
  const existingCategory = await Category.findOne({
    name: { $regex: new RegExp(`^${trimmedName}$`, 'i') },
    status: 'active'
  });

  if (existingCategory) {
    return sendError(res, 'تصنيف بهذا الاسم موجود بالفعل', 409, 'DUPLICATE_NAME');
  }

  // Set default order if not provided
  let categoryOrder = parseInt(order) || 0;
  if (categoryOrder === 0) {
    const lastCategory = await Category.findOne({}, {}, { sort: { order: -1 } });
    categoryOrder = lastCategory ? lastCategory.order + 1 : 1;
  }

  // Create category
  const newCategory = new Category({
    name: trimmedName,
    description: description?.trim() || '',
    icon: icon || 'category',
    color: color || '#6366f1',
    order: categoryOrder,
    status: 'active'
  });

  const savedCategory = await newCategory.save();

  console.log('✅ Category created successfully:', {
    id: savedCategory._id,
    name: savedCategory.name,
    order: savedCategory.order,
    deviceType: req.deviceInfo?.type
  });

  return sendSuccess(res, savedCategory, 'تم إنشاء التصنيف بنجاح', 201);
}));

// Update category
router.put('/:id', authenticateToken, asyncHandler(async (req, res) => {
  const category = await Category.findById(req.params.id);

  if (!category) {
    return sendNotFound(res, 'التصنيف');
  }

  const { name, description, icon, color, order, status } = req.body;

  // Validate and update fields
  if (name !== undefined) {
    const trimmedName = typeof name === 'string' ? name.trim() : '';
    if (!trimmedName) {
      return sendError(res, 'اسم التصنيف لا يمكن أن يكون فارغاً', 400, 'INVALID_NAME');
    }

    // Check for duplicate names (excluding current category)
    const existingCategory = await Category.findOne({
      _id: { $ne: req.params.id },
      name: { $regex: new RegExp(`^${trimmedName}$`, 'i') },
      status: 'active'
    });

    if (existingCategory) {
      return sendError(res, 'تصنيف بهذا الاسم موجود بالفعل', 409, 'DUPLICATE_NAME');
    }

    category.name = trimmedName;
  }

  if (description !== undefined) {
    category.description = description?.trim() || '';
  }

  if (icon !== undefined) {
    category.icon = icon || 'category';
  }

  if (color !== undefined) {
    category.color = color || '#6366f1';
  }

  if (order !== undefined) {
    const numericOrder = parseInt(order);
    if (!isNaN(numericOrder)) {
      category.order = numericOrder;
    }
  }

  if (status !== undefined) {
    const validStatuses = ['active', 'inactive', 'deleted'];
    if (validStatuses.includes(status)) {
      category.status = status;
    }
  }

  category.updatedAt = new Date();
  const updatedCategory = await category.save();

  console.log('✅ Category updated successfully:', {
    id: updatedCategory._id,
    name: updatedCategory.name,
    deviceType: req.deviceInfo?.type
  });

  return sendSuccess(res, updatedCategory, 'تم تحديث التصنيف بنجاح');
}));

// Delete category (soft delete)
router.delete('/:id', authenticateToken, asyncHandler(async (req, res) => {
  const category = await Category.findById(req.params.id);

  if (!category) {
    return sendNotFound(res, 'التصنيف');
  }

  // Check if category has products
  const productCount = await Product.countDocuments({
    category: category._id,
    status: 'active'
  });

  if (productCount > 0) {
    return sendError(res, 
      `لا يمكن حذف التصنيف لأنه يحتوي على ${productCount} منتج`, 
      400, 
      'CATEGORY_HAS_PRODUCTS'
    );
  }

  // Soft delete
  category.status = 'deleted';
  category.updatedAt = new Date();
  await category.save();

  console.log('✅ Category deleted successfully:', {
    id: category._id,
    name: category.name,
    deviceType: req.deviceInfo?.type
  });

  return sendSuccess(res, { id: category._id }, 'تم حذف التصنيف بنجاح');
}));

// Reorder categories
router.patch('/reorder', authenticateToken, asyncHandler(async (req, res) => {
  const { categories } = req.body;

  if (!Array.isArray(categories) || categories.length === 0) {
    return sendError(res, 'قائمة التصنيفات مطلوبة', 400, 'MISSING_CATEGORIES');
  }

  // Update order for each category
  const updatePromises = categories.map((cat, index) => {
    return Category.findByIdAndUpdate(
      cat.id,
      { order: index + 1, updatedAt: new Date() },
      { new: true }
    );
  });

  const updatedCategories = await Promise.all(updatePromises);

  // Filter out null results (categories that weren't found)
  const validUpdates = updatedCategories.filter(cat => cat !== null);

  console.log('✅ Categories reordered successfully:', {
    updatedCount: validUpdates.length,
    deviceType: req.deviceInfo?.type
  });

  return sendSuccess(res, validUpdates, 'تم إعادة ترتيب التصنيفات بنجاح');
}));

// Toggle category status
router.patch('/:id/toggle-status', authenticateToken, asyncHandler(async (req, res) => {
  const category = await Category.findById(req.params.id);

  if (!category) {
    return sendNotFound(res, 'التصنيف');
  }

  // Toggle between active and inactive
  category.status = category.status === 'active' ? 'inactive' : 'active';
  category.updatedAt = new Date();

  const updatedCategory = await category.save();

  const statusText = category.status === 'active' ? 'نشط' : 'غير نشط';

  console.log('✅ Category status toggled:', {
    id: category._id,
    name: category.name,
    newStatus: category.status,
    deviceType: req.deviceInfo?.type
  });

  return sendSuccess(res, updatedCategory, `تم تغيير حالة التصنيف إلى ${statusText}`);
}));

// Get category statistics
router.get('/:id/stats', authenticateToken, asyncHandler(async (req, res) => {
  const category = await Category.findById(req.params.id);

  if (!category) {
    return sendNotFound(res, 'التصنيف');
  }

  // Get product statistics
  const [totalProducts, activeProducts, featuredProducts] = await Promise.all([
    Product.countDocuments({ category: category._id }),
    Product.countDocuments({ category: category._id, status: 'active' }),
    Product.countDocuments({ category: category._id, status: 'active', featured: true })
  ]);

  // Get order statistics (products from this category in orders)
  const orderStats = await Product.aggregate([
    { $match: { category: category._id, status: 'active' } },
    {
      $lookup: {
        from: 'orders',
        localField: '_id',
        foreignField: 'items.product',
        as: 'orders'
      }
    },
    {
      $unwind: '$orders'
    },
    {
      $unwind: '$orders.items'
    },
    {
      $match: {
        '$expr': { $eq: ['$_id', '$orders.items.product'] },
        'orders.status': 'served'
      }
    },
    {
      $group: {
        _id: null,
        totalOrders: { $sum: 1 },
        totalQuantity: { $sum: '$orders.items.quantity' },
        totalRevenue: { $sum: '$orders.items.total' }
      }
    }
  ]);

  const orderData = orderStats[0] || { totalOrders: 0, totalQuantity: 0, totalRevenue: 0 };

  const stats = {
    category: {
      id: category._id,
      name: category.name,
      status: category.status
    },
    products: {
      total: totalProducts,
      active: activeProducts,
      featured: featuredProducts,
      inactive: totalProducts - activeProducts
    },
    orders: {
      totalOrders: orderData.totalOrders,
      totalQuantity: orderData.totalQuantity,
      totalRevenue: orderData.totalRevenue
    }
  };

  console.log('📊 Category statistics generated:', {
    categoryId: category._id,
    name: category.name,
    totalProducts,
    totalRevenue: orderData.totalRevenue,
    deviceType: req.deviceInfo?.type
  });

  return sendSuccess(res, stats, 'تم تحميل إحصائيات التصنيف بنجاح');
}));

module.exports = router;

// حساب المبيعات النهائي
const mongoose = require('mongoose');

async function getFinalSalesReport() {
  try {
    const mongoUri = 'mongodb+srv://besomustafa:<EMAIL>/deshacoffee?retryWrites=true&w=majority&appName=MyCoffeChop';
    
    await mongoose.connect(mongoUri);
    console.log('متصل بقاعدة البيانات');
    
    const db = mongoose.connection.db;
    
    // جلب الطلبات والمستخدمين
    const orders = await db.collection('orders').find({}).toArray();
    const users = await db.collection('users').find({role: 'waiter'}).toArray();
    
    console.log('\n📊 التقرير النهائي للمبيعات 📊');
    console.log('================================');
    
    console.log('\n📈 الإحصائيات العامة:');
    console.log('• إجمالي الطلبات: ' + orders.length);
    console.log('• عدد النُدل: ' + users.length);
    
    // تحليل الطلبات حسب الحالة
    const statusStats = {};
    let allSalesTotal = 0;
    let allDiscountsTotal = 0;
    
    orders.forEach(order => {
      const status = order.status || 'غير محدد';
      statusStats[status] = (statusStats[status] || 0) + 1;
      
      // التأكد من وجود قيم رقمية
      const total = parseFloat(order.total) || 0;
      const discount = parseFloat(order.discount) || 0;
      
      allSalesTotal += total;
      allDiscountsTotal += discount;
    });
    
    console.log('\n📋 توزيع الطلبات:');
    Object.entries(statusStats).forEach(([status, count]) => {
      console.log('• ' + status + ': ' + count + ' طلب');
    });
    
    console.log('\n💰 إجمالي المبيعات (جميع الطلبات):');
    console.log('• إجمالي المبيعات: ' + allSalesTotal.toFixed(2) + ' ريال');
    console.log('• إجمالي الخصومات: ' + allDiscountsTotal.toFixed(2) + ' ريال');
    console.log('• صافي المبيعات: ' + (allSalesTotal - allDiscountsTotal).toFixed(2) + ' ريال');
    
    // حساب مبيعات النُدل
    console.log('\n👥 مبيعات النُدل:');
    console.log('==================');
    
    const waiterSales = {};
    
    // تجميع مبيعات كل نادل
    orders.forEach(order => {
      const waiterId = order.assignedWaiter ? order.assignedWaiter.toString() : null;
      const total = parseFloat(order.total) || 0;
      const discount = parseFloat(order.discount) || 0;
      
      if (waiterId) {
        // البحث عن اسم النادل
        const waiter = users.find(u => u._id.toString() === waiterId);
        const waiterName = waiter ? waiter.username : 'نادل غير معروف';
        
        if (!waiterSales[waiterName]) {
          waiterSales[waiterName] = {
            totalOrders: 0,
            totalSales: 0,
            totalDiscounts: 0,
            netSales: 0
          };
        }
        
        waiterSales[waiterName].totalOrders++;
        waiterSales[waiterName].totalSales += total;
        waiterSales[waiterName].totalDiscounts += discount;
        waiterSales[waiterName].netSales += (total - discount);
      } else {
        // طلبات بدون نادل
        if (!waiterSales['بدون نادل محدد']) {
          waiterSales['بدون نادل محدد'] = {
            totalOrders: 0,
            totalSales: 0,
            totalDiscounts: 0,
            netSales: 0
          };
        }
        
        waiterSales['بدون نادل محدد'].totalOrders++;
        waiterSales['بدون نادل محدد'].totalSales += total;
        waiterSales['بدون نادل محدد'].totalDiscounts += discount;
        waiterSales['بدون نادل محدد'].netSales += (total - discount);
      }
    });
    
    // ترتيب النُدل حسب صافي المبيعات
    const sortedWaiters = Object.entries(waiterSales).sort((a, b) => b[1].netSales - a[1].netSales);
    
    if (sortedWaiters.length === 0) {
      console.log('❌ لا توجد بيانات مبيعات');
    } else {
      sortedWaiters.forEach(([waiterName, stats], index) => {
        console.log('\n' + (index + 1) + '. ' + waiterName);
        console.log('   📦 عدد الطلبات: ' + stats.totalOrders);
        console.log('   💰 إجمالي المبيعات: ' + stats.totalSales.toFixed(2) + ' ريال');
        console.log('   🎯 الخصومات: ' + stats.totalDiscounts.toFixed(2) + ' ريال');
        console.log('   ✅ صافي المبيعات: ' + stats.netSales.toFixed(2) + ' ريال');
        
        if (stats.totalOrders > 0) {
          console.log('   📊 متوسط الطلب: ' + (stats.netSales / stats.totalOrders).toFixed(2) + ' ريال');
        }
        
        if (allSalesTotal > 0) {
          console.log('   📈 نسبة من الإجمالي: ' + ((stats.netSales / (allSalesTotal - allDiscountsTotal)) * 100).toFixed(1) + '%');
        }
      });
    }
    
    console.log('\n✅ انتهى التقرير');
    await mongoose.disconnect();
    
  } catch (error) {
    console.error('❌ خطأ: ' + error.message);
  }
}

getFinalSalesReport();

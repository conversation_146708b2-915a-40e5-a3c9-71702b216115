const mongoose = require('mongoose');
require('dotenv').config();

const userSchema = new mongoose.Schema({}, { strict: false });
const User = mongoose.model('User', userSchema);

async function checkUsers() {
  try {
    await mongoose.connect(process.env.MONGODB_URI);
    console.log('🔗 اتصال بقاعدة البيانات...');
    
    const users = await User.find({}).limit(10);
    console.log(`📋 عرض جميع المستخدمين (${users.length}):`);
    
    users.forEach((user, index) => {
      console.log(`${index + 1}. Username: ${user.username}`);
      console.log(`   Name: ${user.name || 'غير محدد'}`);
      console.log(`   Role: ${user.role}`);
      console.log(`   Active: ${user.isActive}`);
      console.log(`   ID: ${user._id}`);
      console.log('   ---');
    });
    
    // البحث عن admin
    const adminUser = await User.findOne({ 
      $or: [
        { username: 'admin' },
        { role: 'admin' },
        { role: 'manager' }
      ]
    });
    
    if (adminUser) {
      console.log('\n👤 مستخدم Admin موجود:');
      console.log(`Username: ${adminUser.username}`);
      console.log(`Role: ${adminUser.role}`);
      console.log(`Password Hash: ${adminUser.password ? 'موجود' : 'غير موجود'}`);
    } else {
      console.log('\n❌ لا يوجد مستخدم admin');
    }
    
    process.exit(0);
  } catch (error) {
    console.error('❌ خطأ:', error);
    process.exit(1);
  }
}

checkUsers();

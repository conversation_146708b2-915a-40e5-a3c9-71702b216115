/* ====================================
   MENU MANAGER SCREEN - ENHANCED DESIGN
   شاشة قائمة المدير - تصميم محسن
   ==================================== */

/* Container */
.menu-manager-screen {
  padding: 2.5rem;
  background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
  min-height: 100vh;
  direction: rtl;
  font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
}

/* Header */
.menu-manager-screen-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 3rem;
  padding: 2rem;
  background: linear-gradient(135deg, #7c3aed 0%, #8b5cf6 100%);
  border-radius: 20px;
  color: white;
  box-shadow: 0 8px 32px rgba(124, 58, 237, 0.3);
  flex-wrap: wrap;
  gap: 1.5rem;
}

.menu-manager-screen-title {
  flex: 1;
}

.menu-manager-screen-title h1 {
  font-size: 2.2rem;
  font-weight: 800;
  margin: 0 0 0.5rem 0;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
  display: flex;
  align-items: center;
  gap: 1rem;
}

.menu-manager-screen-title h1 i {
  color: #fbbf24;
  text-shadow: 0 0 20px rgba(251, 191, 36, 0.4);
}

.menu-manager-screen-title p {
  font-size: 1.1rem;
  opacity: 0.9;
  margin: 0;
}

/* Controls Section */
.menu-manager-screen-controls {
  display: flex;
  gap: 1rem;
  flex-wrap: wrap;
  align-items: center;
}

.menu-manager-screen-search {
  padding: 1rem 1.5rem;
  border: 2px solid rgba(255, 255, 255, 0.3);
  border-radius: 12px;
  background: rgba(255, 255, 255, 0.1);
  color: white;
  font-size: 1rem;
  font-weight: 500;
  min-width: 250px;
  -webkit-backdrop-filter: blur(10px);
  backdrop-filter: blur(10px);
  transition: all 0.3s ease;
}

.menu-manager-screen-search::placeholder {
  color: rgba(255, 255, 255, 0.7);
}

.menu-manager-screen-search:focus {
  outline: none;
  border-color: rgba(255, 255, 255, 0.6);
  background: rgba(255, 255, 255, 0.2);
}

.menu-manager-screen-category-filter {
  padding: 1rem 1.5rem;
  border: 2px solid rgba(255, 255, 255, 0.3);
  border-radius: 12px;
  background: rgba(255, 255, 255, 0.1);
  color: white;
  font-size: 1rem;
  font-weight: 600;
  -webkit-backdrop-filter: blur(10px);
  backdrop-filter: blur(10px);
  cursor: pointer;
  transition: all 0.3s ease;
  min-width: 150px;
}

.menu-manager-screen-category-filter:focus {
  outline: none;
  border-color: rgba(255, 255, 255, 0.6);
  background: rgba(255, 255, 255, 0.2);
}

.menu-manager-screen-category-filter option {
  background: #7c3aed;
  color: white;
  padding: 0.5rem;
}

.menu-manager-screen-add-btn {
  padding: 1rem 2rem;
  background: linear-gradient(135deg, #10b981, #059669);
  border: none;
  border-radius: 12px;
  color: white;
  font-size: 1rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  box-shadow: 0 4px 15px rgba(16, 185, 129, 0.3);
}

.menu-manager-screen-add-btn:hover {
  background: linear-gradient(135deg, #059669, #047857);
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(16, 185, 129, 0.4);
}

/* Stats Section */
.menu-manager-screen-stats {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 2rem;
  margin-bottom: 3rem;
}

.menu-manager-screen-stat-card {
  background: linear-gradient(145deg, #ffffff 0%, #f8fafc 100%);
  border-radius: 20px;
  padding: 2rem;
  box-shadow: 
    0 8px 32px rgba(0, 0, 0, 0.08),
    0 4px 12px rgba(0, 0, 0, 0.04),
    inset 0 1px 0 rgba(255, 255, 255, 0.8);
  border: 1px solid rgba(226, 232, 240, 0.8);
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  gap: 1.5rem;
}

.menu-manager-screen-stat-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 12px 40px rgba(0, 0, 0, 0.12);
}

.menu-manager-screen-stat-icon {
  width: 70px;
  height: 70px;
  border-radius: 16px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.8rem;
  color: white;
  flex-shrink: 0;
}

.menu-manager-screen-stat-card.total .menu-manager-screen-stat-icon {
  background: linear-gradient(135deg, #3b82f6, #2563eb);
  box-shadow: 0 8px 25px rgba(59, 130, 246, 0.3);
}

.menu-manager-screen-stat-card.available .menu-manager-screen-stat-icon {
  background: linear-gradient(135deg, #10b981, #059669);
  box-shadow: 0 8px 25px rgba(16, 185, 129, 0.3);
}

.menu-manager-screen-stat-card.unavailable .menu-manager-screen-stat-icon {
  background: linear-gradient(135deg, #ef4444, #dc2626);
  box-shadow: 0 8px 25px rgba(239, 68, 68, 0.3);
}

.menu-manager-screen-stat-card.categories .menu-manager-screen-stat-icon {
  background: linear-gradient(135deg, #f59e0b, #d97706);
  box-shadow: 0 8px 25px rgba(245, 158, 11, 0.3);
}

.menu-manager-screen-stat-content h3 {
  font-size: 2.2rem;
  font-weight: 800;
  color: #1e293b;
  margin: 0 0 0.25rem 0;
  line-height: 1;
}

.menu-manager-screen-stat-content p {
  color: #64748b;
  font-size: 1rem;
  font-weight: 600;
  margin: 0;
}

/* Enhanced Menu Grid */
.menu-manager-screen-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(380px, 1fr));
  gap: 2.5rem;
  margin-bottom: 2rem;
}

/* Enhanced Menu Item Cards */
.menu-manager-screen-item {
  background: linear-gradient(145deg, #ffffff 0%, #f8fafc 100%);
  border-radius: 24px;
  overflow: hidden;
  box-shadow: 
    0 12px 40px rgba(0, 0, 0, 0.08),
    0 4px 12px rgba(0, 0, 0, 0.04),
    inset 0 1px 0 rgba(255, 255, 255, 0.8);
  border: 1px solid rgba(226, 232, 240, 0.8);
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  min-height: 320px;
}

.menu-manager-screen-item::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 6px;
  background: linear-gradient(90deg, #7c3aed, #8b5cf6, #a855f7);
  transition: all 0.3s ease;
}

.menu-manager-screen-item::after {
  content: '';
  position: absolute;
  top: -50%;
  right: -50%;
  width: 100%;
  height: 100%;
  background: radial-gradient(circle, rgba(124, 58, 237, 0.05) 0%, transparent 70%);
  opacity: 0;
  transition: opacity 0.3s ease;
}

.menu-manager-screen-item:hover {
  transform: translateY(-8px) scale(1.02);
  box-shadow: 
    0 20px 60px rgba(0, 0, 0, 0.12),
    0 8px 20px rgba(0, 0, 0, 0.08),
    inset 0 1px 0 rgba(255, 255, 255, 0.9);
}

.menu-manager-screen-item:hover::before {
  height: 8px;
  box-shadow: 0 0 20px rgba(124, 58, 237, 0.4);
}

.menu-manager-screen-item:hover::after {
  opacity: 1;
}

.menu-manager-screen-item.unavailable {
  opacity: 0.7;
  filter: grayscale(20%);
}

.menu-manager-screen-item.unavailable::before {
  background: linear-gradient(90deg, #ef4444, #dc2626);
}

/* Item Image */
.menu-manager-screen-item-image {
  width: 100%;
  height: 180px;
  background: linear-gradient(135deg, #f1f5f9, #e2e8f0);
  display: flex;
  align-items: center;
  justify-content: center;
  color: #94a3b8;
  font-size: 3rem;
  position: relative;
  overflow: hidden;
}

.menu-manager-screen-item-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 0.3s ease;
}

.menu-manager-screen-item:hover .menu-manager-screen-item-image img {
  transform: scale(1.05);
}

.menu-manager-screen-item-image i {
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

/* Item Content */
.menu-manager-screen-item-content {
  padding: 2rem;
  position: relative;
  z-index: 1;
}

.menu-manager-screen-item-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 1.5rem;
  gap: 1rem;
}

.menu-manager-screen-item-title {
  flex: 1;
}

.menu-manager-screen-item-title h3 {
  font-size: 1.4rem;
  font-weight: 700;
  color: #1e293b;
  margin: 0 0 0.5rem 0;
  line-height: 1.2;
}

.menu-manager-screen-item-category {
  font-size: 0.9rem;
  color: #7c3aed;
  font-weight: 600;
  background: rgba(124, 58, 237, 0.1);
  padding: 0.25rem 0.75rem;
  border-radius: 12px;
  border: 1px solid rgba(124, 58, 237, 0.2);
}

.menu-manager-screen-item-status {
  padding: 0.5rem 1rem;
  border-radius: 16px;
  font-size: 0.85rem;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  flex-shrink: 0;
}

.menu-manager-screen-item-status.available {
  background: linear-gradient(135deg, #10b981, #059669);
  color: white;
  box-shadow: 0 4px 15px rgba(16, 185, 129, 0.3);
}

.menu-manager-screen-item-status.unavailable {
  background: linear-gradient(135deg, #ef4444, #dc2626);
  color: white;
  box-shadow: 0 4px 15px rgba(239, 68, 68, 0.3);
}

/* Item Price */
.menu-manager-screen-item-price {
  font-size: 1.8rem;
  font-weight: 800;
  color: #059669;
  margin-bottom: 1rem;
  background: linear-gradient(135deg, #10b981, #059669);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

/* Item Description */
.menu-manager-screen-item-description {
  color: #64748b;
  font-size: 0.95rem;
  line-height: 1.5;
  margin-bottom: 1.5rem;
  max-height: 3em;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  line-clamp: 2;
  -webkit-box-orient: vertical;
}

/* Item Actions */
.menu-manager-screen-item-actions {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 0.75rem;
}

.menu-manager-screen-action-btn {
  padding: 0.75rem;
  border: none;
  border-radius: 12px;
  font-size: 0.9rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
  position: relative;
  overflow: hidden;
}

.menu-manager-screen-action-btn::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
  transition: left 0.5s;
}

.menu-manager-screen-action-btn:hover::before {
  left: 100%;
}

.menu-manager-screen-action-btn.edit {
  background: linear-gradient(135deg, #3b82f6, #2563eb);
  color: white;
  box-shadow: 0 4px 15px rgba(59, 130, 246, 0.3);
}

.menu-manager-screen-action-btn.edit:hover {
  background: linear-gradient(135deg, #2563eb, #1d4ed8);
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(59, 130, 246, 0.4);
}

.menu-manager-screen-action-btn.toggle {
  background: linear-gradient(135deg, #f59e0b, #d97706);
  color: white;
  box-shadow: 0 4px 15px rgba(245, 158, 11, 0.3);
}

.menu-manager-screen-action-btn.toggle:hover {
  background: linear-gradient(135deg, #d97706, #b45309);
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(245, 158, 11, 0.4);
}

.menu-manager-screen-action-btn.delete {
  background: linear-gradient(135deg, #ef4444, #dc2626);
  color: white;
  box-shadow: 0 4px 15px rgba(239, 68, 68, 0.3);
}

.menu-manager-screen-action-btn.delete:hover {
  background: linear-gradient(135deg, #dc2626, #b91c1c);
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(239, 68, 68, 0.4);
}

/* Loading State */
.menu-manager-screen-loading {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 4rem;
  color: #64748b;
  font-size: 1.2rem;
  gap: 1rem;
}

.menu-manager-screen-loading i {
  font-size: 2rem;
  color: #7c3aed;
  animation: spin 1s linear infinite;
}

/* Empty State */
.menu-manager-screen-empty {
  text-align: center;
  padding: 4rem;
  color: #64748b;
  grid-column: 1 / -1;
}

.menu-manager-screen-empty-icon {
  font-size: 4rem;
  color: #cbd5e1;
  margin-bottom: 1.5rem;
}

.menu-manager-screen-empty h3 {
  font-size: 1.5rem;
  color: #475569;
  margin: 0 0 1rem 0;
}

.menu-manager-screen-empty p {
  font-size: 1.1rem;
  margin: 0;
}

/* Animations */
@keyframes spin {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

/* Responsive Design */
@media (max-width: 1200px) {
  .menu-manager-screen-grid {
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
  }
}

@media (max-width: 768px) {
  .menu-manager-screen {
    padding: 1.5rem;
  }

  .menu-manager-screen-header {
    flex-direction: column;
    text-align: center;
  }

  .menu-manager-screen-controls {
    width: 100%;
    justify-content: center;
  }

  .menu-manager-screen-search,
  .menu-manager-screen-category-filter {
    min-width: auto;
    flex: 1;
  }

  .menu-manager-screen-stats {
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  }

  .menu-manager-screen-grid {
    grid-template-columns: 1fr;
  }

  .menu-manager-screen-item {
    min-height: 280px;
  }

  .menu-manager-screen-item-content {
    padding: 1.5rem;
  }

  .menu-manager-screen-item-header {
    flex-direction: column;
    gap: 0.75rem;
  }

  .menu-manager-screen-item-status {
    align-self: flex-end;
  }
}

@media (max-width: 480px) {
  .menu-manager-screen-item {
    min-height: 240px;
  }

  .menu-manager-screen-item-content {
    padding: 1.25rem;
  }

  .menu-manager-screen-item-title h3 {
    font-size: 1.2rem;
  }

  .menu-manager-screen-item-price {
    font-size: 1.5rem;
  }

  .menu-manager-screen-action-btn {
    font-size: 0.8rem;
    padding: 0.5rem;
  }

  .menu-manager-screen-controls {
    flex-direction: column;
  }

  .menu-manager-screen-add-btn {
    width: 100%;
    justify-content: center;
  }
}

/* ====================================
   BOOTSTRAP COMPATIBILITY - MENU ENHANCED
   توافق Bootstrap - قائمة محسنة
   ==================================== */

/* Menu Bootstrap Container */
.menu-bootstrap-container {
  background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%) !important;
  min-height: 100vh;
  direction: rtl;
  font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif !important;
}

.menu-item-card {
  background: linear-gradient(145deg, #ffffff 0%, #f8fafc 100%) !important;
  border: none !important;
  border-radius: 24px !important;
  box-shadow: 
    0 12px 40px rgba(0, 0, 0, 0.08),
    0 4px 12px rgba(0, 0, 0, 0.04) !important;
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1) !important;
  position: relative;
  overflow: hidden;
  min-height: 380px !important;
}

.menu-item-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 6px;
  background: linear-gradient(90deg, #7c3aed, #8b5cf6, #a855f7);
  transition: all 0.3s ease;
}

.menu-item-card:hover {
  transform: translateY(-8px) scale(1.02) !important;
  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.12) !important;
}

.menu-item-card:hover::before {
  height: 8px;
  box-shadow: 0 0 20px rgba(124, 58, 237, 0.4);
}

/* Enhanced Menu Search */
.menu-search-enhanced {
  background: rgba(124, 58, 237, 0.1) !important;
  border: 2px solid rgba(124, 58, 237, 0.3) !important;
  border-radius: 12px !important;
  padding: 1rem 1.5rem !important;
  font-weight: 500 !important;
  transition: all 0.3s ease !important;
}

.menu-search-enhanced:focus {
  background: rgba(124, 58, 237, 0.15) !important;
  border-color: rgba(124, 58, 237, 0.5) !important;
  box-shadow: 0 0 0 0.2rem rgba(124, 58, 237, 0.25) !important;
}

/* Enhanced Menu Buttons */
.menu-btn-enhanced {
  border-radius: 12px !important;
  font-weight: 600 !important;
  transition: all 0.3s ease !important;
  position: relative;
  overflow: hidden;
}

.menu-btn-enhanced::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
  transition: left 0.5s;
}

.menu-btn-enhanced:hover::before {
  left: 100%;
}

.menu-btn-enhanced:hover {
  transform: translateY(-2px) !important;
}

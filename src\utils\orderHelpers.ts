// مساعدات إضافية للطلبات
import type { Order } from '../types/Order';

// دالة لحساب إجمالي المبيعات من مجموعة طلبات (تأخذ الخصومات بعين الاعتبار)
export const calculateTotalSales = (orders: Order[]): number => {
  return orders.reduce((sum, order) => sum + getOrderFinalPrice(order), 0);
};

// دالة لحساب متوسط قيمة الطلب
export const calculateAverageOrderValue = (orders: Order[]): number => {
  if (orders.length === 0) return 0;
  return calculateTotalSales(orders) / orders.length;
};

// دالة لتصفية الطلبات حسب الحالة
export const filterOrdersByStatus = (orders: Order[], status: Order['status']): Order[] => {
  return orders.filter(order => order.status === status);
};

// دالة لتصفية طلبات اليوم
export const getTodayOrders = (orders: Order[]): Order[] => {
  const today = new Date();
  const startOfDay = new Date(today.getFullYear(), today.getMonth(), today.getDate());
  const endOfDay = new Date(today.getFullYear(), today.getMonth(), today.getDate() + 1);
  return orders.filter(order => {
    if (!order.createdAt) return false;
    const orderDate = new Date(order.createdAt);
    return orderDate >= startOfDay && orderDate < endOfDay;
  });
};

// دالة لحساب إحصائيات شاملة
export const calculateOrderStats = (orders: Order[]) => {
  const deliveredOrders = filterOrdersByStatus(orders, 'delivered');
  const pendingOrders = filterOrdersByStatus(orders, 'pending');
  const preparingOrders = filterOrdersByStatus(orders, 'preparing');
  const cancelledOrders = filterOrdersByStatus(orders, 'cancelled');
  
  return {
    totalOrders: orders.length,
    deliveredOrders: deliveredOrders.length,
    pendingOrders: pendingOrders.length,
    preparingOrders: preparingOrders.length,
    cancelledOrders: cancelledOrders.length,
    
    totalSales: calculateTotalSales(deliveredOrders),
    pendingSales: calculateTotalSales(pendingOrders),
    averageOrderValue: calculateAverageOrderValue(deliveredOrders),
    
    todayOrders: getTodayOrders(orders),
    todaySales: calculateTotalSales(getTodayOrders(deliveredOrders))
  };
};

// دالة للتحقق من صحة بيانات الطلب
export const validateOrderData = (order: any): boolean => {
  try {
    if (!order || typeof order !== 'object') return false;
    if (!order._id || !order.orderNumber || !order.waiterName) return false;
    if (!order.status || !order.items || !Array.isArray(order.items)) return false;
    if (!order.createdAt) return false;
    
    // التحقق من وجود سعر صالح
    const total = getOrderFinalPrice(order);
    return total >= 0;
  } catch (error) {
    console.error('Error validating order data:', error);
    return false;
  }
};

// دالة لتنظيف بيانات الطلبات
export const sanitizeOrders = (orders: any[]): Order[] => {
  if (!Array.isArray(orders)) return [];
  
  return orders.filter(validateOrderData) as Order[];
};

// دالة موحدة لحساب المجموع النهائي للطلب (تأخذ الخصومات بعين الاعتبار)
export const getOrderFinalPrice = (order: any): number => {
  if (!order) return 0;

  // If the order object comes from the backend and has totals.total, use it directly.
  // This is the most reliable value.
  if (order.totals && order.totals.total !== undefined) {
    return order.totals.total;
  }

  // Provisional calculation for frontend display if totals.total is not available
  // (e.g., for a new order being built in the UI before sending to backend)
  const itemsTotal = order.items ? order.items.reduce((sum: number, item: any) => {
    const price = Number(item.price) || 0;
    const quantity = Number(item.quantity) || 0;
    return sum + (price * quantity);
  }, 0) : 0;

  let finalPrice = itemsTotal;

  // Apply discount if present and approved
  const discountAmount = Number(order.discountAmount) || 0;
  if (order.discountStatus === 'approved' && discountAmount > 0) {
    finalPrice -= discountAmount;
  }

  // Frontend typically doesn't calculate tax directly here unless it's a fixed rate known client-side
  // and the backend isn't providing it. For now, we assume tax is handled by backend.
  // If frontend needs to show an estimated tax, that logic would go here.

  // Delivery fee might be added on the frontend for estimation
  const deliveryFee = Number(order.delivery?.fee) || 0;
  if (order.orderType === 'delivery' && deliveryFee > 0) {
    finalPrice += deliveryFee;
  }
  
  // Ensure final price is not negative
  return Math.max(0, finalPrice);
};

export default {
  calculateTotalSales,
  calculateAverageOrderValue,
  filterOrdersByStatus,
  getTodayOrders,
  calculateOrderStats,
  validateOrderData,
  sanitizeOrders
};

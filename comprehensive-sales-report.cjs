// تقرير مبيعات شامل مع تفاصيل البيانات
const mongoose = require('mongoose');

async function getComprehensiveSalesReport() {
  try {
    const mongoUri = 'mongodb+srv://besomustafa:<EMAIL>/deshacoffee?retryWrites=true&w=majority&appName=MyCoffeChop';
    
    console.log('🔗 الاتصال بقاعدة البيانات...');
    await mongoose.connect(mongoUri);
    console.log('✅ تم الاتصال بنجاح');
    
    const db = mongoose.connection.db;
    
    // جلب البيانات
    const orders = await db.collection('orders').find({}).toArray();
    const users = await db.collection('users').find({}).toArray();
    const waiters = users.filter(user => user.role === 'waiter');
    
    console.log('\n' + '='.repeat(60));
    console.log('📊 تقرير شامل للمبيعات ونظام إدارة المقهى');
    console.log('='.repeat(60));
    
    console.log('\n📈 الإحصائيات الأساسية:');
    console.log('• إجمالي الطلبات في النظام: ' + orders.length);
    console.log('• إجمالي المستخدمين: ' + users.length);
    console.log('• عدد النُدل: ' + waiters.length);
    
    // إحصائيات تفصيلية للطلبات
    const statusBreakdown = {};
    let totalSalesValue = 0;
    let totalDiscountsValue = 0;
    
    // فحص عينة من البيانات لفهم البنية
    console.log('\n🔍 تحليل بنية البيانات:');
    if (orders.length > 0) {
      const sample = orders[0];
      console.log('مثال على طلب:');
      console.log('• ID: ' + sample._id);
      console.log('• الحالة: ' + (sample.status || 'غير محدد'));
      console.log('• الإجمالي: ' + sample.total + ' (نوع: ' + typeof sample.total + ')');
      console.log('• الخصم: ' + sample.discount + ' (نوع: ' + typeof sample.discount + ')');
      console.log('• النادل: ' + sample.assignedWaiter);
      console.log('• التاريخ: ' + sample.createdAt);
    }
    
    orders.forEach(order => {
      const status = order.status || 'غير محدد';
      statusBreakdown[status] = (statusBreakdown[status] || 0) + 1;
      
      // تحويل القيم إلى أرقام بأمان
      const orderTotal = Number(order.total) || 0;
      const orderDiscount = Number(order.discount) || 0;
      
      totalSalesValue += orderTotal;
      totalDiscountsValue += orderDiscount;
    });
    
    console.log('\n📋 توزيع الطلبات حسب الحالة:');
    Object.entries(statusBreakdown).forEach(([status, count]) => {
      const percentage = ((count / orders.length) * 100).toFixed(1);
      console.log('• ' + status + ': ' + count + ' طلب (' + percentage + '%)');
    });
    
    console.log('\n💰 إجمالي القيم المالية:');
    console.log('• مجموع قيم جميع الطلبات: ' + totalSalesValue.toFixed(2) + ' ريال');
    console.log('• مجموع الخصومات: ' + totalDiscountsValue.toFixed(2) + ' ريال');
    console.log('• صافي القيمة: ' + (totalSalesValue - totalDiscountsValue).toFixed(2) + ' ريال');
    
    // تحليل النُدل
    console.log('\n👥 تحليل النُدل:');
    console.log('='.repeat(40));
    
    if (waiters.length === 0) {
      console.log('❌ لا يوجد نُدل مسجلين في النظام');
    } else {
      console.log('📝 قائمة النُدل المسجلين:');
      waiters.forEach((waiter, index) => {
        console.log((index + 1) + '. ' + waiter.username + ' (ID: ' + waiter._id + ')');
        console.log('   الإيميل: ' + (waiter.email || 'غير محدد'));
      });
      
      // حساب مبيعات كل نادل
      console.log('\n💼 أداء النُدل:');
      const waiterPerformance = {};
      
      waiters.forEach(waiter => {
        const waiterOrders = orders.filter(order => 
          order.assignedWaiter && order.assignedWaiter.toString() === waiter._id.toString()
        );
        
        let waiterTotal = 0;
        let waiterDiscount = 0;
        let completedCount = 0;
        
        waiterOrders.forEach(order => {
          const orderTotal = Number(order.total) || 0;
          const orderDiscount = Number(order.discount) || 0;
          
          waiterTotal += orderTotal;
          waiterDiscount += orderDiscount;
          
          if (order.status === 'completed') {
            completedCount++;
          }
        });
        
        waiterPerformance[waiter.username] = {
          totalOrders: waiterOrders.length,
          completedOrders: completedCount,
          totalSales: waiterTotal,
          totalDiscounts: waiterDiscount,
          netSales: waiterTotal - waiterDiscount
        };
      });
      
      // ترتيب النُدل حسب الأداء
      const sortedPerformance = Object.entries(waiterPerformance)
        .sort((a, b) => b[1].netSales - a[1].netSales);
      
      if (sortedPerformance.length === 0) {
        console.log('❌ لا توجد طلبات مرتبطة بالنُدل');
      } else {
        sortedPerformance.forEach(([waiterName, stats], index) => {
          console.log('\n🏆 ' + (index + 1) + '. النادل: ' + waiterName);
          console.log('   📦 إجمالي الطلبات: ' + stats.totalOrders);
          console.log('   ✅ الطلبات المكتملة: ' + stats.completedOrders);
          console.log('   💰 إجمالي المبيعات: ' + stats.totalSales.toFixed(2) + ' ريال');
          console.log('   🎯 إجمالي الخصومات: ' + stats.totalDiscounts.toFixed(2) + ' ريال');
          console.log('   ✨ صافي المبيعات: ' + stats.netSales.toFixed(2) + ' ريال');
          
          if (stats.totalOrders > 0) {
            console.log('   📊 متوسط قيمة الطلب: ' + (stats.netSales / stats.totalOrders).toFixed(2) + ' ريال');
          }
          
          if (totalSalesValue > 0) {
            const percentage = ((stats.netSales / (totalSalesValue - totalDiscountsValue)) * 100).toFixed(1);
            console.log('   📈 نسبة من إجمالي المبيعات: ' + percentage + '%');
          }
          
          console.log('   ' + '-'.repeat(30));
        });
      }
    }
    
    // طلبات بدون نادل
    const ordersWithoutWaiter = orders.filter(order => !order.assignedWaiter);
    if (ordersWithoutWaiter.length > 0) {
      console.log('\n⚠️ طلبات بدون نادل محدد:');
      console.log('عدد الطلبات: ' + ordersWithoutWaiter.length);
      
      let unassignedTotal = 0;
      let unassignedDiscount = 0;
      
      ordersWithoutWaiter.forEach(order => {
        unassignedTotal += (Number(order.total) || 0);
        unassignedDiscount += (Number(order.discount) || 0);
      });
      
      console.log('إجمالي القيمة: ' + unassignedTotal.toFixed(2) + ' ريال');
      console.log('إجمالي الخصومات: ' + unassignedDiscount.toFixed(2) + ' ريال');
      console.log('صافي القيمة: ' + (unassignedTotal - unassignedDiscount).toFixed(2) + ' ريال');
    }
    
    console.log('\n' + '='.repeat(60));
    console.log('📅 تاريخ التقرير: ' + new Date().toLocaleString('ar-SA'));
    console.log('✅ انتهى التقرير الشامل');
    console.log('='.repeat(60));
    
    await mongoose.disconnect();
    
  } catch (error) {
    console.error('❌ خطأ في إنتاج التقرير: ' + error.message);
    console.error(error);
  }
}

getComprehensiveSalesReport();

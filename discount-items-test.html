<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار تفاصيل الأصناف في طلب الخصم</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
            direction: rtl;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .test-section {
            margin-bottom: 30px;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 8px;
            background: #f9f9f9;
        }
        .test-section h3 {
            margin-top: 0;
            color: #333;
            border-bottom: 2px solid #4CAF50;
            padding-bottom: 10px;
        }
        button {
            background: #4CAF50;
            color: white;
            border: none;
            padding: 10px 20px;
            margin: 5px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
        }
        button:hover {
            background: #45a049;
        }
        button.create {
            background: #2196F3;
        }
        button.create:hover {
            background: #1976D2;
        }
        .result {
            background: #f0f8ff;
            border: 1px solid #4CAF50;
            padding: 15px;
            margin-top: 10px;
            border-radius: 4px;
            white-space: pre-wrap;
            font-family: monospace;
            direction: ltr;
            text-align: left;
        }
        .error {
            background: #ffebee;
            border: 1px solid #f44336;
            color: #c62828;
        }
        .success {
            background: #e8f5e8;
            border: 1px solid #4CAF50;
            color: #2e7d32;
        }
        .discount-card {
            border: 1px solid #ddd;
            border-radius: 8px;
            padding: 15px;
            margin: 10px 0;
            background: white;
        }
        .discount-card h4 {
            margin-top: 0;
            color: #2196F3;
        }
        .order-item {
            background: #f8f9fa;
            padding: 10px;
            margin: 5px 0;
            border-radius: 5px;
            border: 1px solid #dee2e6;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔍 اختبار تفاصيل الأصناف في طلب الخصم</h1>
        
        <div class="test-section">
            <h3>🎯 إنشاء بيانات تجريبية</h3>
            <button class="create" onclick="createOrderWithItems()">📦 إنشاء طلب مع أصناف</button>
            <button class="create" onclick="createDiscountRequestWithOrder()">💰 إنشاء طلب خصم مع طلب مرتبط</button>
            <button onclick="testCompleteFlow()">🔄 اختبار التدفق الكامل</button>
            <div id="creation-results"></div>
        </div>

        <div class="test-section">
            <h3>📋 فحص بيانات طلبات الخصم الحالية</h3>
            <button onclick="fetchDiscountRequestsWithItems()">📊 جلب طلبات الخصم مع الأصناف</button>
            <button onclick="analyzeItemsData()">🔍 تحليل بيانات الأصناف</button>
            <div id="analysis-results"></div>
        </div>

        <div class="test-section">
            <h3>🛠️ اختبار العرض</h3>
            <button onclick="testModalDisplay()">🪟 اختبار عرض Modal</button>
            <button onclick="checkCSSStyles()">🎨 فحص تنسيق CSS</button>
            <div id="display-results"></div>
        </div>

        <div class="test-section">
            <h3>📊 عرض البيانات</h3>
            <div id="discount-requests-display"></div>
        </div>
    </div>

    <script>
        const API_BASE = 'http://localhost:3001/api/v1';
        
        let authToken = localStorage.getItem('authToken');
        if (!authToken) {
            authToken = prompt('يرجى إدخال Auth Token:');
            if (authToken) {
                localStorage.setItem('authToken', authToken);
            }
        }

        const headers = {
            'Content-Type': 'application/json',
            'Authorization': `Bearer ${authToken}`
        };

        async function createOrderWithItems() {
            const resultDiv = document.getElementById('creation-results');
            
            try {
                resultDiv.innerHTML = '<div class="result">⏳ جاري إنشاء طلب مع أصناف...</div>';
                
                const orderData = {
                    tableNumber: 'T-' + Math.floor(Math.random() * 100),
                    customerName: 'عميل تجريبي للأصناف',
                    waiterName: 'نادل تجريبي',
                    items: [
                        {
                            name: 'قهوة عربية',
                            productName: 'قهوة عربية',
                            quantity: 2,
                            price: 15.50,
                            size: 'كبير',
                            category: 'مشروبات ساخنة',
                            notes: 'بدون سكر'
                        },
                        {
                            name: 'كعكة الشوكولاتة',
                            productName: 'كعكة الشوكولاتة',
                            quantity: 1,
                            price: 25.00,
                            size: 'متوسط',
                            category: 'حلويات',
                            notes: 'مع الكريمة'
                        },
                        {
                            name: 'عصير برتقال طبيعي',
                            productName: 'عصير برتقال طبيعي',
                            quantity: 1,
                            price: 12.00,
                            size: 'صغير',
                            category: 'مشروبات باردة'
                        }
                    ],
                    totals: {
                        subtotal: 68.00,
                        tax: 6.80,
                        discount: 0,
                        total: 74.80
                    },
                    totalAmount: 74.80,
                    status: 'pending'
                };

                const response = await fetch(`${API_BASE}/orders`, {
                    method: 'POST',
                    headers,
                    body: JSON.stringify(orderData)
                });

                const data = await response.json();
                
                if (data.success) {
                    resultDiv.innerHTML = `<div class="result success">
✅ تم إنشاء الطلب بنجاح:
Order ID: ${data.data._id}
Order Number: ${data.data.orderNumber}
عدد الأصناف: ${data.data.items?.length || 0}
المجموع: ${data.data.totalAmount} ج.م

${JSON.stringify(data.data, null, 2)}
                    </div>`;
                    
                    // حفظ Order ID للاستخدام لاحقاً
                    localStorage.setItem('testOrderId', data.data._id);
                    localStorage.setItem('testOrderNumber', data.data.orderNumber);
                } else {
                    resultDiv.innerHTML = `<div class="result error">❌ فشل في إنشاء الطلب: ${data.message}</div>`;
                }
                
            } catch (error) {
                console.error('Error creating order:', error);
                resultDiv.innerHTML = `<div class="result error">❌ خطأ في إنشاء الطلب: ${error.message}</div>`;
            }
        }

        async function createDiscountRequestWithOrder() {
            const resultDiv = document.getElementById('creation-results');
            
            const testOrderId = localStorage.getItem('testOrderId');
            const testOrderNumber = localStorage.getItem('testOrderNumber');
            
            if (!testOrderId) {
                resultDiv.innerHTML = '<div class="result error">❌ يجب إنشاء طلب أولاً</div>';
                return;
            }
            
            try {
                resultDiv.innerHTML = '<div class="result">⏳ جاري إنشاء طلب خصم...</div>';
                
                const discountData = {
                    orderId: testOrderId,
                    orderNumber: testOrderNumber,
                    customerName: 'عميل تجريبي للأصناف',
                    originalAmount: 74.80,
                    requestedDiscount: 10.00,
                    reason: 'خصم لاختبار عرض الأصناف في Modal',
                    waiterName: 'نادل تجريبي',
                    tableNumber: 'T-999'
                };

                const response = await fetch(`${API_BASE}/discount-requests`, {
                    method: 'POST',
                    headers,
                    body: JSON.stringify(discountData)
                });

                const data = await response.json();
                
                if (data.success) {
                    resultDiv.innerHTML = `<div class="result success">
✅ تم إنشاء طلب الخصم بنجاح:
Discount ID: ${data.data._id}
Order ID المرتبط: ${data.data.orderId}
المبلغ الأصلي: ${data.data.originalAmount} ج.م
مبلغ الخصم: ${data.data.requestedDiscount} ج.م

${JSON.stringify(data.data, null, 2)}
                    </div>`;
                } else {
                    resultDiv.innerHTML = `<div class="result error">❌ فشل في إنشاء طلب الخصم: ${data.message}</div>`;
                }
                
            } catch (error) {
                console.error('Error creating discount request:', error);
                resultDiv.innerHTML = `<div class="result error">❌ خطأ في إنشاء طلب الخصم: ${error.message}</div>`;
            }
        }

        async function testCompleteFlow() {
            const resultDiv = document.getElementById('creation-results');
            resultDiv.innerHTML = '<div class="result">⏳ جاري اختبار التدفق الكامل...</div>';
            
            try {
                // خطوة 1: إنشاء طلب
                await createOrderWithItems();
                await new Promise(resolve => setTimeout(resolve, 1000));
                
                // خطوة 2: إنشاء طلب خصم
                await createDiscountRequestWithOrder();
                await new Promise(resolve => setTimeout(resolve, 1000));
                
                // خطوة 3: جلب البيانات وتحليلها
                await fetchDiscountRequestsWithItems();
                
                resultDiv.innerHTML += '<div class="result success">✅ تم إكمال التدفق الكامل بنجاح!</div>';
                
            } catch (error) {
                console.error('Error in complete flow:', error);
                resultDiv.innerHTML += `<div class="result error">❌ خطأ في التدفق الكامل: ${error.message}</div>`;
            }
        }

        async function fetchDiscountRequestsWithItems() {
            const resultDiv = document.getElementById('analysis-results');
            const displayDiv = document.getElementById('discount-requests-display');
            
            try {
                resultDiv.innerHTML = '<div class="result">⏳ جاري جلب طلبات الخصم...</div>';
                
                const response = await fetch(`${API_BASE}/discount-requests`, { headers });
                const data = await response.json();
                
                if (data.success) {
                    const discountRequests = data.data || [];
                    
                    let analysisResult = `📊 تحليل طلبات الخصم:
عدد طلبات الخصم: ${discountRequests.length}
`;

                    let hasItemsCount = 0;
                    let totalItems = 0;
                    
                    // عرض تفصيلي
                    let displayHTML = '<h3>📋 طلبات الخصم مع تفاصيل الأصناف</h3>';
                    
                    discountRequests.forEach((request, index) => {
                        const orderItems = request.order?.items || [];
                        if (orderItems.length > 0) {
                            hasItemsCount++;
                            totalItems += orderItems.length;
                        }
                        
                        analysisResult += `
طلب خصم #${index + 1}:
  - Order ID: ${request.orderId}
  - Order Number: ${request.orderNumber}
  - له طلب مرتبط: ${request.order ? 'نعم' : 'لا'}
  - عدد الأصناف: ${orderItems.length}
`;

                        // عرض في HTML
                        displayHTML += `
                        <div class="discount-card">
                            <h4>طلب خصم #${request.orderNumber}</h4>
                            <p><strong>المبلغ:</strong> ${request.amount || request.requestedDiscount || 0} ج.م</p>
                            <p><strong>رقم الطاولة:</strong> ${request.order?.tableNumber || request.tableNumber || 'غير محدد'}</p>
                            <p><strong>عدد الأصناف:</strong> ${orderItems.length}</p>
                            
                            ${orderItems.length > 0 ? `
                                <h5>الأصناف:</h5>
                                ${orderItems.map(item => `
                                    <div class="order-item">
                                        <strong>${item.name || item.productName || 'صنف غير محدد'}</strong><br>
                                        الكمية: ${item.quantity || 1} | 
                                        السعر: ${item.price || 'غير محدد'} ج.م |
                                        الفئة: ${item.category || 'غير محدد'}
                                        ${item.notes ? `<br>ملاحظات: ${item.notes}` : ''}
                                    </div>
                                `).join('')}
                            ` : '<p style="color: #f44336;">لا توجد أصناف متاحة</p>'}
                        </div>`;
                    });

                    analysisResult += `
إجمالي الإحصائيات:
  - طلبات لها أصناف: ${hasItemsCount}
  - إجمالي الأصناف: ${totalItems}
  - متوسط الأصناف لكل طلب: ${hasItemsCount > 0 ? (totalItems / hasItemsCount).toFixed(1) : 0}
`;

                    resultDiv.innerHTML = `<div class="result success">${analysisResult}</div>`;
                    displayDiv.innerHTML = displayHTML;
                    
                } else {
                    resultDiv.innerHTML = `<div class="result error">❌ فشل في جلب البيانات: ${data.message}</div>`;
                }
                
            } catch (error) {
                console.error('Error fetching data:', error);
                resultDiv.innerHTML = `<div class="result error">❌ خطأ في جلب البيانات: ${error.message}</div>`;
            }
        }

        async function analyzeItemsData() {
            const resultDiv = document.getElementById('analysis-results');
            
            try {
                // فحص Orders منفصل
                const ordersResponse = await fetch(`${API_BASE}/orders`, { headers });
                const ordersData = await ordersResponse.json();
                
                const orders = ordersData.data || [];
                let ordersWithItems = 0;
                let totalOrderItems = 0;
                
                orders.forEach(order => {
                    if (order.items && order.items.length > 0) {
                        ordersWithItems++;
                        totalOrderItems += order.items.length;
                    }
                });
                
                const analysis = `🔍 تحليل بيانات الأصناف:

في الطلبات العامة:
  - إجمالي الطلبات: ${orders.length}
  - طلبات بها أصناف: ${ordersWithItems}
  - إجمالي الأصناف: ${totalOrderItems}

عينة من طلب عشوائي:
${orders.length > 0 ? JSON.stringify(orders[0], null, 2) : 'لا توجد طلبات'}
`;

                resultDiv.innerHTML += `<div class="result">${analysis}</div>`;
                
            } catch (error) {
                console.error('Error analyzing data:', error);
                resultDiv.innerHTML += `<div class="result error">❌ خطأ في تحليل البيانات: ${error.message}</div>`;
            }
        }

        async function testModalDisplay() {
            const resultDiv = document.getElementById('display-results');
            
            console.log('🔧 اختبار عرض Modal...');
            
            // البحث عن أزرار تفاصيل الخصم
            const detailButtons = document.querySelectorAll('.details-btn');
            const modalElements = document.querySelectorAll('.modal-overlay, .order-details-modal');
            
            const result = `🪟 نتائج فحص Modal:

أزرار التفاصيل الموجودة: ${detailButtons.length}
عناصر Modal في DOM: ${modalElements.length}

للاختبار:
1. اذهب للتطبيق الرئيسي: http://localhost:3000
2. اذهب لشاشة طلبات الخصم
3. انقر على زر "التفاصيل"
4. يجب أن تظهر الأصناف في Modal

CSS Classes المطلوبة:
- .order-items-list ✓
- .order-item ✓
- .item-info ✓
- .items-summary ✓
`;

            console.log(result);
            resultDiv.innerHTML = `<div class="result">${result}</div>`;
        }

        function checkCSSStyles() {
            const resultDiv = document.getElementById('display-results');
            
            // فحص تحميل CSS
            const cssRules = Array.from(document.styleSheets).flatMap(sheet => {
                try {
                    return Array.from(sheet.cssRules || []);
                } catch (e) {
                    return [];
                }
            });
            
            const modalCSS = cssRules.filter(rule => 
                rule.selectorText && (
                    rule.selectorText.includes('order-items-list') ||
                    rule.selectorText.includes('order-item') ||
                    rule.selectorText.includes('items-summary')
                )
            );
            
            const result = `🎨 فحص تنسيق CSS:

إجمالي CSS Rules: ${cssRules.length}
CSS Rules للأصناف: ${modalCSS.length}

CSS Classes المكتشفة:
${modalCSS.map(rule => `- ${rule.selectorText}`).join('\n')}

${modalCSS.length === 0 ? '⚠️ لم يتم العثور على CSS للأصناف' : '✅ تم العثور على CSS للأصناف'}
`;

            resultDiv.innerHTML = `<div class="result">${result}</div>`;
        }

        // تشغيل تلقائي عند التحميل
        window.addEventListener('load', () => {
            console.log('🔧 تم تحميل صفحة اختبار الأصناف');
            if (authToken) {
                fetchDiscountRequestsWithItems();
            }
        });
    </script>
</body>
</html>

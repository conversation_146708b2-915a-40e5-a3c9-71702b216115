// تعريفات TypeScript لـ Socket.IO
import { Socket } from 'socket.io-client';

declare global {
  interface Window {
    socket?: Socket;
  }
}

export interface SocketEvents {
  // أحداث الطلبات
  'order:created': (order: any) => void;
  'order:updated': (order: any) => void;
  'order:completed': (orderId: string) => void;
  'order:cancelled': (orderId: string) => void;
  
  // أحداث المطبخ
  'kitchen:order-received': (order: any) => void;
  'kitchen:order-started': (orderId: string) => void;
  'kitchen:order-ready': (orderId: string) => void;
  
  // أحداث النظام
  'notification': (notification: any) => void;
  'user:connected': (user: any) => void;
  'user:disconnected': (userId: string) => void;
}

export interface OrderData {
  id?: string;
  tableNumber: number;
  items: OrderItem[];
  status: 'pending' | 'confirmed' | 'preparing' | 'ready' | 'completed' | 'cancelled';
  waiterName: string;
  timestamp: string;
  total?: number;
}

export interface OrderItem {
  id: string;
  name: string;
  price: number;
  quantity: number;
  category: string;
  notes?: string;
}
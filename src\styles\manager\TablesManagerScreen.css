/* TablesManagerScreen.css - Scoped styles for Tables Manager Screen */

/* استيراد المتغيرات المميزة لشاشة الطاولات */
@import '../variables/tables-variables.css';

/* Main container */
.tablesManagerScreen {
  padding: 2rem;
  max-width: 1400px;
  margin: 0 auto;
  direction: rtl;
  font-family: var(--tables-font-family-primary);
}

/* Header section */
.tablesManagerScreen__header {
  margin-bottom: 2rem;
  padding: var(--tables-spacing-lg);
  background: var(--tables-bg-gradient);
  border-radius: var(--tables-border-radius);
  box-shadow: 0 8px 25px var(--tables-shadow-color);
  border: 1px solid rgba(255, 255, 255, 0.2);
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex-wrap: wrap;
  gap: 1rem;
}

.tablesManagerScreen__title {
  flex: 1;
}

.tablesManagerScreen__title-main {
  color: var(--tables-primary);
  font-size: 2rem;
  font-weight: 700;
  margin: 0 0 0.5rem 0;
}

.tablesManagerScreen__title-subtitle {
  color: var(--tables-text-primaryLight);
  font-size: 1rem;
  margin: 0;
}

/* Filters and search section */
.tablesManagerScreen__filters {
  display: flex;
  gap: var(--tables-spacing-lg);
  margin-bottom: 2rem;
  flex-wrap: wrap;
  align-items: center;
}

.tablesManagerScreen__filter-group {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
  min-width: 200px;
}

.tablesManagerScreen__filter-label {
  font-size: 0.9rem;
  font-weight: 600;
  color: var(--tables-text-primary);
}

.tablesManagerScreen__filter-input,
.tablesManagerScreen__filter-select {
  padding: 0.75rem 1rem;
  border: 2px solid #e9ecef;
  border-radius: 10px;
  font-size: 1rem;
  transition: border-color 0.2s ease;
  background: white;
}

.tablesManagerScreen__filter-input:focus,
.tablesManagerScreen__filter-select:focus {
  outline: none;
  border-color: var(--tables-accent);
}

/* Tables grid */
.tablesManagerScreen__grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: var(--tables-spacing-lg);
  margin-bottom: 2rem;
}

/* Individual table card */
.tablesManagerScreen__table-card {
  background: var(--tables-bg-primary);
  border-radius: var(--tables-border-radius);
  padding: var(--tables-spacing-lg);
  box-shadow: 0 8px 25px var(--tables-shadow-color);
  border: 2px solid transparent;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.tablesManagerScreen__table-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: var(--tables-accent);
  transform: scaleX(0);
  transition: transform 0.3s ease;
}

.tablesManagerScreen__table-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 15px 35px rgba(0, 0, 0, 0.15);
  border-color: var(--tables-accent);
}

.tablesManagerScreen__table-card:hover::before {
  transform: scaleX(1);
}

/* Table card header */
.tablesManagerScreen__table-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1rem;
  padding-bottom: 0.5rem;
  border-bottom: 1px solid #eee;
}

.tablesManagerScreen__table-number {
  font-size: 1.5rem;
  font-weight: 700;
  color: var(--tables-primary);
  margin: 0;
}

.tablesManagerScreen__table-status {
  padding: 0.25rem 0.75rem;
  border-radius: 20px;
  font-size: 0.85rem;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.tablesManagerScreen__table-status--available {
  background: #d4edda;
  color: #155724;
}

.tablesManagerScreen__table-status--occupied {
  background: #f8d7da;
  color: #721c24;
}

.tablesManagerScreen__table-status--reserved {
  background: #fff3cd;
  color: #856404;
}

.tablesManagerScreen__table-status--maintenance {
  background: #f0f0f0;
  color: #6c757d;
}

/* Table details */
.tablesManagerScreen__table-details {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 1rem;
  margin-bottom: 1rem;
}

.tablesManagerScreen__table-detail {
  text-align: center;
}

.tablesManagerScreen__table-detail-label {
  font-size: 0.85rem;
  color: var(--tables-text-primaryLight);
  margin-bottom: 0.25rem;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.tablesManagerScreen__table-detail-value {
  font-size: 1.1rem;
  font-weight: 600;
  color: var(--tables-text-primary);
}

/* Table action buttons */
.tablesManagerScreen__table-actions {
  display: flex;
  gap: 0.5rem;
  margin-top: 1rem;
}

.tablesManagerScreen__button {
  flex: 1;
  padding: 0.5rem 1rem;
  border: none;
  border-radius: 8px;
  font-size: 0.9rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  text-decoration: none;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
}

.tablesManagerScreen__button--primary {
  background: var(--tables-accent);
  color: white;
}

.tablesManagerScreen__button--primary:hover {
  background: #2980b9;
  transform: translateY(-1px);
}

.tablesManagerScreen__button--success {
  background: var(--tables-success);
  color: white;
}

.tablesManagerScreen__button--success:hover {
  background: #229954;
  transform: translateY(-1px);
}

.tablesManagerScreen__button--warning {
  background: var(--tables-warning);
  color: white;
}

.tablesManagerScreen__button--warning:hover {
  background: #d68910;
  transform: translateY(-1px);
}

.tablesManagerScreen__button--danger {
  background: var(--tables-danger);
  color: white;
}

.tablesManagerScreen__button--danger:hover {
  background: #c0392b;
  transform: translateY(-1px);
}

/* Add table button */
.tablesManagerScreen__add-button {
  position: fixed;
  bottom: 2rem;
  right: 2rem;
  width: 60px;
  height: 60px;
  border-radius: 50%;
  background: var(--tables-accent);
  color: white;
  border: none;
  font-size: 1.5rem;
  cursor: pointer;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
  transition: all 0.3s ease;
  z-index: 100;
}

.tablesManagerScreen__add-button:hover {
  transform: scale(1.1);
  box-shadow: 0 6px 20px rgba(0, 0, 0, 0.3);
}

/* Statistics section */
.tablesManagerScreen__stats {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: var(--tables-spacing-lg);
  margin-bottom: 2rem;
}

.tablesManagerScreen__stat-card {
  background: var(--tables-bg-primary);
  border-radius: var(--tables-border-radius);
  padding: var(--tables-spacing-lg);
  box-shadow: 0 8px 25px var(--tables-shadow-color);
  text-align: center;
  border-left: 4px solid var(--tables-accent);
}

.tablesManagerScreen__stat-value {
  font-size: 2.5rem;
  font-weight: 700;
  color: var(--tables-primary);
  margin-bottom: 0.5rem;
}

.tablesManagerScreen__stat-label {
  font-size: 1rem;
  color: var(--tables-text-primaryLight);
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

/* Responsive design */
@media (max-width: 768px) {
  .tablesManagerScreen {
    padding: 1rem;
  }
  
  .tablesManagerScreen__header {
    flex-direction: column;
    text-align: center;
  }
  
  .tablesManagerScreen__filters {
    flex-direction: column;
    gap: 1rem;
  }
  
  .tablesManagerScreen__filter-group {
    min-width: 100%;
  }
  
  .tablesManagerScreen__grid {
    grid-template-columns: 1fr;
    gap: 1rem;
  }
  
  .tablesManagerScreen__stats {
    grid-template-columns: 1fr;
  }
  
  .tablesManagerScreen__table-details {
    grid-template-columns: 1fr;
    gap: 0.5rem;
  }
  
  .tablesManagerScreen__table-actions {
    flex-direction: column;
  }
}

/* Loading state */
.tablesManagerScreen__loading {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 50vh;
  color: var(--tables-text-primary);
  font-size: 1.2rem;
}

.tablesManagerScreen__spinner {
  width: 40px;
  height: 40px;
  border: 4px solid rgba(52, 152, 219, 0.3);
  border-top: 4px solid var(--tables-accent);
  border-radius: 50%;
  animation: tablesManagerScreen-spin 1s linear infinite;
  margin-right: 1rem;
}

@keyframes tablesManagerScreen-spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Empty state */
.tablesManagerScreen__empty {
  text-align: center;
  color: var(--tables-text-primaryLight);
  padding: 3rem;
}

.tablesManagerScreen__empty-icon {
  font-size: 4rem;
  margin-bottom: 1rem;
  opacity: 0.7;
}

.tablesManagerScreen__empty-message {
  font-size: 1.2rem;
  margin-bottom: 1rem;
}

.tablesManagerScreen__empty-submessage {
  font-size: 1rem;
  opacity: 0.8;
}

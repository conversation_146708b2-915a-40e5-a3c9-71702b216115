<!DOCTYPE html>
<html lang="ar">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>تشخيص مفصل - نظام المقهى</title>
    <style>
        body { 
            font-family: Arial, sans-serif; 
            margin: 20px; 
            direction: rtl; 
            background-color: #f5f5f5;
        }
        .container { max-width: 800px; margin: 0 auto; }
        .status { padding: 15px; margin: 10px 0; border-radius: 8px; border: 2px solid; }
        .connected { background-color: #d4edda; color: #155724; border-color: #c3e6cb; }
        .disconnected { background-color: #f8d7da; color: #721c24; border-color: #f5c6cb; }
        .loading { background-color: #fff3cd; color: #856404; border-color: #ffeaa7; }
        .warning { background-color: #ffeaa7; color: #856404; border-color: #fdd835; }
        .info { background-color: #cce5ff; color: #0056b3; border-color: #b3d9ff; }
        .details { font-size: 12px; margin-top: 10px; padding: 10px; background: rgba(0,0,0,0.1); border-radius: 4px; }
        button { padding: 10px 20px; margin: 5px; border: none; border-radius: 5px; cursor: pointer; }
        .btn-primary { background-color: #007bff; color: white; }
        .btn-success { background-color: #28a745; color: white; }
        .btn-danger { background-color: #dc3545; color: white; }
        h1 { text-align: center; color: #333; }
        h2 { color: #555; border-bottom: 2px solid #ddd; padding-bottom: 10px; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔍 تشخيص مفصل - نظام إدارة المقهى</h1>
        
        <h2>🌐 حالة الاتصالات</h2>
        
        <div id="frontend-status" class="status info">
            ✅ الواجهة الأمامية: متصلة
            <div class="details">
                المنفذ: 3000<br>
                البروتوكول: HTTP<br>
                العنوان: localhost
            </div>
        </div>
        
        <div id="backend-status" class="status loading">
            🔄 جاري فحص الخادم الخلفي...
        </div>
        
        <div id="api-status" class="status loading">
            🔄 جاري فحص API...
        </div>
        
        <div id="database-status" class="status loading">
            🔄 جاري فحص قاعدة البيانات...
        </div>
        
        <div id="socket-status" class="status loading">
            🔄 جاري فحص Socket.IO...
        </div>

        <h2>🛠️ أدوات الاختبار</h2>
        <button class="btn-primary" onclick="testBackend()">🔄 إعادة فحص الخادم</button>
        <button class="btn-success" onclick="testSpecificAPI()">🧪 اختبار API محدد</button>
        <button class="btn-danger" onclick="clearCache()">🗑️ مسح الكاش</button>

        <h2>📊 تفاصيل الشبكة</h2>
        <div id="network-info" class="status info">
            <strong>معلومات الشبكة:</strong><br>
            البروتوكول: <span id="protocol"></span><br>
            المضيف: <span id="host"></span><br>
            المنفذ: <span id="port"></span><br>
            وكيل المستخدم: <span id="userAgent"></span>
        </div>

        <h2>📝 سجل الأخطاء</h2>
        <div id="error-log" class="status" style="min-height: 100px; background-color: #f8f9fa; color: #333; font-family: monospace; font-size: 11px; white-space: pre-wrap;">
        </div>
    </div>

    <script>
        let errorLog = document.getElementById('error-log');
        let logMessages = [];

        function log(message) {
            const timestamp = new Date().toLocaleString('ar-EG');
            const logMessage = `[${timestamp}] ${message}`;
            logMessages.push(logMessage);
            errorLog.textContent = logMessages.join('\n');
            console.log(logMessage);
        }

        // معلومات الشبكة الأساسية
        document.getElementById('protocol').textContent = window.location.protocol;
        document.getElementById('host').textContent = window.location.hostname;
        document.getElementById('port').textContent = window.location.port || 'افتراضي';
        document.getElementById('userAgent').textContent = navigator.userAgent.substring(0, 100) + '...';

        // فحص الخادم الخلفي
        async function testBackend() {
            const backendStatus = document.getElementById('backend-status');
            backendStatus.className = 'status loading';
            backendStatus.innerHTML = '🔄 جاري فحص الخادم الخلفي...';
            
            log('بدء فحص الخادم الخلفي...');
            
            try {
                const response = await fetch('http://localhost:5000/health', {
                    method: 'GET',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    timeout: 10000
                });
                
                log(`استجابة الخادم: HTTP ${response.status} ${response.statusText}`);
                
                if (response.ok) {
                    const data = await response.json();
                    log(`بيانات الاستجابة: ${JSON.stringify(data, null, 2)}`);
                    
                    backendStatus.className = 'status connected';
                    backendStatus.innerHTML = `
                        ✅ الخادم الخلفي: متصل
                        <div class="details">
                            الحالة: ${data.status}<br>
                            المنفذ: 5000<br>
                            الذاكرة المستخدمة: ${Math.round(data.memory?.heapUsed / 1024 / 1024)} MB<br>
                            وقت التشغيل: ${Math.round(data.uptime)} ثانية
                        </div>
                    `;
                    
                    // فحص قاعدة البيانات
                    testDatabase(data);
                } else {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }
            } catch (error) {
                log(`خطأ في الاتصال بالخادم: ${error.message}`);
                backendStatus.className = 'status disconnected';
                backendStatus.innerHTML = `
                    ❌ الخادم الخلفي: غير متصل
                    <div class="details">
                        الخطأ: ${error.message}<br>
                        تأكد من تشغيل الخادم على المنفذ 5000
                    </div>
                `;
                
                // تحديث حالة قاعدة البيانات
                const databaseStatus = document.getElementById('database-status');
                databaseStatus.className = 'status disconnected';
                databaseStatus.innerHTML = '❌ قاعدة البيانات: غير متصلة (الخادم غير متاح)';
            }
        }

        function testDatabase(healthData) {
            const databaseStatus = document.getElementById('database-status');
            
            if (healthData.status === 'healthy') {
                log('قاعدة البيانات متاحة');
                databaseStatus.className = 'status connected';
                databaseStatus.innerHTML = `
                    ✅ قاعدة البيانات: متصلة
                    <div class="details">
                        النوع: MongoDB Atlas<br>
                        الحالة: نشطة<br>
                        آخر فحص: ${new Date().toLocaleString('ar-EG')}
                    </div>
                `;
            } else {
                log('مشكلة في قاعدة البيانات');
                databaseStatus.className = 'status warning';
                databaseStatus.innerHTML = '⚠️ قاعدة البيانات: حالة غير واضحة';
            }
        }

        // فحص APIs محددة
        async function testSpecificAPI() {
            log('فحص APIs محددة...');
            const apiStatus = document.getElementById('api-status');
            apiStatus.className = 'status loading';
            apiStatus.innerHTML = '🔄 جاري فحص APIs...';
            
            try {
                const endpoints = [
                    '/api/v1/products',
                    '/api/v1/orders',
                    '/api/v1/tables',
                    '/api/v1/users'
                ];
                
                let workingAPIs = 0;
                
                for (const endpoint of endpoints) {
                    try {
                        const response = await fetch(`http://localhost:5000${endpoint}`, {
                            method: 'GET',
                            headers: { 'Content-Type': 'application/json' }
                        });
                        
                        if (response.status === 200 || response.status === 401) {
                            workingAPIs++;
                            log(`✅ ${endpoint}: يعمل`);
                        } else {
                            log(`⚠️ ${endpoint}: HTTP ${response.status}`);
                        }
                    } catch (error) {
                        log(`❌ ${endpoint}: ${error.message}`);
                    }
                }
                
                if (workingAPIs > 0) {
                    apiStatus.className = 'status connected';
                    apiStatus.innerHTML = `
                        ✅ APIs: يعمل (${workingAPIs}/${endpoints.length} متاح)
                        <div class="details">
                            APIs المتاحة: ${workingAPIs}<br>
                            إجمالي APIs: ${endpoints.length}
                        </div>
                    `;
                } else {
                    apiStatus.className = 'status disconnected';
                    apiStatus.innerHTML = '❌ APIs: غير متاح';
                }
                
            } catch (error) {
                log(`خطأ في فحص APIs: ${error.message}`);
                apiStatus.className = 'status disconnected';
                apiStatus.innerHTML = '❌ APIs: خطأ في الفحص';
            }
        }

        // فحص Socket.IO
        async function testSocket() {
            const socketStatus = document.getElementById('socket-status');
            socketStatus.className = 'status loading';
            socketStatus.innerHTML = '🔄 جاري فحص Socket.IO...';
            
            log('محاولة الاتصال بـ Socket.IO...');
            
            try {
                // محاولة اتصال بسيطة
                const response = await fetch('http://localhost:5000/socket.io/', {
                    method: 'GET'
                });
                
                if (response.ok || response.status === 400) {
                    log('خادم Socket.IO متاح');
                    socketStatus.className = 'status connected';
                    socketStatus.innerHTML = `
                        ✅ Socket.IO: متاح
                        <div class="details">
                            المنفذ: 5000<br>
                            البروتوكول: HTTP<br>
                            الحالة: جاهز للاتصال
                        </div>
                    `;
                } else {
                    throw new Error(`HTTP ${response.status}`);
                }
            } catch (error) {
                log(`خطأ في Socket.IO: ${error.message}`);
                socketStatus.className = 'status warning';
                socketStatus.innerHTML = `
                    ⚠️ Socket.IO: غير متاح
                    <div class="details">
                        قد يؤثر على التحديثات المباشرة
                    </div>
                `;
            }
        }

        function clearCache() {
            log('مسح الكاش...');
            if ('caches' in window) {
                caches.keys().then(names => {
                    names.forEach(name => {
                        caches.delete(name);
                    });
                });
            }
            location.reload(true);
        }

        // بدء الفحوصات التلقائية
        window.onload = function() {
            log('بدء التشخيص التلقائي...');
            testBackend();
            setTimeout(testSpecificAPI, 2000);
            setTimeout(testSocket, 4000);
        };
    </script>
</body>
</html>

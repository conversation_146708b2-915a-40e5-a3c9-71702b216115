/* ============================== */
/* ReportsManagerScreen - Reports and Analytics Interface */
/* ============================== */

/* ??????? ????????? ??????? ????? ???????? */
@import '../variables/reports-variables.css';

/* ================ */
/* Screen Container */
/* ================ */

.reportsManagerScreen {
  padding: 2rem;
  max-width: 1400px;
  margin: 0 auto;
  direction: rtl;
  background: var(--reports-light-bg);
  min-height: 100vh;
}

/* ================ */
/* Screen Header */
/* ================ */

.reportsManagerScreen__header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 2rem;
  padding-bottom: 1rem;
  border-bottom: 2px solid #f1f2f6;
  flex-wrap: wrap;
  gap: 1rem;
}

.reportsManagerScreen__title-section {
  flex: 1;
}

.reportsManagerScreen__title {
  color: var(--reports-primary-color);
  font-size: 2rem;
  font-weight: 700;
  margin: 0 0 0.5rem 0;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.reportsManagerScreen__title-icon {
  color: var(--reports-secondary-color);
  font-size: 1.8rem;
}

.reportsManagerScreen__subtitle {
  color: #7f8c8d;
  font-size: 1rem;
  margin: 0;
}

.reportsManagerScreen__date-range {
  display: flex;
  align-items: center;
  gap: 1rem;
  background: var(--reports-white);
  padding: 1rem;
  border-radius: var(--reports-border-radius);
  box-shadow: var(--reports-box-shadow);
}

.reportsManagerScreen__date-input {
  padding: 0.5rem;
  border: 2px solid #e1e8ed;
  border-radius: 6px;
  font-size: 0.9rem;
  text-align: right;
  direction: rtl;
}

.reportsManagerScreen__date-input:focus {
  outline: none;
  border-color: var(--reports-secondary-color);
}

/* ================ */
/* Summary Statistics */
/* ================ */

.reportsManagerScreen__summary {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 1.5rem;
  margin-bottom: 2rem;
}

.reportsManagerScreen__summary-card {
  background: var(--reports-white);
  padding: 2rem;
  border-radius: var(--reports-border-radius);
  box-shadow: var(--reports-box-shadow);
  text-align: center;
  border: 1px solid #f1f2f6;
  transition: var(--reports-transition);
  position: relative;
  overflow: hidden;
}

.reportsManagerScreen__summary-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
}

.reportsManagerScreen__summary-card--sales::before {
  background: var(--reports-success-color);
}

.reportsManagerScreen__summary-card--orders::before {
  background: var(--reports-secondary-color);
}

.reportsManagerScreen__summary-card--customers::before {
  background: var(--reports-purple-color);
}

.reportsManagerScreen__summary-card--products::before {
  background: var(--reports-warning-color);
}

.reportsManagerScreen__summary-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

.reportsManagerScreen__summary-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 60px;
  height: 60px;
  border-radius: 50%;
  margin: 0 auto 1rem auto;
  font-size: 1.5rem;
  color: var(--reports-white);
}

.reportsManagerScreen__summary-card--sales .reportsManagerScreen__summary-icon {
  background: linear-gradient(135deg, var(--reports-success-color), #1e8449);
}

.reportsManagerScreen__summary-card--orders .reportsManagerScreen__summary-icon {
  background: linear-gradient(135deg, var(--reports-secondary-color), #2980b9);
}

.reportsManagerScreen__summary-card--customers .reportsManagerScreen__summary-icon {
  background: linear-gradient(135deg, var(--reports-purple-color), #8e44ad);
}

.reportsManagerScreen__summary-card--products .reportsManagerScreen__summary-icon {
  background: linear-gradient(135deg, var(--reports-warning-color), #d68910);
}

.reportsManagerScreen__summary-number {
  font-size: 2.5rem;
  font-weight: 700;
  color: var(--reports-primary-color);
  margin: 0 0 0.5rem 0;
  line-height: 1;
}

.reportsManagerScreen__summary-label {
  color: #7f8c8d;
  font-size: 1rem;
  margin: 0;
  font-weight: 500;
}

.reportsManagerScreen__summary-change {
  margin-top: 0.5rem;
  font-size: 0.85rem;
  font-weight: 600;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.25rem;
}

.reportsManagerScreen__summary-change--positive {
  color: var(--reports-success-color);
}

.reportsManagerScreen__summary-change--negative {
  color: var(--reports-danger-color);
}

/* ================ */
/* Charts Section */
/* ================ */

.reportsManagerScreen__charts {
  display: grid;
  grid-template-columns: 2fr 1fr;
  gap: 2rem;
  margin-bottom: 2rem;
}

.reportsManagerScreen__chart-card {
  background: var(--reports-white);
  padding: 1.5rem;
  border-radius: var(--reports-border-radius);
  box-shadow: var(--reports-box-shadow);
  border: 1px solid #f1f2f6;
}

.reportsManagerScreen__chart-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1.5rem;
  padding-bottom: 1rem;
  border-bottom: 1px solid #f1f2f6;
}

.reportsManagerScreen__chart-title {
  font-size: 1.2rem;
  font-weight: 700;
  color: var(--reports-primary-color);
  margin: 0;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.reportsManagerScreen__chart-options {
  display: flex;
  gap: 0.5rem;
}

.reportsManagerScreen__chart-option-btn {
  padding: 0.5rem 1rem;
  border: 2px solid #e1e8ed;
  background: var(--reports-white);
  color: var(--reports-primary-color);
  border-radius: 6px;
  font-size: 0.8rem;
  font-weight: 500;
  cursor: pointer;
  transition: var(--reports-transition);
}

.reportsManagerScreen__chart-option-btn:hover {
  border-color: var(--reports-secondary-color);
  background: var(--reports-light-bg);
}

.reportsManagerScreen__chart-option-btn--active {
  background: var(--reports-secondary-color);
  color: var(--reports-white);
  border-color: var(--reports-secondary-color);
}

.reportsManagerScreen__chart-container {
  height: 300px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: var(--reports-light-bg);
  border-radius: 8px;
  color: #7f8c8d;
}

/* ================ */
/* Popular Products */
/* ================ */

.reportsManagerScreen__popular-products {
  background: var(--reports-white);
  padding: 1.5rem;
  border-radius: var(--reports-border-radius);
  box-shadow: var(--reports-box-shadow);
  margin-bottom: 2rem;
  border: 1px solid #f1f2f6;
}

.reportsManagerScreen__popular-products-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1.5rem;
  padding-bottom: 1rem;
  border-bottom: 1px solid #f1f2f6;
}

.reportsManagerScreen__popular-products-title {
  font-size: 1.2rem;
  font-weight: 700;
  color: var(--reports-primary-color);
  margin: 0;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.reportsManagerScreen__products-list {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.reportsManagerScreen__product-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 1rem;
  background: var(--reports-light-bg);
  border-radius: 8px;
  transition: var(--reports-transition);
}

.reportsManagerScreen__product-item:hover {
  background: #e9ecef;
  transform: translateX(-5px);
}

.reportsManagerScreen__product-rank {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 30px;
  height: 30px;
  border-radius: 50%;
  font-weight: 700;
  font-size: 0.9rem;
  color: var(--reports-white);
  margin-left: 1rem;
}

.reportsManagerScreen__product-rank--1 {
  background: #ffd700;
  color: var(--reports-primary-color);
}

.reportsManagerScreen__product-rank--2 {
  background: #c0c0c0;
  color: var(--reports-primary-color);
}

.reportsManagerScreen__product-rank--3 {
  background: #cd7f32;
  color: var(--reports-white);
}

.reportsManagerScreen__product-rank--other {
  background: var(--reports-secondary-color);
}

.reportsManagerScreen__product-info {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
}

.reportsManagerScreen__product-name {
  font-weight: 600;
  color: var(--reports-primary-color);
  font-size: 1rem;
}

.reportsManagerScreen__product-category {
  color: #7f8c8d;
  font-size: 0.85rem;
}

.reportsManagerScreen__product-stats {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
  gap: 0.25rem;
}

.reportsManagerScreen__product-quantity {
  font-weight: 700;
  color: var(--reports-secondary-color);
  font-size: 1.1rem;
}

.reportsManagerScreen__product-revenue {
  color: var(--reports-success-color);
  font-size: 0.9rem;
  font-weight: 600;
}

/* ================ */
/* Time Period Filters */
/* ================ */

.reportsManagerScreen__time-filters {
  background: var(--reports-white);
  padding: 1rem;
  border-radius: var(--reports-border-radius);
  box-shadow: var(--reports-box-shadow);
  margin-bottom: 2rem;
  display: flex;
  align-items: center;
  gap: 1rem;
  flex-wrap: wrap;
}

.reportsManagerScreen__time-filter-label {
  font-weight: 600;
  color: var(--reports-primary-color);
  font-size: 0.95rem;
}

.reportsManagerScreen__time-filter-buttons {
  display: flex;
  gap: 0.5rem;
  flex-wrap: wrap;
}

.reportsManagerScreen__time-filter-btn {
  padding: 0.5rem 1rem;
  border: 2px solid #e1e8ed;
  background: var(--reports-white);
  color: var(--reports-primary-color);
  border-radius: 20px;
  font-size: 0.85rem;
  font-weight: 500;
  cursor: pointer;
  transition: var(--reports-transition);
}

.reportsManagerScreen__time-filter-btn:hover {
  border-color: var(--reports-secondary-color);
  background: var(--reports-light-bg);
}

.reportsManagerScreen__time-filter-btn--active {
  background: var(--reports-secondary-color);
  color: var(--reports-white);
  border-color: var(--reports-secondary-color);
}

/* ================ */
/* Export Actions */
/* ================ */

.reportsManagerScreen__export-actions {
  background: var(--reports-white);
  padding: 1rem;
  border-radius: var(--reports-border-radius);
  box-shadow: var(--reports-box-shadow);
  margin-bottom: 2rem;
  display: flex;
  align-items: center;
  justify-content: space-between;
  flex-wrap: wrap;
  gap: 1rem;
}

.reportsManagerScreen__export-title {
  font-weight: 600;
  color: var(--reports-primary-color);
  font-size: 1rem;
  margin: 0;
}

.reportsManagerScreen__export-buttons {
  display: flex;
  gap: 0.5rem;
  flex-wrap: wrap;
}

.reportsManagerScreen__export-btn {
  padding: 0.75rem 1.5rem;
  border: none;
  border-radius: 8px;
  font-size: 0.9rem;
  font-weight: 600;
  cursor: pointer;
  transition: var(--reports-transition);
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.reportsManagerScreen__export-btn--pdf {
  background: var(--reports-danger-color);
  color: var(--reports-white);
}

.reportsManagerScreen__export-btn--pdf:hover {
  background: #c0392b;
  transform: translateY(-1px);
}

.reportsManagerScreen__export-btn--excel {
  background: var(--reports-success-color);
  color: var(--reports-white);
}

.reportsManagerScreen__export-btn--excel:hover {
  background: #229954;
  transform: translateY(-1px);
}

.reportsManagerScreen__export-btn--print {
  background: var(--reports-secondary-color);
  color: var(--reports-white);
}

.reportsManagerScreen__export-btn--print:hover {
  background: #2980b9;
  transform: translateY(-1px);
}

/* ================ */
/* Waiter Performance */
/* ================ */

.reportsManagerScreen__waiter-performance {
  background: var(--reports-white);
  padding: 1.5rem;
  border-radius: var(--reports-border-radius);
  box-shadow: var(--reports-box-shadow);
  margin-bottom: 2rem;
  border: 1px solid #f1f2f6;
}

.reportsManagerScreen__waiter-performance-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1.5rem;
  padding-bottom: 1rem;
  border-bottom: 1px solid #f1f2f6;
}

.reportsManagerScreen__waiter-performance-title {
  font-size: 1.2rem;
  font-weight: 700;
  color: var(--reports-primary-color);
  margin: 0;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.reportsManagerScreen__waiter-list {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 1rem;
}

.reportsManagerScreen__waiter-card {
  background: var(--reports-light-bg);
  padding: 1.5rem;
  border-radius: 8px;
  transition: var(--reports-transition);
}

.reportsManagerScreen__waiter-card:hover {
  background: #e9ecef;
  transform: translateY(-2px);
}

.reportsManagerScreen__waiter-name {
  font-size: 1.1rem;
  font-weight: 700;
  color: var(--reports-primary-color);
  margin: 0 0 1rem 0;
  text-align: center;
}

.reportsManagerScreen__waiter-stats {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 0.75rem;
}

.reportsManagerScreen__waiter-stat {
  text-align: center;
  padding: 0.75rem;
  background: var(--reports-white);
  border-radius: 6px;
}

.reportsManagerScreen__waiter-stat-number {
  font-size: 1.3rem;
  font-weight: 700;
  color: var(--reports-secondary-color);
  margin: 0;
}

.reportsManagerScreen__waiter-stat-label {
  font-size: 0.8rem;
  color: #7f8c8d;
  margin: 0;
}

/* ================ */
/* Loading State */
/* ================ */

.reportsManagerScreen__loading {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 3rem;
  color: var(--reports-secondary-color);
}

.reportsManagerScreen__loading-spinner {
  font-size: 2rem;
  margin-bottom: 1rem;
  animation: reportsManagerScreen-spin 1s linear infinite;
}

@keyframes reportsManagerScreen-spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.reportsManagerScreen__loading-text {
  font-size: 1.1rem;
  font-weight: 500;
}

/* ================ */
/* Responsive Design */
/* ================ */

@media (max-width: 768px) {
  .reportsManagerScreen {
    padding: 1rem;
  }
  
  .reportsManagerScreen__header {
    flex-direction: column;
    align-items: stretch;
  }
  
  .reportsManagerScreen__title {
    font-size: 1.5rem;
    text-align: center;
  }
  
  .reportsManagerScreen__date-range {
    flex-direction: column;
    gap: 0.5rem;
  }
  
  .reportsManagerScreen__summary {
    grid-template-columns: repeat(2, 1fr);
  }
  
  .reportsManagerScreen__charts {
    grid-template-columns: 1fr;
  }
  
  .reportsManagerScreen__time-filters {
    flex-direction: column;
    align-items: stretch;
  }
  
  .reportsManagerScreen__time-filter-buttons {
    justify-content: center;
  }
  
  .reportsManagerScreen__export-actions {
    flex-direction: column;
    align-items: stretch;
  }
  
  .reportsManagerScreen__export-buttons {
    justify-content: center;
  }
  
  .reportsManagerScreen__waiter-list {
    grid-template-columns: 1fr;
  }
}

@media (max-width: 480px) {
  .reportsManagerScreen__summary {
    grid-template-columns: 1fr;
  }
  
  .reportsManagerScreen__product-item {
    flex-direction: column;
    text-align: center;
    gap: 0.5rem;
  }
  
  .reportsManagerScreen__product-rank {
    margin: 0;
  }
  
  .reportsManagerScreen__product-stats {
    align-items: center;
  }
  
  .reportsManagerScreen__waiter-stats {
    grid-template-columns: 1fr;
  }
}


// إصلاح مشكلة عدم ظهور الطلبات في الطاولات
// تحديث دالة fetchTableAccounts في WaiterDashboard.tsx

// دالة محسنة لجلب الطاولات مع الطلبات
const fetchTableAccountsFixed = useCallback(async (forceRefresh = false) => {
  console.log('🔧 إصلاح: جلب الطاولات مع الطلبات...');

  if (tableAccountsFetching.current) {
    console.log('⏳ طلب آخر قيد التنفيذ للطاولات');
    return;
  }

  // الحصول على معرف النادل الحالي
  const userData = JSON.parse(localStorage.getItem('user') || '{}');
  const storedWaiterId = localStorage.getItem('waiterId');
  const waiterId = storedWaiterId || userData._id || userData.id;
  const currentWaiterUsername = localStorage.getItem('username');

  console.log('👤 معلومات النادل:', { 
    waiterId, 
    username: currentWaiterUsername,
    userData 
  });

  if (!waiterId && !currentWaiterUsername) {
    console.error('❌ لا يمكن تحديد معرف النادل');
    setTableAccounts([]);
    return;
  }

  try {
    tableAccountsFetching.current = true;
    
    await new Promise(resolve => setTimeout(resolve, 50));
    
    // الخطوة 1: جلب الطلبات أولاً
    console.log('📋 جلب الطلبات لإنشاء الطاولات...');
    
    let allOrders = [];
    try {
      const ordersResponse = await authenticatedGet('/api/v1/orders');
      const ordersData = ordersResponse?.data || ordersResponse;
      
      if (Array.isArray(ordersData)) {
        // فلترة طلبات النادل الحالي
        allOrders = ordersData.filter(order => {
          const isCurrentWaiterByid = order.waiterId === waiterId;
          const isCurrentWaiterByName = order.waiterName === currentWaiterUsername;
          const isCurrentWaiterByName2 = order.waiterName === 'بوسي';
          
          return isCurrentWaiterByid || isCurrentWaiterByName || isCurrentWaiterByName2;
        });
        
        console.log(`📊 تم جلب ${ordersData.length} طلب، فلترة ${allOrders.length} طلب للنادل`);
      }
    } catch (ordersError) {
      console.error('❌ خطأ في جلب الطلبات:', ordersError);
    }

    // الخطوة 2: محاولة جلب الطاولات من API
    let tableAccountsData = [];
    
    try {
      const apiUrl = waiterId ? 
        `/api/v1/table-accounts?waiterId=${waiterId}` : 
        '/api/v1/table-accounts';
      
      console.log('🔗 جلب الطاولات من:', apiUrl);
      
      const response = await authenticatedGet(apiUrl);
      console.log('📊 استجابة API للطاولات:', response);

      if (Array.isArray(response)) {
        tableAccountsData = response;
      } else if (response.success && Array.isArray(response.data)) {
        tableAccountsData = response.data;
      } else if (response.data && Array.isArray(response.data)) {
        tableAccountsData = response.data;
      }
      
      console.log(`📈 تم جلب ${tableAccountsData.length} طاولة من API`);
    } catch (tableError) {
      console.log('⚠️ لم يتم جلب الطاولات من API، سيتم إنشاؤها من الطلبات');
    }

    // الخطوة 3: إنشاء الطاولات من الطلبات إذا لم توجد
    if (tableAccountsData.length === 0 && allOrders.length > 0) {
      console.log('🔄 إنشاء الطاولات من الطلبات...');
      
      // تجميع الطلبات حسب الطاولة
      const tablesFromOrders = new Map();
      
      allOrders.forEach(order => {
        if (order.tableNumber) {
          const tableNum = String(order.tableNumber);
          
          if (!tablesFromOrders.has(tableNum)) {
            tablesFromOrders.set(tableNum, {
              _id: `generated-table-${tableNum}-${waiterId || currentWaiterUsername}`,
              tableNumber: parseInt(order.tableNumber) || order.tableNumber,
              waiterName: order.waiterName || currentWaiterUsername,
              waiterId: order.waiterId || waiterId,
              customerName: order.customerName || 'عميل',
              status: 'active',
              isOpen: true,
              orders: [],
              totalAmount: 0,
              createdAt: order.createdAt,
              updatedAt: order.createdAt,
              isGenerated: true // علامة للطاولات المُنشأة
            });
          }
          
          const table = tablesFromOrders.get(tableNum);
          table.orders.push(order);
          table.totalAmount += order.totalPrice || 0;
          
          // تحديث آخر وقت تحديث
          if (new Date(order.createdAt) > new Date(table.updatedAt)) {
            table.updatedAt = order.createdAt;
          }
        }
      });
      
      tableAccountsData = Array.from(tablesFromOrders.values());
      console.log(`✅ تم إنشاء ${tableAccountsData.length} طاولة من الطلبات`);
    }

    // الخطوة 4: ربط الطلبات بالطاولات الموجودة
    const enrichedTableAccounts = tableAccountsData.map(account => {
      try {
        // البحث عن طلبات هذه الطاولة
        const tableOrders = allOrders.filter(order => {
          const orderTableStr = String(order.tableNumber || '').trim();
          const accountTableStr = String(account.tableNumber || '').trim();
          const tableMatch = orderTableStr === accountTableStr;

          // مطابقة النادل
          let waiterMatch = false;
          if (waiterId && order.waiterId) {
            waiterMatch = order.waiterId === waiterId;
          } else if (currentWaiterUsername && order.waiterName) {
            waiterMatch = order.waiterName === currentWaiterUsername;
          } else if (order.waiterName === 'بوسي') {
            waiterMatch = true;
          }

          const finalMatch = tableMatch && waiterMatch;
          
          if (finalMatch) {
            console.log(`✅ ربط طلب ${order._id?.slice(-6)} بطاولة ${account.tableNumber}`);
          }

          return finalMatch;
        });

        // حساب المبلغ الإجمالي
        const calculatedTotal = tableOrders.reduce((sum, order) => {
          return sum + (order.totalPrice || 0);
        }, 0);

        console.log(`🏓 طاولة ${account.tableNumber}: ${tableOrders.length} طلب، ${calculatedTotal.toFixed(2)} جنيه`);

        return {
          ...account,
          orders: tableOrders,
          totalAmount: calculatedTotal > 0 ? calculatedTotal : account.totalAmount || 0,
          ordersCount: tableOrders.length,
          lastOrderTime: tableOrders.length > 0 ? 
            Math.max(...tableOrders.map(o => new Date(o.createdAt).getTime())) : 
            new Date(account.updatedAt || Date.now()).getTime()
        };
      } catch (error) {
        console.error(`❌ خطأ في معالجة طاولة ${account.tableNumber}:`, error);
        return {
          ...account,
          orders: [],
          totalAmount: 0,
          ordersCount: 0
        };
      }
    });

    // الخطوة 5: فلترة الطاولات النشطة للنادل الحالي
    const currentWaiterTables = enrichedTableAccounts.filter(account => {
      const isCorrectWaiter = (
        (waiterId && account.waiterId === waiterId) ||
        (currentWaiterUsername && account.waiterName === currentWaiterUsername) ||
        (account.waiter?.username === currentWaiterUsername) ||
        (account.waiter?.id === waiterId) ||
        account.waiterName === 'بوسي'
      );
      
      const isOpenTable = account.isOpen === true && account.status === 'active';
      
      return isCorrectWaiter && isOpenTable;
    });

    console.log(`🎯 النتيجة النهائية: ${currentWaiterTables.length} طاولة نشطة`);
    
    // عرض ملخص للطاولات
    currentWaiterTables.forEach(table => {
      console.log(`📋 طاولة ${table.tableNumber}: ${table.ordersCount} طلب، ${table.totalAmount?.toFixed(2)} جنيه`);
    });

    setTableAccounts(currentWaiterTables);
    setLastFetch(prev => ({ ...prev, tableAccounts: Date.now() }));

  } catch (error) {
    console.error('❌ خطأ في جلب الطاولات:', error);
    handleAPIError(error, 'جلب الطاولات');
    setTableAccounts([]);
  } finally {
    tableAccountsFetching.current = false;
  }
}, []);

// إرشادات التطبيق:
// 1. استبدل دالة fetchTableAccounts الموجودة بهذه الدالة
// 2. تأكد من أن orders متوفرة في النطاق
// 3. تأكد من وجود authenticatedGet function
// 4. تأكد من وجود handleAPIError function

console.log('🔧 تم إنشاء دالة الإصلاح - يرجى تطبيقها في WaiterDashboard.tsx');

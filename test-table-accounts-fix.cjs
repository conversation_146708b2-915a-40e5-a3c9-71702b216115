const mongoose = require('mongoose');
require('./backend/config/database');

async function testTableAccountsAPI() {
  try {
    console.log('🔧 اختبار API لحسابات الطاولات...');

    const Order = require('./backend/models/Order');
    const Table = require('./backend/models/Table');

    // إنشاء بيانات اختبار
    console.log('📊 إحصائيات قاعدة البيانات:');
    
    const tablesCount = await Table.countDocuments({});
    const ordersCount = await Order.countDocuments({});
    
    console.log(`- عدد الطاولات: ${tablesCount}`);
    console.log(`- عدد الطلبات: ${ordersCount}`);

    // اختبار العلاقة بين الطلبات والطاولات
    const ordersWithTable = await Order.find({ table: { $exists: true } })
      .populate('table', 'number')
      .limit(5);
    
    console.log('\n📋 عينة من الطلبات مع بيانات الطاولة:');
    ordersWithTable.forEach((order, index) => {
      console.log(`${index + 1}. طلب ${order.orderNumber}:`);
      console.log(`   - رقم الطاولة: ${order.table?.number || 'غير محدد'}`);
      console.log(`   - pricing.total: ${order.pricing?.total || 0}`);
      console.log(`   - totalPrice: ${order.totalPrice || 0}`);
      console.log(`   - الحالة: ${order.status}`);
    });

    // اختبار تجميع البيانات حسب الطاولة
    const tableStats = await Order.aggregate([
      { $match: { table: { $exists: true } } },
      { $lookup: {
          from: 'tables',
          localField: 'table',
          foreignField: '_id',
          as: 'tableInfo'
      } },
      { $unwind: '$tableInfo' },
      { $group: {
          _id: '$tableInfo.number',
          totalOrders: { $sum: 1 },
          totalSales: { $sum: { $ifNull: ['$pricing.total', '$totalPrice'] } },
          activeOrders: {
            $sum: {
              $cond: {
                if: { $in: ['$status', ['pending', 'preparing', 'ready']] },
                then: 1,
                else: 0
              }
            }
          },
          activeSales: {
            $sum: {
              $cond: {
                if: { $in: ['$status', ['pending', 'preparing', 'ready']] },
                then: { $ifNull: ['$pricing.total', '$totalPrice'] },
                else: 0
              }
            }
          }
      } },
      { $sort: { _id: 1 } }
    ]);

    console.log('\n📈 إحصائيات الطاولات:');
    tableStats.forEach(stat => {
      console.log(`طاولة ${stat._id}:`);
      console.log(`  - إجمالي الطلبات: ${stat.totalOrders}`);
      console.log(`  - إجمالي المبيعات: ${stat.totalSales.toFixed(2)} ج.م`);
      console.log(`  - الطلبات النشطة: ${stat.activeOrders}`);
      console.log(`  - المبيعات النشطة: ${stat.activeSales.toFixed(2)} ج.م`);
    });

    process.exit(0);
  } catch (error) {
    console.error('❌ خطأ في اختبار API:', error);
    process.exit(1);
  }
}

testTableAccountsAPI();

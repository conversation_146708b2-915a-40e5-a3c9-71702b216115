/* ====================================
   CSS منفصل لشاشة التقارير
   Reports Screen - Isolated CSS
   ==================================== */

/* استيراد المتغيرات المميزة لشاشة التقارير */
@import '../variables/reports-variables.css';

/* تم إزالة المتغيرات المحلية - سيتم استخدام المتغيرات من ملف reports-variables.css فقط */

/* Reports Screen Container - معزول تماماً */
.reports-screen-container {
  width: 100%;
  min-height: calc(100vh - 80px);
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  padding: 0;
  margin: 0;
  position: relative;
  overflow: hidden;
}

.reports-screen-container::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grid" width="10" height="10" patternUnits="userSpaceOnUse"><path d="M 10 0 L 0 0 0 10" fill="none" stroke="white" stroke-width="0.5" opacity="0.1"/></pattern></defs><rect width="100" height="100" fill="url(%23grid)"/></svg>');
  pointer-events: none;
}

/* Header للتقارير */
.reports-screen-header {
  padding: 3rem 2rem;
  color: var(--reports-white);
  text-align: center;
  position: relative;
  z-index: 1;
}

.reports-screen-title {
  font-size: 3rem;
  font-weight: 900;
  margin: 0 0 1rem 0;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 1rem;
  text-shadow: 0 4px 8px rgba(0, 0, 0, 0.3);
}

.reports-screen-title-icon {
  color: var(--reports-warning);
  text-shadow: 0 0 30px rgba(255, 128, 8, 0.6);
}

.reports-screen-subtitle {
  font-size: 1.4rem;
  opacity: 0.9;
  margin: 0 0 2rem 0;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

/* Filter Section */
.reports-screen-filter {
  display: flex;
  justify-content: center;
  margin-bottom: 2rem;
}

.reports-screen-filter-select {
  background: var(--reports-white);
  border: none;
  padding: 1rem 2rem;
  border-radius: var(--reports-border-radius);
  font-size: 1.1rem;
  font-weight: 600;
  color: var(--reports-dark);
  cursor: pointer;
  box-shadow: 0 6px 20px rgba(0, 0, 0, 0.15);
  transition: var(--reports-transition);
  min-width: 200px;
}

.reports-screen-filter-select:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.2);
}

.reports-screen-filter-select:focus {
  outline: none;
  box-shadow: 0 0 0 3px rgba(255, 255, 255, 0.3);
}

/* Content Section */
.reports-screen-content {
  padding: 2rem;
  position: relative;
  z-index: 1;
}

/* Overview Statistics */
.reports-screen-overview {
  margin-bottom: 3rem;
}

.reports-screen-section-title {
  color: var(--reports-white);
  font-size: 2rem;
  font-weight: 700;
  text-align: center;
  margin-bottom: 2rem;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

.reports-screen-overview-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 2rem;
}

.reports-screen-overview-card {
  background: var(--reports-white);
  border-radius: var(--reports-border-radius);
  padding: 2rem;
  box-shadow: var(--reports-card-shadow);
  transition: var(--reports-transition);
  position: relative;
  overflow: visible;
  word-break: break-word;
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.reports-screen-overview-card:hover {
  transform: translateY(-10px);
  box-shadow: 0 15px 40px rgba(102, 126, 234, 0.25);
}

.reports-screen-overview-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 6px;
  border-radius: var(--reports-border-radius) var(--reports-border-radius) 0 0;
}

.reports-screen-overview-card.success::before {
  background: var(--reports-gradient-success);
}

.reports-screen-overview-card.primary::before {
  background: var(--reports-gradient-primary);
}

.reports-screen-overview-card.warning::before {
  background: var(--reports-gradient-warning);
}

.reports-screen-overview-card.info::before {
  background: var(--reports-gradient-info);
}

.reports-screen-overview-content {
  display: flex;
  align-items: center;
  gap: 1.5rem;
}

.reports-screen-overview-icon {
  width: 70px;
  height: 70px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.8rem;
  color: var(--reports-white);
  box-shadow: 0 6px 20px rgba(0, 0, 0, 0.2);
}

.reports-screen-overview-card.success .reports-screen-overview-icon {
  background: var(--reports-gradient-success);
}

.reports-screen-overview-card.primary .reports-screen-overview-icon {
  background: var(--reports-gradient-primary);
}

.reports-screen-overview-card.warning .reports-screen-overview-icon {
  background: var(--reports-gradient-warning);
}

.reports-screen-overview-card.info .reports-screen-overview-icon {
  background: var(--reports-gradient-info);
}

.reports-screen-overview-details h3 {
  font-size: 2.5rem;
  font-weight: 900;
  margin: 0;
  color: var(--reports-dark);
}

.reports-screen-overview-details p {
  font-size: 1.1rem;
  color: var(--reports-dark);
  margin: 0.5rem 0 0 0;
  opacity: 0.8;
  font-weight: 500;
}

/* Status Cards */
.reports-screen-status-section {
  margin-bottom: 3rem;
}

.reports-screen-status-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(220px, 1fr));
  gap: 1.5rem;
}

.reports-screen-status-card {
  background: var(--reports-white);
  border-radius: var(--reports-border-radius);
  padding: 1.5rem;
  box-shadow: var(--reports-card-shadow);
  transition: var(--reports-transition);
  text-align: center;
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.reports-screen-status-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 12px 35px rgba(102, 126, 234, 0.2);
}

.reports-screen-status-icon {
  width: 50px;
  height: 50px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.4rem;
  color: var(--reports-white);
  margin: 0 auto 1rem auto;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
}

.reports-screen-status-card.warning .reports-screen-status-icon {
  background: var(--reports-gradient-warning);
}

.reports-screen-status-card.primary .reports-screen-status-icon {
  background: var(--reports-gradient-primary);
}

.reports-screen-status-card.success .reports-screen-status-icon {
  background: var(--reports-gradient-success);
}

.reports-screen-status-card.info .reports-screen-status-icon {
  background: var(--reports-gradient-info);
}

.reports-screen-status-number {
  font-size: 2rem;
  font-weight: 800;
  color: var(--reports-dark);
  margin: 0;
}

.reports-screen-status-label {
  font-size: 1rem;
  color: var(--reports-dark);
  margin: 0.5rem 0 0 0;
  opacity: 0.8;
  font-weight: 500;
}

/* Charts Section */
.reports-screen-charts {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
  gap: 2rem;
  margin-bottom: 3rem;
}

.reports-screen-chart-card {
  background: var(--reports-white);
  border-radius: var(--reports-border-radius);
  padding: 2rem;
  box-shadow: var(--reports-card-shadow);
  transition: var(--reports-transition);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.reports-screen-chart-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 12px 35px rgba(102, 126, 234, 0.2);
}

.reports-screen-chart-title {
  font-size: 1.4rem;
  font-weight: 700;
  color: var(--reports-dark);
  margin: 0 0 1.5rem 0;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.reports-screen-chart-content {
  min-height: 200px;
}

/* Data Lists */
.reports-screen-data-list {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
}

.reports-screen-data-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1rem;
  background: rgba(102, 126, 234, 0.05);
  border-radius: 12px;
  margin-bottom: 0.75rem;
  transition: var(--reports-transition);
}

.reports-screen-data-item:hover {
  background: rgba(102, 126, 234, 0.1);
  transform: translateX(5px);
}

.reports-screen-data-name {
  font-weight: 600;
  color: var(--reports-dark);
}

.reports-screen-data-value {
  font-weight: 700;
  color: var(--reports-primary);
}

/* Progress Bars */
.reports-screen-progress {
  width: 100%;
  height: 8px;
  background: rgba(102, 126, 234, 0.1);
  border-radius: 4px;
  overflow: hidden;
  margin: 0.5rem 0;
}

.reports-screen-progress-fill {
  height: 100%;
  background: var(--reports-gradient-primary);
  border-radius: 4px;
  transition: width 1s ease-in-out;
}

/* Empty State */
.reports-screen-empty {
  text-align: center;
  padding: 4rem 2rem;
  color: var(--reports-white);
}

.reports-screen-empty-icon {
  font-size: 4rem;
  opacity: 0.5;
  margin-bottom: 1.5rem;
}

.reports-screen-empty-title {
  font-size: 1.8rem;
  font-weight: 600;
  margin: 0 0 1rem 0;
}

.reports-screen-empty-text {
  font-size: 1.2rem;
  opacity: 0.8;
  margin: 0;
}

/* Loading State */
.reports-screen-loading {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 4rem;
  color: var(--reports-white);
}

.reports-screen-loading-icon {
  font-size: 3rem;
  margin-bottom: 1rem;
  animation: reports-screen-pulse 2s ease-in-out infinite;
}

.reports-screen-loading-text {
  font-size: 1.3rem;
  font-weight: 500;
}

@keyframes reports-screen-pulse {
  0%, 100% { opacity: 1; transform: scale(1); }
  50% { opacity: 0.7; transform: scale(1.1); }
}

/* Responsive Design */
@media (max-width: 1200px) {
  .reports-screen-charts {
    grid-template-columns: 1fr;
  }
  
  .reports-screen-overview-grid {
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  }
}

@media (max-width: 768px) {
  .reports-screen-header {
    padding: 2rem 1.5rem;
  }
  
  .reports-screen-title {
    font-size: 2.5rem;
  }
  
  .reports-screen-content {
    padding: 1.5rem;
  }
  
  .reports-screen-overview-grid {
    grid-template-columns: 1fr;
    gap: 1.5rem;
  }
  
  .reports-screen-status-grid {
    grid-template-columns: repeat(2, 1fr);
  }
  
  .reports-screen-charts {
    grid-template-columns: 1fr;
    gap: 1.5rem;
  }
}

@media (max-width: 480px) {
  .reports-screen-header {
    padding: 1.5rem 1rem;
  }
  
  .reports-screen-title {
    font-size: 2rem;
    flex-direction: column;
    gap: 0.5rem;
  }
  
  .reports-screen-content {
    padding: 1rem;
  }
  
  .reports-screen-status-grid {
    grid-template-columns: 1fr;
  }
  
  .reports-screen-overview-content {
    flex-direction: column;
    text-align: center;
  }
  
  .reports-screen-filter-select {
    width: 100%;
  }
}




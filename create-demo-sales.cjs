// إنشاء بيانات تجريبية وحساب المبيعات
const mongoose = require('mongoose');

async function createDemoDataAndCalculate() {
  try {
    console.log('🔗 الاتصال بقاعدة البيانات الحية...');
    
    const mongoUri = 'mongodb+srv://besomustafa:<EMAIL>/deshacoffee?retryWrites=true&w=majority&appName=MyCoffeChop';
    
    await mongoose.connect(mongoUri);
    console.log('✅ تم الاتصال بنجاح');
    
    const db = mongoose.connection.db;
    
    // التحقق من البيانات الحالية
    const currentOrders = await db.collection('orders').countDocuments();
    const currentUsers = await db.collection('users').countDocuments();
    
    console.log('📊 البيانات الحالية:');
    console.log('• الطلبات الموجودة: ' + currentOrders);
    console.log('• المستخدمين الموجودين: ' + currentUsers);
    
    // إنشاء نُدل تجريبيين إذا لم يكونوا موجودين
    console.log('\n👥 إنشاء/تحديث النُدل...');
    
    const demoWaiters = [
      { username: 'أحمد محمد', role: 'waiter', email: '<EMAIL>' },
      { username: 'فاطمة علي', role: 'waiter', email: '<EMAIL>' },
      { username: 'محمد سعد', role: 'waiter', email: '<EMAIL>' },
      { username: 'نورا حسن', role: 'waiter', email: '<EMAIL>' }
    ];
    
    const waiterIds = [];
    
    for (const waiter of demoWaiters) {
      const existingWaiter = await db.collection('users').findOne({ username: waiter.username });
      
      if (existingWaiter) {
        waiterIds.push(existingWaiter._id);
        console.log('• موجود: ' + waiter.username);
      } else {
        const result = await db.collection('users').insertOne({
          ...waiter,
          createdAt: new Date(),
          password: 'hashedPassword123' // كلمة مرور تجريبية
        });
        waiterIds.push(result.insertedId);
        console.log('• تم إنشاء: ' + waiter.username);
      }
    }
    
    // إنشاء طلبات تجريبية بقيم مالية حقيقية
    console.log('\n📦 إنشاء طلبات تجريبية بقيم مالية...');
    
    const demoOrders = [];
    const currentDate = new Date();
    
    // طلبات أحمد محمد (أفضل أداء)
    demoOrders.push(
      { total: 156.50, discount: 10.00, status: 'completed', assignedWaiter: waiterIds[0], tableNumber: 1, createdAt: new Date(currentDate - 3 * 24 * 60 * 60 * 1000) },
      { total: 203.75, discount: 0, status: 'completed', assignedWaiter: waiterIds[0], tableNumber: 2, createdAt: new Date(currentDate - 2 * 24 * 60 * 60 * 1000) },
      { total: 178.25, discount: 15.50, status: 'completed', assignedWaiter: waiterIds[0], tableNumber: 3, createdAt: new Date(currentDate - 1 * 24 * 60 * 60 * 1000) },
      { total: 124.80, discount: 5.00, status: 'completed', assignedWaiter: waiterIds[0], tableNumber: 4, createdAt: new Date() },
      { total: 95.30, discount: 0, status: 'preparing', assignedWaiter: waiterIds[0], tableNumber: 5, createdAt: new Date() }
    );
    
    // طلبات فاطمة علي (أداء جيد)
    demoOrders.push(
      { total: 267.40, discount: 25.00, status: 'completed', assignedWaiter: waiterIds[1], tableNumber: 6, createdAt: new Date(currentDate - 2 * 24 * 60 * 60 * 1000) },
      { total: 189.60, discount: 0, status: 'completed', assignedWaiter: waiterIds[1], tableNumber: 7, createdAt: new Date(currentDate - 1 * 24 * 60 * 60 * 1000) },
      { total: 234.80, discount: 30.00, status: 'completed', assignedWaiter: waiterIds[1], tableNumber: 8, createdAt: new Date() },
      { total: 167.25, discount: 10.50, status: 'completed', assignedWaiter: waiterIds[1], tableNumber: 9, createdAt: new Date() },
      { total: 145.70, discount: 0, status: 'pending', assignedWaiter: waiterIds[1], tableNumber: 10, createdAt: new Date() }
    );
    
    // طلبات محمد سعد (أداء متوسط)
    demoOrders.push(
      { total: 134.90, discount: 8.00, status: 'completed', assignedWaiter: waiterIds[2], tableNumber: 11, createdAt: new Date(currentDate - 1 * 24 * 60 * 60 * 1000) },
      { total: 198.45, discount: 0, status: 'completed', assignedWaiter: waiterIds[2], tableNumber: 12, createdAt: new Date() },
      { total: 156.30, discount: 12.00, status: 'completed', assignedWaiter: waiterIds[2], tableNumber: 13, createdAt: new Date() },
      { total: 87.65, discount: 0, status: 'cancelled', assignedWaiter: waiterIds[2], tableNumber: 14, createdAt: new Date() }
    );
    
    // طلبات نورا حسن (أداء ممتاز)
    demoOrders.push(
      { total: 345.20, discount: 50.00, status: 'completed', assignedWaiter: waiterIds[3], tableNumber: 15, createdAt: new Date(currentDate - 1 * 24 * 60 * 60 * 1000) },
      { total: 289.75, discount: 25.50, status: 'completed', assignedWaiter: waiterIds[3], tableNumber: 16, createdAt: new Date() },
      { total: 198.40, discount: 0, status: 'completed', assignedWaiter: waiterIds[3], tableNumber: 17, createdAt: new Date() },
      { total: 234.60, discount: 15.30, status: 'completed', assignedWaiter: waiterIds[3], tableNumber: 18, createdAt: new Date() },
      { total: 176.85, discount: 8.50, status: 'completed', assignedWaiter: waiterIds[3], tableNumber: 19, createdAt: new Date() }
    );
    
    // طلبات بدون نادل محدد
    demoOrders.push(
      { total: 112.30, discount: 0, status: 'completed', assignedWaiter: null, tableNumber: 20, createdAt: new Date() },
      { total: 89.75, discount: 5.25, status: 'completed', assignedWaiter: null, tableNumber: 21, createdAt: new Date() },
      { total: 156.40, discount: 0, status: 'pending', assignedWaiter: null, tableNumber: 22, createdAt: new Date() }
    );
    
    // إدراج الطلبات الجديدة
    await db.collection('orders').insertMany(demoOrders);
    console.log('✅ تم إنشاء ' + demoOrders.length + ' طلب تجريبي بقيم مالية');
    
    // حساب الإحصائيات
    console.log('\n📊 حساب إحصائيات المبيعات...');
    
    const allOrders = await db.collection('orders').find({}).toArray();
    const allUsers = await db.collection('users').find({ role: 'waiter' }).toArray();
    
    // حساب المبيعات الإجمالية
    let totalSalesBeforeDiscount = 0;
    let totalDiscounts = 0;
    let totalNetSales = 0;
    let completedOrders = 0;
    
    const statusStats = {};
    const waiterStats = {};
    
    allOrders.forEach(order => {
      const status = order.status || 'غير محدد';
      statusStats[status] = (statusStats[status] || 0) + 1;
      
      const orderTotal = parseFloat(order.total) || 0;
      const orderDiscount = parseFloat(order.discount) || 0;
      const orderNet = orderTotal - orderDiscount;
      
      if (status === 'completed') {
        completedOrders++;
        totalSalesBeforeDiscount += orderTotal;
        totalDiscounts += orderDiscount;
        totalNetSales += orderNet;
      }
      
      // إحصائيات النُدل
      let waiterName = 'غير محدد';
      if (order.assignedWaiter) {
        const waiter = allUsers.find(u => u._id.toString() === order.assignedWaiter.toString());
        waiterName = waiter ? waiter.username : 'نادل غير معروف';
      }
      
      if (!waiterStats[waiterName]) {
        waiterStats[waiterName] = {
          totalOrders: 0,
          completedOrders: 0,
          totalSales: 0,
          totalDiscounts: 0,
          netSales: 0
        };
      }
      
      waiterStats[waiterName].totalOrders++;
      
      if (status === 'completed') {
        waiterStats[waiterName].completedOrders++;
        waiterStats[waiterName].totalSales += orderTotal;
        waiterStats[waiterName].totalDiscounts += orderDiscount;
        waiterStats[waiterName].netSales += orderNet;
      }
    });
    
    // عرض النتائج
    console.log('\n' + '='.repeat(70));
    console.log('📊 تقرير المبيعات النهائي مع البيانات التجريبية');
    console.log('='.repeat(70));
    
    console.log('\n📈 الإحصائيات العامة:');
    console.log('• إجمالي الطلبات: ' + allOrders.length);
    console.log('• الطلبات المكتملة: ' + completedOrders);
    console.log('• الطلبات غير المكتملة: ' + (allOrders.length - completedOrders));
    
    console.log('\n📋 توزيع الطلبات حسب الحالة:');
    Object.entries(statusStats).forEach(([status, count]) => {
      const percentage = ((count / allOrders.length) * 100).toFixed(1);
      console.log('   ' + status + ': ' + count + ' طلب (' + percentage + '%)');
    });
    
    console.log('\n💰 إجمالي المبيعات (الطلبات المكتملة):');
    console.log('💵 إجمالي المبيعات قبل الخصم: ' + totalSalesBeforeDiscount.toFixed(2) + ' ريال');
    console.log('🎯 إجمالي الخصومات: ' + totalDiscounts.toFixed(2) + ' ريال');
    console.log('✨ صافي المبيعات بعد الخصم: ' + totalNetSales.toFixed(2) + ' ريال');
    console.log('📈 نسبة الخصومات: ' + ((totalDiscounts / totalSalesBeforeDiscount) * 100).toFixed(1) + '%');
    console.log('📊 متوسط قيمة الطلب: ' + (totalNetSales / completedOrders).toFixed(2) + ' ريال');
    
    console.log('\n👥 ترتيب النُدل حسب الأداء:');
    console.log('='.repeat(70));
    
    const sortedWaiters = Object.entries(waiterStats)
      .filter(([_, stats]) => stats.completedOrders > 0)
      .sort((a, b) => b[1].netSales - a[1].netSales);
    
    sortedWaiters.forEach(([waiterName, stats], index) => {
      console.log('\n🏆 ' + (index + 1) + '. النادل: ' + waiterName);
      console.log('   📦 الطلبات المكتملة: ' + stats.completedOrders);
      console.log('   💰 إجمالي المبيعات: ' + stats.totalSales.toFixed(2) + ' ريال');
      console.log('   🎯 الخصومات: ' + stats.totalDiscounts.toFixed(2) + ' ريال');
      console.log('   ✨ صافي المبيعات: ' + stats.netSales.toFixed(2) + ' ريال');
      console.log('   📈 متوسط الطلب: ' + (stats.netSales / stats.completedOrders).toFixed(2) + ' ريال');
      console.log('   📊 نسبة من الإجمالي: ' + ((stats.netSales / totalNetSales) * 100).toFixed(1) + '%');
      console.log('   ' + '-'.repeat(50));
    });
    
    // النُدل مع طلبات غير مكتملة
    const waitersWithPending = Object.entries(waiterStats)
      .filter(([_, stats]) => stats.totalOrders > stats.completedOrders);
    
    if (waitersWithPending.length > 0) {
      console.log('\n⏳ نُدل بطلبات غير مكتملة:');
      waitersWithPending.forEach(([waiterName, stats]) => {
        const pending = stats.totalOrders - stats.completedOrders;
        console.log('   ' + waiterName + ': ' + pending + ' طلب غير مكتمل');
      });
    }
    
    console.log('\n' + '='.repeat(70));
    console.log('📅 تاريخ التقرير: ' + new Date().toLocaleString('ar-SA'));
    console.log('🌐 قاعدة البيانات: MongoDB Atlas (مباشر)');
    console.log('✅ تم إنشاء البيانات التجريبية وحساب المبيعات بنجاح');
    console.log('='.repeat(70));
    
    await mongoose.disconnect();
    
  } catch (error) {
    console.error('❌ خطأ: ' + error.message);
    console.error(error);
  }
}

createDemoDataAndCalculate();

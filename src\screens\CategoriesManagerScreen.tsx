import React, { useEffect } from 'react';
// استدعاء ملف CSS الجديد المنظم
import '../styles/manager/CategoriesManagerScreen.css';

interface Category {
  _id: string;
  name: string;
  description?: string;
  color: string;
  icon?: string;
}

interface MenuItem {
  _id: string;
  category?: string | Category | { _id: string; name: string; };
  categories?: string[];
}

interface CategoriesManagerScreenProps {
  categories: Category[];
  menuItems: MenuItem[];
  selectedCategory: Category | null;
  showCategoryModal: boolean;
  setSelectedCategory: (category: Category | null) => void;
  setShowCategoryModal: (show: boolean) => void;
  deleteCategory: (categoryId: string) => void;
}

const CategoriesManagerScreen: React.FC<CategoriesManagerScreenProps> = ({
  categories,
  menuItems,
  selectedCategory,
  showCategoryModal,
  setSelectedCategory,
  setShowCategoryModal,
  deleteCategory
}) => {
  
  // تطبيق الألوان الديناميكية باستخدام useEffect
  useEffect(() => {
    const categoryHeaders = document.querySelectorAll('[data-color]');
    categoryHeaders.forEach((element) => {
      const color = element.getAttribute('data-color');
      if (color && element instanceof HTMLElement) {
        element.style.backgroundColor = color;
      }
    });
  }, [categories]);

  return (
    <div className="categories-screen">
      <div className="categories-header">
        <h1>
          <i className="fas fa-tags"></i>
          إدارة الفئات
        </h1>
        <button 
          className="add-category-btn"
          onClick={() => {
            setSelectedCategory(null);
            setShowCategoryModal(true);
          }}
          title="إضافة فئة جديدة"
        >
          <i className="fas fa-plus"></i>
          إضافة فئة جديدة
        </button>
      </div>

      <div className="categories-stats">
        <div className="stat-card">
          <i className="fas fa-tags"></i>
          <span className="count">{categories.length}</span>
          <span className="label">إجمالي الفئات</span>
        </div>
        <div className="stat-card">
          <i className="fas fa-coffee"></i>
          <span className="count">{menuItems.length}</span>
          <span className="label">إجمالي المنتجات</span>
        </div>
      </div>

      <div className="categories-grid">
        {categories.map(category => {
          const categoryItems = menuItems.filter(item =>
            item.category === category._id || 
            (typeof item.category === 'object' && item.category?._id === category._id) ||
            item.categories?.includes(category._id) // للتوافق مع البيانات القديمة
          );

          return (
            <div key={category._id} className="category-card">
              <div className="category-header" data-color={category.color}>
                {category.icon && <i className={category.icon}></i>}
                <h3>{category.name}</h3>
              </div>

              <div className="category-info">
                {category.description && (
                  <p className="category-description">{category.description}</p>
                )}
                <div className="category-stats">
                  <span className="items-count">{categoryItems.length} عنصر</span>
                  <span className="color-code" data-color={category.color}>
                    {category.color}
                  </span>
                </div>
              </div>

              <div className="category-actions">
                <button
                  className="edit-category-btn"
                  onClick={() => {
                    setSelectedCategory(category);
                    setShowCategoryModal(true);
                  }}
                  title="تعديل الفئة"
                >
                  <i className="fas fa-edit"></i>
                  تعديل
                </button>
                
                <button
                  className="delete-category-btn"
                  onClick={() => deleteCategory(category._id)}
                  title="حذف الفئة"
                  disabled={categoryItems.length > 0}
                >
                  <i className="fas fa-trash"></i>
                  حذف
                </button>
              </div>
            </div>
          );
        })}
      </div>
    </div>
  );
};

export default CategoriesManagerScreen;

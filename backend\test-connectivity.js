const axios = require('axios');

async function testFrontendConnectivity() {
    try {
        console.log('🔍 Testing frontend connectivity to backend...');
        
        // Test 1: Health endpoint
        console.log('\n1️⃣ Testing health endpoint...');
        const healthResponse = await axios.get('http://localhost:5001/health', {
            headers: {
                'Accept': 'application/json',
                'Origin': 'http://localhost:5190'
            }
        });
        console.log('✅ Health check successful:', healthResponse.data);
        
        // Test 2: API base
        console.log('\n2️⃣ Testing API base...');
        const apiResponse = await axios.get('http://localhost:5001/api/v1/auth/health', {
            headers: {
                'Accept': 'application/json',
                'Origin': 'http://localhost:5190'
            }
        });
        console.log('✅ API base successful:', apiResponse.status);
        
    } catch (error) {
        console.error('❌ Connectivity test failed!');
        console.error('🔴 Error:', error.response?.status || error.code);
        console.error('🔴 Message:', error.response?.data || error.message);
    }
}

testFrontendConnectivity();

const express = require('express');
const router = express.Router();
const { authenticateToken } = require('../middleware/auth');
const DiscountRequest = require('../models/DiscountRequest');

// GET /api/discount-requests - جلب طلبات الخصم
router.get('/', authenticateToken, async (req, res) => {
  try {
    console.log('📋 Discount Requests API called with query:', req.query);
    const { status } = req.query;

    let filter = {};
    if (status && status !== 'all') {
      filter.status = status;
    }

    console.log('📋 Using filter:', filter);

    const discountRequests = await DiscountRequest.find(filter)
      .sort({ createdAt: -1 })
      .populate('requestedBy', 'name username')
      .limit(999999); // إزالة حد الـ 100 طلب خصم

    // تحسين البيانات المرسلة مع إضافة المعلومات المفقودة
    const Order = require('../models/Order');
    const User = require('../models/User');
    const enhancedRequests = await Promise.all(
      discountRequests.map(async (request) => {
        let requestData = request.toObject();
        
        // جلب أسماء المستخدمين للـ approvedBy و rejectedBy
        if (request.approvedBy && !request.approvedByName) {
          try {
            // البحث أولاً بالـ username
            let approver = await User.findOne({ username: request.approvedBy });
            if (!approver) {
              // إذا لم يوجد، البحث بالـ _id (في حالة كان معرف)
              approver = await User.findById(request.approvedBy);
            }
            if (approver) {
              requestData.approvedByName = approver.name;
              requestData.approvedByUsername = approver.username;
            }
          } catch (err) {
            console.warn('خطأ في جلب بيانات الموافق:', err.message);
          }
        }
        
        if (request.rejectedBy && !request.rejectedByName) {
          try {
            // البحث أولاً بالـ username
            let rejecter = await User.findOne({ username: request.rejectedBy });
            if (!rejecter) {
              // إذا لم يوجد، البحث بالـ _id (في حالة كان معرف)
              rejecter = await User.findById(request.rejectedBy);
            }
            if (rejecter) {
              requestData.rejectedByName = rejecter.name;
              requestData.rejectedByUsername = rejecter.username;
            }
          } catch (err) {
            console.warn('خطأ في جلب بيانات الرافض:', err.message);
          }
        }
        
        // جلب اسم النادل المطلوب للطلب
        if (request.requestedBy && !request.waiterName) {
          try {
            // البحث أولاً بالـ username
            let waiter = await User.findOne({ username: request.requestedBy });
            if (!waiter) {
              // إذا لم يوجد، البحث بالـ _id (في حالة كان معرف)
              waiter = await User.findById(request.requestedBy);
            }
            if (waiter) {
              requestData.waiterName = waiter.name;
              requestData.waiterUsername = waiter.username;
            }
          } catch (err) {
            console.warn('خطأ في جلب بيانات النادل:', err.message);
          }
        }
        
        // محاولة جلب الطلب المرتبط
        let relatedOrder = null;
        if (request.orderId) {
          try {
            relatedOrder = await Order.findById(request.orderId).populate('staff.waiter', 'name username');
            if (!relatedOrder) {
              // البحث بناءً على رقم الطلب
              relatedOrder = await Order.findOne({ orderNumber: request.orderNumber }).populate('staff.waiter', 'name username');
            }
          } catch (orderError) {
            console.warn('خطأ في جلب الطلب المرتبط:', orderError.message);
          }
        }
        
        // إضافة بيانات الطلب المرتبط
        if (relatedOrder) {
          // جلب اسم النادل المسؤول عن الطلب الأصلي
          let orderWaiterName = relatedOrder.waiterName;
          
          // إذا لم يكن هناك waiterName أو كان يبدو كمعرف، حاول الحصول عليه من staff.waiter
          if (!orderWaiterName || orderWaiterName.length === 24 || orderWaiterName.includes('_') || !orderWaiterName.includes(' ')) {
            if (relatedOrder.staff?.waiter) {
              // إذا كان staff.waiter مملوء (populated)
              if (typeof relatedOrder.staff.waiter === 'object' && relatedOrder.staff.waiter.name) {
                orderWaiterName = relatedOrder.staff.waiter.name;
              } else {
                // إذا كان staff.waiter مجرد معرف، حاول جلب المستخدم
                try {
                  const waiterUser = await User.findById(relatedOrder.staff.waiter);
                  if (waiterUser) {
                    orderWaiterName = waiterUser.name;
                  }
                } catch (err) {
                  console.warn('خطأ في جلب بيانات النادل من staff.waiter:', err.message);
                }
              }
            } else if (relatedOrder.waiterId) {
              // محاولة من خلال waiterId
              try {
                const waiterUser = await User.findById(relatedOrder.waiterId);
                if (waiterUser) {
                  orderWaiterName = waiterUser.name;
                }
              } catch (err) {
                console.warn('خطأ في جلب بيانات النادل من waiterId:', err.message);
              }
            }
          }
          
          // إذا كان لا يزال يبدو كمعرف أو اسم مستخدم، حاول البحث بطريقة أخرى
          if (orderWaiterName && (orderWaiterName.length === 24 || orderWaiterName.includes('_') || !orderWaiterName.includes(' '))) {
            try {
              let orderWaiter = await User.findOne({ username: orderWaiterName });
              if (!orderWaiter) {
                orderWaiter = await User.findById(orderWaiterName);
              }
              if (orderWaiter) {
                orderWaiterName = orderWaiter.name;
              }
            } catch (err) {
              console.warn('خطأ في جلب اسم نادل الطلب الأصلي:', err.message);
            }
          }
          
          requestData.order = {
            _id: relatedOrder._id,
            orderNumber: relatedOrder.orderNumber,
            tableNumber: relatedOrder.tableNumber,
            totalAmount: relatedOrder.totalAmount,
            totals: relatedOrder.totals,
            status: relatedOrder.status,
            waiterName: orderWaiterName,
            customerName: relatedOrder.customerName,
            items: relatedOrder.items || [], // إضافة تفاصيل الأصناف
            createdAt: relatedOrder.createdAt,
            updatedAt: relatedOrder.updatedAt
          };
          
          // تحديث رقم الطاولة إذا كان مفقوداً
          if (!requestData.tableNumber && relatedOrder.tableNumber) {
            requestData.tableNumber = relatedOrder.tableNumber;
          }
        }
        
        // حساب المبلغ النهائي (amount) من requestedDiscount
        if (request.requestedDiscount !== undefined && request.amount === undefined) {
          requestData.amount = request.requestedDiscount;
        }
        
        // إضافة formattedAmount
        if (requestData.amount !== undefined) {
          requestData.formattedAmount = Number(requestData.amount).toFixed(2) + ' ج.م';
        } else if (requestData.requestedDiscount !== undefined) {
          requestData.formattedAmount = Number(requestData.requestedDiscount).toFixed(2) + ' ج.م';
        } else {
          requestData.formattedAmount = '0.00 ج.م';
        }
        
        return requestData;
      })
    );

    console.log('📋 Discount Requests Query Result:', {
      filter,
      count: enhancedRequests.length,
      sampleData: enhancedRequests.slice(0, 2).map(req => ({
        id: req._id,
        status: req.status,
        orderNumber: req.orderNumber,
        amount: req.amount,
        requestedDiscount: req.requestedDiscount,
        tableNumber: req.tableNumber,
        formattedAmount: req.formattedAmount,
        hasOrder: !!req.order
      }))
    });

    res.json({
      success: true,
      message: 'تم جلب طلبات الخصم بنجاح',
      data: enhancedRequests,
      count: enhancedRequests.length
    });
  } catch (error) {
    console.error('Error fetching discount requests:', error);
    res.status(500).json({
      success: false,
      message: 'خطأ في جلب طلبات الخصم',
      error: error.message
    });
  }
});

// POST /api/discount-requests - إنشاء طلب خصم جديد
router.post('/', authenticateToken, async (req, res) => {
  try {
    console.log('📝 Creating new discount request:', req.body);
    const { 
      orderId, 
      orderNumber,
      customerName, 
      originalAmount, 
      requestedDiscount, 
      reason,
      waiterName,
      tableNumber
    } = req.body;

    if (!orderId || !customerName || !originalAmount || !requestedDiscount || !reason) {
      return res.status(400).json({
        success: false,
        message: 'جميع الحقول مطلوبة (orderId, customerName, originalAmount, requestedDiscount, reason)'
      });
    }

    const newRequest = new DiscountRequest({
      orderId,
      orderNumber: orderNumber || `ORDER-${Date.now()}`,
      customerName,
      originalAmount,
      requestedDiscount,
      amount: requestedDiscount, // استخدام requestedDiscount كمبلغ الخصم
      reason,
      status: 'pending',
      requestedBy: waiterName || req.user.username || req.user.name,
      waiterName: waiterName || req.user.username || req.user.name,
      tableNumber: tableNumber || 'غير محدد'
    });

    await newRequest.save();

    // Send Socket notifications for new discount request
    if (global.socketHandlers) {
      try {
        // Notify managers about new discount request
        global.socketHandlers.sendRoleNotification('manager', 
          `طلب خصم جديد من ${newRequest.waiterName || newRequest.requestedBy}`, {
          type: 'discount-request-created',
          requestId: newRequest._id,
          orderId: newRequest.orderId,
          orderNumber: newRequest.orderNumber,
          customerName: newRequest.customerName,
          originalAmount: newRequest.originalAmount,
          requestedDiscount: newRequest.requestedDiscount,
          requestedBy: newRequest.requestedBy,
          waiterName: newRequest.waiterName,
          tableNumber: newRequest.tableNumber,
          reason: newRequest.reason,
          timestamp: new Date().toISOString()
        });

        console.log(`💰 تم إرسال إشعار طلب خصم جديد من النادل: ${newRequest.waiterName || newRequest.requestedBy}`);
      } catch (socketError) {
        console.error('خطأ في إرسال إشعار Socket:', socketError);
      }
    }

    res.status(201).json({
      success: true,
      message: 'تم إنشاء طلب الخصم بنجاح',
      data: newRequest
    });
  } catch (error) {
    console.error('Error creating discount request:', error);
    res.status(500).json({
      success: false,
      message: 'خطأ في إنشاء طلب الخصم',
      error: error.message
    });
  }
});

// POST /api/discount-requests/create-test - إنشاء طلب خصم تجريبي للاختبار
router.post('/create-test', authenticateToken, async (req, res) => {
  try {
    console.log('🧪 Creating test discount request...');
    
    // إنشاء طلب خصم تجريبي
    const testDiscountRequest = new DiscountRequest({
      orderNumber: 'TEST-' + Date.now(),
      customerName: 'عميل تجريبي',
      originalAmount: 100,
      requestedDiscount: 10,
      reason: 'طلب خصم تجريبي للاختبار',
      status: 'pending',
      requestedBy: req.user._id,
      waiterName: req.user.name || 'نادل تجريبي'
    });

    const savedRequest = await testDiscountRequest.save();
    console.log('✅ Test discount request created:', savedRequest._id);

    res.json({
      success: true,
      message: 'تم إنشاء طلب خصم تجريبي بنجاح',
      data: savedRequest
    });
  } catch (error) {
    console.error('Error creating test discount request:', error);
    res.status(500).json({
      success: false,
      message: 'خطأ في إنشاء طلب الخصم التجريبي',
      error: error.message
    });
  }
});

// PUT /api/discount-requests/:id - تحديث حالة طلب الخصم
router.put('/:id', authenticateToken, async (req, res) => {
  try {
    const { id } = req.params;
    const { status, approvedBy } = req.body;

    console.log('🔄 تحديث طلب خصم:', { id, status, approvedBy, user: req.user.username });

    const discountRequest = await DiscountRequest.findById(id);

    if (!discountRequest) {
      return res.status(404).json({
        success: false,
        message: 'طلب الخصم غير موجود'
      });
    }

    // جلب بيانات المستخدم الحالي لحفظ اسمه
    const currentUser = req.user;
    const currentUserName = currentUser.name || currentUser.username;

    // تحديث الحقول المطلوبة فقط
    const updateData = {
      status: status,
      approvedBy: approvedBy || req.user.username
    };

    // إضافة اسم المستخدم حسب الحالة
    if (status === 'approved') {
      updateData.approvedByName = currentUserName;
      updateData.approvedBy = req.user.username;
    } else if (status === 'rejected') {
      updateData.rejectedBy = req.user.username;
      updateData.rejectedByName = currentUserName;
    }

    // استخدام findByIdAndUpdate لتجنب مشاكل validation
    const updatedRequest = await DiscountRequest.findByIdAndUpdate(
      id, 
      updateData, 
      { 
        new: true, // إرجاع الوثيقة المحدثة
        runValidators: false // تجاهل validators لأن requestedBy موجود بالفعل
      }
    );

    console.log('✅ تم تحديث طلب الخصم:', {
      id: updatedRequest._id,
      status: updatedRequest.status,
      approvedBy: updatedRequest.approvedBy
    });

    // إذا تم الموافقة على الخصم، طبق الخصم على الطلب الأصلي
    if (status === 'approved') {
      console.log('💰 تطبيق الخصم على الطلب الأصلي:', {
        orderId: updatedRequest.orderId,
        originalAmount: updatedRequest.originalAmount,
        discountAmount: updatedRequest.requestedDiscount
      });

      try {
        const Order = require('../models/Order');
        
        // العثور على الطلب الأصلي
        const originalOrder = await Order.findById(updatedRequest.orderId);
        console.log('🔍 الطلب الأصلي قبل التحديث:', {
          id: originalOrder?._id,
          totalsTotal: originalOrder?.totals?.total,
          totalPrice: originalOrder?.totalPrice,
          currentDiscount: originalOrder?.totals?.discount || 0
        });
        
        if (originalOrder) {
          // حساب المبلغ الجديد بعد الخصم
          const currentTotal = originalOrder.totals.total || originalOrder.totalPrice || 0;
          const newTotal = Math.max(0, currentTotal - updatedRequest.requestedDiscount);
          
          // تحديث الطلب مع الخصم
          const updatedOrder = await Order.findByIdAndUpdate(updatedRequest.orderId, {
            $set: {
              'totals.discount': updatedRequest.requestedDiscount,
              'totals.total': newTotal,
              discountApplied: updatedRequest.requestedDiscount,
              discountReason: updatedRequest.reason,
              discountStatus: 'approved',
              discountAmount: updatedRequest.requestedDiscount
            }
          }, { new: true });

          console.log('✅ تم تطبيق الخصم على الطلب:', {
            orderId: updatedRequest.orderId,
            oldTotal: currentTotal,
            discount: updatedRequest.requestedDiscount,
            newTotal: newTotal,
            updatedTotalsTotal: updatedOrder?.totals?.total,
            updatedTotalPrice: updatedOrder?.totalPrice
          });

          // إرسال إشعار Socket لتحديث الطلبات في الواجهات الأمامية
          if (global.socketHandlers) {
            try {
              global.socketHandlers.broadcastToAll('order-updated', {
                type: 'discount-applied',
                orderId: updatedRequest.orderId,
                orderNumber: updatedRequest.orderNumber,
                newTotal: newTotal,
                discountAmount: updatedRequest.requestedDiscount,
                timestamp: new Date().toISOString()
              });
              console.log('📡 تم إرسال إشعار تحديث الطلب بعد تطبيق الخصم');
            } catch (socketError) {
              console.error('خطأ في إرسال إشعار تحديث الطلب:', socketError);
            }
          }
        } else {
          console.warn('⚠️ لم يتم العثور على الطلب الأصلي:', updatedRequest.orderId);
        }
      } catch (orderUpdateError) {
        console.error('❌ خطأ في تحديث الطلب الأصلي:', orderUpdateError);
        // لا نريد إيقاف العملية إذا فشل تحديث الطلب
      }
    }

    // Send Socket notifications for discount request status update
    if (global.socketHandlers) {
      try {
        const statusMessages = {
          'approved': 'تم قبول طلب الخصم',
          'rejected': 'تم رفض طلب الخصم',
          'pending': 'طلب الخصم قيد المراجعة'
        };

        // إرسال إشعار مخصص للنادل الذي طلب الخصم
        if (updatedRequest.requestedBy) {
          global.socketHandlers.sendToUser(updatedRequest.requestedBy, 'discount-request-status', {
            status: status,
            orderNumber: updatedRequest.orderNumber,
            orderId: updatedRequest.orderId,
            customerName: updatedRequest.customerName,
            tableNumber: updatedRequest.tableNumber,
            discountAmount: updatedRequest.requestedDiscount,
            originalAmount: updatedRequest.originalAmount,
            reason: updatedRequest.reason,
            approvedBy: updatedRequest.approvedBy,
            rejectionReason: status === 'rejected' ? req.body.rejectionReason : null,
            timestamp: new Date().toISOString(),
            message: `${statusMessages[status]} للطلب #${updatedRequest.orderNumber}`
          });
          console.log(`💰 تم إرسال إشعار مخصص للنادل ${updatedRequest.requestedBy}: ${status}`);
        }

        // Notify all waiters about discount request status update (for backup)
        global.socketHandlers.sendRoleNotification('waiter', 
          statusMessages[status] || `تم تحديث حالة طلب الخصم`, {
          type: 'discount-request-updated',
          requestId: updatedRequest._id,
          orderId: updatedRequest.orderId,
          orderNumber: updatedRequest.orderNumber,
          customerName: updatedRequest.customerName,
          status: status,
          discountAmount: updatedRequest.requestedDiscount,
          approvedBy: updatedRequest.approvedBy,
          waiterName: updatedRequest.waiterName,
          timestamp: new Date().toISOString()
        });

        // Also notify managers
        global.socketHandlers.sendRoleNotification('manager', 
          `${statusMessages[status]} للعميل ${updatedRequest.customerName}`, {
          type: 'discount-request-processed',
          requestId: updatedRequest._id,
          orderId: updatedRequest.orderId,
          orderNumber: updatedRequest.orderNumber,
          customerName: updatedRequest.customerName,
          status: status,
          discountAmount: updatedRequest.requestedDiscount,
          approvedBy: updatedRequest.approvedBy,
          waiterName: updatedRequest.waiterName,
          timestamp: new Date().toISOString()
        });

        console.log(`💰 تم إرسال إشعار تحديث طلب الخصم: ${status} للعميل ${updatedRequest.customerName}`);
      } catch (socketError) {
        console.error('خطأ في إرسال إشعار Socket:', socketError);
      }
    }

    res.json({
      success: true,
      message: 'تم تحديث طلب الخصم بنجاح',
      data: updatedRequest
    });
  } catch (error) {
    console.error('Error updating discount request:', error);
    res.status(500).json({
      success: false,
      message: 'خطأ في تحديث طلب الخصم',
      error: error.message
    });
  }
});

// DELETE /api/discount-requests/reset-all - إعادة تهيئة جميع طلبات الخصم (للمدير فقط)
// ملاحظة مهمة: يجب وضع هذا الـ route قبل /:id route لتجنب تعارض المسارات
router.delete('/reset-all', authenticateToken, async (req, res) => {
  try {
    console.log('🔄 طلب إعادة تهيئة جميع طلبات الخصم من المدير:', req.user?.username);
    
    // التحقق من صلاحيات المدير
    if (req.user.role !== 'manager') {
      return res.status(403).json({
        success: false,
        message: 'غير مصرح لك بهذه العملية - مديرون فقط'
      });
    }

    // احصل على عدد طلبات الخصم قبل الحذف
    const discountRequestsCount = await DiscountRequest.countDocuments();
    console.log(`📊 عدد طلبات الخصم الحالية: ${discountRequestsCount}`);

    // حذف جميع طلبات الخصم
    const deleteResult = await DiscountRequest.deleteMany({});
    console.log('✅ تم حذف طلبات الخصم:', deleteResult.deletedCount);

    // إرسال إشعار عبر Socket للجميع
    if (global.socketHandlers && global.socketHandlers.io) {
      try {
        global.socketHandlers.io.emit('discount-requests-reset', {
          message: 'تم إعادة تهيئة جميع طلبات الخصم',
          deletedCount: deleteResult.deletedCount,
          timestamp: new Date().toISOString()
        });
        console.log('📡 تم إرسال إشعار Socket لإعادة تهيئة طلبات الخصم');
      } catch (socketError) {
        console.warn('⚠️ خطأ في إرسال إشعار Socket:', socketError.message);
      }
    }

    res.json({
      success: true,
      message: `تم حذف ${deleteResult.deletedCount} طلب خصم بنجاح`,
      deletedCount: deleteResult.deletedCount,
      originalCount: discountRequestsCount,
      timestamp: new Date().toISOString()
    });

  } catch (error) {
    console.error('❌ خطأ في إعادة تهيئة طلبات الخصم:', error);
    res.status(500).json({
      success: false,
      message: 'خطأ في الخادم أثناء إعادة تهيئة طلبات الخصم',
      error: error.message,
      timestamp: new Date().toISOString()
    });
  }
});

// DELETE /api/discount-requests/:id - حذف طلب خصم واحد
router.delete('/:id', authenticateToken, async (req, res) => {
  try {
    const { id } = req.params;

    const discountRequest = await DiscountRequest.findByIdAndDelete(id);

    if (!discountRequest) {
      return res.status(404).json({
        success: false,
        message: 'طلب الخصم غير موجود'
      });
    }

    res.json({
      success: true,
      message: 'تم حذف طلب الخصم بنجاح'
    });
  } catch (error) {
    console.error('Error deleting discount request:', error);
    res.status(500).json({
      success: false,
      message: 'خطأ في حذف طلب الخصم',
      error: error.message
    });
  }
});

module.exports = router;

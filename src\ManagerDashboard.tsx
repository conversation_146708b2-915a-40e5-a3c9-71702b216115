import React, { useState, useEffect, useCallback } from 'react';
import { authenticatedGet, authenticatedPost, authenticatedPut, authenticatedDelete, getCurrentUser } from './utils/apiHelpers';
import { useToast } from './hooks/useToast';
import { notificationSound } from './utils/notificationSound';
import { smartMultiFieldSearch, smartSearchAndSort } from './utils/arabicSearch';
import socket from './socket';
import { SalesDiscrepancyFixer } from './components/SalesDiscrepancyFixer';

// استيراد الشاشات المنفصلة - النسخ المحولة إلى Bootstrap
import EmployeesManagerScreenBootstrap from './screens/EmployeesManagerScreenBootstrap';
import TablesManagerScreenBootstrap from './screens/TablesManagerScreenBootstrap';
import ReportsManagerScreenBootstrap from './screens/ReportsManagerScreenBootstrap';
import MenuManagerScreenBootstrap from './screens/MenuManagerScreenBootstrap';
import OrdersManagerScreenBootstrap from './screens/OrdersManagerScreenBootstrap';
import CategoriesManagerScreenBootstrap from './screens/CategoriesManagerScreenBootstrap';
import SettingsManagerScreenBootstrap from './screens/SettingsManagerScreenBootstrap';
import DiscountRequestsManagerScreenBootstrap from './screens/DiscountRequestsManagerScreenBootstrap';
import InventoryManagerScreenBootstrap from './screens/InventoryManagerScreenBootstrap';

// استدعاء ملفات CSS الجديدة المنظمة فقط
import './styles/layout/ManagerDashboard.css';
import './styles/layout/NoHeaderLayout.css';
import './styles/layout/LayoutSpacingFix.css';
import './styles/components/ModalComponents.css';
import './styles/components/TableDetailsModal.css';
import './styles/screens/HomeScreenIsolated.css';

// Enhanced Card Components
import './styles/components/EnhancedTableCard.css';

import './styles/components/EnhancedMenuCard.css';
import './styles/components/EnhancedSettingsCard.css';
import './styles/components/EnhancedDiscountCard.css';

interface ManagerDashboardProps {
  user?: any;
  onLogout?: () => void;
}

interface OrderItem {
  _id?: string;
  id?: string;
  name?: string;
  productName?: string;
  quantity?: number;
  price?: number;
  size?: string;
  category?: string;
  notes?: string;
  product?: string;
}

interface Order {
  _id: string;
  orderNumber: string;
  items: OrderItem[];
  totalAmount?: number;
  totalPrice?: number;
  totals?: {
    subtotal: number;
    tax: number;
    discount: number;
    total: number;
  };
  status: 'pending' | 'preparing' | 'ready' | 'completed' | 'delivered';
  tableNumber?: number;
  customerName?: string;
  waiterName?: string;
  waiterId?: string;
  chefName?: string;
  staff?: {
    waiter?: string;
    chef?: string;
  };
  createdAt: string;
  updatedAt: string;
}

interface Employee {
  _id: string;
  username: string;
  name: string;
  email?: string;
  phone?: string;
  role: 'waiter' | 'chef' | 'manager';
  status: 'active' | 'inactive';
  isActive: boolean;
  currentShift?: any;
}

interface TableAccount {
  _id: string;
  tableNumber: string;
  waiterName: string;
  totalAmount: number;
  isOpen: boolean;
  status: 'active' | 'closed';
  orders: Order[];
  createdAt: string;
  ordersCount?: number;
  activeOrdersCount?: number;
  totalSales?: number;
}

interface DashboardStats {
  totalOrders: number;
  totalSales: number;
  activeEmployees: number;
  activeTables: number;
  pendingOrders: number;
  preparingOrders: number;
  readyOrders: number;
  completedOrders: number;
  popularProducts?: ProductStats[];
  waiterStats?: WaiterStats[];
}

interface ProductStats {
  productId: string;
  productName: string;
  totalQuantity: number;
  totalOrders: number;
  category?: string;
}

interface WaiterStats {
  waiterId: string;
  waiterName: string;
  totalOrders: number;
  totalSales: number;
  pendingOrders: number;
  completedOrders: number;
}

interface DiscountRequest {
  _id: string;
  orderId: string;
  orderNumber: string;
  waiterName: string;
  amount?: number;
  originalAmount?: number;
  requestedDiscount?: number;
  reason?: string;
  status: 'pending' | 'approved' | 'rejected';
  createdAt: string;
  updatedAt?: string; // إضافة خاصية updatedAt
  order?: Order;
  formattedAmount?: string;
  formattedPercentage?: string;
  discountPercentage?: string;
  tableNumber?: number;
  customerName?: string; // إضافة خاصية customerName
  approvedBy?: string;
  approvedByName?: string;
  rejectedBy?: string;
  rejectedByName?: string;
  waiterUsername?: string;
  approvedByUsername?: string;
  rejectedByUsername?: string;
}

interface Shift {
  _id: string;
  employeeId: string;
  employeeName: string;
  role: 'waiter' | 'chef' | 'manager';
  startTime: string;
  endTime?: string;
  duration?: string;
  status: 'active' | 'completed';
  ordersCount: number;
  salesAmount: number;
  createdAt: string;
}

interface MenuItem {
  _id: string;
  name: string;
  price: number;
  description?: string;
  categories?: string[];
  category?: string | { _id: string; name: string };
  categoryName?: string;
  available: boolean;
  stock?: number | { quantity: number };
}

interface Category {
  _id: string;
  name: string;
  color: string;
  icon?: string;
  description?: string;
  isActive: boolean;
}

function ManagerDashboard({ user: propUser, onLogout }: ManagerDashboardProps) {
  const [currentScreen, setCurrentScreen] = useState<'home' | 'orders' | 'employees' | 'tables' | 'reports' | 'inventory' | 'menu' | 'categories' | 'discount-requests' | 'settings'>('home');
  const [loading, setLoading] = useState(false);
  
  const [sidebarOpen, setSidebarOpen] = useState(() => {
    return window.innerWidth > 768;
  });
  
  const [isMobile, setIsMobile] = useState(window.innerWidth <= 768);
  
  // Data states
  const [orders, setOrders] = useState<Order[]>([]);
  const [employees, setEmployees] = useState<Employee[]>([]);
  const [tableAccounts, setTableAccounts] = useState<TableAccount[]>([]);
  const [discountRequests, setDiscountRequests] = useState<DiscountRequest[]>([]);
  
  // Modal states
  const [selectedTableForDetails, setSelectedTableForDetails] = useState<any>(null);
  const [showTableDetailsModal, setShowTableDetailsModal] = useState(false);
  const [shifts, setShifts] = useState<Shift[]>([]);
  const [menuItems, setMenuItems] = useState<MenuItem[]>([]);
  const [updatingItems, setUpdatingItems] = useState<Set<string>>(new Set());
  const [categories, setCategories] = useState<Category[]>([]);
  
  const [stats, setStats] = useState<DashboardStats>({
    totalOrders: 0,
    totalSales: 0,
    activeEmployees: 0,
    activeTables: 0,
    pendingOrders: 0,
    preparingOrders: 0,
    readyOrders: 0,
    completedOrders: 0,
    popularProducts: [],
    waiterStats: []
  });

  // Filter states
  const [orderStatusFilter, setOrderStatusFilter] = useState<'all' | 'pending' | 'preparing' | 'ready' | 'completed'>('all');
  const [waiterFilter, setWaiterFilter] = useState<string>('all');
  const [dateFilter, setDateFilter] = useState<string>('today');
  const [orderSearchTerm, setOrderSearchTerm] = useState('');
  
  // Menu filter states
  const [selectedCategoryFilter, setSelectedCategoryFilter] = useState<string | null>(null);
  const [availabilityFilter, setAvailabilityFilter] = useState<string>('all');
  const [menuSearchTerm, setMenuSearchTerm] = useState<string>('');

  // Pagination states
  const [drinksCurrentPage, setDrinksCurrentPage] = useState(1);
  const [drinksPerPage, setDrinksPerPage] = useState(10);
  const [showAllDrinks, setShowAllDrinks] = useState(false);
  const [productsCurrentPage, setProductsCurrentPage] = useState(1);
  const [productsPerPage, setProductsPerPage] = useState(6);
  const [showAllProducts, setShowAllProducts] = useState(false);

  // Modal states
  const [showOrderDetailsModal, setShowOrderDetailsModal] = useState(false);
  const [selectedOrder, setSelectedOrder] = useState<Order | null>(null);
  const [showDiscountDetailsModal, setShowDiscountDetailsModal] = useState(false);
  const [selectedDiscountForDetails, setSelectedDiscountForDetails] = useState<DiscountRequest | null>(null);
  const [showSalesDiscrepancyModal, setShowSalesDiscrepancyModal] = useState(false);
  const [showMenuModal, setShowMenuModal] = useState(false);
  const [selectedMenuItem, setSelectedMenuItem] = useState<MenuItem | null>(null);
  const [showCategoryModal, setShowCategoryModal] = useState(false);
  const [selectedCategory, setSelectedCategory] = useState<Category | null>(null);

  // Socket states
  const [isSocketConnected, setIsSocketConnected] = useState(false);
  const [lastDataUpdate, setLastDataUpdate] = useState<number>(Date.now());

  const { showSuccess, showError, showInfo } = useToast();
  
  // User data
  const user = propUser || JSON.parse(localStorage.getItem('user') || 'null');
  const managerName = user?.username || user?.name || localStorage.getItem('username') || 'المدير';
  const managerId = user?._id || user?.id || 'manager-user';

  // Calculate product stats
  const calculateProductStats = useCallback((ordersData: Order[]): ProductStats[] => {
    const productMap = new Map<string, ProductStats>();

    ordersData.forEach(order => {
      if (order.items && Array.isArray(order.items)) {
        order.items.forEach(item => {
          const productId = item.product || item._id || item.id || 'unknown';
          const productName = item.name || item.productName || 'منتج غير معروف';
          const quantity = item.quantity || 1;

          if (productMap.has(productId)) {
            const existing = productMap.get(productId)!;
            existing.totalQuantity += quantity;
            existing.totalOrders += 1;
          } else {
            productMap.set(productId, {
              productId,
              productName,
              totalQuantity: quantity,
              totalOrders: 1,
              category: item.category || 'غير محدد'
            });
          }
        });
      }
    });

    return Array.from(productMap.values())
      .sort((a, b) => b.totalQuantity - a.totalQuantity)
      .slice(0, 10);
  }, []);

  // Calculate waiter stats
  const calculateWaiterStats = useCallback((ordersData: Order[], employeesData: Employee[]): WaiterStats[] => {
    const waiters = employeesData.filter(emp => emp.role === 'waiter');
    
    return waiters.map(waiter => {
      const waiterOrders = ordersData.filter(order => {
        return (
          order.waiterName === waiter.username || 
          order.waiterName === waiter.name ||
          order.waiterId === waiter._id ||
          (order.staff && order.staff.waiter === waiter._id)
        );
      });
      
      const totalOrders = waiterOrders.length;
      const totalSales = waiterOrders.reduce((sum, order) => {
        return sum + (order.totals?.total || order.totalPrice || order.totalAmount || 0);
      }, 0);
      
      const pendingOrders = waiterOrders.filter(order => order.status === 'pending').length;
      const completedOrders = waiterOrders.length;
      
      return {
        waiterId: waiter._id,
        waiterName: waiter.name || waiter.username,
        totalOrders,
        totalSales,
        pendingOrders,
        completedOrders
      };
    }).sort((a, b) => b.totalSales - a.totalSales);
  }, []);

  // Get waiter color class
  const getWaiterColorClass = useCallback((waiterName: string): string => {
    const waiterEmployee = employees.find(emp => 
      emp.role === 'waiter' && (
        emp.name === waiterName || 
        emp.username === waiterName ||
        emp._id === waiterName
      )
    );

    if (waiterEmployee) {
      const colorIndex = waiterEmployee._id.charCodeAt(waiterEmployee._id.length - 1) % 8;
      const colorClasses = [
        'waiter-color-1', 'waiter-color-2', 'waiter-color-3', 'waiter-color-4',
        'waiter-color-5', 'waiter-color-6', 'waiter-color-7', 'waiter-color-8'
      ];
      return colorClasses[colorIndex];
    }

    let hash = 0;
    for (let i = 0; i < waiterName.length; i++) {
      const char = waiterName.charCodeAt(i);
      hash = ((hash << 5) - hash) + char;
      hash = hash & hash;
    }
    const colorIndex = Math.abs(hash) % 8;
    const colorClasses = [
      'waiter-color-1', 'waiter-color-2', 'waiter-color-3', 'waiter-color-4',
      'waiter-color-5', 'waiter-color-6', 'waiter-color-7', 'waiter-color-8'
    ];
    return colorClasses[colorIndex];
  }, [employees]);

  // Fetch functions
  const fetchOrders = useCallback(async () => {
    try {
      setLoading(true);
      const response = await authenticatedGet('/api/v1/orders');
      if (response.success && Array.isArray(response.data)) {
        setOrders(response.data);
        return response.data;
      } else {
        console.error('استجابة غير صحيحة من الخادم:', response);
        setOrders([]);
        return [];
      }
    } catch (error) {
      console.error('خطأ في جلب الطلبات:', error);
      showError('فشل في تحميل الطلبات');
      setOrders([]);
      return [];
    } finally {
      setLoading(false);
    }
  }, [showError]);

  const fetchEmployees = useCallback(async () => {
    try {
      const response = await authenticatedGet('/api/v1/employees');
      if (response.success) {
        setEmployees(response.data || []);
        return response.data || [];
      }
    } catch (error) {
      console.error('خطأ في جلب الموظفين:', error);
      showError('فشل في تحميل بيانات الموظفين');
    }
    return [];
  }, [showError]);

  const fetchTableAccounts = useCallback(async () => {
    try {
      console.log('🔄 بدء جلب حسابات الطاولات...');
      const response = await authenticatedGet('/api/v1/table-accounts');
      console.log('📡 استجابة API الطاولات:', response);
      
      if (response.success) {
        console.log('✅ تم جلب الطاولات بنجاح. العدد:', response.data?.length || 0);
        setTableAccounts(response.data || []);
        return response.data || [];
      } else {
        console.warn('⚠️ استجابة غير ناجحة من API الطاولات:', response);
        return [];
      }
    } catch (error) {
      console.error('❌ خطأ في جلب حسابات الطاولات:', error);
      console.error('❌ تفاصيل الخطأ:', {
        message: error instanceof Error ? error.message : 'خطأ غير معروف',
        response: (error as any)?.response?.data,
        status: (error as any)?.response?.status
      });
      showError('فشل في تحميل بيانات الطاولات');
    }
    return [];
  }, [showError]);

  const fetchDiscountRequests = useCallback(async () => {
    try {
      const response = await authenticatedGet('/api/v1/discount-requests');
      if (response.success) {
        setDiscountRequests(response.data || []);
        return response.data || [];
      }
    } catch (error) {
      console.error('خطأ في جلب طلبات الخصم:', error);
      showError('فشل في تحميل طلبات الخصم');
    }
    return [];
  }, [showError]);

  const fetchMenuItems = useCallback(async () => {
    try {
      const response = await authenticatedGet('/api/v1/products');
      if (response.success) {
        setMenuItems(response.data || []);
        return response.data || [];
      }
    } catch (error) {
      console.error('خطأ في جلب عناصر القائمة:', error);
      showError('فشل في تحميل القائمة');
    }
    return [];
  }, [showError]);

  const fetchCategories = useCallback(async () => {
    try {
      console.log('🔄 Fetching categories...');
      const response = await authenticatedGet('/api/v1/categories');
      console.log('📦 Categories response:', response);
      if (response.success) {
        console.log('✅ Categories data:', response.data);
        setCategories(response.data || []);
        return response.data || [];
      } else {
        console.error('❌ Failed to fetch categories:', response);
      }
    } catch (error) {
      console.error('خطأ في جلب الفئات:', error);
      showError('فشل في تحميل الفئات');
    }
    return [];
  }, [showError]);

  const calculateStats = useCallback(async () => {
    try {
      const ordersData = orders.length > 0 ? orders : await fetchOrders();
      const employeesData = employees.length > 0 ? employees : await fetchEmployees();
      const tablesData = tableAccounts.length > 0 ? tableAccounts : await fetchTableAccounts();

      const totalOrders = ordersData.length;
      const totalSales = ordersData.reduce((sum, order) => {
        return sum + (order.totals?.total || order.totalPrice || order.totalAmount || 0);
      }, 0);

      const activeEmployees = employeesData.filter(emp => emp.status === 'active').length;
      const activeTables = tablesData.filter(table => table.isOpen).length;

      const pendingOrders = ordersData.filter(order => order.status === 'pending').length;
      const preparingOrders = ordersData.filter(order => order.status === 'preparing').length;
      const readyOrders = ordersData.filter(order => order.status === 'ready').length;
      const completedOrders = ordersData.filter(order => 
        order.status === 'completed' || order.status === 'delivered'
      ).length;

      const popularProducts = calculateProductStats(ordersData);
      const waiterStats = calculateWaiterStats(ordersData, employeesData);

      setStats({
        totalOrders,
        totalSales,
        activeEmployees,
        activeTables,
        pendingOrders,
        preparingOrders,
        readyOrders,
        completedOrders,
        popularProducts,
        waiterStats
      });
    } catch (error) {
      console.error('خطأ في حساب الإحصائيات:', error);
    }
  }, [orders, employees, tableAccounts, fetchOrders, fetchEmployees, fetchTableAccounts, calculateProductStats, calculateWaiterStats]);

  // Screen change handler
  const changeScreen = useCallback(async (screen: typeof currentScreen) => {
    console.log(`🔄 Changing screen to: ${screen}`);
    setCurrentScreen(screen);
    
    // منع تحميل البيانات إذا كان النظام في حالة تحميل
    if (loading) {
      console.log('⏳ System is loading, skipping data fetch');
      return;
    }
    
    // Load data based on screen
    switch (screen) {
      case 'home':
        if (orders.length === 0) await fetchOrders();
        if (employees.length === 0) await fetchEmployees();
        if (tableAccounts.length === 0) await fetchTableAccounts();
        break;
      case 'orders':
        if (orders.length === 0) await fetchOrders();
        if (employees.length === 0) await fetchEmployees();
        break;
      case 'employees':
        if (employees.length === 0) await fetchEmployees();
        break;
      case 'tables':
        if (tableAccounts.length === 0) await fetchTableAccounts();
        break;
      case 'inventory':
      case 'menu':
        console.log(`📋 Loading data for ${screen} screen...`);
        console.log(`📊 Current categories count: ${categories.length}`);
        console.log(`📊 Current menuItems count: ${menuItems.length}`);
        if (menuItems.length === 0) await fetchMenuItems();
        if (categories.length === 0) await fetchCategories();
        break;
      case 'categories':
        if (categories.length === 0) await fetchCategories();
        break;
      case 'discount-requests':
        if (discountRequests.length === 0) await fetchDiscountRequests();
        break;
    }
  }, [orders.length, employees.length, tableAccounts.length, menuItems.length, categories.length, discountRequests.length, fetchOrders, fetchEmployees, fetchTableAccounts, fetchMenuItems, fetchCategories, fetchDiscountRequests]);

  // Initial data loading
  useEffect(() => {
    const loadInitialData = async () => {
      // التحقق من وجود token قبل تحميل البيانات
      const token = localStorage.getItem('token') || localStorage.getItem('authToken');
      if (!token) {
        console.log('⚠️ لا يوجد token - تخطي تحميل البيانات');
        return;
      }

      setLoading(true);
      try {
        await Promise.all([
          fetchOrders(),
          fetchEmployees(),
          fetchTableAccounts(),
          fetchDiscountRequests(),
          fetchMenuItems(),
          fetchCategories()
        ]);
      } catch (error) {
        console.error('خطأ في تحميل البيانات الأولية:', error);
      } finally {
        setLoading(false);
        setLastDataUpdate(Date.now());
      }
    };

    loadInitialData();
  }, [fetchOrders, fetchEmployees, fetchTableAccounts, fetchDiscountRequests, fetchMenuItems, fetchCategories]);

  // Calculate stats when data changes
  useEffect(() => {
    if (orders.length > 0 || employees.length > 0 || tableAccounts.length > 0) {
      calculateStats();
    }
  }, [orders, employees, tableAccounts, calculateStats]);

  // Window resize handler
  useEffect(() => {
    const handleResize = () => {
      setIsMobile(window.innerWidth <= 768);
      if (window.innerWidth > 768) {
        setSidebarOpen(true);
      }
    };

    window.addEventListener('resize', handleResize);
    return () => window.removeEventListener('resize', handleResize);
  }, []);

  // Auto-close sidebar on mobile when screen changes
  useEffect(() => {
    if (isMobile && currentScreen !== 'home') {
      setSidebarOpen(false);
    }
  }, [currentScreen, isMobile]);

  // Socket connection
  useEffect(() => {
    const handleConnect = () => {
      setIsSocketConnected(true);
      console.log('✅ Socket.IO متصل');
    };

    const handleDisconnect = () => {
      setIsSocketConnected(false);
      console.log('❌ Socket.IO منقطع');
    };

    socket.on('connect', handleConnect);
    socket.on('disconnect', handleDisconnect);

    // إضافة event listeners للتحديثات الفورية
    socket.on('new-order-notification', (orderData: any) => {
      console.log('📦 طلب جديد وارد:', orderData);
      fetchOrders();
    });

    socket.on('order-status-update', (orderUpdate: any) => {
      console.log('🔄 تحديث حالة الطلب:', orderUpdate);
      fetchOrders();
    });

    socket.on('order-updated', (orderUpdate: any) => {
      console.log('✏️ تحديث الطلب:', orderUpdate);
      fetchOrders();
    });

    socket.on('table-account-updated', (tableUpdate: any) => {
      console.log('🪑 تحديث حساب الطاولة:', tableUpdate);
      fetchTableAccounts();
    });

    socket.on('table-account-closed', (closedTable: any) => {
      console.log('🚪 إغلاق طاولة:', closedTable);
      fetchTableAccounts();
    });

    socket.on('discount-request-status', (discountUpdate: any) => {
      console.log('💰 تحديث طلب الخصم:', discountUpdate);
      fetchDiscountRequests();
    });

    socket.on('menu-item-updated', (update: any) => {
      console.log('🍽️ تحديث عنصر القائمة:', update);
      fetchMenuItems();
    });

    socket.on('menu-item-added', (newItem: any) => {
      console.log('➕ إضافة عنصر جديد للقائمة:', newItem);
      fetchMenuItems();
    });

    socket.on('menu-item-removed', (removedItem: any) => {
      console.log('➖ حذف عنصر من القائمة:', removedItem);
      fetchMenuItems();
    });

    socket.on('category-updated', (categoryUpdate: any) => {
      console.log('📂 تحديث الفئة:', categoryUpdate);
      fetchCategories();
    });

    socket.on('inventory-updated', (inventoryUpdate: any) => {
      console.log('📦 تحديث المخزون:', inventoryUpdate);
      // يمكن إضافة fetch للمخزون إذا كان متوفراً
    });

    if (socket.connected) {
      setIsSocketConnected(true);
    }

    return () => {
      socket.off('connect', handleConnect);
      socket.off('disconnect', handleDisconnect);
      socket.off('new-order-notification');
      socket.off('order-status-update');
      socket.off('order-updated');
      socket.off('table-account-updated');
      socket.off('table-account-closed');
      socket.off('discount-request-status');
      socket.off('menu-item-updated');
      socket.off('menu-item-added');
      socket.off('menu-item-removed');
      socket.off('category-updated');
      socket.off('inventory-updated');
    };
  }, []);

  // تشخيص حالة الـ Modal للخصم
  useEffect(() => {
    console.log('🔍 Discount Modal State Changed:', {
      showDiscountDetailsModal,
      selectedDiscountForDetails: selectedDiscountForDetails ? 'Selected' : 'None',
      selectedId: selectedDiscountForDetails?._id
    });
  }, [showDiscountDetailsModal, selectedDiscountForDetails]);

  // Modal handlers
  const closeOrderDetailsModal = useCallback(() => {
    setShowOrderDetailsModal(false);
    setSelectedOrder(null);
  }, []);

  const closeDiscountDetailsModal = useCallback(() => {
    console.log('🔒 Closing discount details modal');
    setShowDiscountDetailsModal(false);
    setSelectedDiscountForDetails(null);
    console.log('✅ Discount modal closed');
  }, []);

  const closeMenuModal = useCallback(() => {
    setShowMenuModal(false);
    setSelectedMenuItem(null);
  }, []);

  const closeCategoryModal = useCallback(() => {
    setShowCategoryModal(false);
    setSelectedCategory(null);
  }, []);

  // Force refresh data
  const forceRefreshData = useCallback(async () => {
    setLoading(true);
    try {
      await Promise.all([
        fetchOrders(),
        fetchEmployees(),
        fetchTableAccounts(),
        fetchDiscountRequests(),
        fetchMenuItems(),
        fetchCategories()
      ]);
      setLastDataUpdate(Date.now());
      showInfo('تم تحديث البيانات بنجاح');
    } catch (error) {
      console.error('خطأ في تحديث البيانات:', error);
      showError('فشل في تحديث البيانات');
    } finally {
      setLoading(false);
    }
  }, [fetchOrders, fetchEmployees, fetchTableAccounts, fetchDiscountRequests, fetchMenuItems, fetchCategories, showInfo, showError]);

  // Update item stock
  const updateItemStock = useCallback(async (itemId: string, newQuantity: number) => {
    console.log('🎯 updateItemStock called with:', { itemId, newQuantity });
    
    // حفظ القيمة الأصلية قبل التحديث
    const originalItem = menuItems.find(item => item._id === itemId);
    const originalQuantity = originalItem?.stock ? 
      (typeof originalItem.stock === 'number' ? originalItem.stock : originalItem.stock.quantity) : 0;
    
    // إضافة العنصر لقائمة العناصر قيد التحديث
    setUpdatingItems(prev => new Set(prev).add(itemId));
    
    try {
      // تحديث فوري في الواجهة للاستجابة السريعة
      setMenuItems(prev => prev.map(item => 
        item._id === itemId 
          ? { ...item, stock: typeof item.stock === 'number' ? newQuantity : { quantity: newQuantity } }
          : item
      ));
      
      console.log('📡 Sending PUT request to update stock...');
      const response = await authenticatedPut(`/api/v1/products/${itemId}`, {
        stock: { quantity: newQuantity }
      });

      console.log('📨 Response received:', response);

      if (response.success) {
        showSuccess('تم تحديث المخزون بنجاح');
        console.log('✅ Stock update successful, keeping local state');
        // إزالة العنصر من قائمة العناصر قيد التحديث فوراً
        setUpdatingItems(prev => {
          const newSet = new Set(prev);
          newSet.delete(itemId);
          return newSet;
        });
      } else {
        console.error('❌ Update failed:', response.message);
        showError(response.message || 'فشل في تحديث المخزون');
        // إعادة القيمة السابقة محلياً في حالة الفشل
        setMenuItems(prev => prev.map(item => 
          item._id === itemId 
            ? { ...item, stock: typeof item.stock === 'number' ? originalQuantity : { quantity: originalQuantity } }
            : item
        ));
        setUpdatingItems(prev => {
          const newSet = new Set(prev);
          newSet.delete(itemId);
          return newSet;
        });
      }
    } catch (error) {
      console.error('❌ خطأ في تحديث المخزون:', error);
      showError('فشل في تحديث المخزون');
      // إعادة القيمة السابقة محلياً في حالة الخطأ
       setMenuItems(prev => prev.map(item => 
         item._id === itemId 
           ? { ...item, stock: typeof item.stock === 'number' ? originalQuantity : { quantity: originalQuantity } }
           : item
       ));
      setUpdatingItems(prev => {
        const newSet = new Set(prev);
        newSet.delete(itemId);
        return newSet;
      });
    }
  }, [fetchMenuItems, showSuccess, showError]);

  // Update item availability
  const updateItemAvailability = useCallback(async (itemId: string, isAvailable: boolean) => {
    console.log('🎯 updateItemAvailability called with:', { itemId, isAvailable });
    
    // حفظ القيمة الأصلية قبل التحديث
    const originalItem = menuItems.find(item => item._id === itemId);
    const originalAvailability = originalItem?.available || false;
    
    // إضافة العنصر لقائمة العناصر قيد التحديث
    setUpdatingItems(prev => new Set(prev).add(itemId));
    
    try {
      // تحديث فوري في الواجهة للاستجابة السريعة
      setMenuItems(prev => prev.map(item => 
        item._id === itemId 
          ? { ...item, available: isAvailable, isAvailable: isAvailable }
          : item
      ));
      
      console.log('📡 Sending PUT request to update availability...');
      const response = await authenticatedPut(`/api/v1/products/${itemId}`, {
        isAvailable: isAvailable
      });

      console.log('📨 Response received:', response);

      if (response.success) {
        showSuccess(`تم ${isAvailable ? 'تفعيل' : 'إلغاء'} توفر المنتج بنجاح`);
        console.log('✅ Availability update successful, keeping local state');
        // إزالة العنصر من قائمة العناصر قيد التحديث فوراً
        setUpdatingItems(prev => {
          const newSet = new Set(prev);
          newSet.delete(itemId);
          return newSet;
        });
      } else {
        console.error('❌ Update failed:', response.message);
        showError(response.message || 'فشل في تحديث حالة التوفر');
        // إعادة القيمة السابقة محلياً في حالة الفشل
        setMenuItems(prev => prev.map(item => 
          item._id === itemId 
            ? { ...item, available: originalAvailability, isAvailable: originalAvailability }
            : item
        ));
        setUpdatingItems(prev => {
          const newSet = new Set(prev);
          newSet.delete(itemId);
          return newSet;
        });
      }
    } catch (error) {
      console.error('❌ خطأ في تحديث حالة التوفر:', error);
      showError('فشل في تحديث حالة التوفر');
      // إعادة القيمة السابقة محلياً في حالة الخطأ
      setMenuItems(prev => prev.map(item => 
        item._id === itemId 
          ? { ...item, available: originalAvailability, isAvailable: originalAvailability }
          : item
      ));
      setUpdatingItems(prev => {
        const newSet = new Set(prev);
        newSet.delete(itemId);
        return newSet;
      });
    }
  }, [fetchMenuItems, showSuccess, showError]);

  // Delete category
  const deleteCategory = useCallback(async (categoryId: string) => {
    try {
      const response = await authenticatedDelete(`/api/v1/categories/${categoryId}`);
      if (response.success) {
        showSuccess('تم حذف الفئة بنجاح');
        await fetchCategories();
      } else {
        showError(response.message || 'فشل في حذف الفئة');
      }
    } catch (error) {
      console.error('خطأ في حذف الفئة:', error);
      showError('فشل في حذف الفئة');
    }
  }, [fetchCategories, showSuccess, showError]);

  // Toggle category active status
  const toggleCategoryStatus = useCallback(async (categoryId: string, currentStatus: boolean) => {
    try {
      const response = await authenticatedPut(`/api/v1/categories/${categoryId}`, {
        isActive: !currentStatus
      });
      if (response.success) {
        showSuccess(`تم ${!currentStatus ? 'تفعيل' : 'إلغاء تفعيل'} الفئة بنجاح`);
        await fetchCategories();
      } else {
        showError(response.message || 'فشل في تحديث حالة الفئة');
      }
    } catch (error) {
      console.error('خطأ في تحديث حالة الفئة:', error);
      showError('فشل في تحديث حالة الفئة');
    }
  }, [fetchCategories, showSuccess, showError]);

  // Reset functions
  const resetOrders = useCallback(async () => {
    if (loading) {
      showError('يرجى انتظار انتهاء العملية الحالية');
      return;
    }
    
    if (window.confirm('هل أنت متأكد من حذف جميع الطلبات؟ هذا الإجراء لا يمكن التراجع عنه!')) {
      try {
        setLoading(true);
        const response = await authenticatedDelete('/api/v1/orders/reset-all');
        if (response.success) {
          showSuccess('تم حذف جميع الطلبات بنجاح');
          await fetchOrders();
        } else {
          showError('فشل في حذف الطلبات');
        }
      } catch (error) {
        console.error('خطأ في حذف الطلبات:', error);
        showError('فشل في حذف الطلبات');
      } finally {
        setLoading(false);
      }
    }
  }, [fetchOrders, showSuccess, showError]);

  const resetTables = useCallback(async () => {
    if (loading) {
      showError('يرجى انتظار انتهاء العملية الحالية');
      return;
    }
    
    if (window.confirm('هل أنت متأكد من إعادة تعيين جميع الطاولات؟ هذا الإجراء لا يمكن التراجع عنه!')) {
      try {
        setLoading(true);
        const response = await authenticatedDelete('/api/v1/table-accounts/reset-all');
        if (response.success) {
          showSuccess('تم إعادة تعيين جميع الطاولات بنجاح');
          await fetchTableAccounts();
        } else {
          showError('فشل في إعادة تعيين الطاولات');
        }
      } catch (error) {
        console.error('خطأ في إعادة تعيين الطاولات:', error);
        showError('فشل في إعادة تعيين الطاولات');
      } finally {
        setLoading(false);
      }
    }
  }, [fetchTableAccounts, showSuccess, showError]);

  const resetDiscountRequests = useCallback(async () => {
    if (loading) {
      showError('يرجى انتظار انتهاء العملية الحالية');
      return;
    }
    
    if (window.confirm('هل أنت متأكد من حذف جميع طلبات الخصم؟ هذا الإجراء لا يمكن التراجع عنه!')) {
      try {
        setLoading(true);
        const response = await authenticatedDelete('/api/v1/discount-requests/reset-all');
        if (response.success) {
          showSuccess('تم حذف جميع طلبات الخصم بنجاح');
          await fetchDiscountRequests();
        } else {
          showError('فشل في حذف طلبات الخصم');
        }
      } catch (error) {
        console.error('خطأ في حذف طلبات الخصم:', error);
        showError('فشل في حذف طلبات الخصم');
      } finally {
        setLoading(false);
      }
    }
  }, [fetchDiscountRequests, showSuccess, showError]);

  const resetAll = useCallback(async () => {
    if (loading) {
      showError('يرجى انتظار انتهاء العملية الحالية');
      return;
    }
    
    if (window.confirm('هل أنت متأكد من حذف جميع البيانات؟ هذا الإجراء لا يمكن التراجع عنه!')) {
      try {
        setLoading(true);
        
        // استدعاء API endpoints مباشرة بدون استدعاء دوال reset الفردية
        const [ordersResponse, tablesResponse, discountResponse] = await Promise.all([
          authenticatedDelete('/api/v1/orders/reset-all'),
          authenticatedDelete('/api/v1/table-accounts/reset-all'),
          authenticatedDelete('/api/v1/discount-requests/reset-all')
        ]);
        
        // التحقق من نجاح جميع العمليات
        const allSuccessful = ordersResponse.success && tablesResponse.success && discountResponse.success;
        
        if (allSuccessful) {
          // إعادة تحميل البيانات مرة واحدة فقط
          await Promise.all([
            fetchOrders(),
            fetchTableAccounts(),
            fetchDiscountRequests()
          ]);
          showSuccess('تم حذف جميع البيانات بنجاح');
        } else {
          showError('فشل في حذف بعض البيانات');
        }
      } catch (error) {
        console.error('خطأ في حذف البيانات:', error);
        showError('فشل في حذف البيانات');
      } finally {
        setLoading(false);
      }
    }
  }, [fetchOrders, fetchTableAccounts, fetchDiscountRequests, showSuccess, showError]);

  // Logout handler
  const handleLogout = useCallback(() => {
    if (window.confirm('هل أنت متأكد من تسجيل الخروج؟')) {
      localStorage.removeItem('token');
      localStorage.removeItem('user');
      localStorage.removeItem('username');
      if (onLogout) {
        onLogout();
      } else {
        window.location.href = '/login';
      }
    }
  }, [onLogout]);

  // Render Home Screen
  const renderHomeScreen = () => (
    <div className="homeScreen">
      <div className="homeScreen__header">
        <div className="homeScreen__welcome-section">
          <h1 className="homeScreen__title">
            <i className="homeScreen__title-icon fas fa-user-shield"></i>
            مرحباً، {managerName}
          </h1>
          <p className="homeScreen__role-badge">مدير</p>
        </div>
        
        <div className="homeScreen__control-buttons">
          <button
            className={`homeScreen__refresh-btn ${loading ? 'homeScreen__refresh-btn--loading' : ''}`}
            onClick={forceRefreshData}
            title="تحديث جميع البيانات"
            disabled={loading}
          >
            <i className="fas fa-sync-alt"></i> 
            {loading ? 'جاري التحديث...' : 'تحديث البيانات'}
          </button>
          
          {!isSocketConnected && (
            <div className={`homeScreen__connection-status ${isSocketConnected ? 'homeScreen__connection-status--online' : 'homeScreen__connection-status--offline'}`}>
              <i className={`homeScreen__connection-icon fas ${isSocketConnected ? 'fa-wifi' : 'fa-wifi-slash'}`}></i>
              {isSocketConnected ? 'متصل' : 'غير متصل'}
            </div>
          )}
          
          <div className="homeScreen__last-update">
            <i className="homeScreen__update-icon fas fa-clock"></i>
            آخر تحديث: {new Date(lastDataUpdate).toLocaleTimeString('ar-EG')}
          </div>
        </div>
      </div>

      {/* إحصائيات سريعة */}
      <div className="homeScreen__stats-grid">
        <div className="homeScreen__stat-card homeScreen__stat-card--orders">
          <div className="homeScreen__stat-icon">
            <i className="fas fa-shopping-cart"></i>
          </div>
          <div className="homeScreen__stat-content">
            <h3 className="homeScreen__stat-number">{stats.totalOrders}</h3>
            <p className="homeScreen__stat-label">إجمالي الطلبات</p>
          </div>
        </div>

        <div className="homeScreen__stat-card homeScreen__stat-card--sales">
          <div className="homeScreen__stat-icon">
            <i className="fas fa-money-bill-wave"></i>
          </div>
          <div className="homeScreen__stat-content">
            <h3 className="homeScreen__stat-number">{stats.totalSales.toFixed(2)}</h3>
            <p className="homeScreen__stat-label">إجمالي المبيعات (ج.م)</p>
          </div>
        </div>

        <div className="homeScreen__stat-card homeScreen__stat-card--employees">
          <div className="homeScreen__stat-icon">
            <i className="fas fa-users"></i>
          </div>
          <div className="homeScreen__stat-content">
            <h3 className="homeScreen__stat-number">{stats.activeEmployees}</h3>
            <p className="homeScreen__stat-label">الموظفون النشطون</p>
          </div>
        </div>

        <div className="homeScreen__stat-card homeScreen__stat-card--tables">
          <div className="homeScreen__stat-icon">
            <i className="fas fa-table"></i>
          </div>
          <div className="homeScreen__stat-content">
            <h3 className="homeScreen__stat-number">{stats.activeTables}</h3>
            <p className="homeScreen__stat-label">الطاولات النشطة</p>
          </div>
        </div>
      </div>

      {/* إحصائيات الطلبات المحسّنة */}
      <div className="homeScreen__orders-stats">
        <h2 className="homeScreen__orders-stats-title">
          <i className="fas fa-chart-line"></i>
          حالة الطلبات الحالية
          <span className="orders-total-badge">
            إجمالي: {stats.pendingOrders + stats.preparingOrders + stats.readyOrders + stats.completedOrders}
          </span>
        </h2>
        <div className="homeScreen__orders-stats-grid">
          <div 
            className="homeScreen__order-stat homeScreen__order-stat--pending"
            onClick={() => {
              setCurrentScreen('orders');
              setOrderStatusFilter('pending');
            }}
            title="اضغط لعرض الطلبات قيد الانتظار"
          >
            <div className="homeScreen__order-stat-icon-wrapper">
              <i className="fas fa-clock"></i>
              {stats.pendingOrders > 5 && (
                <span className="urgent-indicator">
                  <i className="fas fa-exclamation"></i>
                </span>
              )}
            </div>
            <div className="homeScreen__order-stat-details">
              <span className="homeScreen__order-stat-count">{stats.pendingOrders}</span>
              <span className="homeScreen__order-stat-label">قيد الانتظار</span>
              <div className="homeScreen__progress-bar">
                <div className="homeScreen__progress-fill homeScreen__progress-fill--pending"></div>
              </div>
              {stats.pendingOrders > 0 && (
                <div className="homeScreen__order-stat-alert">
                  <i className="fas fa-exclamation-triangle"></i>
                  {stats.pendingOrders > 5 ? 'عاجل - يتطلب معالجة فورية' : 'يتطلب معالجة'}
                </div>
              )}
            </div>
          </div>
          
          <div 
            className="homeScreen__order-stat homeScreen__order-stat--preparing"
            onClick={() => {
              setCurrentScreen('orders');
              setOrderStatusFilter('preparing');
            }}
            title="اضغط لعرض الطلبات قيد التحضير"
          >
            <div className="homeScreen__order-stat-icon-wrapper">
              <i className="fas fa-fire"></i>
              {stats.preparingOrders > 0 && (
                <span className="active-indicator">
                  <i className="fas fa-circle"></i>
                </span>
              )}
            </div>
            <div className="homeScreen__order-stat-details">
              <span className="homeScreen__order-stat-count">{stats.preparingOrders}</span>
              <span className="homeScreen__order-stat-label">قيد التحضير</span>
              <div className="homeScreen__progress-bar">
                <div className="homeScreen__progress-fill homeScreen__progress-fill--preparing"></div>
              </div>
              {stats.preparingOrders > 0 && (
                <div className="homeScreen__order-stat-working">
                  <i className="fas fa-spinner fa-spin"></i>
                  جاري التحضير - {stats.preparingOrders} طلب نشط
                </div>
              )}
            </div>
          </div>
          
          <div 
            className="homeScreen__order-stat homeScreen__order-stat--ready"
            onClick={() => {
              setCurrentScreen('orders');
              setOrderStatusFilter('ready');
            }}
            title="اضغط لعرض الطلبات جاهزة للتقديم"
          >
            <div className="homeScreen__order-stat-icon-wrapper">
              <i className="fas fa-check-circle"></i>
              {stats.readyOrders > 0 && (
                <span className="ready-indicator">
                  <i className="fas fa-bell"></i>
                </span>
              )}
            </div>
            <div className="homeScreen__order-stat-details">
              <span className="homeScreen__order-stat-count">{stats.readyOrders}</span>
              <span className="homeScreen__order-stat-label">جاهزة للتقديم</span>
              <div className="homeScreen__progress-bar">
                <div className="homeScreen__progress-fill homeScreen__progress-fill--ready"></div>
              </div>
              {stats.readyOrders > 0 && (
                <div className="homeScreen__order-stat-ready">
                  <i className="fas fa-utensils"></i>
                  جاهزة للتقديم - {stats.readyOrders} طلب في الانتظار
                </div>
              )}
            </div>
          </div>
          
          <div 
            className="homeScreen__order-stat homeScreen__order-stat--completed"
            onClick={() => {
              setCurrentScreen('orders');
              setOrderStatusFilter('completed');
            }}
            title="اضغط لعرض الطلبات المكتملة"
          >
            <div className="homeScreen__order-stat-icon-wrapper">
              <i className="fas fa-check-double"></i>
              {stats.completedOrders > 0 && (
                <span className="success-indicator">
                  <i className="fas fa-check"></i>
                </span>
              )}
            </div>
            <div className="homeScreen__order-stat-details">
              <span className="homeScreen__order-stat-count">{stats.completedOrders}</span>
              <span className="homeScreen__order-stat-label">مكتملة</span>
              <div className="homeScreen__progress-bar">
                <div className="homeScreen__progress-fill homeScreen__progress-fill--completed"></div>
              </div>
              {stats.completedOrders > 0 && (
                <div className="homeScreen__order-stat-completed">
                  <i className="fas fa-thumbs-up"></i>
                  مكتملة بنجاح - {stats.completedOrders} طلب اليوم
                </div>
              )}
            </div>
          </div>
        </div>
        
        {/* ملخص الطلبات اليومية المحسّن */}
        <div className="homeScreen__daily-orders-summary">
          <div 
            className="homeScreen__summary-card" 
            data-alert={stats.totalOrders === 0 ? "true" : "false"}
            title={`إجمالي ${stats.totalOrders} طلب اليوم`}
          >
            <i className="homeScreen__summary-card-icon fas fa-shopping-bag"></i>
            <div>
              <span className="homeScreen__summary-count">{stats.totalOrders}</span>
              <span className="homeScreen__summary-label">إجمالي طلبات اليوم</span>
            </div>
          </div>
          
          <div 
            className="homeScreen__summary-card"
            title={`إجمالي المبيعات: ${stats.totalSales?.toFixed(2) || '0.00'} جنيه مصري`}
          >
            <i className="homeScreen__summary-card-icon fas fa-money-bill-wave"></i>
            <div>
              <span className="homeScreen__summary-count">{stats.totalSales?.toFixed(2) || '0.00'}</span>
              <span className="homeScreen__summary-label">إجمالي المبيعات (ج.م)</span>
            </div>
          </div>
          
          <div 
            className="homeScreen__summary-card"
            title={`متوسط قيمة الطلب الواحد: ${stats.totalOrders > 0 ? Math.round(stats.totalSales / stats.totalOrders) : 0} جنيه`}
          >
            <i className="homeScreen__summary-card-icon fas fa-chart-line"></i>
            <div>
              <span className="homeScreen__summary-count">
                {stats.totalOrders > 0 ? Math.round(stats.totalSales / stats.totalOrders) : 0}
              </span>
              <span className="homeScreen__summary-label">متوسط قيمة الطلب (ج.م)</span>
            </div>
          </div>
          
          <div 
            className="homeScreen__summary-card"
            data-alert={(stats.pendingOrders + stats.preparingOrders) > 10 ? "true" : "false"}
            title={`${stats.pendingOrders + stats.preparingOrders} طلب نشط يحتاج متابعة`}
          >
            <i className="homeScreen__summary-card-icon fas fa-heartbeat"></i>
            <div>
              <span className="homeScreen__summary-count">
                {stats.pendingOrders + stats.preparingOrders}
              </span>
              <span className="homeScreen__summary-label">طلبات نشطة</span>
            </div>
          </div>
        </div>
      </div>

      {/* إحصائيات المنتجات الشائعة المحسّنة */}
      {stats.popularProducts && stats.popularProducts.length > 0 && (
        <div className="homeScreen__popular-products">
          <div className="section-header">
            <h2 className="homeScreen__popular-products-title">
              <i className="fas fa-trophy"></i>
              المنتجات الأكثر طلباً
            </h2>
            <div className="header-actions">
              <button
                className={`view-toggle ${showAllProducts ? 'active' : ''}`}
                onClick={() => setShowAllProducts(!showAllProducts)}
                title={showAllProducts ? 'عرض مُبسط' : 'عرض جميع المنتجات'}
              >
                <i className={`fas ${showAllProducts ? 'fa-compress' : 'fa-expand'}`}></i>
                {showAllProducts ? 'عرض مُبسط' : 'عرض الكل'}
              </button>
            </div>
          </div>
          
          <div className="homeScreen__popular-products-grid">
            {(() => {
              const productsToShow = showAllProducts 
                ? (stats.popularProducts || [])
                : (stats.popularProducts || []).slice(
                    (productsCurrentPage - 1) * productsPerPage,
                    productsCurrentPage * productsPerPage
                  );
              
              return productsToShow.map((product, index) => {
                const actualIndex = showAllProducts 
                  ? index 
                  : (productsCurrentPage - 1) * productsPerPage + index;
                
                const popularityPercentage = Math.min(100, (product.totalQuantity / (stats.popularProducts?.[0]?.totalQuantity || 1)) * 100);
                
                // تحديد نوع الشعبية للتصميم
                const popularityClass = popularityPercentage >= 80 ? 'excellent' : 
                                      popularityPercentage >= 50 ? 'good' : 'average';
                
                return (
                  <div
                    key={product.productId}
                    className="homeScreen__popular-product-card"
                  >
                    <div className="homeScreen__product-rank-badge">
                      <span className="rank-number">#{actualIndex + 1}</span>
                      <div className="rank-icon">
                        {actualIndex === 0 && <i className="fas fa-crown gold"></i>}
                        {actualIndex === 1 && <i className="fas fa-medal silver"></i>}
                        {actualIndex === 2 && <i className="fas fa-award bronze"></i>}
                        {actualIndex > 2 && <i className="fas fa-star"></i>}
                      </div>
                    </div>
                    
                    <div className="product-info enhanced">
                      <h4 className="homeScreen__product-name">{product.productName}</h4>
                      
                      <div className="homeScreen__product-stats">
                        <div className="homeScreen__stat-item primary">
                          <i className="fas fa-shopping-basket"></i>
                          <div className="stat-details">
                            <span className="homeScreen__stat-value">{product.totalQuantity}</span>
                            <span className="homeScreen__stat-label">قطعة مباعة</span>
                          </div>
                        </div>
                        
                        <div className="homeScreen__stat-item secondary">
                          <i className="fas fa-receipt"></i>
                          <div className="stat-details">
                            <span className="homeScreen__stat-value">{product.totalOrders}</span>
                            <span className="homeScreen__stat-label">طلب</span>
                          </div>
                        </div>
                        
                        <div className="homeScreen__stat-item tertiary">
                          <i className="fas fa-tag"></i>
                          <div className="stat-details">
                            <span className="homeScreen__stat-value">{product.category}</span>
                            <span className="homeScreen__stat-label">الفئة</span>
                          </div>
                        </div>
                      </div>
                      
                      {/* شريط شعبية المنتج المحسّن */}
                      <div className="homeScreen__popularity-bar">
                        <div 
                          className="homeScreen__popularity-fill" 
                          data-popularity={popularityPercentage.toFixed(0)}
                        ></div>
                        <span className={`homeScreen__popularity-text homeScreen__popularity-text--${popularityClass}`}>
                          {popularityPercentage.toFixed(0)}% شعبية
                        </span>
                      </div>
                    </div>
                  </div>
                );
              });
            })()}
          </div>
          
          {/* عناصر التحكم في pagination للمنتجات */}
          <div className="products-pagination-controls">
            <div className="pagination-info">
              <span className="total-products-count">
                <i className="fas fa-chart-bar"></i>
                إجمالي المنتجات: <strong>{(stats.popularProducts || []).length}</strong>
              </span>
              <div className="view-options">
                <button 
                  className={`view-option-btn ${showAllProducts ? 'active' : ''}`}
                  onClick={() => setShowAllProducts(!showAllProducts)}
                >
                  <i className="fas fa-list"></i>
                  {showAllProducts ? 'عرض بصفحات' : 'عرض الكل'}
                </button>
              </div>
            </div>
            
            {!showAllProducts && (
              <div className="pagination-controls">
                <div className="items-per-page">
                  <label>عدد المنتجات في الصفحة:</label>
                  <select 
                    value={productsPerPage} 
                    onChange={(e) => {
                      setProductsPerPage(Number(e.target.value));
                      setProductsCurrentPage(1);
                    }}
                    className="per-page-select"
                    aria-label="عدد المنتجات في الصفحة"
                  >
                    <option value={3}>3</option>
                    <option value={6}>6</option>
                    <option value={9}>9</option>
                    <option value={12}>12</option>
                  </select>
                </div>
                
                <div className="pagination-buttons">
                  {(() => {
                    const totalProducts = (stats.popularProducts || []).length;
                    const totalPages = Math.ceil(totalProducts / productsPerPage);
                    
                    return (
                      <div className="page-navigation">
                        <button
                          className="page-btn prev-btn"
                          onClick={() => setProductsCurrentPage(Math.max(1, productsCurrentPage - 1))}
                          disabled={productsCurrentPage === 1}
                        >
                          <i className="fas fa-chevron-right"></i>
                          السابق
                        </button>
                        
                        <div className="page-numbers">
                          {Array.from({ length: Math.min(5, totalPages) }, (_, i) => {
                            let pageNum;
                            if (totalPages <= 5) {
                              pageNum = i + 1;
                            } else if (productsCurrentPage <= 3) {
                              pageNum = i + 1;
                            } else if (productsCurrentPage >= totalPages - 2) {
                              pageNum = totalPages - 4 + i;
                            } else {
                              pageNum = productsCurrentPage - 2 + i;
                            }
                            
                            return (
                              <button
                                key={pageNum}
                                className={`page-number ${productsCurrentPage === pageNum ? 'active' : ''}`}
                                onClick={() => setProductsCurrentPage(pageNum)}
                              >
                                {pageNum}
                              </button>
                            );
                          })}
                        </div>
                        
                        <button
                          className="page-btn next-btn"
                          onClick={() => setProductsCurrentPage(Math.min(totalPages, productsCurrentPage + 1))}
                          disabled={productsCurrentPage === totalPages}
                        >
                          التالي
                          <i className="fas fa-chevron-left"></i>
                        </button>
                        
                        <div className="page-info">
                          صفحة {productsCurrentPage} من {totalPages}
                        </div>
                      </div>
                    );
                  })()}
                </div>
              </div>
            )}
          </div>
        </div>
      )}
    </div>
  );

  return (
    <div className="manager-dashboard">
      {/* Header Bar */}
      <header className="app-header">
        <div className="header-left">
          <button
            className="sidebar-toggle"
            onClick={(e) => {
              e.preventDefault();
              e.stopPropagation();
              setSidebarOpen(!sidebarOpen);
            }}
            onTouchStart={(e) => e.preventDefault()}
            title={sidebarOpen ? "إغلاق القائمة" : "فتح القائمة"}
          >
            <i className={`fas ${sidebarOpen ? 'fa-times' : 'fa-bars'}`}></i>
          </button>
          <h1 className="app-title">
            <i className="fas fa-coffee"></i>
            نظام إدارة المقهى
          </h1>
        </div>
        
        <div className="header-right">
          <button
            className="logout-button"
            onClick={handleLogout}
            title="تسجيل الخروج"
          >
            <i className="fas fa-sign-out-alt"></i>
            خروج
          </button>
        </div>
      </header>

      {/* Dashboard Content Container */}
      <div className={`dashboard-content ${sidebarOpen ? '' : 'sidebar-closed'}`}>
        {/* Sidebar Overlay */}
        {sidebarOpen && isMobile && (
          <div 
            className="sidebar-overlay active"
            onClick={(e) => {
              e.preventDefault();
              e.stopPropagation();
              setSidebarOpen(false);
            }}
            onTouchStart={(e) => e.preventDefault()}
          />
        )}

        {/* Sidebar */}
        <div className={`manager-sidebar ${sidebarOpen ? 'open' : 'closed'}`}>
        <div className="sidebar-content">
          <div className="sidebar-header">
            <div className="manager-profile">
              <div className="manager-avatar">
                <i className="fas fa-user-shield"></i>
              </div>
              <div className="manager-info">
                <h3>{managerName}</h3>
                <span className="role">مدير</span>
              </div>
            </div>

            {isMobile && (
              <button
                className="sidebar-close-btn"
                onClick={(e) => {
                  e.preventDefault();
                  e.stopPropagation();
                  setSidebarOpen(false);
                }}
                onTouchStart={(e) => e.preventDefault()}
                title="إغلاق القائمة"
              >
                <i className="fas fa-times"></i>
              </button>
            )}
          </div>

          <nav className="sidebar-nav">
            <button
              className={`nav-btn ${currentScreen === 'home' ? 'active' : ''}`}
              onClick={() => changeScreen('home')}
            >
              <i className="fas fa-home"></i>
              <span>الرئيسية</span>
            </button>

            <button
              className={`nav-btn ${currentScreen === 'orders' ? 'active' : ''}`}
              onClick={() => changeScreen('orders')}
            >
              <i className="fas fa-shopping-cart"></i>
              <span>الطلبات</span>
              {stats.pendingOrders > 0 && (
                <span className="badge">{stats.pendingOrders}</span>
              )}
            </button>

            <button
              className={`nav-btn ${currentScreen === 'discount-requests' ? 'active' : ''}`}
              onClick={() => changeScreen('discount-requests')}
            >
              <i className="fas fa-percentage"></i>
              <span>طلبات الخصم</span>
              {discountRequests.filter(req => req.status === 'pending').length > 0 && (
                <span className="badge">
                  {discountRequests.filter(req => req.status === 'pending').length}
                </span>
              )}
            </button>

            <button
              className={`nav-btn ${currentScreen === 'employees' ? 'active' : ''}`}
              onClick={() => changeScreen('employees')}
            >
              <i className="fas fa-users"></i>
              <span>الموظفون</span>
            </button>

            <button
              className={`nav-btn ${currentScreen === 'tables' ? 'active' : ''}`}
              onClick={() => changeScreen('tables')}
            >
              <i className="fas fa-table"></i>
              <span>الطاولات</span>
              {stats.activeTables > 0 && (
                <span className="badge active">{stats.activeTables}</span>
              )}
            </button>

            <button
              className={`nav-btn ${currentScreen === 'reports' ? 'active' : ''}`}
              onClick={() => changeScreen('reports')}
            >
              <i className="fas fa-chart-bar"></i>
              <span>التقارير</span>
            </button>

            <button
              className={`nav-btn ${currentScreen === 'inventory' ? 'active' : ''}`}
              onClick={() => changeScreen('inventory')}
            >
              <i className="fas fa-boxes"></i>
              <span>المخزون</span>
            </button>

            <button
              className={`nav-btn ${currentScreen === 'menu' ? 'active' : ''}`}
              onClick={() => changeScreen('menu')}
            >
              <i className="fas fa-coffee"></i>
              <span>القائمة</span>
            </button>

            <button
              className={`nav-btn ${currentScreen === 'categories' ? 'active' : ''}`}
              onClick={() => changeScreen('categories')}
            >
              <i className="fas fa-tags"></i>
              <span>الفئات</span>
            </button>

            <button
              className={`nav-btn ${currentScreen === 'settings' ? 'active' : ''}`}
              onClick={() => changeScreen('settings')}
            >
              <i className="fas fa-cogs"></i>
              <span>الإعدادات</span>
            </button>
          </nav>

          <div className="sidebar-footer">
            {!isSocketConnected && (
              <div className="connection-status">
                <div className={`socket-indicator ${isSocketConnected ? 'connected' : 'disconnected'}`}>
                  <span className="socket-dot"></span>
                  <span className="socket-text">
                    {isSocketConnected ? 'متصل فورياً' : 'غير متصل'}
                  </span>
                </div>
              </div>
            )}
          </div>
        </div>
      </div>

      {/* Overlay for mobile when sidebar is open */}
      {isMobile && sidebarOpen && (
        <div 
          className="sidebar-overlay active" 
          onClick={() => setSidebarOpen(false)}
        ></div>
      )}

      {/* Main Content */}
      <main className={`manager-main ${sidebarOpen ? 'sidebar-open' : ''}`}>
        <div className="screen-content">
          {loading ? (
            <div className="loading">
              <i className="fas fa-spinner fa-spin"></i>
              <span>جاري تحميل البيانات...</span>
            </div>
          ) : (
            <>
              {currentScreen === 'home' && renderHomeScreen()}
              {currentScreen === 'orders' && (
                <OrdersManagerScreenBootstrap 
                  orders={orders}
                  employees={employees}
                onOrdersUpdate={fetchOrders}
                loading={loading}
              />
            )}
            {currentScreen === 'discount-requests' && (
              <DiscountRequestsManagerScreenBootstrap 
                discountRequests={discountRequests}
                managerId={managerId}
                selectedDiscountForDetails={selectedDiscountForDetails}
                showDiscountDetailsModal={showDiscountDetailsModal}
                setSelectedDiscountForDetails={setSelectedDiscountForDetails}
                setShowDiscountDetailsModal={setShowDiscountDetailsModal}
                fetchDiscountRequests={fetchDiscountRequests}
                showSuccess={showSuccess}
                showError={showError}
              />
            )}
            {currentScreen === 'employees' && (
              <EmployeesManagerScreenBootstrap 
                employees={employees}
                onEmployeesUpdate={fetchEmployees}
                loading={loading}
              />
            )}
            {currentScreen === 'tables' && (
              <TablesManagerScreenBootstrap 
                tableAccounts={tableAccounts}
                orders={orders}
                onTablesUpdate={fetchTableAccounts}
                onShowTableDetails={(table: any) => {
                  console.log('عرض تفاصيل الطاولة:', table);
                  setSelectedTableForDetails(table);
                  setShowTableDetailsModal(true);
                }}
                loading={loading}
              />
            )}
            {currentScreen === 'reports' && (
              <ReportsManagerScreenBootstrap 
                stats={stats as any}
                orders={orders}
                loading={loading}
              />
            )}
            {currentScreen === 'menu' && (
              <MenuManagerScreenBootstrap 
                menuItems={menuItems as any}
                categories={categories as any}
                onMenuItemsUpdate={() => {
                  fetchMenuItems();
                  fetchCategories();
                }}
                loading={loading}
              />
            )}
            {currentScreen === 'inventory' && (
              <InventoryManagerScreenBootstrap 
                menuItems={menuItems as any}
                isLoading={loading}
                loadingStates={{
                  inventory: loading
                }}
                updatingItems={updatingItems}
                onUpdateStock={updateItemStock}
                onToggleAvailability={updateItemAvailability}
              />
            )}
            {currentScreen === 'categories' && (
              <CategoriesManagerScreenBootstrap 
                categories={categories}
                menuItems={menuItems as any}
                selectedCategory={selectedCategory}
                showCategoryModal={showCategoryModal}
                setSelectedCategory={setSelectedCategory}
                setShowCategoryModal={setShowCategoryModal}
                deleteCategory={deleteCategory}
                toggleCategoryStatus={toggleCategoryStatus}
              />
            )}
            {currentScreen === 'settings' && (
              <SettingsManagerScreenBootstrap 
                resetOrders={resetOrders}
                resetTables={resetTables}
                resetDiscountRequests={resetDiscountRequests}
                resetAll={resetAll}
              />
            )}
          </>
        )}
        </div>
      </main>

      {/* Sales Discrepancy Modal */}
      {showSalesDiscrepancyModal && (
        <SalesDiscrepancyFixer
          onClose={() => setShowSalesDiscrepancyModal(false)}
        />
      )}

      {/* Order Details Modal */}
      {showOrderDetailsModal && selectedOrder && (
        <div className="modal-overlay" onClick={closeOrderDetailsModal}>
          <div className="order-details-modal" onClick={(e) => e.stopPropagation()}>
            <div className="order-header">
              <button 
                className="close-btn"
                onClick={closeOrderDetailsModal}
                title="إغلاق"
              >
                <i className="fas fa-times"></i>
              </button>
              <div className="order-header-content">
                <h3>
                  <i className="fas fa-receipt"></i>
                  تفاصيل الطلب #{selectedOrder.orderNumber}
                </h3>
              </div>
            </div>
            
            <div className="order-body">
              <div className="order-details-grid">
                <div className="detail-group">
                  <h4>المعلومات الأساسية</h4>
                  <div className="detail-item">
                    <span className="label">رقم الطلب:</span>
                    <span className="value">{selectedOrder.orderNumber}</span>
                  </div>
                  <div className="detail-item">
                    <span className="label">رقم الطاولة:</span>
                    <span className="value">{selectedOrder.tableNumber || 'غير محدد'}</span>
                  </div>
                  <div className="detail-item">
                    <span className="label">اسم العميل:</span>
                    <span className="value">{selectedOrder.customerName || 'غير محدد'}</span>
                  </div>
                  <div className="detail-item">
                    <span className="label">الحالة:</span>
                    <span className={`value status-${selectedOrder.status}`}>
                      {selectedOrder.status === 'pending' && 'قيد الانتظار'}
                      {selectedOrder.status === 'preparing' && 'قيد التحضير'}
                      {selectedOrder.status === 'ready' && 'جاهز'}
                      {selectedOrder.status === 'completed' && 'مكتمل'}
                      {selectedOrder.status === 'delivered' && 'تم التسليم'}
                    </span>
                  </div>
                </div>

                <div className="detail-group">
                  <h4>معلومات الموظفين</h4>
                  <div className="detail-item">
                    <span className="label">النادل:</span>
                    <span className="value">{selectedOrder.waiterName || selectedOrder.staff?.waiter || 'غير محدد'}</span>
                  </div>
                  <div className="detail-item">
                    <span className="label">الطباخ:</span>
                    <span className="value">{selectedOrder.chefName || selectedOrder.staff?.chef || 'غير محدد'}</span>
                  </div>
                </div>

                <div className="detail-group">
                  <h4>التواريخ</h4>
                  <div className="detail-item">
                    <span className="label">تاريخ الإنشاء:</span>
                    <span className="value">
                      {new Date(selectedOrder.createdAt).toLocaleDateString('ar-SA', {
                        year: 'numeric',
                        month: 'long',
                        day: 'numeric',
                        hour: '2-digit',
                        minute: '2-digit'
                      })}
                    </span>
                  </div>
                  <div className="detail-item">
                    <span className="label">آخر تحديث:</span>
                    <span className="value">
                      {new Date(selectedOrder.updatedAt).toLocaleDateString('ar-SA', {
                        year: 'numeric',
                        month: 'long',
                        day: 'numeric',
                        hour: '2-digit',
                        minute: '2-digit'
                      })}
                    </span>
                  </div>
                </div>

                <div className="detail-group full-width">
                  <h4>عناصر الطلب</h4>
                  <div className="order-items-list">
                    {selectedOrder.items && selectedOrder.items.length > 0 ? (
                      selectedOrder.items.map((item, index) => (
                        <div key={index} className="order-item">
                          <div className="item-info">
                            <span className="item-name">{item.name || 'عنصر غير محدد'}</span>
                            <span className="item-quantity">الكمية: {item.quantity || 1}</span>
                          </div>
                          <div className="item-price">
                            {item.price ? `${Number(item.price).toFixed(2)} ج.م` : 'غير محدد'}
                          </div>
                        </div>
                      ))
                    ) : (
                      <div className="no-items">لا توجد عناصر في هذا الطلب</div>
                    )}
                  </div>
                </div>

                <div className="detail-group">
                  <h4>المبالغ المالية</h4>
                  {selectedOrder.totals ? (
                    <>
                      <div className="detail-item">
                        <span className="label">المجموع الفرعي:</span>
                        <span className="value">{Number(selectedOrder.totals.subtotal || 0).toFixed(2)} ج.م</span>
                      </div>
                      <div className="detail-item">
                        <span className="label">الضريبة:</span>
                        <span className="value">{Number(selectedOrder.totals.tax || 0).toFixed(2)} ج.م</span>
                      </div>
                      <div className="detail-item">
                        <span className="label">الخصم:</span>
                        <span className="value">{Number(selectedOrder.totals.discount || 0).toFixed(2)} ج.م</span>
                      </div>
                      <div className="detail-item total">
                        <span className="label">المجموع النهائي:</span>
                        <span className="value">{Number(selectedOrder.totals.total || 0).toFixed(2)} ج.م</span>
                      </div>
                    </>
                  ) : (
                    <div className="detail-item total">
                      <span className="label">المجموع:</span>
                      <span className="value">{Number(selectedOrder.totalAmount || 0).toFixed(2)} ج.م</span>
                    </div>
                  )}
                </div>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Discount Request Details Modal */}
      {showDiscountDetailsModal && selectedDiscountForDetails && (
        <div className="modal-overlay enhanced-modal-overlay" onClick={closeDiscountDetailsModal}>
          <div className="discount-details-modal enhanced-modal" onClick={(e) => e.stopPropagation()}>
            {/* Header */}
            <div className="discount-modal-header enhanced-modal-header">
              <div className="modal-header-content">
                <div className="modal-icon">
                  <i className="fas fa-percentage"></i>
                </div>
                <div className="modal-title-section">
                  <h3 className="modal-title">تفاصيل طلب الخصم</h3>
                  <p className="modal-subtitle">طلب #{selectedDiscountForDetails.orderNumber}</p>
                </div>
              </div>
              <button 
                className="enhanced-close-btn"
                onClick={closeDiscountDetailsModal}
                title="إغلاق"
              >
                <i className="fas fa-times"></i>
              </button>
            </div>
            
            {/* Body */}
            <div className="discount-modal-body enhanced-modal-body">
              <div className="discount-details-grid">
                
                {/* Status Card */}
                <div className="detail-card status-card">
                  <div className="card-header">
                    <i className="fas fa-info-circle"></i>
                    <span>حالة الطلب</span>
                  </div>
                  <div className="card-content">
                    <div className={`status-badge status-${selectedDiscountForDetails.status}`}>
                      <i className={`fas ${
                        selectedDiscountForDetails.status === 'pending' ? 'fa-clock' :
                        selectedDiscountForDetails.status === 'approved' ? 'fa-check-circle' :
                        'fa-times-circle'
                      }`}></i>
                      <span>
                        {selectedDiscountForDetails.status === 'pending' && 'قيد الانتظار'}
                        {selectedDiscountForDetails.status === 'approved' && 'مقبول'}
                        {selectedDiscountForDetails.status === 'rejected' && 'مرفوض'}
                      </span>
                    </div>
                  </div>
                </div>

                {/* Order Info Card */}
                <div className="detail-card order-info-card">
                  <div className="card-header">
                    <i className="fas fa-receipt"></i>
                    <span>معلومات الطلب</span>
                  </div>
                  <div className="card-content">
                    <div className="info-grid">
                      <div className="info-item">
                        <span className="label">رقم الطلب:</span>
                        <span className="value">{selectedDiscountForDetails.orderNumber}</span>
                      </div>
                      <div className="info-item">
                        <span className="label">رقم الطاولة:</span>
                        <span className="value">
                          {selectedDiscountForDetails.order?.tableNumber || 
                           selectedDiscountForDetails.tableNumber || 
                           'غير محدد'}
                        </span>
                      </div>
                      <div className="info-item">
                        <span className="label">اسم العميل:</span>
                        <span className="value">{selectedDiscountForDetails.customerName || 'غير محدد'}</span>
                      </div>
                      <div className="info-item">
                        <span className="label">النادل:</span>
                        <span className="value">{selectedDiscountForDetails.waiterName || 'غير محدد'}</span>
                      </div>
                      <div className="info-item">
                        <span className="label">تاريخ الطلب:</span>
                        <span className="value">
                          {new Date(selectedDiscountForDetails.createdAt).toLocaleDateString('ar-SA', {
                            year: 'numeric',
                            month: 'long',
                            day: 'numeric',
                            hour: '2-digit',
                            minute: '2-digit'
                          })}
                        </span>
                      </div>
                    </div>
                  </div>
                </div>

                {/* Amount Details Card */}
                <div className="detail-card amount-card">
                  <div className="card-header">
                    <i className="fas fa-calculator"></i>
                    <span>تفاصيل المبالغ</span>
                  </div>
                  <div className="card-content">
                    <div className="amount-breakdown">
                      <div className="amount-row original">
                        <span className="amount-label">المبلغ الأصلي:</span>
                        <span className="amount-value">
                          {selectedDiscountForDetails.originalAmount 
                            ? `${Number(selectedDiscountForDetails.originalAmount).toFixed(2)} ج.م`
                            : (selectedDiscountForDetails.order?.totals?.total 
                              ? `${Number(selectedDiscountForDetails.order.totals.total).toFixed(2)} ج.م`
                              : (selectedDiscountForDetails.order?.totalAmount 
                                ? `${Number(selectedDiscountForDetails.order.totalAmount).toFixed(2)} ج.م`
                                : 'غير محدد'))}
                        </span>
                      </div>
                      <div className="amount-row discount">
                        <span className="amount-label">مبلغ الخصم:</span>
                        <span className="amount-value discount-amount">
                          -{selectedDiscountForDetails.amount !== undefined 
                            ? `${Number(selectedDiscountForDetails.amount).toFixed(2)} ج.م`
                            : (selectedDiscountForDetails.requestedDiscount !== undefined
                              ? `${Number(selectedDiscountForDetails.requestedDiscount).toFixed(2)} ج.م`
                              : 'غير محدد')}
                        </span>
                      </div>
                      <div className="amount-separator"></div>
                      <div className="amount-row final">
                        <span className="amount-label">المبلغ النهائي:</span>
                        <span className="amount-value final-amount">
                          {(() => {
                            const original = selectedDiscountForDetails.originalAmount || 
                                           selectedDiscountForDetails.order?.totals?.total || 
                                           selectedDiscountForDetails.order?.totalAmount || 0;
                            const discount = selectedDiscountForDetails.amount || 
                                           selectedDiscountForDetails.requestedDiscount || 0;
                            const final = original - discount;
                            return `${Number(final).toFixed(2)} ج.م`;
                          })()}
                        </span>
                      </div>
                    </div>
                  </div>
                </div>

                {/* Manager Decision Section */}
                {(selectedDiscountForDetails.status === 'approved' || selectedDiscountForDetails.status === 'rejected') && (
                  <div className="detail-card manager-decision-card">
                    <div className="card-header">
                      <i className="fas fa-user-tie"></i>
                      <span>{selectedDiscountForDetails.status === 'approved' ? 'تمت الموافقة من قبل' : 'تم الرفض من قبل'}</span>
                    </div>
                    <div className="card-content">
                      <div className="info-grid">
                        <div className="info-item">
                          <span className="label">اسم المدير:</span>
                          <span className="value manager-name">
                            {selectedDiscountForDetails.status === 'approved'
                              ? (selectedDiscountForDetails.approvedByName || selectedDiscountForDetails.approvedBy || 'غير محدد')
                              : selectedDiscountForDetails.status === 'rejected'
                              ? (selectedDiscountForDetails.rejectedByName || selectedDiscountForDetails.rejectedBy || 'غير محدد')
                              : 'غير محدد'}
                          </span>
                        </div>
                        {selectedDiscountForDetails.status === 'approved' && selectedDiscountForDetails.approvedByUsername && (
                          <div className="info-item">
                            <span className="label">اسم المستخدم:</span>
                            <span className="value">{selectedDiscountForDetails.approvedByUsername}</span>
                          </div>
                        )}
                        {selectedDiscountForDetails.status === 'rejected' && selectedDiscountForDetails.rejectedByUsername && (
                          <div className="info-item">
                            <span className="label">اسم المستخدم:</span>
                            <span className="value">{selectedDiscountForDetails.rejectedByUsername}</span>
                          </div>
                        )}
                        <div className="info-item">
                          <span className="label">وقت الرد:</span>
                          <span className="value">
                            {selectedDiscountForDetails.updatedAt
                              ? new Date(selectedDiscountForDetails.updatedAt).toLocaleDateString('ar-SA', {
                                  year: 'numeric',
                                  month: 'long',
                                  day: 'numeric',
                                  hour: '2-digit',
                                  minute: '2-digit'
                                })
                              : 'غير محدد'}
                          </span>
                        </div>
                        <div className="info-item">
                          <span className="label">الإجراء المتخذ:</span>
                          <span className={`value action-badge ${selectedDiscountForDetails.status}`}>
                            <i className={`fas ${selectedDiscountForDetails.status === 'approved' ? 'fa-check-circle' : 'fa-times-circle'}`}></i>
                            {selectedDiscountForDetails.status === 'approved' ? 'تم قبول طلب الخصم' : 'تم رفض طلب الخصم'}
                          </span>
                        </div>
                      </div>
                    </div>
                  </div>
                )}

                {/* Reason Section */}
                {selectedDiscountForDetails.reason && (
                  <div className="reason-section">
                    <div className="reason-header">
                      <i className="fas fa-comment-dots"></i>
                      <span>سبب طلب الخصم</span>
                    </div>
                    <p className="reason-text">{selectedDiscountForDetails.reason}</p>
                  </div>
                )}

                {/* Order Items Section */}
                {selectedDiscountForDetails.order && selectedDiscountForDetails.order.items && (
                  <div className="order-items-section">
                    <div className="section-header">
                      <i className="fas fa-utensils"></i>
                      <span>أصناف الطلب ({selectedDiscountForDetails.order.items.length} صنف)</span>
                    </div>
                    <table className="order-items-table">
                      <thead>
                        <tr>
                          <th>الصنف</th>
                          <th>الكمية</th>
                          <th>السعر</th>
                          <th>المجموع</th>
                        </tr>
                      </thead>
                      <tbody>
                        {selectedDiscountForDetails.order.items.map((item, index) => (
                          <tr key={index}>
                            <td>
                              <div className="item-name">{item.name || item.productName || 'صنف غير محدد'}</div>
                              {item.size && <div className="item-size">الحجم: {item.size}</div>}
                              {item.notes && <div className="item-notes">ملاحظات: {item.notes}</div>}
                            </td>
                            <td className="item-quantity">{item.quantity || 1}</td>
                            <td className="item-price">
                              {item.price !== undefined ? `${Number(item.price).toFixed(2)} ج.م` : 'غير محدد'}
                            </td>
                            <td className="item-price">
                              {item.price !== undefined && item.quantity
                                ? `${(Number(item.price) * Number(item.quantity)).toFixed(2)} ج.م`
                                : 'غير محدد'}
                            </td>
                          </tr>
                        ))}
                      </tbody>
                      <tfoot>
                        <tr>
                          <td colSpan={3}><strong>إجمالي الطلب:</strong></td>
                          <td><strong>
                            {selectedDiscountForDetails.order.items.reduce((total, item) => {
                              const itemTotal = (Number(item.price) || 0) * (Number(item.quantity) || 1);
                              return total + itemTotal;
                            }, 0).toFixed(2)} ج.م
                          </strong></td>
                        </tr>
                      </tfoot>
                    </table>
                  </div>
                )}

                {!selectedDiscountForDetails.order?.items && selectedDiscountForDetails.order && (
                  <div className="detail-group full-width">
                    <h4>أصناف الطلب</h4>
                    <div className="no-items">
                      <i className="fas fa-exclamation-triangle"></i>
                      لا توجد تفاصيل الأصناف متاحة لهذا الطلب
                    </div>
                  </div>
                )}

                {selectedDiscountForDetails.order && (
                  <div className="detail-group full-width">
                    <h4>تفاصيل الطلب الأصلي</h4>
                    <div className="linked-order-info">
                      <div className="detail-item">
                        <span className="label">حالة الطلب:</span>
                        <span className={`value status-${selectedDiscountForDetails.order.status}`}>
                          {selectedDiscountForDetails.order.status === 'pending' && 'قيد الانتظار'}
                          {selectedDiscountForDetails.order.status === 'preparing' && 'قيد التحضير'}
                          {selectedDiscountForDetails.order.status === 'ready' && 'جاهز'}
                          {selectedDiscountForDetails.order.status === 'completed' && 'مكتمل'}
                          {selectedDiscountForDetails.order.status === 'delivered' && 'تم التسليم'}
                        </span>
                      </div>
                      <div className="detail-item">
                        <span className="label">النادل المسؤول:</span>
                        <span className="value">{selectedDiscountForDetails.order.waiterName || 'غير محدد'}</span>
                      </div>
                    </div>
                  </div>
                )}

                <div className="detail-group">
                  <h4>التواريخ</h4>
                  <div className="detail-item">
                    <span className="label">تاريخ طلب الخصم:</span>
                    <span className="value">
                      {new Date(selectedDiscountForDetails.createdAt).toLocaleDateString('ar-SA', {
                        year: 'numeric',
                        month: 'long',
                        day: 'numeric',
                        hour: '2-digit',
                        minute: '2-digit'
                      })}
                    </span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Table Details Modal */}
      {showTableDetailsModal && selectedTableForDetails && (
        <div className="modal-overlay" onClick={() => setShowTableDetailsModal(false)}>
          <div className="modal-content table-details-modal" onClick={e => e.stopPropagation()}>
            <div className="modal-header">
              <h3>
                <i className="fas fa-table"></i>
                تفاصيل الطاولة رقم {selectedTableForDetails.tableNumber}
              </h3>
              <button 
                className="modal-close"
                onClick={() => setShowTableDetailsModal(false)}
                title="إغلاق النافذة"
                aria-label="إغلاق نافذة تفاصيل الطاولة"
              >
                <i className="fas fa-times"></i>
              </button>
            </div>
            
            <div className="modal-body">
              <div className="table-details-content">
                {/* Header Card - معلومات سريعة */}
                <div className="table-header-card">
                  <div className="table-info-primary">
                    <div className="table-number-display">
                      <i className="fas fa-table"></i>
                      <span className="table-number">طاولة {selectedTableForDetails.tableNumber}</span>
                      <span className={`table-status-badge ${selectedTableForDetails.isOpen ? 'active' : 'closed'}`}>
                        <i className={`fas ${selectedTableForDetails.isOpen ? 'fa-circle' : 'fa-circle'}`}></i>
                        {selectedTableForDetails.isOpen ? 'مفتوحة' : 'مغلقة'}
                      </span>
                    </div>
                    <div className="table-summary">
                      <div className="summary-item">
                        <i className="fas fa-user-tie"></i>
                        <span>النادل: {selectedTableForDetails.waiterName || selectedTableForDetails.waiter?.name || 'غير محدد'}</span>
                      </div>
                      <div className="summary-item">
                        <i className="fas fa-building"></i>
                        <span>القسم: {selectedTableForDetails.section || 'غير محدد'}</span>
                      </div>
                      {selectedTableForDetails.isOpen && selectedTableForDetails.customer && (
                        <div className="summary-item">
                          <i className="fas fa-user"></i>
                          <span>العميل: {selectedTableForDetails.customer.name || selectedTableForDetails.customerName || 'غير محدد'}</span>
                        </div>
                      )}
                    </div>
                  </div>
                </div>

                {/* Financial Overview - نظرة عامة مالية */}
                <div className="financial-overview">
                  <div className="overview-cards">
                    <div className="overview-card current-amount">
                      <div className="card-icon">
                        <i className="fas fa-coins"></i>
                      </div>
                      <div className="card-content">
                        <h4>المبلغ الحالي</h4>
                        <p className="amount">
                          {selectedTableForDetails.totalAmount 
                            ? `${Number(selectedTableForDetails.totalAmount).toFixed(2)} ج.م`
                            : '0.00 ج.م'}
                        </p>
                        <small>من الطلبات النشطة</small>
                      </div>
                    </div>
                    <div className="overview-card total-sales">
                      <div className="card-icon">
                        <i className="fas fa-chart-line"></i>
                      </div>
                      <div className="card-content">
                        <h4>إجمالي المبيعات</h4>
                        <p className="amount">
                          {selectedTableForDetails.totalSales 
                            ? `${Number(selectedTableForDetails.totalSales).toFixed(2)} ج.م`
                            : '0.00 ج.م'}
                        </p>
                        <small>من جميع الطلبات</small>
                      </div>
                    </div>
                    <div className="overview-card orders-count">
                      <div className="card-icon">
                        <i className="fas fa-receipt"></i>
                      </div>
                      <div className="card-content">
                        <h4>الطلبات</h4>
                        <p className="count">
                          {selectedTableForDetails.activeOrdersCount || 0} نشط
                        </p>
                        <small>من إجمالي {selectedTableForDetails.ordersCount || 0}</small>
                      </div>
                    </div>
                  </div>
                </div>

                {/* Customer Information - معلومات العميل */}
                {selectedTableForDetails.isOpen && selectedTableForDetails.customer && (
                  <div className="detail-group customer-info">
                    <h4>
                      <i className="fas fa-user-circle"></i>
                      معلومات العميل
                    </h4>
                    <div className="customer-card">
                      <div className="customer-details">
                        <div className="detail-row">
                          <span className="label">
                            <i className="fas fa-user"></i>
                            الاسم
                          </span>
                          <span className="value">{selectedTableForDetails.customer.name || selectedTableForDetails.customerName || 'غير محدد'}</span>
                        </div>
                        <div className="detail-row">
                          <span className="label">
                            <i className="fas fa-phone"></i>
                            الهاتف
                          </span>
                          <span className="value">{selectedTableForDetails.customer.phone || 'غير محدد'}</span>
                        </div>
                        {selectedTableForDetails.customer.email && (
                          <div className="detail-row">
                            <span className="label">
                              <i className="fas fa-envelope"></i>
                              البريد الإلكتروني
                            </span>
                            <span className="value">{selectedTableForDetails.customer.email}</span>
                          </div>
                        )}
                      </div>
                    </div>
                  </div>
                )}

                {/* Active Orders - الطلبات النشطة */}
                {selectedTableForDetails.orders && selectedTableForDetails.orders.length > 0 && (
                  <div className="detail-group orders-section">
                    <h4>
                      <i className="fas fa-list-alt"></i>
                      الطلبات النشطة ({selectedTableForDetails.orders.length})
                    </h4>
                    <div className="orders-grid">
                      {selectedTableForDetails.orders.map((order: any, index: number) => (
                        <div key={index} className="order-card">
                          <div className="order-header">
                            <div className="order-number">
                              <i className="fas fa-receipt"></i>
                              <span>{order.orderNumber || `طلب ${index + 1}`}</span>
                            </div>
                            <div className={`order-status status-${order.status}`}>
                              <i className={`fas ${
                                order.status === 'pending' ? 'fa-clock' :
                                order.status === 'preparing' ? 'fa-fire' :
                                order.status === 'ready' ? 'fa-check-circle' :
                                'fa-check-double'
                              }`}></i>
                              {order.status === 'pending' && 'قيد الانتظار'}
                              {order.status === 'preparing' && 'قيد التحضير'}
                              {order.status === 'ready' && 'جاهز'}
                              {order.status === 'completed' && 'مكتمل'}
                            </div>
                          </div>
                          <div className="order-body">
                            <div className="order-info">
                              <div className="info-item">
                                <i className="fas fa-utensils"></i>
                                <span>الأصناف: {order.items?.length || 0}</span>
                              </div>
                              <div className="info-item">
                                <i className="fas fa-money-bill-wave"></i>
                                <span className="order-amount">
                                  {order.totalPrice 
                                    ? `${Number(order.totalPrice).toFixed(2)} ج.م`
                                    : '0.00 ج.م'}
                                </span>
                              </div>
                            </div>
                            <div className="order-time">
                              <i className="fas fa-clock"></i>
                              <span>
                                {order.createdAt 
                                  ? new Date(order.createdAt).toLocaleString('ar-SA', {
                                      month: 'short',
                                      day: 'numeric',
                                      hour: '2-digit',
                                      minute: '2-digit'
                                    })
                                  : 'غير محدد'}
                              </span>
                            </div>
                          </div>
                        </div>
                      ))}
                    </div>
                  </div>
                )}

                {/* Table Details - تفاصيل الطاولة */}
                <div className="detail-group table-details">
                  <h4>
                    <i className="fas fa-info-circle"></i>
                    تفاصيل الطاولة
                  </h4>
                  <div className="details-grid">
                    <div className="detail-card">
                      <div className="detail-row">
                        <span className="label">
                          <i className="fas fa-hashtag"></i>
                          رقم الطاولة
                        </span>
                        <span className="value">{selectedTableForDetails.tableNumber}</span>
                      </div>
                      <div className="detail-row">
                        <span className="label">
                          <i className="fas fa-building"></i>
                          القسم
                        </span>
                        <span className="value">{selectedTableForDetails.section || 'غير محدد'}</span>
                      </div>
                      <div className="detail-row">
                        <span className="label">
                          <i className="fas fa-user-tie"></i>
                          النادل المسؤول
                        </span>
                        <span className="value waiter-name">
                          {selectedTableForDetails.waiterName || selectedTableForDetails.waiter?.name || 'غير محدد'}
                        </span>
                      </div>
                      {selectedTableForDetails.capacity && (
                        <div className="detail-row">
                          <span className="label">
                            <i className="fas fa-users"></i>
                            السعة
                          </span>
                          <span className="value">{selectedTableForDetails.capacity} أشخاص</span>
                        </div>
                      )}
                    </div>
                  </div>
                </div>

                {/* Timing Information - معلومات الأوقات */}
                {(selectedTableForDetails.openTime || selectedTableForDetails.closeTime || selectedTableForDetails.createdAt) && (
                  <div className="detail-group timing-info">
                    <h4>
                      <i className="fas fa-history"></i>
                      معلومات الأوقات
                    </h4>
                    <div className="timing-grid">
                      {selectedTableForDetails.openTime && (
                        <div className="timing-card">
                          <div className="timing-icon">
                            <i className="fas fa-door-open"></i>
                          </div>
                          <div className="timing-content">
                            <h5>وقت الفتح</h5>
                            <p>
                              {new Date(selectedTableForDetails.openTime).toLocaleString('ar-SA', {
                                year: 'numeric',
                                month: 'short',
                                day: 'numeric',
                                hour: '2-digit',
                                minute: '2-digit'
                              })}
                            </p>
                          </div>
                        </div>
                      )}
                      {selectedTableForDetails.closeTime && (
                        <div className="timing-card">
                          <div className="timing-icon closed">
                            <i className="fas fa-door-closed"></i>
                          </div>
                          <div className="timing-content">
                            <h5>وقت الإغلاق</h5>
                            <p>
                              {new Date(selectedTableForDetails.closeTime).toLocaleString('ar-SA', {
                                year: 'numeric',
                                month: 'short',
                                day: 'numeric',
                                hour: '2-digit',
                                minute: '2-digit'
                              })}
                            </p>
                          </div>
                        </div>
                      )}
                      {selectedTableForDetails.createdAt && (
                        <div className="timing-card">
                          <div className="timing-icon">
                            <i className="fas fa-calendar-plus"></i>
                          </div>
                          <div className="timing-content">
                            <h5>تاريخ الإنشاء</h5>
                            <p>
                              {new Date(selectedTableForDetails.createdAt).toLocaleString('ar-SA', {
                                year: 'numeric',
                                month: 'short',
                                day: 'numeric',
                                hour: '2-digit',
                                minute: '2-digit'
                              })}
                            </p>
                          </div>
                        </div>
                      )}
                    </div>
                  </div>
                )}

                {/* Action Buttons - أزرار الإجراءات */}
                <div className="modal-actions">
                  {selectedTableForDetails.isOpen && (
                    <button 
                      className="action-btn close-table"
                      onClick={async () => {
                        try {
                          const response = await authenticatedPut(`/api/v1/table-accounts/${selectedTableForDetails._id}/close`, {});
                          if (response.success) {
                            showSuccess('تم إغلاق الطاولة بنجاح');
                            setShowTableDetailsModal(false);
                            await fetchTableAccounts();
                          }
                        } catch (error) {
                          showError('حدث خطأ أثناء إغلاق الطاولة');
                        }
                      }}
                    >
                      <i className="fas fa-door-closed"></i>
                      إغلاق الطاولة
                    </button>
                  )}
                  <button 
                    className="action-btn refresh"
                    onClick={async () => {
                      await fetchTableAccounts();
                      showInfo('تم تحديث بيانات الطاولات');
                    }}
                  >
                    <i className="fas fa-sync-alt"></i>
                    تحديث البيانات
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>
      )}
      </div>
    </div>
  );
};

export default ManagerDashboard;

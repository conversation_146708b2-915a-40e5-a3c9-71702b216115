/* ============================== */
/* NavigationBarComponent - Navigation Component */
/* ============================== */

/* CSS Variables for NavigationBarComponent */
/* ?? ????? ????????? ??????? ?????? ??? ????? */

/* ================ */
/* Navigation Bar Container */
/* ================ */

.navigationBarComponent {
  background: #ffffff;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  position: sticky;
  top: 0;
  z-index: 1000;
  direction: rtl;
  border-bottom: 1px solid #e1e8ed;
}

.navigationBarComponent__container {
  max-width: 1400px;
  margin: 0 auto;
  padding: 0 1rem;
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: 70px;
}

/* ================ */
/* Logo Section */
/* ================ */

.navigationBarComponent__logo {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  text-decoration: none;
  color: #2c3e50;
  font-size: 1.5rem;
  font-weight: 700;
  transition: all 0.3s ease;
}

.navigationBarComponent__logo:hover {
  color: #3498db;
}

.navigationBarComponent__logo-icon {
  font-size: 2rem;
  color: #3498db;
}

.navigationBarComponent__logo-text {
  font-size: 1.3rem;
  font-weight: 700;
}

/* ================ */
/* Navigation Menu */
/* ================ */

.navigationBarComponent__nav {
  display: flex;
  align-items: center;
  gap: 2rem;
}

.navigationBarComponent__nav-list {
  display: flex;
  list-style: none;
  margin: 0;
  padding: 0;
  gap: 1.5rem;
}

.navigationBarComponent__nav-item {
  position: relative;
}

.navigationBarComponent__nav-link {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.75rem 1rem;
  text-decoration: none;
  color: #2c3e50;
  font-weight: 500;
  font-size: 0.95rem;
  border-radius: 8px;
  transition: all 0.3s ease;
  position: relative;
}

.navigationBarComponent__nav-link:hover {
  background: #f8f9fa;
  color: #3498db;
}

.navigationBarComponent__nav-link--active {
  background: #3498db;
  color: #ffffff;
}

.navigationBarComponent__nav-link--active::after {
  content: '';
  position: absolute;
  bottom: -1px;
  left: 0;
  right: 0;
  height: 3px;
  background: #3498db;
  border-radius: 2px 2px 0 0;
}

.navigationBarComponent__nav-icon {
  font-size: 1rem;
}

/* ================ */
/* User Section */
/* ================ */

.navigationBarComponent__user-section {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.navigationBarComponent__user-info {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  padding: 0.5rem 1rem;
  background: #f8f9fa;
  border-radius: 8px;
  border: 1px solid #e1e8ed;
}

.navigationBarComponent__user-avatar {
  width: 36px;
  height: 36px;
  border-radius: 50%;
  background: linear-gradient(135deg, #3498db, #2980b9);
  display: flex;
  align-items: center;
  justify-content: center;
  color: #ffffff;
  font-weight: 700;
  font-size: 0.9rem;
}

.navigationBarComponent__user-details {
  display: flex;
  flex-direction: column;
  gap: 0.125rem;
}

.navigationBarComponent__user-name {
  font-size: 0.9rem;
  font-weight: 600;
  color: #2c3e50;
  margin: 0;
}

.navigationBarComponent__user-role {
  font-size: 0.75rem;
  color: #7f8c8d;
  margin: 0;
}

/* ================ */
/* Action Buttons */
/* ================ */

.navigationBarComponent__actions {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.navigationBarComponent__action-btn {
  padding: 0.5rem;
  background: none;
  border: 1px solid #e1e8ed;
  border-radius: 8px;
  color: #2c3e50;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 36px;
  height: 36px;
}

.navigationBarComponent__action-btn:hover {
  background: #f8f9fa;
  border-color: #3498db;
  color: #3498db;
}

.navigationBarComponent__notifications-btn {
  position: relative;
}

.navigationBarComponent__notification-badge {
  position: absolute;
  top: -4px;
  left: -4px;
  background: #e74c3c;
  color: #ffffff;
  font-size: 0.7rem;
  font-weight: 600;
  padding: 0.125rem 0.375rem;
  border-radius: 10px;
  min-width: 18px;
  height: 18px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.navigationBarComponent__logout-btn {
  background: #e74c3c;
  color: #ffffff;
  border: 1px solid #e74c3c;
  padding: 0.5rem 1rem;
  font-size: 0.85rem;
  font-weight: 500;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  width: auto;
  height: auto;
}

.navigationBarComponent__logout-btn:hover {
  background: #c0392b;
  border-color: #c0392b;
  color: #ffffff;
}

/* ================ */
/* Mobile Menu Toggle */
/* ================ */

.navigationBarComponent__mobile-toggle {
  display: none;
  background: none;
  border: 1px solid #e1e8ed;
  border-radius: 8px;
  color: #2c3e50;
  cursor: pointer;
  transition: all 0.3s ease;
  padding: 0.5rem;
  width: 36px;
  height: 36px;
  align-items: center;
  justify-content: center;
  font-size: 1.2rem;
}

.navigationBarComponent__mobile-toggle:hover {
  background: #f8f9fa;
  border-color: #3498db;
  color: #3498db;
}

/* ================ */
/* Dropdown Menu */
/* ================ */

.navigationBarComponent__dropdown {
  position: absolute;
  top: 100%;
  right: 0;
  background: #ffffff;
  border: 1px solid #e1e8ed;
  border-radius: 8px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  min-width: 200px;
  z-index: 1000;
  opacity: 0;
  visibility: hidden;
  transform: translateY(-10px);
  transition: all 0.3s ease;
}

.navigationBarComponent__nav-item:hover .navigationBarComponent__dropdown {
  opacity: 1;
  visibility: visible;
  transform: translateY(0);
}

.navigationBarComponent__dropdown-list {
  list-style: none;
  margin: 0;
  padding: 0.5rem 0;
}

.navigationBarComponent__dropdown-item {
  padding: 0;
}

.navigationBarComponent__dropdown-link {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.75rem 1rem;
  text-decoration: none;
  color: #2c3e50;
  font-size: 0.9rem;
  transition: all 0.3s ease;
}

.navigationBarComponent__dropdown-link:hover {
  background: #f8f9fa;
  color: #3498db;
}

/* ================ */
/* Breadcrumb */
/* ================ */

.navigationBarComponent__breadcrumb {
  background: #f8f9fa;
  padding: 0.75rem 0;
  border-bottom: 1px solid #e1e8ed;
}

.navigationBarComponent__breadcrumb-container {
  max-width: 1400px;
  margin: 0 auto;
  padding: 0 1rem;
}

.navigationBarComponent__breadcrumb-list {
  display: flex;
  list-style: none;
  margin: 0;
  padding: 0;
  gap: 0.5rem;
  align-items: center;
}

.navigationBarComponent__breadcrumb-item {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-size: 0.9rem;
}

.navigationBarComponent__breadcrumb-link {
  color: #3498db;
  text-decoration: none;
  transition: all 0.3s ease;
}

.navigationBarComponent__breadcrumb-link:hover {
  text-decoration: underline;
}

.navigationBarComponent__breadcrumb-current {
  color: #2c3e50;
  font-weight: 500;
}

.navigationBarComponent__breadcrumb-separator {
  color: #7f8c8d;
  font-size: 0.8rem;
}

/* ================ */
/* Search Bar */
/* ================ */

.navigationBarComponent__search {
  position: relative;
  display: flex;
  align-items: center;
  max-width: 300px;
  width: 100%;
}

.navigationBarComponent__search-input {
  width: 100%;
  padding: 0.5rem 0.75rem 0.5rem 2.5rem;
  border: 1px solid #e1e8ed;
  border-radius: 8px;
  font-size: 0.9rem;
  background: #ffffff;
  transition: all 0.3s ease;
  text-align: right;
  direction: rtl;
}

.navigationBarComponent__search-input:focus {
  outline: none;
  border-color: #3498db;
  box-shadow: 0 0 0 3px rgba(52, 152, 219, 0.1);
}

.navigationBarComponent__search-icon {
  position: absolute;
  right: 0.75rem;
  color: #7f8c8d;
  font-size: 0.9rem;
  pointer-events: none;
}

/* ================ */
/* Responsive Design */
/* ================ */

@media (max-width: 768px) {
  .navigationBarComponent__container {
    padding: 0 0.5rem;
  }
  
  .navigationBarComponent__mobile-toggle {
    display: flex;
  }
  
  .navigationBarComponent__nav {
    position: fixed;
    top: 70px;
    right: 0;
    width: 100%;
    background: #ffffff;
    border-top: 1px solid #e1e8ed;
    flex-direction: column;
    padding: 1rem;
    gap: 0;
    transform: translateY(-100vh);
    transition: all 0.3s ease;
    opacity: 0;
    visibility: hidden;
  }
  
  .navigationBarComponent__nav--open {
    transform: translateY(0);
    opacity: 1;
    visibility: visible;
  }
  
  .navigationBarComponent__nav-list {
    flex-direction: column;
    width: 100%;
    gap: 0;
  }
  
  .navigationBarComponent__nav-item {
    width: 100%;
  }
  
  .navigationBarComponent__nav-link {
    padding: 1rem;
    justify-content: flex-end;
    border-bottom: 1px solid #f1f2f6;
  }
  
  .navigationBarComponent__user-section {
    gap: 0.5rem;
  }
  
  .navigationBarComponent__user-info {
    display: none;
  }
  
  .navigationBarComponent__search {
    display: none;
  }
  
  .navigationBarComponent__actions {
    gap: 0.25rem;
  }
}

@media (max-width: 480px) {
  .navigationBarComponent__container {
    height: 60px;
  }
  
  .navigationBarComponent__logo-text {
    display: none;
  }
  
  .navigationBarComponent__logo-icon {
    font-size: 1.5rem;
  }
  
  .navigationBarComponent__action-btn,
  .navigationBarComponent__mobile-toggle {
    width: 32px;
    height: 32px;
  }
  
  .navigationBarComponent__logout-btn {
    padding: 0.4rem 0.6rem;
    font-size: 0.75rem;
  }
  
  .navigationBarComponent__nav {
    top: 60px;
  }
}

/* ================ */
/* Dark Mode Support */
/* ================ */

@media (prefers-color-scheme: dark) {
  .navigationBarComponent {
    background: #1a252f;
    border-bottom-color: #444;
  }
  
  .navigationBarComponent__logo,
  .navigationBarComponent__nav-link,
  .navigationBarComponent__user-name,
  .navigationBarComponent__breadcrumb-current {
    color: #ffffff;
  }
  
  .navigationBarComponent__user-info {
    background: #2a2a2a;
    border-color: #444;
  }
  
  .navigationBarComponent__action-btn,
  .navigationBarComponent__mobile-toggle {
    border-color: #444;
    color: #ffffff;
  }
  
  .navigationBarComponent__action-btn:hover,
  .navigationBarComponent__mobile-toggle:hover {
    background: #3a3a3a;
  }
  
  .navigationBarComponent__search-input {
    background: #2a2a2a;
    border-color: #444;
    color: #ffffff;
  }
  
  .navigationBarComponent__dropdown {
    background: #1a252f;
    border-color: #444;
  }
  
  .navigationBarComponent__dropdown-link {
    color: #ffffff;
  }
  
  .navigationBarComponent__dropdown-link:hover {
    background: #3a3a3a;
  }
}

/* ================ */
/* Accessibility */
/* ================ */

.navigationBarComponent__nav-link:focus,
.navigationBarComponent__action-btn:focus,
.navigationBarComponent__mobile-toggle:focus,
.navigationBarComponent__search-input:focus {
  outline: 2px solid #3498db;
  outline-offset: 2px;
}

/* ================ */
/* Animations */
/* ================ */

.navigationBarComponent__nav-link::before {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  width: 0;
  height: 2px;
  background: #3498db;
  transition: width 0.3s ease;
}

.navigationBarComponent__nav-link:hover::before {
  width: 100%;
}

.navigationBarComponent__notification-badge {
  animation: navigationBarComponent-pulse 2s infinite;
}

@keyframes navigationBarComponent-pulse {
  0% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.1);
  }
  100% {
    transform: scale(1);
  }
}


const fetch = require('node-fetch');

const backEndURL = 'http://localhost:4010';
const productionURL = 'https://deshacoffee-production.up.railway.app';

async function freeAllTables() {
  console.log('🔧 تحرير جميع الطاولات المحجوزة...\n');

  try {
    // Determine backend URL
    let baseURL = backEndURL;
    try {
      await fetch(`${baseURL}/health`, { timeout: 3000 });
      console.log('✅ استخدام الخادم المحلي');
    } catch (error) {
      baseURL = productionURL;
      console.log('🌐 استخدام خادم الإنتاج');
    }

    // Login as manager
    console.log('\n🔐 تسجيل الدخول كمدير...');
    const loginResponse = await fetch(`${baseURL}/api/auth/login`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        username: 'بيسو',  // Manager account
        password: '253040'
      })
    });

    if (!loginResponse.ok) {
      throw new Error('فشل تسجيل الدخول');
    }

    const loginData = await loginResponse.json();
    console.log(`✅ تم تسجيل الدخول: ${loginData.user.name} (${loginData.user.role})`);

    if (loginData.user.role !== 'manager' && loginData.user.role !== 'admin') {
      throw new Error('المستخدم ليس مدير - لا يمكن تحرير الطاولات');
    }

    // Call the new endpoint to free all tables
    console.log('\n🔓 تحرير جميع الطاولات المحجوزة...');
    const freeResponse = await fetch(`${baseURL}/api/tables/free-all`, {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${loginData.token}`,
        'Content-Type': 'application/json'
      }
    });

    const freeResult = await freeResponse.json();
    
    if (!freeResponse.ok) {
      throw new Error(`خطأ ${freeResponse.status}: ${freeResult.message}`);
    }

    console.log('📊 نتيجة التحرير:');
    console.log(`✅ تم تحرير: ${freeResult.data.freed} طاولة`);
    console.log(`❌ أخطاء: ${freeResult.data.errors}`);

    if (freeResult.data.tables && freeResult.data.tables.length > 0) {
      console.log('\n📋 الطاولات المحررة:');
      freeResult.data.tables.forEach(table => {
        console.log(`  📍 طاولة ${table.number} - كانت محجوزة من: ${table.previousWaiter}`);
      });
    }

    // Test if the issue is fixed
    console.log('\n🧪 اختبار إصلاح المشكلة...');
    
    // Login as waiter
    const waiterLogin = await fetch(`${baseURL}/api/auth/login`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        username: 'Bosy',
        password: '253040'
      })
    });

    if (waiterLogin.ok) {
      const waiterData = await waiterLogin.json();
      
      // Try to create a test order
      const testOrder = {
        items: [{ productId: '6123456789abcdef12345678', quantity: 1 }],
        tableNumber: 1,
        customerName: 'عميل اختبار',
        orderType: 'dine-in'
      };

      const orderTest = await fetch(`${baseURL}/api/orders`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${waiterData.token}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(testOrder)
      });

      console.log(`📊 اختبار الطلب: ${orderTest.status} ${orderTest.statusText}`);
      
      if (orderTest.status === 409) {
        console.log('❌ المشكلة لا تزال موجودة');
        const errorText = await orderTest.text();
        console.log(`📝 الخطأ: ${errorText}`);
      } else if (orderTest.status === 404) {
        console.log('✅ لا يوجد خطأ 409 - تم حل المشكلة! (منتج غير موجود فقط)');
      } else if (orderTest.ok) {
        console.log('✅ تم إرسال الطلب بنجاح - المشكلة محلولة!');
      } else {
        console.log(`⚠️ خطأ آخر: ${orderTest.status}`);
      }
    }

    console.log('\n🎉 انتهى التحرير');
    console.log('💡 يمكن للنوادل الآن إرسال الطلبات بدون خطأ 409');

  } catch (error) {
    console.error('❌ خطأ:', error.message);
  }
}

// تشغيل التحرير
freeAllTables();

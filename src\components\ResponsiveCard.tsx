import React from 'react';

interface ResponsiveCardProps {
  title?: string;
  subtitle?: string;
  icon?: string;
  iconColor?: string;
  status?: 'success' | 'warning' | 'danger' | 'info' | 'primary' | 'secondary';
  children?: React.ReactNode;
  actions?: React.ReactNode;
  className?: string;
  onClick?: () => void;
  borderPosition?: 'start' | 'end' | 'top' | 'bottom';
  style?: React.CSSProperties;
  role?: string;
  'aria-label'?: string;
}

const ResponsiveCard: React.FC<ResponsiveCardProps> = ({
  title,
  subtitle,
  icon,
  iconColor = 'primary',
  status,
  children,
  actions,
  className = '',
  onClick,
  borderPosition = 'start',
  style,
  role,
  'aria-label': ariaLabel
}) => {
  const getBorderClass = () => {
    if (status) {
      return `border-${borderPosition} border-${status} border-4`;
    }
    return '';
  };

  const cardClasses = [
    'card',
    'border-0',
    'shadow-sm',
    'h-100',
    getBorderClass(),
    className
  ].filter(Boolean).join(' ');

  return (
    <div 
      className={`${cardClasses} ${onClick ? 'cursor-pointer' : ''}`}
      onClick={onClick}
      {...(style && { style })}
      {...(onClick && { role: 'button', tabIndex: 0 })}
      {...(role && { role })}
      {...(ariaLabel && { 'aria-label': ariaLabel })}
    >
      {(title || subtitle || icon) && (
        <div className="card-header bg-transparent border-0 pb-0">
          <div className="d-flex justify-content-between align-items-start">
            <div className="d-flex align-items-center">
              {icon && (
                <i className={`fas ${icon} text-${iconColor} me-2 fa-lg`}></i>
              )}
              <div>
                {title && <h6 className="card-title mb-0">{title}</h6>}
                {subtitle && <small className="text-muted">{subtitle}</small>}
              </div>
            </div>
            {status && (
              <span className={`badge bg-${status}`}>
                <i className={`fas ${getStatusIcon(status)} me-1`}></i>
                {getStatusText(status)}
              </span>
            )}
          </div>
        </div>
      )}

      {children && (
        <div className="card-body">
          {children}
        </div>
      )}

      {actions && (
        <div className="card-footer bg-transparent border-0">
          {actions}
        </div>
      )}
    </div>
  );
};

const getStatusIcon = (status: string) => {
  switch (status) {
    case 'success': return 'fa-check';
    case 'warning': return 'fa-exclamation-triangle';
    case 'danger': return 'fa-times';
    case 'info': return 'fa-info';
    case 'primary': return 'fa-circle';
    case 'secondary': return 'fa-circle';
    default: return 'fa-circle';
  }
};

const getStatusText = (status: string) => {
  switch (status) {
    case 'success': return 'نشط';
    case 'warning': return 'تحذير';
    case 'danger': return 'خطر';
    case 'info': return 'معلومات';
    case 'primary': return 'أساسي';
    case 'secondary': return 'ثانوي';
    default: return status;
  }
};

export default ResponsiveCard;

// دالة البحث الذكي للنصوص العربية
// تتعامل مع التشكيل والحروف المتشابهة

/**
 * تطبيع النص العربي لإزالة التشكيل والتعامل مع الحروف المتشابهة
 */
export function normalizeArabicText(text: string): string {
  if (!text || typeof text !== 'string') return '';
  
  return text
    // تحويل إلى أحرف صغيرة
    .toLowerCase()
    // إزالة التشكيل (الحركات)
    .replace(/[\u064B-\u0652\u0670\u0640]/g, '')
    // توحيد الهمزات
    .replace(/[أإآا]/g, 'ا')
    .replace(/[ؤو]/g, 'و')
    .replace(/[ئي]/g, 'ي')
    // توحيد التاء المربوطة والهاء
    .replace(/[ةه]/g, 'ه')
    // توحيد الياء والألف المقصورة
    .replace(/[يى]/g, 'ي')
    // إزالة المسافات الزائدة
    .trim()
    // إزالة المسافات المتعددة
    .replace(/\s+/g, ' ');
}

/**
 * البحث الذكي في النص العربي
 */
export function smartArabicSearch(text: string, searchTerm: string): boolean {
  if (!searchTerm || searchTerm.trim() === '') return true;
  if (!text) return false;
  
  const normalizedText = normalizeArabicText(text);
  const normalizedSearchTerm = normalizeArabicText(searchTerm);
  
  return normalizedText.includes(normalizedSearchTerm);
}

/**
 * البحث في عدة حقول نصية
 */
export function smartMultiFieldSearch(
  fields: (string | undefined | null)[], 
  searchTerm: string
): boolean {
  if (!searchTerm || searchTerm.trim() === '') return true;
  
  return fields.some(field => {
    if (!field) return false;
    return smartArabicSearch(field, searchTerm);
  });
}

/**
 * تمييز النص المطابق في البحث
 */
export function highlightSearchTerm(text: string, searchTerm: string): string {
  if (!searchTerm || searchTerm.trim() === '' || !text) return text;
  
  const normalizedText = normalizeArabicText(text);
  const normalizedSearchTerm = normalizeArabicText(searchTerm);
  
  if (!normalizedText.includes(normalizedSearchTerm)) return text;
  
  // البحث عن الكلمة الأصلية في النص
  const regex = new RegExp(`(${searchTerm.split('').join('.*?')})`, 'gi');
  return text.replace(regex, '<mark>$1</mark>');
}

/**
 * ترتيب النتائج حسب أولوية المطابقة وطول الاسم
 * @param items مصفوفة العناصر
 * @param searchTerm مصطلح البحث
 * @param getNameField دالة للحصول على اسم العنصر
 * @param getDescriptionField دالة للحصول على وصف العنصر (اختياري)
 */
export function smartSortResults<T>(
  items: T[], 
  searchTerm: string, 
  getNameField: (item: T) => string,
  getDescriptionField?: (item: T) => string
): T[] {
  if (!searchTerm || searchTerm.trim() === '') {
    // إذا لم يكن هناك بحث، ارجع القائمة كما هي
    return items;
  }

  const normalizedSearchTerm = normalizeArabicText(searchTerm);
  
  return items.sort((a, b) => {
    const nameA = getNameField(a) || '';
    const nameB = getNameField(b) || '';
    const descA = getDescriptionField ? (getDescriptionField(a) || '') : '';
    const descB = getDescriptionField ? (getDescriptionField(b) || '') : '';
    
    const normalizedNameA = normalizeArabicText(nameA);
    const normalizedNameB = normalizeArabicText(nameB);
    const normalizedDescA = normalizeArabicText(descA);
    const normalizedDescB = normalizeArabicText(descB);
    
    // حساب أولوية المطابقة
    const getMatchPriority = (name: string, desc: string) => {
      // أولوية 1: مطابقة كاملة للاسم
      if (name === normalizedSearchTerm) return 1;
      
      // أولوية 2: الاسم يبدأ بمصطلح البحث
      if (name.startsWith(normalizedSearchTerm)) return 2;
      
      // أولوية 3: الاسم يحتوي على مصطلح البحث
      if (name.includes(normalizedSearchTerm)) return 3;
      
      // أولوية 4: الوصف يحتوي على مصطلح البحث
      if (desc.includes(normalizedSearchTerm)) return 4;
      
      // أولوية 5: لا توجد مطابقة (لا يجب أن يحدث هذا في النتائج المفلترة)
      return 5;
    };
    
    const priorityA = getMatchPriority(normalizedNameA, normalizedDescA);
    const priorityB = getMatchPriority(normalizedNameB, normalizedDescB);
    
    // إذا كانت الأولوية مختلفة، رتب حسب الأولوية
    if (priorityA !== priorityB) {
      return priorityA - priorityB;
    }
    
    // إذا كانت الأولوية نفسها، رتب حسب طول الاسم (الأقصر أولاً)
    const lengthDiff = nameA.length - nameB.length;
    if (lengthDiff !== 0) {
      return lengthDiff;
    }
    
    // إذا كان الطول نفسه، رتب أبجدياً
    return nameA.localeCompare(nameB, 'ar');
  });
}

/**
 * دالة مساعدة للبحث والترتيب الذكي في مصفوفة من العناصر
 * @param items مصفوفة العناصر
 * @param searchTerm مصطلح البحث
 * @param getNameField دالة للحصول على اسم العنصر
 * @param getDescriptionField دالة للحصول على وصف العنصر (اختياري)
 */
export function smartSearchAndSort<T>(
  items: T[], 
  searchTerm: string, 
  getNameField: (item: T) => string,
  getDescriptionField?: (item: T) => string
): T[] {
  if (!searchTerm || searchTerm.trim() === '') {
    return items;
  }

  // فلترة العناصر التي تطابق البحث
  const filteredItems = items.filter(item => {
    const fields = [getNameField(item)];
    if (getDescriptionField) {
      fields.push(getDescriptionField(item));
    }
    return smartMultiFieldSearch(fields, searchTerm);
  });

  // ترتيب النتائج
  return smartSortResults(filteredItems, searchTerm, getNameField, getDescriptionField);
}

// أمثلة للاختبار
/*
console.log(normalizeArabicText('قَهْوَة')); // قهوه
console.log(normalizeArabicText('قهوه'));     // قهوه
console.log(normalizeArabicText('أحمد'));     // احمد
console.log(normalizeArabicText('احمد'));     // احمد

console.log(smartArabicSearch('قهوة عربية', 'قهوه')); // true
console.log(smartArabicSearch('أحمد محمد', 'احمد'));   // true
console.log(smartArabicSearch('شاي أخضر', 'شاى'));    // true

const items = [
  { id: 1, name: 'قهوة عربية', description: 'مشروب ساخن' },
  { id: 2, name: 'شاي أخضر', description: 'مشروب صحي' },
  { id: 3, name: 'عصير برتقال', description: 'مشروب منعش' },
  { id: 4, name: 'ماء', description: 'مشروب ضروري' }
];

console.log(smartSearchAndSort(items, 'قهوه', item => item.name, item => item.description));
*/

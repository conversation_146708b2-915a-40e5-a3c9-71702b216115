import React from 'react';

interface BootstrapSidebarProps {
  isOpen: boolean;
  onClose: () => void;
  activeScreen: string;
  onNavigate: (screen: string) => void;
}

const BootstrapSidebar: React.FC<BootstrapSidebarProps> = ({
  isOpen,
  onClose,
  activeScreen,
  onNavigate
}) => {
  const menuItems = [
    {
      id: 'tables',
      label: 'إدارة الطاولات',
      icon: 'fas fa-table',
      description: 'عرض وإدارة حالة الطاولات'
    },
    {
      id: 'orders',
      label: 'إدارة الطلبات',
      icon: 'fas fa-shopping-cart',
      description: 'عرض ومتابعة الطلبات'
    },
    {
      id: 'employees',
      label: 'إدارة الموظفين',
      icon: 'fas fa-users',
      description: 'إدارة بيانات الموظفين'
    },
    {
      id: 'menu',
      label: 'إدارة القائمة',
      icon: 'fas fa-utensils',
      description: 'إدارة عناصر القائمة'
    },
    {
      id: 'categories',
      label: 'إدارة الفئات',
      icon: 'fas fa-tags',
      description: 'إدارة فئات المنتجات'
    },
    {
      id: 'inventory',
      label: 'إدارة المخزون',
      icon: 'fas fa-boxes',
      description: 'متابعة المخزون والمواد'
    },
    {
      id: 'discounts',
      label: 'طلبات الخصم',
      icon: 'fas fa-percentage',
      description: 'مراجعة طلبات الخصم'
    },
    {
      id: 'reports',
      label: 'التقارير',
      icon: 'fas fa-chart-bar',
      description: 'تقارير المبيعات والأداء'
    },
    {
      id: 'settings',
      label: 'الإعدادات',
      icon: 'fas fa-cog',
      description: 'إعدادات النظام'
    }
  ];

  const handleItemClick = (screenId: string) => {
    onNavigate(screenId);
    // إغلاق القائمة الجانبية في الشاشات الصغيرة
    if (window.innerWidth < 992) {
      onClose();
    }
  };

  return (
    <>
      {/* Overlay للشاشات الصغيرة */}
      <div 
        className={`sidebar-overlay ${isOpen ? 'show' : ''} d-lg-none`}
        onClick={onClose}
      ></div>

      {/* القائمة الجانبية */}
      <nav className={`manager-sidebar ${isOpen ? 'show' : ''}`}>
        {/* Header القائمة الجانبية */}
        <div className="sidebar-header p-3 border-bottom border-light border-opacity-25">
          <div className="d-flex align-items-center justify-content-between">
            <h5 className="text-white mb-0">
              <i className="fas fa-coffee text-warning me-2"></i>
              لوحة التحكم
            </h5>
            <button
              className="btn btn-sm text-white d-lg-none"
              onClick={onClose}
              type="button"
              aria-label="إغلاق القائمة"
            >
              <i className="fas fa-times"></i>
            </button>
          </div>
        </div>

        {/* عناصر القائمة */}
        <ul className="sidebar-nav">
          {menuItems.map((item) => (
            <li key={item.id} className="sidebar-item">
              <button
                className={`sidebar-link w-100 text-start border-0 ${
                  activeScreen === item.id ? 'active' : ''
                }`}
                onClick={() => handleItemClick(item.id)}
                type="button"
              >
                <i className={item.icon}></i>
                <div className="flex-grow-1">
                  <div className="fw-medium">{item.label}</div>
                  <small className="text-light opacity-75 d-none d-xl-block">
                    {item.description}
                  </small>
                </div>
              </button>
            </li>
          ))}
        </ul>

        {/* Footer القائمة الجانبية */}
        <div className="sidebar-footer mt-auto p-3 border-top border-light border-opacity-25">
          <div className="text-center text-light opacity-75">
            <small>
              <i className="fas fa-code me-1"></i>
              نظام إدارة المقهى v2.0
            </small>
          </div>
        </div>
      </nav>
    </>
  );
};

export default BootstrapSidebar;

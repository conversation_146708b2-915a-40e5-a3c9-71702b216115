import React, { useState, useEffect } from 'react';
import { useToast } from './hooks/useToast';
import { ToastContainer } from './components/Toast';
import Button from './components/Button';
import Modal from './components/Modal';
import { authenticatedGet, authenticatedPost, authenticatedPut, authenticatedDelete, handleApiError } from './utils/apiHelpers';
import { APP_CONFIG } from './config/app.config'; // Import APP_CONFIG

interface Category {
  _id?: string;
  name: string;
  description?: string;
  active: boolean;
  order: number;
  color: string;
}

export default function CategoryManagement() {
  const [categories, setCategories] = useState<Category[]>([]);
  const [loading, setLoading] = useState(false);
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [editingCategory, setEditingCategory] = useState<Category | null>(null);
  const [formData, setFormData] = useState<Category>({
    name: '',
    description: '',
    active: true,
    order: 0,
    color: '#8B4513'
  });
  const { toasts, removeToast, showSuccess, showError } = useToast();

  // جلب الفئات من السيرفر
  useEffect(() => {
    fetchCategories();
  }, []);

  const fetchCategories = async () => {
    try {
      setLoading(true);
      const response = await authenticatedGet('/api/v1/categories');
      
      // Handle both response formats for compatibility
      let categories = response;
      if (response && typeof response === 'object' && response.data) {
        categories = response.data;
      }
      
      // Ensure we have an array
      if (Array.isArray(categories)) {
        setCategories(categories);
      } else {
        console.error('Categories response is not an array:', categories);
        setCategories([]);
        showError('تنسيق البيانات غير صحيح');
      }
    } catch (error) {
      const errorMessage = handleApiError(error);
      showError(errorMessage);
      setCategories([]);
    } finally {
      setLoading(false);
    }
  };

  // فتح نافذة إضافة/تعديل
  const openModal = (category?: Category) => {
    if (category) {
      setEditingCategory(category);
      setFormData(category);
    } else {
      setEditingCategory(null);
      setFormData({
        name: '',
        description: '',
        active: true,
        order: categories.length + 1,
        color: '#8B4513'
      });
    }
    setIsModalOpen(true);
  };

  // إغلاق النافذة
  const closeModal = () => {
    setIsModalOpen(false);
    setEditingCategory(null);
    setFormData({
      name: '',
      description: '',
      active: true,
      order: 0,
      color: '#8B4513'
    });
  };

  // حفظ الفئة
  const handleSave = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!formData.name.trim()) {
      showError('يرجى إدخال اسم الفئة');
      return;
    }

    try {
      setLoading(true);
      
      if (editingCategory) {
        await authenticatedPut(`/api/v1/categories/${editingCategory._id}`, formData);
        showSuccess('تم تحديث الفئة بنجاح');
      } else {
        await authenticatedPost('/api/v1/categories', formData);
        showSuccess('تم إضافة الفئة بنجاح');
      }
      
      closeModal();
      fetchCategories();
    } catch (error) {
      const errorMessage = handleApiError(error);
      showError(errorMessage);
    } finally {
      setLoading(false);
    }
  };

  // حذف فئة
  const handleDelete = async (category: Category) => {
    if (!window.confirm(`هل أنت متأكد من حذف فئة "${category.name}"؟`)) {
      return;
    }

    try {
      setLoading(true);
      await authenticatedDelete(`/api/v1/categories/${category._id}`);
      showSuccess('تم حذف الفئة بنجاح');
      fetchCategories();
    } catch (error) {
      const errorMessage = handleApiError(error);
      showError(errorMessage);
    } finally {
      setLoading(false);
    }
  };

  // تفعيل/إلغاء تفعيل فئة
  const toggleActive = async (category: Category) => {
    try {
      setLoading(true);
      // Use PATCH request for toggle operation
      const response = await fetch(`${APP_CONFIG.API.BASE_URL}/api/v1/categories/${category._id}/toggle`, {
        method: 'PATCH',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${localStorage.getItem('token')}`
        }
      });
      
      if (!response.ok) {
        throw new Error('فشل في تغيير حالة الفئة');
      }
      
      showSuccess(`تم ${category.active ? 'إلغاء تفعيل' : 'تفعيل'} الفئة`);
      fetchCategories();
    } catch (error) {
      const errorMessage = handleApiError(error);
      showError(errorMessage);
    } finally {
      setLoading(false);
    }
  };

  // ألوان محددة مسبقاً
  const predefinedColors = [
    '#8B4513', '#4682B4', '#228B22', '#FF8C00', '#DC143C',
    '#DA70D6', '#CD853F', '#FFD700', '#32CD32', '#FF6347',
    '#9370DB', '#20B2AA', '#FF69B4', '#1E90FF', '#FFA500'
  ];

  return (
    <div style={{ direction: 'rtl', padding: '1rem' }}>
      <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: '2rem' }}>
        <h2 style={{ margin: 0, color: '#6d4c41' }}>إدارة فئات المشروبات</h2>
        <Button
          onClick={() => openModal()}
          variant="primary"
          disabled={loading}
        >
          <i className="fas fa-plus" style={{ marginLeft: '0.5rem' }}></i>
          إضافة فئة جديدة
        </Button>
      </div>

      {loading && <div style={{ textAlign: 'center', padding: '2rem' }}>جاري التحميل...</div>}

      {!loading && (
        <div style={{
          display: 'grid',
          gridTemplateColumns: 'repeat(auto-fill, minmax(300px, 1fr))',
          gap: '1rem'
        }}>
          {categories.map(category => (
            <div
              key={category._id}
              style={{
                background: '#fff',
                border: `3px solid ${category.color}`,
                borderRadius: '12px',
                padding: '1.5rem',
                boxShadow: '0 2px 8px rgba(0,0,0,0.1)',
                position: 'relative',
                opacity: category.active ? 1 : 0.6
              }}
            >
              {/* شريط الحالة */}
              <div style={{
                position: 'absolute',
                top: '0',
                left: '0',
                right: '0',
                height: '4px',
                background: category.active ? category.color : '#ccc',
                borderRadius: '12px 12px 0 0'
              }} />

              {/* محتوى البطاقة */}
              <div style={{ marginTop: '0.5rem' }}>
                <div style={{ display: 'flex', alignItems: 'center', marginBottom: '1rem' }}>
                  <div style={{
                    width: '20px',
                    height: '20px',
                    borderRadius: '50%',
                    background: category.color,
                    marginLeft: '0.75rem'
                  }} />
                  <h3 style={{
                    margin: 0,
                    color: category.color,
                    fontSize: '1.2rem',
                    fontWeight: 'bold'
                  }}>
                    {category.name}
                  </h3>
                </div>

                {category.description && (
                  <p style={{
                    color: '#666',
                    margin: '0 0 1rem 0',
                    lineHeight: '1.4'
                  }}>
                    {category.description}
                  </p>
                )}

                <div style={{
                  display: 'flex',
                  justifyContent: 'space-between',
                  alignItems: 'center',
                  marginBottom: '1rem'
                }}>
                  <span style={{
                    fontSize: '0.9rem',
                    color: '#888',
                    background: '#f5f5f5',
                    padding: '0.25rem 0.5rem',
                    borderRadius: '4px'
                  }}>
                    ترتيب: {category.order}
                  </span>
                  <span style={{
                    fontSize: '0.9rem',
                    color: category.active ? '#4caf50' : '#f44336',
                    fontWeight: 'bold'
                  }}>
                    {category.active ? '✓ مفعلة' : '✗ معطلة'}
                  </span>
                </div>

                {/* أزرار الإجراءات */}
                <div style={{
                  display: 'flex',
                  gap: '0.5rem',
                  flexWrap: 'wrap'
                }}>
                  <Button
                    variant="primary"
                    size="sm"
                    onClick={() => openModal(category)}
                    disabled={loading}
                  >
                    <i className="fas fa-edit" style={{ marginLeft: '0.25rem' }}></i>
                    تعديل
                  </Button>

                  <Button
                    variant={category.active ? "warning" : "success"}
                    size="sm"
                    onClick={() => toggleActive(category)}
                    disabled={loading}
                  >
                    <i className={`fas fa-${category.active ? 'pause' : 'play'}`} style={{ marginLeft: '0.25rem' }}></i>
                    {category.active ? 'إلغاء تفعيل' : 'تفعيل'}
                  </Button>

                  <Button
                    variant="error"
                    size="sm"
                    onClick={() => handleDelete(category)}
                    disabled={loading}
                  >
                    <i className="fas fa-trash" style={{ marginLeft: '0.25rem' }}></i>
                    حذف
                  </Button>
                </div>
              </div>
            </div>
          ))}
        </div>
      )}

      {!loading && categories.length === 0 && (
        <div style={{
          textAlign: 'center',
          padding: '3rem',
          color: '#666',
          background: '#f9f9f9',
          borderRadius: '8px'
        }}>
          <i className="fas fa-tags" style={{ fontSize: '3rem', marginBottom: '1rem', color: '#ccc' }}></i>
          <p>لا توجد فئات محددة بعد</p>
          <Button onClick={() => openModal()} variant="primary">
            إضافة أول فئة
          </Button>
        </div>
      )}

      {/* نافذة إضافة/تعديل */}
      <Modal
        isOpen={isModalOpen}
        onClose={closeModal}
        title={editingCategory ? 'تعديل الفئة' : 'إضافة فئة جديدة'}
      >
        <form onSubmit={handleSave} style={{ display: 'flex', flexDirection: 'column', gap: '1rem' }}>
          <div>
            <label style={{ display: 'block', marginBottom: '0.5rem', fontWeight: 'bold' }}>
              اسم الفئة *
            </label>
            <input
              type="text"
              value={formData.name}
              onChange={(e) => setFormData({ ...formData, name: e.target.value })}
              required
              style={{
                width: '100%',
                padding: '0.75rem',
                border: '1px solid #ddd',
                borderRadius: '4px',
                fontSize: '1rem'
              }}
              placeholder="مثال: قهوة ساخنة"
            />
          </div>

          <div>
            <label style={{ display: 'block', marginBottom: '0.5rem', fontWeight: 'bold' }}>
              الوصف
            </label>
            <textarea
              value={formData.description}
              onChange={(e) => setFormData({ ...formData, description: e.target.value })}
              style={{
                width: '100%',
                padding: '0.75rem',
                border: '1px solid #ddd',
                borderRadius: '4px',
                fontSize: '1rem',
                minHeight: '80px',
                resize: 'vertical'
              }}
              placeholder="وصف مختصر للفئة..."
            />
          </div>

          <div>
            <label style={{ display: 'block', marginBottom: '0.5rem', fontWeight: 'bold' }}>
              اللون المميز
            </label>
            <div style={{ display: 'flex', gap: '0.5rem', flexWrap: 'wrap', marginBottom: '0.5rem' }}>
              {predefinedColors.map(color => (
                <button
                  key={color}
                  type="button"
                  onClick={() => setFormData({ ...formData, color })}
                  style={{
                    width: '30px',
                    height: '30px',
                    borderRadius: '50%',
                    background: color,
                    border: formData.color === color ? '3px solid #333' : '1px solid #ddd',
                    cursor: 'pointer'
                  }}
                />
              ))}
            </div>
            <input
              type="color"
              value={formData.color}
              onChange={(e) => setFormData({ ...formData, color: e.target.value })}
              style={{
                width: '50px',
                height: '30px',
                border: 'none',
                borderRadius: '4px',
                cursor: 'pointer'
              }}
            />
          </div>

          <div style={{ display: 'flex', gap: '1rem' }}>
            <div style={{ flex: 1 }}>
              <label style={{ display: 'block', marginBottom: '0.5rem', fontWeight: 'bold' }}>
                ترتيب العرض
              </label>
              <input
                type="number"
                min="1"
                value={formData.order}
                onChange={(e) => setFormData({ ...formData, order: parseInt(e.target.value) || 1 })}
                style={{
                  width: '100%',
                  padding: '0.75rem',
                  border: '1px solid #ddd',
                  borderRadius: '4px',
                  fontSize: '1rem'
                }}
              />
            </div>

            <div style={{ flex: 1, display: 'flex', alignItems: 'center' }}>
              <label style={{ display: 'flex', alignItems: 'center', gap: '0.5rem', cursor: 'pointer' }}>
                <input
                  type="checkbox"
                  checked={formData.active}
                  onChange={(e) => setFormData({ ...formData, active: e.target.checked })}
                  style={{ transform: 'scale(1.2)' }}
                />
                <span style={{ fontWeight: 'bold' }}>فئة مفعلة</span>
              </label>
            </div>
          </div>

          <div style={{ display: 'flex', gap: '1rem', justifyContent: 'flex-end', marginTop: '1rem' }}>
            <Button
              type="button"
              variant="secondary"
              onClick={closeModal}
              disabled={loading}
            >
              إلغاء
            </Button>
            <Button
              type="submit"
              variant="primary"
              disabled={loading}
            >
              {loading ? 'جاري الحفظ...' : (editingCategory ? 'تحديث' : 'إضافة')}
            </Button>
          </div>
        </form>
      </Modal>

      <ToastContainer toasts={toasts} onRemove={removeToast} />
    </div>
  );
}

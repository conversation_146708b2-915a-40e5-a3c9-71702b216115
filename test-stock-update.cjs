const mongoose = require('mongoose');
require('dotenv').config();

// تعريف نموذج المنتج
const productSchema = new mongoose.Schema({
  name: String,
  price: Number,
  available: Boolean,
  stock: {
    quantity: { type: Number, default: 100 },
    unit: { type: String, default: 'قطعة' },
    lowStockAlert: { type: Number, default: 5 }
  }
}, { collection: 'products' });

const Product = mongoose.model('Product', productSchema);

// دالة الاتصال بقاعدة البيانات
const connectDB = async () => {
  try {
    const mongoURI = process.env.MONGODB_URI;
    console.log('🔗 الاتصال بقاعدة البيانات...');
    
    await mongoose.connect(mongoURI, {
      serverSelectionTimeoutMS: 10000,
      socketTimeoutMS: 20000
    });
    
    console.log('✅ تم الاتصال بنجاح');
  } catch (error) {
    console.error('❌ خطأ في الاتصال:', error.message);
    throw error;
  }
};

// دالة اختبار تحديث المخزون
const testStockUpdate = async () => {
  try {
    console.log('🧪 بدء اختبار تحديث المخزون...');
    
    // الحصول على أول منتج
    const product = await Product.findOne({});
    
    if (!product) {
      console.log('❌ لا توجد منتجات للاختبار');
      return;
    }
    
    console.log(`\n📦 اختبار المنتج: ${product.name}`);
    console.log(`المخزون الحالي: ${product.stock?.quantity || 'غير محدد'} ${product.stock?.unit || ''}`);
    
    // حفظ القيمة الأصلية
    const originalQuantity = product.stock?.quantity || 100;
    const testQuantity = originalQuantity + 10;
    
    console.log(`\n🔄 تحديث المخزون إلى ${testQuantity}...`);
    
    // تحديث المخزون
    const updateResult = await Product.updateOne(
      { _id: product._id },
      { 
        $set: { 
          'stock.quantity': testQuantity,
          'stock.unit': 'قطعة',
          'stock.lowStockAlert': 5
        } 
      }
    );
    
    console.log(`📊 نتيجة التحديث: ${updateResult.modifiedCount} منتج تم تعديله`);
    
    // التحقق من التحديث
    const updatedProduct = await Product.findById(product._id);
    console.log(`✅ المخزون الجديد: ${updatedProduct.stock.quantity} ${updatedProduct.stock.unit}`);
    
    // انتظار قليل
    console.log('\n⏳ انتظار 3 ثوان...');
    await new Promise(resolve => setTimeout(resolve, 3000));
    
    // إعادة المخزون للقيمة الأصلية
    console.log(`\n🔄 إعادة المخزون للقيمة الأصلية ${originalQuantity}...`);
    
    await Product.updateOne(
      { _id: product._id },
      { 
        $set: { 
          'stock.quantity': originalQuantity,
          'stock.unit': product.stock?.unit || 'قطعة',
          'stock.lowStockAlert': product.stock?.lowStockAlert || 5
        } 
      }
    );
    
    // التحقق النهائي
    const finalProduct = await Product.findById(product._id);
    console.log(`✅ المخزون النهائي: ${finalProduct.stock.quantity} ${finalProduct.stock.unit}`);
    
    console.log('\n🎉 اكتمل الاختبار بنجاح!');
    
  } catch (error) {
    console.error('❌ خطأ في الاختبار:', error.message);
  }
};

// دالة عرض حالة جميع المنتجات
const showAllProductsStatus = async () => {
  try {
    console.log('\n📋 حالة جميع المنتجات:');
    console.log('=' .repeat(60));
    
    const products = await Product.find({}).select('name stock available').sort({ name: 1 });
    
    products.forEach((product, index) => {
      const quantity = product.stock?.quantity || 'غير محدد';
      const unit = product.stock?.unit || '';
      const available = product.available ? '✅ متوفر' : '❌ غير متوفر';
      
      console.log(`${index + 1}. ${product.name}`);
      console.log(`   المخزون: ${quantity} ${unit}`);
      console.log(`   الحالة: ${available}`);
      console.log(`   ID: ${product._id}`);
      console.log('-'.repeat(40));
    });
    
    console.log(`\n📊 إجمالي المنتجات: ${products.length}`);
    
  } catch (error) {
    console.error('❌ خطأ في عرض المنتجات:', error.message);
  }
};

// الدالة الرئيسية
const main = async () => {
  try {
    await connectDB();
    
    console.log('\n🧪 أداة اختبار تحديث المخزون');
    console.log('=' .repeat(40));
    
    // عرض حالة المنتجات
    await showAllProductsStatus();
    
    // اختبار تحديث المخزون
    await testStockUpdate();
    
    console.log('\n✅ تم الانتهاء من جميع الاختبارات');
    
  } catch (error) {
    console.error('❌ خطأ عام:', error.message);
  } finally {
    await mongoose.disconnect();
    console.log('🔌 تم قطع الاتصال بقاعدة البيانات');
    process.exit(0);
  }
};

// تشغيل الأداة
if (require.main === module) {
  main();
}

module.exports = { testStockUpdate, showAllProductsStatus };
<!doctype html>
<html lang="ar" dir="rtl">
  <head>
    <meta charset="UTF-8" />
    <link rel="icon" type="image/svg+xml" href="/coffee-cup.svg" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Desha Coffee - نظام إدارة المقهى</title>
    <!-- إضافة خط Cairo من Google Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@400;500;600;700&display=swap" rel="stylesheet">
    <!-- إضافة Font Awesome للأيقونات - أحدث إصدار -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.6.0/css/all.min.css" integrity="sha512-Kc323vGBEqzTmouAECnVceyQqyqdsSiqLQISBL29aUW4U/M7pSPA/gEUZQqv1cwx4OnYxTxve5UMg5GT6L4JJg==" crossorigin="anonymous" referrerpolicy="no-referrer" />
    <!-- Font Awesome Backup CDN -->
    <link rel="stylesheet" href="https://use.fontawesome.com/releases/v6.6.0/css/all.css" crossorigin="anonymous" />
    <!-- Fallback Icons CSS إذا فشل CDN -->
    <style>
      .fa, .fas, .far, .fal, .fab {
        font-family: "Font Awesome 6 Free", "Font Awesome 6 Pro", FontAwesome, Arial, sans-serif;
        font-weight: 900;
        display: inline-block;
        text-rendering: auto;
        -webkit-font-smoothing: antialiased;
        -moz-osx-font-smoothing: grayscale;
        font-style: normal;
        font-variant: normal;
        line-height: 1;
      }
      /* Fallback for common icons - محسّن للوضوح */
      .fa-search:before { content: "🔍"; font-size: 0.9em; }
      .fa-times:before { content: "✖"; font-size: 0.8em; }
      .fa-th:before { content: "⊞"; font-size: 0.9em; }
      .fa-list:before { content: "☰"; font-size: 0.9em; }
      .fa-sync-alt:before, .fa-refresh:before { content: "↻"; font-size: 0.9em; }
      .fa-download:before { content: "⬇"; font-size: 0.9em; }
      .fa-table:before { content: "⊞"; font-size: 0.9em; }
      .fa-door-open:before { content: "🚪"; font-size: 0.8em; }
      .fa-door-closed:before { content: "🚪"; font-size: 0.8em; }
      .fa-money-bill-wave:before { content: "💰"; font-size: 0.8em; }
      .fa-receipt:before { content: "🧾"; font-size: 0.8em; }
      .fa-users:before { content: "👥"; font-size: 0.8em; }
      .fa-spinner:before { content: "⟳"; font-size: 0.9em; }
      .fa-exclamation-triangle:before { content: "⚠"; font-size: 0.9em; }
      .fa-eye:before { content: "👁"; font-size: 0.8em; }
      .fa-sort-up:before { content: "▲"; font-size: 0.8em; }
      .fa-sort-down:before { content: "▼"; font-size: 0.8em; }

      /* إضافة أيقونات المخزون */
      .fa-box:before, .fa-boxes:before { content: "📦"; font-size: 0.8em; }
      .fa-plus:before { content: "+"; font-weight: bold; }
      .fa-minus:before { content: "-"; font-weight: bold; }
      .fa-edit:before { content: "✏"; font-size: 0.9em; }
      .fa-trash:before { content: "🗑"; font-size: 0.8em; }
      .fa-check:before { content: "✓"; font-weight: bold; color: green; }
      .fa-times-circle:before { content: "✖"; color: red; }
      /* Animation for spinning icons */
      .fa-spin {
        animation: fa-spin 2s infinite linear;
      }
      @keyframes fa-spin {
        0% { transform: rotate(0deg); }
        100% { transform: rotate(360deg); }
      }
    </style>
    <!-- PWA Manifest -->
    <link rel="manifest" href="/manifest.json">
    <!-- Meta tags for PWA -->
    <!-- theme-color: Supported in Chrome, Safari, Edge - graceful degradation in Firefox/Opera -->
    <meta name="theme-color" content="#795548">
    <meta name="mobile-web-app-capable" content="yes">
    <meta name="apple-mobile-web-app-capable" content="yes">
    <meta name="apple-mobile-web-app-status-bar-style" content="default">
    
    <!-- Script لإخفاء تحذيرات DOM Mutation Events المهجورة -->
    <script>
      // إخفاء تحذيرات DOM Mutation Events المهجورة
      const originalConsoleWarn = console.warn;
      console.warn = function(...args) {
        const message = args.join(' ');
        if (
          message.includes('DOMNodeInserted') ||
          message.includes('DOMNodeRemoved') ||
          message.includes('DOMCharacterDataModified') ||
          message.includes('DOMSubtreeModified') ||
          message.includes('mutation event')
        ) {
          // إخفاء هذه التحذيرات
          return;
        }
        // عرض التحذيرات الأخرى
        originalConsoleWarn.apply(console, args);
      };
    </script>
    <meta name="apple-mobile-web-app-title" content="Desha Coffee">
    <link rel="apple-touch-icon" href="/coffee-logo.svg">
  </head>
  <body>
    <div id="root"></div>
    <script type="module" src="/src/main.tsx"></script>
    <!-- Debug script for development -->
    <script type="module" src="/debug.js"></script>
  </body>
</html>

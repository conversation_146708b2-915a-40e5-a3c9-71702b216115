const axios = require('axios');

async function testFrontendLogin() {
    try {
        console.log('🔍 Testing frontend-style login request...');
        
        const loginData = {
            username: '<PERSON><PERSON>',
            password: 'MOHAMEDmostafa123'
        };
        
        console.log('📤 Sending login request with data:', loginData);
        console.log('🌐 Frontend API URL: http://localhost:5001/api/v1/auth/login');
        
        const response = await axios.post('http://localhost:5001/api/v1/auth/login', loginData, {
            headers: {
                'Content-Type': 'application/json',
                'Accept': 'application/json',
                'x-request-id': Math.random().toString(36).substring(7)
            }
        });
        
        console.log('✅ Login successful!');
        console.log('📊 Response status:', response.status);
        console.log('🎯 Response data:', JSON.stringify(response.data, null, 2));
        
    } catch (error) {
        console.error('❌ Login failed!');
        console.error('🔴 Error status:', error.response?.status);
        console.error('🔴 Error message:', error.response?.data || error.message);
        console.error('🔴 Full error:', error.response?.data);
    }
}

testFrontendLogin();

// سكريبت مبسط لحساب المبيعات
const path = require('path');

// الانتقال لمجلد backend
process.chdir(path.join(__dirname, 'backend'));

// استيراد التكوين
require('dotenv').config();
const mongoose = require('mongoose');

// استيراد النماذج الموجودة
const Order = require('./models/Order');
const User = require('./models/User');

async function main() {
  try {
    console.log('🔌 جاري الاتصال بقاعدة البيانات...');
    
    // الاتصال بقاعدة البيانات باستخدام التكوين الموجود
    const mongoUri = process.env.MONGODB_URI || 'mongodb+srv://besomustafa:<EMAIL>/deshacoffee?retryWrites=true&w=majority&appName=MyCoffeChop';
    
    await mongoose.connect(mongoUri);
    console.log('✅ تم الاتصال بقاعدة البيانات بنجاح');
    
    // جلب البيانات
    console.log('📥 جاري جلب البيانات...');
    const orders = await Order.find({}).populate('assignedWaiter', 'username');
    const users = await User.find({ role: 'waiter' });
    
    console.log(`📊 تم العثور على ${orders.length} طلب و ${users.length} نادل`);
    
    if (orders.length === 0) {
      console.log('⚠️ لا توجد طلبات في قاعدة البيانات حالياً');
      console.log('');
      console.log('📋 معلومات النُدل المسجلين:');
      if (users.length > 0) {
        users.forEach((user, index) => {
          console.log(`${index + 1}. ${user.username} (${user.email || 'لا يوجد إيميل'})`);
        });
      } else {
        console.log('لا يوجد نُدل مسجلين في النظام');
      }
      await mongoose.disconnect();
      return;
    }
    
    // حساب الإحصائيات
    let totalSalesBeforeDiscount = 0;
    let totalDiscounts = 0;
    let totalNetSales = 0;
    let completedOrders = 0;
    
    const waiterStats = {};
    const statusCounts = {};
    
    orders.forEach(order => {
      const orderTotal = order.total || 0;
      const orderDiscount = order.discount || 0;
      const orderNet = orderTotal - orderDiscount;
      const status = order.status || 'غير محدد';
      
      // عد الحالات
      statusCounts[status] = (statusCounts[status] || 0) + 1;
      
      // حساب المبيعات للطلبات المكتملة
      if (status === 'completed') {
        completedOrders++;
        totalSalesBeforeDiscount += orderTotal;
        totalDiscounts += orderDiscount;
        totalNetSales += orderNet;
      }
      
      // إحصائيات النُدل
      const waiterName = order.assignedWaiter?.username || 'غير محدد';
      
      if (!waiterStats[waiterName]) {
        waiterStats[waiterName] = {
          totalOrders: 0,
          completedOrders: 0,
          totalSales: 0,
          totalDiscounts: 0,
          netSales: 0
        };
      }
      
      waiterStats[waiterName].totalOrders++;
      
      if (status === 'completed') {
        waiterStats[waiterName].completedOrders++;
        waiterStats[waiterName].totalSales += orderTotal;
        waiterStats[waiterName].totalDiscounts += orderDiscount;
        waiterStats[waiterName].netSales += orderNet;
      }
    });
    
    // عرض النتائج
    console.log('');
    console.log('='.repeat(60));
    console.log('🏪 تقرير المبيعات وإحصائيات النُدل');
    console.log('='.repeat(60));
    
    console.log('');
    console.log('📊 إحصائيات عامة:');
    console.log(`📦 إجمالي الطلبات: ${orders.length}`);
    console.log(`✅ الطلبات المكتملة: ${completedOrders}`);
    console.log(`⏳ الطلبات غير المكتملة: ${orders.length - completedOrders}`);
    
    console.log('');
    console.log('📋 توزيع الطلبات حسب الحالة:');
    Object.entries(statusCounts).forEach(([status, count]) => {
      const percentage = ((count / orders.length) * 100).toFixed(1);
      console.log(`   ${status}: ${count} طلب (${percentage}%)`);
    });
    
    if (completedOrders > 0) {
      console.log('');
      console.log('💰 إجمالي المبيعات (الطلبات المكتملة):');
      console.log(`💵 إجمالي المبيعات قبل الخصم: ${totalSalesBeforeDiscount.toFixed(2)} ريال`);
      console.log(`🎯 إجمالي الخصومات: ${totalDiscounts.toFixed(2)} ريال`);
      console.log(`✨ صافي المبيعات: ${totalNetSales.toFixed(2)} ريال`);
      console.log(`📈 نسبة الخصومات: ${((totalDiscounts / totalSalesBeforeDiscount) * 100).toFixed(1)}%`);
      console.log(`📊 متوسط قيمة الطلب: ${(totalNetSales / completedOrders).toFixed(2)} ريال`);
    }
    
    console.log('');
    console.log('👥 إحصائيات النُدل:');
    console.log('='.repeat(60));
    
    // فلترة وترتيب النُدل
    const waitersWithSales = Object.entries(waiterStats)
      .filter(([_, stats]) => stats.completedOrders > 0)
      .sort((a, b) => b[1].netSales - a[1].netSales);
    
    if (waitersWithSales.length === 0) {
      console.log('⚠️ لا توجد مبيعات مكتملة للنُدل');
    } else {
      waitersWithSales.forEach(([waiterName, stats], index) => {
        console.log(`');
        console.log(`🏆 ${index + 1}. النادل: ${waiterName}`);
        console.log(`   📦 الطلبات المكتملة: ${stats.completedOrders}`);
        console.log(`   💰 إجمالي المبيعات: ${stats.totalSales.toFixed(2)} ريال`);
        console.log(`   🎯 الخصومات: ${stats.totalDiscounts.toFixed(2)} ريال`);
        console.log(`   ✨ صافي المبيعات: ${stats.netSales.toFixed(2)} ريال`);
        console.log(`   📈 متوسط الطلب: ${(stats.netSales / stats.completedOrders).toFixed(2)} ريال`);
        console.log(`   📊 نسبة من الإجمالي: ${((stats.netSales / totalNetSales) * 100).toFixed(1)}%`);
        console.log('   ' + '-'.repeat(45));
      });
    }
    
    // النُدل مع طلبات غير مكتملة
    const waitersWithPending = Object.entries(waiterStats)
      .filter(([_, stats]) => stats.totalOrders > stats.completedOrders);
    
    if (waitersWithPending.length > 0) {
      console.log('');
      console.log('⏳ نُدل بطلبات غير مكتملة:');
      waitersWithPending.forEach(([waiterName, stats]) => {
        const pending = stats.totalOrders - stats.completedOrders;
        console.log(`   ${waiterName}: ${pending} طلب غير مكتمل`);
      });
    }
    
    await mongoose.disconnect();
    console.log('');
    console.log('✅ تم إنهاء الاتصال بقاعدة البيانات');
    
  } catch (error) {
    console.error('❌ خطأ:', error.message);
    process.exit(1);
  }
}

main();

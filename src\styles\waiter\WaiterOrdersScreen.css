/* ====================================
   WaiterOrdersScreen Component Styles
   ==================================== */

/* استيراد المتغيرات المميزة لشاشة الطلبات */
@import '../variables/orders-variables.css';

/* متغيرات CSS خاصة بشاشة الطلبات */


/* Header شاشة الطلبات */
.waiter-orders-screen .screen-header {
  background: linear-gradient(135deg, var(--orders-primary-color), var(--orders-accent-color));
  padding: var(--orders-spacing-xl) var(--orders-spacing-lg);
  border-radius: 20px;
  margin-bottom: var(--orders-spacing-lg);
  color: white;
  box-shadow: 0 8px 32px rgba(0, 123, 255, 0.2);
  position: relative;
  overflow: hidden;
}

.waiter-orders-screen .screen-header::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="20" cy="20" r="1" fill="white" opacity="0.1"/><circle cx="80" cy="40" r="1" fill="white" opacity="0.1"/><circle cx="40" cy="80" r="1" fill="white" opacity="0.1"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>') repeat;
  pointer-events: none;
}

.waiter-orders-screen .action-buttons-flex {
  display: flex;
  justify-content: space-between;
  align-items: center;
  gap: var(--orders-spacing-md);
  margin-bottom: var(--orders-spacing-md);
  flex-wrap: wrap;
}

.waiter-orders-screen .screen-title {
  font-size: 2rem;
  font-weight: 800;
  margin: 0;
  display: flex;
  align-items: center;
  gap: var(--orders-spacing-sm);
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  position: relative;
  z-index: 1;
}

.waiter-orders-screen .screen-subtitle {
  margin: 0;
  opacity: 0.95;
  font-size: 1rem;
  font-weight: 500;
  position: relative;
  z-index: 1;
}

.waiter-orders-screen .btn-refresh {
  background: rgba(255, 255, 255, 0.15);
  color: white;
  border: 2px solid rgba(255, 255, 255, 0.3);
  padding: var(--orders-spacing-md) var(--orders-spacing-lg);
  border-radius: 12px;
  cursor: pointer;
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  font-size: 0.95rem;
  font-weight: 600;
  display: flex;
  align-items: center;
  gap: var(--orders-spacing-sm);
  backdrop-filter: blur(10px);
  position: relative;
  z-index: 1;
  min-width: 120px;
  justify-content: center;
}

.waiter-orders-screen .btn-refresh:hover {
  background: rgba(255, 255, 255, 0.25);
  border-color: rgba(255, 255, 255, 0.5);
  transform: translateY(-2px) scale(1.02);
  box-shadow: 0 4px 20px rgba(255, 255, 255, 0.2);
}

.waiter-orders-screen .btn-refresh:active {
  transform: translateY(0) scale(0.98);
}

/* قسم البحث والفلاتر */
.waiter-orders-screen .filter-section {
  background: var(--orders-bg-primary);
  padding: var(--orders-spacing-xl);
  border-radius: 20px;
  margin-bottom: var(--orders-spacing-lg);
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  border: 2px solid var(--orders-border-color);
  backdrop-filter: blur(10px);
  transition: all 0.3s ease;
}

.waiter-orders-screen .filter-section:hover {
  border-color: var(--orders-primary-color);
  box-shadow: 0 8px 30px rgba(0, 123, 255, 0.1);
}

.waiter-orders-screen .search-section {
  margin-bottom: var(--orders-spacing-xl);
}

.waiter-orders-screen .search-container {
  position: relative;
  max-width: 500px;
  margin: 0 auto;
}

.waiter-orders-screen .search-icon {
  position: absolute;
  right: var(--orders-spacing-lg);
  top: 50%;
  transform: translateY(-50%);
  color: var(--orders-primary-color);
  z-index: 2;
  font-size: 1.1rem;
  transition: all 0.3s ease;
}

.waiter-orders-screen .search-input {
  width: 100%;
  padding: var(--orders-spacing-lg) var(--orders-spacing-xl) var(--orders-spacing-lg) var(--orders-spacing-lg);
  border: 2px solid var(--orders-border-color);
  border-radius: 16px;
  font-size: 1.05rem;
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  background: var(--orders-bg-primary);
  text-align: right;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
  font-weight: 500;
}

.waiter-orders-screen .search-input:focus {
  outline: none;
  border-color: var(--orders-primary-color);
  box-shadow: 0 0 0 4px rgba(0, 123, 255, 0.1), 0 4px 20px rgba(0, 123, 255, 0.1);
  transform: translateY(-1px);
}

.waiter-orders-screen .search-input:focus + .search-icon {
  color: var(--orders-primary-color);
  transform: translateY(-50%) scale(1.1);
}

.waiter-orders-screen .clear-search {
  position: absolute;
  left: var(--orders-spacing-md);
  top: 50%;
  transform: translateY(-50%);
  background: transparent;
  border: none;
  color: var(--orders-text-secondary);
  cursor: pointer;
  padding: var(--orders-spacing-xs);
  border-radius: 50%;
  transition: all 0.3s ease;
}

.waiter-orders-screen .clear-search:hover {
  background: var(--orders-bg-tertiary);
  color: var(--orders-danger-color);
}

/* فلاتر الحالة */
.waiter-orders-screen .status-filters {
  display: flex;
  gap: var(--orders-spacing-md);
  overflow-x: auto;
  padding: var(--orders-spacing-md) 0;
  justify-content: center;
  margin-top: var(--orders-spacing-md);
}

.waiter-orders-screen .status-filter {
  background: var(--orders-bg-primary);
  border: 2px solid var(--orders-border-color);
  padding: var(--orders-spacing-md) var(--orders-spacing-lg);
  border-radius: 12px;
  cursor: pointer;
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  white-space: nowrap;
  display: flex;
  align-items: center;
  gap: var(--orders-spacing-xs);
  font-size: 0.95rem;
  font-weight: 600;
  min-height: 40px;
  position: relative;
  overflow: hidden;
  min-width: 100px;
  text-align: center;
  backdrop-filter: blur(5px);
}

.waiter-orders-screen .status-filter::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left 0.5s;
}

.waiter-orders-screen .status-filter:hover {
  border-color: var(--orders-primary-color);
  background: rgba(0, 123, 255, 0.1);
  transform: translateY(-2px);
  box-shadow: 0 4px 15px rgba(0, 123, 255, 0.2);
}

.waiter-orders-screen .status-filter:hover::before {
  left: 100%;
}

.waiter-orders-screen .status-filter.active {
  background: linear-gradient(135deg, var(--orders-primary-color), #0056b3);
  color: white;
  border-color: var(--orders-primary-color);
  box-shadow: 0 4px 20px rgba(0, 123, 255, 0.3);
  transform: translateY(-1px);
}

.waiter-orders-screen .status-filter:active {
  transform: translateY(0) scale(0.95);
}

.waiter-orders-screen .status-filter .count {
  background: rgba(255, 255, 255, 0.2);
  padding: 2px var(--orders-spacing-xs);
  border-radius: 12px;
  font-size: 0.75rem;
  font-weight: 600;
}

.waiter-orders-screen .status-filter:not(.active) .count {
  background: var(--orders-bg-tertiary);
  color: var(--orders-text-secondary);
}

/* قائمة الطلبات */
.waiter-orders-screen .orders-list {
  margin-bottom: var(--orders-spacing-xl);
}

.waiter-orders-screen .orders-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
  gap: var(--orders-spacing-xl);
  padding: var(--orders-spacing-md);
}

.waiter-orders-screen .order-card {
  background: var(--orders-bg-primary);
  border: 2px solid var(--orders-border-color);
  border-radius: 16px;
  padding: var(--orders-spacing-xl);
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  position: relative;
  overflow: hidden;
  min-height: 280px;
  display: flex;
  flex-direction: column;
}

.waiter-orders-screen .order-card:hover {
  transform: translateY(-6px) scale(1.02);
  box-shadow: 0 12px 40px rgba(0, 123, 255, 0.15);
  border-color: var(--orders-primary-color);
}

.waiter-orders-screen .order-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: var(--orders-spacing-lg);
  gap: var(--orders-spacing-md);
  padding-bottom: var(--orders-spacing-md);
  border-bottom: 2px solid var(--orders-bg-secondary);
}

.waiter-orders-screen .order-number {
  font-weight: 700;
  color: var(--orders-text-primary);
  display: flex;
  align-items: center;
  gap: var(--orders-spacing-sm);
  font-size: 1.2rem;
  background: linear-gradient(135deg, var(--orders-primary-color), var(--orders-accent-color));
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.waiter-orders-screen .order-status {
  padding: var(--orders-spacing-xs) var(--orders-spacing-sm);
  border-radius: 12px;
  font-size: 0.8rem;
  font-weight: 600;
  display: flex;
  align-items: center;
  gap: var(--orders-spacing-xs);
  white-space: nowrap;
}

.waiter-orders-screen .order-status.pending {
  background: #fff3cd;
  color: #856404;
  border: 1px solid #ffeaa7;
}

.waiter-orders-screen .order-status.preparing {
  background: #d4edda;
  color: #155724;
  border: 1px solid #c3e6cb;
}

.waiter-orders-screen .order-status.ready {
  background: #cce5ff;
  color: #004085;
  border: 1px solid #99d6ff;
}

.waiter-orders-screen .order-status.delivered {
  background: #d1ecf1;
  color: #0c5460;
  border: 1px solid #b8daff;
}

.waiter-orders-screen .order-status.cancelled {
  background: #f8d7da;
  color: #721c24;
  border: 1px solid #f5c6cb;
}

/* معلومات الطلب */
.waiter-orders-screen .order-info {
  margin-bottom: var(--orders-spacing-lg);
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: var(--orders-spacing-sm);
}

.waiter-orders-screen .info-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: var(--orders-spacing-sm) var(--orders-spacing-md);
  background: var(--orders-bg-secondary);
  border-radius: 12px;
  margin-bottom: var(--orders-spacing-sm);
  gap: var(--orders-spacing-sm);
  transition: all 0.3s ease;
  border: 1px solid transparent;
}

.waiter-orders-screen .info-row:hover {
  background: var(--orders-bg-tertiary);
  border-color: var(--orders-border-color);
  transform: translateX(4px);
}

.waiter-orders-screen .info-row .label {
  display: flex;
  align-items: center;
  gap: var(--orders-spacing-sm);
  color: var(--orders-text-secondary);
  font-size: 0.95rem;
  font-weight: 600;
  min-width: 0;
}

.waiter-orders-screen .info-row .value {
  color: var(--orders-text-primary);
  font-weight: 700;
  text-align: left;
  word-break: break-word;
  font-size: 1rem;
}

.waiter-orders-screen .chef-info {
  background: linear-gradient(135deg, var(--orders-bg-secondary), var(--orders-bg-tertiary));
  padding: var(--orders-spacing-md);
  border-radius: 12px;
  margin: var(--orders-spacing-sm) 0;
  border: 2px solid var(--orders-border-color);
  transition: all 0.3s ease;
}

.waiter-orders-screen .chef-info:hover {
  border-color: var(--orders-success-color);
  transform: scale(1.02);
}

.waiter-orders-screen .chef-name-card {
  background: linear-gradient(135deg, var(--orders-success-color), #20c997);
  color: white;
  padding: var(--orders-spacing-xs) var(--orders-spacing-sm);
  border-radius: 8px;
  font-size: 0.85rem;
  font-weight: 600;
}

/* تفاصيل الخصم */
.waiter-orders-screen .discount-info {
  background: linear-gradient(135deg, #f8f9fa, #e9ecef);
  border: 2px solid #dee2e6;
  border-radius: var(--orders-border-radius);
  padding: var(--orders-spacing-md);
  margin-top: var(--orders-spacing-md);
  position: relative;
}

.waiter-orders-screen .discount-info::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: linear-gradient(90deg, #ffc107, #fd7e14);
  border-radius: var(--orders-border-radius) var(--orders-border-radius) 0 0;
}

.waiter-orders-screen .discount-status-badge {
  padding: var(--orders-spacing-xs) var(--orders-spacing-sm);
  border-radius: 12px;
  font-size: 0.8rem;
  font-weight: 600;
  display: inline-flex;
  align-items: center;
  gap: var(--orders-spacing-xs);
}

.waiter-orders-screen .discount-status-badge.pending {
  background: #fff3cd;
  color: #856404;
  border: 1px solid #ffeaa7;
}

.waiter-orders-screen .discount-status-badge.approved {
  background: #d4edda;
  color: #155724;
  border: 1px solid #c3e6cb;
}

.waiter-orders-screen .discount-status-badge.rejected {
  background: #f8d7da;
  color: #721c24;
  border: 1px solid #f5c6cb;
}

.waiter-orders-screen .discount-amount {
  color: var(--orders-success-color);
  font-weight: 700;
  font-size: 1.1rem;
}

.waiter-orders-screen .discount-reason {
  background: rgba(255, 193, 7, 0.1);
  padding: var(--orders-spacing-sm);
  border-radius: 8px;
  margin-top: var(--orders-spacing-xs);
  border-right: 4px solid #ffc107;
}

.waiter-orders-screen .discount-reason .value {
  font-style: italic;
  color: #6c757d;
  font-size: 0.9rem;
  line-height: 1.4;
}

/* أزرار العمليات */
.waiter-orders-screen .order-actions {
  display: flex;
  flex-wrap: wrap;
  gap: var(--orders-spacing-md);
  margin-top: auto;
  padding-top: var(--orders-spacing-md);
  border-top: 2px solid var(--orders-bg-secondary);
  width: 100%;
  box-sizing: border-box;
}

.waiter-orders-screen .btn-details {
  flex: 1;
  min-width: 120px;
  max-width: 100%;
  background: linear-gradient(135deg, var(--orders-info-color), #138496);
  color: white;
  border: none;
  padding: var(--orders-spacing-md) var(--orders-spacing-lg);
  border-radius: 12px;
  cursor: pointer;
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  font-weight: 700;
  font-size: 0.95rem;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: var(--orders-spacing-sm);
  box-shadow: 0 4px 15px rgba(23, 162, 184, 0.3);
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.waiter-orders-screen .btn-details:hover {
  background: linear-gradient(135deg, #138496, #0f6674);
  transform: translateY(-3px) scale(1.05);
  box-shadow: 0 8px 25px rgba(23, 162, 184, 0.4);
}

.waiter-orders-screen .btn-deliver {
  flex: 1;
  min-width: 120px;
  max-width: 100%;
  background: linear-gradient(135deg, var(--orders-success-color), #20c997);
  color: white;
  border: none;
  padding: var(--orders-spacing-md) var(--orders-spacing-lg);
  border-radius: 12px;
  cursor: pointer;
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  font-weight: 700;
  font-size: 0.95rem;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: var(--orders-spacing-sm);
  white-space: nowrap;
  box-shadow: 0 4px 15px rgba(40, 167, 69, 0.3);
  overflow: hidden;
  text-overflow: ellipsis;
}

.waiter-orders-screen .btn-deliver:hover {
  background: linear-gradient(135deg, #218838, #1e7e34);
  transform: translateY(-3px) scale(1.05);
  box-shadow: 0 8px 25px rgba(40, 167, 69, 0.4);
}

.waiter-orders-screen .btn-discount {
  flex: 1;
  min-width: 120px;
  max-width: 100%;
  background: linear-gradient(135deg, var(--orders-warning-color), #fd7e14);
  color: #212529;
  border: none;
  padding: var(--orders-spacing-md) var(--orders-spacing-lg);
  border-radius: 12px;
  cursor: pointer;
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  font-weight: 700;
  font-size: 0.95rem;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: var(--orders-spacing-sm);
  white-space: nowrap;
  box-shadow: 0 4px 15px rgba(255, 193, 7, 0.3);
  overflow: hidden;
  text-overflow: ellipsis;
}

.waiter-orders-screen .btn-discount:hover {
  background: linear-gradient(135deg, #e0a800, #d39e00);
  transform: translateY(-3px) scale(1.05);
  box-shadow: 0 8px 25px rgba(255, 193, 7, 0.4);
}

/* حالات الفراغ */
.waiter-orders-screen .empty-state {
  text-align: center;
  padding: var(--orders-spacing-xl);
  grid-column: 1 / -1;
}

.waiter-orders-screen .empty-icon {
  font-size: 3rem;
  color: var(--orders-text-muted);
  margin-bottom: var(--orders-spacing-md);
}

.waiter-orders-screen .empty-state h3 {
  color: var(--orders-text-primary);
  margin-bottom: var(--orders-spacing-sm);
}

.waiter-orders-screen .empty-state p {
  color: var(--orders-text-secondary);
  margin-bottom: var(--orders-spacing-lg);
}

/* Responsive Design */
@media (max-width: 768px) {
  .waiter-orders-screen .screen-header {
    padding: var(--orders-spacing-lg);
    margin-bottom: var(--orders-spacing-md);
  }

  .waiter-orders-screen .action-buttons-flex {
    flex-direction: column;
    gap: var(--orders-spacing-md);
    align-items: stretch;
  }

  .waiter-orders-screen .btn-refresh {
    width: 100%;
    max-width: 200px;
    margin: 0 auto;
  }

  .waiter-orders-screen .screen-title {
    font-size: 1.8rem;
    text-align: center;
  }

  .waiter-orders-screen .screen-subtitle {
    font-size: 0.95rem;
    text-align: center;
  }

  .waiter-orders-screen .filter-section {
    padding: var(--orders-spacing-lg);
    border-radius: 16px;
  }

  .waiter-orders-screen .search-container {
    max-width: 100%;
  }

  .waiter-orders-screen .status-filters {
    gap: var(--orders-spacing-sm);
    justify-content: center;
  }

  .waiter-orders-screen .status-filter {
    min-width: 80px;
    padding: var(--orders-spacing-sm) var(--orders-spacing-md);
    font-size: 0.9rem;
  }
  
  .waiter-orders-screen .orders-grid {
    grid-template-columns: 1fr;
    gap: var(--orders-spacing-lg);
    padding: var(--orders-spacing-sm);
  }
  
  .waiter-orders-screen .order-header {
    flex-direction: column;
    align-items: stretch;
    gap: var(--orders-spacing-sm);
  }
  
  .waiter-orders-screen .order-status {
    align-self: flex-start;
  }
  
  .waiter-orders-screen .order-actions {
    flex-direction: column;
    gap: var(--orders-spacing-sm);
    width: 100%;
  }
  
  .waiter-orders-screen .btn-details,
  .waiter-orders-screen .btn-deliver,
  .waiter-orders-screen .btn-discount {
    width: 100%;
    min-width: auto;
    max-width: 100%;
  }
  
  .waiter-orders-screen .order-card {
    min-height: auto;
  }
}

@media (max-width: 480px) {
  .waiter-orders-screen .screen-header {
    padding: var(--orders-spacing-md);
    border-radius: 12px;
    margin-bottom: var(--orders-spacing-sm);
  }

  .waiter-orders-screen .screen-title {
    font-size: 1.5rem;
    text-align: center;
  }

  .waiter-orders-screen .screen-subtitle {
    font-size: 0.85rem;
    text-align: center;
  }

  .waiter-orders-screen .btn-refresh {
    width: 100%;
    max-width: 180px;
    padding: var(--orders-spacing-sm) var(--orders-spacing-md);
    font-size: 0.9rem;
  }

  .waiter-orders-screen .filter-section {
    padding: var(--orders-spacing-md);
    border-radius: 12px;
  }

  .waiter-orders-screen .search-input {
    padding: var(--orders-spacing-md);
    font-size: 1rem;
    border-radius: 12px;
  }

  .waiter-orders-screen .status-filters {
    gap: var(--orders-spacing-xs);
    flex-wrap: wrap;
  }

  .waiter-orders-screen .status-filter {
    min-width: 70px;
    padding: var(--orders-spacing-xs) var(--orders-spacing-sm);
    font-size: 0.85rem;
    border-radius: 8px;
  }

  .waiter-orders-screen .orders-grid {
    padding: 0;
    gap: var(--orders-spacing-sm);
  }

  .waiter-orders-screen .order-card {
    padding: var(--orders-spacing-md);
    border-radius: 12px;
  }

  .waiter-orders-screen .order-header {
    flex-direction: column;
    align-items: flex-start;
    gap: var(--orders-spacing-xs);
  }

  .waiter-orders-screen .order-status {
    align-self: flex-end;
  }

  .waiter-orders-screen .order-actions {
    flex-direction: column;
    width: 100%;
    gap: var(--orders-spacing-xs);
  }

  .waiter-orders-screen .btn-details,
  .waiter-orders-screen .btn-deliver,
  .waiter-orders-screen .btn-discount {
    width: 100%;
    min-width: auto;
    max-width: 100%;
    padding: var(--orders-spacing-sm) var(--orders-spacing-md);
    font-size: 0.9rem;
  }

  .waiter-orders-screen .info-row {
    flex-direction: column;
    align-items: flex-start;
    gap: var(--orders-spacing-xs);
    padding: var(--orders-spacing-sm);
  }

  .waiter-orders-screen .info-row .value {
    text-align: right;
    margin-top: var(--orders-spacing-xs);
    font-size: 0.95rem;
  }
}


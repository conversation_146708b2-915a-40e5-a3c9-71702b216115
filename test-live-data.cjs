const https = require('https');

async function fetch(url) {
  return new Promise((resolve, reject) => {
    https.get(url, (res) => {
      let data = '';
      res.on('data', chunk => data += chunk);
      res.on('end', () => {
        try {
          resolve({ json: () => JSON.parse(data), status: res.statusCode });
        } catch (e) {
          resolve({ json: () => ({}), status: res.statusCode, text: data });
        }
      });
    }).on('error', reject);
  });
}

async function testLiveAPI() {
  const backendURL = 'https://deshacoffee-production.up.railway.app';
  
  console.log('🔍 فحص البيانات الحية للطاولات 1، 2، 29 للنادلة Bosy...\n');
  
  // فحص الطلبات
  try {
    const ordersResponse = await fetch(`${backendURL}/api/v1/orders`);
    const ordersData = await ordersResponse.json();
    const orders = ordersData.data || ordersData || [];
    
    console.log('📋 إجمالي الطلبات:', orders.length);
    
    // فلترة طلبات Bosy للطاولات 1، 2، 29
    const bosyOrders = orders.filter(order => {
      const waiterMatch = order.waiterName === 'Bosy' || order.waiterName === 'بوسي';
      const tableMatch = ['1', '2', '29'].includes(String(order.tableNumber));
      return waiterMatch && tableMatch;
    });
    
    console.log('📊 طلبات Bosy للطاولات 1، 2، 29:', bosyOrders.length);
    
    if (bosyOrders.length > 0) {
      console.log('تفاصيل الطلبات:');
      bosyOrders.forEach(order => {
        console.log(`  - طاولة ${order.tableNumber}: طلب ${order._id} - ${order.totalPrice || 0} جنيه - ${order.status}`);
        console.log(`    النادل: ${order.waiterName} (ID: ${order.waiterId || 'غير محدد'})`);
      });
    } else {
      console.log('❌ لم يتم العثور على طلبات لـ Bosy في الطاولات 1، 2، 29');
    }
    
    // إحصائيات إضافية
    const bosyAllOrders = orders.filter(order => 
      order.waiterName === 'Bosy' || order.waiterName === 'بوسي'
    );
    console.log('📈 إجمالي طلبات Bosy في جميع الطاولات:', bosyAllOrders.length);
    
    if (bosyAllOrders.length > 0) {
      const tableNumbers = [...new Set(bosyAllOrders.map(o => o.tableNumber))];
      console.log('🍽️ الطاولات التي لديها طلبات لـ Bosy:', tableNumbers.join(', '));
    }
    
    // فحص الطاولات
    console.log('\n🏓 فحص API الطاولات...');
    const tablesResponse = await fetch(`${backendURL}/api/v1/table-accounts`);
    const tablesData = await tablesResponse.json();
    const tables = tablesData.data || tablesData || [];
    
    console.log('🏢 إجمالي الطاولات من API:', tables.length);
    
    if (tables.length > 0) {
      const bosyTables = tables.filter(table => 
        table.waiterName === 'Bosy' || table.waiterName === 'بوسي'
      );
      console.log('🏢 طاولات Bosy من API:', bosyTables.length);
      
      if (bosyTables.length > 0) {
        bosyTables.forEach(table => {
          console.log(`  - طاولة ${table.tableNumber}: ${table.status} - النادل: ${table.waiterName}`);
        });
      }
    }
    
  } catch (error) {
    console.error('❌ خطأ في فحص البيانات:', error.message);
  }
}

// تشغيل الاختبار
testLiveAPI();

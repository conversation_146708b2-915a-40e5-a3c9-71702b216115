// حساب الإحصائيات الحقيقية للنُدل بالجنيه المصري
const mongoose = require('mongoose');

async function getRealWaiterStats() {
  try {
    console.log('🔗 الاتصال بقاعدة البيانات المباشرة...');
    console.log('🌐 الخادم: https://deshacoffee-production.up.railway.app');
    
    const mongoUri = 'mongodb+srv://besomustafa:<EMAIL>/deshacoffee?retryWrites=true&w=majority&appName=MyCoffeChop';
    
    await mongoose.connect(mongoUri);
    console.log('✅ تم الاتصال بقاعدة البيانات بنجاح');
    
    const db = mongoose.connection.db;
    
    console.log('\n' + '='.repeat(80));
    console.log('🇪🇬 تقرير الإحصائيات الحقيقية للنُدل - مقهى دشة');
    console.log('💱 العملة: الجنيه المصري (ج.م)');
    console.log('='.repeat(80));
    
    // جلب البيانات الحقيقية
    const orders = await db.collection('orders').find({}).toArray();
    const users = await db.collection('users').find({}).toArray();
    const waiters = users.filter(user => user.role === 'waiter');
    
    console.log('\n📊 البيانات الأساسية:');
    console.log('• إجمالي الطلبات: ' + orders.length);
    console.log('• إجمالي المستخدمين: ' + users.length);
    console.log('• عدد النُدل: ' + waiters.length);
    
    // عرض قائمة النُدل المسجلين
    console.log('\n👥 النُدل المسجلين في النظام:');
    if (waiters.length > 0) {
      waiters.forEach((waiter, index) => {
        console.log((index + 1) + '. ' + (waiter.username || 'بدون اسم'));
        console.log('   الإيميل: ' + (waiter.email || 'غير محدد'));
        console.log('   تاريخ التسجيل: ' + (waiter.createdAt ? new Date(waiter.createdAt).toLocaleDateString('ar-EG') : 'غير محدد'));
        console.log('   ---');
      });
    } else {
      console.log('❌ لا يوجد نُدل مسجلين في النظام');
    }
    
    // تحليل الطلبات
    console.log('\n📋 تحليل الطلبات:');
    const statusCounts = {};
    let totalOrdersValue = 0;
    let totalDiscountsValue = 0;
    let completedOrdersCount = 0;
    
    // عرض عينة من الطلبات لفهم البنية
    console.log('\n🔍 عينة من الطلبات الحقيقية:');
    orders.slice(0, 10).forEach((order, index) => {
      const status = order.status || 'غير محدد';
      const orderTotal = parseFloat(order.total) || 0;
      const orderDiscount = parseFloat(order.discount) || 0;
      
      console.log((index + 1) + '. طلب رقم: ' + order._id.toString().substring(0, 8) + '...');
      console.log('   الحالة: ' + status);
      console.log('   الإجمالي: ' + orderTotal.toFixed(2) + ' ج.م');
      console.log('   الخصم: ' + orderDiscount.toFixed(2) + ' ج.م');
      console.log('   النادل: ' + (order.assignedWaiter || 'غير محدد'));
      console.log('   رقم الطاولة: ' + (order.tableNumber || 'غير محدد'));
      console.log('   التاريخ: ' + (order.createdAt ? new Date(order.createdAt).toLocaleDateString('ar-EG') : 'غير محدد'));
      
      // عرض العناصر إن وجدت
      if (order.items && order.items.length > 0) {
        console.log('   العناصر (' + order.items.length + '):');
        order.items.slice(0, 3).forEach(item => {
          const itemPrice = parseFloat(item.price) || 0;
          const itemQuantity = parseInt(item.quantity) || 1;
          console.log('     - ' + (item.name || 'عنصر') + ' × ' + itemQuantity + ' = ' + (itemPrice * itemQuantity).toFixed(2) + ' ج.م');
        });
      }
      console.log('   ---');
    });
    
    // حساب الإحصائيات العامة
    orders.forEach(order => {
      const status = order.status || 'غير محدد';
      statusCounts[status] = (statusCounts[status] || 0) + 1;
      
      const orderTotal = parseFloat(order.total) || 0;
      const orderDiscount = parseFloat(order.discount) || 0;
      
      totalOrdersValue += orderTotal;
      totalDiscountsValue += orderDiscount;
      
      if (status === 'completed') {
        completedOrdersCount++;
      }
    });
    
    console.log('\n📊 الإحصائيات العامة:');
    console.log('• توزيع الطلبات حسب الحالة:');
    Object.entries(statusCounts).forEach(([status, count]) => {
      const percentage = ((count / orders.length) * 100).toFixed(1);
      console.log('  - ' + status + ': ' + count + ' طلب (' + percentage + '%)');
    });
    
    console.log('\n💰 الإحصائيات المالية (بالجنيه المصري):');
    console.log('• إجمالي قيمة جميع الطلبات: ' + totalOrdersValue.toFixed(2) + ' ج.م');
    console.log('• إجمالي الخصومات: ' + totalDiscountsValue.toFixed(2) + ' ج.م');
    console.log('• صافي القيمة الإجمالية: ' + (totalOrdersValue - totalDiscountsValue).toFixed(2) + ' ج.م');
    console.log('• الطلبات المكتملة: ' + completedOrdersCount + ' من ' + orders.length);
    
    // حساب مبيعات النُدل بالتفصيل
    console.log('\n👥 إحصائيات النُدل التفصيلية (بالجنيه المصري):');
    console.log('='.repeat(80));
    
    const waiterStats = {};
    
    // تجميع البيانات لكل نادل
    orders.forEach(order => {
      const orderTotal = parseFloat(order.total) || 0;
      const orderDiscount = parseFloat(order.discount) || 0;
      const orderNet = orderTotal - orderDiscount;
      const status = order.status || 'غير محدد';
      
      let waiterName = 'طلبات غير مخصصة';
      let waiterId = 'unassigned';
      
      if (order.assignedWaiter) {
        const waiter = waiters.find(w => w._id.toString() === order.assignedWaiter.toString());
        if (waiter) {
          waiterName = waiter.username || 'نادل غير معروف';
          waiterId = waiter._id.toString();
        } else {
          waiterName = 'نادل محذوف (' + order.assignedWaiter.toString().substring(0, 8) + '...)';
          waiterId = order.assignedWaiter.toString();
        }
      }
      
      if (!waiterStats[waiterId]) {
        waiterStats[waiterId] = {
          name: waiterName,
          totalOrders: 0,
          completedOrders: 0,
          pendingOrders: 0,
          cancelledOrders: 0,
          totalSales: 0,
          totalDiscounts: 0,
          netSales: 0,
          completedSales: 0,
          completedDiscounts: 0,
          completedNet: 0
        };
      }
      
      const stats = waiterStats[waiterId];
      stats.totalOrders++;
      stats.totalSales += orderTotal;
      stats.totalDiscounts += orderDiscount;
      stats.netSales += orderNet;
      
      if (status === 'completed') {
        stats.completedOrders++;
        stats.completedSales += orderTotal;
        stats.completedDiscounts += orderDiscount;
        stats.completedNet += orderNet;
      } else if (status === 'pending') {
        stats.pendingOrders++;
      } else if (status === 'cancelled') {
        stats.cancelledOrders++;
      }
    });
    
    // ترتيب النُدل حسب صافي المبيعات المكتملة
    const sortedWaiters = Object.entries(waiterStats)
      .sort((a, b) => b[1].completedNet - a[1].completedNet);
    
    if (sortedWaiters.length === 0) {
      console.log('❌ لا توجد بيانات للنُدل');
    } else {
      console.log('🏆 ترتيب النُدل حسب الأداء (الطلبات المكتملة):');
      console.log('');
      
      sortedWaiters.forEach(([waiterId, stats], index) => {
        console.log('🏅 ' + (index + 1) + '. النادل: ' + stats.name);
        console.log('   📊 إحصائيات الطلبات:');
        console.log('     • إجمالي الطلبات: ' + stats.totalOrders);
        console.log('     • الطلبات المكتملة: ' + stats.completedOrders);
        console.log('     • الطلبات المعلقة: ' + stats.pendingOrders);
        console.log('     • الطلبات الملغية: ' + stats.cancelledOrders);
        
        console.log('   💰 إحصائيات المبيعات الإجمالية:');
        console.log('     • إجمالي المبيعات: ' + stats.totalSales.toFixed(2) + ' ج.م');
        console.log('     • إجمالي الخصومات: ' + stats.totalDiscounts.toFixed(2) + ' ج.م');
        console.log('     • صافي المبيعات: ' + stats.netSales.toFixed(2) + ' ج.م');
        
        console.log('   ✅ إحصائيات الطلبات المكتملة فقط:');
        console.log('     • مبيعات مكتملة: ' + stats.completedSales.toFixed(2) + ' ج.م');
        console.log('     • خصومات مكتملة: ' + stats.completedDiscounts.toFixed(2) + ' ج.م');
        console.log('     • صافي مكتمل: ' + stats.completedNet.toFixed(2) + ' ج.م');
        
        if (stats.completedOrders > 0) {
          console.log('     • متوسط الطلب المكتمل: ' + (stats.completedNet / stats.completedOrders).toFixed(2) + ' ج.م');
        }
        
        // حساب النسبة من الإجمالي
        const totalCompletedSales = sortedWaiters.reduce((sum, [_, s]) => sum + s.completedNet, 0);
        if (totalCompletedSales > 0) {
          const percentage = ((stats.completedNet / totalCompletedSales) * 100).toFixed(1);
          console.log('     • نسبة من إجمالي المبيعات المكتملة: ' + percentage + '%');
        }
        
        console.log('   ' + '-'.repeat(60));
        console.log('');
      });
      
      // ملخص إجمالي
      const totalCompletedSales = sortedWaiters.reduce((sum, [_, stats]) => sum + stats.completedSales, 0);
      const totalCompletedDiscounts = sortedWaiters.reduce((sum, [_, stats]) => sum + stats.completedDiscounts, 0);
      const totalCompletedNet = sortedWaiters.reduce((sum, [_, stats]) => sum + stats.completedNet, 0);
      
      console.log('📈 الملخص الإجمالي للطلبات المكتملة:');
      console.log('• إجمالي المبيعات المكتملة: ' + totalCompletedSales.toFixed(2) + ' ج.م');
      console.log('• إجمالي الخصومات المكتملة: ' + totalCompletedDiscounts.toFixed(2) + ' ج.م');
      console.log('• صافي المبيعات المكتملة: ' + totalCompletedNet.toFixed(2) + ' ج.م');
      console.log('• عدد الطلبات المكتملة: ' + completedOrdersCount);
      
      if (completedOrdersCount > 0) {
        console.log('• متوسط قيمة الطلب المكتمل: ' + (totalCompletedNet / completedOrdersCount).toFixed(2) + ' ج.م');
      }
      
      if (totalCompletedSales > 0) {
        console.log('• نسبة الخصومات: ' + ((totalCompletedDiscounts / totalCompletedSales) * 100).toFixed(1) + '%');
      }
    }
    
    console.log('\n' + '='.repeat(80));
    console.log('📅 تاريخ التقرير: ' + new Date().toLocaleString('ar-EG'));
    console.log('🌐 مصدر البيانات: قاعدة البيانات المباشرة (MongoDB Atlas)');
    console.log('🏪 النظام: مقهى دشة');
    console.log('💱 العملة: الجنيه المصري (ج.م)');
    console.log('🔗 الخادم: https://deshacoffee-production.up.railway.app');
    console.log('🖥️ الواجهة: https://desha-coffee.vercel.app/');
    console.log('✅ تم إنهاء التقرير بنجاح');
    console.log('='.repeat(80));
    
    await mongoose.disconnect();
    
  } catch (error) {
    console.error('❌ خطأ في الحصول على البيانات:');
    console.error('الرسالة: ' + error.message);
    if (error.code) {
      console.error('الكود: ' + error.code);
    }
  }
}

getRealWaiterStats();

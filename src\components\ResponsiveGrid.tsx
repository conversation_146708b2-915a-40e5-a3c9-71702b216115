import React from 'react';

interface ResponsiveGridProps {
  children: React.ReactNode;
  cols?: {
    xs?: number;
    sm?: number;
    md?: number;
    lg?: number;
    xl?: number;
    xxl?: number;
  };
  gap?: number;
  className?: string;
}

const ResponsiveGrid: React.FC<ResponsiveGridProps> = ({
  children,
  cols = { xs: 1, sm: 2, md: 3, lg: 4, xl: 6, xxl: 6 },
  gap = 4,
  className = ''
}) => {
  const getColClass = () => {
    const classes: string[] = [];
    
    if (cols.xs) classes.push(`col-${12 / cols.xs}`);
    if (cols.sm) classes.push(`col-sm-${12 / cols.sm}`);
    if (cols.md) classes.push(`col-md-${12 / cols.md}`);
    if (cols.lg) classes.push(`col-lg-${12 / cols.lg}`);
    if (cols.xl) classes.push(`col-xl-${12 / cols.xl}`);
    if (cols.xxl) classes.push(`col-xxl-${12 / cols.xxl}`);
    
    return classes.join(' ');
  };

  const gapClass = `g-${gap}`;

  return (
    <div className={`row ${gapClass} ${className}`}>
      {React.Children.map(children, (child, index) => (
        <div key={index} className={getColClass()}>
          {child}
        </div>
      ))}
    </div>
  );
};

export default ResponsiveGrid;

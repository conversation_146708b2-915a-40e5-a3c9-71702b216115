.error-boundary {
  min-height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  background: var(--background);
  padding: var(--spacing-lg);
}

.error-boundary-content {
  background: var(--surface);
  border-radius: var(--border-radius-lg);
  padding: var(--spacing-xl);
  box-shadow: var(--shadow-lg);
  text-align: center;
  max-width: 500px;
  width: 100%;
}

.error-icon {
  font-size: 4rem;
  color: var(--error);
  margin-bottom: var(--spacing-lg);
}

.error-boundary-content h2 {
  color: var(--text-primary);
  margin-bottom: var(--spacing-md);
  font-size: var(--font-size-2xl);
}

.error-boundary-content p {
  color: var(--text-secondary);
  margin-bottom: var(--spacing-lg);
  line-height: 1.6;
}

.error-actions {
  display: flex;
  gap: var(--spacing-md);
  justify-content: center;
  flex-wrap: wrap;
  margin-bottom: var(--spacing-lg);
}

.error-details {
  text-align: right;
  margin-top: var(--spacing-lg);
  border: 1px solid var(--border);
  border-radius: var(--border-radius-md);
  overflow: hidden;
}

.error-details summary {
  background: var(--primary-light);
  color: white;
  padding: var(--spacing-sm) var(--spacing-md);
  cursor: pointer;
  font-weight: var(--font-weight-medium);
}

.error-details summary:hover {
  background: var(--primary);
}

.error-stack {
  background: #f8f9fa;
  padding: var(--spacing-md);
  margin: 0;
  font-family: 'Courier New', monospace;
  font-size: var(--font-size-sm);
  color: #333;
  white-space: pre-wrap;
  word-break: break-word;
  max-height: 300px;
  overflow-y: auto;
}

[data-theme="dark"] .error-stack {
  background: #2d2d2d;
  color: #f8f9fa;
}

@media (max-width: 480px) {
  .error-boundary-content {
    padding: var(--spacing-lg);
  }
  
  .error-actions {
    flex-direction: column;
  }
  
  .error-actions .btn {
    width: 100%;
  }
}

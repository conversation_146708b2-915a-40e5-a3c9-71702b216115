/*
 * Bootstrap Native Grid System
 * نظام شبكة Bootstrap الأصلي - بدون قياسات ثابتة
 * 
 * يستخدم هذا الملف نظام Bootstrap Grid فقط
 * لضمان التجاوب الكامل في جميع الأحجام
 */

/* ============================== */
/* إعادة تعيين النظام الأساسي */
/* ============================== */

/* إزالة جميع القياسات الثابتة */
.manager-dashboard,
.manager-main,
.manager-sidebar,
.content-container {
  width: auto !important;
  max-width: none !important;
  margin: 0 !important;
  padding: 0 !important;
}

/* ============================== */
/* نظام Bootstrap Grid الكامل */
/* ============================== */

/* Dashboard Container - استخدام Bootstrap Container */
.manager-dashboard {
  display: flex !important;
  flex-direction: column !important;
  min-height: 100vh !important;
  overflow-x: hidden !important;
  background: #f8f9fa !important;
  direction: rtl !important;
}

/* Main Content Area - استخدام Bootstrap Row/Col */
.dashboard-content {
  display: flex !important;
  flex: 1 !important;
  position: relative !important;
}

/* Sidebar - استخدام Bootstrap Columns */
.manager-sidebar {
  background: linear-gradient(180deg, #2c3e50 0%, #34495e 100%) !important;
  color: white !important;
  min-height: 100vh !important;
  position: fixed !important;
  top: 0 !important;
  right: -100% !important;
  z-index: 1040 !important;
  transition: right 0.3s ease !important;
  overflow: hidden !important;
  box-shadow: -5px 0 15px rgba(0, 0, 0, 0.2) !important;
}

/* Sidebar Open State */
.manager-sidebar.open {
  right: 0 !important;
}

/* Main Content - استخدام Bootstrap Columns */
.manager-main {
  flex: 1 !important;
  transition: margin-right 0.3s ease !important;
  overflow-x: hidden !important;
}

/* Content Container - Bootstrap Container */
.main-content {
  width: 100% !important;
  overflow-x: hidden !important;
}

/* ============================== */
/* Bootstrap Responsive Breakpoints */
/* ============================== */

/* Extra Small devices (portrait phones, less than 576px) */
@media (max-width: 575.98px) {
  .manager-sidebar {
    width: 100vw !important; /* عرض كامل في الهواتف */
  }
  
  .manager-main {
    margin-right: 0 !important;
  }
  
  .main-content {
    padding: 1rem !important;
  }
}

/* Small devices (landscape phones, 576px and up) */
@media (min-width: 576px) {
  .manager-sidebar {
    width: 75vw !important; /* 75% من عرض الشاشة */
    max-width: 320px !important;
  }
  
  .main-content {
    padding: 1.5rem !important;
  }
}

/* Medium devices (tablets, 768px and up) */
@media (min-width: 768px) {
  .manager-sidebar {
    width: 50vw !important; /* 50% من عرض الشاشة */
    max-width: 320px !important;
  }
  
  .manager-main.sidebar-open {
    margin-right: 320px !important;
  }
  
  .main-content {
    padding: 2rem !important;
  }
}

/* Large devices (desktops, 992px and up) */
@media (min-width: 992px) {
  .manager-sidebar {
    width: 320px !important; /* عرض ثابت للشاشات الكبيرة */
  }
  
  .manager-main.sidebar-open {
    margin-right: 320px !important;
  }
  
  .main-content {
    padding: 2rem 3rem !important;
  }
}

/* Extra large devices (large desktops, 1200px and up) */
@media (min-width: 1200px) {
  .manager-sidebar {
    width: 350px !important; /* عرض أكبر للشاشات الكبيرة جداً */
  }
  
  .manager-main.sidebar-open {
    margin-right: 350px !important;
  }
  
  .main-content {
    padding: 2rem 4rem !important;
  }
}

/* Extra extra large devices (larger desktops, 1400px and up) */
@media (min-width: 1400px) {
  .manager-sidebar {
    width: 380px !important;
  }
  
  .manager-main.sidebar-open {
    margin-right: 380px !important;
  }
  
  .main-content {
    padding: 3rem 5rem !important;
  }
}

/* ============================== */
/* Bootstrap Grid للمحتوى الداخلي */
/* ============================== */

/* Container Fluid داخل المحتوى */
.screen-container {
  width: 100% !important;
  margin: 0 !important;
  padding: 0 !important;
}

/* Row داخل الشاشات */
.screen-row {
  margin: 0 !important;
  width: 100% !important;
}

/* Columns داخل الشاشات */
.screen-col {
  padding: 0.75rem !important;
}

/* Cards Bootstrap */
.screen-card {
  border: none !important;
  border-radius: 12px !important;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08) !important;
  transition: all 0.3s ease !important;
  overflow: hidden !important;
}

.screen-card:hover {
  transform: translateY(-2px) !important;
  box-shadow: 0 8px 30px rgba(0, 0, 0, 0.12) !important;
}

/* ============================== */
/* إصلاح النوافذ المنبثقة */
/* ============================== */

/* Modal مع Bootstrap */
.bootstrap-modal {
  z-index: 1055 !important;
}

.bootstrap-modal .modal-dialog {
  margin: 1rem auto !important;
  max-width: none !important;
}

/* Responsive Modal Sizes */
@media (min-width: 576px) {
  .bootstrap-modal .modal-dialog {
    max-width: 500px !important;
  }
}

@media (min-width: 768px) {
  .bootstrap-modal .modal-dialog {
    max-width: 700px !important;
  }
}

@media (min-width: 992px) {
  .bootstrap-modal .modal-dialog {
    max-width: 800px !important;
  }
}

@media (min-width: 1200px) {
  .bootstrap-modal .modal-dialog {
    max-width: 900px !important;
  }
}

/* Modal Content */
.bootstrap-modal .modal-content {
  border: none !important;
  border-radius: 16px !important;
  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3) !important;
  overflow: hidden !important;
}

/* ============================== */
/* إزالة كل الفيض غير المرغوب */
/* ============================== */

/* منع الفيض الأفقي في جميع المستويات */
html, body, #root {
  overflow-x: hidden !important;
  max-width: 100vw !important;
}

/* منع الفيض في المكونات الرئيسية */
.manager-dashboard *,
.manager-main *,
.main-content * {
  max-width: 100% !important;
  box-sizing: border-box !important;
}

/* ============================== */
/* تحسينات الأداء */
/* ============================== */

/* تسريع التحولات */
.manager-sidebar,
.manager-main {
  transition: all 0.25s cubic-bezier(0.4, 0, 0.2, 1) !important;
}

/* تحسين الرسم */
.manager-sidebar,
.manager-main,
.screen-card {
  will-change: transform !important;
  transform: translateZ(0) !important;
}

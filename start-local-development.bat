@echo off
echo ===============================================
echo    DESHA COFFEE - LOCAL DEVELOPMENT SETUP
echo ===============================================
echo.

:: التحقق من وجود Node.js
where node >nul 2>nul
if %errorlevel% neq 0 (
    echo ❌ Node.js غير مثبت على النظام
    echo 📥 قم بتثبيت Node.js من: https://nodejs.org
    pause
    exit /b 1
)

echo ✅ Node.js مثبت على النظام

:: التحقق من وجود npm
where npm >nul 2>nul
if %errorlevel% neq 0 (
    echo ❌ npm غير متاح
    pause
    exit /b 1
)

echo ✅ npm متاح

:: التحقق من ملفات البيئة
if not exist ".env.local" (
    echo ❌ ملف .env.local غير موجود
    echo 📝 يرجى التأكد من وجود ملف البيئة المحلية
    pause
    exit /b 1
)

if not exist "backend\.env.local" (
    echo ❌ ملف backend\.env.local غير موجود
    echo 📝 يرجى التأكد من وجود ملف البيئة المحلية للباك اند
    pause
    exit /b 1
)

echo ✅ ملفات البيئة المحلية موجودة

:: تثبيت الحزم إذا لم تكن مثبتة
if not exist "node_modules" (
    echo 📦 تثبيت حزم الفرونت اند...
    call npm install
    if %errorlevel% neq 0 (
        echo ❌ فشل في تثبيت حزم الفرونت اند
        pause
        exit /b 1
    )
)

if not exist "backend\node_modules" (
    echo 📦 تثبيت حزم الباك اند...
    cd backend
    call npm install
    if %errorlevel% neq 0 (
        echo ❌ فشل في تثبيت حزم الباك اند
        pause
        exit /b 1
    )
    cd ..
)

echo ✅ جميع الحزم مثبتة

:: تشغيل النظام
echo.
echo 🚀 بدء تشغيل النظام المحلي...
echo.
echo 📋 سيتم تشغيل:
echo    - Frontend: http://localhost:3000
echo    - Backend API: http://localhost:5000
echo    - Socket.IO: http://localhost:5001
echo.
echo ⚠️  تأكد من تشغيل MongoDB أولاً باستخدام: start-mongodb-local.bat
echo.

:: تشغيل الباك اند والفرونت اند معاً
start "Desha Coffee Backend" cmd /k "cd backend && npm run dev:local"
timeout /t 3
start "Desha Coffee Frontend" cmd /k "npm run dev:local"

echo.
echo ✅ تم بدء تشغيل النظام المحلي
echo 🌐 افتح http://localhost:3000 في المتصفح
echo.
echo 📝 لإيقاف النظام: أغلق نوافذ الطرفية أو اضغط Ctrl+C
echo.
pause

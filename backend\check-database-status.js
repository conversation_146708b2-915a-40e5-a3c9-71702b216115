const mongoose = require('mongoose');
require('dotenv').config();

// Connect to MongoDB
async function checkDatabaseStatus() {
  try {
    console.log('Connecting to MongoDB Atlas...');
    await mongoose.connect(process.env.MONGODB_URI);
    console.log('✅ Connected to MongoDB Atlas successfully!');

    // Get all collections
    const collections = await mongoose.connection.db.listCollections().toArray();
    console.log('\n📊 Available Collections:');
    collections.forEach(col => console.log(`- ${col.name}`));

    // Check orders collection specifically
    const ordersCollection = mongoose.connection.db.collection('orders');
    const ordersCount = await ordersCollection.countDocuments();
    console.log(`\n📦 Total Orders: ${ordersCount}`);

    if (ordersCount > 0) {
      // Count orders by waiter
      const waiterStats = await ordersCollection.aggregate([
        {
          $group: {
            _id: "$waiterName",
            count: { $sum: 1 },
            totalValue: { $sum: "$totalAmount" }
          }
        },
        { $sort: { count: -1 } }
      ]).toArray();

      console.log('\n👥 Orders by Waiter:');
      waiterStats.forEach(stat => {
        console.log(`- ${stat._id}: ${stat.count} orders, Total: ${stat.totalValue?.toFixed(2) || 0} EGP`);
      });

      // Check order statuses
      const statusStats = await ordersCollection.aggregate([
        {
          $group: {
            _id: "$status",
            count: { $sum: 1 }
          }
        }
      ]).toArray();

      console.log('\n📋 Orders by Status:');
      statusStats.forEach(stat => {
        console.log(`- ${stat._id}: ${stat.count} orders`);
      });

      // Sample orders
      const sampleOrders = await ordersCollection.find().limit(3).toArray();
      console.log('\n📝 Sample Orders:');
      sampleOrders.forEach((order, index) => {
        console.log(`${index + 1}. Order #${order.orderNumber} - ${order.waiterName} - ${order.totalAmount} EGP - ${order.status}`);
      });
    }

    // Check users collection
    const usersCollection = mongoose.connection.db.collection('users');
    const usersCount = await usersCollection.countDocuments();
    console.log(`\n👤 Total Users: ${usersCount}`);

    if (usersCount > 0) {
      const waiters = await usersCollection.find({ role: 'waiter' }).toArray();
      console.log('\n🧑‍💼 Waiters in Database:');
      waiters.forEach(waiter => {
        console.log(`- ${waiter.name} (${waiter.username})`);
      });
    }

  } catch (error) {
    console.error('❌ Error:', error.message);
  } finally {
    await mongoose.connection.close();
    console.log('\n🔐 Database connection closed.');
  }
}

checkDatabaseStatus();

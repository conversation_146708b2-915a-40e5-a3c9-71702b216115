<!DOCTYPE html>
<html lang="ar">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار اتصال نظام المقهى</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; direction: rtl; }
        .status { padding: 10px; margin: 10px 0; border-radius: 5px; }
        .connected { background-color: #d4edda; color: #155724; border: 1px solid #c3e6cb; }
        .disconnected { background-color: #f8d7da; color: #721c24; border: 1px solid #f5c6cb; }
        .loading { background-color: #fff3cd; color: #856404; border: 1px solid #ffeaa7; }
    </style>
</head>
<body>
    <h1>اختبار اتصال نظام إدارة المقهى</h1>
    
    <div id="frontend-status" class="status loading">
        🔄 جاري فحص الواجهة الأمامية...
    </div>
    
    <div id="backend-status" class="status loading">
        🔄 جاري فحص الخادم الخلفي...
    </div>
    
    <div id="database-status" class="status loading">
        🔄 جاري فحص قاعدة البيانات...
    </div>

    <script>
        // فحص حالة الواجهة الأمامية
        document.getElementById('frontend-status').innerHTML = '✅ الواجهة الأمامية: متصلة';
        document.getElementById('frontend-status').className = 'status connected';

        // فحص حالة الخادم الخلفي
        fetch('http://localhost:5000/health')
            .then(response => response.json())
            .then(data => {
                document.getElementById('backend-status').innerHTML = '✅ الخادم الخلفي: متصل';
                document.getElementById('backend-status').className = 'status connected';
                
                // فحص قاعدة البيانات من خلال الخادم
                if (data.status === 'healthy') {
                    document.getElementById('database-status').innerHTML = '✅ قاعدة البيانات: متصلة';
                    document.getElementById('database-status').className = 'status connected';
                }
            })
            .catch(error => {
                document.getElementById('backend-status').innerHTML = '❌ الخادم الخلفي: غير متصل';
                document.getElementById('backend-status').className = 'status disconnected';
                
                document.getElementById('database-status').innerHTML = '❌ قاعدة البيانات: غير متصلة';
                document.getElementById('database-status').className = 'status disconnected';
            });
    </script>
</body>
</html>

import { Link, useNavigate, useLocation } from 'react-router-dom';
import ThemeToggle from './components/ThemeToggle';

interface NavigationBarProps {
  role: 'waiter' | 'chef' | 'manager';
}

export default function NavigationBar({ role }: NavigationBarProps) {
  const navigate = useNavigate();
  const location = useLocation();

  const handleLogout = () => {
    localStorage.removeItem('user');
    navigate('/');
  };

  // تحديد الرابط النشط
  const isActive = (path: string) => {
    return location.pathname === path;
  };

  const homePath = role === 'waiter' ? '/waiter' : role === 'chef' ? '/chef' : '/manager';

  return (
    <nav className="navbar">
      <div className="navbar-brand">
        <img src="/coffee-logo.svg" alt="شعار المقهى" />
        <span>نظام إدارة المقهى</span>
      </div>
      <div className="navbar-nav">
        <Link
          to={homePath}
          className={`nav-link ${isActive(homePath) ? 'active' : ''}`}
        >
          الرئيسية
        </Link>

        {role === 'waiter' && (
          <Link
            to="/waiter/orders"
            className={`nav-link ${isActive('/waiter/orders') ? 'active' : ''}`}
          >
            الطلبات
          </Link>
        )}

        {role === 'chef' && (
          <Link
            to="/chef/orders"
            className={`nav-link ${isActive('/chef/orders') ? 'active' : ''}`}
          >
            طلبات التحضير
          </Link>
        )}

        {role === 'manager' && (
          <>
            <Link
              to="/manager/orders"
              className={`nav-link ${isActive('/manager/orders') ? 'active' : ''}`}
            >
              كل الطلبات
            </Link>
            <Link
              to="/manager/employees"
              className={`nav-link ${isActive('/manager/employees') ? 'active' : ''}`}
            >
              الموظفون
            </Link>
            <Link
              to="/manager/reports"
              className={`nav-link ${isActive('/manager/reports') ? 'active' : ''}`}
            >
              التقارير
            </Link>
            <Link
              to="/manager/inventory"
              className={`nav-link ${isActive('/manager/inventory') ? 'active' : ''}`}
            >
              المخزون
            </Link>
          </>
        )}

        <ThemeToggle />

        <button onClick={handleLogout} className="logout-btn">
          <i className="fas fa-sign-out-alt ml-1"></i>
          تسجيل الخروج
        </button>
      </div>
    </nav>
  );
}

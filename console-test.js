// Quick test script for the browser console
// Copy and paste this into the browser console on http://localhost:3000

(async function testConnection() {
    try {
        console.log('=== تشخيص مشكلة الاتصال ===');
        
        // 1. اختبار مباشر بـ fetch
        console.log('1. اختبار مباشر بـ fetch:');
        const directResponse = await fetch('http://localhost:5000/health');
        const directData = await directResponse.json();
        console.log('   - Direct fetch response:', {
            ok: directResponse.ok,
            status: directResponse.status,
            data: directData
        });
        
        // 2. اختبار باستخدام دالة getApiUrl من التطبيق
        console.log('2. اختبار getApiUrl:');
        // تحقق من وجود الدالة
        if (typeof getApiUrl !== 'undefined') {
            const apiUrl = getApiUrl('/health');
            console.log('   - API URL:', apiUrl);
        } else {
            console.log('   - getApiUrl غير متاحة، جاري استخدام الرابط المباشر');
        }
        
        // 3. فحص متغيرات البيئة
        console.log('3. متغيرات البيئة:');
        console.log('   - VITE_API_URL من import.meta.env:', import.meta.env?.VITE_API_URL);
        console.log('   - جميع متغيرات VITE:', Object.keys(import.meta.env || {}).filter(k => k.startsWith('VITE_')));
        
        // 4. اختبار checkServerHealth إذا كانت متاحة
        console.log('4. اختبار checkServerHealth:');
        if (typeof checkServerHealth !== 'undefined') {
            const healthResponse = await checkServerHealth();
            console.log('   - checkServerHealth response:', healthResponse);
            
            // تطبيق نفس منطق ConnectionStatus
            const healthData = healthResponse.data;
            const isServerHealthy = healthResponse.success === true && healthData?.status === 'healthy';
            const isDatabaseConnected = healthResponse.success === true && healthData?.database?.connected === true;
            
            console.log('   - Server healthy:', isServerHealthy);
            console.log('   - Database connected:', isDatabaseConnected);
            console.log('   - Overall connected:', isServerHealthy && isDatabaseConnected);
        } else {
            console.log('   - checkServerHealth غير متاحة في النطاق العام');
        }
        
        // 5. فحص Local Storage
        console.log('5. Local Storage:');
        console.log('   - token:', localStorage.getItem('token') ? 'موجود' : 'غير موجود');
        console.log('   - authToken:', localStorage.getItem('authToken') ? 'موجود' : 'غير موجود');
        
        console.log('=== انتهى التشخيص ===');
        
    } catch (error) {
        console.error('خطأ في تشخيص الاتصال:', error);
    }
})();

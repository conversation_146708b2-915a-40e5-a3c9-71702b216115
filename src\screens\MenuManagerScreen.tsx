import React, { useState } from 'react';
import { authenticatedGet, authenticatedPost, authenticatedPut, authenticatedDelete } from '../utils/apiHelpers';
import { useToast } from '../hooks/useToast';
// استدعاء ملف CSS الجديد المنظم
import '../styles/manager/MenuManagerScreen.css';

interface MenuItem {
  _id: string;
  name: string;
  price: number;
  category: string | any; // يمكن أن يكون string أو object
  categoryId?: string;
  description?: string;
  isAvailable: boolean;
  available?: boolean; // للتوافق مع الحقول القديمة
  image?: string;
  preparationTime?: number;
  ingredients?: string[];
  calories?: number;
  createdAt: string;
  updatedAt: string;
}

interface Category {
  _id: string;
  name: string;
  description?: string;
  isActive: boolean;
}

interface MenuManagerScreenProps {
  menuItems: MenuItem[];
  categories: Category[];
  onMenuItemsUpdate: () => void;
  loading: boolean;
}

const MenuManagerScreen: React.FC<MenuManagerScreenProps> = ({
  menuItems,
  categories,
  onMenuItemsUpdate,
  loading
}) => {
  const { showSuccess, showError } = useToast();
  
  // دالة للتحقق من حالة التوفر (يدعم isAvailable و available)
  const getItemAvailability = (item: MenuItem): boolean => {
    // التحقق من isAvailable أولاً، ثم available كخيار احتياطي
    if (item.isAvailable !== undefined) {
      return item.isAvailable;
    }
    if (item.available !== undefined) {
      return item.available;
    }
    // افتراضياً المنتج متاح
    return true;
  };
  
  const [menuScreenSearchTerm, setMenuScreenSearchTerm] = useState('');
  const [menuScreenCategoryFilter, setMenuScreenCategoryFilter] = useState<string>('all');
  const [menuScreenAvailabilityFilter, setMenuScreenAvailabilityFilter] = useState<'all' | 'available' | 'unavailable'>('all');
  const [menuScreenShowModal, setMenuScreenShowModal] = useState(false);
  const [menuScreenEditingItem, setMenuScreenEditingItem] = useState<MenuItem | null>(null);
  const [menuScreenFormData, setMenuScreenFormData] = useState({
    name: '',
    price: 0,
    category: '',
    categoryId: '',
    description: '',
    preparationTime: 0,
    ingredients: [] as string[],
    calories: 0
  });
  const [menuScreenIngredientInput, setMenuScreenIngredientInput] = useState('');

  // Filter menu items
  const filteredMenuItems = menuItems.filter(item => {
    const matchesSearch = item.name.toLowerCase().includes(menuScreenSearchTerm.toLowerCase());
    
    const itemCategoryId = item.categoryId || (typeof item.category === 'object' ? item.category?._id : item.category);
    const matchesCategory = menuScreenCategoryFilter === 'all' || itemCategoryId === menuScreenCategoryFilter;
    
    const isItemAvailable = getItemAvailability(item);
    const matchesAvailability = menuScreenAvailabilityFilter === 'all' || 
      (menuScreenAvailabilityFilter === 'available' && isItemAvailable) ||
      (menuScreenAvailabilityFilter === 'unavailable' && !isItemAvailable);
    
    return matchesSearch && matchesCategory && matchesAvailability;
  });

  // Get category name helper
  const getCategoryName = (categoryData: string | any): string => {
    if (typeof categoryData === 'string') {
      const category = categories.find(cat => cat._id === categoryData);
      return category?.name || categoryData;
    }
    if (typeof categoryData === 'object' && categoryData) {
      return categoryData.name || categoryData._id || 'غير محدد';
    }
    return 'غير محدد';
  };

  const handleMenuScreenSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    try {
      const menuItemData = {
        ...menuScreenFormData,
        price: Number(menuScreenFormData.price),
        preparationTime: Number(menuScreenFormData.preparationTime) || undefined,
        calories: Number(menuScreenFormData.calories) || undefined,
        categoryId: menuScreenFormData.categoryId || menuScreenFormData.category,
        category: menuScreenFormData.categoryId || menuScreenFormData.category
      };

      if (menuScreenEditingItem) {
        const response = await authenticatedPut(`/api/v1/products/${menuScreenEditingItem._id}`, menuItemData);
        if (response.success) {
          showSuccess('تم تحديث عنصر القائمة بنجاح');
          onMenuItemsUpdate();
          handleMenuScreenCloseModal();
        }
      } else {
        const response = await authenticatedPost('/api/v1/products', menuItemData);
        if (response.success) {
          showSuccess('تم إضافة عنصر جديد للقائمة');
          onMenuItemsUpdate();
          handleMenuScreenCloseModal();
        }
      }
    } catch (error) {
      showError('حدث خطأ أثناء حفظ عنصر القائمة');
    }
  };

  const handleMenuScreenEdit = (item: MenuItem) => {
    setMenuScreenEditingItem(item);
    setMenuScreenFormData({
      name: item.name,
      price: item.price,
      category: typeof item.category === 'string' ? item.category : item.category?._id || '',
      categoryId: item.categoryId || (typeof item.category === 'object' ? item.category?._id : item.category) || '',
      description: item.description || '',
      preparationTime: item.preparationTime || 0,
      ingredients: item.ingredients || [],
      calories: item.calories || 0
    });
    setMenuScreenShowModal(true);
  };

  const handleMenuScreenDelete = async (itemId: string) => {
    if (!window.confirm('هل أنت متأكد من حذف هذا العنصر؟')) return;
    
    try {
      const response = await authenticatedDelete(`/api/v1/products/${itemId}`);
      if (response.success) {
        showSuccess('تم حذف عنصر القائمة بنجاح');
        onMenuItemsUpdate();
      }
    } catch (error) {
      showError('حدث خطأ أثناء حذف عنصر القائمة');
    }
  };

  const handleMenuScreenToggleAvailability = async (item: MenuItem) => {
    try {
      const currentAvailability = getItemAvailability(item);
      const response = await authenticatedPut(`/api/v1/products/${item._id}`, {
        isAvailable: !currentAvailability,
        available: !currentAvailability // للتوافق مع أنظمة قديمة
      });
      
      if (response.success) {
        showSuccess(`تم ${!currentAvailability ? 'إتاحة' : 'إخفاء'} العنصر`);
        onMenuItemsUpdate();
      }
    } catch (error) {
      showError('حدث خطأ أثناء تغيير حالة العنصر');
    }
  };

  const handleMenuScreenCloseModal = () => {
    setMenuScreenShowModal(false);
    setMenuScreenEditingItem(null);
    setMenuScreenFormData({
      name: '',
      price: 0,
      category: '',
      categoryId: '',
      description: '',
      preparationTime: 0,
      ingredients: [],
      calories: 0
    });
    setMenuScreenIngredientInput('');
  };

  const handleMenuScreenAddIngredient = () => {
    if (menuScreenIngredientInput.trim() && !menuScreenFormData.ingredients.includes(menuScreenIngredientInput.trim())) {
      setMenuScreenFormData(prev => ({
        ...prev,
        ingredients: [...prev.ingredients, menuScreenIngredientInput.trim()]
      }));
      setMenuScreenIngredientInput('');
    }
  };

  const handleMenuScreenRemoveIngredient = (ingredient: string) => {
    setMenuScreenFormData(prev => ({
      ...prev,
      ingredients: prev.ingredients.filter(ing => ing !== ingredient)
    }));
  };

  return (
    <div className="menu-manager-screen">
      {/* Header */}
      <div className="menu-manager-screen-header">
        <div className="menu-manager-screen-title">
          <h1>
            <i className="fas fa-utensils" aria-hidden="true"></i>
            إدارة القائمة
          </h1>
        </div>
        <div className="menu-manager-screen-actions">
          <button
            className="menu-manager-screen-add-btn"
            onClick={() => setMenuScreenShowModal(true)}
            title="إضافة منتج جديد"
          >
            <i className="fas fa-plus" aria-hidden="true"></i>
            إضافة منتج جديد
          </button>
          <button
            className="menu-manager-screen-refresh-btn"
            onClick={onMenuItemsUpdate}
            title="تحديث القائمة"
          >
            <i className="fas fa-sync-alt" aria-hidden="true"></i>
            تحديث
          </button>
        </div>
      </div>

      {/* Filters */}
      <div className="menu-manager-screen-filters">
        <div className="menu-manager-screen-search">
          <i className="fas fa-search" aria-hidden="true"></i>
          <input
            type="text"
            placeholder="البحث في المنتجات..."
            value={menuScreenSearchTerm}
            onChange={(e) => setMenuScreenSearchTerm(e.target.value)}
            aria-label="البحث في المنتجات"
          />
        </div>
        
        <select
          value={menuScreenCategoryFilter}
          onChange={(e) => setMenuScreenCategoryFilter(e.target.value)}
          className="menu-manager-screen-category-filter"
          aria-label="تصفية حسب الفئة"
        >
          <option value="all">جميع الفئات</option>
          {categories.map(category => (
            <option key={category._id} value={category._id}>
              {category.name}
            </option>
          ))}
        </select>
        
        <select
          value={menuScreenAvailabilityFilter}
          onChange={(e) => setMenuScreenAvailabilityFilter(e.target.value as 'all' | 'available' | 'unavailable')}
          className="menu-manager-screen-availability-filter"
          aria-label="تصفية حسب التوفر"
        >
          <option value="all">جميع المنتجات</option>
          <option value="available">المتاح فقط</option>
          <option value="unavailable">غير المتاح فقط</option>
        </select>
      </div>

      {/* Stats */}
      <div className="menu-manager-screen-stats">
        <div className="menu-manager-screen-stat-card total">
          <div className="menu-manager-screen-stat-icon">
            <i className="fas fa-utensils"></i>
          </div>
          <div className="menu-manager-screen-stat-content">
            <h3>{menuItems.length}</h3>
            <p>إجمالي المنتجات</p>
          </div>
        </div>
        
        <div className="menu-manager-screen-stat-card available">
          <div className="menu-manager-screen-stat-icon">
            <i className="fas fa-check-circle"></i>
          </div>
          <div className="menu-manager-screen-stat-content">
            <h3>{menuItems.filter(item => getItemAvailability(item)).length}</h3>
            <p>المنتجات المتاحة</p>
          </div>
        </div>
        
        <div className="menu-manager-screen-stat-card categories">
          <div className="menu-manager-screen-stat-icon">
            <i className="fas fa-tags"></i>
          </div>
          <div className="menu-manager-screen-stat-content">
            <h3>{categories.filter(cat => cat.isActive).length}</h3>
            <p>الفئات النشطة</p>
          </div>
        </div>
        
        <div className="menu-manager-screen-stat-card average-price">
          <div className="menu-manager-screen-stat-icon">
            <i className="fas fa-money-bill-wave"></i>
          </div>
          <div className="menu-manager-screen-stat-content">
            <h3>
              {menuItems.length > 0 
                ? (menuItems.reduce((sum, item) => sum + item.price, 0) / menuItems.length).toFixed(2)
                : '0.00'
              }
            </h3>
            <p>متوسط السعر (ج.م)</p>
          </div>
        </div>
      </div>

      {/* Menu Items Grid */}
      <div className="menu-manager-screen-grid">
        {loading ? (
          <div className="menu-manager-screen-loading">
            <i className="fas fa-spinner fa-spin"></i>
            <p>جاري تحميل القائمة...</p>
          </div>
        ) : (
          <>
            {filteredMenuItems.map(item => {
              const isAvailable = getItemAvailability(item);
              return (
                <div 
                  key={item._id}
                  className={`menu-manager-screen-item-card ${isAvailable ? 'available' : 'unavailable'}`}
                >
                  <div className="menu-manager-screen-item-header">
                    <div className="menu-manager-screen-item-name">
                      <h4>{item.name}</h4>
                      <span className="menu-manager-screen-item-category">
                        {getCategoryName(item.categoryId || item.category)}
                      </span>
                    </div>
                    <div className={`menu-manager-screen-item-status ${isAvailable ? 'available' : 'unavailable'}`}>
                      {isAvailable ? 'متاح' : 'غير متاح'}
                    </div>
                  </div>

                  <div className="menu-manager-screen-item-info">
                    {item.description && (
                      <p className="menu-manager-screen-item-description">
                        {item.description}
                      </p>
                    )}
                    
                    <div className="menu-manager-screen-item-details">
                      <div className="menu-manager-screen-item-price">
                        <i className="fas fa-tag"></i>
                        <span>{item.price.toFixed(2)} ج.م</span>
                      </div>
                      
                      {item.preparationTime && (
                        <div className="menu-manager-screen-item-time">
                          <i className="fas fa-clock"></i>
                          <span>{item.preparationTime} دقيقة</span>
                        </div>
                      )}
                      
                      {item.calories && (
                        <div className="menu-manager-screen-item-calories">
                          <i className="fas fa-fire"></i>
                          <span>{item.calories} سعرة</span>
                        </div>
                      )}
                    </div>

                    {item.ingredients && item.ingredients.length > 0 && (
                      <div className="menu-manager-screen-item-ingredients">
                        <h5>المكونات:</h5>
                        <div className="menu-manager-screen-ingredients-list">
                          {item.ingredients.slice(0, 3).map((ingredient, index) => (
                            <span key={index} className="menu-manager-screen-ingredient-tag">
                              {ingredient}
                            </span>
                          ))}
                          {item.ingredients.length > 3 && (
                            <span className="menu-manager-screen-ingredient-tag more">
                              +{item.ingredients.length - 3} أخرى
                            </span>
                          )}
                        </div>
                      </div>
                    )}
                  </div>

                  <div className="menu-manager-screen-item-actions">
                    <button
                      className="menu-manager-screen-action-btn edit"
                      onClick={() => handleMenuScreenEdit(item)}
                      title="تعديل"
                    >
                      <i className="fas fa-edit"></i>
                    </button>
                    
                    <button
                      className="menu-manager-screen-action-btn toggle"
                      onClick={() => handleMenuScreenToggleAvailability(item)}
                      title={isAvailable ? 'إخفاء' : 'إظهار'}
                    >
                      <i className={`fas ${isAvailable ? 'fa-eye-slash' : 'fa-eye'}`}></i>
                    </button>
                    
                    <button
                      className="menu-manager-screen-action-btn delete"
                      onClick={() => handleMenuScreenDelete(item._id)}
                      title="حذف"
                    >
                      <i className="fas fa-trash"></i>
                    </button>
                  </div>
                </div>
              );
            })}
          </>
        )}
        
        {!loading && filteredMenuItems.length === 0 && (
          <div className="menu-manager-screen-no-data">
            <i className="fas fa-utensils"></i>
            <h3>لا توجد منتجات</h3>
            <p>لا توجد منتجات تطابق معايير البحث المحددة</p>
          </div>
        )}
      </div>

      {/* Modal */}
      {menuScreenShowModal && (
        <div className="menu-manager-screen-modal-overlay" onClick={handleMenuScreenCloseModal}>
          <div className="menu-manager-screen-modal" onClick={(e) => e.stopPropagation()}>
            <div className="menu-manager-screen-modal-header">
              <h2>
                {menuScreenEditingItem ? 'تعديل منتج' : 'إضافة منتج جديد'}
              </h2>
              <button
                className="menu-manager-screen-modal-close"
                onClick={handleMenuScreenCloseModal}
                title="إغلاق"
              >
                <i className="fas fa-times"></i>
              </button>
            </div>

            <form onSubmit={handleMenuScreenSubmit} className="menu-manager-screen-modal-form">
              <div className="menu-manager-screen-form-group">
                <label htmlFor="product-name">اسم المنتج *</label>
                <input
                  id="product-name"
                  type="text"
                  value={menuScreenFormData.name}
                  onChange={(e) => setMenuScreenFormData(prev => ({ ...prev, name: e.target.value }))}
                  required
                  placeholder="أدخل اسم المنتج"
                />
              </div>

              <div className="menu-manager-screen-form-row">
                <div className="menu-manager-screen-form-group">
                  <label htmlFor="product-price">السعر (ج.م) *</label>
                  <input
                    id="product-price"
                    type="number"
                    step="0.01"
                    min="0"
                    value={menuScreenFormData.price}
                    onChange={(e) => setMenuScreenFormData(prev => ({ ...prev, price: Number(e.target.value) }))}
                    required
                    placeholder="0.00"
                  />
                </div>

                <div className="menu-manager-screen-form-group">
                  <label htmlFor="product-category">الفئة *</label>
                  <select
                    id="product-category"
                    value={menuScreenFormData.categoryId || menuScreenFormData.category}
                    onChange={(e) => setMenuScreenFormData(prev => ({ 
                      ...prev, 
                      categoryId: e.target.value,
                      category: e.target.value 
                    }))}
                    required
                  >
                    <option value="">اختر الفئة</option>
                    {categories.map(category => (
                      <option key={category._id} value={category._id}>
                        {category.name}
                      </option>
                    ))}
                  </select>
                </div>
              </div>

              <div className="menu-manager-screen-form-group">
                <label htmlFor="product-description">الوصف</label>
                <textarea
                  id="product-description"
                  value={menuScreenFormData.description}
                  onChange={(e) => setMenuScreenFormData(prev => ({ ...prev, description: e.target.value }))}
                  placeholder="وصف المنتج (اختياري)"
                  rows={3}
                />
              </div>

              <div className="menu-manager-screen-form-row">
                <div className="menu-manager-screen-form-group">
                  <label htmlFor="product-prep-time">وقت التحضير (دقيقة)</label>
                  <input
                    id="product-prep-time"
                    type="number"
                    min="0"
                    value={menuScreenFormData.preparationTime}
                    onChange={(e) => setMenuScreenFormData(prev => ({ ...prev, preparationTime: Number(e.target.value) }))}
                    placeholder="0"
                  />
                </div>

                <div className="menu-manager-screen-form-group">
                  <label htmlFor="product-calories">السعرات الحرارية</label>
                  <input
                    id="product-calories"
                    type="number"
                    min="0"
                    value={menuScreenFormData.calories}
                    onChange={(e) => setMenuScreenFormData(prev => ({ ...prev, calories: Number(e.target.value) }))}
                    placeholder="0"
                  />
                </div>
              </div>

              <div className="menu-manager-screen-form-group">
                <label htmlFor="product-ingredients">المكونات</label>
                <div className="menu-manager-screen-ingredients-input">
                  <input
                    id="product-ingredients"
                    type="text"
                    value={menuScreenIngredientInput}
                    onChange={(e) => setMenuScreenIngredientInput(e.target.value)}
                    placeholder="أدخل مكون واضغط إضافة"
                    onKeyPress={(e) => {
                      if (e.key === 'Enter') {
                        e.preventDefault();
                        handleMenuScreenAddIngredient();
                      }
                    }}
                  />
                  <button
                    type="button"
                    onClick={handleMenuScreenAddIngredient}
                    className="menu-manager-screen-add-ingredient-btn"
                    title="إضافة مكون"
                  >
                    <i className="fas fa-plus"></i>
                  </button>
                </div>
                
                {menuScreenFormData.ingredients.length > 0 && (
                  <div className="menu-manager-screen-ingredients-tags">
                    {menuScreenFormData.ingredients.map((ingredient, index) => (
                      <span key={index} className="menu-manager-screen-ingredient-tag">
                        {ingredient}
                        <button
                          type="button"
                          onClick={() => handleMenuScreenRemoveIngredient(ingredient)}
                          className="menu-manager-screen-remove-ingredient"
                          title="حذف المكون"
                        >
                          <i className="fas fa-times"></i>
                        </button>
                      </span>
                    ))}
                  </div>
                )}
              </div>

              <div className="menu-manager-screen-modal-actions">
                <button
                  type="button"
                  onClick={handleMenuScreenCloseModal}
                  className="menu-manager-screen-cancel-btn"
                >
                  إلغاء
                </button>
                <button
                  type="submit"
                  className="menu-manager-screen-save-btn"
                >
                  {menuScreenEditingItem ? 'حفظ التغييرات' : 'إضافة المنتج'}
                </button>
              </div>
            </form>
          </div>
        </div>
      )}
    </div>
  );
};

export default MenuManagerScreen;

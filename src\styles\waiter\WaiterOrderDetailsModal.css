/* ====================================
   Waiter Order Details Modal Enhancement
   تحسين modal تفاصيل الطلب للنادل
   ==================================== */

/* Modal Overlay - Enhanced */
.modal-overlay {
  position: fixed !important;
  top: 0 !important;
  left: 0 !important;
  right: 0 !important;
  bottom: 0 !important;
  background: rgba(0, 0, 0, 0.65) !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  z-index: 1000000 !important;
  padding: 1rem !important;
  -webkit-backdrop-filter: blur(8px);
  backdrop-filter: blur(8px);
  animation: fadeInOverlay 0.3s ease-out;
}

@keyframes fadeInOverlay {
  from {
    opacity: 0;
    backdrop-filter: blur(0px);
  }
  to {
    opacity: 1;
    backdrop-filter: blur(8px);
  }
}

/* Order Details Modal Container - Enhanced */
.modal-content.order-details-modal {
  background: linear-gradient(145deg, #ffffff 0%, #f8fafc 50%, #ffffff 100%) !important;
  border: none !important;
  border-radius: 20px !important;
  box-shadow: 
    0 25px 60px rgba(0, 0, 0, 0.15),
    0 8px 25px rgba(0, 0, 0, 0.1),
    inset 0 1px 0 rgba(255, 255, 255, 0.9) !important;
  max-width: 900px !important;
  width: 95% !important;
  max-height: 90vh !important;
  overflow: hidden !important;
  animation: slideInModal 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
  position: relative;
  font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
}

@keyframes slideInModal {
  from {
    opacity: 0;
    transform: translateY(-30px) scale(0.95);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

/* Modal Header - Enhanced */
.order-details-modal .modal-header {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%) !important;
  color: white !important;
  padding: 1.5rem 2rem !important;
  border-radius: 20px 20px 0 0 !important;
  border-bottom: none !important;
  position: relative;
  overflow: hidden;
}

.order-details-modal .modal-header::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(45deg, rgba(255,255,255,0.1) 0%, transparent 50%, rgba(255,255,255,0.1) 100%);
  pointer-events: none;
}

.order-details-modal .modal-header h2 {
  font-size: 1.4rem !important;
  font-weight: 600 !important;
  margin: 0 !important;
  display: flex !important;
  align-items: center !important;
  gap: 0.75rem !important;
  position: relative;
  z-index: 1;
}

.order-details-modal .modal-header h2 i {
  font-size: 1.2rem !important;
  opacity: 0.9;
}

.order-details-modal .modal-close {
  background: rgba(255, 255, 255, 0.2) !important;
  border: 1px solid rgba(255, 255, 255, 0.3) !important;
  color: white !important;
  width: 40px !important;
  height: 40px !important;
  border-radius: 50% !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  cursor: pointer !important;
  transition: all 0.3s ease !important;
  font-size: 1.1rem !important;
  position: relative;
  z-index: 1;
}

.order-details-modal .modal-close:hover {
  background: rgba(255, 255, 255, 0.3) !important;
  transform: rotate(90deg) scale(1.1);
}

/* Modal Body - Enhanced */
.order-details-modal .modal-body {
  padding: 2rem !important;
  max-height: calc(90vh - 160px) !important;
  overflow-y: auto !important;
  background: #ffffff;
}

/* Custom Scrollbar */
.order-details-modal .modal-body::-webkit-scrollbar {
  width: 6px;
}

.order-details-modal .modal-body::-webkit-scrollbar-track {
  background: #f1f3f4;
  border-radius: 3px;
}

.order-details-modal .modal-body::-webkit-scrollbar-thumb {
  background: linear-gradient(135deg, #667eea, #764ba2);
  border-radius: 3px;
}

.order-details-modal .modal-body::-webkit-scrollbar-thumb:hover {
  background: linear-gradient(135deg, #5a6fd8, #6a4190);
}

/* Order Info Section - Enhanced */
.order-info-section {
  margin-bottom: 2rem;
  background: linear-gradient(135deg, #f8fafc 0%, #e3f2fd 100%);
  border-radius: 16px;
  padding: 1.5rem;
  border: 1px solid rgba(102, 126, 234, 0.1);
  position: relative;
  overflow: hidden;
}

.order-info-section::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 3px;
  background: linear-gradient(90deg, #667eea, #764ba2);
}

.order-info-section h3 {
  font-size: 1.2rem;
  font-weight: 600;
  color: #2d3748;
  margin: 0 0 1.5rem 0;
  display: flex;
  align-items: center;
  gap: 0.75rem;
}

.order-info-section h3 i {
  font-size: 1.1rem;
  color: #667eea;
  background: rgba(102, 126, 234, 0.1);
  padding: 0.5rem;
  border-radius: 50%;
  width: 35px;
  height: 35px;
  display: flex;
  align-items: center;
  justify-content: center;
}

/* Order Info Grid - Enhanced */
.order-info-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 1.25rem;
}

.info-item {
  background: white;
  padding: 1rem;
  border-radius: 12px;
  border: 1px solid rgba(102, 126, 234, 0.08);
  display: flex;
  justify-content: space-between;
  align-items: center;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.info-item::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 3px;
  height: 100%;
  background: linear-gradient(135deg, #667eea, #764ba2);
  transform: scaleY(0);
  transition: transform 0.3s ease;
}

.info-item:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(102, 126, 234, 0.15);
}

.info-item:hover::before {
  transform: scaleY(1);
}

.info-item .label {
  font-weight: 600;
  color: #4a5568;
  font-size: 0.95rem;
}

.info-item .value {
  font-weight: 500;
  color: #2d3748;
  font-size: 0.95rem;
}

/* Status Badge - Enhanced */
.status-badge {
  padding: 0.5rem 1rem !important;
  border-radius: 25px !important;
  font-weight: 600 !important;
  font-size: 0.85rem !important;
  display: flex !important;
  align-items: center !important;
  gap: 0.5rem !important;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.status-badge.pending {
  background: linear-gradient(135deg, #fbbf24, #f59e0b) !important;
  color: white !important;
}

.status-badge.preparing {
  background: linear-gradient(135deg, #3b82f6, #1d4ed8) !important;
  color: white !important;
}

.status-badge.ready {
  background: linear-gradient(135deg, #10b981, #059669) !important;
  color: white !important;
}

.status-badge.completed {
  background: linear-gradient(135deg, #6b7280, #4b5563) !important;
  color: white !important;
}

/* Chef Name - Enhanced */
.chef-name {
  background: linear-gradient(135deg, #667eea, #764ba2) !important;
  color: white !important;
  padding: 0.5rem 1rem !important;
  border-radius: 20px !important;
  font-weight: 600 !important;
  display: flex !important;
  align-items: center !important;
  gap: 0.5rem !important;
  font-size: 0.9rem !important;
}

.chef-name i {
  font-size: 0.8rem;
}

/* Pricing Section - Enhanced */
.pricing-section-waiter {
  grid-column: 1 / -1;
  background: linear-gradient(135deg, #f0f9ff 0%, #e0f2fe 100%);
  border-radius: 12px;
  padding: 1.5rem;
  border: 1px solid rgba(14, 165, 233, 0.1);
  margin-top: 1rem;
}

.pricing-header {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  margin-bottom: 1rem;
  font-weight: 600;
  color: #0369a1;
  font-size: 1.1rem;
}

.pricing-header i {
  background: rgba(14, 165, 233, 0.1);
  padding: 0.5rem;
  border-radius: 50%;
  color: #0369a1;
}

.pricing-breakdown-waiter {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
}

.pricing-line {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0.75rem 1rem;
  background: white;
  border-radius: 8px;
  border: 1px solid rgba(14, 165, 233, 0.08);
}

.pricing-line.original .pricing-label {
  color: #6b7280;
  text-decoration: line-through;
}

.pricing-line.discount .pricing-label {
  color: #dc2626;
  font-weight: 600;
}

.pricing-line.discount .pricing-value {
  color: #dc2626;
  font-weight: 700;
}

.pricing-line.final {
  background: linear-gradient(135deg, #10b981, #059669);
  color: white;
  font-weight: 700;
  font-size: 1.1rem;
}

.pricing-line.total {
  background: linear-gradient(135deg, #667eea, #764ba2);
  color: white;
  font-weight: 700;
  font-size: 1.1rem;
}

/* Order Items Section - Enhanced */
.order-items-section {
  margin-bottom: 2rem;
  background: linear-gradient(135deg, #fefefe 0%, #f8fafc 100%);
  border-radius: 16px;
  padding: 1.5rem;
  border: 1px solid rgba(102, 126, 234, 0.1);
}

.order-items-section h3 {
  font-size: 1.2rem;
  font-weight: 600;
  color: #2d3748;
  margin: 0 0 1.5rem 0;
  display: flex;
  align-items: center;
  gap: 0.75rem;
}

.order-items-section h3 i {
  font-size: 1.1rem;
  color: #667eea;
  background: rgba(102, 126, 234, 0.1);
  padding: 0.5rem;
  border-radius: 50%;
  width: 35px;
  height: 35px;
  display: flex;
  align-items: center;
  justify-content: center;
}

/* Items List - Enhanced */
.items-list {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.item-card {
  background: white;
  border-radius: 12px;
  padding: 1.25rem;
  border: 1px solid rgba(102, 126, 234, 0.08);
  display: flex;
  justify-content: space-between;
  align-items: center;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.item-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 4px;
  height: 100%;
  background: linear-gradient(135deg, #667eea, #764ba2);
  transform: scaleY(0);
  transition: transform 0.3s ease;
}

.item-card:hover {
  transform: translateX(5px);
  box-shadow: 0 8px 25px rgba(102, 126, 234, 0.15);
}

.item-card:hover::before {
  transform: scaleY(1);
}

.item-info {
  flex: 1;
}

.item-name {
  font-weight: 600;
  color: #2d3748;
  font-size: 1rem;
  margin-bottom: 0.5rem;
}

.item-notes {
  color: #6b7280;
  font-size: 0.85rem;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  background: #f9fafb;
  padding: 0.5rem 0.75rem;
  border-radius: 6px;
  margin-top: 0.5rem;
}

.item-notes i {
  color: #fbbf24;
}

.item-details {
  display: flex;
  align-items: center;
  gap: 1rem;
  font-weight: 600;
}

.item-quantity {
  background: linear-gradient(135deg, #667eea, #764ba2);
  color: white;
  padding: 0.5rem 0.75rem;
  border-radius: 20px;
  font-size: 0.9rem;
  display: flex;
  align-items: center;
  gap: 0.25rem;
}

.item-price {
  color: #6b7280;
  font-size: 0.9rem;
}

.item-total {
  color: #059669;
  font-size: 1rem;
  font-weight: 700;
}

/* Discount Section - Enhanced */
.discount-section {
  background: linear-gradient(135deg, #fef3c7 0%, #fde68a 100%);
  border-radius: 16px;
  padding: 1.5rem;
  border: 1px solid rgba(245, 158, 11, 0.2);
  margin-bottom: 1rem;
}

.discount-section h3 {
  font-size: 1.2rem;
  font-weight: 600;
  color: #92400e;
  margin: 0 0 1rem 0;
  display: flex;
  align-items: center;
  gap: 0.75rem;
}

.discount-section h3 i {
  font-size: 1.1rem;
  color: #f59e0b;
  background: rgba(245, 158, 11, 0.1);
  padding: 0.5rem;
  border-radius: 50%;
  width: 35px;
  height: 35px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.discount-status {
  background: white;
  padding: 1rem 1.25rem;
  border-radius: 10px;
  border: 1px solid rgba(245, 158, 11, 0.2);
  display: flex;
  align-items: center;
  gap: 0.75rem;
  font-weight: 600;
}

.discount-status.pending {
  color: #f59e0b;
  background: linear-gradient(135deg, #fef3c7, #fde68a);
}

.discount-status.approved {
  color: #059669;
  background: linear-gradient(135deg, #d1fae5, #a7f3d0);
}

.discount-status.rejected {
  color: #dc2626;
  background: linear-gradient(135deg, #fee2e2, #fecaca);
}

.btn-discount {
  background: linear-gradient(135deg, #f59e0b, #d97706) !important;
  color: white !important;
  border: none !important;
  padding: 0.75rem 1.5rem !important;
  border-radius: 25px !important;
  font-weight: 600 !important;
  font-size: 0.95rem !important;
  cursor: pointer !important;
  transition: all 0.3s ease !important;
  display: flex !important;
  align-items: center !important;
  gap: 0.5rem !important;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  box-shadow: 0 4px 15px rgba(245, 158, 11, 0.3);
}

.btn-discount:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(245, 158, 11, 0.4);
  background: linear-gradient(135deg, #d97706, #b45309) !important;
}

/* Modal Footer - Enhanced */
.order-details-modal .modal-footer {
  background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%) !important;
  padding: 1.5rem 2rem !important;
  border-top: 1px solid rgba(102, 126, 234, 0.1) !important;
  border-radius: 0 0 20px 20px !important;
  display: flex !important;
  justify-content: flex-end !important;
  gap: 1rem !important;
}

.btn-deliver {
  background: linear-gradient(135deg, #10b981, #059669) !important;
  color: white !important;
  border: none !important;
  padding: 0.75rem 1.5rem !important;
  border-radius: 25px !important;
  font-weight: 600 !important;
  font-size: 0.95rem !important;
  cursor: pointer !important;
  transition: all 0.3s ease !important;
  display: flex !important;
  align-items: center !important;
  gap: 0.5rem !important;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  box-shadow: 0 4px 15px rgba(16, 185, 129, 0.3);
}

.btn-deliver:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(16, 185, 129, 0.4);
  background: linear-gradient(135deg, #059669, #047857) !important;
}

.btn-close {
  background: linear-gradient(135deg, #6b7280, #4b5563) !important;
  color: white !important;
  border: none !important;
  padding: 0.75rem 1.5rem !important;
  border-radius: 25px !important;
  font-weight: 600 !important;
  font-size: 0.95rem !important;
  cursor: pointer !important;
  transition: all 0.3s ease !important;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  box-shadow: 0 4px 15px rgba(107, 114, 128, 0.3);
}

.btn-close:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(107, 114, 128, 0.4);
  background: linear-gradient(135deg, #4b5563, #374151) !important;
}

/* Responsive Design */
@media (max-width: 768px) {
  .modal-content.order-details-modal {
    width: 98% !important;
    max-height: 95vh !important;
    border-radius: 16px !important;
  }

  .order-details-modal .modal-header {
    padding: 1.25rem 1.5rem !important;
    border-radius: 16px 16px 0 0 !important;
  }

  .order-details-modal .modal-header h2 {
    font-size: 1.2rem !important;
  }

  .order-details-modal .modal-body {
    padding: 1.5rem !important;
    max-height: calc(95vh - 140px) !important;
  }

  .order-info-grid {
    grid-template-columns: 1fr;
    gap: 1rem;
  }

  .item-card {
    flex-direction: column;
    align-items: flex-start;
    gap: 1rem;
  }

  .item-details {
    width: 100%;
    justify-content: space-between;
  }

  .order-details-modal .modal-footer {
    padding: 1.25rem 1.5rem !important;
    flex-direction: column;
    gap: 0.75rem;
  }

  .btn-deliver,
  .btn-close,
  .btn-discount {
    width: 100%;
    justify-content: center;
  }
}

@media (max-width: 480px) {
  .modal-overlay {
    padding: 0.5rem !important;
  }

  .modal-content.order-details-modal {
    width: 100% !important;
    max-height: 98vh !important;
    border-radius: 12px !important;
  }

  .order-details-modal .modal-header {
    padding: 1rem 1.25rem !important;
    border-radius: 12px 12px 0 0 !important;
  }

  .order-details-modal .modal-header h2 {
    font-size: 1.1rem !important;
    gap: 0.5rem !important;
  }

  .order-details-modal .modal-body {
    padding: 1.25rem !important;
    max-height: calc(98vh - 120px) !important;
  }

  .order-info-section,
  .order-items-section,
  .discount-section {
    padding: 1.25rem;
  }

  .info-item {
    padding: 0.75rem;
    flex-direction: column;
    align-items: flex-start;
    gap: 0.5rem;
  }

  .item-card {
    padding: 1rem;
  }

  .order-details-modal .modal-footer {
    padding: 1rem 1.25rem !important;
    border-radius: 0 0 12px 12px !important;
  }
}

/* Loading Animation */
@keyframes shimmer {
  0% {
    background-position: -200px 0;
  }
  100% {
    background-position: calc(200px + 100%) 0;
  }
}

.loading-shimmer {
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
  background-size: 200px 100%;
  animation: shimmer 1.5s infinite;
}

/* Accessibility Enhancements */
.order-details-modal *:focus {
  outline: 2px solid #667eea;
  outline-offset: 2px;
}

.order-details-modal button:focus {
  outline: 2px solid #667eea;
  outline-offset: 2px;
}

/* Print Styles */
@media print {
  .modal-overlay {
    position: static !important;
    background: none !important;
    backdrop-filter: none !important;
  }

  .modal-content.order-details-modal {
    box-shadow: none !important;
    border: 1px solid #ccc !important;
    max-width: none !important;
    width: 100% !important;
    max-height: none !important;
  }

  .order-details-modal .modal-header {
    background: #f5f5f5 !important;
    color: #333 !important;
  }

  .order-details-modal .modal-footer {
    display: none !important;
  }

  .btn-discount,
  .modal-close {
    display: none !important;
  }
}
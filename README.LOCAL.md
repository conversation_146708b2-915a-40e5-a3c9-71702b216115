# 🏠 البيئة المحلية لنظام إدارة مقهى ديشا

## 🚀 تشغيل سريع

```bash
# 1. تشغيل MongoDB
start-mongodb-local.bat

# 2. تهيئة قاعدة البيانات (المرة الأولى فقط)
node init-local-database.cjs

# 3. تشغيل النظام
start-local-development.bat

# 4. اختبار النظام
node test-local-system.cjs
```

## 🌐 عناوين الوصول

- **التطبيق**: http://localhost:3000
- **API**: http://localhost:5000
- **قاعدة البيانات**: mongodb://localhost:27017/deshacoffee_local

## 👤 بيانات الدخول

| المستخدم | كلمة المرور | الدور |
|---------|------------|-------|
| admin | admin123 | مدير |
| waiter1 | 123456 | نادل |
| chef1 | 123456 | طاهي |

## 📁 الملفات المهمة

```
📁 ملفات البيئة المحلية:
├── .env.local                    # متغيرات الفرونت اند
├── backend/.env.local            # متغيرات الباك اند
├── vite.config.local.js          # إعدادات Vite
├── init-local-database.cjs       # تهيئة قاعدة البيانات
├── test-local-system.cjs         # اختبار النظام
├── start-mongodb-local.bat       # تشغيل MongoDB
├── start-local-development.bat   # تشغيل النظام
└── LOCAL_DEVELOPMENT_GUIDE.md    # الدليل الشامل
```

## ⚡ أوامر سريعة

```bash
# تشغيل الفرونت اند فقط
npm run dev:local

# تشغيل الباك اند فقط
npm run backend:dev:local

# تشغيل كلاهما معاً
npm run dev:all:local

# إعادة تهيئة قاعدة البيانات
node init-local-database.cjs

# اختبار النظام
node test-local-system.cjs
```

## 🛠️ استكشاف الأخطاء

### المشكلة: لا يمكن الوصول للنظام
```bash
# تحقق من المنافذ
netstat -ano | findstr :3000
netstat -ano | findstr :5000

# أعد تشغيل النظام
start-local-development.bat
```

### المشكلة: خطأ في قاعدة البيانات
```bash
# تأكد من تشغيل MongoDB
start-mongodb-local.bat

# أعد تهيئة قاعدة البيانات
node init-local-database.cjs
```

### المشكلة: خطأ في التبعيات
```bash
# أعد تثبيت التبعيات
npm run fix-deps
cd backend && npm install
```

## 📊 مراقبة النظام

- **سجلات الباك اند**: طرفية الباك اند
- **سجلات MongoDB**: `logs/mongodb.log`
- **سجلات الفرونت اند**: متصفح Developer Tools

## 🔒 ملاحظات أمنية

⚠️ **للتطوير المحلي فقط**
- لا تستخدم في بيئة الإنتاج
- كلمات المرور ضعيفة عمداً للتطوير
- لا ترفع ملفات `.env.local` لـ Git

## 📚 وثائق إضافية

- 📖 [الدليل الشامل](LOCAL_DEVELOPMENT_GUIDE.md)
- 🔧 [إعدادات التطوير](vite.config.local.js)
- 🗄️ [سكريپت تهيئة قاعدة البيانات](init-local-database.cjs)

---

**✨ استمتع بالتطوير! ✨**

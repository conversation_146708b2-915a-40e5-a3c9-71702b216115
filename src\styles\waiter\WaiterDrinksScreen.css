/* ====================================
   WaiterDrinksScreen Component Styles
   ==================================== */

/* استيراد المتغيرات المميزة لشاشة القوائم */
@import '../variables/menu-variables.css';

/* متغيرات CSS خاصة بشاشة المشروبات */


/* Header شاشة المشروبات */
.waiter-drinks-screen .screen-header {
  background: linear-gradient(135deg, var(--menu-primary-color), var(--menu-secondary-color));
  padding: var(--menu-spacing-lg);
  border-radius: var(--menu-border-radius);
  margin-bottom: var(--menu-spacing-lg);
  color: white;
  box-shadow: var(--menu-shadow);
}

.waiter-drinks-screen .waiter-header-flex {
  display: flex;
  justify-content: space-between;
  align-items: center;
  gap: var(--menu-spacing-md);
}

.waiter-drinks-screen .screen-title {
  font-size: 1.75rem;
  font-weight: 700;
  margin: 0;
  display: flex;
  align-items: center;
  gap: var(--menu-spacing-sm);
}

.waiter-drinks-screen .screen-subtitle {
  margin: var(--menu-spacing-xs) 0 0 0;
  opacity: 0.9;
  font-size: 0.95rem;
}

.waiter-drinks-screen .flex-gap-8 {
  display: flex;
  gap: var(--menu-spacing-sm);
  align-items: center;
}

/* أزرار التحديث */
.waiter-drinks-screen .btn-refresh {
  background: rgba(255, 255, 255, 0.2);
  color: white;
  border: 1px solid rgba(255, 255, 255, 0.3);
  padding: var(--menu-spacing-sm) var(--menu-spacing-md);
  border-radius: var(--menu-border-radius);
  cursor: pointer;
  transition: all 0.3s ease;
  font-size: 0.9rem;
  display: flex;
  align-items: center;
  gap: var(--menu-spacing-xs);
}

.waiter-drinks-screen .btn-refresh:hover {
  background: rgba(255, 255, 255, 0.3);
  transform: translateY(-1px);
}

.waiter-drinks-screen .btn-refresh:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.waiter-drinks-screen .btn-refresh.loading {
  opacity: 0.8;
}

.waiter-drinks-screen .status-badge {
  background: rgba(255, 255, 255, 0.2);
  padding: var(--menu-spacing-xs) var(--menu-spacing-sm);
  border-radius: 20px;
  font-size: 0.85rem;
  display: flex;
  align-items: center;
  gap: var(--menu-spacing-xs);
  border: 1px solid rgba(255, 255, 255, 0.3);
}

/* قسم الفلاتر الرئيسي */
.waiter-drinks-screen .drinks-filters {
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.95), rgba(248, 249, 250, 0.95));
  border-radius: 15px;
  padding: var(--menu-spacing-md);
  margin-bottom: var(--menu-spacing-xl);
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
  border: 1px solid rgba(139, 69, 19, 0.1);
  position: relative;
  overflow: hidden;
}

.waiter-drinks-screen .drinks-filters::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 3px;
  background: linear-gradient(90deg, var(--menu-primary-color), var(--menu-secondary-color), var(--menu-primary-color));
  background-size: 200% 100%;
  animation: gradient-flow 3s ease-in-out infinite;
}

@keyframes gradient-flow {
  0%, 100% { background-position: 0% 50%; }
  50% { background-position: 100% 50%; }
}

/* قسم البحث المحسن */
.waiter-drinks-screen .search-section {
  margin-bottom: var(--menu-spacing-lg);
  padding: var(--menu-spacing-lg);
  background: linear-gradient(135deg, rgba(139, 69, 19, 0.05), rgba(160, 82, 45, 0.05));
  border-radius: var(--menu-border-radius);
  border: 1px solid rgba(139, 69, 19, 0.1);
  position: relative;
}

.waiter-drinks-screen .filter-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: var(--menu-spacing-md);
  padding-bottom: var(--menu-spacing-sm);
  border-bottom: 2px solid rgba(139, 69, 19, 0.1);
}

.waiter-drinks-screen .filter-title {
  font-size: 1.3rem;
  font-weight: 700;
  color: var(--menu-primary-color);
  margin: 0;
  display: flex;
  align-items: center;
  gap: var(--menu-spacing-xs);
}

.waiter-drinks-screen .filter-title i {
  font-size: 1.2rem;
  color: var(--menu-secondary-color);
}

.waiter-drinks-screen .results-counter {
  background: linear-gradient(135deg, var(--menu-primary-color), var(--menu-secondary-color));
  color: white;
  padding: var(--menu-spacing-xs) var(--menu-spacing-md);
  border-radius: 20px;
  font-size: 0.9rem;
  font-weight: 600;
  display: flex;
  align-items: center;
  gap: var(--menu-spacing-xs);
  box-shadow: 0 2px 8px rgba(139, 69, 19, 0.2);
  animation: pulse-counter 2s infinite;
}

@keyframes pulse-counter {
  0%, 100% { transform: scale(1); }
  50% { transform: scale(1.05); }
}

.waiter-drinks-screen .search-container {
  position: relative;
  max-width: 500px;
  margin: 0 auto;
}

.waiter-drinks-screen .search-icon {
  position: absolute;
  right: var(--menu-spacing-md);
  top: 50%;
  transform: translateY(-50%);
  color: var(--menu-primary-color);
  z-index: 2;
  font-size: 1.1rem;
  transition: all 0.3s ease;
}

.waiter-drinks-screen .search-input {
  width: 100%;
  padding: var(--menu-spacing-md) var(--menu-spacing-xl) var(--menu-spacing-md) var(--menu-spacing-md);
  border: 2px solid var(--menu-border-color);
  border-radius: 25px;
  font-size: 1.1rem;
  transition: all 0.3s ease;
  background: var(--menu-bg-primary);
  text-align: right;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  font-weight: 500;
}

.waiter-drinks-screen .search-input:focus {
  outline: none;
  border-color: var(--menu-primary-color);
  box-shadow: 0 0 0 4px rgba(139, 69, 19, 0.15), 0 4px 12px rgba(0, 0, 0, 0.15);
  transform: translateY(-1px);
}

.waiter-drinks-screen .search-input:focus + .search-icon {
  color: var(--menu-primary-color);
  transform: translateY(-50%) scale(1.1);
}

.waiter-drinks-screen .clear-search {
  position: absolute;
  left: var(--menu-spacing-md);
  top: 50%;
  transform: translateY(-50%);
  background: var(--menu-danger-color);
  border: none;
  color: white;
  cursor: pointer;
  padding: var(--menu-spacing-xs);
  border-radius: 50%;
  transition: all 0.3s ease;
  width: 24px;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 0.8rem;
}

.waiter-drinks-screen .clear-search:hover {
  background: #dc3545;
  transform: translateY(-50%) scale(1.1);
  box-shadow: 0 2px 8px rgba(220, 53, 69, 0.3);
}

/* فلتر الفئات المحسن */
.waiter-drinks-screen .categories-section {
  margin-bottom: var(--menu-spacing-lg);
  padding: var(--menu-spacing-lg);
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.9), rgba(248, 249, 250, 0.9));
  border-radius: var(--menu-border-radius);
  border: 1px solid rgba(139, 69, 19, 0.1);
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);
}

.waiter-drinks-screen .categories-header {
  margin-bottom: var(--menu-spacing-md);
  padding-bottom: var(--menu-spacing-sm);
  border-bottom: 2px solid rgba(139, 69, 19, 0.1);
}

.waiter-drinks-screen .categories-title {
  font-size: 1.2rem;
  font-weight: 700;
  color: var(--menu-primary-color);
  margin: 0;
  display: flex;
  align-items: center;
  gap: var(--menu-spacing-xs);
}

.waiter-drinks-screen .categories-title i {
  font-size: 1.1rem;
  color: var(--menu-secondary-color);
  animation: rotate-tags 3s ease-in-out infinite;
}

@keyframes rotate-tags {
  0%, 100% { transform: rotate(0deg); }
  25% { transform: rotate(-5deg); }
  75% { transform: rotate(5deg); }
}

.waiter-drinks-screen .category-filter {
  display: flex;
  gap: var(--menu-spacing-sm);
  overflow-x: auto;
  padding: var(--menu-spacing-sm) 0;
  /* Scrollbar styling with fallbacks */
  -ms-overflow-style: -ms-autohiding-scrollbar; /* IE 10+ */
  /* Firefox-only features - safe progressive enhancement */
  scrollbar-width: thin; /* ⚠️ Warning expected: Firefox-only */
  scrollbar-color: var(--menu-primary-color) var(--menu-bg-tertiary); /* ⚠️ Warning expected: Firefox-only */
}

.waiter-drinks-screen .category-filter::-webkit-scrollbar {
  height: 8px;
}

.waiter-drinks-screen .category-filter::-webkit-scrollbar-track {
  background: rgba(139, 69, 19, 0.1);
  border-radius: 4px;
}

.waiter-drinks-screen .category-filter::-webkit-scrollbar-thumb {
  background: linear-gradient(135deg, var(--menu-primary-color), var(--menu-secondary-color));
  border-radius: 4px;
}

.waiter-drinks-screen .category-filter::-webkit-scrollbar-thumb:hover {
  background: linear-gradient(135deg, var(--menu-secondary-color), var(--menu-primary-color));
}

.waiter-drinks-screen .category-btn {
  background: linear-gradient(135deg, var(--menu-bg-primary), rgba(255, 255, 255, 0.9));
  border: 2px solid var(--menu-border-color);
  padding: var(--menu-spacing-sm) var(--menu-spacing-lg);
  border-radius: 25px;
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  white-space: nowrap;
  display: flex;
  align-items: center;
  gap: var(--menu-spacing-xs);
  font-size: 1rem;
  font-weight: 600;
  min-height: 45px;
  position: relative;
  overflow: hidden;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.waiter-drinks-screen .category-btn::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.4), transparent);
  transition: left 0.5s ease;
}

.waiter-drinks-screen .category-btn:hover::before {
  left: 100%;
}

.waiter-drinks-screen .category-btn:hover {
  border-color: var(--menu-primary-color);
  background: linear-gradient(135deg, var(--menu-bg-secondary), rgba(139, 69, 19, 0.05));
  transform: translateY(-2px) scale(1.02);
  box-shadow: 0 4px 16px rgba(139, 69, 19, 0.2);
}

.waiter-drinks-screen .category-btn.active {
  background: linear-gradient(135deg, var(--menu-primary-color), var(--menu-secondary-color));
  color: white;
  border-color: var(--menu-primary-color);
  box-shadow: 0 4px 20px rgba(139, 69, 19, 0.3);
  transform: translateY(-1px);
}

.waiter-drinks-screen .category-btn.active:hover {
  transform: translateY(-2px) scale(1.02);
  box-shadow: 0 6px 24px rgba(139, 69, 19, 0.4);
}

.waiter-drinks-screen .category-btn i {
  font-size: 1.1rem;
  transition: transform 0.3s ease;
}

.waiter-drinks-screen .category-btn:hover i {
  transform: scale(1.1);
}

.waiter-drinks-screen .category-btn.active i {
  text-shadow: 0 1px 3px rgba(0, 0, 0, 0.3);
}

/* تحسينات الاستجابة للأجهزة المختلفة */
@media (max-width: 768px) {
  .waiter-drinks-screen .drinks-filters {
    padding: var(--menu-spacing-sm);
    margin-bottom: var(--menu-spacing-lg);
  }
  
  .waiter-drinks-screen .filter-header {
    flex-direction: column;
    gap: var(--menu-spacing-sm);
    align-items: flex-start;
  }
  
  .waiter-drinks-screen .filter-title {
    font-size: 1.1rem;
  }
  
  .waiter-drinks-screen .results-counter {
    align-self: flex-end;
    font-size: 0.8rem;
  }
  
  .waiter-drinks-screen .search-container {
    max-width: 100%;
  }
  
  .waiter-drinks-screen .search-input {
    font-size: 1rem;
    padding: var(--menu-spacing-sm) var(--menu-spacing-lg) var(--menu-spacing-sm) var(--menu-spacing-sm);
  }
  
  .waiter-drinks-screen .categories-title {
    font-size: 1rem;
  }
  
  .waiter-drinks-screen .category-btn {
    font-size: 0.9rem;
    padding: var(--menu-spacing-xs) var(--menu-spacing-md);
    min-height: 40px;
  }
  
  .waiter-drinks-screen .category-filter {
    gap: var(--menu-spacing-xs);
  }
  
  /* تحسينات الكروت للأجهزة المحمولة */
  .waiter-drinks-screen .menu-grid {
    grid-template-columns: 1fr;
    gap: var(--menu-spacing-md);
    padding: var(--menu-spacing-sm);
  }
  
  .waiter-drinks-screen .menu-item-card {
    padding: var(--menu-spacing-md);
  }
  
  .waiter-drinks-screen .menu-item-name {
    font-size: 1rem;
  }
  
  .waiter-drinks-screen .menu-item-price {
    font-size: 0.8rem;
    padding: var(--menu-spacing-xs) var(--menu-spacing-sm);
  }
  
  .waiter-drinks-screen .menu-item-description {
    font-size: 0.8rem;
  }
  
  .waiter-drinks-screen .category-tag {
    font-size: 0.65rem;
    padding: 2px var(--menu-spacing-xs);
  }
  
  .waiter-drinks-screen .add-to-cart-btn {
    font-size: 0.8rem;
    padding: var(--menu-spacing-xs) var(--menu-spacing-sm);
  }
}

@media (max-width: 480px) {
  .waiter-drinks-screen .search-section,
  .waiter-drinks-screen .categories-section {
    padding: var(--menu-spacing-md);
  }
  
  .waiter-drinks-screen .filter-title {
    font-size: 1rem;
  }
  
  .waiter-drinks-screen .results-counter {
    font-size: 0.75rem;
    padding: 4px var(--menu-spacing-xs);
  }
  
  .waiter-drinks-screen .category-btn {
    font-size: 0.8rem;
    padding: 6px var(--menu-spacing-sm);
    min-height: 36px;
  }
  
  /* تحسينات إضافية للشاشات الصغيرة جداً */
  .waiter-drinks-screen .menu-item-card {
    padding: var(--menu-spacing-sm);
    border-radius: 12px;
  }
  
  .waiter-drinks-screen .menu-item-header {
    flex-direction: column;
    align-items: flex-start;
    gap: var(--menu-spacing-xs);
  }
  
  .waiter-drinks-screen .menu-item-name {
    font-size: 0.95rem;
  }
  
  .waiter-drinks-screen .menu-item-price {
    align-self: flex-end;
    font-size: 0.75rem;
  }
  
  .waiter-drinks-screen .item-actions {
    flex-direction: column;
    gap: var(--menu-spacing-xs);
  }
  
  .waiter-drinks-screen .add-with-notes-btn {
    min-width: 100%;
  }
}

/* تحسينات للشاشات الكبيرة */
@media (min-width: 1200px) {
  .waiter-drinks-screen .search-container {
    max-width: 600px;
  }
  
  .waiter-drinks-screen .category-filter {
    justify-content: center;
    flex-wrap: wrap;
  }
  
  .waiter-drinks-screen .category-btn {
    min-width: 150px;
    justify-content: center;
  }
  
  /* تحسينات الكروت للشاشات الكبيرة */
  .waiter-drinks-screen .menu-grid {
    grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
    gap: var(--menu-spacing-lg);
    padding: var(--menu-spacing-lg);
  }
  
  .waiter-drinks-screen .menu-item-card {
    padding: var(--menu-spacing-lg);
    min-height: 220px;
  }
  
  .waiter-drinks-screen .menu-item-name {
    font-size: 1.2rem;
  }
  
  .waiter-drinks-screen .menu-item-price {
    font-size: 0.9rem;
    padding: var(--menu-spacing-xs) var(--menu-spacing-md);
  }
  
  .waiter-drinks-screen .menu-item-description {
    font-size: 0.9rem;
    padding: var(--menu-spacing-sm);
  }
  
  .waiter-drinks-screen .category-tag {
    font-size: 0.75rem;
    padding: var(--menu-spacing-xs) var(--menu-spacing-sm);
  }
  
  .waiter-drinks-screen .add-to-cart-btn {
    font-size: 0.9rem;
    padding: var(--menu-spacing-sm) var(--menu-spacing-md);
  }
  
  .waiter-drinks-screen .add-with-notes-btn {
    min-width: 60px;
    padding: var(--menu-spacing-sm);
  }
}

/* تحسينات للشاشات الكبيرة جداً */
@media (min-width: 1600px) {
  .waiter-drinks-screen .menu-grid {
    grid-template-columns: repeat(auto-fill, minmax(320px, 1fr));
    max-width: 1200px;
    margin: 0 auto;
  }
  
  .waiter-drinks-screen .menu-item-card {
    min-height: 240px;
  }
}

.waiter-drinks-screen .category-btn[data-border-color] {
  border-color: attr(data-border-color);
}

/* شبكة المشروبات المحسنة */
.waiter-drinks-screen .menu-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: var(--menu-spacing-xl);
  margin-bottom: var(--menu-spacing-xl);
  padding: var(--menu-spacing-md);
}

.waiter-drinks-screen .menu-item-card {
  background: linear-gradient(145deg, #ffffff 0%, #f8f9fa 50%, #ffffff 100%);
  border: 2px solid transparent;
  border-radius: 20px;
  padding: var(--menu-spacing-lg);
  transition: all 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.12), 0 2px 8px rgba(0, 0, 0, 0.08);
  position: relative;
  overflow: hidden;
  cursor: pointer;
  transform-style: preserve-3d;
  display: flex;
  flex-direction: column;
  height: fit-content;
}

.waiter-drinks-screen .menu-item-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: linear-gradient(90deg, var(--menu-primary-color), var(--menu-secondary-color), var(--menu-success-color));
  background-size: 200% 100%;
  animation: gradient-flow 3s ease-in-out infinite;
}

@keyframes gradient-flow {
  0%, 100% { background-position: 0% 50%; }
  50% { background-position: 100% 50%; }
}

.waiter-drinks-screen .menu-item-card::after {
  content: '';
  position: absolute;
  top: -50%;
  left: -50%;
  width: 200%;
  height: 200%;
  background: linear-gradient(45deg, transparent 30%, rgba(255, 255, 255, 0.1) 50%, transparent 70%);
  transform: rotate(45deg);
  transition: all 0.6s ease;
  opacity: 0;
}

.waiter-drinks-screen .menu-item-card:hover {
  transform: translateY(-8px) rotateX(2deg);
  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.15), 0 8px 24px rgba(139, 69, 19, 0.1);
  border-color: var(--menu-primary-color);
}

.waiter-drinks-screen .menu-item-card:hover::after {
  opacity: 1;
  animation: shine 0.6s ease-out;
}

@keyframes shine {
  0% { transform: translateX(-100%) translateY(-100%) rotate(45deg); }
  100% { transform: translateX(100%) translateY(100%) rotate(45deg); }
}

.waiter-drinks-screen .menu-item-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: var(--menu-spacing-md);
  gap: var(--menu-spacing-sm);
  position: relative;
}

.waiter-drinks-screen .menu-item-name {
  font-size: 1.1rem;
  font-weight: 700;
  color: var(--menu-text-primary);
  margin: 0;
  flex: 1;
  line-height: 1.2;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
  transition: all 0.3s ease;
  position: relative;
}

.waiter-drinks-screen .menu-item-card:hover .menu-item-name {
  color: var(--menu-primary-color);
  transform: translateX(2px);
}

.waiter-drinks-screen .menu-item-price {
  background: linear-gradient(135deg, var(--menu-primary-color) 0%, var(--menu-secondary-color) 50%, var(--menu-success-color) 100%);
  color: white;
  padding: var(--menu-spacing-xs) var(--menu-spacing-md);
  border-radius: 20px;
  font-weight: 700;
  font-size: 0.85rem;
  white-space: nowrap;
  box-shadow: 0 4px 12px rgba(139, 69, 19, 0.3);
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.waiter-drinks-screen .menu-item-price::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
  transition: left 0.5s ease;
}

.waiter-drinks-screen .menu-item-card:hover .menu-item-price {
  transform: scale(1.05);
  box-shadow: 0 6px 20px rgba(139, 69, 19, 0.4);
}

.waiter-drinks-screen .menu-item-card:hover .menu-item-price::before {
  left: 100%;
}

.waiter-drinks-screen .menu-item-description {
  color: var(--menu-text-secondary);
  font-size: 0.85rem;
  line-height: 1.4;
  margin-bottom: var(--menu-spacing-md);
  padding: var(--menu-spacing-xs);
  background: rgba(248, 249, 250, 0.5);
  border-radius: 8px;
  border-left: 2px solid var(--menu-secondary-color);
  transition: all 0.3s ease;
  position: relative;
}

.waiter-drinks-screen .menu-item-card:hover .menu-item-description {
  background: rgba(139, 69, 19, 0.05);
  border-left-color: var(--menu-primary-color);
  transform: translateX(2px);
}

.waiter-drinks-screen .menu-item-categories {
  display: flex;
  flex-wrap: wrap;
  gap: var(--menu-spacing-xs);
  margin-bottom: var(--menu-spacing-md);
  padding: var(--menu-spacing-xs) 0;
}

.waiter-drinks-screen .category-tag {
  padding: var(--menu-spacing-xs) var(--menu-spacing-sm);
  border-radius: 15px;
  font-size: 0.7rem;
  font-weight: 600;
  background: linear-gradient(135deg, var(--menu-bg-secondary), rgba(255, 255, 255, 0.9));
  color: var(--menu-text-primary);
  border: 1px solid var(--menu-border-color);
  display: flex;
  align-items: center;
  gap: 2px;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  overflow: hidden;
  box-shadow: 0 1px 4px rgba(0, 0, 0, 0.06);
}

.waiter-drinks-screen .category-tag::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(139, 69, 19, 0.1), transparent);
  transition: left 0.4s ease;
}

.waiter-drinks-screen .category-tag:hover {
  transform: translateY(-2px) scale(1.05);
  border-color: var(--menu-primary-color);
  box-shadow: 0 4px 16px rgba(139, 69, 19, 0.2);
  color: var(--menu-primary-color);
}

.waiter-drinks-screen .category-tag:hover::before {
  left: 100%;
}

.waiter-drinks-screen .category-tag[data-bg-color] {
  background-color: attr(data-bg-color);
  opacity: 0.9;
}

/* أزرار العمليات المحسنة */
.waiter-drinks-screen .item-actions {
  display: flex;
  gap: var(--menu-spacing-sm);
  margin-top: auto;
  padding-top: var(--menu-spacing-sm);
  border-top: 1px solid rgba(139, 69, 19, 0.1);
}

.waiter-drinks-screen .add-to-cart-btn {
  flex: 1;
  background: linear-gradient(135deg, var(--menu-success-color) 0%, #27ae60 50%, #2ecc71 100%);
  color: white;
  border: none;
  padding: var(--menu-spacing-sm) var(--menu-spacing-md);
  border-radius: 20px;
  cursor: pointer;
  transition: all 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
  font-weight: 700;
  font-size: 0.85rem;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: var(--menu-spacing-xs);
  position: relative;
  overflow: hidden;
  box-shadow: 0 3px 12px rgba(39, 174, 96, 0.3);
  text-transform: uppercase;
  letter-spacing: 0.3px;
}

.waiter-drinks-screen .add-to-cart-btn::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left 0.5s ease;
}

.waiter-drinks-screen .add-to-cart-btn:hover {
  background: linear-gradient(135deg, #27ae60 0%, #2ecc71 50%, #58d68d 100%);
  transform: translateY(-3px) scale(1.02);
  box-shadow: 0 8px 25px rgba(39, 174, 96, 0.4);
}

.waiter-drinks-screen .add-to-cart-btn:hover::before {
  left: 100%;
}

.waiter-drinks-screen .add-to-cart-btn:active {
  transform: translateY(-1px) scale(0.98);
}

.waiter-drinks-screen .add-with-notes-btn {
  background: linear-gradient(135deg, var(--menu-info-color) 0%, #3498db 50%, #5dade2 100%);
  color: white;
  border: none;
  padding: var(--menu-spacing-sm);
  border-radius: 20px;
  cursor: pointer;
  transition: all 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
  display: flex;
  align-items: center;
  justify-content: center;
  min-width: 40px;
  position: relative;
  overflow: hidden;
  box-shadow: 0 3px 12px rgba(52, 152, 219, 0.3);
}

.waiter-drinks-screen .add-with-notes-btn::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left 0.5s ease;
}

.waiter-drinks-screen .add-with-notes-btn:hover {
  background: linear-gradient(135deg, #3498db 0%, #5dade2 50%, #85c1e9 100%);
  transform: translateY(-3px) scale(1.05);
  box-shadow: 0 8px 25px rgba(52, 152, 219, 0.4);
}

.waiter-drinks-screen .add-with-notes-btn:hover::before {
  left: 100%;
}

.waiter-drinks-screen .add-with-notes-btn:active {
  transform: translateY(-1px) scale(0.95);
}

.waiter-drinks-screen .add-with-notes-btn i {
  font-size: 1.1rem;
  transition: transform 0.3s ease;
}

.waiter-drinks-screen .add-with-notes-btn:hover i {
  transform: rotate(15deg) scale(1.1);
}

/* حالات التحميل والفراغ */
.waiter-drinks-screen .loading-state,
.waiter-drinks-screen .empty-state {
  text-align: center;
  padding: var(--menu-spacing-xl);
  grid-column: 1 / -1;
}

.waiter-drinks-screen .loading-spinner {
  width: 40px;
  height: 40px;
  border: 4px solid var(--menu-bg-tertiary);
  border-top: 4px solid var(--menu-primary-color);
  border-radius: 50%;
  animation: waiter-drinks-spin 1s linear infinite;
  margin: 0 auto var(--menu-spacing-md);
}

@keyframes waiter-drinks-spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.waiter-drinks-screen .empty-icon {
  font-size: 3rem;
  color: var(--menu-text-muted);
  margin-bottom: var(--menu-spacing-md);
}

.waiter-drinks-screen .empty-state h3 {
  color: var(--menu-text-primary);
  margin-bottom: var(--menu-spacing-sm);
}

.waiter-drinks-screen .empty-state p {
  color: var(--menu-text-secondary);
  margin-bottom: var(--menu-spacing-lg);
}

.waiter-drinks-screen .retry-btn {
  background: var(--menu-primary-color);
  color: white;
  border: none;
  padding: var(--menu-spacing-sm) var(--menu-spacing-lg);
  border-radius: var(--menu-border-radius);
  cursor: pointer;
  transition: all 0.3s ease;
  display: inline-flex;
  align-items: center;
  gap: var(--menu-spacing-xs);
}

.waiter-drinks-screen .retry-btn:hover {
  background: var(--menu-secondary-color);
  transform: translateY(-1px);
}

/* Responsive Design */
@media (max-width: 768px) {
  .waiter-drinks-screen .waiter-header-flex {
    flex-direction: column;
    align-items: stretch;
  }
  
  .waiter-drinks-screen .flex-gap-8 {
    justify-content: center;
    flex-wrap: wrap;
  }
  
  .waiter-drinks-screen .menu-grid {
    grid-template-columns: 1fr;
    gap: var(--menu-spacing-md);
  }
  
  .waiter-drinks-screen .category-filter {
    justify-content: flex-start;
  }
  
  .waiter-drinks-screen .search-input-group {
    max-width: 100%;
  }
}

@media (max-width: 480px) {
  .waiter-drinks-screen .screen-header {
    padding: var(--menu-spacing-md);
  }
  
  .waiter-drinks-screen .screen-title {
    font-size: 1.5rem;
  }
  
  .waiter-drinks-screen .menu-item-card {
    padding: var(--menu-spacing-md);
  }
  
  .waiter-drinks-screen .menu-item-header {
    flex-direction: column;
    align-items: stretch;
  }
  
  .waiter-drinks-screen .menu-item-price {
    align-self: flex-start;
  }
}


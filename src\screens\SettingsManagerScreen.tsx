import React from 'react';
// استدعاء ملف CSS الجديد المنظم
import '../styles/manager/SettingsManagerScreen.css';

interface SettingsManagerScreenProps {
  resetOrders: () => void;
  resetTables: () => void;
  resetDiscountRequests: () => void;
  resetAll: () => void;
}

const SettingsManagerScreen: React.FC<SettingsManagerScreenProps> = ({
  resetOrders,
  resetTables,
  resetDiscountRequests,
  resetAll
}) => {
  return (
    <div className="settings-screen">
      <div className="settings-header">
        <h1>
          <i className="fas fa-cogs"></i>
          إعدادات النظام
        </h1>
        <p>إدارة الإعدادات العامة للنظام وأدوات التهيئة</p>
      </div>

      {/* قسم إعادة التهيئة */}
      <div className="settings-section">
        <div className="section-header">
          <h2>
            <i className="fas fa-refresh"></i>
            إعادة تهيئة النظام
          </h2>
          <p className="section-description">
            استخدم هذه الأدوات بحذر - إعادة التهيئة ستحذف البيانات نهائياً
          </p>
        </div>

        <div className="reset-controls">
          <div className="reset-card">
            <div className="reset-info">
              <h3>
                <i className="fas fa-shopping-cart"></i>
                إعادة تهيئة الطلبات
              </h3>
              <p>حذف جميع الطلبات من النظام نهائياً</p>
              <div className="warning">
                <i className="fas fa-exclamation-triangle"></i>
                تحذير: لا يمكن التراجع عن هذا الإجراء
              </div>
            </div>
            <button 
              className="reset-btn reset-orders-btn"
              onClick={resetOrders}
              title="إعادة تهيئة جميع الطلبات"
            >
              <i className="fas fa-redo-alt"></i>
              إعادة تهيئة الطلبات
            </button>
          </div>

          <div className="reset-card">
            <div className="reset-info">
              <h3>
                <i className="fas fa-table"></i>
                إعادة تهيئة الطاولات
              </h3>
              <p>إغلاق جميع الطاولات وحذف حساباتها</p>
              <div className="warning">
                <i className="fas fa-exclamation-triangle"></i>
                سيتم إغلاق جميع الطاولات المفتوحة
              </div>
            </div>
            <button 
              className="reset-btn reset-tables-btn"
              onClick={resetTables}
              title="إعادة تهيئة جميع الطاولات"
            >
              <i className="fas fa-table"></i>
              إعادة تهيئة الطاولات
            </button>
          </div>

          <div className="reset-card">
            <div className="reset-info">
              <h3>
                <i className="fas fa-percentage"></i>
                إعادة تهيئة طلبات الخصم
              </h3>
              <p>حذف جميع طلبات الخصم من النظام</p>
              <div className="warning">
                <i className="fas fa-exclamation-triangle"></i>
                سيتم حذف جميع طلبات الخصم المعلقة والمكتملة
              </div>
            </div>
            <button 
              className="reset-btn reset-discounts-btn"
              onClick={resetDiscountRequests}
              title="إعادة تهيئة جميع طلبات الخصم"
            >
              <i className="fas fa-percentage"></i>
              إعادة تهيئة طلبات الخصم
            </button>
          </div>

          <div className="reset-card danger">
            <div className="reset-info">
              <h3>
                <i className="fas fa-bomb"></i>
                إعادة تهيئة النظام بالكامل
              </h3>
              <p>حذف جميع البيانات: الطلبات، الطاولات، وطلبات الخصم</p>
              <div className="danger-warning">
                <i className="fas fa-skull-crossbones"></i>
                خطر شديد: سيتم فقدان جميع البيانات نهائياً
              </div>
            </div>
            <button 
              className="reset-btn reset-all-btn"
              onClick={resetAll}
              title="إعادة تهيئة النظام بالكامل"
            >
              <i className="fas fa-bomb"></i>
              إعادة تهيئة النظام بالكامل
            </button>
          </div>
        </div>
      </div>

      {/* قسم الإعدادات العامة - للمستقبل */}
      <div className="settings-section">
        <div className="section-header">
          <h2>
            <i className="fas fa-sliders-h"></i>
            الإعدادات العامة
          </h2>
          <p className="section-description">
            إعدادات التطبيق العامة (قيد التطوير)
          </p>
        </div>

        <div className="settings-grid">
          <div className="setting-card coming-soon">
            <div className="setting-info">
              <h3>
                <i className="fas fa-language"></i>
                إعدادات اللغة
              </h3>
              <p>اختيار لغة واجهة التطبيق</p>
            </div>
            <div className="coming-soon-badge">
              <i className="fas fa-clock"></i>
              قريباً
            </div>
          </div>

          <div className="setting-card coming-soon">
            <div className="setting-info">
              <h3>
                <i className="fas fa-palette"></i>
                إعدادات المظهر
              </h3>
              <p>تخصيص ألوان وتصميم التطبيق</p>
            </div>
            <div className="coming-soon-badge">
              <i className="fas fa-clock"></i>
              قريباً
            </div>
          </div>

          <div className="setting-card coming-soon">
            <div className="setting-info">
              <h3>
                <i className="fas fa-bell"></i>
                إعدادات الإشعارات
              </h3>
              <p>تخصيص طريقة وتوقيت الإشعارات</p>
            </div>
            <div className="coming-soon-badge">
              <i className="fas fa-clock"></i>
              قريباً
            </div>
          </div>

          <div className="setting-card coming-soon">
            <div className="setting-info">
              <h3>
                <i className="fas fa-database"></i>
                إعدادات النسخ الاحتياطي
              </h3>
              <p>جدولة النسخ الاحتياطي التلقائي</p>
            </div>
            <div className="coming-soon-badge">
              <i className="fas fa-clock"></i>
              قريباً
            </div>
          </div>

          <div className="setting-card coming-soon">
            <div className="setting-info">
              <h3>
                <i className="fas fa-shield-alt"></i>
                إعدادات الأمان
              </h3>
              <p>إدارة كلمات المرور والصلاحيات</p>
            </div>
            <div className="coming-soon-badge">
              <i className="fas fa-clock"></i>
              قريباً
            </div>
          </div>

          <div className="setting-card coming-soon">
            <div className="setting-info">
              <h3>
                <i className="fas fa-print"></i>
                إعدادات الطباعة
              </h3>
              <p>تخصيص إعدادات طباعة الفواتير</p>
            </div>
            <div className="coming-soon-badge">
              <i className="fas fa-clock"></i>
              قريباً
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default SettingsManagerScreen;

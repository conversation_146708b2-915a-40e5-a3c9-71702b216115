# Server Configuration
PORT=4003
NODE_ENV=production

# Database Configuration (MongoDB Atlas)
# Replace with your actual MongoDB Atlas connection string
MONGODB_URI=mongodb+srv://deshacoffee:<EMAIL>/deshacoffee?retryWrites=true&w=majority

# Alternative local MongoDB (for development)
# MONGODB_URI=mongodb://localhost:27017/deshacoffee

# JWT Configuration
JWT_SECRET=your-super-secret-jwt-key-here
JWT_EXPIRES_IN=7d

# CORS Configuration
FRONTEND_URL=https://desha-coffee.vercel.app

# Rate Limiting
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=100

# Security
BCRYPT_SALT_ROUNDS=12

# Email Configuration (Optional)
EMAIL_HOST=smtp.gmail.com
EMAIL_PORT=587
EMAIL_USER=<EMAIL>
EMAIL_PASS=your-app-password

# File Upload (Optional)
MAX_FILE_SIZE=10485760
UPLOAD_PATH=uploads/

/* تنسيق مكون إصلاح التباين في المبيعات */
.sales-discrepancy-fixer {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.7);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 10000;
}

.sales-discrepancy-fixer .modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px;
  background: #2c3e50;
  color: white;
  border-radius: 10px 10px 0 0;
}

.sales-discrepancy-fixer .modal-header h2 {
  margin: 0;
  font-size: 24px;
}

.sales-discrepancy-fixer .close-btn {
  background: none;
  border: none;
  color: white;
  font-size: 24px;
  cursor: pointer;
  padding: 5px 10px;
  border-radius: 5px;
  transition: background 0.3s;
}

.sales-discrepancy-fixer .close-btn:hover {
  background: rgba(255, 255, 255, 0.2);
}

.sales-discrepancy-fixer .modal-content {
  background: white;
  max-width: 800px;
  width: 90%;
  max-height: 80vh;
  overflow-y: auto;
  border-radius: 0 0 10px 10px;
  padding: 20px;
}

.sales-discrepancy-fixer .loading,
.sales-discrepancy-fixer .error {
  text-align: center;
  padding: 40px;
  font-size: 18px;
}

.sales-discrepancy-fixer .loading {
  color: #3498db;
}

.sales-discrepancy-fixer .error {
  color: #e74c3c;
}

.sales-discrepancy-fixer .summary-cards {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 15px;
  margin-bottom: 30px;
}

.sales-discrepancy-fixer .summary-card {
  background: #f8f9fa;
  padding: 20px;
  border-radius: 10px;
  text-align: center;
  border: 2px solid #dee2e6;
  transition: transform 0.2s;
}

.sales-discrepancy-fixer .summary-card:hover {
  transform: translateY(-2px);
}

.sales-discrepancy-fixer .summary-card.success {
  border-color: #28a745;
  background: #d4edda;
}

.sales-discrepancy-fixer .summary-card.error {
  border-color: #dc3545;
  background: #f8d7da;
}

.sales-discrepancy-fixer .summary-card h3 {
  margin: 0 0 10px 0;
  font-size: 16px;
  color: #6c757d;
}

.sales-discrepancy-fixer .summary-card .amount {
  font-size: 24px;
  font-weight: bold;
  color: #2c3e50;
}

.sales-discrepancy-fixer .summary-card.success .amount {
  color: #28a745;
}

.sales-discrepancy-fixer .summary-card.error .amount {
  color: #dc3545;
}

.sales-discrepancy-fixer .issues-section {
  background: #fff3cd;
  border: 1px solid #ffeaa7;
  border-radius: 10px;
  padding: 20px;
  margin-bottom: 30px;
}

.sales-discrepancy-fixer .issues-section h3 {
  margin: 0 0 15px 0;
  color: #856404;
}

.sales-discrepancy-fixer .issue-item {
  display: flex;
  align-items: center;
  gap: 10px;
  margin-bottom: 10px;
  font-size: 16px;
}

.sales-discrepancy-fixer .issue-icon {
  font-size: 20px;
}

.sales-discrepancy-fixer .waiters-breakdown {
  background: #e3f2fd;
  border-radius: 10px;
  padding: 20px;
  margin-bottom: 30px;
}

.sales-discrepancy-fixer .waiters-breakdown h3 {
  margin: 0 0 15px 0;
  color: #1565c0;
}

.sales-discrepancy-fixer .waiters-list {
  display: grid;
  gap: 10px;
}

.sales-discrepancy-fixer .waiter-item {
  display: grid;
  grid-template-columns: 1fr auto auto;
  gap: 15px;
  align-items: center;
  background: white;
  padding: 15px;
  border-radius: 8px;
  border: 1px solid #bbdefb;
}

.sales-discrepancy-fixer .waiter-name {
  font-weight: bold;
  color: #2c3e50;
}

.sales-discrepancy-fixer .waiter-sales {
  color: #27ae60;
  font-weight: bold;
}

.sales-discrepancy-fixer .waiter-orders {
  color: #7f8c8d;
  font-size: 14px;
}

.sales-discrepancy-fixer .fix-section {
  text-align: center;
  margin-bottom: 30px;
}

.sales-discrepancy-fixer .fix-btn {
  background: #e74c3c;
  color: white;
  border: none;
  padding: 15px 30px;
  border-radius: 8px;
  font-size: 18px;
  cursor: pointer;
  transition: background 0.3s;
}

.sales-discrepancy-fixer .fix-btn:hover:not(:disabled) {
  background: #c0392b;
}

.sales-discrepancy-fixer .fix-btn:disabled {
  background: #95a5a6;
  cursor: not-allowed;
}

.sales-discrepancy-fixer .fix-result {
  background: #d4edda;
  border: 1px solid #c3e6cb;
  border-radius: 10px;
  padding: 20px;
}

.sales-discrepancy-fixer .fix-result h3 {
  margin: 0 0 15px 0;
  color: #155724;
}

.sales-discrepancy-fixer .result-item {
  margin-bottom: 10px;
  padding: 8px 12px;
  background: white;
  border-radius: 5px;
  border: 1px solid #c3e6cb;
}

.sales-discrepancy-fixer .result-status {
  margin-top: 15px;
  padding: 12px;
  border-radius: 8px;
  font-weight: bold;
  text-align: center;
}

.sales-discrepancy-fixer .result-status.success {
  background: #d4edda;
  color: #155724;
  border: 1px solid #c3e6cb;
}

.sales-discrepancy-fixer .result-status.warning {
  background: #fff3cd;
  color: #856404;
  border: 1px solid #ffeaa7;
}

/* استجابة للشاشات الصغيرة */
@media (max-width: 768px) {
  .sales-discrepancy-fixer .summary-cards {
    grid-template-columns: 1fr;
  }
  
  .sales-discrepancy-fixer .waiter-item {
    grid-template-columns: 1fr;
    text-align: center;
  }
  
  .sales-discrepancy-fixer .modal-content {
    width: 95%;
    max-height: 90vh;
    padding: 15px;
  }
  
  .sales-discrepancy-fixer .modal-header {
    padding: 15px;
  }
  
  .sales-discrepancy-fixer .modal-header h2 {
    font-size: 20px;
  }
}

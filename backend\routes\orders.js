const express = require('express');
const Order = require('../models/Order');
const Product = require('../models/Product');
const User = require('../models/User');
const Table = require('../models/Table');
const { parseTableNumber, validateTableNumber } = require('../utils/numberUtils');
const { authenticateToken, requireManager } = require('../middleware/auth');
const { 
  applyUnifiedMiddleware, 
  asyncHandler, 
  formatResponse, 
  formatError 
} = require('../middleware/unifiedRouteHandler');
const { 
  sendSuccess, 
  sendError, 
  sendNotFound, 
  handleDatabaseError 
} = require('../middleware/unifiedResponse');
const responseMapping = require('../middleware/responseMapping');

const router = express.Router();

// Apply unified middleware
applyUnifiedMiddleware(router);

// Get all orders
router.get('/', asyncHandler(async (req, res) => {
  const { status, orderType, date, waiterName, chefName, page = 1, limit = 999999 } = req.query;
  let query = {};

  // Build query filters
  if (status) {
    query.status = status;
  }

  if (orderType) {
    query.orderType = orderType;
  }

  if (waiterName) {
    query['staff.waiter'] = waiterName;
  }

  if (chefName) {
    query['staff.chef'] = chefName;
  }

  if (date) {
    const targetDate = new Date(date);
    const startOfDay = new Date(targetDate.getFullYear(), targetDate.getMonth(), targetDate.getDate());
    const endOfDay = new Date(targetDate.getFullYear(), targetDate.getMonth(), targetDate.getDate() + 1);

    query['timing.orderTime'] = {
      $gte: startOfDay,
      $lt: endOfDay
    };
  }

  // Pagination
  const pageNum = parseInt(page);
  const limitNum = parseInt(limit);
  const skip = (pageNum - 1) * limitNum;

  // Get orders from database
  const orders = await Order.find(query)
    .populate('staff.waiter', 'name username')
    .populate('staff.chef', 'name username')
    .populate('items.product', 'name price category')
    .populate('table', 'number name')
    .sort({ 'timing.orderTime': -1 })
    .skip(skip)
    .limit(limitNum);

  // Get total count for pagination
  const totalOrders = await Order.countDocuments(query);

  // Transform data for frontend compatibility
  const mappedOrders = responseMapping.mapOrdersArrayForFrontend(orders);

  const meta = {
    pagination: {
      currentPage: pageNum,
      totalPages: Math.ceil(totalOrders / limitNum),
      totalItems: totalOrders,
      itemsPerPage: limitNum,
      hasNextPage: pageNum < Math.ceil(totalOrders / limitNum),
      hasPrevPage: pageNum > 1
    },
    filters: {
      status,
      orderType,
      date,
      waiterName,
      chefName
    }
  };

  console.log('📋 Orders Query Result:', {
    query,
    count: orders.length,
    totalOrders,
    deviceType: req.deviceInfo?.type,
    pagination: meta.pagination
  });

  return sendSuccess(res, mappedOrders, 'تم تحميل الطلبات بنجاح', 200, meta);
}));

// Get order by ID
router.get('/:id', asyncHandler(async (req, res) => {
  const order = await Order.findById(req.params.id)
    .populate('staff.waiter', 'name username role')
    .populate('staff.chef', 'name username role')
    .populate('items.product', 'name price category image')
    .populate('table', 'number name location')
    .populate('customer', 'name phone');

  if (!order) {
    return sendNotFound(res, 'الطلب');
  }

  // Transform data for frontend compatibility
  const mappedOrder = responseMapping.mapOrderForFrontend(order);

  console.log('📄 Order Details Retrieved:', {
    orderId: order._id,
    status: order.status,
    total: order.pricing?.total,
    deviceType: req.deviceInfo?.type
  });

  return sendSuccess(res, mappedOrder, 'تم تحميل تفاصيل الطلب بنجاح');
}));

// Create new order
router.post('/', authenticateToken, asyncHandler(async (req, res) => {
  console.log('📱 Order Creation Request:', {
    deviceType: req.deviceInfo?.type,
    userAgent: req.headers['user-agent'],
    bodyKeys: Object.keys(req.body || {}),
    user: req.user?.name || 'Unknown'
  });  const { 
    items, 
    tableId, 
    tableNumber, // Support both tableId and tableNumber
    orderType = 'dine-in', 
    customerInfo,
    customer, // Support customer object from frontend
    customerName, // Support customerName from frontend
    specialInstructions,
    priority = 'normal'
  } = req.body;

  // Unified customer data handling - support multiple formats
  const unifiedCustomer = customerInfo || customer || (customerName ? {
    name: customerName,
    phone: '',
    email: ''
  } : null);

  console.log('📋 Customer data processing:', {
    hasCustomerInfo: !!customerInfo,
    hasCustomer: !!customer,
    hasCustomerName: !!customerName,
    finalCustomer: unifiedCustomer
  });

  // Validate required fields
  if (!items || !Array.isArray(items) || items.length === 0) {
    return sendError(res, 'يجب إضافة عناصر للطلب', 400, 'MISSING_ITEMS');
  }

  // Validate table for dine-in orders
  let table = null;
  if (orderType === 'dine-in') {
    // Support both tableId and tableNumber for backward compatibility
    if (!tableId && !tableNumber) {
      return sendError(res, 'رقم الطاولة مطلوب للطلبات الداخلية', 400, 'MISSING_TABLE');
    }
      // Find table by ID or number
    if (tableId) {
      table = await Table.findOne({ _id: tableId });
    } else if (tableNumber) {
      // التحقق من صحة رقم الطاولة وتحويل الأرقام العربية
      const parsedNumber = parseTableNumber(tableNumber);
      if (!parsedNumber) {
        return sendError(res, 'رقم الطاولة غير صحيح', 400, 'INVALID_TABLE_NUMBER');
      }
      table = await Table.findOne({ number: parsedNumber });
    }
      if (!table) {
      return sendNotFound(res, 'الطاولة');
    }
      // Check if table is occupied by another waiter
    if (table.status === 'occupied') {
      console.log(`🔍 فحص الطاولة ${table.number}:`, {
        tableStatus: table.status,
        assignedWaiter: table.assignedWaiter,
        assignedWaiterStr: table.assignedWaiter?.toString(),
        currentUser: req.user._id,
        currentUserStr: req.user._id?.toString(),
        isSameWaiter: table.assignedWaiter?.toString() === req.user._id?.toString()
      });

      // Allow if the same waiter is using the table OR if no waiter is assigned
      if (table.assignedWaiter && table.assignedWaiter.toString() !== req.user._id.toString()) {
        const assignedWaiterInfo = await User.findById(table.assignedWaiter).select('name username');
        console.log(`❌ الطاولة ${table.number} محجوزة من قبل نادل آخر:`, assignedWaiterInfo);
        
        const waiterName = assignedWaiterInfo?.name || assignedWaiterInfo?.username || 'غير محدد';
        const currentWaiterName = req.user?.name || req.user?.username || 'غير محدد';
        
        // البحث عن الطاولات المتاحة لاقتراحها
        const availableTables = await Table.find({ 
          assignedWaiter: null, 
          status: 'available',
          isActive: true 
        }).select('number').limit(8);
        
        const availableTableNumbers = availableTables.map(t => t.number).sort((a, b) => a - b);
        const suggestedTables = availableTableNumbers.length > 0 
          ? `\n💡 الطاولات المتاحة: ${availableTableNumbers.join(', ')}`
          : '\n⚠️ لا توجد طاولات متاحة حالياً';
        
        return sendError(res, 
          `🚫 الطاولة رقم ${table.number} مفتوحة حالياً من قبل النادل "${waiterName}"\n\n` +
          `👤 النادل الحالي: ${currentWaiterName}\n` +
          `🔒 الطاولة محجوزة لـ: ${waiterName}\n` +
          `${suggestedTables}\n\n` +
          `💡 يرجى اختيار طاولة متاحة أو التواصل مع المدير لتحرير الطاولة.`, 
          409, 
          'TABLE_OCCUPIED_BY_OTHER_WAITER',
          {
            tableNumber: table.number,
            occupiedByWaiter: waiterName,
            currentWaiter: currentWaiterName,
            availableTables: availableTableNumbers,
            suggestedAction: 'choose_available_table'
          }
        );
      }
      // If same waiter or no assigned waiter, continue with the order
      console.log(`✅ نفس النادل يستخدم الطاولة ${table.number} أو لا يوجد نادل معين`);
    }
  }

  // Validate and process items
  const processedItems = [];
  let totalAmount = 0;
  for (const item of items) {
    const { productId, product, quantity, specialRequests, modifications } = item;
    
    // Support both productId and product for backward compatibility
    const actualProductId = productId || product;
    
    if (!actualProductId || !quantity || quantity <= 0) {
      return sendError(res, 'معرف المنتج والكمية مطلوبان', 400, 'INVALID_ITEM');
    }

    const productDoc = await Product.findById(actualProductId);
    if (!productDoc) {
      return sendError(res, `المنتج غير موجود: ${actualProductId}`, 404, 'PRODUCT_NOT_FOUND');
    }

    if (!productDoc.available) {
      return sendError(res, `المنتج غير متاح: ${productDoc.name}`, 400, 'PRODUCT_UNAVAILABLE');
    }    const itemTotal = productDoc.price * quantity;
    totalAmount += itemTotal;

    console.log('Processing item:', {
      productId: actualProductId,
      productName: productDoc.name,
      quantity,
      price: productDoc.price,
      itemTotal,
      typeOfItemTotal: typeof itemTotal
    });

    processedItems.push({
      product: productDoc._id,
      productName: productDoc.name,
      quantity,
      price: productDoc.price,
      subtotal: itemTotal,
      notes: specialRequests || '',
      specialRequests: specialRequests || '',
      modifications: modifications || []
    });
  }
  // Calculate pricing - إزالة الضريبة لتجنب اختلاف الأسعار
  const subtotal = totalAmount;
  const tax = 0; // إزالة الضريبة مؤقتاً لحل مشكلة اختلاف الأسعار
  const total = subtotal; // المجموع = المبلغ الأساسي بدون ضريبة

  console.log('Final pricing calculation:', {
    subtotal: subtotal,
    tax: tax,
    total: total,
    itemsBreakdown: processedItems.map(item => ({
      name: item.productName,
      quantity: item.quantity,
      price: item.price,
      subtotal: item.subtotal
    }))
  });console.log('Final Order Data before save:', {
    processedItems: JSON.stringify(processedItems, null, 2),
    customer: customerInfo,
    totals: { subtotal, tax, total }
  });

  // Create order
  const newOrder = new Order({
    orderNumber: `ORD-${Date.now()}`,
    items: processedItems,
    table: table?._id,
    orderType,
    status: 'pending',
    priority,
    // إضافة معلومات النادل للبحث السهل
    waiterName: req.user.name || req.user.username,
    waiterId: req.user._id,    customer: unifiedCustomer ? {
      name: unifiedCustomer.name,
      phone: unifiedCustomer.phone,
      email: unifiedCustomer.email
    } : null,
    staff: {
      waiter: req.user.id,
      chef: null
    },
    timing: {
      orderTime: new Date(),
      estimatedPrepTime: processedItems.length * 5 // 5 minutes per item
    },
    pricing: {
      subtotal,
      tax,
      total,
      currency: 'EGP'
    },
    totals: {
      subtotal,
      tax,
      discount: 0,
      total
    },
    specialInstructions: specialInstructions || '',
    deviceInfo: {
      type: req.deviceInfo?.type,
      userAgent: req.headers['user-agent']
    }
  });  let savedOrder;
  try {
    savedOrder = await newOrder.save();

    console.log('✅ Order saved successfully:', {
      orderId: savedOrder._id,
      orderNumber: savedOrder.orderNumber
    });
  } catch (saveError) {
    console.error('❌ Order save failed:', {
      error: saveError.message,
      details: saveError.errors ? Object.keys(saveError.errors).map(key => ({
        field: key,
        message: saveError.errors[key].message
      })) : null,      orderData: {
        itemsCount: processedItems.length,
        hasCustomer: !!unifiedCustomer,
        customerName: unifiedCustomer?.name,
        orderType,
        tableNumber: tableNumber || 'N/A'
      }
    });
    
    // Return detailed error
    if (saveError.name === 'ValidationError') {
      const validationErrors = Object.keys(saveError.errors).map(key => ({
        field: key,
        message: saveError.errors[key].message,
        value: saveError.errors[key].value
      }));
      
      return res.status(400).json({
        success: false,
        message: 'خطأ في بيانات الطلب - تفاصيل الأخطاء',
        error: 'VALIDATION_ERROR',
        details: validationErrors,
        timestamp: new Date().toISOString()
      });
    }
    
    throw saveError; // Re-throw if not validation error
  }  // Update table status if dine-in
  if (table && orderType === 'dine-in') {
    // Always update table info, even if already occupied by same waiter
    table.status = 'occupied';
    table.currentOrder = savedOrder._id;
    table.assignedWaiter = req.user._id; // تعيين النادل للطاولة دائماً
    table.occupiedAt = new Date();
    
    try {
      await table.save();
      console.log(`✅ تم تحديث الطاولة ${table.number}:`, {
        status: table.status,
        assignedWaiter: req.user._id,
        waiterUsername: req.user.username,
        waiterName: req.user.name
      });
    } catch (tableError) {
      console.error(`❌ فشل في تحديث الطاولة ${table.number}:`, tableError);
    }
  }

  // Populate order details
  const populatedOrder = await Order.findById(savedOrder._id)
    .populate('staff.waiter', 'name username')
    .populate('items.product', 'name price category')
    .populate('table', 'number name');

  // Transform data for frontend compatibility
  const mappedOrder = responseMapping.mapOrderForFrontend(populatedOrder);
  // Emit socket event for real-time updates
  if (req.app.get('io')) {
    const io = req.app.get('io');
    
    // Send comprehensive order data for real-time updates
    const orderNotification = {
      orderId: savedOrder._id,
      orderNumber: savedOrder.orderNumber,
      tableNumber: table ? table.number : null,
      tableName: table ? table.name : null,
      waiterName: req.user.name || req.user.username,
      waiterId: req.user._id,
      items: mappedOrder.items,
      status: 'pending',
      customer: mappedOrder.customer,
      totalAmount: mappedOrder.pricing?.total || total,
      orderType: orderType,
      timing: {
        orderTime: new Date(),
        estimatedPrepTime: processedItems.length * 5
      },
      message: `طلب جديد من الطاولة رقم ${table?.number || 'تيك أواي'} للعميل ${unifiedCustomer?.name || 'غير محدد'}`,
      timestamp: new Date().toISOString()
    };

    // Emit multiple events for comprehensive coverage
    io.emit('new-order', mappedOrder); // For order lists
    io.emit('order-created', orderNotification); // For general notifications
    io.to('role-chef').emit('new-order-notification', orderNotification); // For chefs specifically
    io.to('role-manager').emit('order-activity', {
      type: 'order-created',
      ...orderNotification
    }); // For managers

    console.log('📤 Socket.IO events emitted:', {
      orderId: savedOrder._id,
      events: ['new-order', 'order-created', 'new-order-notification', 'order-activity'],
      tableNumber: table?.number
    });
  } else {
    console.warn('⚠️ Socket.IO not available for real-time updates');
  }

  console.log('✅ Order created successfully:', {
    orderId: savedOrder._id,
    orderNumber: savedOrder.orderNumber,
    total: total,
    itemsCount: processedItems.length,
    tableId: table?._id,
    deviceType: req.deviceInfo?.type
  });

  return sendSuccess(res, mappedOrder, 'تم إنشاء الطلب بنجاح', 201);
}));

// Update order status
router.patch('/:id/status', authenticateToken, asyncHandler(async (req, res) => {
  const { status, chefId } = req.body;
  
  if (!status) {
    return sendError(res, 'حالة الطلب مطلوبة', 400, 'MISSING_STATUS');
  }

  const validStatuses = ['pending', 'preparing', 'ready', 'served', 'delivered', 'cancelled'];
  if (!validStatuses.includes(status)) {
    console.log('❌ Invalid status received:', {
      receivedStatus: status,
      validStatuses: validStatuses,
      requestBody: req.body
    });
    return sendError(res, `حالة الطلب غير صحيحة. الحالات المتاحة: ${validStatuses.join(', ')}`, 400, 'INVALID_STATUS');
  }

  const order = await Order.findById(req.params.id);
  if (!order) {
    return sendNotFound(res, 'الطلب');
  }

  // Update order status and timing
  const oldStatus = order.status;
  order.status = status;

  // Update timing based on status
  const now = new Date();
  switch (status) {
    case 'preparing':
      order.timing.prepStartTime = now;
      if (chefId) {
        const chef = await User.findById(chefId);
        if (chef && chef.role === 'chef') {
          order.staff.chef = chefId;
        }
      } else if (req.user && req.user.role === 'chef') {
        // تعيين الطباخ تلقائياً إذا كان المستخدم الحالي طباخ
        order.staff.chef = req.user._id;
        console.log(`✅ تم تعيين الطباخ ${req.user.name || req.user.username} للطلب ${order._id}`);
      }
      break;
    case 'ready':
      order.timing.readyTime = now;
      break;    case 'served':
    case 'delivered': // إضافة دعم حالة "delivered"
      order.timing.servedTime = now;
      // Free up table if dine-in
      if (order.table && order.orderType === 'dine-in') {
        const table = await Table.findById(order.table);
        if (table) {
          table.status = 'available';
          table.currentOrder = null;
          table.assignedWaiter = null; // إضافة إلغاء تعيين النادل
          table.occupiedAt = null;
          await table.save();
          
          console.log(`✅ تم تحرير الطاولة ${table.number} بعد تسليم الطلب`);
        }
      }
      break;    case 'cancelled':
      order.timing.cancelledTime = now;
      // Free up table if dine-in
      if (order.table && order.orderType === 'dine-in') {
        try {
          const Table = require('../models/Table');
          console.log('🔍 PATCH-Cancel: Attempting to find table:', {
            tableId: order.table,
            tableType: typeof order.table,
            orderId: order._id
          });
          
          const table = await Table.findById(order.table);
          if (table) {
            table.status = 'available';
            table.currentOrder = null;
            table.occupiedAt = null;
            await table.save();
            
            console.log(`✅ PATCH-Cancel: تم تحرير الطاولة ${table.number} بعد إلغاء الطلب`);
          } else {
            console.log(`⚠️ PATCH-Cancel: الطاولة غير موجودة: ${order.table}`);
          }
        } catch (tableError) {
          console.error(`❌ PATCH-Cancel: خطأ في تحرير الطاولة:`, tableError);
          // لا نوقف العملية إذا كان هناك خطأ في الطاولة
        }
      }
      break;
  }

  order.updatedAt = now;
  const updatedOrder = await order.save();

  // Populate updated order
  const populatedOrder = await Order.findById(updatedOrder._id)
    .populate('staff.waiter', 'name username')
    .populate('staff.chef', 'name username')
    .populate('items.product', 'name price')
    .populate('table', 'number name');

  // Transform data for frontend compatibility
  const mappedOrder = responseMapping.mapOrderForFrontend(populatedOrder);

  // Emit socket event for real-time updates
  if (req.app.get('io')) {
    req.app.get('io').emit('order-status-updated', {
      orderId: order._id,
      oldStatus,
      newStatus: status,
      order: mappedOrder
    });
  }

  console.log('✅ Order status updated:', {
    orderId: order._id,
    oldStatus,
    newStatus: status,
    deviceType: req.deviceInfo?.type
  });

  return sendSuccess(res, mappedOrder, `تم تحديث حالة الطلب إلى ${getStatusInArabic(status)}`);
}));

// Add item to existing order
router.post('/:id/items', authenticateToken, asyncHandler(async (req, res) => {
  const { productId, quantity, specialRequests, modifications } = req.body;

  if (!productId || !quantity || quantity <= 0) {
    return sendError(res, 'معرف المنتج والكمية مطلوبان', 400, 'INVALID_ITEM');
  }

  const order = await Order.findById(req.params.id);
  if (!order) {
    return sendNotFound(res, 'الطلب');
  }

  if (order.status === 'served' || order.status === 'cancelled') {
    return sendError(res, 'لا يمكن تعديل طلب مُكتمل أو مُلغى', 400, 'ORDER_COMPLETED');
  }

  const product = await Product.findById(productId);
  if (!product) {
    return sendNotFound(res, 'المنتج');
  }

  if (!product.available) {
    return sendError(res, `المنتج غير متاح: ${product.name}`, 400, 'PRODUCT_UNAVAILABLE');
  }

  // Add item to order
  const itemTotal = product.price * quantity;
  const newItem = {
    product: product._id,
    productName: product.name,
    quantity,
    price: product.price,
    total: itemTotal,
    specialRequests: specialRequests || '',
    modifications: modifications || []
  };

  order.items.push(newItem);

  // Recalculate pricing
  const subtotal = order.items.reduce((sum, item) => sum + item.total, 0);
  const tax = subtotal * 0.15;
  const total = subtotal + tax;

  order.pricing = {
    subtotal,
    tax,
    total,
    currency: 'SAR'
  };

  order.updatedAt = new Date();
  const updatedOrder = await order.save();

  // Populate and transform
  const populatedOrder = await Order.findById(updatedOrder._id)
    .populate('staff.waiter', 'name username')
    .populate('staff.chef', 'name username')
    .populate('items.product', 'name price')
    .populate('table', 'number name');

  const mappedOrder = responseMapping.mapOrderForFrontend(populatedOrder);

  // Emit socket event
  if (req.app.get('io')) {
    req.app.get('io').emit('order-updated', mappedOrder);
  }

  console.log('✅ Item added to order:', {
    orderId: order._id,
    productName: product.name,
    quantity,
    newTotal: total,
    deviceType: req.deviceInfo?.type
  });

  return sendSuccess(res, mappedOrder, 'تم إضافة العنصر للطلب بنجاح');
}));

// Remove item from order
router.delete('/:id/items/:itemId', authenticateToken, asyncHandler(async (req, res) => {
  const order = await Order.findById(req.params.id);
  if (!order) {
    return sendNotFound(res, 'الطلب');
  }

  if (order.status === 'served' || order.status === 'cancelled') {
    return sendError(res, 'لا يمكن تعديل طلب مُكتمل أو مُلغى', 400, 'ORDER_COMPLETED');
  }

  const itemIndex = order.items.findIndex(item => item._id.toString() === req.params.itemId);
  if (itemIndex === -1) {
    return sendNotFound(res, 'العنصر');
  }

  // Remove item
  const removedItem = order.items[itemIndex];
  order.items.splice(itemIndex, 1);

  // Check if order still has items
  if (order.items.length === 0) {
    return sendError(res, 'لا يمكن حذف آخر عنصر من الطلب', 400, 'CANNOT_REMOVE_LAST_ITEM');
  }

  // Recalculate pricing
  const subtotal = order.items.reduce((sum, item) => sum + item.total, 0);
  const tax = subtotal * 0.15;
  const total = subtotal + tax;

  order.pricing = {
    subtotal,
    tax,
    total,
    currency: 'SAR'
  };

  order.updatedAt = new Date();
  const updatedOrder = await order.save();

  // Populate and transform
  const populatedOrder = await Order.findById(updatedOrder._id)
    .populate('staff.waiter', 'name username')
    .populate('staff.chef', 'name username')
    .populate('items.product', 'name price')
    .populate('table', 'number name');

  const mappedOrder = responseMapping.mapOrderForFrontend(populatedOrder);

  // Emit socket event
  if (req.app.get('io')) {
    req.app.get('io').emit('order-updated', mappedOrder);
  }

  console.log('✅ Item removed from order:', {
    orderId: order._id,
    removedItem: removedItem.productName,
    newTotal: total,
    deviceType: req.deviceInfo?.type
  });

  return sendSuccess(res, mappedOrder, 'تم حذف العنصر من الطلب بنجاح');
}));

// Get order statistics
router.get('/stats/summary', authenticateToken, asyncHandler(async (req, res) => {
  const { date, period = 'today' } = req.query;
  
  let startDate, endDate;
  const now = new Date();

  switch (period) {
    case 'today':
      startDate = new Date(now.getFullYear(), now.getMonth(), now.getDate());
      endDate = new Date(now.getFullYear(), now.getMonth(), now.getDate() + 1);
      break;
    case 'week':
      const startOfWeek = new Date(now);
      startOfWeek.setDate(now.getDate() - now.getDay());
      startDate = new Date(startOfWeek.getFullYear(), startOfWeek.getMonth(), startOfWeek.getDate());
      endDate = new Date(startDate);
      endDate.setDate(startDate.getDate() + 7);
      break;
    case 'month':
      startDate = new Date(now.getFullYear(), now.getMonth(), 1);
      endDate = new Date(now.getFullYear(), now.getMonth() + 1, 1);
      break;
    case 'custom':
      if (date) {
        const targetDate = new Date(date);
        startDate = new Date(targetDate.getFullYear(), targetDate.getMonth(), targetDate.getDate());
        endDate = new Date(targetDate.getFullYear(), targetDate.getMonth(), targetDate.getDate() + 1);
      } else {
        return sendError(res, 'التاريخ مطلوب للفترة المخصصة', 400, 'MISSING_DATE');
      }
      break;
    default:
      return sendError(res, 'فترة غير صحيحة', 400, 'INVALID_PERIOD');
  }

  const dateFilter = {
    'timing.orderTime': {
      $gte: startDate,
      $lt: endDate
    }
  };

  // Get basic statistics
  const [totalOrders, completedOrders, cancelledOrders, pendingOrders] = await Promise.all([
    Order.countDocuments(dateFilter),
    Order.countDocuments({ ...dateFilter, status: 'served' }),
    Order.countDocuments({ ...dateFilter, status: 'cancelled' }),
    Order.countDocuments({ ...dateFilter, status: { $in: ['pending', 'preparing', 'ready'] } })
  ]);

  // Get revenue statistics
  const revenueStats = await Order.aggregate([
    { $match: { ...dateFilter, status: 'served' } },
    {
      $group: {
        _id: null,
        totalRevenue: { $sum: '$pricing.total' },
        averageOrderValue: { $avg: '$pricing.total' },
        totalTax: { $sum: '$pricing.tax' }
      }
    }
  ]);

  const revenue = revenueStats[0] || { totalRevenue: 0, averageOrderValue: 0, totalTax: 0 };

  // Get popular items
  const popularItems = await Order.aggregate([
    { $match: { ...dateFilter, status: 'served' } },
    { $unwind: '$items' },
    {
      $group: {
        _id: '$items.product',
        productName: { $first: '$items.productName' },
        totalQuantity: { $sum: '$items.quantity' },
        totalRevenue: { $sum: '$items.total' }
      }
    },
    { $sort: { totalQuantity: -1 } },
    { $limit: 10 }
  ]);

  const stats = {
    period,
    dateRange: { startDate, endDate },
    orders: {
      total: totalOrders,
      completed: completedOrders,
      cancelled: cancelledOrders,
      pending: pendingOrders,
      completionRate: totalOrders > 0 ? ((completedOrders / totalOrders) * 100).toFixed(2) : 0
    },
    revenue: {
      total: revenue.totalRevenue,
      average: revenue.averageOrderValue,
      tax: revenue.totalTax
    },
    popularItems
  };

  console.log('📊 Order statistics generated:', {
    period,
    totalOrders,
    totalRevenue: revenue.totalRevenue,
    deviceType: req.deviceInfo?.type
  });  return sendSuccess(res, stats, 'تم تحميل إحصائيات الطلبات بنجاح');
}));

// Update order status (PUT method for frontend compatibility)
router.put('/:id', authenticateToken, asyncHandler(async (req, res) => {
  const { status, chefId } = req.body;
  
  console.log('📝 PUT order status update request:', {
    orderId: req.params.id,
    requestBody: req.body,
    userRole: req.user?.role,
    userName: req.user?.username
  });
  
  if (!status) {
    return sendError(res, 'حالة الطلب مطلوبة', 400, 'MISSING_STATUS');
  }

  const validStatuses = ['pending', 'preparing', 'ready', 'served', 'delivered', 'cancelled'];
  if (!validStatuses.includes(status)) {
    console.log('❌ Invalid status received in PUT:', {
      receivedStatus: status,
      validStatuses: validStatuses,
      requestBody: req.body
    });
    return sendError(res, `حالة الطلب غير صحيحة. الحالات المتاحة: ${validStatuses.join(', ')}`, 400, 'INVALID_STATUS');
  }

  const order = await Order.findById(req.params.id);
  if (!order) {
    return sendNotFound(res, 'الطلب');
  }

  // Update order status and timing
  const oldStatus = order.status;
  order.status = status;

  // Update timing based on status
  const now = new Date();
  switch (status) {
    case 'preparing':
      order.timing.prepStartTime = now;
      if (chefId) {
        const chef = await User.findById(chefId);
        if (chef && chef.role === 'chef') {
          order.staff.chef = chefId;
        }
      } else if (req.user && req.user.role === 'chef') {
        // تعيين الطباخ تلقائياً إذا كان المستخدم الحالي طباخ
        order.staff.chef = req.user._id;
        console.log(`✅ تم تعيين الطباخ ${req.user.name || req.user.username} للطلب ${order._id}`);
      }
      break;
    case 'ready':
      order.timing.readyTime = now;
      break;    case 'served':
    case 'delivered': // إضافة دعم حالة "delivered"
      order.timing.servedTime = now;
      // Free up table if dine-in
      if (order.table && order.orderType === 'dine-in') {
        try {
          const Table = require('../models/Table');
          console.log('🔍 Attempting to find table:', {
            tableId: order.table,
            tableType: typeof order.table,
            orderId: order._id
          });
          
          const table = await Table.findById(order.table);
          if (table) {
            table.status = 'available';
            table.currentOrder = null;
            table.assignedWaiter = null;
            table.occupiedAt = null;
            await table.save();
            
            console.log(`✅ تم تحرير الطاولة ${table.number} بعد تسليم الطلب`);
          } else {
            console.log(`⚠️ الطاولة غير موجودة: ${order.table}`);
          }
        } catch (tableError) {
          console.error(`❌ خطأ في تحرير الطاولة:`, tableError);
          // لا نوقف العملية إذا كان هناك خطأ في الطاولة
        }
      }
      break;    case 'cancelled':
      order.timing.cancelledTime = now;
      // Free up table if dine-in
      if (order.table && order.orderType === 'dine-in') {
        try {
          const Table = require('../models/Table');
          console.log('🔍 Attempting to find table for cancellation:', {
            tableId: order.table,
            tableType: typeof order.table,
            orderId: order._id
          });
          
          const table = await Table.findById(order.table);
          if (table) {
            table.status = 'available';
            table.currentOrder = null;
            table.assignedWaiter = null;
            table.occupiedAt = null;
            await table.save();
            
            console.log(`✅ تم تحرير الطاولة ${table.number} بعد إلغاء الطلب`);
          } else {
            console.log(`⚠️ الطاولة غير موجودة للإلغاء: ${order.table}`);
          }
        } catch (tableError) {
          console.error(`❌ خطأ في تحرير الطاولة عند الإلغاء:`, tableError);
          // لا نوقف العملية إذا كان هناك خطأ في الطاولة
        }
      }
      break;
  }

  // Save the updated order
  await order.save();

  // Populate the order for response
  const populatedOrder = await Order.findById(order._id)
    .populate('staff.waiter', 'name username')
    .populate('staff.chef', 'name username')
    .populate('items.product', 'name price category')
    .populate('table', 'number name');

  // Transform data for frontend compatibility
  const responseMapping = require('../middleware/responseMapping');
  const mappedOrder = responseMapping.mapOrderForFrontend(populatedOrder);

  // Emit socket events for real-time updates
  if (req.app.get('io')) {
    const io = req.app.get('io');
    
    const statusUpdateNotification = {
      orderId: order._id,
      orderNumber: order.orderNumber,
      oldStatus: oldStatus,
      newStatus: status,
      tableNumber: populatedOrder.table?.number,
      waiterName: populatedOrder.staff?.waiter?.name || populatedOrder.staff?.waiter?.username,
      chefName: populatedOrder.staff?.chef?.name || populatedOrder.staff?.chef?.username,
      timestamp: new Date().toISOString(),
      updatedBy: req.user.username || req.user.name
    };

    // Emit to all connected users
    io.emit('order-status-update', statusUpdateNotification);
    
    // Emit to specific roles based on status
    if (status === 'ready') {
      // Notify waiters when order is ready
      io.to('role-waiter').emit('order-ready-notification', {
        ...statusUpdateNotification,
        message: `الطلب #${order.orderNumber} جاهز للتسليم من الطاولة ${populatedOrder.table?.number || 'تيك أواي'}`
      });
    } else if (status === 'served' || status === 'delivered') {
      // Notify managers when order is completed
      io.to('role-manager').emit('order-completed-notification', {
        ...statusUpdateNotification,
        message: `تم تسليم الطلب #${order.orderNumber} من الطاولة ${populatedOrder.table?.number || 'تيك أواي'}`
      });
    }

    console.log('📤 Socket notifications sent for status update:', {
      orderId: order._id,
      oldStatus,
      newStatus: status,
      events: ['order-status-update', status === 'ready' ? 'order-ready-notification' : 'order-completed-notification']
    });
  }

  console.log('✅ Order status updated successfully:', {
    orderId: order._id,
    oldStatus: oldStatus,
    newStatus: status,
    updatedBy: req.user.username
  });

  return sendSuccess(res, mappedOrder, `تم تحديث حالة الطلب إلى ${status} بنجاح`);
}));

// Update order status (PATCH method - keeping existing functionality)
router.patch('/:id/status', authenticateToken, asyncHandler(async (req, res) => {
  const { status, chefId } = req.body;
  
  if (!status) {
    return sendError(res, 'حالة الطلب مطلوبة', 400, 'MISSING_STATUS');
  }

  const validStatuses = ['pending', 'preparing', 'ready', 'served', 'delivered', 'cancelled'];
  if (!validStatuses.includes(status)) {
    console.log('❌ Invalid status received:', {
      receivedStatus: status,
      validStatuses: validStatuses,
      requestBody: req.body
    });
    return sendError(res, `حالة الطلب غير صحيحة. الحالات المتاحة: ${validStatuses.join(', ')}`, 400, 'INVALID_STATUS');
  }

  const order = await Order.findById(req.params.id);
  if (!order) {
    return sendNotFound(res, 'الطلب');
  }

  // Update order status and timing
  const oldStatus = order.status;
  order.status = status;

  // Update timing based on status
  const now = new Date();
  switch (status) {
    case 'preparing':
      order.timing.prepStartTime = now;
      if (chefId) {
        const chef = await User.findById(chefId);
        if (chef && chef.role === 'chef') {
          order.staff.chef = chefId;
        }
      } else if (req.user && req.user.role === 'chef') {
        // تعيين الطباخ تلقائياً إذا كان المستخدم الحالي طباخ
        order.staff.chef = req.user._id;
        console.log(`✅ تم تعيين الطباخ ${req.user.name || req.user.username} للطلب ${order._id}`);
      }
      break;
    case 'ready':
      order.timing.readyTime = now;
      break;
    case 'served':
    case 'delivered': // إضافة دعم حالة "delivered"
      order.timing.servedTime = now;
      // Free up table if dine-in
      if (order.table && order.orderType === 'dine-in') {
        const table = await Table.findById(order.table);
        if (table) {
          table.status = 'available';
          table.currentOrder = null;
          table.assignedWaiter = null; // إضافة إلغاء تعيين النادل
          table.occupiedAt = null;
          await table.save();
          
          console.log(`✅ تم تحرير الطاولة ${table.number} بعد تسليم الطلب`);
        }
      }
      break;
    case 'cancelled':
      order.timing.cancelledTime = now;
      // Free up table if dine-in
      if (order.table && order.orderType === 'dine-in') {
        const table = await Table.findById(order.table);
        if (table) {
          table.status = 'available';
          table.currentOrder = null;
          table.assignedWaiter = null;
          table.occupiedAt = null;
          await table.save();
        }
      }
      break;
  }

  order.updatedAt = now;
  const updatedOrder = await order.save();

  // Populate updated order
  const populatedOrder = await Order.findById(updatedOrder._id)
    .populate('staff.waiter', 'name username')
    .populate('staff.chef', 'name username')
    .populate('items.product', 'name price')
    .populate('table', 'number name');

  // Transform data for frontend compatibility
  const mappedOrder = responseMapping.mapOrderForFrontend(populatedOrder);

  // Emit socket event for real-time updates
  if (req.app.get('io')) {
    req.app.get('io').emit('order-status-updated', {
      orderId: order._id,
      oldStatus,
      newStatus: status,
      order: mappedOrder
    });
  }

  console.log('✅ Order status updated:', {
    orderId: order._id,
    oldStatus,
    newStatus: status,
    deviceType: req.deviceInfo?.type
  });

  return sendSuccess(res, mappedOrder, `تم تحديث حالة الطلب إلى ${getStatusInArabic(status)}`);
}));

// Add item to existing order
router.post('/:id/items', authenticateToken, asyncHandler(async (req, res) => {
  const { productId, quantity, specialRequests, modifications } = req.body;

  if (!productId || !quantity || quantity <= 0) {
    return sendError(res, 'معرف المنتج والكمية مطلوبان', 400, 'INVALID_ITEM');
  }

  const order = await Order.findById(req.params.id);
  if (!order) {
    return sendNotFound(res, 'الطلب');
  }

  if (order.status === 'served' || order.status === 'cancelled') {
    return sendError(res, 'لا يمكن تعديل طلب مُكتمل أو مُلغى', 400, 'ORDER_COMPLETED');
  }

  const product = await Product.findById(productId);
  if (!product) {
    return sendNotFound(res, 'المنتج');
  }

  if (!product.available) {
    return sendError(res, `المنتج غير متاح: ${product.name}`, 400, 'PRODUCT_UNAVAILABLE');
  }

  // Add item to order
  const itemTotal = product.price * quantity;
  const newItem = {
    product: product._id,
    productName: product.name,
    quantity,
    price: product.price,
    total: itemTotal,
    specialRequests: specialRequests || '',
    modifications: modifications || []
  };

  order.items.push(newItem);

  // Recalculate pricing
  const subtotal = order.items.reduce((sum, item) => sum + item.total, 0);
  const tax = subtotal * 0.15;
  const total = subtotal + tax;

  order.pricing = {
    subtotal,
    tax,
    total,
    currency: 'SAR'
  };

  order.updatedAt = new Date();
  const updatedOrder = await order.save();

  // Populate and transform
  const populatedOrder = await Order.findById(updatedOrder._id)
    .populate('staff.waiter', 'name username')
    .populate('staff.chef', 'name username')
    .populate('items.product', 'name price')
    .populate('table', 'number name');

  const mappedOrder = responseMapping.mapOrderForFrontend(populatedOrder);

  // Emit socket event
  if (req.app.get('io')) {
    req.app.get('io').emit('order-updated', mappedOrder);
  }

  console.log('✅ Item added to order:', {
    orderId: order._id,
    productName: product.name,
    quantity,
    newTotal: total,
    deviceType: req.deviceInfo?.type
  });

  return sendSuccess(res, mappedOrder, 'تم إضافة العنصر للطلب بنجاح');
}));

// Remove item from order
router.delete('/:id/items/:itemId', authenticateToken, asyncHandler(async (req, res) => {
  const order = await Order.findById(req.params.id);
  if (!order) {
    return sendNotFound(res, 'الطلب');
  }

  if (order.status === 'served' || order.status === 'cancelled') {
    return sendError(res, 'لا يمكن تعديل طلب مُكتمل أو مُلغى', 400, 'ORDER_COMPLETED');
  }

  const itemIndex = order.items.findIndex(item => item._id.toString() === req.params.itemId);
  if (itemIndex === -1) {
    return sendNotFound(res, 'العنصر');
  }

  // Remove item
  const removedItem = order.items[itemIndex];
  order.items.splice(itemIndex, 1);

  // Check if order still has items
  if (order.items.length === 0) {
    return sendError(res, 'لا يمكن حذف آخر عنصر من الطلب', 400, 'CANNOT_REMOVE_LAST_ITEM');
  }

  // Recalculate pricing
  const subtotal = order.items.reduce((sum, item) => sum + item.total, 0);
  const tax = subtotal * 0.15;
  const total = subtotal + tax;

  order.pricing = {
    subtotal,
    tax,
    total,
    currency: 'SAR'
  };

  order.updatedAt = new Date();
  const updatedOrder = await order.save();

  // Populate and transform
  const populatedOrder = await Order.findById(updatedOrder._id)
    .populate('staff.waiter', 'name username')
    .populate('staff.chef', 'name username')
    .populate('items.product', 'name price')
    .populate('table', 'number name');

  const mappedOrder = responseMapping.mapOrderForFrontend(populatedOrder);

  // Emit socket event
  if (req.app.get('io')) {
    req.app.get('io').emit('order-updated', mappedOrder);
  }

  console.log('✅ Item removed from order:', {
    orderId: order._id,
    removedItem: removedItem.productName,
    newTotal: total,
    deviceType: req.deviceInfo?.type
  });

  return sendSuccess(res, mappedOrder, 'تم حذف العنصر من الطلب بنجاح');
}));

// Get order statistics
router.get('/stats/summary', authenticateToken, asyncHandler(async (req, res) => {
  const { date, period = 'today' } = req.query;
  
  let startDate, endDate;
  const now = new Date();

  switch (period) {
    case 'today':
      startDate = new Date(now.getFullYear(), now.getMonth(), now.getDate());
      endDate = new Date(now.getFullYear(), now.getMonth(), now.getDate() + 1);
      break;
    case 'week':
      const startOfWeek = new Date(now);
      startOfWeek.setDate(now.getDate() - now.getDay());
      startDate = new Date(startOfWeek.getFullYear(), startOfWeek.getMonth(), startOfWeek.getDate());
      endDate = new Date(startDate);
      endDate.setDate(startDate.getDate() + 7);
      break;
    case 'month':
      startDate = new Date(now.getFullYear(), now.getMonth(), 1);
      endDate = new Date(now.getFullYear(), now.getMonth() + 1, 1);
      break;
    case 'custom':
      if (date) {
        const targetDate = new Date(date);
        startDate = new Date(targetDate.getFullYear(), targetDate.getMonth(), targetDate.getDate());
        endDate = new Date(targetDate.getFullYear(), targetDate.getMonth(), targetDate.getDate() + 1);
      } else {
        return sendError(res, 'التاريخ مطلوب للفترة المخصصة', 400, 'MISSING_DATE');
      }
      break;
    default:
      return sendError(res, 'فترة غير صحيحة', 400, 'INVALID_PERIOD');
  }

  const dateFilter = {
    'timing.orderTime': {
      $gte: startDate,
      $lt: endDate
    }
  };

  // Get basic statistics
  const [totalOrders, completedOrders, cancelledOrders, pendingOrders] = await Promise.all([
    Order.countDocuments(dateFilter),
    Order.countDocuments({ ...dateFilter, status: 'served' }),
    Order.countDocuments({ ...dateFilter, status: 'cancelled' }),
    Order.countDocuments({ ...dateFilter, status: { $in: ['pending', 'preparing', 'ready'] } })
  ]);

  // Get revenue statistics
  const revenueStats = await Order.aggregate([
    { $match: { ...dateFilter, status: 'served' } },
    {
      $group: {
        _id: null,
        totalRevenue: { $sum: '$pricing.total' },
        averageOrderValue: { $avg: '$pricing.total' },
        totalTax: { $sum: '$pricing.tax' }
      }
    }
  ]);

  const revenue = revenueStats[0] || { totalRevenue: 0, averageOrderValue: 0, totalTax: 0 };

  // Get popular items
  const popularItems = await Order.aggregate([
    { $match: { ...dateFilter, status: 'served' } },
    { $unwind: '$items' },
    {
      $group: {
        _id: '$items.product',
        productName: { $first: '$items.productName' },
        totalQuantity: { $sum: '$items.quantity' },
        totalRevenue: { $sum: '$items.total' }
      }
    },
    { $sort: { totalQuantity: -1 } },
    { $limit: 10 }
  ]);

  const stats = {
    period,
    dateRange: { startDate, endDate },
    orders: {
      total: totalOrders,
      completed: completedOrders,
      cancelled: cancelledOrders,
      pending: pendingOrders,
      completionRate: totalOrders > 0 ? ((completedOrders / totalOrders) * 100).toFixed(2) : 0
    },
    revenue: {
      total: revenue.totalRevenue,
      average: revenue.averageOrderValue,
      tax: revenue.totalTax
    },
    popularItems
  };

  console.log('📊 Order statistics generated:', {
    period,
    totalOrders,
    totalRevenue: revenue.totalRevenue,
    deviceType: req.deviceInfo?.type
  });  return sendSuccess(res, stats, 'تم تحميل إحصائيات الطلبات بنجاح');
}));

// PUT /:id - Update order (full update for frontend compatibility)
router.put('/:id', authenticateToken, asyncHandler(async (req, res) => {
  console.log('🔄 PUT Order Update Request:', {
    orderId: req.params.id,
    body: req.body,
    user: req.user.username
  });

  const { status, chefId, waiterComment, specialInstructions } = req.body;
  
  const order = await Order.findById(req.params.id);
  if (!order) {
    console.log('❌ Order not found:', req.params.id);
    return sendNotFound(res, 'الطلب');
  }

  console.log('📋 Current order status:', order.status, '→ New status:', status);

  // Update status if provided
  if (status) {
    const validStatuses = ['pending', 'preparing', 'ready', 'served', 'cancelled'];
    if (!validStatuses.includes(status)) {
      return sendError(res, 'حالة الطلب غير صحيحة', 400, 'INVALID_STATUS');
    }

    const oldStatus = order.status;
    order.status = status;

    // Update timing based on status
    const now = new Date();
    switch (status) {
      case 'preparing':
        if (!order.timing.prepStartTime) {
          order.timing.prepStartTime = now;
        }
        break;
      case 'ready':
        if (!order.timing.prepEndTime) {
          order.timing.prepEndTime = now;
        }
        break;
      case 'served':
        if (!order.timing.servedTime) {
          order.timing.servedTime = now;
        }
        break;
      case 'cancelled':
        order.timing.cancelledTime = now;
        break;
    }

    // Emit socket events for status changes
    if (oldStatus !== status && global.socketHandlers) {
      try {
        global.socketHandlers.orderStatusUpdated({
          orderId: order._id,
          orderNumber: order.orderNumber,
          oldStatus,
          newStatus: status,
          tableNumber: order.tableNumber,
          timestamp: now.toISOString()
        });
      } catch (socketError) {
        console.error('Socket error:', socketError);
      }
    }
  }

  // Update other fields if provided
  if (chefId) {
    order.staff.chef = chefId;
  }

  if (waiterComment !== undefined) {
    order.waiterComment = waiterComment;
  }

  if (specialInstructions !== undefined) {
    order.specialInstructions = specialInstructions;
  }

  // Save the updated order
  const updatedOrder = await order.save();

  console.log('✅ Order updated successfully:', {
    id: updatedOrder._id,
    status: updatedOrder.status,
    updatedBy: req.user.username
  });
  // Map order for response
  const mappedOrder = {
    _id: updatedOrder._id,
    orderNumber: updatedOrder.orderNumber,
    status: updatedOrder.status,
    orderType: updatedOrder.orderType,
    tableNumber: updatedOrder.tableNumber,
    items: updatedOrder.items,
    customer: updatedOrder.customer,
    totalAmount: updatedOrder.totalAmount,
    timing: updatedOrder.timing,
    staff: updatedOrder.staff,
    waiterComment: updatedOrder.waiterComment,
    specialInstructions: updatedOrder.specialInstructions,
    createdAt: updatedOrder.createdAt,
    updatedAt: updatedOrder.updatedAt
  };

  return sendSuccess(res, mappedOrder, 'تم تحديث الطلب بنجاح');
}));

// DELETE /api/orders/reset-all - Reset all orders (Manager only)
router.delete('/reset-all', authenticateToken, requireManager, asyncHandler(async (req, res) => {
  console.log('🗑️ Reset all orders request from user:', req.user.username);

  try {
    // Get count before deletion
    const orderCount = await Order.countDocuments();
    
    // Delete all orders
    const deleteResult = await Order.deleteMany({});
    
    console.log('✅ Orders reset completed:', {
      deletedCount: deleteResult.deletedCount,
      originalCount: orderCount,
      user: req.user.username,
      timestamp: new Date().toISOString()
    });

    return sendSuccess(res, {
      deletedCount: deleteResult.deletedCount,
      originalCount: orderCount,
      message: `تم حذف ${deleteResult.deletedCount} طلب بنجاح`
    }, 'تم إعادة تهيئة جميع الطلبات بنجاح');

  } catch (error) {
    console.error('❌ Error resetting orders:', error);
    return sendError(res, 'خطأ في إعادة تهيئة الطلبات', 500, error.message);
  }
}));

// Helper function to get status in Arabic
function getStatusInArabic(status) {
  const statusMap = {
    'pending': 'في الانتظار',
    'preparing': 'قيد التحضير',
    'ready': 'جاهز',
    'served': 'تم التقديم',
    'cancelled': 'مُلغى'
  };
  return statusMap[status] || status;
}

// Delete order (hard delete) - Manager only
router.delete('/:id', authenticateToken, requireManager, asyncHandler(async (req, res) => {
  const { id } = req.params;
  
  console.log(`🗑️ Delete order request for ID: ${id} by user:`, req.user.name);
  
  // Check if order exists
  const order = await Order.findById(id);
  if (!order) {
    console.log(`❌ Order not found: ${id}`);
    return sendNotFound(res, 'الطلب غير موجود');
  }
  
  console.log(`🔍 Order found: ${order.orderNumber || order._id}`);
  
  // Delete the order
  await Order.findByIdAndDelete(id);
  
  console.log(`✅ Order deleted successfully: ${id}`);
  
  sendSuccess(res, null, 'تم حذف الطلب نهائياً');
}));

module.exports = router;

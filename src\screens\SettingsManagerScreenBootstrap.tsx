import React, { useEffect } from 'react';
import ResponsiveGrid from '../components/ResponsiveGrid';
import ResponsiveCard from '../components/ResponsiveCard';
import socket from '../socket';
import '../styles/screens/SettingsScreenIsolated.css';

interface SettingsManagerScreenBootstrapProps {
  resetOrders: () => void;
  resetTables: () => void;
  resetDiscountRequests: () => void;
  resetAll: () => void;
}

const SettingsManagerScreenBootstrap: React.FC<SettingsManagerScreenBootstrapProps> = ({
  resetOrders,
  resetTables,
  resetDiscountRequests,
  resetAll
}) => {
  // Socket.IO event listeners for real-time updates
  useEffect(() => {
    const handleSystemUpdate = () => {
      console.log('📡 Real-time system update received');
      // Settings screen doesn't need to refresh data, but we can log the events
    };

    // Add event listeners for system-wide events
    socket.on('systemReset', handleSystemUpdate);
    socket.on('dataReset', handleSystemUpdate);
    socket.on('settingsUpdated', handleSystemUpdate);

    // Cleanup function
    return () => {
      socket.off('systemReset', handleSystemUpdate);
      socket.off('dataReset', handleSystemUpdate);
      socket.off('settingsUpdated', handleSystemUpdate);
    };
  }, []);

  // Reset actions data
  const resetActions = [
    {
      title: 'إعادة تهيئة الطلبات',
      description: 'حذف جميع الطلبات من النظام نهائياً',
      icon: 'fas fa-shopping-cart',
      warning: 'تحذير: لا يمكن التراجع عن هذا الإجراء',
      buttonText: 'إعادة تهيئة الطلبات',
      buttonClass: 'btn-warning',
      action: resetOrders,
      type: 'warning'
    },
    {
      title: 'إعادة تهيئة الطاولات',
      description: 'إغلاق جميع الطاولات وحذف حساباتها',
      icon: 'fas fa-table',
      warning: 'سيتم إغلاق جميع الطاولات المفتوحة',
      buttonText: 'إعادة تهيئة الطاولات',
      buttonClass: 'btn-info',
      action: resetTables,
      type: 'info'
    },
    {
      title: 'إعادة تهيئة طلبات الخصم',
      description: 'حذف جميع طلبات الخصم من النظام',
      icon: 'fas fa-percentage',
      warning: 'سيتم حذف جميع طلبات الخصم المعلقة والمكتملة',
      buttonText: 'إعادة تهيئة طلبات الخصم',
      buttonClass: 'btn-secondary',
      action: resetDiscountRequests,
      type: 'secondary'
    },
    {
      title: 'إعادة تهيئة النظام بالكامل',
      description: 'حذف جميع البيانات: الطلبات، الطاولات، وطلبات الخصم',
      icon: 'fas fa-bomb',
      warning: 'خطر شديد: سيتم فقدان جميع البيانات نهائياً',
      buttonText: 'إعادة تهيئة النظام بالكامل',
      buttonClass: 'btn-danger',
      action: resetAll,
      type: 'danger',
      isDanger: true
    }
  ];

  // Coming soon features
  const comingSoonFeatures = [
    {
      title: 'إعدادات اللغة',
      description: 'اختيار لغة واجهة التطبيق',
      icon: 'fas fa-language'
    },
    {
      title: 'إعدادات المظهر',
      description: 'تخصيص ألوان وتصميم التطبيق',
      icon: 'fas fa-palette'
    },
    {
      title: 'إعدادات الإشعارات',
      description: 'تخصيص طريقة وتوقيت الإشعارات',
      icon: 'fas fa-bell'
    },
    {
      title: 'إعدادات النسخ الاحتياطي',
      description: 'جدولة النسخ الاحتياطي التلقائي',
      icon: 'fas fa-database'
    },
    {
      title: 'إعدادات الأمان',
      description: 'إدارة كلمات المرور والصلاحيات',
      icon: 'fas fa-shield-alt'
    },
    {
      title: 'إعدادات الطباعة',
      description: 'تخصيص إعدادات طباعة الفواتير',
      icon: 'fas fa-print'
    }
  ];

  return (
    <div className="settings-bootstrap-container container-fluid py-4">
      {/* Header */}
      <div className="text-center mb-5">
        <h1 className="display-4 text-white mb-2">
          <i className="fas fa-cogs me-3"></i>
          إعدادات النظام
        </h1>
        <p className="lead text-white-50">إدارة الإعدادات العامة للنظام وأدوات التهيئة</p>
      </div>

      {/* Reset System Section */}
      <div className="mb-5">
        <div className="text-center mb-4">
          <h2 className="text-white mb-2">
            <i className="fas fa-refresh me-2"></i>
            إعادة تهيئة النظام
          </h2>
          <p className="text-warning fw-bold">
            <i className="fas fa-exclamation-triangle me-1"></i>
            استخدم هذه الأدوات بحذر - إعادة التهيئة ستحذف البيانات نهائياً
          </p>
        </div>

        <ResponsiveGrid
          cols={{ xs: 1, sm: 1, md: 2, lg: 2, xl: 2, xxl: 2 }}
          gap={4}
        >
          {resetActions.map((action, index) => (
            <div
              key={index}
              className={`settings-action-card-enhanced ${action.type} ${action.isDanger ? 'danger' : ''}`}
            >
              {/* Status indicator bar */}
              <div className={`settings-status-bar ${action.type}`}></div>

              {/* Header */}
              <div className="settings-card-header">
                <div className={`settings-action-icon ${action.type}`}>
                  <i className={action.icon}></i>
                </div>
                <h4 className="settings-action-title">{action.title}</h4>
              </div>

              {/* Body */}
              <div className="settings-card-body">
                <p className="settings-action-description">{action.description}</p>

                <div className={`settings-warning-box ${action.type}`}>
                  <i className={`fas ${action.isDanger ? 'fa-skull-crossbones' : 'fa-exclamation-triangle'}`}></i>
                  <span>{action.warning}</span>
                </div>

                <button
                  className={`settings-action-btn ${action.type}`}
                  onClick={action.action}
                  title={action.title}
                >
                  <i className={action.icon}></i>
                  <span>{action.buttonText}</span>
                </button>
              </div>
            </div>
          ))}
        </ResponsiveGrid>
      </div>

      {/* General Settings Section */}
      <div className="mb-5">
        <div className="text-center mb-4">
          <h2 className="text-white mb-2">
            <i className="fas fa-sliders-h me-2"></i>
            الإعدادات العامة
          </h2>
          <p className="text-white-50">إعدادات التطبيق العامة (قيد التطوير)</p>
        </div>

        <ResponsiveGrid
          cols={{ xs: 1, sm: 2, md: 2, lg: 3, xl: 3, xxl: 3 }}
          gap={3}
        >
          {comingSoonFeatures.map((feature, index) => (
            <ResponsiveCard key={index} className="settings-coming-soon-card">
              <div className="card-body text-center">
                <div className="settings-feature-icon mb-3 text-muted">
                  <i className={feature.icon}></i>
                </div>
                <h5 className="card-title text-dark mb-2">{feature.title}</h5>
                <p className="card-text text-muted mb-3">{feature.description}</p>
                
                <div className="settings-coming-soon-badge">
                  <i className="fas fa-clock me-1"></i>
                  قريباً
                </div>
              </div>
            </ResponsiveCard>
          ))}
        </ResponsiveGrid>
      </div>

      {/* Info Section */}
      <div className="text-center">
        <ResponsiveCard className="settings-info-card mx-auto">
          <div className="card-body text-center py-4">
            <div className="mb-3">
              <i className="fas fa-info-circle display-4 text-info"></i>
            </div>
            <h4 className="text-dark mb-2">معلومات مهمة</h4>
            <p className="text-muted mb-0">
              تأكد من إجراء نسخة احتياطية قبل استخدام أدوات إعادة التهيئة.
              <br />
              في حالة الشك، راجع المطور أو المدير المسؤول عن النظام.
            </p>
          </div>
        </ResponsiveCard>
      </div>
    </div>
  );
};

export default SettingsManagerScreenBootstrap;

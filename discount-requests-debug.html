<!DOCTYPE html>
<html dir="rtl" lang="ar">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار طلبات الخصم</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            padding: 20px;
            direction: rtl;
            background: #f5f5f5;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .info-box {
            background: #e3f2fd;
            border: 1px solid #2196f3;
            padding: 15px;
            margin: 10px 0;
            border-radius: 5px;
        }
        .error-box {
            background: #ffebee;
            border: 1px solid #f44336;
            padding: 15px;
            margin: 10px 0;
            border-radius: 5px;
        }
        .success-box {
            background: #e8f5e8;
            border: 1px solid #4caf50;
            padding: 15px;
            margin: 10px 0;
            border-radius: 5px;
        }
        button {
            padding: 10px 20px;
            margin: 5px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            background: #2196f3;
            color: white;
            font-size: 14px;
        }
        button:hover {
            background: #1976d2;
        }
        .discount-card {
            border: 1px solid #ddd;
            margin: 10px 0;
            padding: 15px;
            border-radius: 8px;
            background: #fafafa;
        }
        .loading {
            text-align: center;
            padding: 20px;
            color: #666;
        }
        pre {
            background: #f4f4f4;
            padding: 10px;
            border-radius: 5px;
            overflow-x: auto;
            font-size: 12px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔍 اختبار طلبات الخصم</h1>
        
        <div class="info-box">
            <h3>📋 هذه الأداة ستساعد في تشخيص مشاكل طلبات الخصم:</h3>
            <ul>
                <li>مشكلة الطاولة تظهر "غير محدد"</li>
                <li>مشكلة المبلغ يظهر 0.00</li>
                <li>مشكلة زر التفاصيل لا يعمل</li>
            </ul>
        </div>
        
        <div>
            <button onclick="testAuth()">🔐 اختبار المصادقة</button>
            <button onclick="fetchDiscountRequests()">📄 جلب طلبات الخصم</button>
            <button onclick="testSampleRequest()">🧪 اختبار طلب عينة</button>
            <button onclick="clearResults()">🗑️ مسح النتائج</button>
        </div>
        
        <div id="results"></div>
    </div>

    <script>
        const API_BASE = 'http://localhost:5000/api/v1';
        
        function log(message, type = 'info') {
            const resultsDiv = document.getElementById('results');
            const boxClass = type === 'error' ? 'error-box' : 
                            type === 'success' ? 'success-box' : 'info-box';
            resultsDiv.innerHTML += `<div class="${boxClass}">${message}</div>`;
        }
        
        function clearResults() {
            document.getElementById('results').innerHTML = '';
        }
        
        async function getAuthHeaders() {
            const token = localStorage.getItem('token') || localStorage.getItem('authToken');
            return {
                'Authorization': `Bearer ${token}`,
                'Content-Type': 'application/json'
            };
        }
        
        async function testAuth() {
            log('🔄 اختبار المصادقة...');
            
            try {
                const headers = await getAuthHeaders();
                const response = await fetch(`${API_BASE}/orders`, { headers });
                
                if (response.ok) {
                    log('✅ المصادقة تعمل بشكل صحيح', 'success');
                } else {
                    const error = await response.json();
                    log(`❌ فشل في المصادقة: ${JSON.stringify(error)}`, 'error');
                }
            } catch (error) {
                log(`❌ خطأ في المصادقة: ${error.message}`, 'error');
            }
        }
        
        async function fetchDiscountRequests() {
            log('🔄 جاري جلب طلبات الخصم...');
            
            try {
                const headers = await getAuthHeaders();
                const response = await fetch(`${API_BASE}/discount-requests`, { headers });
                
                if (!response.ok) {
                    const error = await response.json();
                    log(`❌ فشل في جلب طلبات الخصم: ${JSON.stringify(error)}`, 'error');
                    return;
                }
                
                const data = await response.json();
                log(`✅ تم جلب ${data.length || 0} طلب خصم`, 'success');
                
                if (data && data.length > 0) {
                    data.forEach((request, index) => {
                        analyzeDiscountRequest(request, index);
                    });
                } else {
                    log('📭 لا توجد طلبات خصم', 'info');
                }
                
            } catch (error) {
                log(`❌ خطأ في جلب طلبات الخصم: ${error.message}`, 'error');
            }
        }
        
        function analyzeDiscountRequest(request, index) {
            log(`
                <div class="discount-card">
                    <h4>📄 طلب خصم #${index + 1}</h4>
                    <h5>📊 تحليل البيانات:</h5>
                    <ul>
                        <li><strong>معرف الطلب:</strong> ${request._id}</li>
                        <li><strong>رقم الطلب:</strong> ${request.orderNumber || 'غير موجود'}</li>
                        <li><strong>اسم النادل:</strong> ${request.waiterName || 'غير موجود'}</li>
                        <li><strong>المبلغ:</strong> ${request.amount !== undefined ? request.amount : 'غير محدد'}</li>
                        <li><strong>المبلغ المنسق:</strong> ${request.formattedAmount || 'غير موجود'}</li>
                        <li><strong>الحالة:</strong> ${request.status}</li>
                        <li><strong>السبب:</strong> ${request.reason || 'غير موجود'}</li>
                        <li><strong>تاريخ الإنشاء:</strong> ${request.createdAt}</li>
                    </ul>
                    
                    <h5>🏓 معلومات الطاولة:</h5>
                    <ul>
                        <li><strong>رقم الطاولة (من الطلب المضمن):</strong> ${request.order?.tableNumber || 'غير موجود'}</li>
                        <li><strong>رقم الطاولة (مباشر):</strong> ${request.tableNumber || 'غير موجود'}</li>
                        <li><strong>معرف الطلب المرتبط:</strong> ${request.orderId || 'غير موجود'}</li>
                    </ul>
                    
                    <h5>💰 معلومات المبلغ:</h5>
                    <ul>
                        <li><strong>المبلغ المباشر:</strong> ${request.amount !== undefined ? request.amount : 'غير محدد'}</li>
                        <li><strong>إجمالي الطلب (totals.total):</strong> ${request.order?.totals?.total || 'غير موجود'}</li>
                        <li><strong>إجمالي الطلب (totalAmount):</strong> ${request.order?.totalAmount || 'غير موجود'}</li>
                        <li><strong>إجمالي الطلب (totalPrice):</strong> ${request.order?.totalPrice || 'غير موجود'}</li>
                    </ul>
                    
                    <h5>📦 بيانات الطلب المضمن:</h5>
                    <pre>${JSON.stringify(request.order, null, 2)}</pre>
                    
                    <h5>🔧 بيانات الطلب الكاملة:</h5>
                    <pre>${JSON.stringify(request, null, 2)}</pre>
                </div>
            `);
        }
        
        function testSampleRequest() {
            log('🧪 اختبار طلب خصم عينة...');
            
            const sampleRequest = {
                "_id": "test123",
                "orderId": "order456",
                "orderNumber": "ORD-001",
                "waiterName": "أحمد محمد",
                "amount": 25.50,
                "reason": "خصم للعميل المميز",
                "status": "pending",
                "createdAt": new Date().toISOString(),
                "tableNumber": 5,
                "order": {
                    "tableNumber": 5,
                    "totals": {
                        "total": 100.00,
                        "subtotal": 85.00,
                        "tax": 15.00,
                        "discount": 0
                    },
                    "totalAmount": 100.00,
                    "totalPrice": 100.00
                }
            };
            
            analyzeDiscountRequest(sampleRequest, 0);
        }
        
        // تشغيل تلقائي عند تحميل الصفحة
        window.addEventListener('load', () => {
            log('🚀 أداة اختبار طلبات الخصم جاهزة للاستخدام!');
            log('💡 انقر على "اختبار المصادقة" أولاً، ثم "جلب طلبات الخصم"');
        });
    </script>
</body>
</html>

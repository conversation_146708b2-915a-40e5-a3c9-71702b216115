// اختبار الاتصال بالـ API الحي وفحص البيانات
const API_BASE_URL = 'https://deshacoffee-production.up.railway.app';

async function testLiveAPI() {
  console.log('🧪 بدء اختبار API الحي...');
  
  try {
    // 1. اختبار صحة الخادم
    console.log('📡 اختبار صحة الخادم...');
    try {
      const healthResponse = await fetch(`${API_BASE_URL}/api/v1/health`);
      console.log('✅ حالة الخادم:', healthResponse.status);
    } catch (error) {
      console.log('⚠️ لا يمكن الوصول لـ health endpoint، سنتابع مع باقي الاختبارات...');
    }
    
    // 2. اختبار تسجيل الدخول لنادل Bosy
    console.log('🔐 اختبار تسجيل دخول النادل...');
    const loginResponse = await fetch(`${API_BASE_URL}/api/v1/auth/login`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        username: 'Bosy',
        password: '253040'
      })
    });
    
    const loginData = await loginResponse.json();
    console.log('📋 بيانات تسجيل الدخول:', loginData);
    
    if (loginData.success && loginData.token) {
      const token = loginData.token;
      const waiterId = loginData.user?._id || loginData.user?.id;
      
      console.log('✅ تم تسجيل الدخول بنجاح');
      console.log('👤 معرف النادل:', waiterId);
      
      // 3. اختبار جلب الطلبات
      console.log('📦 جلب الطلبات...');
      const ordersResponse = await fetch(`${API_BASE_URL}/api/v1/orders`, {
        headers: {
          'Authorization': `Bearer ${token}`
        }
      });
      
      const ordersData = await ordersResponse.json();
      console.log('📋 الطلبات المسترجعة:', {
        success: ordersData.success,
        total: ordersData.data?.length || 0,
        firstFew: ordersData.data?.slice(0, 3).map(order => ({
          id: order._id,
          orderNumber: order.orderNumber,
          tableNumber: order.tableNumber,
          waiterId: order.waiterId,
          waiterName: order.waiterName,
          status: order.status,
          totalPrice: order.totalPrice
        }))
      });
      
      // 4. اختبار جلب الطاولات
      console.log('🏓 جلب الطاولات...');
      
      // جرب عدة endpoints مختلفة للطاولات
      const tableEndpoints = [
        `/api/v1/table-accounts?waiterId=${waiterId}`,
        `/api/v1/table-accounts`,
        `/api/v1/tables?waiterId=${waiterId}`,
        `/api/v1/tables`
      ];
      
      let tablesData = null;
      
      for (const endpoint of tableEndpoints) {
        try {
          console.log(`🔍 جرب endpoint: ${endpoint}`);
          const tablesResponse = await fetch(`${API_BASE_URL}${endpoint}`, {
            headers: {
              'Authorization': `Bearer ${token}`
            }
          });
          
          const data = await tablesResponse.json();
          console.log(`📊 استجابة ${endpoint}:`, {
            status: tablesResponse.status,
            success: data.success,
            hasData: !!data.data,
            dataLength: data.data?.length || 0
          });
          
          if (data.success && data.data && data.data.length > 0) {
            tablesData = data;
            console.log(`✅ نجح ${endpoint}`);
            break;
          }
        } catch (error) {
          console.log(`❌ فشل ${endpoint}:`, error.message);
        }
      }
      console.log('🏓 الطاولات المسترجعة:', {
        success: tablesData.success,
        total: tablesData.data?.length || 0,
        firstFew: tablesData.data?.slice(0, 3).map(table => ({
          id: table._id,
          tableNumber: table.tableNumber,
          waiterId: table.waiterId,
          waiterName: table.waiterName,
          status: table.status,
          isOpen: table.isOpen
        }))
      });
      
      // 5. فحص مطابقة البيانات
      console.log('🔍 فحص مطابقة الطلبات مع الطاولات...');
      const orders = ordersData.data || [];
      const tables = tablesData.data || [];
      
      tables.forEach(table => {
        const matchingOrders = orders.filter(order => {
          const tableMatch = String(order.tableNumber) === String(table.tableNumber);
          const waiterMatch = order.waiterId === waiterId || order.waiterId === table.waiterId;
          return tableMatch && waiterMatch;
        });
        
        console.log(`🏓 طاولة ${table.tableNumber}: ${matchingOrders.length} طلب مطابق`);
        matchingOrders.forEach(order => {
          console.log(`   - طلب ${order.orderNumber || order._id.slice(-6)}: ${order.totalPrice} جنيه`);
        });
      });
      
    } else {
      console.error('❌ فشل في تسجيل الدخول:', loginData);
    }
    
  } catch (error) {
    console.error('❌ خطأ في الاختبار:', error);
  }
}

// تشغيل الاختبار
testLiveAPI();

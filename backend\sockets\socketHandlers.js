// filepath: backend/sockets/socketHandlers.js
const Order = require('../models/Order');
const Table = require('../models/Table');

class SocketHandlers {
  constructor(io) {
    this.io = io;
    this.connectedUsers = new Map(); // userId -> { socketId, role, rooms: Set }
  }

  handleConnection(socket) {
    console.log('👤 User connected:', socket.id);

    // Handle user registration with role
    socket.on('register-user', (userData) => {
      try {
        const { userId, role, name } = userData;
        
        this.connectedUsers.set(userId, {
          socketId: socket.id,
          role: role,
          name: name || 'مستخدم',
          rooms: new Set()
        });

        // Join role-based room
        socket.join(`role-${role}`);
        
        console.log(`👤 User registered: ${name} (${role}) - Socket: ${socket.id}`);
        
        // Send confirmation
        socket.emit('registration-confirmed', {
          userId,
          role,
          message: 'تم تسجيل الدخول بنجاح'
        });

        // Notify about current pending orders if chef
        if (role === 'chef') {
          this.sendPendingOrdersToChef(socket);
        }

      } catch (error) {
        console.error('Error registering user:', error);
        socket.emit('registration-error', { message: 'خطأ في تسجيل المستخدم' });
      }
    });

    // Handle joining specific rooms
    socket.on('join-room', (roomData) => {
      try {
        const { room, userId } = roomData;
        socket.join(room);
        
        // Update user's rooms
        const user = this.connectedUsers.get(userId);
        if (user) {
          user.rooms.add(room);
        }
        
        console.log(`👤 User ${socket.id} joined room: ${room}`);
        socket.emit('room-joined', { room, message: `انضممت إلى ${room}` });
      } catch (error) {
        console.error('Error joining room:', error);
      }
    });

    // Handle new order creation
    socket.on('order-created', async (orderData) => {
      try {
        const { orderId, tableNumber, waiterName, items } = orderData;
        
        console.log(`📋 New order created: ${orderId} for table ${tableNumber}`);
          // Notify all chefs about new order
        this.io.to('role-chef').emit('new-order-notification', {
          orderId,
          tableNumber,
          waiterName,
          items,
          status: 'pending',
          customer: orderData.customer,
          message: `طلب جديد من الطاولة رقم ${tableNumber || 'غير محدد'} للعميل ${orderData.customer?.name || orderData.customer || 'غير محدد'}`,
          timestamp: new Date().toISOString()
        });

        // Notify managers
        this.io.to('role-manager').emit('order-activity', {
          type: 'order-created',
          orderId,
          tableNumber,
          waiterName,
          customer: orderData.customer,
          message: `طلب جديد من الطاولة رقم ${tableNumber || 'غير محدد'} للعميل ${orderData.customer?.name || orderData.customer || 'غير محدد'}`,
          timestamp: new Date().toISOString()
        });

        console.log(`✅ Order ${orderId} notifications sent to chefs and managers`);
        
      } catch (error) {
        console.error('Error handling order creation:', error);
      }
    });    // Handle order status updates with enhanced waiter notifications
    socket.on('order-status-update', async (updateData) => {
      try {
        const { orderId, newStatus, chefName, tableNumber, waiterName } = updateData;
        
        console.log(`🔄 Order ${orderId} status updated to: ${newStatus} by ${chefName || 'unknown'}`);
        
        // Update order in database
        const updatedOrder = await Order.findByIdAndUpdate(orderId, { 
          status: newStatus,
          ...(newStatus === 'preparing' && chefName && { 'staff.chef': chefName }),
          ...(newStatus === 'ready' && { 'timing.readyTime': new Date() }),
          ...(newStatus === 'served' && { 'timing.servedTime': new Date() })
        }, { new: true }).populate('staff.waiter', 'name username')
          .populate('table', 'number name');

        const statusMessages = {
          'preparing': `🍳 بدأ الطباخ ${chefName || 'غير محدد'} تحضير طلبك من الطاولة رقم ${tableNumber || 'غير محدد'}`,
          'ready': `🔔 طلبك جاهز للتقديم! الطاولة رقم ${tableNumber || 'غير محدد'} - العميل ${updateData.customer?.name || updateData.customer || 'غير محدد'}`,
          'served': `✅ تم تقديم الطلب من الطاولة رقم ${tableNumber || 'غير محدد'} للعميل ${updateData.customer?.name || updateData.customer || 'غير محدد'}`,
          'delivered': `✅ تم تسليم الطلب من الطاولة رقم ${tableNumber || 'غير محدد'} للعميل ${updateData.customer?.name || updateData.customer || 'غير محدد'}`
        };

        // Enhanced notification data
        const notificationData = {
          orderId,
          orderNumber: updatedOrder?.orderNumber || `ORDER-${orderId.slice(-6)}`,
          newStatus,
          oldStatus: updateData.oldStatus,
          tableNumber,
          chefName: chefName || 'غير محدد',
          waiterName: waiterName || updatedOrder?.staff?.waiter?.username || 'غير محدد',
          customer: updateData.customer,
          message: statusMessages[newStatus] || `تم تحديث حالة الطلب إلى ${newStatus}`,
          timestamp: new Date().toISOString(),
          priority: newStatus === 'ready' ? 'high' : 'normal'
        };

        // 1. Notify specific waiter first (most important)
        if (waiterName || updatedOrder?.staff?.waiter?.username) {
          const targetWaiter = waiterName || updatedOrder.staff.waiter.username;
          
          // Find waiter's socket and send direct notification
          for (const [userId, userInfo] of this.connectedUsers.entries()) {
            if (userInfo.role === 'waiter' && 
                (userId.includes(targetWaiter) || userInfo.name === targetWaiter)) {
              
              this.io.to(userInfo.socketId).emit('order-status-update', {
                ...notificationData,
                message: `🔔 ${statusMessages[newStatus]}`,
                isDirectNotification: true
              });
              
              // Special ready notification for waiters
              if (newStatus === 'ready') {
                this.io.to(userInfo.socketId).emit('order-ready-notification', {
                  ...notificationData,
                  message: `🔔 طلبك جاهز! الطاولة ${tableNumber} - ${chefName}`,
                  sound: true,
                  vibrate: true
                });
              }
              
              console.log(`📱 Direct notification sent to waiter: ${targetWaiter} (${userInfo.socketId})`);
              break;
            }
          }
        }

        // 2. Broadcast to all waiters (backup notification)
        this.io.to('role-waiter').emit('order-status-update', notificationData);
        
        // 3. Special ready notifications for all waiters
        if (newStatus === 'ready') {
          this.io.to('role-waiter').emit('order-ready-notification', {
            ...notificationData,
            message: `🔔 طلب جاهز للتقديم - الطاولة ${tableNumber}`,
            sound: true
          });
        }

        // 4. Notify managers
        this.io.to('role-manager').emit('order-activity', {
          type: 'status-update',
          ...notificationData,
          message: `تحديث حالة الطلب: ${newStatus} - الطاولة ${tableNumber} بواسطة ${chefName || 'الطباخ'}`
        });

        // 5. Notify table-specific room
        if (tableNumber) {
          this.io.to(`table-${tableNumber}`).emit('table-order-update', {
            ...notificationData,
            message: `تحديث طلب الطاولة ${tableNumber}: ${newStatus}`
          });
        }

        // 6. General broadcast for dashboard updates
        this.io.emit('order-updated', {
          orderId,
          newStatus,
          tableNumber,
          timestamp: new Date().toISOString()
        });

        console.log(`✅ Enhanced order ${orderId} status update notifications sent to:`, {
          directWaiter: waiterName || updatedOrder?.staff?.waiter?.username,
          broadcastChannels: ['role-waiter', 'role-manager', `table-${tableNumber}`, 'general'],
          newStatus,
          priority: notificationData.priority
        });
        
      } catch (error) {
        console.error('❌ Error handling order status update:', error);
        
        // Fallback notification on error
        this.io.to('role-waiter').emit('order-status-update', {
          orderId: updateData.orderId,
          newStatus: updateData.newStatus,
          message: `تم تحديث حالة الطلب - قد تحتاج لتحديث الصفحة`,
          error: true,
          timestamp: new Date().toISOString()
        });
      }
    });

    // Handle table status changes
    socket.on('table-status-change', async (tableData) => {
      try {
        const { tableNumber, isOpen, waiterName, action } = tableData;
        
        console.log(`🪑 Table ${tableNumber} ${action} by ${waiterName}`);
        
        // Notify all waiters about table status change
        this.io.to('role-waiter').emit('table-status-updated', {
          tableNumber,
          isOpen,
          waiterName,
          action,
          message: action === 'opened' 
            ? `تم فتح الطاولة ${tableNumber} بواسطة ${waiterName}`
            : `تم إغلاق الطاولة ${tableNumber} بواسطة ${waiterName}`,
          timestamp: new Date().toISOString()
        });

        // Notify managers
        this.io.to('role-manager').emit('table-activity', {
          type: 'status-change',
          tableNumber,
          isOpen,
          waiterName,
          action,
          message: `${action === 'opened' ? 'فتح' : 'إغلاق'} الطاولة ${tableNumber}`,
          timestamp: new Date().toISOString()
        });

        console.log(`✅ Table ${tableNumber} status notifications sent`);
        
      } catch (error) {
        console.error('Error handling table status change:', error);
      }
    });

    // Handle table access conflicts
    socket.on('table-access-conflict', (conflictData) => {
      try {
        const { tableNumber, currentWaiter, attemptedWaiter } = conflictData;
        
        // Notify the waiter who tried to access the table
        socket.emit('table-access-denied', {
          tableNumber,
          currentWaiter,
          message: `الطاولة ${tableNumber} مفتوحة حالياً بواسطة ${currentWaiter}`,
          timestamp: new Date().toISOString()
        });

        console.log(`⚠️ Table access conflict: ${attemptedWaiter} tried to access table ${tableNumber} (used by ${currentWaiter})`);
        
      } catch (error) {
        console.error('Error handling table access conflict:', error);
      }
    });

    // Handle disconnection
    socket.on('disconnect', () => {
      // Find and remove user from connected users
      for (const [userId, userData] of this.connectedUsers.entries()) {
        if (userData.socketId === socket.id) {
          console.log(`👤 User disconnected: ${userData.name} (${userData.role})`);
          this.connectedUsers.delete(userId);
          break;
        }
      }
    });
  }

  // Helper method to send pending orders to newly connected chef
  async sendPendingOrdersToChef(socket) {
    try {
      const pendingOrders = await Order.find({ status: 'pending' })
        .populate('items.product')
        .sort({ createdAt: 1 });

      if (pendingOrders.length > 0) {
        socket.emit('pending-orders-list', {
          orders: pendingOrders,
          count: pendingOrders.length,
          message: `لديك ${pendingOrders.length} طلب في الانتظار`,
          timestamp: new Date().toISOString()
        });

        console.log(`📋 Sent ${pendingOrders.length} pending orders to chef`);
      }
    } catch (error) {
      console.error('Error sending pending orders:', error);
    }
  }

  // Broadcast urgent notifications to all users
  broadcastUrgentNotification(message, type = 'info') {
    this.io.emit('urgent-notification', {
      message,
      type,
      timestamp: new Date().toISOString()
    });
    console.log(`📢 Urgent notification broadcasted: ${message}`);
  }

  // Send notification to specific role
  sendRoleNotification(role, message, data = {}) {
    this.io.to(`role-${role}`).emit('role-notification', {
      message,
      ...data,
      timestamp: new Date().toISOString()
    });
    console.log(`📢 Notification sent to ${role}: ${message}`);
  }

  // Get connected users count by role
  getConnectedUsersByRole() {
    const counts = {
      waiter: 0,
      chef: 0,
      manager: 0,
      total: 0
    };

    for (const userData of this.connectedUsers.values()) {
      if (counts[userData.role] !== undefined) {
        counts[userData.role]++;
      }
      counts.total++;
    }

    return counts;
  }

  // Send low stock alerts
  async sendLowStockAlerts() {
    try {
      const Inventory = require('../models/Inventory');
      const lowStockItems = await Inventory.getLowStock();
      
      if (lowStockItems.length > 0) {
        // Notify managers about low stock items
        this.sendRoleNotification('manager', 
          `${lowStockItems.length} عنصر في المخزون المنخفض`, {
          type: 'low-stock-batch-alert',
          itemsCount: lowStockItems.length,
          items: lowStockItems.map(item => ({
            id: item._id,
            name: item.name,
            quantity: item.quantity,
            min: item.min
          })),
          timestamp: new Date().toISOString()
        });

        console.log(`⚠️ تم إرسال تحذير المخزون المنخفض: ${lowStockItems.length} عنصر`);
      }
    } catch (error) {
      console.error('Error sending low stock alerts:', error);
    }
  }

  // Send out of stock alerts
  async sendOutOfStockAlerts() {
    try {
      const Inventory = require('../models/Inventory');
      const outOfStockItems = await Inventory.getOutOfStock();
      
      if (outOfStockItems.length > 0) {
        // Notify managers about out of stock items
        this.sendRoleNotification('manager', 
          `${outOfStockItems.length} عنصر نفد من المخزون`, {
          type: 'out-of-stock-batch-alert',
          itemsCount: outOfStockItems.length,
          items: outOfStockItems.map(item => ({
            id: item._id,
            name: item.name,
            category: item.category
          })),
          timestamp: new Date().toISOString()
        });

        console.log(`🚫 تم إرسال تحذير نفاد المخزون: ${outOfStockItems.length} عنصر`);
      }
    } catch (error) {
      console.error('Error sending out of stock alerts:', error);
    }
  }

  // Check and send stock alerts (can be called periodically)
  async checkAndSendStockAlerts() {
    await Promise.all([
      this.sendLowStockAlerts(),
      this.sendOutOfStockAlerts()
    ]);
  }

  // Send system health notifications
  sendSystemHealthNotification(status, message) {
    this.io.emit('system-health', {
      status,
      message,
      timestamp: new Date().toISOString()
    });
    console.log(`🏥 System health notification: ${status} - ${message}`);
  }
}

module.exports = SocketHandlers;

const mongoose = require('mongoose');
require('dotenv').config();

async function verifyCorrectOrders() {
  try {
    console.log('🔌 Connecting to MongoDB Atlas...');
    await mongoose.connect(process.env.MONGODB_URI);
    console.log('✅ Connected successfully!');

    const ordersCollection = mongoose.connection.db.collection('orders');
    const usersCollection = mongoose.connection.db.collection('users');
    const tablesCollection = mongoose.connection.db.collection('tables');
    
    const totalOrders = await ordersCollection.countDocuments();
    console.log(`\n📦 Total Orders: ${totalOrders}`);

    // Get waiters for reference
    const waiters = await usersCollection.find({ role: 'waiter' }).toArray();
    const waiterMap = {};
    waiters.forEach(w => waiterMap[w._id.toString()] = w.name);

    // Verify orders by waiter (using correct field structure)
    console.log('\n👥 Orders by Waiter:');
    for (const waiter of waiters) {
      const waiterOrders = await ordersCollection.aggregate([
        { $match: { 'staff.waiter': waiter._id } },
        {
          $group: {
            _id: null,
            count: { $sum: 1 },
            totalValue: { $sum: '$totals.total' }
          }
        }
      ]).toArray();

      const count = waiterOrders[0]?.count || 0;
      const total = waiterOrders[0]?.totalValue || 0;
      console.log(`- ${waiter.name}: ${count} orders, Total: ${total.toFixed(2)} EGP`);
    }

    // Order status distribution
    console.log('\n📋 Orders by Status:');
    const statusStats = await ordersCollection.aggregate([
      {
        $group: {
          _id: '$status',
          count: { $sum: 1 },
          totalValue: { $sum: '$totals.total' }
        }
      },
      { $sort: { count: -1 } }
    ]).toArray();

    statusStats.forEach(stat => {
      console.log(`- ${stat._id}: ${stat.count} orders (${stat.totalValue.toFixed(2)} EGP)`);
    });

    // Sample orders verification
    console.log('\n📝 Sample Orders Verification:');
    const sampleOrders = await ordersCollection.aggregate([
      {
        $lookup: {
          from: 'users',
          localField: 'staff.waiter',
          foreignField: '_id',
          as: 'waiterInfo'
        }
      },
      {
        $lookup: {
          from: 'tables',
          localField: 'table',
          foreignField: '_id',
          as: 'tableInfo'
        }
      },
      { $limit: 5 }
    ]).toArray();

    sampleOrders.forEach((order, index) => {
      const waiterName = order.waiterInfo[0]?.name || 'Unknown';
      const tableNumber = order.tableInfo[0]?.number || 'Unknown';
      console.log(`${index + 1}. Order #${order.orderNumber} - ${waiterName} - Table ${tableNumber} - ${order.totals.total} EGP - ${order.status}`);
      console.log(`   Items: ${order.items.length} products, Total: ${order.totals.total} EGP`);
    });

    // Test the data for dashboard purposes
    console.log('\n📊 Dashboard Data Test:');
    
    // Daily sales (last 7 days)
    const dailySales = await ordersCollection.aggregate([
      {
        $match: {
          createdAt: { $gte: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000) }
        }
      },
      {
        $group: {
          _id: {
            $dateToString: { format: "%Y-%m-%d", date: "$createdAt" }
          },
          dailyOrders: { $sum: 1 },
          dailySales: { $sum: '$totals.total' }
        }
      },
      { $sort: { "_id": -1 } }
    ]).toArray();

    console.log('📈 Daily Sales (Last 7 days):');
    dailySales.forEach(day => {
      console.log(`- ${day._id}: ${day.dailyOrders} orders, ${day.dailySales.toFixed(2)} EGP`);
    });

    // Top products
    const topProducts = await ordersCollection.aggregate([
      { $unwind: '$items' },
      {
        $group: {
          _id: '$items.productName',
          totalQuantity: { $sum: '$items.quantity' },
          totalRevenue: { $sum: '$items.subtotal' }
        }
      },
      { $sort: { totalQuantity: -1 } },
      { $limit: 5 }
    ]).toArray();

    console.log('\n🏆 Top 5 Products:');
    topProducts.forEach(product => {
      console.log(`- ${product._id}: ${product.totalQuantity} units, ${product.totalRevenue.toFixed(2)} EGP`);
    });

    console.log('\n✅ Verification Complete - Data structure is correct!');

  } catch (error) {
    console.error('❌ Error:', error.message);
  } finally {
    await mongoose.connection.close();
    console.log('\n🔐 Database connection closed.');
  }
}

verifyCorrectOrders();

const mongoose = require('mongoose');
require('dotenv').config();

async function generateCorrectOrders() {
  try {
    console.log('🔌 Connecting to MongoDB Atlas...');
    await mongoose.connect(process.env.MONGODB_URI);
    console.log('✅ Connected successfully!');

    // Get collections
    const ordersCollection = mongoose.connection.db.collection('orders');
    const usersCollection = mongoose.connection.db.collection('users');
    const tablesCollection = mongoose.connection.db.collection('tables');
    const productsCollection = mongoose.connection.db.collection('products');

    // Get waiters, tables, and products
    const waiters = await usersCollection.find({ role: 'waiter' }).toArray();
    const tables = await tablesCollection.find({ isActive: true }).toArray();
    const products = await productsCollection.find().toArray(); // Remove isActive filter

    console.log(`\n📊 Found ${waiters.length} waiters, ${tables.length} tables, ${products.length} products`);

    if (waiters.length === 0 || tables.length === 0 || products.length === 0) {
      console.log('❌ Missing required data. Cannot generate orders.');
      return;
    }

    // Clear existing orders
    console.log('🧹 Clearing existing orders...');
    await ordersCollection.deleteMany({});
    console.log('✅ Orders cleared');

    // Order statuses
    const statuses = ['pending', 'in-progress', 'ready', 'completed', 'delivered'];

    // Generate orders for each waiter
    const orderRequests = [
      { waiterName: 'عزة', count: 57 },
      { waiterName: 'بوسي', count: 85 },
      { waiterName: 'سارة', count: 313 }
    ];

    let totalGenerated = 0;

    for (const request of orderRequests) {
      console.log(`\n📝 Generating ${request.count} orders for ${request.waiterName}...`);
      
      // Find the waiter
      const waiter = waiters.find(w => w.name === request.waiterName);
      if (!waiter) {
        console.log(`❌ Waiter ${request.waiterName} not found!`);
        continue;
      }

      // Generate orders for this waiter
      for (let i = 0; i < request.count; i++) {
        try {
          // Random table
          const randomTable = tables[Math.floor(Math.random() * tables.length)];
          
          // Random number of items (1-4)
          const itemCount = Math.floor(Math.random() * 4) + 1;
          const orderItems = [];
          let subtotal = 0;

          // Generate random items
          for (let j = 0; j < itemCount; j++) {
            const randomProduct = products[Math.floor(Math.random() * products.length)];
            const quantity = Math.floor(Math.random() * 3) + 1; // 1-3
            const itemSubtotal = randomProduct.price * quantity;
            
            orderItems.push({
              product: randomProduct._id,
              productName: randomProduct.name,
              quantity: quantity,
              price: randomProduct.price,
              subtotal: itemSubtotal,
              notes: "",
              specialRequests: "",
              modifications: []
            });
            
            subtotal += itemSubtotal;
          }

          // Random date in last 30 days
          const orderDate = new Date();
          orderDate.setDate(orderDate.getDate() - Math.floor(Math.random() * 30));

          // Generate order number
          const orderNumber = `ORD-${Date.now()}-${Math.floor(Math.random() * 1000)}-${i}`;

          // Create order with correct structure
          const order = {
            orderNumber: orderNumber,
            customer: {
              name: "عميل",
              phone: "",
              email: ""
            },
            items: orderItems,
            totals: {
              subtotal: subtotal,
              tax: 0,
              discount: 0,
              total: subtotal
            },
            status: statuses[Math.floor(Math.random() * statuses.length)],
            orderType: "dine-in",
            table: randomTable._id,
            delivery: {
              address: "",
              phone: "",
              notes: "",
              estimatedTime: null
            },
            payment: {
              method: "cash",
              status: "pending",
              amount: subtotal,
              change: 0
            },
            discountApplied: 0,
            discountStatus: "none",
            discountAmount: 0,
            timing: {
              ordered: orderDate,
              estimated: null,
              prepared: null,
              served: null
            },
            staff: {
              waiter: waiter._id,
              chef: null
            },
            priority: "normal",
            createdAt: orderDate,
            updatedAt: orderDate
          };

          // Insert order
          await ordersCollection.insertOne(order);
          totalGenerated++;

          // Progress indicator
          if ((i + 1) % 10 === 0) {
            console.log(`   Progress: ${i + 1}/${request.count} orders`);
          }

          // Small delay to ensure unique timestamps
          await new Promise(resolve => setTimeout(resolve, 10));

        } catch (error) {
          console.error(`❌ Error creating order ${i + 1} for ${request.waiterName}:`, error.message);
        }
      }
      
      console.log(`✅ Created ${request.count} orders for ${request.waiterName}`);
    }

    console.log(`\n🎉 Total orders generated: ${totalGenerated}`);

    // Verify the results
    console.log('\n📊 Verification:');
    for (const request of orderRequests) {
      const waiter = waiters.find(w => w.name === request.waiterName);
      if (waiter) {
        const count = await ordersCollection.countDocuments({ 'staff.waiter': waiter._id });
        const totalValue = await ordersCollection.aggregate([
          { $match: { 'staff.waiter': waiter._id } },
          { $group: { _id: null, total: { $sum: '$totals.total' } } }
        ]).toArray();
        
        console.log(`- ${request.waiterName}: ${count} orders, Total: ${totalValue[0]?.total || 0} EGP`);
      }
    }

  } catch (error) {
    console.error('❌ Error:', error.message);
  } finally {
    await mongoose.connection.close();
    console.log('\n🔐 Database connection closed.');
  }
}

generateCorrectOrders();

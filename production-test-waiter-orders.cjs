#!/usr/bin/env node

/**
 * DESHA COFFEE - Production Waiter Orders Test
 * اختبار طلبات النادل في البيئة الحية
 * 
 * This script tests the waiter dashboard orders display functionality
 * using the live Railway backend and MongoDB Atlas database.
 */

const https = require('https');

// Production configuration
const CONFIG = {
    API_BASE: 'https://deshacoffee-production.up.railway.app',
    FRONTEND_URL: 'https://desha-coffee-git-main-mediafutures-projects.vercel.app',
    TEST_WAITER_ID: '************************', // Test waiter ID
    TIMEOUT: 30000
};

/**
 * Make HTTP request to the API
 */
function makeRequest(url, options = {}) {
    return new Promise((resolve, reject) => {
        const requestOptions = {
            timeout: CONFIG.TIMEOUT,
            ...options
        };

        const req = https.request(url, requestOptions, (res) => {
            let data = '';
            
            res.on('data', (chunk) => {
                data += chunk;
            });
            
            res.on('end', () => {
                try {
                    const result = {
                        status: res.statusCode,
                        headers: res.headers,
                        data: res.headers['content-type']?.includes('application/json') 
                            ? JSON.parse(data) 
                            : data
                    };
                    resolve(result);
                } catch (error) {
                    resolve({
                        status: res.statusCode,
                        headers: res.headers,
                        data: data,
                        parseError: error.message
                    });
                }
            });
        });

        req.on('error', (error) => {
            reject(error);
        });

        req.on('timeout', () => {
            req.destroy();
            reject(new Error('Request timeout'));
        });

        req.setTimeout(CONFIG.TIMEOUT);
        req.end();
    });
}

/**
 * Test API health
 */
async function testApiHealth() {
    console.log('\n🔍 Testing API Health...');
    console.log('=' .repeat(50));
    
    try {
        const response = await makeRequest(`${CONFIG.API_BASE}/health`);
        
        console.log(`Status: ${response.status}`);
        console.log(`Response:`, JSON.stringify(response.data, null, 2));
        
        if (response.status === 200) {
            console.log('✅ API is healthy and responsive');
            return true;
        } else {
            console.log('❌ API health check failed');
            return false;
        }
    } catch (error) {
        console.log('❌ API health check error:', error.message);
        return false;
    }
}

/**
 * Test fetching all orders
 */
async function testFetchOrders() {
    console.log('\n📋 Testing Orders Fetch...');
    console.log('=' .repeat(50));
    
    try {
        const response = await makeRequest(`${CONFIG.API_BASE}/api/orders`);
        
        console.log(`Status: ${response.status}`);
        
        if (response.status === 200 && response.data) {
            const orders = Array.isArray(response.data) ? response.data : response.data.orders || [];
            console.log(`✅ Found ${orders.length} total orders`);
            
            // Analyze orders by waiter
            const ordersByWaiter = {};
            orders.forEach(order => {
                const waiterId = order.waiterId || 'unknown';
                const waiterName = order.waiterName || 'Unknown';
                
                if (!ordersByWaiter[waiterId]) {
                    ordersByWaiter[waiterId] = {
                        waiterId,
                        waiterName,
                        orders: [],
                        totalAmount: 0
                    };
                }
                
                ordersByWaiter[waiterId].orders.push(order);
                ordersByWaiter[waiterId].totalAmount += (order.total || 0);
            });
            
            console.log('\n📊 Orders by Waiter:');
            Object.values(ordersByWaiter).forEach(waiterData => {
                console.log(`  - ${waiterData.waiterName} (ID: ${waiterData.waiterId}): ${waiterData.orders.length} orders, Total: ${waiterData.totalAmount} EGP`);
            });
            
            return orders;
        } else {
            console.log('❌ Failed to fetch orders:', response.data);
            return [];
        }
    } catch (error) {
        console.log('❌ Orders fetch error:', error.message);
        return [];
    }
}

/**
 * Test fetching tables with accounts
 */
async function testFetchTables() {
    console.log('\n🏪 Testing Tables Fetch...');
    console.log('=' .repeat(50));
    
    try {
        const response = await makeRequest(`${CONFIG.API_BASE}/api/tables/accounts`);
        
        console.log(`Status: ${response.status}`);
        
        if (response.status === 200 && response.data) {
            const tables = Array.isArray(response.data) ? response.data : response.data.tables || [];
            console.log(`✅ Found ${tables.length} tables with accounts`);
            
            // Analyze tables
            tables.forEach((table, index) => {
                console.log(`\n  Table ${index + 1}:`);
                console.log(`    - Number: ${table.number}`);
                console.log(`    - Orders: ${table.orders?.length || 0}`);
                console.log(`    - Total: ${table.total || 0} EGP`);
                console.log(`    - Waiter: ${table.waiterName || 'N/A'} (ID: ${table.waiterId || 'N/A'})`);
                
                if (table.orders && table.orders.length > 0) {
                    console.log(`    - Order details:`);
                    table.orders.forEach(order => {
                        console.log(`      * Order ID: ${order._id}, Total: ${order.total || 0} EGP`);
                    });
                }
            });
            
            return tables;
        } else {
            console.log('❌ Failed to fetch tables:', response.data);
            return [];
        }
    } catch (error) {
        console.log('❌ Tables fetch error:', error.message);
        return [];
    }
}

/**
 * Test waiter specific filtering
 */
async function testWaiterFiltering(orders, tables) {
    console.log('\n👨‍💼 Testing Waiter Filtering Logic...');
    console.log('=' .repeat(50));
    
    // Get unique waiters from orders
    const waiters = {};
    orders.forEach(order => {
        const waiterId = order.waiterId;
        const waiterName = order.waiterName;
        
        if (waiterId && !waiters[waiterId]) {
            waiters[waiterId] = {
                id: waiterId,
                name: waiterName,
                orders: []
            };
        }
        
        if (waiterId) {
            waiters[waiterId].orders.push(order);
        }
    });
    
    console.log(`\n📋 Available Waiters (${Object.keys(waiters).length}):`);
    Object.values(waiters).forEach(waiter => {
        console.log(`  - ${waiter.name} (ID: ${waiter.id}): ${waiter.orders.length} orders`);
    });
    
    // Test filtering for each waiter
    console.log('\n🔍 Testing Table Filtering for Each Waiter:');
    Object.values(waiters).forEach(waiter => {
        console.log(`\n  Waiter: ${waiter.name} (ID: ${waiter.id})`);
        
        // Filter tables by waiterId first, then by waiterName
        const waiterTables = tables.filter(table => {
            const matchById = table.waiterId === waiter.id;
            const matchByName = table.waiterName === waiter.name;
            
            console.log(`    Table ${table.number}: waiterId match = ${matchById}, waiterName match = ${matchByName}`);
            
            return matchById || matchByName;
        });
        
        console.log(`    → Found ${waiterTables.length} tables for this waiter`);
        
        waiterTables.forEach(table => {
            console.log(`      * Table ${table.number}: ${table.orders?.length || 0} orders, ${table.total || 0} EGP`);
        });
    });
}

/**
 * Main test function
 */
async function runTests() {
    console.log('🚀 DESHA COFFEE - Production Waiter Orders Test');
    console.log('=' .repeat(60));
    console.log(`API Base: ${CONFIG.API_BASE}`);
    console.log(`Frontend: ${CONFIG.FRONTEND_URL}`);
    console.log(`Test Time: ${new Date().toLocaleString()}`);
    
    try {
        // Test API health
        const isHealthy = await testApiHealth();
        if (!isHealthy) {
            console.log('\n❌ API is not healthy. Stopping tests.');
            return;
        }
        
        // Test orders fetch
        const orders = await testFetchOrders();
        if (orders.length === 0) {
            console.log('\n⚠️ No orders found. Some tests will be limited.');
        }
        
        // Test tables fetch
        const tables = await testFetchTables();
        if (tables.length === 0) {
            console.log('\n⚠️ No tables found. Some tests will be limited.');
        }
        
        // Test waiter filtering logic
        if (orders.length > 0 && tables.length > 0) {
            await testWaiterFiltering(orders, tables);
        }
        
        console.log('\n✅ Production Test Completed Successfully!');
        console.log('\n📋 Summary:');
        console.log(`  - API Health: ${isHealthy ? 'Healthy' : 'Failed'}`);
        console.log(`  - Total Orders: ${orders.length}`);
        console.log(`  - Total Tables: ${tables.length}`);
        
        if (orders.length > 0 && tables.length > 0) {
            console.log('\n💡 Next Steps:');
            console.log('  1. Check WaiterDashboard.tsx filtering logic');
            console.log('  2. Verify waiterId matching in frontend');
            console.log('  3. Test live dashboard with different waiters');
        }
        
    } catch (error) {
        console.error('\n❌ Test failed with error:', error.message);
        console.error('Stack:', error.stack);
    }
}

// Run tests
runTests().catch(console.error);

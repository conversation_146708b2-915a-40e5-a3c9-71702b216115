#!/bin/bash

# Unified Coffee Shop Backend Deployment Script
# سكريبت نشر النظام الموحد لإدارة المقهى

echo "🚀 بدء تطبيق النظام الموحد..."
echo "🚀 Starting Unified Coffee Shop System..."

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${GREEN}✅ $1${NC}"
}

print_warning() {
    echo -e "${YELLOW}⚠️ $1${NC}"
}

print_error() {
    echo -e "${RED}❌ $1${NC}"
}

print_info() {
    echo -e "${BLUE}ℹ️ $1${NC}"
}

# Check if we're in the right directory
if [ ! -f "package.json" ]; then
    print_error "package.json not found. Please run this script from the backend directory."
    exit 1
fi

print_info "النظام الموحد لإدارة المقهى | Unified Coffee Shop Management System"
print_info "الإصدار 1.0.1 | Version 1.0.1"
echo

# Step 1: Install dependencies
print_info "تثبيت التبعيات المطلوبة | Installing required dependencies..."
npm install joi multer sharp winston node-cron --save

if [ $? -eq 0 ]; then
    print_status "تم تثبيت التبعيات بنجاح | Dependencies installed successfully"
else
    print_error "فشل في تثبيت التبعيات | Failed to install dependencies"
    exit 1
fi

# Step 2: Backup original files
print_info "إنشاء نسخ احتياطية | Creating backups..."

if [ -f "server.js" ]; then
    cp server.js server_original_backup.js
    print_status "تم إنشاء نسخة احتياطية من server.js | Backed up server.js"
fi

# Create backup directory for routes
mkdir -p routes_backup
if [ -d "routes" ]; then
    cp -r routes/* routes_backup/ 2>/dev/null || true
    print_status "تم إنشاء نسخة احتياطية من المسارات | Backed up routes"
fi

# Step 3: Replace server.js with unified version
print_info "تطبيق النظام الموحد | Applying unified system..."

if [ -f "server_unified.js" ]; then
    cp server.js server_legacy.js 2>/dev/null || true
    cp server_unified.js server.js
    print_status "تم تطبيق الخادم الموحد | Applied unified server"
else
    print_error "ملف server_unified.js غير موجود | server_unified.js not found"
    exit 1
fi

# Step 4: Update routes
print_info "تحديث المسارات | Updating routes..."

# Replace products route
if [ -f "routes/products_unified.js" ]; then
    cp routes/products.js routes/products_legacy.js 2>/dev/null || true
    cp routes/products_unified.js routes/products.js
    print_status "تم تحديث مسار المنتجات | Updated products route"
fi

# Replace orders route
if [ -f "routes/orders_unified.js" ]; then
    cp routes/orders.js routes/orders_legacy.js 2>/dev/null || true
    cp routes/orders_unified.js routes/orders.js
    print_status "تم تحديث مسار الطلبات | Updated orders route"
fi

# Replace categories route
if [ -f "routes/categories_unified.js" ]; then
    cp routes/categories.js routes/categories_legacy.js 2>/dev/null || true
    cp routes/categories_unified.js routes/categories.js
    print_status "تم تحديث مسار التصنيفات | Updated categories route"
fi

# Step 5: Test the unified system
print_info "اختبار النظام الموحد | Testing unified system..."

# Check if Node.js is available
if ! command -v node &> /dev/null; then
    print_error "Node.js غير مثبت | Node.js not installed"
    exit 1
fi

# Test syntax
node -c server.js
if [ $? -eq 0 ]; then
    print_status "تم التحقق من صحة الكود | Code syntax validated"
else
    print_error "خطأ في صيغة الكود | Code syntax error"
    exit 1
fi

# Step 6: Create startup scripts
print_info "إنشاء سكريبتات التشغيل | Creating startup scripts..."

# Create start script
cat > start_unified.sh << 'EOF'
#!/bin/bash
echo "🚀 تشغيل النظام الموحد | Starting Unified System..."
echo "📱 محسن للهاتف المحمول | Mobile Optimized"
echo "🌐 منافذ الوصول | Access Points:"
echo "   - Main: http://localhost:3001"
echo "   - Health: http://localhost:3001/health"
echo "   - API Docs: http://localhost:3001/api/docs"
echo "   - Products: http://localhost:3001/api/v1/products"
echo ""
node server.js
EOF

chmod +x start_unified.sh

# Create development script
cat > dev_unified.sh << 'EOF'
#!/bin/bash
echo "🛠️ تشغيل النظام في وضع التطوير | Starting in Development Mode..."
echo "📱 محسن للهاتف المحمول | Mobile Optimized"
echo "🔄 إعادة التشغيل التلقائي | Auto-restart enabled"
echo ""
if command -v nodemon &> /dev/null; then
    nodemon server.js
else
    echo "⚠️ nodemon غير مثبت، التشغيل العادي | nodemon not installed, using normal node"
    node server.js
fi
EOF

chmod +x dev_unified.sh

print_status "تم إنشاء سكريبتات التشغيل | Created startup scripts"

# Step 7: Final checks
print_info "الفحوصات النهائية | Final checks..."

# Check if all required files exist
required_files=(
    "server.js"
    "middleware/unifiedRouteHandler.js"
    "middleware/enhancedMobileCompatibility.js" 
    "middleware/unifiedAPIManager.js"
    "config/unifiedConfig.js"
    "routes/products.js"
    "routes/orders.js"
    "routes/categories.js"
)

all_files_exist=true
for file in "${required_files[@]}"; do
    if [ -f "$file" ]; then
        print_status "✓ $file موجود | exists"
    else
        print_error "✗ $file مفقود | missing"
        all_files_exist=false
    fi
done

if [ "$all_files_exist" = true ]; then
    print_status "جميع الملفات المطلوبة موجودة | All required files exist"
else
    print_error "بعض الملفات مفقودة | Some files are missing"
    exit 1
fi

# Step 8: Success message and instructions
echo
echo "🎉================================🎉"
print_status "تم تطبيق النظام الموحد بنجاح!"
print_status "Unified System Applied Successfully!"
echo "🎉================================🎉"
echo

print_info "طرق التشغيل | How to run:"
echo "  📱 للتطوير | Development:    ./dev_unified.sh"
echo "  🚀 للإنتاج | Production:     ./start_unified.sh"
echo "  📦 NPM:                      npm run dev:unified"
echo

print_info "نقاط الوصول | Access Points:"
echo "  🌐 الخادم الرئيسي | Main Server:     http://localhost:3001"
echo "  ❤️ فحص الصحة | Health Check:        http://localhost:3001/health"
echo "  📚 وثائق API | API Documentation:    http://localhost:3001/api/docs"
echo "  🛍️ المنتجات | Products API:         http://localhost:3001/api/v1/products"
echo "  📋 الطلبات | Orders API:            http://localhost:3001/api/v1/orders"
echo

print_info "الميزات الجديدة | New Features:"
echo "  📱 محسن للهاتف المحمول | Mobile Optimized"
echo "  🔄 نظام API موحد | Unified API System"  
echo "  🛡️ معالجة أخطاء محسنة | Enhanced Error Handling"
echo "  📊 تتبع الطلبات | Request Tracking"
echo "  🌍 دعم متعدد الأجهزة | Multi-device Support"
echo

print_warning "ملاحظات مهمة | Important Notes:"
echo "  - تم إنشاء نسخ احتياطية من الملفات الأصلية"
echo "  - Original files backed up with _legacy suffix"
echo "  - يرجى اختبار النظام قبل النشر في الإنتاج"
echo "  - Please test the system before production deployment"
echo

print_info "للحصول على المساعدة | For help:"
echo "  📖 راجع ملف UNIFIED_BACKEND_IMPLEMENTATION_GUIDE.md"
echo "  📖 Check UNIFIED_BACKEND_IMPLEMENTATION_GUIDE.md"
echo

print_status "النظام جاهز للتشغيل! | System ready to run!"
echo "🚀 ./dev_unified.sh للبدء | Run ./dev_unified.sh to start"

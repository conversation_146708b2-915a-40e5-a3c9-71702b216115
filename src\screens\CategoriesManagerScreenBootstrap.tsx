import React, { useEffect } from 'react';
import ResponsiveGrid from '../components/ResponsiveGrid';
import ResponsiveCard from '../components/ResponsiveCard';
import socket from '../socket';
import '../styles/screens/CategoriesScreenIsolated.css';

interface Category {
  _id: string;
  name: string;
  description?: string;
  color: string;
  icon?: string;
  isActive: boolean;
}

interface MenuItem {
  _id: string;
  category?: string | Category | { _id: string; name: string; };
  categories?: string[];
}

interface CategoriesManagerScreenBootstrapProps {
  categories: Category[];
  menuItems: MenuItem[];
  selectedCategory: Category | null;
  showCategoryModal: boolean;
  setSelectedCategory: (category: Category | null) => void;
  setShowCategoryModal: (show: boolean) => void;
  deleteCategory: (categoryId: string) => void;
  toggleCategoryStatus: (categoryId: string, currentStatus: boolean) => void;
}

const CategoriesManagerScreenBootstrap: React.FC<CategoriesManagerScreenBootstrapProps> = ({
  categories,
  menuItems,
  selectedCategory,
  showCategoryModal,
  setSelectedCategory,
  setShowCategoryModal,
  deleteCategory,
  toggleCategoryStatus
}) => {
  
  // Apply dynamic colors using useEffect
  useEffect(() => {
    const categoryHeaders = document.querySelectorAll('.categories-dynamic-header[data-color]');
    categoryHeaders.forEach((element) => {
      const color = element.getAttribute('data-color');
      if (color && element instanceof HTMLElement) {
        element.style.backgroundColor = color;
      }
    });

    const colorCodes = document.querySelectorAll('.categories-color-indicator[data-color]');
    colorCodes.forEach((element) => {
      const color = element.getAttribute('data-color');
      if (color && element instanceof HTMLElement) {
        element.style.backgroundColor = color;
      }
    });
  }, [categories]);

  // Socket.IO event listeners for real-time updates
  useEffect(() => {
    const handleCategoryUpdate = () => {
      console.log('📡 Real-time category update received');
      // Trigger parent component to refresh categories
      window.location.reload();
    };

    const handleMenuUpdate = () => {
      console.log('📡 Real-time menu update received');
      // Trigger parent component to refresh data
      window.location.reload();
    };

    // Add event listeners
    socket.on('categoryAdded', handleCategoryUpdate);
    socket.on('categoryUpdated', handleCategoryUpdate);
    socket.on('categoryDeleted', handleCategoryUpdate);
    socket.on('menuItemAdded', handleMenuUpdate);
    socket.on('menuItemUpdated', handleMenuUpdate);
    socket.on('menuItemDeleted', handleMenuUpdate);

    // Cleanup function
    return () => {
      socket.off('categoryAdded', handleCategoryUpdate);
      socket.off('categoryUpdated', handleCategoryUpdate);
      socket.off('categoryDeleted', handleCategoryUpdate);
      socket.off('menuItemAdded', handleMenuUpdate);
      socket.off('menuItemUpdated', handleMenuUpdate);
      socket.off('menuItemDeleted', handleMenuUpdate);
    };
  }, []);

  // Stats data
  const activeCategories = categories.filter(cat => cat.isActive !== false);
  const inactiveCategories = categories.filter(cat => cat.isActive === false);
  
  const statsCards = [
    {
      title: 'إجمالي الفئات',
      value: categories.length,
      icon: 'fas fa-tags',
      color: 'primary'
    },
    {
      title: 'الفئات النشطة',
      value: activeCategories.length,
      icon: 'fas fa-check-circle',
      color: 'success'
    },
    {
      title: 'الفئات غير النشطة',
      value: inactiveCategories.length,
      icon: 'fas fa-times-circle',
      color: 'warning'
    },
    {
      title: 'إجمالي المنتجات',
      value: menuItems.length,
      icon: 'fas fa-coffee',
      color: 'info'
    }
  ];

  return (
    <div className="categories-bootstrap-container container-fluid py-4">
      {/* Header */}
      <div className="text-center mb-4">
        <div className="d-flex justify-content-between align-items-center flex-wrap gap-3">
          <div className="text-start">
            <h1 className="display-5 text-white mb-1">
              <i className="fas fa-tags me-3"></i>
              إدارة الفئات
            </h1>
            <p className="text-white-50 mb-0">إدارة وتنظيم فئات المنتجات</p>
          </div>
          <button 
            className="btn btn-light btn-lg categories-add-btn"
            onClick={() => {
              setSelectedCategory(null);
              setShowCategoryModal(true);
            }}
            title="إضافة فئة جديدة"
          >
            <i className="fas fa-plus me-2"></i>
            إضافة فئة جديدة
          </button>
        </div>
      </div>

      {/* Statistics */}
      <div className="mb-4">
        <ResponsiveGrid
          cols={{ xs: 1, sm: 2, md: 2, lg: 2, xl: 2, xxl: 2 }}
          gap={3}
        >
          {statsCards.map((stat, index) => (
            <ResponsiveCard key={index} className="categories-stat-card">
              <div className="card-body d-flex align-items-center">
                <div className={`categories-stat-icon me-3 text-${stat.color}`}>
                  <i className={stat.icon}></i>
                </div>
                <div>
                  <h3 className="h2 mb-1 fw-bold text-dark">{stat.value}</h3>
                  <p className="mb-0 text-muted fw-medium">{stat.title}</p>
                </div>
              </div>
            </ResponsiveCard>
          ))}
        </ResponsiveGrid>
      </div>

      {/* Warning for no active categories */}
      {categories.length > 0 && activeCategories.length === 0 && (
        <div className="alert alert-warning mb-4">
          <div className="d-flex align-items-center">
            <i className="fas fa-exclamation-triangle me-3 text-warning fs-4"></i>
            <div>
              <h6 className="alert-heading mb-1">تحذير: لا توجد فئات نشطة!</h6>
              <p className="mb-0">
                جميع الفئات غير مفعلة حالياً. لن تظهر الفئات في قوائم إضافة/تعديل المنتجات حتى يتم تفعيلها.
                <strong> يرجى تفعيل فئة واحدة على الأقل باستخدام زر "تفعيل" في كارت الفئة أدناه.</strong>
              </p>
            </div>
          </div>
        </div>
      )}

      {/* Categories Grid */}
      <div className="mb-4">
        {categories.length === 0 ? (
          <div className="text-center py-5">
            <ResponsiveCard className="categories-empty-card mx-auto">
              <div className="card-body text-center py-5">
                <div className="mb-4">
                  <i className="fas fa-tags display-1 text-muted"></i>
                </div>
                <h3 className="card-title text-dark">لا توجد فئات</h3>
                <p className="card-text text-muted mb-3">ابدأ بإضافة فئة جديدة لتنظيم منتجاتك</p>
                <button 
                  className="btn btn-primary btn-lg"
                  onClick={() => {
                    setSelectedCategory(null);
                    setShowCategoryModal(true);
                  }}
                >
                  <i className="fas fa-plus me-2"></i>
                  إضافة أول فئة
                </button>
              </div>
            </ResponsiveCard>
          </div>
        ) : (
          <ResponsiveGrid
            cols={{ xs: 1, sm: 2, md: 2, lg: 3, xl: 4, xxl: 4 }}
            gap={3}
          >
            {categories.map(category => {
              const categoryItems = menuItems.filter(item =>
                item.category === category._id || 
                (typeof item.category === 'object' && item.category?._id === category._id) ||
                item.categories?.includes(category._id)
              );

              return (
                <ResponsiveCard key={category._id} className="categories-item-card">
                  {/* Dynamic colored header */}
                  <div 
                    className="categories-dynamic-header text-white p-3" 
                    data-color={category.color}
                  >
                    <div className="d-flex align-items-center justify-content-center text-center">
                      {category.icon && (
                        <i className={`${category.icon} me-2 categories-header-icon`}></i>
                      )}
                      <h5 className="mb-0 fw-bold">{category.name}</h5>
                    </div>
                  </div>

                  <div className="card-body">
                    {/* Description */}
                    {category.description && (
                      <p className="card-text text-muted mb-3">{category.description}</p>
                    )}

                    {/* Category Info */}
                    <div className="d-flex justify-content-between align-items-center mb-3">
                      <div className="d-flex align-items-center">
                        <i className="fas fa-cube text-info me-2"></i>
                        <span className="fw-medium text-dark">{categoryItems.length} عنصر</span>
                      </div>
                      <div className="d-flex align-items-center">
                        <span className="me-2 small text-muted">اللون:</span>
                        <div 
                          className="categories-color-indicator rounded-circle"
                          data-color={category.color}
                          title={category.color}
                        ></div>
                        <small className="text-muted ms-2">{category.color}</small>
                      </div>
                    </div>

                    {/* Active Status */}
                    <div className="d-flex justify-content-between align-items-center mb-3">
                      <span className="small text-muted">حالة الفئة:</span>
                      <span className={`badge ${(category.isActive !== false) ? 'bg-success' : 'bg-warning'}`}>
                        <i className={`fas ${(category.isActive !== false) ? 'fa-check' : 'fa-times'} me-1`}></i>
                        {(category.isActive !== false) ? 'نشطة' : 'غير نشطة'}
                      </span>
                    </div>

                    {/* Actions */}
                    <div className="d-flex gap-2 mb-2">
                      <button
                        className="btn btn-outline-primary btn-sm flex-fill categories-edit-btn"
                        onClick={() => {
                          setSelectedCategory(category);
                          setShowCategoryModal(true);
                        }}
                        title="تعديل الفئة"
                      >
                        <i className="fas fa-edit me-1"></i>
                        تعديل
                      </button>
                      
                      <button
                        className={`btn btn-sm flex-fill ${
                          (category.isActive !== false) ? 'btn-outline-warning' : 'btn-outline-success'
                        }`}
                        onClick={() => toggleCategoryStatus(category._id, category.isActive !== false)}
                        title={(category.isActive !== false) ? 'إلغاء تفعيل الفئة' : 'تفعيل الفئة'}
                      >
                        <i className={`fas ${(category.isActive !== false) ? 'fa-eye-slash' : 'fa-eye'} me-1`}></i>
                        {(category.isActive !== false) ? 'إلغاء تفعيل' : 'تفعيل'}
                      </button>
                    </div>

                    <div className="d-flex gap-2">
                      <button
                        className={`btn btn-sm flex-fill categories-delete-btn ${
                          categoryItems.length > 0 ? 'btn-outline-secondary' : 'btn-outline-danger'
                        }`}
                        onClick={() => deleteCategory(category._id)}
                        title={categoryItems.length > 0 ? 'لا يمكن حذف فئة تحتوي على منتجات' : 'حذف الفئة'}
                        disabled={categoryItems.length > 0}
                      >
                        <i className="fas fa-trash me-1"></i>
                        حذف
                      </button>
                    </div>

                    {/* Warning for categories with items */}
                    {categoryItems.length > 0 && (
                      <div className="alert alert-warning mt-3 py-2 px-3 mb-0">
                        <small>
                          <i className="fas fa-exclamation-triangle me-1"></i>
                          يجب حذف جميع المنتجات من هذه الفئة أولاً
                        </small>
                      </div>
                    )}
                  </div>
                </ResponsiveCard>
              );
            })}
          </ResponsiveGrid>
        )}
      </div>
    </div>
  );
};

export default CategoriesManagerScreenBootstrap;

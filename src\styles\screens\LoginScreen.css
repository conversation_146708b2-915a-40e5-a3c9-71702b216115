/* ============================== */
/* LoginScreen - Login Page Styles */
/* ============================== */

/* CSS Variables for LoginScreen */
/* ?? ????? ????????? ??????? ?????? ??? ????? */

/* ================ */
/* Login Container */
/* ================ */

.loginScreen {
  min-height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, rgba(121, 85, 72, 0.1), rgba(161, 136, 127, 0.1));
  direction: rtl;
  position: relative;
  padding: 1rem;
}

.loginScreen__theme-toggle-container {
  position: absolute;
  top: 20px;
  left: 20px;
  z-index: 10;
}

.loginScreen__wrapper {
  display: flex;
  flex-direction: column;
  align-items: center;
  max-width: 400px;
  width: 100%;
}

/* ================ */
/* Logo Section */
/* ================ */

.loginScreen__logo {
  width: 120px;
  height: 120px;
  margin-bottom: 2rem;
  filter: drop-shadow(0 4px 8px rgba(0, 0, 0, 0.1));
  animation: loginScreen-float 3s ease-in-out infinite;
}

@keyframes loginScreen-float {
  0% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-10px);
  }
  100% {
    transform: translateY(0px);
  }
}

/* ================ */
/* Login Card */
/* ================ */

.loginScreen__card {
  background: #ffffff;
  padding: 2rem;
  border-radius: 12px;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
  width: 100%;
  position: relative;
  overflow: hidden;
}

.loginScreen__card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: linear-gradient(90deg, #3498db, #27ae60);
}

/* ================ */
/* Form Header */
/* ================ */

.loginScreen__header {
  text-align: center;
  margin-bottom: 2rem;
}

.loginScreen__title {
  color: #2c3e50;
  font-size: 1.8rem;
  font-weight: 700;
  margin: 0 0 0.5rem 0;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
}

.loginScreen__title-icon {
  color: #3498db;
  font-size: 1.5rem;
}

.loginScreen__subtitle {
  color: #7f8c8d;
  font-size: 1rem;
  margin: 0;
  font-weight: 400;
}

/* ================ */
/* Form Elements */
/* ================ */

.loginScreen__form {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.loginScreen__form-group {
  position: relative;
}

.loginScreen__form-label {
  display: block;
  color: #2c3e50;
  font-size: 0.9rem;
  font-weight: 600;
  margin-bottom: 0.5rem;
  text-align: right;
}

.loginScreen__form-input {
  width: 100%;
  padding: 1rem;
  border: 2px solid #e1e8ed;
  border-radius: 8px;
  font-size: 1rem;
  font-family: inherit;
  background: #ffffff;
  color: #2c3e50;
  transition: all 0.3s ease;
  text-align: right;
  direction: rtl;
}

.loginScreen__form-input:focus {
  outline: none;
  border-color: #3498db;
  box-shadow: 0 0 0 3px rgba(52, 152, 219, 0.1);
}

.loginScreen__form-input:focus + .loginScreen__form-label {
  color: #3498db;
}

.loginScreen__form-input--error {
  border-color: #e74c3c;
}

.loginScreen__form-input--error:focus {
  border-color: #e74c3c;
  box-shadow: 0 0 0 3px rgba(231, 76, 60, 0.1);
}

/* ================ */
/* Password Input */
/* ================ */

.loginScreen__password-wrapper {
  position: relative;
}

.loginScreen__password-toggle {
  position: absolute;
  left: 1rem;
  top: 50%;
  transform: translateY(-50%);
  background: none;
  border: none;
  color: #7f8c8d;
  cursor: pointer;
  font-size: 1rem;
  transition: all 0.3s ease;
  padding: 0.5rem;
  border-radius: 4px;
}

.loginScreen__password-toggle:hover {
  color: #3498db;
  background: rgba(52, 152, 219, 0.1);
}

/* ================ */
/* Submit Button */
/* ================ */

.loginScreen__submit-btn {
  background: linear-gradient(135deg, #3498db, #2980b9);
  color: #ffffff;
  border: none;
  padding: 1rem 2rem;
  border-radius: 8px;
  font-size: 1rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
  margin-top: 1rem;
}

.loginScreen__submit-btn:hover:not(:disabled) {
  background: linear-gradient(135deg, #2980b9, #1f4e79);
  transform: translateY(-2px);
  box-shadow: 0 4px 15px rgba(52, 152, 219, 0.3);
}

.loginScreen__submit-btn:active {
  transform: translateY(0);
}

.loginScreen__submit-btn:disabled {
  background: #95a5a6;
  cursor: not-allowed;
  transform: none;
  box-shadow: none;
}

.loginScreen__submit-btn--loading {
  opacity: 0.8;
}

.loginScreen__submit-btn-icon {
  font-size: 1rem;
}

.loginScreen__submit-btn-text {
  font-size: 1rem;
}

/* ================ */
/* Loading Spinner */
/* ================ */

.loginScreen__loading-spinner {
  width: 20px;
  height: 20px;
  border: 2px solid rgba(255, 255, 255, 0.3);
  border-top: 2px solid #ffffff;
  border-radius: 50%;
  animation: loginScreen-spin 1s linear infinite;
}

@keyframes loginScreen-spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* ================ */
/* Error Messages */
/* ================ */

.loginScreen__error-message {
  background: rgba(244, 67, 54, 0.1);
  color: #e74c3c;
  border: 1px solid rgba(244, 67, 54, 0.2);
  padding: 1rem;
  border-radius: 8px;
  text-align: center;
  font-size: 0.9rem;
  font-weight: 500;
  margin-bottom: 1rem;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
}

.loginScreen__error-icon {
  font-size: 1rem;
}

/* ================ */
/* Success Messages */
/* ================ */

.loginScreen__success-message {
  background: rgba(76, 175, 80, 0.1);
  color: #27ae60;
  border: 1px solid rgba(76, 175, 80, 0.2);
  padding: 1rem;
  border-radius: 8px;
  text-align: center;
  font-size: 0.9rem;
  font-weight: 500;
  margin-bottom: 1rem;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
}

.loginScreen__success-icon {
  font-size: 1rem;
}

/* ================ */
/* Footer Links */
/* ================ */

.loginScreen__footer {
  text-align: center;
  margin-top: 2rem;
  padding-top: 1rem;
  border-top: 1px solid #e1e8ed;
}

.loginScreen__footer-link {
  color: #3498db;
  text-decoration: none;
  font-size: 0.9rem;
  font-weight: 500;
  transition: all 0.3s ease;
}

.loginScreen__footer-link:hover {
  color: #2980b9;
  text-decoration: underline;
}

/* ================ */
/* Remember Me Checkbox */
/* ================ */

.loginScreen__remember-wrapper {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin: 1rem 0;
}

.loginScreen__checkbox-wrapper {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  cursor: pointer;
}

.loginScreen__checkbox {
  width: 18px;
  height: 18px;
  margin: 0;
  cursor: pointer;
}

.loginScreen__checkbox-label {
  color: #2c3e50;
  font-size: 0.9rem;
  cursor: pointer;
  -webkit-user-select: none;
  -moz-user-select: none;
  user-select: none;
}

.loginScreen__forgot-password {
  color: #3498db;
  text-decoration: none;
  font-size: 0.9rem;
  font-weight: 500;
  transition: all 0.3s ease;
}

.loginScreen__forgot-password:hover {
  color: #2980b9;
  text-decoration: underline;
}

/* ================ */
/* Dark Mode Support */
/* ================ */

@media (prefers-color-scheme: dark) {
  .loginScreen {
    background: linear-gradient(135deg, rgba(30, 30, 30, 0.9), rgba(50, 50, 50, 0.9));
  }
  
  .loginScreen__card {
    background: #1a1a1a;
    color: #ffffff;
  }
  
  .loginScreen__title,
  .loginScreen__form-label,
  .loginScreen__checkbox-label {
    color: #ffffff;
  }
  
  .loginScreen__subtitle {
    color: #b0b0b0;
  }
  
  .loginScreen__form-input {
    background: #2a2a2a;
    border-color: #444;
    color: #ffffff;
  }
  
  .loginScreen__form-input:focus {
    border-color: #3498db;
  }
}

/* ================ */
/* Responsive Design */
/* ================ */

@media (max-width: 768px) {
  .loginScreen {
    padding: 0.5rem;
  }
  
  .loginScreen__wrapper {
    max-width: 100%;
  }
  
  .loginScreen__card {
    padding: 1rem;
  }
  
  .loginScreen__logo {
    width: 100px;
    height: 100px;
    margin-bottom: 1rem;
  }
  
  .loginScreen__title {
    font-size: 1.5rem;
  }
  
  .loginScreen__form-input,
  .loginScreen__submit-btn {
    padding: 0.875rem;
  }
}

@media (max-width: 480px) {
  .loginScreen__theme-toggle-container {
    top: 10px;
    left: 10px;
  }
  
  .loginScreen__card {
    margin: 0.5rem;
  }
  
  .loginScreen__title {
    font-size: 1.3rem;
    flex-direction: column;
    gap: 0.5rem;
  }
  
  .loginScreen__remember-wrapper {
    flex-direction: column;
    gap: 0.5rem;
    align-items: stretch;
  }
  
  .loginScreen__checkbox-wrapper {
    justify-content: center;
  }
  
  .loginScreen__forgot-password {
    text-align: center;
  }
}

/* ================ */
/* Accessibility */
/* ================ */

.loginScreen__form-input:focus-visible,
.loginScreen__submit-btn:focus-visible,
.loginScreen__password-toggle:focus-visible {
  outline: 2px solid #3498db;
  outline-offset: 2px;
}

.loginScreen__checkbox:focus-visible {
  outline: 2px solid #3498db;
  outline-offset: 1px;
}

/* ================ */
/* Animations */
/* ================ */

.loginScreen__card {
  animation: loginScreen-slideUp 0.5s ease-out;
}

@keyframes loginScreen-slideUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.loginScreen__error-message,
.loginScreen__success-message {
  animation: loginScreen-fadeIn 0.3s ease-out;
}

@keyframes loginScreen-fadeIn {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* ================ */
/* Form Elements - Alternative Classes */
/* ================ */

/* تنسيقات للـ classes المستخدمة فعلياً في HTML */
.loginScreen__input {
  width: 100%;
  padding: 1rem;
  border: 2px solid #e1e8ed;
  border-radius: 8px;
  font-size: 1rem;
  font-family: inherit;
  background: #ffffff;
  color: #2c3e50;
  transition: all 0.3s ease;
  text-align: right;
  direction: rtl;
  margin-top: 0.5rem;
}

.loginScreen__input:focus {
  outline: none;
  border-color: #3498db;
  box-shadow: 0 0 0 3px rgba(52, 152, 219, 0.1);
}

.loginScreen__input-icon {
  color: #3498db;
  margin-left: 0.5rem;
  font-size: 1.1rem;
}

/* تنسيقات الـ form */
.loginScreen__form {
  background: #ffffff;
  padding: 2rem;
  border-radius: 12px;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
  width: 100%;
  max-width: 400px;
}

.loginScreen__form h2 {
  text-align: center;
  color: #2c3e50;
  margin-bottom: 2rem;
  font-size: 1.8rem;
  font-weight: 700;
}

.loginScreen__form label {
  display: block;
  margin-bottom: 1.5rem;
  color: #2c3e50;
  font-weight: 600;
  text-align: right;
}

/* Button Styles */
.loginScreen__button {
  width: 100%;
  padding: 1rem;
  background: #3498db;
  color: white;
  border: none;
  border-radius: 8px;
  font-size: 1rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  margin-top: 1rem;
}

.loginScreen__button:hover:not(:disabled) {
  background: #2980b9;
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(52, 152, 219, 0.3);
}

.loginScreen__button:disabled {
  background: #bdc3c7;
  cursor: not-allowed;
  transform: none;
}

/* Help Section */
.loginScreen__help {
  text-align: center;
  margin-top: 2rem;
  padding: 1rem;
  background: #f8f9fa;
  border-radius: 8px;
  font-size: 0.9rem;
  color: #2c3e50;
}


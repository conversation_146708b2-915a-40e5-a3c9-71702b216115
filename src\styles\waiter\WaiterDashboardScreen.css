/* ============================== */
/* WaiterDashboardScreen - Waiter Interface */
/* ============================== */

/* استيراد المتغيرات المميزة للوحة الرئيسية */
@import '../variables/home-variables.css';


/* ================ */
/* Main Container */
/* ================ */

.waiterDashboardScreen {
  display: flex;
  min-height: 100vh;
  background: var(--home-gray-100);
  direction: rtl;
  font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
  position: relative;
}

/* ================ */
/* Mobile Menu Toggle */
/* ================ */

.waiterDashboardScreen__mobile-menu-toggle {
  position: fixed;
  top: 1rem;
  right: 1rem;
  z-index: 1000;
  background: var(--home-primary-color);
  color: var(--home-white);
  border: none;
  padding: 1rem;
  border-radius: var(--home-border-radius);
  font-size: 1.2rem;
  cursor: pointer;
  box-shadow: var(--home-box-shadow-lg);
  transition: var(--home-transition);
  display: none;
}

.waiterDashboardScreen__mobile-menu-toggle:hover {
  background: var(--home-primary-light);
  transform: scale(1.05);
}

/* ================ */
/* Sidebar */
/* ================ */

.waiterDashboardScreen__sidebar {
  width: 280px;
  background: linear-gradient(180deg, #ffffff 0%, #f8f9fa 100%);
  box-shadow: -4px 0 20px rgba(0, 0, 0, 0.15);
  height: 100vh;
  position: fixed;
  right: 0;
  top: 0;
  z-index: 999;
  display: flex;
  flex-direction: column;
  transition: all 0.4s cubic-bezier(0.25, 0.8, 0.25, 1);
  overflow-y: auto;
  border-left: 1px solid rgba(0, 0, 0, 0.05);
}

.waiterDashboardScreen__sidebar::-webkit-scrollbar {
  width: 6px;
}

.waiterDashboardScreen__sidebar::-webkit-scrollbar-track {
  background: rgba(0, 0, 0, 0.05);
}

.waiterDashboardScreen__sidebar::-webkit-scrollbar-thumb {
  background: rgba(52, 152, 219, 0.3);
  border-radius: 3px;
}

.waiterDashboardScreen__sidebar::-webkit-scrollbar-thumb:hover {
  background: rgba(52, 152, 219, 0.5);
}

.waiterDashboardScreen__sidebar--hidden {
  transform: translateX(100%);
}

.waiterDashboardScreen__sidebar--visible {
  transform: translateX(0);
}

.waiterDashboardScreen__sidebar-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.6);
  z-index: 998;
  opacity: 0;
  visibility: hidden;
  transition: all 0.3s cubic-bezier(0.25, 0.8, 0.25, 1);
  backdrop-filter: blur(3px);
  -webkit-backdrop-filter: blur(3px);
}

.waiterDashboardScreen__sidebar-overlay--active {
  opacity: 1;
  visibility: visible;
}

/* ================ */
/* Sidebar Header */
/* ================ */

.waiterDashboardScreen__sidebar-header {
  padding: 1.5rem;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
  background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
  color: var(--home-white);
  display: flex;
  align-items: center;
  justify-content: space-between;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  position: relative;
  overflow: hidden;
}

.waiterDashboardScreen__sidebar-header::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(45deg, rgba(255, 255, 255, 0.1) 0%, transparent 50%);
  pointer-events: none;
}

.waiterDashboardScreen__sidebar-logo {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  font-size: 1.4rem;
  font-weight: 700;
  position: relative;
  z-index: 1;
  text-shadow: 0 1px 3px rgba(0, 0, 0, 0.3);
}

.waiterDashboardScreen__sidebar-logo-icon {
  font-size: 1.8rem;
  color: #f39c12;
  filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.3));
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0%, 100% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.05);
  }
}

.waiterDashboardScreen__sidebar-close-btn {
  background: rgba(255, 255, 255, 0.1);
  border: 2px solid rgba(255, 255, 255, 0.2);
  color: var(--home-white);
  font-size: 1.2rem;
  cursor: pointer;
  padding: 0.6rem;
  border-radius: 50%;
  transition: all 0.3s cubic-bezier(0.25, 0.8, 0.25, 1);
  display: none;
  position: relative;
  z-index: 2;
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.waiterDashboardScreen__sidebar-close-btn:hover {
  background: rgba(231, 76, 60, 0.8);
  border-color: #e74c3c;
  transform: rotate(90deg) scale(1.1);
  box-shadow: 0 4px 15px rgba(231, 76, 60, 0.3);
}

/* ================ */
/* Sidebar Navigation */
/* ================ */

.waiterDashboardScreen__sidebar-nav {
  flex: 1;
  padding: 1.5rem 0;
  background: rgba(255, 255, 255, 0.5);
  backdrop-filter: blur(10px);
  margin: 0.5rem;
  border-radius: 15px;
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.waiterDashboardScreen__nav-menu {
  list-style: none;
  margin: 0;
  padding: 0;
}

.waiterDashboardScreen__nav-item {
  margin: 0;
}

.waiterDashboardScreen__nav-link {
  display: flex;
  align-items: center;
  gap: 1rem;
  padding: 1rem 1.5rem;
  color: #5a6c7d;
  text-decoration: none;
  transition: all 0.3s cubic-bezier(0.25, 0.8, 0.25, 1);
  border: none;
  background: rgba(255, 255, 255, 0.05);
  width: 100%;
  text-align: right;
  cursor: pointer;
  font-size: 1rem;
  border-right: 4px solid transparent;
  margin: 0.25rem 0.5rem;
  border-radius: 12px;
  position: relative;
  overflow: hidden;
}

.waiterDashboardScreen__nav-link::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.1), transparent);
  transition: left 0.5s;
}

.waiterDashboardScreen__nav-link:hover::before {
  left: 100%;
}

.waiterDashboardScreen__nav-link:hover {
  background: rgba(255, 255, 255, 0.15);
  color: #3498db;
  transform: translateX(-5px);
  box-shadow: 0 4px 15px rgba(52, 152, 219, 0.2);
}

.waiterDashboardScreen__nav-link--active {
  background: linear-gradient(135deg, #3498db 0%, #2980b9 100%);
  color: var(--home-white);
  border-right-color: #2980b9;
  box-shadow: 0 4px 15px rgba(52, 152, 219, 0.3);
  transform: translateX(-5px);
}

.waiterDashboardScreen__nav-icon {
  font-size: 1.3rem;
  width: 24px;
  text-align: center;
  transition: all 0.3s ease;
  filter: drop-shadow(0 1px 2px rgba(0, 0, 0, 0.1));
}

.waiterDashboardScreen__nav-link:hover .waiterDashboardScreen__nav-icon {
  transform: scale(1.1);
  color: #3498db;
}

.waiterDashboardScreen__nav-link--active .waiterDashboardScreen__nav-icon {
  color: #ffffff;
  transform: scale(1.05);
}

.waiterDashboardScreen__nav-text {
  font-weight: 600;
  font-size: 0.95rem;
  letter-spacing: 0.3px;
}

/* ================ */
/* Sidebar Footer */
/* ================ */

.waiterDashboardScreen__sidebar-footer {
  padding: 1rem 1.5rem;
  border-top: 1px solid rgba(255, 255, 255, 0.1);
  background: rgba(0, 0, 0, 0.1);
  backdrop-filter: blur(10px);
}

.waiterDashboardScreen__logout-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.75rem;
  width: 100%;
  padding: 1rem 1.5rem;
  background: linear-gradient(135deg, #e74c3c 0%, #c0392b 100%);
  color: var(--home-white);
  border: none;
  border-radius: 12px;
  font-size: 1rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.25, 0.8, 0.25, 1);
  position: relative;
  overflow: hidden;
  box-shadow: 0 4px 15px rgba(231, 76, 60, 0.2);
}

.waiterDashboardScreen__logout-btn::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left 0.5s;
}

.waiterDashboardScreen__logout-btn:hover::before {
  left: 100%;
}

.waiterDashboardScreen__logout-btn:hover {
  background: linear-gradient(135deg, #c0392b 0%, #a93226 100%);
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(231, 76, 60, 0.4);
}

/* ================ */
/* Main Content */
/* ================ */

.waiterDashboardScreen__main {
  flex: 1;
  margin-right: 280px;
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
  min-height: 100vh;
  display: flex;
  flex-direction: column;
  transition: all 0.3s ease;
}

/* ================ */
/* Responsive Design */
/* ================ */

@media (max-width: 768px) {
  .waiterDashboardScreen__main {
    margin-right: 0;
  }
  
  .waiterDashboardScreen__sidebar-close-btn {
    display: flex !important;
  }
  
  .waiterDashboardScreen__sidebar {
    width: 100%;
    max-width: 320px;
  }
}

/* ================ */
/* Connection Error Banner */
/* ================ */

.waiterDashboardScreen__connection-error-banner {
  background: var(--home-danger-color);
  color: var(--home-white);
  padding: 1rem;
  text-align: center;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
  position: relative;
}

.waiterDashboardScreen__connection-error-close {
  position: absolute;
  left: 1rem;
  top: 50%;
  transform: translateY(-50%);
  background: none;
  border: none;
  color: var(--home-white);
  font-size: 1.2rem;
  cursor: pointer;
  padding: 0.5rem;
  border-radius: var(--home-border-radius);
  transition: var(--home-transition);
}

.waiterDashboardScreen__connection-error-close:hover {
  background: rgba(255, 255, 255, 0.1);
}

/* ================ */
/* Content Container */
/* ================ */

.waiterDashboardScreen__content-container {
  flex: 1;
  padding: 2rem;
  overflow-y: auto;
}

/* ================ */
/* Screen Header */
/* ================ */

.waiterDashboardScreen__screen-header {
  margin-bottom: 2rem;
}

.waiterDashboardScreen__header-flex {
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex-wrap: wrap;
  gap: 1rem;
  margin-bottom: 1rem;
}

.waiterDashboardScreen__screen-title {
  color: var(--home-primary-color);
  font-size: 2rem;
  font-weight: 700;
  margin: 0;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.waiterDashboardScreen__title-icon {
  color: var(--home-warning-color);
  font-size: 1.8rem;
}

.waiterDashboardScreen__screen-subtitle {
  color: var(--home-gray-600);
  font-size: 1rem;
  margin: 0.5rem 0 0 0;
}

/* ================ */
/* Action Buttons */
/* ================ */

.waiterDashboardScreen__flex-gap-8 {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  flex-wrap: wrap;
}

.waiterDashboardScreen__btn-refresh {
  background: var(--home-secondary-color);
  color: var(--home-white);
  border: none;
  padding: 0.75rem 1.5rem;
  border-radius: var(--home-border-radius);
  font-size: 0.9rem;
  font-weight: 500;
  cursor: pointer;
  transition: var(--home-transition);
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.waiterDashboardScreen__btn-refresh:hover:not(.waiterDashboardScreen__btn-refresh--loading) {
  background: var(--home-secondary-dark);
  transform: translateY(-1px);
}

.waiterDashboardScreen__btn-refresh--loading {
  opacity: 0.7;
  cursor: not-allowed;
}

.waiterDashboardScreen__status-badge {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  background: var(--home-success-color);
  color: var(--home-white);
  padding: 0.5rem 1rem;
  border-radius: 20px;
  font-size: 0.85rem;
  font-weight: 500;
}

/* ================ */
/* Search Section */
/* ================ */

.waiterDashboardScreen__search-section {
  margin-bottom: 2rem;
}

.waiterDashboardScreen__search-input-group {
  position: relative;
  max-width: 400px;
}

.waiterDashboardScreen__search-icon {
  position: absolute;
  right: 1rem;
  top: 50%;
  transform: translateY(-50%);
  color: var(--home-gray-400);
  font-size: 1rem;
}

.waiterDashboardScreen__search-input {
  width: 100%;
  padding: 1rem 1rem 1rem 3rem;
  padding-right: 3rem;
  border: 2px solid var(--home-gray-300);
  border-radius: var(--home-border-radius);
  font-size: 1rem;
  background: var(--home-white);
  transition: var(--home-transition);
  text-align: right;
  direction: rtl;
}

.waiterDashboardScreen__search-input:focus {
  outline: none;
  border-color: var(--home-secondary-color);
  box-shadow: 0 0 0 3px rgba(52, 152, 219, 0.1);
}

.waiterDashboardScreen__clear-search {
  position: absolute;
  left: 0.5rem;
  top: 50%;
  transform: translateY(-50%);
  background: var(--home-gray-400);
  color: var(--home-white);
  border: none;
  width: 24px;
  height: 24px;
  border-radius: 50%;
  font-size: 0.7rem;
  cursor: pointer;
  transition: var(--home-transition);
  display: flex;
  align-items: center;
  justify-content: center;
}

.waiterDashboardScreen__clear-search:hover {
  background: var(--home-gray-600);
}

/* ================ */
/* Category Filter */
/* ================ */

.waiterDashboardScreen__category-filter {
  display: flex;
  gap: 0.5rem;
  margin-bottom: 2rem;
  flex-wrap: wrap;
}

.waiterDashboardScreen__category-btn {
  background: var(--home-white);
  color: var(--home-gray-600);
  border: 2px solid var(--home-gray-300);
  padding: 0.75rem 1.5rem;
  border-radius: var(--home-border-radius);
  font-size: 0.9rem;
  font-weight: 500;
  cursor: pointer;
  transition: var(--home-transition);
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.waiterDashboardScreen__category-btn:hover {
  background: var(--home-gray-100);
  border-color: var(--home-gray-400);
}

.waiterDashboardScreen__category-btn--active {
  background: var(--home-secondary-color);
  color: var(--home-white);
  border-color: var(--home-secondary-color);
}

/* ================ */
/* Menu Items Grid */
/* ================ */

.waiterDashboardScreen__menu-items-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: 1.5rem;
  margin-bottom: 2rem;
}

.waiterDashboardScreen__menu-item {
  background: var(--home-white);
  border-radius: var(--home-border-radius-lg);
  box-shadow: var(--home-box-shadow);
  transition: var(--home-transition);
  overflow: hidden;
  border: 1px solid var(--home-gray-200);
}

.waiterDashboardScreen__menu-item:hover {
  transform: translateY(-4px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

.waiterDashboardScreen__menu-item-header {
  padding: 1.5rem;
  background: var(--home-gray-50);
  border-bottom: 1px solid var(--home-gray-200);
}

.waiterDashboardScreen__menu-item-name {
  font-size: 1.2rem;
  font-weight: 700;
  color: var(--home-primary-color);
  margin: 0 0 0.5rem 0;
}

.waiterDashboardScreen__menu-item-category {
  display: inline-block;
  background: var(--home-secondary-color);
  color: var(--home-white);
  padding: 0.25rem 0.75rem;
  border-radius: 15px;
  font-size: 0.8rem;
  font-weight: 500;
}

.waiterDashboardScreen__menu-item-body {
  padding: 1.5rem;
}

.waiterDashboardScreen__menu-item-description {
  color: var(--home-gray-600);
  font-size: 0.95rem;
  line-height: 1.5;
  margin-bottom: 1rem;
}

.waiterDashboardScreen__menu-item-sizes {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
}

.waiterDashboardScreen__size-option {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0.75rem;
  background: var(--home-gray-50);
  border-radius: var(--home-border-radius);
  border: 2px solid transparent;
  cursor: pointer;
  transition: var(--home-transition);
}

.waiterDashboardScreen__size-option:hover {
  background: var(--home-gray-100);
  border-color: var(--home-secondary-color);
}

.waiterDashboardScreen__size-option--selected {
  background: var(--home-secondary-color);
  color: var(--home-white);
  border-color: var(--home-secondary-dark);
}

.waiterDashboardScreen__size-name {
  font-weight: 600;
  font-size: 0.95rem;
}

.waiterDashboardScreen__size-price {
  font-weight: 700;
  font-size: 1rem;
  color: var(--home-success-color);
}

.waiterDashboardScreen__size-option--selected .waiterDashboardScreen__size-price {
  color: var(--home-white);
}

/* ================ */
/* Add to Cart Section */
/* ================ */

.waiterDashboardScreen__add-to-cart-section {
  padding: 1.5rem;
  background: var(--home-gray-50);
  border-top: 1px solid var(--home-gray-200);
}

.waiterDashboardScreen__quantity-selector {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 1rem;
  margin-bottom: 1rem;
}

.waiterDashboardScreen__quantity-btn {
  background: var(--home-secondary-color);
  color: var(--home-white);
  border: none;
  width: 36px;
  height: 36px;
  border-radius: 50%;
  font-size: 1.2rem;
  cursor: pointer;
  transition: var(--home-transition);
  display: flex;
  align-items: center;
  justify-content: center;
}

.waiterDashboardScreen__quantity-btn:hover:not(:disabled) {
  background: var(--home-secondary-dark);
  transform: scale(1.1);
}

.waiterDashboardScreen__quantity-btn:disabled {
  background: var(--home-gray-400);
  cursor: not-allowed;
  transform: none;
}

.waiterDashboardScreen__quantity-display {
  font-size: 1.2rem;
  font-weight: 700;
  color: var(--home-primary-color);
  min-width: 40px;
  text-align: center;
}

.waiterDashboardScreen__add-to-cart-btn {
  width: 100%;
  background: var(--home-success-color);
  color: var(--home-white);
  border: none;
  padding: 1rem;
  border-radius: var(--home-border-radius);
  font-size: 1rem;
  font-weight: 600;
  cursor: pointer;
  transition: var(--home-transition);
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
}

.waiterDashboardScreen__add-to-cart-btn:hover:not(:disabled) {
  background: var(--home-success-dark);
  transform: translateY(-2px);
}

.waiterDashboardScreen__add-to-cart-btn:disabled {
  background: var(--home-gray-400);
  cursor: not-allowed;
  transform: none;
}

/* ================ */
/* Responsive Design */
/* ================ */

@media (max-width: 768px) {
  .waiterDashboardScreen__mobile-menu-toggle {
    display: block;
  }
  
  .waiterDashboardScreen__sidebar {
    width: 100%;
    transform: translateX(100%);
  }
  
  .waiterDashboardScreen__sidebar-close-btn {
    display: block;
  }
  
  .waiterDashboardScreen__main {
    margin-right: 0;
  }
  
  .waiterDashboardScreen__content-container {
    padding: 1rem;
  }
  
  .waiterDashboardScreen__header-flex {
    flex-direction: column;
    align-items: stretch;
  }
  
  .waiterDashboardScreen__screen-title {
    font-size: 1.5rem;
    text-align: center;
  }
  
  .waiterDashboardScreen__flex-gap-8 {
    justify-content: center;
  }
  
  .waiterDashboardScreen__search-input-group {
    max-width: none;
  }
  
  .waiterDashboardScreen__menu-items-grid {
    grid-template-columns: 1fr;
    gap: 1rem;
  }
  
  .waiterDashboardScreen__category-filter {
    overflow-x: auto;
    /* إخفاء شريط التمرير للمتصفحات المختلفة */
    -ms-overflow-style: none; /* Internet Explorer and Edge */
    /* Firefox only - safe progressive enhancement (other browsers ignore) */
    scrollbar-width: none; /* ⚠️ Warning expected: Firefox-only feature */
  }
  
  /* إخفاء شريط التمرير لمتصفحات WebKit (Chrome, Safari, Edge) */
  .waiterDashboardScreen__category-filter::-webkit-scrollbar {
    display: none;
  }
}

@media (max-width: 480px) {
  .waiterDashboardScreen__content-container {
    padding: 0.5rem;
  }
  
  .waiterDashboardScreen__menu-item-header,
  .waiterDashboardScreen__menu-item-body,
  .waiterDashboardScreen__add-to-cart-section {
    padding: 1rem;
  }
  
  .waiterDashboardScreen__screen-title {
    font-size: 1.3rem;
  }
  
  .waiterDashboardScreen__category-btn {
    padding: 0.5rem 1rem;
    font-size: 0.8rem;
  }
}

/* ================ */
/* Sidebar Stats & Footer */
/* ================ */

.waiterDashboardScreen__sidebar-stats {
  padding: 1rem;
  border-top: 1px solid var(--home-gray-200);
}

.waiterDashboardScreen__connection-status-only {
  display: flex;
  justify-content: center;
}

.waiterDashboardScreen__connection-status {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.5rem 1rem;
  border-radius: var(--home-border-radius);
  font-size: 0.9rem;
  font-weight: 500;
}

.waiterDashboardScreen__connection-status--connected {
  background: var(--home-success-light);
  color: var(--home-success-dark);
}

.waiterDashboardScreen__connection-status--disconnected {
  background: var(--home-danger-light);
  color: var(--home-danger-dark);
}

.waiterDashboardScreen__retry-connection-btn {
  background: none;
  border: none;
  color: inherit;
  cursor: pointer;
  padding: 0.25rem;
  border-radius: var(--home-border-radius);
  transition: var(--home-transition);
}

.waiterDashboardScreen__retry-connection-btn:hover {
  background: rgba(0, 0, 0, 0.1);
}

.waiterDashboardScreen__logout-btn {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  width: 100%;
  padding: 1rem;
  background: var(--home-danger-color);
  color: var(--home-white);
  border: none;
  text-decoration: none;
  border-radius: var(--home-border-radius);
  transition: var(--home-transition);
  cursor: pointer;
  font-size: 1rem;
  font-weight: 500;
}

.waiterDashboardScreen__logout-btn:hover {
  background: var(--home-danger-dark);
}

/* ================ */
/* Connection Error Banner */
/* ================ */

.waiterDashboardScreen__connection-error-banner {
  background: var(--home-danger-color);
  color: var(--home-white);
  padding: 1rem;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  margin-bottom: 1rem;
  border-radius: var(--home-border-radius);
}

.waiterDashboardScreen__error-close-btn {
  background: none;
  border: none;
  color: inherit;
  cursor: pointer;
  padding: 0.25rem;
  margin-right: auto;
  border-radius: var(--home-border-radius);
  transition: var(--home-transition);
}

.waiterDashboardScreen__error-close-btn:hover {
  background: rgba(255, 255, 255, 0.2);
}

/* ================ */
/* Content Card */
/* ================ */

.waiterDashboardScreen__content-card {
  background: var(--home-white);
  border-radius: var(--home-border-radius-lg);
  box-shadow: var(--home-box-shadow);
  overflow: hidden;
}


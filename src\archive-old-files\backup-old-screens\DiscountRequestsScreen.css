/* تصميم شاشة طلبات الخصم */

.discount-requests-screen {
  padding: 0;
  background: #f8f9fa;
  min-height: 100vh;
}

/* رأس الشاشة */
.discount-requests-screen .screen-header {
  background: linear-gradient(135deg, #e74c3c 0%, #c0392b 100%);
  color: white;
  padding: 2rem;
  margin-bottom: 2rem;
  box-shadow: 0 4px 15px rgba(231, 76, 60, 0.2);
}

.discount-requests-screen .header-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  max-width: 1200px;
  margin: 0 auto;
  flex-wrap: wrap;
  gap: 1rem;
}

.discount-requests-screen .header-content h2 {
  margin: 0;
  font-size: 2rem;
  font-weight: 700;
  display: flex;
  align-items: center;
  gap: 1rem;
}

.discount-requests-screen .header-content h2 i {
  font-size: 1.8rem;
  color: rgba(255, 255, 255, 0.9);
}

.discount-requests-screen .header-actions {
  display: flex;
  gap: 1rem;
  flex-wrap: wrap;
}

.discount-requests-screen .refresh-btn {
  background: rgba(255, 255, 255, 0.2);
  border: 1px solid rgba(255, 255, 255, 0.3);
  color: white;
  padding: 0.75rem 1.5rem;
  border-radius: 8px;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  transition: all 0.3s ease;
  cursor: pointer;
}

.discount-requests-screen .refresh-btn:hover {
  background: rgba(255, 255, 255, 0.3);
  transform: translateY(-2px);
}

/* إحصائيات طلبات الخصم */
.discount-stats-section {
  max-width: 1200px;
  margin: 0 auto 2rem;
  padding: 0 2rem;
}

.discount-stats-section .stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 1.5rem;
}

.discount-stats-section .stat-card {
  background: white;
  border-radius: 12px;
  padding: 2rem;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.08);
  transition: all 0.3s ease;
  border: 1px solid #e9ecef;
  position: relative;
  overflow: hidden;
}

.discount-stats-section .stat-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: linear-gradient(90deg, #e74c3c, #c0392b);
}

.discount-stats-section .stat-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

.discount-stats-section .stat-card.total::before {
  background: linear-gradient(90deg, #3498db, #2980b9);
}

.discount-stats-section .stat-card.pending::before {
  background: linear-gradient(90deg, #f39c12, #e67e22);
}

.discount-stats-section .stat-card.approved::before {
  background: linear-gradient(90deg, #27ae60, #229954);
}

.discount-stats-section .stat-card.rejected::before {
  background: linear-gradient(90deg, #e74c3c, #c0392b);
}

.discount-stats-section .stat-icon {
  width: 60px;
  height: 60px;
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.5rem;
  color: white;
  margin-bottom: 1rem;
}

.discount-stats-section .stat-card.total .stat-icon {
  background: linear-gradient(135deg, #3498db, #2980b9);
}

.discount-stats-section .stat-card.pending .stat-icon {
  background: linear-gradient(135deg, #f39c12, #e67e22);
}

.discount-stats-section .stat-card.approved .stat-icon {
  background: linear-gradient(135deg, #27ae60, #229954);
}

.discount-stats-section .stat-card.rejected .stat-icon {
  background: linear-gradient(135deg, #e74c3c, #c0392b);
}

.discount-stats-section .stat-card.amount .stat-icon {
  background: linear-gradient(135deg, #8e44ad, #732d91);
}

.discount-stats-section .stat-content {
  text-align: center;
}

.discount-stats-section .stat-value {
  font-size: 2rem;
  font-weight: bold;
  color: #2c3e50;
  margin-bottom: 0.5rem;
  display: block;
}

.discount-stats-section .stat-label {
  color: #6c757d;
  font-size: 0.9rem;
  font-weight: 500;
}

/* قسم طلبات الخصم */
.discount-requests-section {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 2rem 2rem;
}

.discount-requests-section .section-header {
  background: white;
  padding: 1.5rem 2rem;
  border-radius: 12px 12px 0 0;
  border-bottom: 3px solid #e74c3c;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
}

.discount-requests-section .section-header h3 {
  margin: 0;
  color: #2c3e50;
  font-size: 1.3rem;
  font-weight: 600;
  display: flex;
  align-items: center;
  gap: 0.75rem;
}

.discount-requests-section .section-header h3 i {
  color: #e74c3c;
  font-size: 1.1rem;
}

/* حالة فارغة */
.empty-state {
  background: white;
  padding: 4rem 2rem;
  text-align: center;
  border-radius: 0 0 12px 12px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
}

.empty-state .empty-icon {
  font-size: 4rem;
  color: #bdc3c7;
  margin-bottom: 1.5rem;
}

.empty-state h3 {
  color: #2c3e50;
  margin-bottom: 0.5rem;
  font-size: 1.5rem;
}

.empty-state p {
  color: #6c757d;
  font-size: 1rem;
}

/* شبكة طلبات الخصم */
.discount-requests-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(400px, 1fr));
  gap: 1.5rem;
  background: white;
  padding: 2rem;
  border-radius: 0 0 12px 12px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
}

/* كارت طلب الخصم */
.discount-request-card {
  background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
  border: 1px solid #e9ecef;
  border-radius: 12px;
  padding: 1.5rem;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.discount-request-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: linear-gradient(90deg, #6c757d, #495057);
}

.discount-request-card.pending::before {
  background: linear-gradient(90deg, #f39c12, #e67e22);
}

.discount-request-card.approved::before {
  background: linear-gradient(90deg, #27ae60, #229954);
}

.discount-request-card.rejected::before {
  background: linear-gradient(90deg, #e74c3c, #c0392b);
}

.discount-request-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
  border-color: #e74c3c;
}

/* رأس الكارت */
.request-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1.5rem;
  flex-wrap: wrap;
  gap: 1rem;
}

.request-number {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-weight: 600;
  color: #2c3e50;
  font-size: 1.1rem;
}

.request-number i {
  color: #e74c3c;
}

/* شارة الحالة */
.status-badge {
  padding: 0.5rem 1rem;
  border-radius: 20px;
  font-size: 0.85rem;
  font-weight: 600;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  white-space: nowrap;
}

.status-badge.pending {
  background: linear-gradient(135deg, #fff3cd, #ffeaa7);
  color: #856404;
  border: 1px solid #ffeaa7;
}

.status-badge.approved {
  background: linear-gradient(135deg, #d4edda, #c3e6cb);
  color: #155724;
  border: 1px solid #c3e6cb;
}

.status-badge.rejected {
  background: linear-gradient(135deg, #f8d7da, #f5c6cb);
  color: #721c24;
  border: 1px solid #f5c6cb;
}

/* معلومات الطلب */
.request-info {
  margin-bottom: 1.5rem;
}

.info-row {
  display: flex;
  justify-content: space-between;
  margin-bottom: 1rem;
  flex-wrap: wrap;
  gap: 0.5rem;
}

.info-item {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  color: #495057;
  font-size: 0.9rem;
  min-width: 0;
  flex: 1;
}

.info-item i {
  color: #6c757d;
  width: 16px;
  text-align: center;
  flex-shrink: 0;
}

.info-item span {
  word-break: break-word;
}

.request-reason {
  background: #fff3cd;
  border: 1px solid #ffeaa7;
  border-radius: 8px;
  padding: 0.75rem;
  margin-bottom: 1rem;
  display: flex;
  align-items: flex-start;
  gap: 0.5rem;
  font-size: 0.9rem;
  color: #856404;
}

.request-reason i {
  color: #f39c12;
  margin-top: 2px;
  flex-shrink: 0;
}

.request-time {
  background: #f8f9fa;
  border-radius: 6px;
  padding: 0.5rem 0.75rem;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-size: 0.85rem;
  color: #6c757d;
  border: 1px solid #e9ecef;
}

.request-time i {
  color: #9c27b0;
}

/* أزرار العمل */
.request-actions {
  display: flex;
  gap: 0.75rem;
  flex-wrap: wrap;
}

.request-actions .btn {
  flex: 1;
  min-width: 0;
  padding: 0.75rem 1rem;
  border: none;
  border-radius: 8px;
  cursor: pointer;
  font-weight: 500;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
  transition: all 0.3s ease;
  text-decoration: none;
  font-size: 0.9rem;
  white-space: nowrap;
}

.request-actions .details-btn {
  background: linear-gradient(135deg, #3498db, #2980b9);
  color: white;
}

.request-actions .details-btn:hover {
  background: linear-gradient(135deg, #2980b9, #21618c);
  transform: translateY(-2px);
}

.request-actions .approve-btn {
  background: linear-gradient(135deg, #27ae60, #229954);
  color: white;
}

.request-actions .approve-btn:hover {
  background: linear-gradient(135deg, #229954, #1e8449);
  transform: translateY(-2px);
}

.request-actions .reject-btn {
  background: linear-gradient(135deg, #e74c3c, #c0392b);
  color: white;
}

.request-actions .reject-btn:hover {
  background: linear-gradient(135deg, #c0392b, #a93226);
  transform: translateY(-2px);
}

/* تحسينات responsive */
@media (max-width: 768px) {
  .discount-requests-screen .screen-header {
    padding: 1.5rem 1rem;
  }
  
  .discount-requests-screen .header-content {
    flex-direction: column;
    align-items: stretch;
  }
  
  .discount-requests-screen .header-content h2 {
    font-size: 1.5rem;
    text-align: center;
  }
  
  .discount-stats-section {
    padding: 0 1rem;
  }
  
  .discount-stats-section .stats-grid {
    grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
    gap: 1rem;
  }
  
  .discount-stats-section .stat-card {
    padding: 1.5rem 1rem;
  }
  
  .discount-stats-section .stat-value {
    font-size: 1.5rem;
  }
  
  .discount-requests-section {
    padding: 0 1rem 1rem;
  }
  
  .discount-requests-section .section-header {
    padding: 1rem 1.5rem;
  }
  
  .discount-requests-grid {
    grid-template-columns: 1fr;
    gap: 1rem;
    padding: 1.5rem;
  }
  
  .discount-request-card {
    padding: 1rem;
  }
  
  .request-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 0.75rem;
  }
  
  .info-row {
    flex-direction: column;
    gap: 0.75rem;
  }
  
  .request-actions {
    flex-direction: column;
  }
  
  .request-actions .btn {
    width: 100%;
  }
}

@media (max-width: 480px) {
  .discount-requests-screen .header-content h2 {
    font-size: 1.3rem;
  }
  
  .discount-stats-section .stats-grid {
    grid-template-columns: repeat(2, 1fr);
  }
  
  .discount-stats-section .stat-card {
    padding: 1rem;
  }
  
  .discount-stats-section .stat-icon {
    width: 50px;
    height: 50px;
    font-size: 1.2rem;
  }
  
  .discount-stats-section .stat-value {
    font-size: 1.3rem;
  }
  
  .discount-requests-grid {
    padding: 1rem;
  }
  
  .discount-request-card {
    padding: 0.75rem;
  }
  
  .request-number {
    font-size: 1rem;
  }
  
  .status-badge {
    padding: 0.4rem 0.8rem;
    font-size: 0.8rem;
  }
}

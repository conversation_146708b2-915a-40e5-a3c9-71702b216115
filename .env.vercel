# Vercel Environment Variables for Desha Coffee Management System
# Copy these variables to your Vercel project settings
# Project Settings → Environment Variables

# API Configuration
VITE_API_URL=https://deshacoffee-production.up.railway.app
VITE_API_TIMEOUT=30000
VITE_API_RETRY_ATTEMPTS=3
VITE_API_RETRY_DELAY=1000

# Socket.IO Configuration
VITE_SOCKET_URL=https://deshacoffee-production.up.railway.app
VITE_SOCKET_TIMEOUT=20000
VITE_SOCKET_RECONNECTION_ATTEMPTS=5
VITE_SOCKET_RECONNECTION_DELAY=1000

# Environment Settings
NODE_ENV=production
VITE_ENV=production
VITE_FRONTEND_PORT=5176

# App Information
VITE_APP_NAME=Desha Coffee Management System
VITE_APP_VERSION=1.0.0
VITE_APP_DESCRIPTION=نظام إدارة المقهى المتكامل

# Authentication & Security
VITE_JWT_EXPIRES_IN=24h
VITE_SESSION_TIMEOUT=3600000

# API Endpoints (for reference - automatically constructed from VITE_API_URL)
# VITE_API_URL + /api/auth
# VITE_API_URL + /api/users
# VITE_API_URL + /api/products
# VITE_API_URL + /api/orders
# VITE_API_URL + /api/categories
# VITE_API_URL + /api/inventory
# VITE_API_URL + /api/discount-requests

# Performance Settings
VITE_ENABLE_CACHE=true
VITE_CACHE_DURATION=300000

# Feature Flags
VITE_ENABLE_NOTIFICATIONS=true
VITE_ENABLE_REAL_TIME=true
VITE_ENABLE_OFFLINE_MODE=false

# Debug Settings (for production monitoring)
VITE_ENABLE_ERROR_REPORTING=true
VITE_LOG_LEVEL=error

# UI/UX Settings
VITE_DEFAULT_LANGUAGE=ar
VITE_THEME=light
VITE_RTL_SUPPORT=true

# Build Information
VITE_BUILD_TIME=2025-05-31
VITE_COMMIT_HASH=latest

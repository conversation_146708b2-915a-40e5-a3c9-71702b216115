// خدمة تشغيل الصوت للإشعارات مع دعم الصفحات غير النشطة
export class NotificationSoundService {
  private audio: HTMLAudioElement | null = null;
  private soundEnabled: boolean = true;
  private wakeLock: any = null; // لمنع الجهاز من الدخول في وضع السكون
  private audioContext: AudioContext | null = null;
  private gainNode: GainNode | null = null;

  constructor() {
    this.initializeSound();
    this.initializeAudioContext();
    this.setupPageVisibilityHandling();
    this.setupServiceWorkerListener();
  }

  private initializeSound() {
    try {
      // إنشاء عنصر الصوت
      this.audio = new Audio('/notification.wav');
      this.audio.volume = 0.7; // مستوى الصوت 70%
      this.audio.preload = 'auto';
      this.audio.loop = false;

      // إعداد الأحداث
      this.audio.addEventListener('canplaythrough', () => {
        console.log('✅ ملف الصوت جاهز للتشغيل');
      });

      this.audio.addEventListener('error', (e) => {
        console.error('❌ خطأ في تحميل ملف الصوت:', e);
        this.soundEnabled = false;
      });

      // محاولة تحميل الصوت مسبقاً
      this.audio.load();
    } catch (error) {
      console.error('❌ خطأ في إنشاء عنصر الصوت:', error);
      this.soundEnabled = false;
    }
  }

  // تهيئة Audio Context للتحكم المتقدم في الصوت
  private initializeAudioContext() {
    try {
      // التحقق من دعم AudioContext
      const AudioContextClass = window.AudioContext || (window as any).webkitAudioContext;
      
      if (AudioContextClass) {
        this.audioContext = new AudioContextClass();
        this.gainNode = this.audioContext.createGain();
        this.gainNode.connect(this.audioContext.destination);
        
        console.log('✅ تم تهيئة AudioContext');
      }
    } catch (error) {
      console.error('❌ خطأ في تهيئة AudioContext:', error);
    }
  }

  // إعداد التعامل مع حالة الصفحة (نشطة/غير نشطة)
  private setupPageVisibilityHandling() {
    document.addEventListener('visibilitychange', () => {
      if (document.hidden) {
        console.log('🌙 الصفحة أصبحت غير نشطة - استمرار دعم الصوت');
        this.enableBackgroundAudio();
      } else {
        console.log('☀️ الصفحة أصبحت نشطة');
        this.disableBackgroundAudio();
      }
    });

    // التعامل مع فقدان التركيز
    window.addEventListener('blur', () => {
      console.log('👁️ فقدان تركيز النافذة');
    });

    window.addEventListener('focus', () => {
      console.log('👁️ استعادة تركيز النافذة');
      this.resumeAudioContext();
    });
  }

  // إعداد الاستماع لطلبات تشغيل الصوت من Service Worker
  private setupServiceWorkerListener() {
    if ('serviceWorker' in navigator) {
      navigator.serviceWorker.addEventListener('message', (event) => {
        if (event.data && event.data.type === 'PLAY_SOUND_REQUEST') {
          console.log('🔊 طلب تشغيل صوت من Service Worker');
          
          // تشغيل الصوت بشكل عاجل
          this.playNotificationAdvanced({
            volume: 0.8,
            repeat: event.data.urgent ? 2 : 1,
            urgent: event.data.urgent || false
          });
        }
      });
    }
  }

  // تفعيل الصوت في الخلفية
  private async enableBackgroundAudio() {
    try {
      // محاولة الحصول على Wake Lock لمنع توقف الصوت
      if ('wakeLock' in navigator) {
        this.wakeLock = await (navigator as any).wakeLock.request('screen');
        console.log('🔒 تم الحصول على Wake Lock');
      }
    } catch (error) {
      console.log('⚠️ لا يمكن الحصول على Wake Lock:', error);
    }
  }

  // تعطيل الصوت في الخلفية
  private disableBackgroundAudio() {
    if (this.wakeLock) {
      this.wakeLock.release();
      this.wakeLock = null;
      console.log('🔓 تم تحرير Wake Lock');
    }
  }

  // استئناف AudioContext
  private resumeAudioContext() {
    if (this.audioContext && this.audioContext.state === 'suspended') {
      this.audioContext.resume().then(() => {
        console.log('🎵 تم استئناف AudioContext');
      });
    }
  }

  // تشغيل الصوت - الطريقة الأساسية للتوافق مع الكود الموجود
  public playNotification(): void {
    this.playNotificationAdvanced({
      volume: 0.7,
      repeat: 1,
      urgent: false
    }).catch(error => {
      console.error('❌ خطأ في تشغيل الصوت:', error);
    });
  }

  // تشغيل الصوت مع دعم محسن للصفحات غير النشطة
  public async playNotificationAdvanced(options: {
    volume?: number;
    repeat?: number;
    urgent?: boolean;
  } = {}): Promise<void> {
    if (!this.soundEnabled || !this.audio) {
      console.log('⚠️ الصوت غير متاح');
      return;
    }

    const { volume = 0.7, repeat = 1, urgent = false } = options;

    try {
      // تحديث مستوى الصوت
      this.audio.volume = Math.min(Math.max(volume, 0), 1);

      // إذا كانت الصفحة غير نشطة، استخدم طرق إضافية
      if (document.hidden) {
        await this.playBackgroundSound(repeat, urgent);
      } else {
        await this.playForegroundSound(repeat, urgent);
      }
    } catch (error) {
      console.error('❌ خطأ في تشغيل الصوت:', error);
    }
  }

  // تشغيل الصوت في المقدمة (الصفحة نشطة)
  private async playForegroundSound(repeat: number, urgent: boolean): Promise<void> {
    for (let i = 0; i < repeat; i++) {
      try {
        // إعادة تعيين الوقت إلى البداية
        this.audio!.currentTime = 0;
        
        // تشغيل الصوت
        await this.audio!.play();
        console.log(`🔊 تم تشغيل صوت الإشعار (${i + 1}/${repeat})`);
        
        // انتظار قصير بين التكرارات
        if (i < repeat - 1) {
          await new Promise(resolve => setTimeout(resolve, urgent ? 300 : 500));
        }
      } catch (error) {
        console.error('❌ خطأ في تشغيل الصوت:', error);
        
        // محاولة باستخدام AudioContext كبديل
        if (this.audioContext && this.gainNode) {
          await this.playWithAudioContext();
        }
      }
    }
  }

  // تشغيل الصوت في الخلفية (الصفحة غير نشطة)
  private async playBackgroundSound(repeat: number, urgent: boolean): Promise<void> {
    console.log('🌙 تشغيل الصوت في الخلفية');
    
    // استراتيجيات متعددة لضمان تشغيل الصوت
    const strategies = [
      () => this.playWithHtmlAudio(repeat, urgent),
      () => this.playWithAudioContext(),
      () => this.playWithServiceWorker(),
      () => this.playWithVisibilityTrick(repeat, urgent)
    ];

    for (const strategy of strategies) {
      try {
        await strategy();
        break; // نجحت الاستراتيجية
      } catch (error) {
        console.warn('⚠️ فشلت استراتيجية تشغيل الصوت:', error);
        continue; // جرب الاستراتيجية التالية
      }
    }
  }

  // تشغيل بـ HTML Audio
  private async playWithHtmlAudio(repeat: number, urgent: boolean): Promise<void> {
    for (let i = 0; i < repeat; i++) {
      this.audio!.currentTime = 0;
      await this.audio!.play();
      
      if (i < repeat - 1) {
        await new Promise(resolve => setTimeout(resolve, urgent ? 200 : 300));
      }
    }
  }

  // تشغيل بـ AudioContext
  private async playWithAudioContext(): Promise<void> {
    if (!this.audioContext || !this.gainNode) {
      throw new Error('AudioContext غير متاح');
    }

    // تحميل ملف الصوت
    const response = await fetch('/notification.wav');
    const arrayBuffer = await response.arrayBuffer();
    const audioBuffer = await this.audioContext.decodeAudioData(arrayBuffer);

    // إنشاء مصدر الصوت
    const source = this.audioContext.createBufferSource();
    source.buffer = audioBuffer;
    source.connect(this.gainNode);

    // تشغيل الصوت
    source.start();
    console.log('🎵 تم تشغيل الصوت بـ AudioContext');
  }

  // تشغيل عبر Service Worker
  private async playWithServiceWorker(): Promise<void> {
    if ('serviceWorker' in navigator && navigator.serviceWorker.controller) {
      navigator.serviceWorker.controller.postMessage({
        type: 'PLAY_NOTIFICATION_SOUND'
      });
      console.log('🔊 طلب تشغيل الصوت من Service Worker');
    } else {
      throw new Error('Service Worker غير متاح');
    }
  }

  // تشغيل باستخدام خدعة الرؤية
  private async playWithVisibilityTrick(repeat: number, urgent: boolean): Promise<void> {
    // محاولة جعل الصفحة تبدو نشطة مؤقتاً
    const originalHidden = Object.getOwnPropertyDescriptor(Document.prototype, 'hidden');
    
    try {
      // إخفاء حالة hidden مؤقتاً
      Object.defineProperty(document, 'hidden', {
        value: false,
        configurable: true
      });

      await this.playForegroundSound(repeat, urgent);
    } finally {
      // استعادة الحالة الأصلية
      if (originalHidden) {
        Object.defineProperty(document, 'hidden', originalHidden);
      }
    }
  }

  // طلب تفاعل المستخدم لتمكين الصوت
  private requestUserInteraction(): void {
    console.log('⚠️ يتطلب تفاعل المستخدم لتشغيل الصوت');
    // يمكن إضافة منطق هنا لإظهار رسالة للمستخدم
  }

  // تمكين/تعطيل الصوت
  public setSoundEnabled(enabled: boolean): void {
    this.soundEnabled = enabled;
    console.log(`🔊 الصوت ${enabled ? 'مُفعل' : 'مُعطل'}`);
  }

  // التحقق من حالة الصوت
  public isSoundEnabled(): boolean {
    return this.soundEnabled;
  }

  // تعديل مستوى الصوت
  public setVolume(volume: number): void {
    if (this.audio && volume >= 0 && volume <= 1) {
      this.audio.volume = volume;
      console.log(`🔊 مستوى الصوت: ${Math.round(volume * 100)}%`);
    }
  }

  // تنظيف الموارد
  public cleanup(): void {
    if (this.audio) {
      this.audio.pause();
      this.audio.removeEventListener('canplaythrough', () => {});
      this.audio.removeEventListener('error', () => {});
    }

    if (this.wakeLock) {
      this.wakeLock.release();
      this.wakeLock = null;
    }

    if (this.audioContext) {
      this.audioContext.close();
    }
  }
}

// إنشاء مثيل واحد للخدمة
export const notificationSound = new NotificationSoundService();

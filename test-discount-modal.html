<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار تفاصيل طلبات الخصم - Desha Coffee</title>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 1rem;
        }

        .test-container {
            background: white;
            border-radius: 15px;
            padding: 2rem;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
            max-width: 400px;
            width: 100%;
            text-align: center;
        }

        .test-title {
            color: #2c3e50;
            margin-bottom: 1rem;
            font-size: 1.5rem;
        }

        .test-description {
            color: #6c757d;
            margin-bottom: 2rem;
            line-height: 1.6;
        }

        .test-btn {
            background: linear-gradient(135deg, #3498db, #2980b9);
            color: white;
            border: none;
            padding: 1rem 2rem;
            border-radius: 10px;
            font-size: 1rem;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            text-decoration: none;
            display: inline-block;
        }

        .test-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 20px rgba(52, 152, 219, 0.3);
        }

        .features-list {
            text-align: right;
            margin: 2rem 0;
            padding: 1rem;
            background: #f8f9fa;
            border-radius: 8px;
        }

        .features-list li {
            padding: 0.3rem 0;
            color: #2c3e50;
            list-style: none;
            position: relative;
            padding-right: 1.5rem;
        }

        .features-list li::before {
            content: "✓";
            position: absolute;
            right: 0;
            color: #28a745;
            font-weight: bold;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1 class="test-title">
            <i class="fas fa-percentage"></i>
            اختبار تفاصيل طلبات الخصم
        </h1>
        
        <p class="test-description">
            تم تحسين مودال تفاصيل طلبات الخصم لاستغلال المساحة العرضية بشكل أفضل
        </p>

        <div class="features-list">
            <h3 style="margin-bottom: 1rem; color: #2c3e50;">التحسينات المطبقة:</h3>
            <ul>
                <li>تخطيط أفقي بدلاً من العمودي</li>
                <li>عرض أكبر (900px بدلاً من 500px)</li>
                <li>معلومات مدمجة وأكثر تنظيماً</li>
                <li>استغلال أفضل للمساحة الشاشة</li>
                <li>عناصر أصغر ولكن أكثر وضوحاً</li>
            </ul>
        </div>

        <a href="http://localhost:3000" class="test-btn">
            <i class="fas fa-external-link-alt"></i>
            اختبار النظام
        </a>

        <div style="margin-top: 2rem; font-size: 0.9rem; color: #6c757d;">
            <p><strong>خطوات الاختبار:</strong></p>
            <p>1. انتقل إلى لوحة المدير</p>
            <p>2. اختر "طلبات الخصم"</p>
            <p>3. اضغط "التفاصيل" لأي طلب</p>
            <p>4. لاحظ التخطيط الأفقي الجديد</p>
        </div>
    </div>
</body>
</html>

const mongoose = require('mongoose');
const User = require('../models/User');
const Category = require('../models/Category');
const Product = require('../models/Product');
const Order = require('../models/Order');
const config = require('../config/environment');

// إعدادات قاعدة البيانات - استخدام التكوين المركزي
const MONGODB_URI = config.database.mongoUri;

// بيانات المستخدمين الأساسية - Production Real Users
const users = [
  {
    username: 'Be<PERSON>',
    email: '<EMAIL>',
    password: 'MOHAMEDmostafa123',
    name: 'Be<PERSON> - مدير عام',
    role: 'manager',
    status: 'active',
    phone: '+966501234567'
  },
  {
    username: 'azza',
    email: '<EMAIL>',
    password: '253040',
    name: 'عزة - نادل',
    role: 'waiter',
    status: 'active',
    phone: '+966509876543'
  },
  {
    username: 'khaled',
    email: '<EMAIL>',
    password: '253040',
    name: 'خالد - طباخ',
    role: 'chef',
    status: 'active',
    phone: '+966502468135'
  },
  {
    username: 'admin',
    email: '<EMAIL>',
    password: 'DeshaCoffee2024Admin!',
    name: 'مدير النظام',
    role: 'admin',
    status: 'active',
    phone: '+966507654321'
  }
];

// بيانات الفئات
const categories = [
  {
    name: 'مشروبات ساخنة',
    description: 'قهوة وشاي ومشروبات ساخنة متنوعة',
    image: '/images/hot-drinks.jpg',
    active: true,
    sortOrder: 1
  },
  {
    name: 'مشروبات باردة',
    description: 'عصائر ومشروبات باردة منعشة',
    image: '/images/cold-drinks.jpg',
    active: true,
    sortOrder: 2
  },
  {
    name: 'حلويات',
    description: 'كيك وحلويات شرقية وغربية',
    image: '/images/desserts.jpg',
    active: true,
    sortOrder: 3
  },
  {
    name: 'وجبات خفيفة',
    description: 'ساندويتشات ووجبات خفيفة',
    image: '/images/snacks.jpg',
    active: true,
    sortOrder: 4
  }
];

// بيانات المنتجات
const products = [
  {
    name: 'قهوة عربية',
    description: 'قهوة عربية أصيلة محمصة طازجة مع الهيل',
    price: 15,
    categoryName: 'مشروبات ساخنة',
    image: '/images/arabic-coffee.jpg',
    available: true,
    featured: true,
    preparationTime: 5,
    ingredients: ['قهوة عربية', 'هيل', 'ماء'],
    tags: ['قهوة', 'عربي', 'تقليدي']
  },
  {
    name: 'كابتشينو',
    description: 'كابتشينو إيطالي كلاسيكي مع رغوة الحليب الكريمية',
    price: 20,
    categoryName: 'مشروبات ساخنة',
    image: '/images/cappuccino.jpg',
    available: true,
    featured: true,
    preparationTime: 8,
    ingredients: ['إسبريسو', 'حليب', 'رغوة حليب'],
    tags: ['كابتشينو', 'إيطالي', 'حليب']
  },
  {
    name: 'لاتيه',
    description: 'لاتيه كريمي مع إسبريسو وحليب مبخر',
    price: 18,
    categoryName: 'مشروبات ساخنة',
    image: '/images/latte.jpg',
    available: true,
    featured: false,
    preparationTime: 7,
    ingredients: ['إسبريسو', 'حليب مبخر'],
    tags: ['لاتيه', 'حليب', 'كريمي']
  },
  {
    name: 'عصير برتقال طازج',
    description: 'عصير برتقال طبيعي 100% بدون إضافات',
    price: 12,
    categoryName: 'مشروبات باردة',
    image: '/images/orange-juice.jpg',
    available: true,
    featured: true,
    preparationTime: 3,
    ingredients: ['برتقال طازج'],
    tags: ['عصير', 'طبيعي', 'فيتامين سي']
  },
  {
    name: 'تشيز كيك',
    description: 'تشيز كيك كريمي بالفراولة',
    price: 25,
    categoryName: 'حلويات',
    image: '/images/cheesecake.jpg',
    available: true,
    featured: true,
    preparationTime: 2,
    ingredients: ['جبن كريمي', 'فراولة', 'بسكويت'],
    tags: ['تشيز كيك', 'فراولة', 'حلوى']
  }
];

async function setupDatabase() {
  try {
    console.log('🚀 بدء إعداد قاعدة البيانات...');
    
    // الاتصال بقاعدة البيانات
    await mongoose.connect(MONGODB_URI, {
      useNewUrlParser: true,
      useUnifiedTopology: true,
    });
    console.log('✅ تم الاتصال بقاعدة البيانات');

    // إضافة المستخدمين
    console.log('\n👥 إضافة المستخدمين...');
    for (const userData of users) {
      const existingUser = await User.findOne({ 
        $or: [{ username: userData.username }, { email: userData.email }] 
      });
      
      if (!existingUser) {
        const user = new User(userData);
        await user.save();
        console.log(`✅ تم إضافة المستخدم: ${userData.name} (${userData.username})`);
      } else {
        console.log(`⚠️ المستخدم موجود بالفعل: ${userData.username}`);
      }
    }

    // إضافة الفئات
    console.log('\n📂 إضافة الفئات...');
    for (const categoryData of categories) {
      const existingCategory = await Category.findOne({ name: categoryData.name });
      
      if (!existingCategory) {
        const category = new Category(categoryData);
        await category.save();
        console.log(`✅ تم إضافة الفئة: ${categoryData.name}`);
      } else {
        console.log(`⚠️ الفئة موجودة بالفعل: ${categoryData.name}`);
      }
    }

    // إضافة المنتجات
    console.log('\n🍽️ إضافة المنتجات...');
    for (const productData of products) {
      const existingProduct = await Product.findOne({ name: productData.name });
      
      if (!existingProduct) {
        // البحث عن الفئة
        const category = await Category.findOne({ name: productData.categoryName });
        if (category) {
          const product = new Product({
            ...productData,
            category: category._id
          });
          await product.save();
          console.log(`✅ تم إضافة المنتج: ${productData.name}`);
        } else {
          console.log(`❌ لم يتم العثور على الفئة: ${productData.categoryName}`);
        }
      } else {
        console.log(`⚠️ المنتج موجود بالفعل: ${productData.name}`);
      }
    }

    // إحصائيات قاعدة البيانات
    console.log('\n📊 إحصائيات قاعدة البيانات:');
    const userCount = await User.countDocuments();
    const categoryCount = await Category.countDocuments();
    const productCount = await Product.countDocuments();
    const orderCount = await Order.countDocuments();

    console.log(`👥 المستخدمين: ${userCount}`);
    console.log(`📂 الفئات: ${categoryCount}`);
    console.log(`🍽️ المنتجات: ${productCount}`);
    console.log(`📋 الطلبات: ${orderCount}`);

    console.log('\n🎉 تم إعداد قاعدة البيانات بنجاح!');
    console.log('\n🔐 بيانات تسجيل الدخول - Production Real Users:');
    console.log('👤 المدير العام: Beso / MOHAMEDmostafa123');
    console.log('👤 النادل: azza / 253040');
    console.log('👤 الطباخ: khaled / 253040');
    console.log('👤 الأدمن: admin / DeshaCoffee2024Admin!');

  } catch (error) {
    console.error('❌ خطأ في إعداد قاعدة البيانات:', error);
  } finally {
    await mongoose.connection.close();
    console.log('\n🔒 تم إغلاق الاتصال بقاعدة البيانات');
    process.exit(0);
  }
}

// تشغيل السكريپت
if (require.main === module) {
  setupDatabase();
}

module.exports = { setupDatabase };

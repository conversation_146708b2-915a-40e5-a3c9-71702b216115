const mongoose = require('mongoose');
const config = require('./environment');

const connectDB = async () => {
  try {
    // استخدام التكوين المركزي
    const mongoURI = config.database.mongoUri;

    if (!mongoURI) {
      console.error('❌ MONGODB_URI is required');
      console.error('💥 Application cannot start without MongoDB connection');
      console.error('🔧 Please add MONGODB_URI to .env file');
      process.exit(1);
    }

    console.log('🔄 Attempting to connect to MongoDB...');
    console.log(`📡 Target: ${mongoURI.replace(/:[^:@]*@/, ':***@')}`); // Hide password in logs

    const conn = await mongoose.connect(mongoURI, {
      useNewUrlParser: true,
      useUnifiedTopology: true,
      serverSelectionTimeoutMS: 10000,
      socketTimeoutMS: 45000,
      family: 4,
      maxPoolSize: config.database.maxPoolSize,
      minPoolSize: config.database.minPoolSize,
      maxIdleTimeMS: config.database.maxIdleTimeMS,
      heartbeatFrequencyMS: 10000
    });

    console.log(`✅ MongoDB Atlas Connected Successfully!`);
    console.log(`📊 Database: ${conn.connection.name}`);
    console.log(`🌐 Host: ${conn.connection.host}`);
    console.log(`🔗 Connection State: ${conn.connection.readyState}`);
    console.log(`🎯 Using MongoDB Atlas as primary database`);

    // Handle connection events
    mongoose.connection.on('error', (err) => {
      console.error('❌ MongoDB connection error:', err);
    });

    mongoose.connection.on('disconnected', () => {
      console.log('⚠️ MongoDB disconnected');
    });

    mongoose.connection.on('reconnected', () => {
      console.log('🔄 MongoDB reconnected');
    });

    // Graceful shutdown
    process.on('SIGINT', async () => {
      await mongoose.connection.close();
      console.log('🔒 MongoDB connection closed through app termination');
      process.exit(0);
    });

    return conn;
  } catch (error) {
    console.error('❌ MongoDB Atlas connection failed:', error.message);
    console.error('💡 Possible causes:');
    console.error('   - Network connectivity issues');
    console.error('   - Invalid credentials in MONGODB_URI');
    console.error('   - MongoDB Atlas cluster not accessible');
    console.error('   - IP address not whitelisted in Atlas');
    console.error('💥 Application cannot continue without MongoDB');
    console.error('🔧 Please fix MongoDB connection and restart');
    process.exit(1); // Exit if MongoDB connection fails
  }
};

// Check database connection status
const isConnected = () => {
  return mongoose.connection.readyState === 1;
};

// Get connection info
const getConnectionInfo = () => {
  if (!isConnected()) {
    return { connected: false, message: 'Database not connected' };
  }

  return {
    connected: true,
    host: mongoose.connection.host,
    name: mongoose.connection.name,
    readyState: mongoose.connection.readyState,
    collections: Object.keys(mongoose.connection.collections)
  };
};

module.exports = {
  connectDB,
  isConnected,
  getConnectionInfo
};

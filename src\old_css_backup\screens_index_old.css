/* ====================================
   Waiter Screens CSS Index File
   ==================================== */

/* استيراد جميع ملفات CSS الخاصة بشاشات النادل */

/* شاشة المشروبات */
@import './WaiterDrinksScreen.css';

/* شاشة الطلبات */
@import './WaiterOrdersScreen.css';

/* شاشة الطاولات */
@import './WaiterTablesScreen.css';

/* شاشة السلة */
@import './WaiterCartScreen.css';

/* شاشة طلبات الخصم */
@import './WaiterDiscountsScreen.css';

/* ====================================
   متغيرات مشتركة عامة لجميع الشاشات
   ==================================== */

:root {
  /* الألوان المشتركة */
  --waiter-common-white: #ffffff;
  --waiter-common-black: #000000;
  --waiter-common-gray-light: #f8f9fa;
  --waiter-common-gray-medium: #dee2e6;
  --waiter-common-gray-dark: #6c757d;
  
  /* الخطوط */
  --waiter-common-font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
  --waiter-common-font-size-base: 16px;
  --waiter-common-line-height-base: 1.5;
  
  /* الانتقالات */
  --waiter-common-transition-fast: 0.2s ease;
  --waiter-common-transition-medium: 0.3s ease;
  --waiter-common-transition-slow: 0.5s ease;
  
  /* الظلال المشتركة */
  --waiter-common-shadow-light: 0 1px 3px rgba(0, 0, 0, 0.05);
  --waiter-common-shadow-medium: 0 2px 8px rgba(0, 0, 0, 0.1);
  --waiter-common-shadow-heavy: 0 4px 16px rgba(0, 0, 0, 0.15);
  
  /* أشعة الحدود المشتركة */
  --waiter-common-border-radius-sm: 4px;
  --waiter-common-border-radius-md: 8px;
  --waiter-common-border-radius-lg: 12px;
  --waiter-common-border-radius-xl: 16px;
}

/* ====================================
   أنماط مشتركة لجميع شاشات النادل
   ==================================== */

/* إعداد الحاوي الأساسي */
.content-container {
  padding: 20px;
  max-width: 100%;
  margin: 0 auto;
  min-height: calc(100vh - 140px);
  background: var(--waiter-common-gray-light);
}

/* التحسينات المشتركة للأزرار */
.content-container button {
  font-family: var(--waiter-common-font-family);
  cursor: pointer;
  border: none;
  outline: none;
  transition: var(--waiter-common-transition-medium);
}

.content-container button:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  transform: none !important;
}

/* تحسينات مشتركة للمدخلات */
.content-container input,
.content-container textarea,
.content-container select {
  font-family: var(--waiter-common-font-family);
  outline: none;
  transition: var(--waiter-common-transition-medium);
}

/* تحسينات الأيقونات */
.content-container i {
  transition: var(--waiter-common-transition-fast);
}

/* تحسينات الكروت المشتركة */
.content-container .card,
.content-container .order-card,
.content-container .table-card,
.content-container .menu-item-card,
.content-container .discount-request-card,
.content-container .cart-item {
  transition: var(--waiter-common-transition-medium);
}

/* تحسينات التمرير */
.content-container ::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

.content-container ::-webkit-scrollbar-track {
  background: var(--waiter-common-gray-light);
  border-radius: var(--waiter-common-border-radius-sm);
}

.content-container ::-webkit-scrollbar-thumb {
  background: var(--waiter-common-gray-dark);
  border-radius: var(--waiter-common-border-radius-sm);
}

.content-container ::-webkit-scrollbar-thumb:hover {
  background: var(--waiter-common-gray-dark);
}

/* ====================================
   إعدادات الاستجابة العامة
   ==================================== */

/* الهواتف الصغيرة */
@media (max-width: 480px) {
  .content-container {
    padding: 12px;
  }
}

/* الأجهزة اللوحية */
@media (min-width: 768px) and (max-width: 1024px) {
  .content-container {
    padding: 24px;
  }
}

/* الشاشات الكبيرة */
@media (min-width: 1200px) {
  .content-container {
    max-width: 1400px;
    padding: 32px;
  }
}

/* ====================================
   تحسينات الأداء
   ==================================== */

/* تحسين الرسوم المتحركة */
.content-container * {
  backface-visibility: hidden;
  perspective: 1000px;
}

/* تحسين عرض الصور */
.content-container img {
  max-width: 100%;
  height: auto;
  display: block;
}

/* منع تحديد النصوص غير المرغوب فيها */
.content-container button,
.content-container .btn,
.content-container .icon {
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
}

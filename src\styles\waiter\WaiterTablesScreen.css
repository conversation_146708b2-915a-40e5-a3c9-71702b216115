/* ====================================
   WaiterTablesScreen Component Styles
   ==================================== */

/* استيراد المتغيرات المميزة لشاشة الطاولات */
@import '../variables/tables-variables.css';


/* Header شاشة الطاولات */
.waiter-tables-screen .screen-header {
  background: linear-gradient(135deg, var(--tables-primary-color), var(--tables-secondary-color));
  padding: var(--tables-spacing-lg);
  border-radius: var(--tables-border-radius);
  margin-bottom: var(--tables-spacing-lg);
  color: white;
  box-shadow: var(--tables-shadow);
}

.waiter-tables-screen .action-buttons-flex {
  display: flex;
  justify-content: space-between;
  align-items: center;
  gap: var(--tables-spacing-md);
  margin-bottom: var(--tables-spacing-md);
}

.waiter-tables-screen .screen-title {
  font-size: 1.75rem;
  font-weight: 700;
  margin: 0;
  display: flex;
  align-items: center;
  gap: var(--tables-spacing-sm);
}

.waiter-tables-screen .btn-refresh {
  background: rgba(255, 255, 255, 0.2);
  color: white;
  border: 1px solid rgba(255, 255, 255, 0.3);
  padding: var(--tables-spacing-sm) var(--tables-spacing-md);
  border-radius: var(--tables-border-radius);
  cursor: pointer;
  transition: all 0.3s ease;
  font-size: 0.9rem;
  display: flex;
  align-items: center;
  gap: var(--tables-spacing-xs);
}

.waiter-tables-screen .btn-refresh:hover {
  background: rgba(255, 255, 255, 0.3);
  transform: translateY(-1px);
}

.waiter-tables-screen .clear-tables-btn {
  background: var(--tables-danger-color);
  color: white;
  border: 2px solid var(--tables-danger-color);
  padding: var(--tables-spacing-sm) var(--tables-spacing-md);
  border-radius: var(--tables-border-radius);
  cursor: pointer;
  transition: all 0.3s ease;
  font-size: 0.9rem;
  display: flex;
  align-items: center;
  gap: var(--tables-spacing-xs);
  margin-top: var(--tables-spacing-sm);
}

.waiter-tables-screen .clear-tables-btn:hover {
  background: #c0392b;
  border-color: #c0392b;
  transform: translateY(-1px);
  box-shadow: var(--tables-shadow);
}

.waiter-tables-screen .trash-icon-spacing {
  margin-left: var(--tables-spacing-xs);
}

/* إحصائيات الطاولات */
.waiter-tables-screen .stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: var(--tables-spacing-lg);
  margin-bottom: var(--tables-spacing-lg);
}

.waiter-tables-screen .stat-card {
  background: var(--tables-bg-primary);
  border: 1px solid var(--tables-border-color);
  border-radius: var(--tables-border-radius);
  padding: var(--tables-spacing-lg);
  display: flex;
  align-items: center;
  gap: var(--tables-spacing-md);
  box-shadow: var(--tables-shadow);
  transition: all 0.3s ease;
}

.waiter-tables-screen .stat-card:hover {
  transform: translateY(-2px);
  box-shadow: var(--tables-shadow-hover);
}

.waiter-tables-screen .stat-icon {
  width: 60px;
  height: 60px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, var(--tables-primary-color), var(--tables-secondary-color));
  color: white;
  font-size: 1.5rem;
}

.waiter-tables-screen .stat-content {
  flex: 1;
}

.waiter-tables-screen .stat-number {
  font-size: 2rem;
  font-weight: 700;
  color: var(--tables-primary-color);
  margin-bottom: var(--tables-spacing-xs);
}

.waiter-tables-screen .stat-label {
  color: var(--tables-text-secondary);
  font-size: 0.9rem;
  font-weight: 500;
}

/* قائمة الطاولات */
.waiter-tables-screen .tables-list {
  margin-bottom: var(--tables-spacing-xl);
}

.waiter-tables-screen .tables-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(320px, 1fr));
  gap: var(--tables-spacing-lg);
}

.waiter-tables-screen .table-card {
  background: var(--tables-bg-primary);
  border: 1px solid var(--tables-border-color);
  border-radius: var(--tables-border-radius);
  padding: var(--tables-spacing-lg);
  transition: all 0.3s ease;
  box-shadow: var(--tables-shadow);
  position: relative;
  overflow: hidden;
}

.waiter-tables-screen .table-card:hover {
  transform: translateY(-4px);
  box-shadow: var(--tables-shadow-hover);
  border-color: var(--tables-primary-color);
}

.waiter-tables-screen .table-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: var(--tables-spacing-md);
  gap: var(--tables-spacing-sm);
}

.waiter-tables-screen .table-number {
  font-weight: 600;
  color: var(--tables-text-primary);
  display: flex;
  align-items: center;
  gap: var(--tables-spacing-xs);
  font-size: 1.1rem;
}

.waiter-tables-screen .table-status {
  padding: var(--tables-spacing-xs) var(--tables-spacing-sm);
  border-radius: 12px;
  font-size: 0.8rem;
  font-weight: 600;
  display: flex;
  align-items: center;
  gap: var(--tables-spacing-xs);
  white-space: nowrap;
}

.waiter-tables-screen .table-status.active {
  background: #d4edda;
  color: #155724;
  border: 1px solid #c3e6cb;
}

.waiter-tables-screen .table-status.closed {
  background: #f8d7da;
  color: #721c24;
  border: 1px solid #f5c6cb;
}

/* معلومات الطاولة */
.waiter-tables-screen .table-info {
  margin-bottom: var(--tables-spacing-lg);
}

.waiter-tables-screen .info-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: var(--tables-spacing-sm);
  gap: var(--tables-spacing-sm);
}

.waiter-tables-screen .info-row .label {
  display: flex;
  align-items: center;
  gap: var(--tables-spacing-xs);
  color: var(--tables-text-secondary);
  font-size: 0.9rem;
  font-weight: 500;
  min-width: 0;
}

.waiter-tables-screen .info-row .value {
  color: var(--tables-text-primary);
  font-weight: 600;
  text-align: left;
  word-break: break-word;
}

.waiter-tables-screen .error-text {
  color: var(--tables-danger-color);
  font-size: 0.75rem;
  font-weight: 500;
  margin-right: var(--tables-spacing-xs);
}

/* أزرار العمليات */
.waiter-tables-screen .table-actions {
  display: flex;
  gap: var(--tables-spacing-sm);
}

.waiter-tables-screen .btn-details {
  flex: 1;
  background: var(--tables-info-color);
  color: white;
  border: none;
  padding: var(--tables-spacing-sm) var(--tables-spacing-md);
  border-radius: var(--tables-border-radius);
  cursor: pointer;
  transition: all 0.3s ease;
  font-weight: 600;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: var(--tables-spacing-xs);
}

.waiter-tables-screen .btn-details:hover {
  background: #138496;
  transform: translateY(-1px);
  box-shadow: var(--tables-shadow);
}

.waiter-tables-screen .btn-close-table {
  background: var(--tables-danger-color);
  color: white;
  border: none;
  padding: var(--tables-spacing-sm) var(--tables-spacing-md);
  border-radius: var(--tables-border-radius);
  cursor: pointer;
  transition: all 0.3s ease;
  font-weight: 600;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: var(--tables-spacing-xs);
  white-space: nowrap;
}

.waiter-tables-screen .btn-close-table:hover {
  background: #c82333;
  transform: translateY(-1px);
  box-shadow: var(--tables-shadow);
}

/* حالات الفراغ */
.waiter-tables-screen .empty-state {
  text-align: center;
  padding: var(--tables-spacing-xl);
  grid-column: 1 / -1;
}

.waiter-tables-screen .empty-icon {
  font-size: 3rem;
  color: var(--tables-text-muted);
  margin-bottom: var(--tables-spacing-md);
}

.waiter-tables-screen .empty-state h3 {
  color: var(--tables-text-primary);
  margin-bottom: var(--tables-spacing-sm);
}

.waiter-tables-screen .empty-state p {
  color: var(--tables-text-secondary);
  margin-bottom: var(--tables-spacing-lg);
}

/* تحسينات للطاولات النشطة */
.waiter-tables-screen .table-card.active {
  border-left: 4px solid var(--tables-success-color);
}

.waiter-tables-screen .table-card.inactive {
  opacity: 0.7;
  border-left: 4px solid var(--tables-text-muted);
}

/* Responsive Design */
@media (max-width: 768px) {
  .waiter-tables-screen .action-buttons-flex {
    flex-direction: column;
    align-items: stretch;
  }
  
  .waiter-tables-screen .stats-grid {
    grid-template-columns: 1fr;
    gap: var(--tables-spacing-md);
  }
  
  .waiter-tables-screen .tables-grid {
    grid-template-columns: 1fr;
    gap: var(--tables-spacing-md);
  }
  
  .waiter-tables-screen .table-header {
    flex-direction: column;
    align-items: stretch;
    gap: var(--tables-spacing-sm);
  }
  
  .waiter-tables-screen .table-status {
    align-self: flex-start;
  }
  
  .waiter-tables-screen .table-actions {
    flex-direction: column;
  }
  
  .waiter-tables-screen .clear-tables-btn {
    margin-top: var(--tables-spacing-md);
  }
}

@media (max-width: 480px) {
  .waiter-tables-screen .screen-header {
    padding: var(--tables-spacing-md);
  }
  
  .waiter-tables-screen .screen-title {
    font-size: 1.5rem;
  }
  
  .waiter-tables-screen .table-card {
    padding: var(--tables-spacing-md);
  }
  
  .waiter-tables-screen .stat-card {
    padding: var(--tables-spacing-md);
  }
  
  .waiter-tables-screen .stat-icon {
    width: 50px;
    height: 50px;
    font-size: 1.25rem;
  }
  
  .waiter-tables-screen .stat-number {
    font-size: 1.5rem;
  }
  
  .waiter-tables-screen .info-row {
    flex-direction: column;
    align-items: stretch;
  }
  
  .waiter-tables-screen .info-row .value {
    text-align: right;
    margin-top: var(--tables-spacing-xs);
  }
}


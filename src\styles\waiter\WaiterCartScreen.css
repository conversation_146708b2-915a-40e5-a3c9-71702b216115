/* ====================================
   WaiterCartScreen Component Styles
   ==================================== */

/* استيراد المتغيرات المميزة لشاشة الطلبات */
@import '../variables/orders-variables.css';

/* متغيرات CSS خاصة بشاشة السلة */


/* Header شاشة السلة */
.waiter-cart-screen .screen-header {
  background: linear-gradient(135deg, var(--orders-primary-color), var(--orders-secondary-color));
  padding: var(--orders-spacing-lg);
  border-radius: var(--orders-border-radius);
  margin-bottom: var(--orders-spacing-lg);
  color: white;
  box-shadow: var(--orders-shadow);
}

.waiter-cart-screen .screen-title {
  font-size: 1.75rem;
  font-weight: 700;
  margin: 0 0 var(--orders-spacing-xs) 0;
  display: flex;
  align-items: center;
  gap: var(--orders-spacing-sm);
}

.waiter-cart-screen .screen-subtitle {
  margin: 0;
  opacity: 0.9;
  font-size: 0.95rem;
}

/* حالة السلة الفارغة */
.waiter-cart-screen .empty-cart-state {
  text-align: center;
  padding: var(--orders-spacing-xl);
  background: var(--orders-bg-primary);
  border-radius: var(--orders-border-radius);
  box-shadow: var(--orders-shadow);
  border: 1px solid var(--orders-border-color);
}

.waiter-cart-screen .empty-cart-icon {
  width: 80px;
  height: 80px;
  margin: 0 auto var(--orders-spacing-lg);
  background: linear-gradient(135deg, var(--orders-primary-color), var(--orders-secondary-color));
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 2rem;
}

.waiter-cart-screen .empty-cart-state h3 {
  color: var(--orders-text-primary);
  margin-bottom: var(--orders-spacing-sm);
  font-size: 1.5rem;
}

.waiter-cart-screen .empty-cart-state p {
  color: var(--orders-text-secondary);
  margin-bottom: var(--orders-spacing-lg);
  font-size: 1rem;
}

.waiter-cart-screen .browse-menu-btn {
  background: var(--orders-primary-color);
  color: white;
  border: none;
  padding: var(--orders-spacing-md) var(--orders-spacing-lg);
  border-radius: var(--orders-border-radius);
  cursor: pointer;
  transition: all 0.3s ease;
  font-weight: 600;
  font-size: 1rem;
  display: inline-flex;
  align-items: center;
  gap: var(--orders-spacing-sm);
}

.waiter-cart-screen .browse-menu-btn:hover {
  background: var(--orders-secondary-color);
  transform: translateY(-1px);
  box-shadow: var(--orders-shadow);
}

/* محتوى السلة */
.waiter-cart-screen .cart-content {
  display: flex;
  flex-direction: column;
  gap: var(--orders-spacing-lg);
}

/* عناصر السلة */
.waiter-cart-screen .cart-items {
  background: var(--orders-bg-primary);
  border-radius: var(--orders-border-radius);
  box-shadow: var(--orders-shadow);
  border: 1px solid var(--orders-border-color);
  overflow: hidden;
}

.waiter-cart-screen .cart-item {
  display: flex;
  align-items: flex-start;
  gap: var(--orders-spacing-md);
  padding: var(--orders-spacing-lg);
  border-bottom: 1px solid var(--orders-border-color);
  transition: all 0.3s ease;
}

.waiter-cart-screen .cart-item:last-child {
  border-bottom: none;
}

.waiter-cart-screen .cart-item:hover {
  background: var(--orders-bg-secondary);
}

.waiter-cart-screen .cart-item-info {
  flex: 1;
  min-width: 0;
}

.waiter-cart-screen .cart-item-info h4 {
  margin: 0 0 var(--orders-spacing-xs) 0;
  color: var(--orders-text-primary);
  font-size: 1.1rem;
  font-weight: 600;
}

.waiter-cart-screen .cart-item-price {
  color: var(--orders-primary-color);
  font-weight: 600;
  font-size: 1rem;
  margin-bottom: var(--orders-spacing-md);
}

/* ملاحظات العنصر */
.waiter-cart-screen .cart-item-notes {
  margin-top: var(--orders-spacing-md);
}

.waiter-cart-screen .notes-label {
  display: flex;
  align-items: center;
  gap: var(--orders-spacing-xs);
  color: var(--orders-text-secondary);
  font-size: 0.9rem;
  font-weight: 500;
  margin-bottom: var(--orders-spacing-xs);
}

.waiter-cart-screen .notes-input {
  width: 100%;
  padding: var(--orders-spacing-sm);
  border: 1px solid var(--orders-border-color);
  border-radius: var(--orders-border-radius);
  font-size: 0.9rem;
  resize: vertical;
  min-height: 60px;
  transition: all 0.3s ease;
  text-align: right;
  font-family: inherit;
}

.waiter-cart-screen .notes-input:focus {
  outline: none;
  border-color: var(--orders-primary-color);
  box-shadow: 0 0 0 3px rgba(253, 126, 20, 0.1);
}

.waiter-cart-screen .notes-input::placeholder {
  color: var(--orders-text-muted);
}

/* التحكم في الكمية */
.waiter-cart-screen .cart-item-controls {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: var(--orders-spacing-md);
  min-width: 120px; /* Base minimum width for all browsers */
  flex-shrink: 0; /* Prevent shrinking below content size */
}

.waiter-cart-screen .quantity-controls {
  display: flex;
  align-items: center;
  gap: var(--orders-spacing-sm);
  background: var(--orders-bg-secondary);
  border-radius: var(--orders-border-radius);
  padding: var(--orders-spacing-xs);
  border: 1px solid var(--orders-border-color);
}

.waiter-cart-screen .quantity-btn {
  width: 36px;
  height: 36px;
  border: none;
  background: var(--orders-primary-color);
  color: white;
  border-radius: 6px;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
  font-size: 0.9rem;
}

.waiter-cart-screen .quantity-btn:hover {
  background: var(--orders-secondary-color);
  transform: scale(1.05);
}

.waiter-cart-screen .quantity-btn:active {
  transform: scale(0.95);
}

.waiter-cart-screen .quantity {
  min-width: 40px;
  text-align: center;
  font-weight: 600;
  font-size: 1.1rem;
  color: var(--orders-text-primary);
}

.waiter-cart-screen .remove-item-btn {
  width: 36px;
  height: 36px;
  border: none;
  background: var(--orders-danger-color);
  color: white;
  border-radius: 6px;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
  font-size: 0.9rem;
}

.waiter-cart-screen .remove-item-btn:hover {
  background: #c82333;
  transform: scale(1.05);
}

/* نموذج السلة */
.waiter-cart-screen .cart-form {
  background: var(--orders-bg-primary);
  padding: var(--orders-spacing-lg);
  border-radius: var(--orders-border-radius);
  box-shadow: var(--orders-shadow);
  border: 1px solid var(--orders-border-color);
}

.waiter-cart-screen .form-group {
  margin-bottom: var(--orders-spacing-lg);
}

.waiter-cart-screen .form-group:last-child {
  margin-bottom: 0;
}

.waiter-cart-screen .form-group label {
  display: block;
  color: var(--orders-text-primary);
  font-weight: 600;
  margin-bottom: var(--orders-spacing-sm);
  font-size: 1rem;
}

.waiter-cart-screen .form-group input {
  width: 100%;
  padding: var(--orders-spacing-md);
  border: 2px solid var(--orders-border-color);
  border-radius: var(--orders-border-radius);
  font-size: 1rem;
  transition: all 0.3s ease;
  text-align: right;
}

.waiter-cart-screen .form-group input:focus {
  outline: none;
  border-color: var(--orders-primary-color);
  box-shadow: 0 0 0 3px rgba(253, 126, 20, 0.1);
}

.waiter-cart-screen .form-group input::placeholder {
  color: var(--orders-text-muted);
}

/* إجمالي السلة */
.waiter-cart-screen .cart-total {
  background: linear-gradient(135deg, var(--orders-primary-color), var(--orders-secondary-color));
  color: white;
  padding: var(--orders-spacing-lg);
  border-radius: var(--orders-border-radius);
  text-align: center;
  font-size: 1.25rem;
  font-weight: 700;
  box-shadow: var(--orders-shadow);
}

/* أزرار العمليات */
.waiter-cart-screen .cart-actions {
  display: flex;
  gap: var(--orders-spacing-md);
  justify-content: space-between;
}

.waiter-cart-screen .clear-cart-btn {
  background: transparent;
  color: var(--orders-danger-color);
  border: 2px solid var(--orders-danger-color);
  padding: var(--orders-spacing-md) var(--orders-spacing-lg);
  border-radius: var(--orders-border-radius);
  cursor: pointer;
  transition: all 0.3s ease;
  font-weight: 600;
  display: flex;
  align-items: center;
  gap: var(--orders-spacing-sm);
  flex: 0 0 auto;
}

.waiter-cart-screen .clear-cart-btn:hover {
  background: var(--orders-danger-color);
  color: white;
  transform: translateY(-1px);
  box-shadow: var(--orders-shadow);
}

.waiter-cart-screen .submit-order-btn {
  background: var(--orders-success-color);
  color: white;
  border: none;
  padding: var(--orders-spacing-md) var(--orders-spacing-lg);
  border-radius: var(--orders-border-radius);
  cursor: pointer;
  transition: all 0.3s ease;
  font-weight: 600;
  font-size: 1.1rem;
  display: flex;
  align-items: center;
  gap: var(--orders-spacing-sm);
  flex: 1;
  justify-content: center;
}

.waiter-cart-screen .submit-order-btn:hover:not(:disabled) {
  background: #218838;
  transform: translateY(-1px);
  box-shadow: var(--orders-shadow);
}

.waiter-cart-screen .submit-order-btn:disabled {
  background: var(--orders-text-muted);
  cursor: not-allowed;
  opacity: 0.6;
}

/* Responsive Design */
@media (max-width: 768px) {
  .waiter-cart-screen .cart-item {
    flex-direction: column;
    align-items: stretch;
  }
  
  .waiter-cart-screen .cart-item-controls {
    flex-direction: row;
    justify-content: space-between;
    align-items: center;
    margin-top: var(--orders-spacing-md);
  }
  
  .waiter-cart-screen .cart-actions {
    flex-direction: column;
  }
  
  .waiter-cart-screen .clear-cart-btn,
  .waiter-cart-screen .submit-order-btn {
    justify-content: center;
  }
}

@media (max-width: 480px) {
  .waiter-cart-screen .screen-header {
    padding: var(--orders-spacing-md);
  }
  
  .waiter-cart-screen .screen-title {
    font-size: 1.5rem;
  }
  
  .waiter-cart-screen .cart-item {
    padding: var(--orders-spacing-md);
  }
  
  .waiter-cart-screen .cart-form {
    padding: var(--orders-spacing-md);
  }
  
  .waiter-cart-screen .cart-total {
    padding: var(--orders-spacing-md);
    font-size: 1.1rem;
  }
  
  .waiter-cart-screen .empty-cart-state {
    padding: var(--orders-spacing-lg);
  }
  
  .waiter-cart-screen .empty-cart-icon {
    width: 60px;
    height: 60px;
    font-size: 1.5rem;
  }
  
  .waiter-cart-screen .quantity-controls {
    gap: var(--orders-spacing-xs);
  }
  
  .waiter-cart-screen .quantity-btn,
  .waiter-cart-screen .remove-item-btn {
    width: 32px;
    height: 32px;
    font-size: 0.8rem;
  }
  
  .waiter-cart-screen .quantity {
    min-width: 30px;
    font-size: 1rem;
  }
}


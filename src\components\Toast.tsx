import React, { useState, useEffect } from 'react';
import './Toast.css';
import type { ToastMessage } from '../hooks/useToast';

interface ToastProps {
  toast: ToastMessage;
  onRemove: (id: string) => void;
}

const Toast: React.FC<ToastProps> = ({ toast, onRemove }) => {
  const [isVisible, setIsVisible] = useState(false);

  useEffect(() => {
    // إظهار التوست
    const showTimer = setTimeout(() => setIsVisible(true), 100);

    // إخفاء التوست بعد المدة المحددة
    const hideTimer = setTimeout(() => {
      setIsVisible(false);
      setTimeout(() => onRemove(toast.id), 300);
    }, toast.duration || 5000);

    return () => {
      clearTimeout(showTimer);
      clearTimeout(hideTimer);
    };
  }, [toast.id, toast.duration, onRemove]);

  const getIcon = () => {
    switch (toast.type) {
      case 'success':
        return 'fa-circle-check';
      case 'error':
        return 'fa-circle-exclamation';
      case 'warning':
        return 'fa-triangle-exclamation';
      case 'info':
      default:
        return 'fa-circle-info';
    }
  };

  return (
    <div className={`toast toast-${toast.type} ${isVisible ? 'toast-visible' : ''}`}>
      <div className="toast-icon">
        <i className={`fas ${getIcon()}`}></i>
      </div>
      <div className="toast-content">
        <p>{toast.message}</p>
        {/* زر نسخ نص الخطأ إذا كان نوع التوست خطأ */}
        {toast.type === 'error' && (
          <button
            className="toast-copy-btn"
            onClick={() => {
              navigator.clipboard.writeText(toast.message);
            }}
            style={{
              background: 'transparent',
              border: 'none',
              color: '#795548',
              cursor: 'pointer',
              fontSize: '0.95em',
              marginTop: 4,
              display: 'flex',
              alignItems: 'center',
              gap: 4
            }}
            title="نسخ نص الخطأ"
          >
            <i className="fas fa-copy"></i> نسخ الخطأ
          </button>
        )}
      </div>
      <button
        className="toast-close"
        onClick={() => {
          setIsVisible(false);
          setTimeout(() => onRemove(toast.id), 300);
        }}
        aria-label="إغلاق الإشعار"
      >
        <i className="fas fa-times"></i>
      </button>
    </div>
  );
};

// مكون حاوي التوست
interface ToastContainerProps {
  toasts: ToastMessage[];
  onRemove: (id: string) => void;
}

export const ToastContainer: React.FC<ToastContainerProps> = ({ toasts, onRemove }) => {
  return (
    <div className="toast-container">
      {toasts.map((toast) => (
        <Toast key={toast.id} toast={toast} onRemove={onRemove} />
      ))}
    </div>
  );
};

export default Toast;

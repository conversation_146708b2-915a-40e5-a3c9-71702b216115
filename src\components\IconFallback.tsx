import React from 'react';

interface IconFallbackProps {
  iconClass: string;
  fallbackText?: string;
  className?: string;
  'aria-hidden'?: boolean;
}

// خريطة الأيقونات البديلة
const iconFallbacks: { [key: string]: string } = {
  'fa-plus': '+',
  'fa-minus': '−',
  'fa-plus-circle': '⊕',
  'fa-minus-circle': '⊖',
  'fa-tag': '🏷️',
  'fa-boxes': '📦',
  'fa-cubes': '📦',
  'fa-eye': '👁️',
  'fa-eye-slash': '🚫',
  'fa-toggle-on': '🟢',
  'fa-toggle-off': '🔴',
  'fa-exclamation-triangle': '⚠️',
  'fa-search': '🔍',
  'fa-filter': '🔽',
  'fa-sync-alt': '🔄',
  'fa-refresh': '🔄',
  'fa-spinner': '⟳',
  'fa-loading': '⏳',
  'fa-check': '✓',
  'fa-times': '✖',
  'fa-edit': '✏️',
  'fa-trash': '🗑️',
  'fa-save': '💾',
  'fa-download': '⬇️',
  'fa-upload': '⬆️',
  'fa-print': '🖨️',
  'fa-cog': '⚙️',
  'fa-settings': '⚙️',
  'fa-user': '👤',
  'fa-users': '👥',
  'fa-home': '🏠',
  'fa-dashboard': '📊',
  'fa-chart': '📈',
  'fa-table': '📋',
  'fa-list': '📝',
  'fa-grid': '⊞',
  'fa-th': '⊞',
  'fa-money': '💰',
  'fa-dollar': '$',
  'fa-receipt': '🧾',
  'fa-calendar': '📅',
  'fa-clock': '🕐',
  'fa-bell': '🔔',
  'fa-notification': '🔔',
  'fa-warning': '⚠️',
  'fa-info': 'ℹ️',
  'fa-question': '❓',
  'fa-help': '❓'
};

const IconFallback: React.FC<IconFallbackProps> = ({
  iconClass,
  fallbackText,
  className = '',
  'aria-hidden': ariaHidden = true
}) => {
  // استخراج اسم الأيقونة من الفئة
  const getIconName = (iconClass: string): string => {
    const matches = iconClass.match(/fa-([\w-]+)/);
    return matches ? `fa-${matches[1]}` : iconClass;
  };

  const iconName = getIconName(iconClass);
  const fallback = fallbackText || iconFallbacks[iconName] || '•';

  return (
    <span 
      className={`icon-fallback ${iconClass} ${className}`}
      aria-hidden={ariaHidden}
      role={ariaHidden ? undefined : 'img'}
      aria-label={ariaHidden ? undefined : `أيقونة ${iconName}`}
    >
      <i className={iconClass} style={{ fontFamily: 'Font Awesome 6 Free, FontAwesome, Arial, sans-serif' }}></i>
      <span className="fallback-text" style={{ 
        display: 'none',
        fontFamily: 'Arial, sans-serif',
        fontSize: '0.9em'
      }}>
        {fallback}
      </span>
    </span>
  );
};

export default IconFallback;

// CSS للأيقونات البديلة
export const iconFallbackStyles = `
.icon-fallback {
  position: relative;
  display: inline-block;
}

.icon-fallback i {
  display: inline-block;
}

.icon-fallback .fallback-text {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  display: none;
  align-items: center;
  justify-content: center;
}

/* إظهار النص البديل عند فشل تحميل الخط */
@supports not (font-family: 'Font Awesome 6 Free') {
  .icon-fallback i {
    display: none;
  }
  
  .icon-fallback .fallback-text {
    display: flex;
  }
}

/* للمتصفحات التي لا تدعم @supports */
.icon-fallback i:before {
  content: '';
}

.icon-fallback i:empty + .fallback-text {
  display: flex;
}
`;
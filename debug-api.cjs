const fetch = require('node-fetch');
require('dotenv').config();

const backEndURL = process.env.BACKEND_URL || 'http://localhost:4010';

async function debugAPI() {
  try {
    // تسجيل الدخول
    console.log('🔐 تسجيل الدخول...');
    const loginResponse = await fetch(`${backEndURL}/api/auth/login`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        username: 'admin',
        password: 'admin123'
      })
    });

    const loginData = await loginResponse.json();
    const token = loginData.token;
    console.log('✅ تم تسجيل الدخول بنجاح');

    // فحص النادلين
    console.log('\n🔍 فحص API النادلين...');
    const usersResponse = await fetch(`${backEndURL}/api/users`, {
      headers: {
        'Authorization': `Bearer ${token}`,
        'Content-Type': 'application/json'
      }
    });

    console.log(`Status: ${usersResponse.status}`);
    
    if (usersResponse.ok) {
      const usersData = await usersResponse.json();
      console.log('👥 بيانات النادلين:', JSON.stringify(usersData, null, 2));
    } else {
      console.log('❌ فشل في جلب النادلين');
      const errorText = await usersResponse.text();
      console.log('Error:', errorText);
    }

    // فحص waiter-stats API
    console.log('\n🔍 فحص waiter-stats API...');
    const statsResponse = await fetch(`${backEndURL}/api/v1/waiter-stats`, {
      headers: {
        'Authorization': `Bearer ${token}`,
        'Content-Type': 'application/json'
      }
    });

    console.log(`Status: ${statsResponse.status}`);
    
    if (statsResponse.ok) {
      const statsData = await statsResponse.json();
      console.log('📊 بيانات الإحصائيات:', JSON.stringify(statsData, null, 2));
    } else {
      console.log('❌ فشل في جلب الإحصائيات');
      const errorText = await statsResponse.text();
      console.log('Error:', errorText);
    }

  } catch (error) {
    console.error('❌ خطأ:', error);
  }
}

debugAPI();

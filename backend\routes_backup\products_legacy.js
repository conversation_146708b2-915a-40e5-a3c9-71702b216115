const express = require('express');
const Product = require('../models/Product');
const Category = require('../models/Category');
const { authenticateToken } = require('../middleware/auth');
const { 
  applyUnifiedMiddleware, 
  asyncHandler, 
  formatResponse, 
  formatError 
} = require('../middleware/unifiedRouteHandler');
const { 
  sendSuccess, 
  sendError, 
  sendNotFound, 
  handleDatabaseError 
} = require('../middleware/unifiedResponse');

const router = express.Router();

// Apply unified middleware
applyUnifiedMiddleware(router);

// Get all products (alias for /api/menu)
router.get('/', async (req, res) => {
  try {
    const { category, available, featured, search } = req.query;
    let query = { status: 'active' };

    // Build query filters
    if (category) {
      query.category = category;
    }

    if (available !== undefined) {
      query.available = available === 'true';
    }

    if (featured !== undefined) {
      query.featured = featured === 'true';
    }

    let products;
    if (search) {
      products = await Product.search(search);
    } else {
      products = await Product.find(query)
        .populate('category', 'name icon color')
        .sort({ createdAt: -1 });
    }

    res.json(products);
  } catch (error) {
    console.error('Get products error:', error);
    res.status(500).json({
      success: false,
      message: 'خطأ في جلب المنتجات',
      error: error.message
    });
  }
});

// Get product by ID
router.get('/:id', async (req, res) => {
  try {
    const product = await Product.findById(req.params.id).populate('category', 'name icon color');

    if (!product) {
      return res.status(404).json({
        success: false,
        message: 'المنتج غير موجود'
      });
    }

    res.json({
      success: true,
      data: product
    });
  } catch (error) {
    console.error('Get product error:', error);
    res.status(500).json({
      success: false,
      message: 'خطأ في جلب المنتج'
    });
  }
});

// Create new product
router.post('/', authenticateToken, async (req, res) => {
  try {
    console.log('📱 Mobile Debug - Product Creation Request:');
    console.log('User-Agent:', req.headers['user-agent']);
    console.log('Content-Type:', req.headers['content-type']);
    console.log('Body:', JSON.stringify(req.body, null, 2));
    console.log('User:', req.user?.name || 'Unknown');

    const { name, description, price, category, categoryName, image } = req.body;

    // Enhanced validation with mobile-friendly error messages
    const trimmedName = typeof name === 'string' ? name.trim() : '';
    if (!trimmedName) {
      console.log('❌ Validation Error: Invalid name - Mobile Debug:', {
        originalName: name,
        type: typeof name,
        trimmed: trimmedName
      });
      return res.status(400).json({
        success: false,
        message: 'اسم المنتج مطلوب ويجب أن يكون نص صحيح',
        error: 'INVALID_NAME',
        details: { name: typeof name, received: name }
      });
    }

    // More flexible price validation for mobile inputs
    let numericPrice;
    if (typeof price === 'string') {
      numericPrice = parseFloat(price.replace(/[^\d.]/g, '')); // Remove non-numeric chars except dots
    } else {
      numericPrice = parseFloat(price);
    }
    
    if (!numericPrice || isNaN(numericPrice) || numericPrice <= 0) {
      console.log('❌ Validation Error: Invalid price - Mobile Debug:', {
        originalPrice: price,
        type: typeof price,
        parsed: numericPrice
      });
      return res.status(400).json({
        success: false,
        message: 'السعر مطلوب ويجب أن يكون رقم أكبر من صفر',
        error: 'INVALID_PRICE',
        details: { price: typeof price, value: price, parsed: numericPrice }
      });
    }

    // More flexible category validation
    const categoryId = typeof category === 'string' ? category.trim() : category;
    if (!categoryId) {
      console.log('❌ Validation Error: Invalid category - Mobile Debug:', {
        originalCategory: category,
        type: typeof category,
        trimmed: categoryId
      });
      return res.status(400).json({
        success: false,
        message: 'الفئة مطلوبة ويجب أن تكون معرف صحيح',
        error: 'INVALID_CATEGORY',
        details: { category: typeof category, received: category }
      });
    }

    // Verify category exists
    const categoryExists = await Category.findById(categoryId);
    if (!categoryExists) {
      console.log('❌ Category Not Found - Mobile Debug:', {
        categoryId: categoryId,
        type: typeof categoryId
      });
      return res.status(400).json({
        success: false,
        message: 'الفئة غير موجودة',
        error: 'CATEGORY_NOT_FOUND',
        details: { categoryId: categoryId }
      });
    }

    const newProduct = new Product({
      name: trimmedName,
      description: description || '',
      price: numericPrice,
      category: categoryId,
      categoryName: categoryName || categoryExists.name,
      image: image || '/images/default-product.jpg',
      available: true,
      status: 'active',
      createdBy: req.user._id
    });

    await newProduct.save();

    const populatedProduct = await Product.findById(newProduct._id).populate('category', 'name icon color');

    // Send Socket notifications for new product
    if (global.socketHandlers) {
      try {
        const userDisplayName = req.user.name || req.user.username || 'مستخدم';

        // Notify managers about new product
        global.socketHandlers.sendRoleNotification('manager', 
          `تم إضافة منتج جديد: ${populatedProduct.name}`, {
          type: 'product-created',
          productId: populatedProduct._id,
          productName: populatedProduct.name,
          price: populatedProduct.price,
          category: populatedProduct.categoryName,
          createdBy: userDisplayName,
          timestamp: new Date().toISOString()
        });

        console.log(`🛍️ تم إرسال إشعار إضافة منتج جديد: ${populatedProduct.name}`);
      } catch (socketError) {
        console.error('خطأ في إرسال إشعار Socket:', socketError);
      }
    }

    res.status(201).json({
      success: true,
      message: 'تم إنشاء المنتج بنجاح',
      data: populatedProduct
    });
  } catch (error) {
    console.error('❌ Create product error:', error);
    console.error('❌ Error details:', {
      message: error.message,
      stack: error.stack,
      name: error.name
    });
    
    // Handle specific MongoDB errors
    if (error.name === 'ValidationError') {
      const validationErrors = Object.values(error.errors).map(err => err.message);
      return res.status(400).json({
        success: false,
        message: 'خطأ في التحقق من البيانات',
        error: 'VALIDATION_ERROR',
        details: validationErrors
      });
    }
    
    if (error.code === 11000) {
      return res.status(400).json({
        success: false,
        message: 'اسم المنتج موجود بالفعل',
        error: 'DUPLICATE_PRODUCT',
        details: error.keyValue
      });
    }
    
    res.status(500).json({
      success: false,
      message: 'خطأ في إنشاء المنتج',
      error: 'SERVER_ERROR',
      details: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
});

// Update product
router.put('/:id', authenticateToken, async (req, res) => {
  try {
    const product = await Product.findById(req.params.id);

    if (!product) {
      return res.status(404).json({
        success: false,
        message: 'المنتج غير موجود'
      });
    }

    // If category is being updated, verify it exists
    if (req.body.category) {
      const categoryExists = await Category.findById(req.body.category);
      if (!categoryExists) {
        return res.status(400).json({
          success: false,
          message: 'الفئة غير موجودة'
        });
      }
    }

    Object.assign(product, req.body);
    product.updatedBy = req.user._id;
    
    await product.save();

    const updatedProduct = await Product.findById(product._id).populate('category', 'name icon color');

    // Send Socket notifications for updated product
    if (global.socketHandlers) {
      try {
        const userDisplayName = req.user.name || req.user.username || 'مستخدم';

        // Notify managers about updated product
        global.socketHandlers.sendRoleNotification('manager', 
          `تم تحديث منتج: ${updatedProduct.name}`, {
          type: 'product-updated',
          productId: updatedProduct._id,
          productName: updatedProduct.name,
          price: updatedProduct.price,
          available: updatedProduct.available,
          updatedBy: userDisplayName,
          timestamp: new Date().toISOString()
        });

        console.log(`🔄 تم إرسال إشعار تحديث منتج: ${updatedProduct.name}`);
      } catch (socketError) {
        console.error('خطأ في إرسال إشعار Socket:', socketError);
      }
    }

    res.json({
      success: true,
      message: 'تم تحديث المنتج بنجاح',
      data: updatedProduct
    });
  } catch (error) {
    console.error('Update product error:', error);
    res.status(500).json({
      success: false,
      message: 'خطأ في تحديث المنتج'
    });
  }
});

// Toggle product availability
router.patch('/:id/toggle', authenticateToken, async (req, res) => {
  try {
    const product = await Product.findById(req.params.id);

    if (!product) {
      return res.status(404).json({
        success: false,
        message: 'المنتج غير موجود'
      });
    }

    // Toggle availability
    product.available = !product.available;
    product.updatedBy = req.user._id;
    await product.save();

    const updatedProduct = await Product.findById(product._id).populate('category', 'name icon color');

    res.json({
      success: true,
      message: `تم ${product.available ? 'تفعيل' : 'إلغاء تفعيل'} المنتج بنجاح`,
      data: updatedProduct
    });
  } catch (error) {
    console.error('Toggle product error:', error);
    res.status(500).json({
      success: false,
      message: 'خطأ في تغيير حالة المنتج',
      error: error.message
    });
  }
});

// Delete product
router.delete('/:id', authenticateToken, async (req, res) => {
  try {
    const product = await Product.findById(req.params.id);

    if (!product) {
      return res.status(404).json({
        success: false,
        message: 'المنتج غير موجود'
      });
    }

    // Soft delete - mark as inactive instead of removing
    product.status = 'inactive';
    product.updatedBy = req.user._id;
    await product.save();

    res.json({
      success: true,
      message: 'تم حذف المنتج بنجاح'
    });
  } catch (error) {
    console.error('Delete product error:', error);
    res.status(500).json({
      success: false,
      message: 'خطأ في حذف المنتج',
      error: error.message
    });
  }
});

// Fix products availability (admin only)
router.post('/fix-availability', authenticateToken, async (req, res) => {
  try {
    // Check if user is admin
    if (req.user.role !== 'admin' && req.user.role !== 'manager') {
      return res.status(403).json({
        success: false,
        message: 'غير مصرح لك بهذه العملية'
      });
    }

    // Find all products with availability issues
    const products = await Product.find({ status: 'active' });
    let fixedCount = 0;

    for (const product of products) {
      let needsUpdate = false;

      // Fix missing or zero stock
      if (!product.stock || product.stock.quantity === undefined || product.stock.quantity === 0) {
        product.stock = {
          quantity: 100,
          unit: 'piece',
          lowStockAlert: 5
        };
        needsUpdate = true;
      }

      // Fix availability for products that should be available
      if (!product.available && product.status === 'active') {
        product.available = true;
        needsUpdate = true;
      }

      // Set trackInventory to false for existing products
      if (product.trackInventory === undefined) {
        product.trackInventory = false;
        needsUpdate = true;
      }

      if (needsUpdate) {
        await product.save();
        fixedCount++;
      }
    }

    res.json({
      success: true,
      message: `تم إصلاح ${fixedCount} منتج بنجاح`,
      data: {
        totalProducts: products.length,
        fixedProducts: fixedCount
      }
    });

  } catch (error) {
    console.error('Fix availability error:', error);
    res.status(500).json({
      success: false,
      message: 'خطأ في إصلاح المنتجات',
      error: error.message
    });
  }
});

module.exports = router;

const mongoose = require('mongoose');

const productSchema = new mongoose.Schema({
  name: {
    type: String,
    required: [true, 'اسم المنتج مطلوب'],
    trim: true,
    maxlength: [100, 'اسم المنتج لا يمكن أن يزيد عن 100 حرف']
  },
  description: {
    type: String,
    trim: true,
    maxlength: [500, 'الوصف لا يمكن أن يزيد عن 500 حرف']
  },
  price: {
    type: Number,
    required: [true, 'السعر مطلوب'],
    min: [0, 'السعر لا يمكن أن يكون سالب'],
    max: [10000, 'السعر لا يمكن أن يزيد عن 10000']
  },
  category: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Category',
    required: [true, 'الفئة مطلوبة']
  },
  categoryName: {
    type: String,
    required: [true, 'اسم الفئة مطلوب']
  },
  image: {
    type: String,
    default: '/images/default-product.jpg'
  },
  images: [{
    type: String
  }],
  available: {
    type: Boolean,
    default: true
  },
  featured: {
    type: Boolean,
    default: false
  },
  ingredients: [{
    type: String,
    trim: true
  }],
  allergens: [{
    type: String,
    trim: true
  }],
  nutritionInfo: {
    calories: { type: Number, min: 0 },
    protein: { type: Number, min: 0 },
    carbs: { type: Number, min: 0 },
    fat: { type: Number, min: 0 },
    sugar: { type: Number, min: 0 }
  },
  preparationTime: {
    type: Number, // in minutes
    min: [1, 'وقت التحضير يجب أن يكون دقيقة واحدة على الأقل'],
    max: [120, 'وقت التحضير لا يمكن أن يزيد عن 120 دقيقة']
  },
  tags: [{
    type: String,
    trim: true,
    lowercase: true
  }],
  rating: {
    average: { type: Number, default: 0, min: 0, max: 5 },
    count: { type: Number, default: 0, min: 0 }
  },
  stock: {
    quantity: { type: Number, default: 100, min: 0 }, // Default to 100 instead of 0
    unit: { type: String, default: 'piece' },
    lowStockAlert: { type: Number, default: 5, min: 0 }
  },
  trackInventory: {
    type: Boolean,
    default: false // Most cafe items don't need strict inventory tracking
  },
  sales: {
    totalSold: { type: Number, default: 0, min: 0 },
    revenue: { type: Number, default: 0, min: 0 }
  },
  status: {
    type: String,
    enum: ['active', 'inactive', 'discontinued'],
    default: 'active'
  },
  createdBy: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User'
  },
  updatedBy: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User'
  }
}, {
  timestamps: true,
  toJSON: { virtuals: true },
  toObject: { virtuals: true }
});

// Indexes for better performance
productSchema.index({ name: 'text', description: 'text' });
productSchema.index({ category: 1 });
productSchema.index({ available: 1 });
productSchema.index({ featured: 1 });
productSchema.index({ price: 1 });
productSchema.index({ 'rating.average': -1 });
productSchema.index({ createdAt: -1 });

// Virtual for low stock status
productSchema.virtual('isLowStock').get(function() {
  return this.stock.quantity <= this.stock.lowStockAlert;
});

// Virtual for out of stock status
productSchema.virtual('isOutOfStock').get(function() {
  return this.stock.quantity === 0;
});

// Pre-save middleware
productSchema.pre('save', function(next) {
  // Initialize stock if undefined
  if (!this.stock) {
    this.stock = {
      quantity: 100, // Default stock
      unit: 'piece',
      lowStockAlert: 5
    };
  }
  
  // Only auto-disable if explicitly set to track inventory and stock is 0
  // For most cafe items, availability should be manually controlled
  if (this.trackInventory === true && this.stock.quantity === 0) {
    this.available = false;
  }
  
  next();
});

// Static method to get available products
productSchema.statics.getAvailable = function() {
  return this.find({ 
    available: true, 
    status: 'active' 
  }).populate('category', 'name icon color');
};

// Static method to get featured products
productSchema.statics.getFeatured = function() {
  return this.find({ 
    featured: true, 
    available: true, 
    status: 'active' 
  }).populate('category', 'name icon color');
};

// Static method to search products
productSchema.statics.search = function(query) {
  return this.find({
    $text: { $search: query },
    available: true,
    status: 'active'
  }).populate('category', 'name icon color');
};

// Instance method to update rating
productSchema.methods.updateRating = function(newRating) {
  const totalRating = (this.rating.average * this.rating.count) + newRating;
  this.rating.count += 1;
  this.rating.average = totalRating / this.rating.count;
  return this.save();
};

// Instance method to update sales
productSchema.methods.recordSale = function(quantity, price) {
  this.sales.totalSold += quantity;
  this.sales.revenue += (quantity * price);
  
  // Update stock
  if (this.stock.quantity >= quantity) {
    this.stock.quantity -= quantity;
  }
  
  return this.save();
};

module.exports = mongoose.model('Product', productSchema);

// اختبار حالة الاتصال - Test Connection Status
// يقوم بإرسال طلب إلى الـ API ويطبع النتيجة

const fetch = require('node-fetch');

async function testConnectionStatus() {
  console.log('🔍 اختبار حالة الاتصال...');
  
  try {
    const response = await fetch('http://localhost:5001/api/health');
    
    console.log('📊 تفاصيل الاستجابة:');
    console.log('- Status:', response.status);
    console.log('- Status Text:', response.statusText);
    console.log('- Content-Type:', response.headers.get('content-type'));
    
    if (response.ok) {
      const data = await response.json();
      console.log('\n✅ بيانات الصحة:');
      console.log('- حالة الخادم:', data.status);
      console.log('- متصل بقاعدة البيانات:', data.database.connected);
      console.log('- اسم قاعدة البيانات:', data.database.name);
      console.log('- مضيف قاعدة البيانات:', data.database.host);
      
      // محاكاة ما يفعله مكون ConnectionStatus
      const apiResponse = { success: true, data: data };
      const isServerHealthy = apiResponse.success === true && data.status === 'healthy';
      const isDatabaseConnected = apiResponse.success === true && data.database.connected === true;
      
      console.log('\n🎯 النتائج:');
      console.log('- الخادم صحي:', isServerHealthy ? '✅ نعم' : '❌ لا');
      console.log('- قاعدة البيانات متصلة:', isDatabaseConnected ? '✅ نعم' : '❌ لا');
      console.log('- الحالة الإجمالية:', isServerHealthy && isDatabaseConnected ? '✅ متصل' : '❌ غير متصل');
      
      console.log('\n📱 ما سيظهر في الواجهة:');
      console.log(`- الخادم: ${isServerHealthy ? 'متصل' : 'غير متصل'}`);
      console.log(`- قاعدة البيانات: ${isDatabaseConnected ? 'متصل' : 'غير متصل'}`);
      
    } else {
      console.error('❌ خطأ في الاستجابة:', response.status, response.statusText);
    }
    
  } catch (error) {
    console.error('❌ خطأ في الاتصال:', error.message);
  }
}

// تشغيل الاختبار
testConnectionStatus();

# دليل ملفات CSS لشاشات النادل

## البنية الجديدة

تم إنشاء ملفات CSS منفصلة لكل مكون من مكونات شاشة النادل لضمان عدم التداخل وسهولة الصيانة.

## ملفات CSS المُنشأة

### 1. WaiterDrinksScreen.css
**المسار:** `src/styles/screens/WaiterDrinksScreen.css`
**الغرض:** تنسيقات شاشة المشروبات
**متغيرات CSS:** تبدأ بـ `--waiter-drinks-`
**الكلاس الأساسي:** `.waiter-drinks-screen`

### 2. WaiterOrdersScreen.css
**المسار:** `src/styles/screens/WaiterOrdersScreen.css`
**الغرض:** تنسيقات شاشة الطلبات
**متغيرات CSS:** تبدأ بـ `--waiter-orders-`
**الكلاس الأساسي:** `.waiter-orders-screen`

### 3. WaiterTablesScreen.css
**المسار:** `src/styles/screens/WaiterTablesScreen.css`
**الغرض:** تنسيقات شاشة الطاولات
**متغيرات CSS:** تبدأ بـ `--waiter-tables-`
**الكلاس الأساسي:** `.waiter-tables-screen`

### 4. WaiterCartScreen.css
**المسار:** `src/styles/screens/WaiterCartScreen.css`
**الغرض:** تنسيقات شاشة السلة
**متغيرات CSS:** تبدأ بـ `--waiter-cart-`
**الكلاس الأساسي:** `.waiter-cart-screen`

### 5. WaiterDiscountsScreen.css
**المسار:** `src/styles/screens/WaiterDiscountsScreen.css`
**الغرض:** تنسيقات شاشة طلبات الخصم
**متغيرات CSS:** تبدأ بـ `--waiter-discounts-`
**الكلاس الأساسي:** `.waiter-discounts-screen`

### 6. index.css
**المسار:** `src/styles/screens/index.css`
**الغرض:** ملف فهرس يجمع جميع الملفات + متغيرات مشتركة
**متغيرات CSS:** تبدأ بـ `--waiter-common-`

## نظام التسمية

### متغيرات CSS
كل ملف يحتوي على متغيرات فريدة تبدأ بـ:
- `--waiter-drinks-` للمشروبات
- `--waiter-orders-` للطلبات  
- `--waiter-tables-` للطاولات
- `--waiter-cart-` للسلة
- `--waiter-discounts-` لطلبات الخصم
- `--waiter-common-` للمتغيرات المشتركة

### أسماء الكلاسات
كل مكون يُلف في كلاس أساسي:
```css
.waiter-[screen-name]-screen .element-class
```

مثال:
```css
.waiter-drinks-screen .menu-item-card
.waiter-orders-screen .order-card
.waiter-tables-screen .table-card
```

## الاستخدام

### في المكونات
كل مكون يستورد ملف CSS الخاص به:

```tsx
// في WaiterDrinksScreen.tsx
import '../styles/screens/WaiterDrinksScreen.css';

// في WaiterOrdersScreen.tsx
import '../styles/screens/WaiterOrdersScreen.css';
```

### في الـ JSX
كل مكون يضع الكلاس الأساسي على العنصر الجذر:

```tsx
return (
  <div className="content-container waiter-drinks-screen">
    {/* محتوى المكون */}
  </div>
);
```

## المزايا

### 1. منع التداخل
- كل مكون له متغيرات CSS فريدة
- أسماء كلاسات مُسبقة بـ namespace
- لا توجد تضارب في التنسيقات

### 2. سهولة الصيانة
- كل شاشة لها ملف منفصل
- سهولة العثور على التنسيقات المطلوبة
- تغيير تنسيق شاشة لا يؤثر على الأخرى

### 3. إعادة الاستخدام
- متغيرات مشتركة في `index.css`
- نظام ألوان موحد لكل شاشة
- تنسيقات responsive منفصلة

### 4. الأداء
- تحميل CSS فقط عند الحاجة
- ملفات أصغر وأكثر تنظيماً
- تحسينات الأداء المضمنة

## نمط الألوان

كل شاشة لها نمط ألوان فريد:

| الشاشة | اللون الأساسي | اللون الثانوي | الاستخدام |
|--------|------------|-------------|----------|
| المشروبات | `#8B4513` (بني) | `#D2691E` (برتقالي بني) | دافئ ومريح للقهوة |
| الطلبات | `#007bff` (أزرق) | `#17a2b8` (أزرق فاتح) | مهني وموثوق |
| الطاولات | `#6f42c1` (بنفسجي) | `#9775c7` (بنفسجي فاتح) | أنيق ومتميز |
| السلة | `#fd7e14` (برتقالي) | `#ff922b` (برتقالي فاتح) | نشط ومحفز |
| طلبات الخصم | `#e83e8c` (وردي) | `#f093c2` (وردي فاتح) | مميز وملفت |

## إرشادات التطوير

### إضافة عنصر جديد
1. حدد الشاشة المناسبة
2. أضف التنسيق في الملف المقابل
3. استخدم متغيرات الشاشة المناسبة
4. اتبع نمط التسمية

### تعديل تنسيق موجود
1. ابحث عن الملف المناسب
2. عدل المتغيرات أو الكلاسات
3. تأكد من عدم التأثير على شاشات أخرى

### إضافة متغير مشترك
1. أضفه في `index.css` تحت `:root`
2. استخدم البادئة `--waiter-common-`
3. استخدمه في الملفات الأخرى

## الملفات المؤثرة

تم تحديث الملفات التالية لتستخدم النظام الجديد:

### المكونات
- `src/screens/WaiterDrinksScreen.tsx`
- `src/screens/WaiterOrdersScreen.tsx`
- `src/screens/WaiterTablesScreen.tsx`
- `src/screens/WaiterCartScreen.tsx`
- `src/screens/WaiterDiscountsScreen.tsx`

### ملفات CSS الجديدة
- `src/styles/screens/WaiterDrinksScreen.css`
- `src/styles/screens/WaiterOrdersScreen.css`
- `src/styles/screens/WaiterTablesScreen.css`
- `src/styles/screens/WaiterCartScreen.css`
- `src/styles/screens/WaiterDiscountsScreen.css`
- `src/styles/screens/index.css`

## نصائح للمطورين

1. **استخدم متغيرات CSS دائماً** بدلاً من القيم المباشرة
2. **اتبع نمط التسمية** لضمان عدم التداخل
3. **اختبر على جميع الشاشات** عند إضافة تنسيقات جديدة
4. **استخدم الـ responsive design** المناسب لكل شاشة
5. **حافظ على التناسق** في الألوان والخطوط

## دعم الـ Responsive

جميع ملفات CSS تدعم:
- الهواتف الذكية (< 480px)
- الأجهزة اللوحية (768px - 1024px)  
- الشاشات الكبيرة (> 1200px)

## الدعم والصيانة

هذا النظام يضمن:
- ✅ عدم تداخل التنسيقات
- ✅ سهولة الصيانة والتطوير
- ✅ أداء محسن
- ✅ مرونة في التخصيص
- ✅ دعم كامل للـ responsive design

// Unified Route Handler Middleware
// نظام موحد لمعالجة جميع الطلبات

const { 
  sendSuccess, 
  sendError, 
  sendNotFound, 
  handleDatabaseError,
  deviceDetection,
  setCorsHeaders,
  requestLogger
} = require('./unifiedResponse');

/**
 * Unified route wrapper
 * دالة موحدة لتطبيق جميع middleware على الـ routes
 */
const createUnifiedRoute = (handler) => {
  return async (req, res, next) => {
    try {
      // Apply CORS headers
      setCorsHeaders(req, res, () => {});
      
      // Detect device type
      deviceDetection(req, res, () => {});
      
      // Log request
      requestLogger(req, res, () => {});
      
      // Handle OPTIONS request
      if (req.method === 'OPTIONS') {
        return res.status(200).end();
      }
      
      // Execute the actual route handler
      await handler(req, res, next);
      
    } catch (error) {
      console.error('❌ Route Handler Error:', {
        path: req.path,
        method: req.method,
        error: error.message,
        stack: process.env.NODE_ENV === 'development' ? error.stack : undefined
      });
      
      // Handle different types of errors
      if (error.name === 'ValidationError') {
        return handleDatabaseError(res, error, `${req.method} ${req.path}`);
      } else if (error.name === 'CastError') {
        return sendError(res, 'معرف غير صحيح', 400, 'INVALID_ID');
      } else if (error.code === 11000) {
        return sendError(res, 'البيانات موجودة بالفعل', 409, 'DUPLICATE_DATA');
      } else if (error.message.includes('not found')) {
        return sendNotFound(res);
      } else {
        return handleDatabaseError(res, error, `${req.method} ${req.path}`);
      }
    }
  };
};

/**
 * Unified middleware for all routes
 * ميدلوير موحد لجميع الطلبات
 */
const applyUnifiedMiddleware = (router) => {
  // Apply to all routes in this router
  router.use((req, res, next) => {
    // CORS Headers
    res.header('Access-Control-Allow-Origin', '*');
    res.header('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE, OPTIONS, PATCH');
    res.header('Access-Control-Allow-Headers', 'Origin, X-Requested-With, Content-Type, Accept, Authorization, x-device-type, x-request-id');
    res.header('Access-Control-Allow-Credentials', 'true');
    res.header('Access-Control-Max-Age', '86400'); // 24 hours
    
    // Mobile specific headers
    res.header('Cache-Control', 'no-cache, no-store, must-revalidate');
    res.header('Pragma', 'no-cache');
    res.header('Expires', '0');
    
    // Device detection
    const userAgent = req.headers['user-agent'] || '';
    const isMobile = /Mobile|Android|iPhone|iPad|BlackBerry|IEMobile|Opera Mini/i.test(userAgent);
    const isTablet = /iPad|Android(?!.*Mobile)/i.test(userAgent);
    
    req.deviceInfo = {
      isMobile,
      isTablet,
      isDesktop: !isMobile && !isTablet,
      userAgent,
      type: isMobile ? 'mobile' : isTablet ? 'tablet' : 'desktop'
    };
    
    // Request logging
    if (process.env.NODE_ENV === 'development' || isMobile) {
      console.log(`📊 ${req.deviceInfo.type.toUpperCase()} Request:`, {
        method: req.method,
        path: req.path,
        query: req.query,
        hasBody: !!req.body && Object.keys(req.body).length > 0,
        contentType: req.headers['content-type'],
        timestamp: new Date().toISOString()
      });
    }
    
    // Handle preflight requests
    if (req.method === 'OPTIONS') {
      return res.status(200).end();
    }
    
    next();
  });
  
  return router;
};

/**
 * Unified response formatter
 * تنسيق موحد للاستجابات
 */
const formatResponse = (data, message = 'نجح', meta = null) => {
  const response = {
    success: true,
    message,
    data,
    timestamp: new Date().toISOString()
  };
  
  if (meta) {
    response.meta = meta;
  }
  
  return response;
};

/**
 * Unified error formatter
 * تنسيق موحد للأخطاء
 */
const formatError = (message, code = null, details = null) => {
  const response = {
    success: false,
    message,
    timestamp: new Date().toISOString()
  };
  
  if (code) {
    response.error = code;
  }
  
  if (details && process.env.NODE_ENV === 'development') {
    response.details = details;
  }
  
  return response;
};

/**
 * Async handler wrapper
 * غلاف للدوال غير المتزامنة
 */
const asyncHandler = (fn) => {
  return (req, res, next) => {
    Promise.resolve(fn(req, res, next)).catch(next);
  };
};

/**
 * Unified validation middleware
 * ميدلوير موحد للتحقق من البيانات
 */
const validateRequest = (schema) => {
  return (req, res, next) => {
    const { error, value } = schema.validate(req.body);
    
    if (error) {
      const details = error.details.map(detail => ({
        field: detail.path.join('.'),
        message: detail.message,
        value: detail.context.value
      }));
      
      return res.status(400).json(formatError(
        'خطأ في التحقق من البيانات',
        'VALIDATION_ERROR',
        details
      ));
    }
    
    req.validatedBody = value;
    next();
  };
};

module.exports = {
  createUnifiedRoute,
  applyUnifiedMiddleware,
  formatResponse,
  formatError,
  asyncHandler,
  validateRequest
};

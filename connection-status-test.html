<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار منطق ConnectionStatus - نظام المقهى</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            padding: 20px;
            direction: rtl;
            text-align: right;
            background: #f5f5f5;
        }
        .test-container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .test-section {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
            background: #fafafa;
        }
        .result {
            margin: 10px 0;
            padding: 10px;
            border-radius: 3px;
            font-family: monospace;
        }
        .success {
            background-color: #d4edda;
            border: 1px solid #c3e6cb;
            color: #155724;
        }
        .error {
            background-color: #f8d7da;
            border: 1px solid #f5c6cb;
            color: #721c24;
        }
        .info {
            background-color: #d1ecf1;
            border: 1px solid #bee5eb;
            color: #0c5460;
        }
        button {
            padding: 10px 20px;
            margin: 5px;
            border: none;
            border-radius: 3px;
            cursor: pointer;
            background-color: #007bff;
            color: white;
            font-size: 14px;
        }
        button:hover {
            background-color: #0056b3;
        }
        button:disabled {
            background-color: #6c757d;
            cursor: not-allowed;
        }
        pre {
            background-color: #f8f9fa;
            padding: 10px;
            border-radius: 3px;
            overflow-x: auto;
            font-size: 12px;
            white-space: pre-wrap;
        }
        .step {
            margin: 10px 0;
            padding: 10px;
            border-left: 4px solid #007bff;
            background: #f8f9fa;
        }
        .connection-status {
            padding: 15px;
            border-radius: 5px;
            margin: 10px 0;
        }
        .connection-status.connected {
            background-color: #d4edda;
            border: 1px solid #c3e6cb;
        }
        .connection-status.error {
            background-color: #f8d7da;
            border: 1px solid #f5c6cb;
        }
        .status-indicator {
            display: inline-block;
            width: 10px;
            height: 10px;
            border-radius: 50%;
            margin-left: 10px;
        }
        .status-indicator.success {
            background-color: #28a745;
        }
        .status-indicator.error {
            background-color: #dc3545;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>🔍 اختبار منطق ConnectionStatus</h1>
        <p>هذا الاختبار يحاكي تماماً نفس منطق الكود في ConnectionStatus.tsx</p>
        
        <div class="test-section">
            <h3>خطوات الاختبار</h3>
            <button onclick="runCompleteTest()" id="testBtn">🚀 تشغيل الاختبار الكامل</button>
            <div id="test-steps"></div>
        </div>

        <div class="test-section">
            <h3>نتيجة محاكاة ConnectionStatus</h3>
            <div id="connection-result"></div>
        </div>

        <div class="test-section">
            <h3>تفاصيل المتغيرات</h3>
            <div id="variables-info"></div>
        </div>

        <div class="test-section">
            <h3>سجل العمليات (Console Log)</h3>
            <div id="console-log"></div>
        </div>
    </div>

    <script>
        // محاكاة إعدادات التطبيق الأصلي
        const APP_CONFIG = {
            API: {
                BASE_URL: 'http://localhost:5000',
                TIMEOUT: 30000
            }
        };

        // محاكاة getApiUrl function
        const getApiUrl = (endpoint) => {
            return `${APP_CONFIG.API.BASE_URL}${endpoint.startsWith('/') ? endpoint : '/' + endpoint}`;
        };

        // محاكاة makeRequest function (نفس المنطق من api.ts)
        const makeRequest = async (endpoint, options = {}) => {
            const controller = new AbortController();
            const timeoutId = setTimeout(() => controller.abort(), APP_CONFIG.API.TIMEOUT);

            try {
                const token = localStorage.getItem('token') || localStorage.getItem('authToken');
                const fullUrl = getApiUrl(endpoint);
                
                logToConsole('🌐 makeRequest called:', {
                    endpoint,
                    'API_BASE_URL': APP_CONFIG.API.BASE_URL,
                    'full URL': fullUrl,
                    options
                });

                const response = await fetch(fullUrl, {
                    ...options,
                    signal: controller.signal,
                    headers: {
                        'Content-Type': 'application/json',
                        'Accept': 'application/json',
                        'x-request-id': Math.random().toString(36).substring(7),
                        ...(token && { 'Authorization': `Bearer ${token}` }),
                        ...options.headers,
                    },
                });

                logToConsole('🌐 Response received:', {
                    status: response.status,
                    statusText: response.statusText,
                    ok: response.ok,
                    headers: Object.fromEntries(response.headers.entries())
                });

                clearTimeout(timeoutId);

                if (!response.ok) {
                    let errorMessage = `خطأ HTTP ${response.status}`;
                    try {
                        const errorData = await response.clone().json();
                        errorMessage = errorData.message || errorData.error || errorMessage;
                    } catch {
                        // استخدم الرسالة الافتراضية
                    }
                    throw new Error(errorMessage);
                }

                const data = await response.json();
                logToConsole('🌐 Response data:', data);
                return { success: true, data };
            } catch (error) {
                clearTimeout(timeoutId);
                logToConsole('❌ API Request Error:', error);
                return {
                    success: false,
                    error: error.message || 'حدث خطأ في الشبكة'
                };
            }
        };

        // محاكاة checkServerHealth function
        const checkServerHealth = async () => {
            logToConsole('🏥 checkServerHealth called with API_BASE_URL:', APP_CONFIG.API.BASE_URL);
            logToConsole('🏥 Environment VITE_API_URL:', 'http://localhost:5000'); // محاكاة
            return makeRequest('/health');
        };

        // محاكاة state من ConnectionStatus.tsx
        let connectionState = {
            isConnected: false,
            serverStatus: 'جاري الفحص...',
            databaseStatus: 'جاري الفحص...',
            lastChecked: null,
            error: null
        };

        // Log console محلي
        const logs = [];
        function logToConsole(message, data = null) {
            const timestamp = new Date().toLocaleTimeString();
            const logEntry = { timestamp, message, data };
            logs.push(logEntry);
            console.log(message, data);
            updateConsoleDisplay();
        }

        function updateConsoleDisplay() {
            const consoleDiv = document.getElementById('console-log');
            consoleDiv.innerHTML = logs.map(log => `
                <div class="result info">
                    <strong>[${log.timestamp}]</strong> ${log.message}
                    ${log.data ? `<pre>${JSON.stringify(log.data, null, 2)}</pre>` : ''}
                </div>
            `).join('');
            consoleDiv.scrollTop = consoleDiv.scrollHeight;
        }

        // محاكاة دالة checkConnection من ConnectionStatus.tsx
        const checkConnection = async () => {
            try {
                logToConsole('🔍 Starting health check...');
                logToConsole('🔍 API URL being used:', APP_CONFIG.API.BASE_URL);
                const response = await checkServerHealth();
                logToConsole('🔍 Health check response:', response);

                // تحقق من صحة الاستجابة - makeRequest يرجع { success: true, data: healthData }
                const healthData = response.data;
                logToConsole('📊 Health data:', healthData);
                logToConsole('📊 Response success:', response.success);
                logToConsole('📊 Health status:', healthData?.status);
                logToConsole('📊 Database connected:', healthData?.database?.connected);
                
                // التحقق من حالة الاتصال
                const isServerHealthy = response.success === true && healthData?.status === 'healthy';
                const isDatabaseConnected = response.success === true && healthData?.database?.connected === true;

                logToConsole('✅ Server healthy:', isServerHealthy);
                logToConsole('✅ Database connected:', isDatabaseConnected);
                logToConsole('✅ Overall connection:', isServerHealthy && isDatabaseConnected);
                logToConsole('🔍 Response.success check:', response.success === true);
                logToConsole('🔍 HealthData.status check:', healthData?.status === 'healthy');
                logToConsole('🔍 Database.connected check:', healthData?.database?.connected === true);

                connectionState = {
                    isConnected: isServerHealthy && isDatabaseConnected,
                    serverStatus: isServerHealthy ? 'متصل' : 'غير متصل',
                    databaseStatus: isDatabaseConnected ? 'متصل' : 'غير متصل',
                    lastChecked: new Date(),
                    error: null
                };

                updateConnectionDisplay();
                updateVariablesDisplay();
                return connectionState;
            } catch (error) {
                logToConsole('❌ Connection check failed:', error);
                connectionState = {
                    isConnected: false,
                    serverStatus: 'غير متصل',
                    databaseStatus: 'غير متصل',
                    lastChecked: new Date(),
                    error: error.message || 'فشل في الاتصال بالخادم'
                };
                updateConnectionDisplay();
                updateVariablesDisplay();
                return connectionState;
            }
        };

        function updateConnectionDisplay() {
            const resultDiv = document.getElementById('connection-result');
            const statusClass = connectionState.isConnected ? 'connected' : 'error';
            
            resultDiv.innerHTML = `
                <div class="connection-status ${statusClass}">
                    <div>
                        <span class="status-indicator ${connectionState.isConnected ? 'success' : 'error'}"></span>
                        <strong>
                            ${connectionState.isConnected ? 'متصل بالخادم' : 'غير متصل بالخادم'}
                        </strong>
                    </div>
                    <div style="margin-top: 10px;">
                        <span>الخادم: ${connectionState.serverStatus}</span><br/>
                        <span>قاعدة البيانات: ${connectionState.databaseStatus}</span>
                        ${connectionState.error ? `<div style="color: red; margin-top: 5px;">${connectionState.error}</div>` : ''}
                    </div>
                    <div style="margin-top: 10px; font-size: 12px; color: #666;">
                        ${connectionState.lastChecked
                            ? `آخر فحص: ${connectionState.lastChecked.toLocaleTimeString()}`
                            : 'لم يتم الفحص بعد'}
                    </div>
                </div>
            `;
        }

        function updateVariablesDisplay() {
            const varsDiv = document.getElementById('variables-info');
            varsDiv.innerHTML = `
                <div class="result info">
                    <h4>حالة المتغيرات الحالية:</h4>
                    <pre>${JSON.stringify({
                        'connectionState': connectionState,
                        'APP_CONFIG.API.BASE_URL': APP_CONFIG.API.BASE_URL,
                        'localStorage.token': localStorage.getItem('token') ? 'موجود' : 'غير موجود',
                        'localStorage.authToken': localStorage.getItem('authToken') ? 'موجود' : 'غير موجود'
                    }, null, 2)}</pre>
                </div>
            `;
        }

        async function runCompleteTest() {
            const testBtn = document.getElementById('testBtn');
            const stepsDiv = document.getElementById('test-steps');
            
            testBtn.disabled = true;
            testBtn.textContent = 'جاري الاختبار...';
            
            // مسح السجلات السابقة
            logs.length = 0;
            updateConsoleDisplay();
            
            stepsDiv.innerHTML = `
                <div class="step">الخطوة 1: بدء اختبار الاتصال...</div>
            `;
            
            try {
                // اختبار مباشر للـ /health endpoint
                stepsDiv.innerHTML += `<div class="step">الخطوة 2: اختبار /health endpoint مباشرة...</div>`;
                const directTest = await fetch('http://localhost:5000/health');
                const directData = await directTest.json();
                
                stepsDiv.innerHTML += `
                    <div class="step">
                        الخطوة 3: نتيجة الاختبار المباشر:
                        <div class="result success">
                            Status: ${directTest.status}<br>
                            Response: <pre>${JSON.stringify(directData, null, 2)}</pre>
                        </div>
                    </div>
                `;
                
                // اختبار منطق checkConnection
                stepsDiv.innerHTML += `<div class="step">الخطوة 4: اختبار منطق checkConnection...</div>`;
                const result = await checkConnection();
                
                stepsDiv.innerHTML += `
                    <div class="step">
                        الخطوة 5: نتيجة checkConnection:
                        <div class="result ${result.isConnected ? 'success' : 'error'}">
                            <strong>النتيجة النهائية:</strong> ${result.isConnected ? 'متصل' : 'غير متصل'}<br>
                            الخادم: ${result.serverStatus}<br>
                            قاعدة البيانات: ${result.databaseStatus}
                        </div>
                    </div>
                `;
                
            } catch (error) {
                stepsDiv.innerHTML += `
                    <div class="step">
                        <div class="result error">
                            خطأ في الاختبار: ${error.message}
                        </div>
                    </div>
                `;
            }
            
            testBtn.disabled = false;
            testBtn.textContent = '🔄 إعادة تشغيل الاختبار';
        }

        // تشغيل اختبار أولي عند تحميل الصفحة
        window.onload = function() {
            updateVariablesDisplay();
            updateConnectionDisplay();
            
            // تشغيل اختبار بسيط بعد ثانيتين
            setTimeout(() => {
                runCompleteTest();
            }, 2000);
        };
    </script>
</body>
</html>

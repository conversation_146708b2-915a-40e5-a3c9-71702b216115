/* ملخص سريع للتغييرات المطبقة على ManagerDashboard.css */

/* 1. إصلاح التخطيط الأساسي */
.manager-dashboard {
  overflow-x: hidden; /* منع التمرير الأفقي */
}

.dashboard-content {
  display: flex;
  gap: 0; /* إزالة المسافات الخالية */
  min-height: calc(100vh - 80px);
}

.manager-sidebar {
  width: 280px;
  flex-shrink: 0; /* منع تقلص القائمة */
  /* تم إزالة position: fixed للشاشات الكبيرة */
}

.manager-main {
  flex: 1;
  width: 100%;
  box-sizing: border-box;
  /* تم إزالة margin-right: 280px */
}

/* 2. تحسينات الـ Responsive */

/* الشاشات الكبيرة (769px+) */
@media (min-width: 769px) {
  .manager-sidebar {
    position: relative; /* بدلاً من fixed */
    transform: translateX(0);
  }
  .sidebar-toggle {
    display: none; /* إخفاء زر التبديل */
  }
}

/* الهاتف (768px-) */
@media (max-width: 768px) {
  .manager-sidebar {
    position: fixed;
    transform: translateX(100%); /* مخفية افتراضياً */
    z-index: 999;
  }
  .manager-sidebar.open {
    transform: translateX(0); /* ظاهرة عند الفتح */
  }
  .manager-main {
    padding: 1rem; /* padding أصغر للهاتف */
  }
}

/* التابلت (769px-1024px) */
@media (min-width: 769px) and (max-width: 1024px) {
  .manager-sidebar {
    width: 260px; /* عرض أصغر */
  }
  .manager-main {
    padding: 1.5rem;
  }
}

/* الشاشات الكبيرة جداً (1400px+) */
@media (min-width: 1400px) {
  .manager-sidebar {
    width: 320px; /* عرض أكبر */
  }
  .manager-main {
    padding: 3rem;
  }
}

/* 3. تحسينات إضافية */
.manager-main * {
  box-sizing: border-box; /* منع تداخل المحتوى */
}

.stats-grid,
.summary-cards,
.data-table {
  width: 100%;
  max-width: 100%;
  overflow-x: auto; /* تمرير ذكي للجداول */
}

/* 4. إصلاح خطأ syntax */
/* تم إصلاح السطر 2896: إضافة solid #e2e8f0; للـ border */

import React, { useState, useEffect } from 'react';
import { useToast } from './hooks/useToast';
import { ToastContainer } from './components/Toast';
import { authenticatedGet, authenticatedPost, authenticatedPut, authenticatedDelete } from './utils/apiHelpers';

interface Item {
  _id?: string;
  name: string;
  quantity: number;
  min: number;
  price?: number;
  last_updated?: string;
  unit?: string;
}

export default function Inventory() {
  const [items, setItems] = useState<Item[]>([]);
  const [name, setName] = useState('');
  const [quantity, setQuantity] = useState(1);
  const [min, setMin] = useState(1);
  const [price, setPrice] = useState(0);
  const [unit, setUnit] = useState('قطعة');
  const [loading, setLoading] = useState(false);
  const [editingItem, setEditingItem] = useState<Item | null>(null);
  const { toasts, removeToast, showSuccess, showError } = useToast();

  // جلب الأصناف من السيرفر عند التحميل
  useEffect(() => {
    fetchItems();
  }, []);

  const fetchItems = async () => {
    try {
      const response = await authenticatedGet('/api/v1/inventory');
      
      // Handle both response formats for compatibility
      let items = response;
      if (response && typeof response === 'object' && response.data) {
        items = response.data;
      }
      
      // Ensure we have an array
      if (Array.isArray(items)) {
        setItems(items);
      } else {
        console.error('Inventory response is not an array:', items);
        setItems([]);
        showError('تنسيق البيانات غير صحيح');
      }
    } catch (error) {
      console.error('خطأ في جلب المخزون:', error);
      showError('فشل في جلب بيانات المخزون');
    }
  };

  const handleAdd = async (e: React.FormEvent) => {
    e.preventDefault();
    setLoading(true);
    try {
      await authenticatedPost('/api/v1/inventory', {
        name,
        quantity,
        min,
        price,
        unit
      });
      setName('');
      setQuantity(1);
      setMin(1);
      setPrice(0);
      setUnit('قطعة');
      await fetchItems();
      showSuccess('تم إضافة الصنف بنجاح');
    } catch (error) {
      console.error('خطأ في إضافة الصنف:', error);
      showError('فشل في إضافة الصنف');
    }
    setLoading(false);
  };

  const handleUpdate = async (item: Item) => {
    setLoading(true);
    try {
      await authenticatedPut(`/api/v1/inventory/${item._id}`, item);
      setEditingItem(null);
      await fetchItems();
      showSuccess('تم تحديث الصنف بنجاح');
    } catch (error) {
      console.error('خطأ في تحديث الصنف:', error);
      showError('فشل في تحديث الصنف');
    }
    setLoading(false);
  };

  const handleDelete = async (id: string) => {
    if (!confirm('هل أنت متأكد من حذف هذا الصنف؟')) return;

    setLoading(true);
    try {
      await authenticatedDelete(`/api/v1/inventory/${id}`);
      await fetchItems();
      showSuccess('تم حذف الصنف بنجاح');
    } catch (error) {
      console.error('خطأ في حذف الصنف:', error);
      showError('فشل في حذف الصنف');
    }
    setLoading(false);
  };

  return (
    <div>
      <div>
        <h2>إدارة المخزون</h2>
        <p>
          إضافة وإدارة أصناف المخزون مع تتبع الكميات والحد الأدنى
        </p>

        <form onSubmit={handleAdd}>
          <div>
            <label htmlFor="item-name">
              اسم الصنف:
            </label>
            <input
              id="item-name"
              type="text"
              value={name}
              onChange={e => setName(e.target.value)}
              required
              placeholder="أدخل اسم الصنف"
              aria-describedby="item-name-desc"
            />
            <span id="item-name-desc">أدخل اسم الصنف المراد إضافته للمخزون</span>
          </div>

          <div>
            <label htmlFor="item-unit">
              الوحدة:
            </label>
            <select
              id="item-unit"
              value={unit}
              onChange={e => setUnit(e.target.value)}
              aria-describedby="item-unit-desc"
            >
              <option value="قطعة">قطعة</option>
              <option value="كيلو">كيلو</option>
              <option value="لتر">لتر</option>
              <option value="حبة">حبة</option>
              <option value="علبة">علبة</option>
              <option value="كيس">كيس</option>
            </select>
            <span id="item-unit-desc">اختر وحدة قياس الصنف</span>
          </div>

          <div>
            <label htmlFor="item-quantity">
              الكمية الحالية:
            </label>
            <input
              id="item-quantity"
              type="number"
              value={quantity}
              onChange={e => setQuantity(Number(e.target.value))}
              min="0"
              required
              placeholder="الكمية المتوفرة"
              aria-describedby="item-quantity-desc"
            />
            <span id="item-quantity-desc">أدخل الكمية المتوفرة حالياً من هذا الصنف</span>
          </div>

          <div>
            <label htmlFor="item-min">
              الحد الأدنى:
            </label>
            <input
              id="item-min"
              type="number"
              value={min}
              onChange={e => setMin(Number(e.target.value))}
              min="1"
              required
              placeholder="الحد الأدنى للتنبيه"
              aria-describedby="item-min-desc"
            />
            <span id="item-min-desc">أدخل الحد الأدنى للكمية قبل التنبيه</span>
          </div>

          <div>
            <label htmlFor="item-price">
              السعر (اختياري):
            </label>
            <input
              id="item-price"
              type="number"
              value={price}
              onChange={e => setPrice(Number(e.target.value))}
              min="0"
              step="0.01"
              placeholder="سعر الوحدة"
              aria-describedby="item-price-desc"
            />
            <span id="item-price-desc">أدخل سعر الوحدة الواحدة من الصنف (اختياري)</span>
          </div>

          <button 
            type="submit" 
            disabled={loading}
            aria-describedby="add-button-desc"
          >
            {loading ? 'جاري الإضافة...' : 'إضافة صنف'}
          </button>
          <span id="add-button-desc">اضغط لإضافة الصنف الجديد للمخزون</span>
        </form>

        <div>
          <table role="table" aria-label="جدول أصناف المخزون">
            <thead>
              <tr>
                <th scope="col">الصنف</th>
                <th scope="col">الوحدة</th>
                <th scope="col">الكمية</th>
                <th scope="col">الحد الأدنى</th>
                <th scope="col">الحالة</th>
                <th scope="col">آخر تحديث</th>
                <th scope="col">الإجراءات</th>
              </tr>
            </thead>
            <tbody>
              {items.map(item => (
                <tr 
                  key={item._id}
                >
                  <td>
                    {editingItem?._id === item._id ? (
                      <input
                        type="text"
                        value={editingItem?.name || ''}
                        onChange={e => editingItem && setEditingItem({...editingItem, name: e.target.value})}
                        aria-label={`تعديل اسم الصنف ${item.name}`}
                      />
                    ) : (
                      item.name
                    )}
                  </td>
                  <td>
                    {editingItem?._id === item._id ? (
                      <select
                        value={editingItem?.unit || 'قطعة'}
                        onChange={e => editingItem && setEditingItem({...editingItem, unit: e.target.value})}
                        aria-label={`تعديل وحدة الصنف ${item.name}`}
                      >
                        <option value="قطعة">قطعة</option>
                        <option value="كيلو">كيلو</option>
                        <option value="لتر">لتر</option>
                        <option value="حبة">حبة</option>
                        <option value="علبة">علبة</option>
                        <option value="كيس">كيس</option>
                      </select>
                    ) : (
                      item.unit || 'قطعة'
                    )}
                  </td>
                  <td>
                    {editingItem?._id === item._id ? (
                      <input
                        type="number"
                        value={editingItem?.quantity || 0}
                        onChange={e => editingItem && setEditingItem({...editingItem, quantity: Number(e.target.value)})}
                        min="0"
                        aria-label={`تعديل كمية الصنف ${item.name}`}
                      />
                    ) : (
                      item.quantity
                    )}
                  </td>
                  <td>
                    {editingItem?._id === item._id ? (
                      <input
                        type="number"
                        value={editingItem?.min || 0}
                        onChange={e => editingItem && setEditingItem({...editingItem, min: Number(e.target.value)})}
                        min="1"
                        aria-label={`تعديل الحد الأدنى للصنف ${item.name}`}
                      />
                    ) : (
                      item.min
                    )}
                  </td>
                  <td>
                    {item.quantity <= item.min ? (
                      <span>⚠️ نفدت الكمية</span>
                    ) : item.quantity <= item.min * 1.5 ? (
                      <span>⚡ قريب من النفاد</span>
                    ) : (
                      <span>✅ متوفر</span>
                    )}
                  </td>
                  <td>
                    {item.last_updated ? new Date(item.last_updated).toLocaleString('ar-EG') : 'غير محدد'}
                  </td>
                  <td>
                    {editingItem?._id === item._id ? (
                      <div>
                        <button
                          onClick={() => editingItem && handleUpdate(editingItem)}
                          aria-label={`حفظ تعديلات الصنف ${item.name}`}
                        >
                          حفظ
                        </button>
                        <button
                          onClick={() => setEditingItem(null)}
                          aria-label={`إلغاء تعديل الصنف ${item.name}`}
                        >
                          إلغاء
                        </button>
                      </div>
                    ) : (
                      <div>
                        <button
                          onClick={() => setEditingItem(item)}
                          aria-label={`تعديل الصنف ${item.name}`}
                        >
                          تعديل
                        </button>
                        <button
                          onClick={() => handleDelete(item._id!)}
                          aria-label={`حذف الصنف ${item.name}`}
                        >
                          حذف
                        </button>
                      </div>
                    )}
                  </td>
                </tr>
              ))}
              {items.length === 0 && (
                <tr>
                  <td colSpan={7}>
                    لا توجد أصناف في المخزون
                  </td>
                </tr>
              )}
            </tbody>
          </table>
        </div>
      </div>
      
      <ToastContainer toasts={toasts} onRemove={removeToast} />
    </div>
  );
}

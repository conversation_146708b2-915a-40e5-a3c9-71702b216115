const mongoose = require('mongoose');

const categorySchema = new mongoose.Schema({
  name: {
    type: String,
    required: [true, 'اسم الفئة مطلوب'],
    unique: true,
    trim: true,
    maxlength: [50, 'اسم الفئة لا يمكن أن يزيد عن 50 حرف']
  },
  description: {
    type: String,
    trim: true,
    maxlength: [200, 'الوصف لا يمكن أن يزيد عن 200 حرف']
  },
  icon: {
    type: String,
    default: '📦'
  },
  color: {
    type: String,
    default: '#6B7280',
    match: [/^#([A-Fa-f0-9]{6}|[A-Fa-f0-9]{3})$/, 'اللون يجب أن يكون بصيغة hex صحيحة']
  },
  image: {
    type: String,
    default: null
  },
  active: {
    type: Boolean,
    default: true
  },
  featured: {
    type: Boolean,
    default: false
  },
  sortOrder: {
    type: Number,
    default: 0
  },
  parentCategory: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Category',
    default: null
  },
  tags: [{
    type: String,
    trim: true,
    lowercase: true
  }],
  seo: {
    slug: {
      type: String,
      unique: true,
      sparse: true
    },
    metaTitle: String,
    metaDescription: String
  },
  createdBy: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User'
  },
  updatedBy: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User'
  }
}, {
  timestamps: true,
  toJSON: { virtuals: true },
  toObject: { virtuals: true }
});

// Indexes
categorySchema.index({ name: 1 });
categorySchema.index({ active: 1 });
categorySchema.index({ featured: 1 });
categorySchema.index({ sortOrder: 1 });
categorySchema.index({ parentCategory: 1 });

// Virtual for subcategories
categorySchema.virtual('subcategories', {
  ref: 'Category',
  localField: '_id',
  foreignField: 'parentCategory'
});

// Virtual for products count
categorySchema.virtual('productsCount', {
  ref: 'Product',
  localField: '_id',
  foreignField: 'category',
  count: true
});

// Pre-save middleware to generate slug
categorySchema.pre('save', function(next) {
  if (this.isModified('name') && !this.seo.slug) {
    this.seo.slug = this.name
      .toLowerCase()
      .replace(/[^a-z0-9\u0600-\u06FF]/g, '-')
      .replace(/-+/g, '-')
      .replace(/^-|-$/g, '');
  }
  next();
});

// Static method to get active categories
categorySchema.statics.getActive = function() {
  return this.find({ active: true }).sort({ sortOrder: 1, name: 1 });
};

// Static method to get featured categories
categorySchema.statics.getFeatured = function() {
  return this.find({ featured: true, active: true }).sort({ sortOrder: 1 });
};

// Static method to get main categories (no parent)
categorySchema.statics.getMainCategories = function() {
  return this.find({ 
    parentCategory: null, 
    active: true 
  }).sort({ sortOrder: 1, name: 1 });
};

// Instance method to get all products in this category
categorySchema.methods.getProducts = function() {
  return mongoose.model('Product').find({ 
    category: this._id,
    status: 'active'
  });
};

// Instance method to get subcategories
categorySchema.methods.getSubcategories = function() {
  return this.constructor.find({ 
    parentCategory: this._id,
    active: true 
  }).sort({ sortOrder: 1, name: 1 });
};

module.exports = mongoose.model('Category', categorySchema);

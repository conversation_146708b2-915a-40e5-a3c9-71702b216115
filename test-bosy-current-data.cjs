const fetch = require('node-fetch');

class BosyDataTester {
  constructor() {
    this.baseURL = 'https://deshacoffee-production.up.railway.app';
    this.frontendURL = 'https://desha-coffee.vercel.app';
    this.waiterId = '************************'; // Bosy ID
    this.username = '<PERSON><PERSON>';
    this.password = '253040';
    this.targetTables = ['1', '2', '29'];
  }

  async testCurrentData() {
    console.log('🔍 اختبار البيانات الحالية للنادلة بوسي...\n');
    console.log('='.repeat(60));

    // 1. تسجيل الدخول
    const authResult = await this.login();
    if (!authResult.success) {
      console.log('❌ فشل في تسجيل الدخول');
      return;
    }

    console.log('\n' + '-'.repeat(40) + '\n');

    // 2. جلب البيانات
    await this.fetchOrdersData(authResult.token);
    console.log('\n' + '-'.repeat(40) + '\n');
    await this.fetchTablesData(authResult.token);

    console.log('\n' + '='.repeat(60));
    console.log('✅ اكتمل الاختبار - يمكن الآن تطبيق الإصلاح');
  }

  async login() {
    console.log('🔐 تسجيل الدخول...');
    
    try {
      const response = await fetch(`${this.baseURL}/api/auth/login`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          username: this.username,
          password: this.password,
          role: 'نادل'
        })
      });

      const result = await response.json();
      
      if (response.ok && result.token) {
        console.log('✅ تم تسجيل الدخول بنجاح');
        console.log('👤 معرف النادل:', result.user?.id || 'غير محدد');
        return { success: true, token: result.token, user: result.user };
      } else {
        console.log('❌ فشل تسجيل الدخول:', result.message);
        return { success: false };
      }
    } catch (error) {
      console.log('❌ خطأ في تسجيل الدخول:', error.message);
      return { success: false };
    }
  }

  async fetchOrdersData(token) {
    console.log('📋 جلب طلبات النادلة بوسي...');
    
    try {
      const response = await fetch(`${this.baseURL}/api/v1/orders`, {
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        }
      });

      const result = await response.json();
      const allOrders = Array.isArray(result) ? result : (result.data || []);
      
      console.log(`📊 إجمالي الطلبات في النظام: ${allOrders.length}`);

      // فلترة طلبات بوسي
      const bosyOrders = allOrders.filter(order => {
        return order.waiterId === this.waiterId || 
               order.waiterName === 'بوسي' ||
               order.waiterName === 'Bosy';
      });

      console.log(`👤 طلبات النادلة بوسي: ${bosyOrders.length}`);

      // فحص الطاولات المستهدفة
      const targetTableOrders = bosyOrders.filter(order => 
        this.targetTables.includes(String(order.tableNumber))
      );

      console.log(`🏓 طلبات الطاولات 1,2,29: ${targetTableOrders.length}`);

      if (targetTableOrders.length > 0) {
        console.log('\n📝 تفاصيل الطلبات:');
        targetTableOrders.forEach(order => {
          console.log(`  طاولة ${order.tableNumber}: طلب #${order.orderNumber || order._id?.slice(-6)}`);
          console.log(`    - الحالة: ${order.status}`);
          console.log(`    - المبلغ: ${order.totalPrice} جنيه`);
          console.log(`    - النادل: ${order.waiterName} (${order.waiterId})`);
          console.log(`    - التاريخ: ${new Date(order.createdAt).toLocaleString('ar-EG')}`);
          console.log('');
        });

        // حساب الإجماليات
        const totalsByTable = {};
        targetTableOrders.forEach(order => {
          const table = order.tableNumber;
          if (!totalsByTable[table]) {
            totalsByTable[table] = { count: 0, total: 0 };
          }
          totalsByTable[table].count++;
          totalsByTable[table].total += order.totalPrice || 0;
        });

        console.log('📊 إجماليات الطاولات:');
        Object.entries(totalsByTable).forEach(([table, data]) => {
          console.log(`  طاولة ${table}: ${data.count} طلب، ${data.total.toFixed(2)} جنيه`);
        });
      } else {
        console.log('⚠️ لا توجد طلبات للطاولات المستهدفة');
      }

    } catch (error) {
      console.log('❌ خطأ في جلب الطلبات:', error.message);
    }
  }

  async fetchTablesData(token) {
    console.log('🏓 جلب بيانات الطاولات...');
    
    try {
      const response = await fetch(`${this.baseURL}/api/v1/table-accounts`, {
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        }
      });

      const result = await response.json();
      const allTables = Array.isArray(result) ? result : (result.data || []);
      
      console.log(`📊 إجمالي الطاولات في النظام: ${allTables.length}`);

      // فلترة طاولات بوسي
      const bosyTables = allTables.filter(table => {
        return table.waiterId === this.waiterId || 
               table.waiterName === 'بوسي' ||
               table.waiterName === 'Bosy';
      });

      console.log(`👤 طاولات النادلة بوسي: ${bosyTables.length}`);

      // فحص الطاولات المستهدفة
      const targetTables = bosyTables.filter(table => 
        this.targetTables.includes(String(table.tableNumber))
      );

      console.log(`🎯 الطاولات المستهدفة (1,2,29): ${targetTables.length}`);

      if (targetTables.length > 0) {
        console.log('\n📝 تفاصيل الطاولات:');
        targetTables.forEach(table => {
          console.log(`  طاولة ${table.tableNumber}:`);
          console.log(`    - الحالة: ${table.status} (${table.isOpen ? 'مفتوحة' : 'مغلقة'})`);
          console.log(`    - النادل: ${table.waiterName} (${table.waiterId})`);
          console.log(`    - العميل: ${table.customerName || 'غير محدد'}`);
          console.log(`    - عدد الطلبات: ${table.orders?.length || 0}`);
          console.log(`    - إجمالي المبلغ: ${(table.totalAmount || 0).toFixed(2)} جنيه`);
          console.log(`    - تاريخ الفتح: ${new Date(table.createdAt).toLocaleString('ar-EG')}`);
          console.log('');
        });
      } else {
        console.log('⚠️ لا توجد طاولات للأرقام المستهدفة');
        console.log('📋 جميع طاولات بوسي:', bosyTables.map(t => t.tableNumber));
      }

    } catch (error) {
      console.log('❌ خطأ في جلب الطاولات:', error.message);
    }
  }
}

async function main() {
  const tester = new BosyDataTester();
  await tester.testCurrentData();
}

main().catch(console.error);

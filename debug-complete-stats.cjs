const fetch = require('node-fetch');

const backEndURL = 'https://deshacoffee-production.up.railway.app';

async function debugWaiterStats() {
  try {
    console.log('🔐 تسجيل الدخول...');
    
    // تسجيل الدخول
    const loginResponse = await fetch(`${backEndURL}/api/v1/auth/login`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ username: '<PERSON><PERSON>', password: 'MOHAMEDmostafa123' })
    });
    
    const loginResult = await loginResponse.json();
    const token = loginResult.token;
    console.log('✅ تم تسجيل الدخول بنجاح');
    
    // جلب جميع الطلبات
    console.log('📋 جلب جميع الطلبات...');
    const ordersResponse = await fetch(`${backEndURL}/api/v1/orders?limit=999999`, {
      headers: { 'Authorization': `Bearer ${token}` }
    });
    
    const ordersData = await ordersResponse.json();
    let allOrders = [];
    
    if (Array.isArray(ordersData)) {
      allOrders = ordersData;
    } else if (ordersData.success && Array.isArray(ordersData.data)) {
      allOrders = ordersData.data;
    } else if (ordersData.orders) {
      allOrders = ordersData.orders;
    }
    
    console.log(`📊 إجمالي الطلبات في النظام: ${allOrders.length}`);
    
    // جلب جميع النادلين
    console.log('👥 جلب جميع النادلين...');
    const usersResponse = await fetch(`${backEndURL}/api/v1/users`, {
      headers: { 'Authorization': `Bearer ${token}` }
    });
    
    const usersData = await usersResponse.json();
    let allUsers = [];
    
    if (Array.isArray(usersData)) {
      allUsers = usersData;
    } else if (usersData.success && Array.isArray(usersData.data)) {
      allUsers = usersData.data;
    } else if (usersData.users) {
      allUsers = usersData.users;
    }
    
    const waiters = allUsers.filter(user => user.role === 'waiter');
    console.log(`👥 وجد ${waiters.length} نادل في النظام`);
    
    // حساب مبيعات كل نادل
    console.log('\n📊 حساب مبيعات النادلين:');
    const waiterStats = {};
    
    waiters.forEach(waiter => {
      const waiterOrders = allOrders.filter(order => {
        return (
          order.waiterName === waiter.username || 
          order.waiterName === waiter.name ||
          order.waiterId === waiter._id ||
          (order.staff && order.staff.waiter === waiter._id)
        );
      });
      
      const totalAmount = waiterOrders.reduce((sum, order) => {
        return sum + (order.totals?.total || order.totalPrice || order.totalAmount || 0);
      }, 0);
      
      waiterStats[waiter.name || waiter.username] = {
        orders: waiterOrders.length,
        totalAmount: totalAmount,
        waiterInfo: waiter
      };
      
      console.log(`👤 ${waiter.name || waiter.username}: ${waiterOrders.length} طلب، ${totalAmount.toFixed(2)} جنيه`);
    });
    
    // اختبار API إحصائيات النادلين
    console.log('\n🔍 اختبار API إحصائيات النادلين...');
    const statsResponse = await fetch(`${backEndURL}/api/v1/waiter-stats`, {
      headers: { 'Authorization': `Bearer ${token}` }
    });
    
    console.log(`Status: ${statsResponse.status}`);
    
    if (statsResponse.ok) {
      const statsData = await statsResponse.json();
      console.log('📊 نتائج API إحصائيات النادلين:', JSON.stringify(statsData, null, 2));
    } else {
      const errorText = await statsResponse.text();
      console.log('❌ فشل API إحصائيات النادلين:', errorText);
    }
    
  } catch (error) {
    console.error('❌ خطأ:', error);
  }
}

debugWaiterStats();

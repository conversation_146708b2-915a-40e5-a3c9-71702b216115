const https = require('https');

// معلومات الاتصال
const backEndURL = 'https://deshacoffee-production.up.railway.app';

// بيانات النادل
const waiterAccount = {
  username: '<PERSON><PERSON>',
  password: '253040',
  role: 'نادل'
};

// دالة مساعدة لإرسال طلبات HTTP
function makeRequest(url, options, data = null) {
  return new Promise((resolve, reject) => {
    const req = https.request(url, options, (res) => {
      let body = '';
      res.on('data', chunk => body += chunk);
      res.on('end', () => {
        try {
          const jsonData = JSON.parse(body);
          resolve({ status: res.statusCode, data: jsonData, headers: res.headers });
        } catch (e) {
          resolve({ status: res.statusCode, data: body, headers: res.headers });
        }
      });
    });

    req.on('error', reject);
    
    if (data) {
      req.write(JSON.stringify(data));
    }
    
    req.end();
  });
}

// دالة تسجيل الدخول
async function login() {
  console.log(`🔐 تسجيل الدخول كنادل: ${waiterAccount.username}`);
  
  const options = {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json'
    }
  };

  try {
    const response = await makeRequest(`${backEndURL}/api/auth/login`, options, {
      username: waiterAccount.username,
      password: waiterAccount.password
    });

    if (response.status === 200 && response.data.token) {
      console.log(`✅ تم تسجيل الدخول بنجاح`);
      return {
        token: response.data.token,
        user: response.data.user
      };
    } else {
      console.log(`❌ فشل تسجيل الدخول:`, response.data);
      return null;
    }
  } catch (error) {
    console.error(`❌ خطأ في تسجيل الدخول:`, error.message);
    return null;
  }
}

// دالة اختبار الإشعار المحسن للخطأ 409
async function testTableOccupiedNotification(token, user) {
  console.log('\n🧪 === اختبار الإشعار المحسن للطاولة المحجوزة ===');
  
  // اختبار طاولة محجوزة (رقم 1 محجوزة للنادل azza)
  const occupiedTableNumber = 1;
  
  const testOrder = {
    items: [
      {
        name: 'قهوة عربية',
        price: 15,
        quantity: 1,
        category: 'مشروبات ساخنة',
        _id: '64b7f1234567890123456789' // معرف وهمي للمنتج
      }
    ],
    customerName: 'عميل تجريبي',
    tableNumber: occupiedTableNumber,
    notes: 'طلب تجريبي لاختبار الإشعار المحسن',
    waiterName: user.username,
    waiterId: user._id
  };

  const options = {
    method: 'POST',
    headers: {
      'Authorization': `Bearer ${token}`,
      'Content-Type': 'application/json'
    }
  };

  try {
    console.log(`📝 محاولة إرسال طلب للطاولة المحجوزة رقم ${occupiedTableNumber}...`);
    
    const response = await makeRequest(`${backEndURL}/api/orders`, options, testOrder);
    
    console.log(`📊 رمز الاستجابة: ${response.status}`);
    
    if (response.status === 409) {
      console.log('\n🎯 === اختبار ناجح: تم استلام الخطأ 409 المحسن ===');
      console.log('📝 الرسالة المحسنة:');
      console.log('────────────────────────────────────────');
      console.log(response.data.message);
      console.log('────────────────────────────────────────');
      
      if (response.data.tableNumber) {
        console.log(`📍 رقم الطاولة: ${response.data.tableNumber}`);
      }
      
      if (response.data.occupiedByWaiter) {
        console.log(`👤 محجوزة للنادل: ${response.data.occupiedByWaiter}`);
      }
      
      if (response.data.currentWaiter) {
        console.log(`👤 النادل الحالي: ${response.data.currentWaiter}`);
      }
      
      if (response.data.suggestion) {
        console.log(`💡 الاقتراح: ${response.data.suggestion}`);
      }
      
      console.log('\n✅ الرسالة الآن أكثر وضوحاً ومفيدة!');
      
      // عرض الطاولات المتاحة
      const availableTables = [2, 4, 5, 6, 7, 9, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 24, 25, 26, 27, 28];
      console.log('\n📋 === الطاولات المتاحة الآن ===');
      console.log('✅ يمكن للنادل استخدام هذه الطاولات:');
      console.log(availableTables.join(', '));
      
      return true;
    } else if (response.status >= 200 && response.status < 300) {
      console.log('⚠️ تم إرسال الطلب بنجاح (غير متوقع لطاولة محجوزة)');
      console.log('📦 بيانات الطلب:', response.data);
      return false;
    } else {
      console.log(`❌ خطأ آخر: ${response.status}`);
      console.log('📝 تفاصيل الخطأ:', response.data);
      return false;
    }
  } catch (error) {
    console.error('❌ خطأ في اختبار الإشعار:', error.message);
    return false;
  }
}

// دالة اختبار طاولة متاحة
async function testAvailableTable(token, user) {
  console.log('\n🧪 === اختبار طاولة متاحة ===');
  
  // اختبار طاولة متاحة (رقم 2)
  const availableTableNumber = 2;
  
  const testOrder = {
    items: [
      {
        name: 'شاي أحمر',
        price: 10,
        quantity: 1,
        category: 'مشروبات ساخنة',
        _id: '64b7f1234567890123456788' // معرف وهمي للمنتج
      }
    ],
    customerName: 'عميل اختبار',
    tableNumber: availableTableNumber,
    notes: 'طلب تجريبي لطاولة متاحة',
    waiterName: user.username,
    waiterId: user._id
  };

  const options = {
    method: 'POST',
    headers: {
      'Authorization': `Bearer ${token}`,
      'Content-Type': 'application/json'
    }
  };

  try {
    console.log(`📝 محاولة إرسال طلب للطاولة المتاحة رقم ${availableTableNumber}...`);
    
    const response = await makeRequest(`${backEndURL}/api/orders`, options, testOrder);
    
    console.log(`📊 رمز الاستجابة: ${response.status}`);
    
    if (response.status >= 200 && response.status < 300) {
      console.log('✅ تم إرسال الطلب بنجاح للطاولة المتاحة!');
      console.log('📦 معرف الطلب:', response.data._id || response.data.id);
      return true;
    } else {
      console.log(`❌ فشل إرسال الطلب: ${response.status}`);
      console.log('📝 تفاصيل الخطأ:', response.data);
      return false;
    }
  } catch (error) {
    console.error('❌ خطأ في اختبار الطاولة المتاحة:', error.message);
    return false;
  }
}

// الدالة الرئيسية
async function main() {
  console.log('🚀 === اختبار الإشعار المحسن للخطأ 409 ===\n');
  
  // 1. تسجيل الدخول
  const auth = await login();
  
  if (!auth) {
    console.log('❌ فشل في تسجيل الدخول. لا يمكن المتابعة.');
    return;
  }
  
  // 2. اختبار الطاولة المحجوزة (يجب أن يعطي 409 مع رسالة محسنة)
  const occupiedTest = await testTableOccupiedNotification(auth.token, auth.user);
  
  // 3. اختبار الطاولة المتاحة (يجب أن ينجح)
  const availableTest = await testAvailableTable(auth.token, auth.user);
  
  // 4. النتائج النهائية
  console.log('\n📊 === ملخص نتائج الاختبار ===');
  console.log(`✅ اختبار الطاولة المحجوزة (409): ${occupiedTest ? 'نجح' : 'فشل'}`);
  console.log(`✅ اختبار الطاولة المتاحة: ${availableTest ? 'نجح' : 'فشل'}`);
  
  if (occupiedTest) {
    console.log('\n🎉 === تم تحسين رسالة الخطأ 409 بنجاح! ===');
    console.log('الآن النوادل سيرون رسالة واضحة ومفيدة بدلاً من خطأ تقني');
  } else {
    console.log('\n⚠️ يحتاج الإشعار إلى مراجعة إضافية');
  }
  
  console.log('\n✅ انتهى الاختبار');
}

// تشغيل البرنامج
main().catch(error => {
  console.error('❌ خطأ عام في البرنامج:', error);
});

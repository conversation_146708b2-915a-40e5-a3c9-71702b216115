const { MongoMemoryServer } = require('mongodb-memory-server');

async function startMemoryMongoDB() {
  try {
    console.log('🚀 Starting MongoDB Memory Server...');
    const mongod = await MongoMemoryServer.create({
      instance: {
        port: 27017, // Use standard MongoDB port
        dbName: 'deshacoffee_local'
      }
    });
    
    const uri = mongod.getUri();
    console.log('✅ MongoDB Memory Server started!');
    console.log('📡 Connection URI:', uri);
    
    // Keep the server running
    process.on('SIGINT', async () => {
      console.log('\n🛑 Shutting down MongoDB Memory Server...');
      await mongod.stop();
      process.exit(0);
    });
    
    return mongod;
  } catch (error) {
    console.error('❌ Failed to start MongoDB Memory Server:', error);
    process.exit(1);
  }
}

if (require.main === module) {
  startMemoryMongoDB().then(() => {
    console.log('💾 MongoDB Memory Server is running. Press Ctrl+C to stop.');
  });
}

module.exports = { startMemoryMongoDB };

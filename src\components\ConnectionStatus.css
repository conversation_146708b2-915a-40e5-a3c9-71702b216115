/* Connection Status Styles */
.connection-status {
  position: fixed;
  top: 1rem;
  right: 1rem;
  background-color: #ffffff;
  color: #333333;
  padding: 1rem;
  border-radius: 0.5rem;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  z-index: 1000;
  min-width: 300px;
  direction: rtl;
}

.connection-status.connected {
  border: 1px solid #22c55e;
}

.connection-status.error {
  border: 1px solid #ff4444;
}

.connection-status-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1rem;
  padding-bottom: 0.5rem;
  border-bottom: 1px solid #eee;
}

.status-indicator {
  display: inline-block;
  width: 12px;
  height: 12px;
  border-radius: 50%;
  margin-left: 8px;
}

.status-indicator.error {
  background: #ff4444;
}

.status-indicator.success {
  background: #22c55e;
}

.connection-details {
  font-size: 14px;
  margin-top: 8px;
  line-height: 1.5;
}

.connection-details p {
  margin: 0.25rem 0;
  font-size: 0.9rem;
}

.error-message {
  color: #ff4444;
  margin-top: 4px;
  font-size: 13px;
}

.retry-button {
  background-color: #ff4444 !important;
  color: #fff !important;
  border: none !important;
  padding: 6px 12px;
  border-radius: 4px;
  cursor: pointer;
  font-size: 14px;
  margin-bottom: 8px;
}

.retry-button:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.connection-actions {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 0.5rem;
}

.connection-actions button {
  background-color: #fff;
  color: #ff4444;
  border: 1px solid #ff4444;
  padding: 0.5rem 1rem;
  border-radius: 0.25rem;
  cursor: pointer;
  font-weight: bold;
  width: 100%;
  transition: all 0.2s ease;
}

.connection-actions button:hover:not(:disabled) {
  background-color: #ff4444;
  color: #fff;
}

.connection-actions button:disabled {
  opacity: 0.7;
  cursor: not-allowed;
}

.connection-actions small {
  color: #666;
  font-size: 0.8rem;
}

.last-checked {
  display: block;
  margin-top: 4px;
  color: #888;
  font-size: 12px;
}

.debug-info {
  display: block;
  margin-top: 5px;
  opacity: 0.7;
  font-size: 11px;
  color: #666;
}

// عرض البيانات الحقيقية من قاعدة البيانات
const mongoose = require('mongoose');

async function getRealDatabaseStats() {
  try {
    console.log('🔗 الاتصال بقاعدة البيانات الحية...');
    
    const mongoUri = 'mongodb+srv://besomustafa:<EMAIL>/deshacoffee?retryWrites=true&w=majority&appName=MyCoffeChop';
    
    await mongoose.connect(mongoUri);
    console.log('✅ متصل بقاعدة البيانات بنجاح');
    
    const db = mongoose.connection.db;
    
    console.log('\n' + '='.repeat(80));
    console.log('📊 البيانات الحقيقية من قاعدة بيانات مقهى دشة');
    console.log('='.repeat(80));
    
    // إحصائيات المجموعات
    console.log('\n📋 مجموعات قاعدة البيانات:');
    const collections = await db.listCollections().toArray();
    collections.forEach(col => {
      console.log('• ' + col.name);
    });
    
    // جلب وتحليل الطلبات
    console.log('\n📦 تحليل الطلبات:');
    const orders = await db.collection('orders').find({}).toArray();
    console.log('إجمالي الطلبات: ' + orders.length);
    
    if (orders.length > 0) {
      // تحليل الحالات
      const statusCounts = {};
      let ordersWithValues = 0;
      let ordersWithoutValues = 0;
      let totalValue = 0;
      let totalDiscount = 0;
      
      console.log('\n🔍 تحليل مفصل للطلبات:');
      
      orders.forEach((order, index) => {
        const status = order.status || 'غير محدد';
        statusCounts[status] = (statusCounts[status] || 0) + 1;
        
        const orderTotal = parseFloat(order.total) || 0;
        const orderDiscount = parseFloat(order.discount) || 0;
        
        if (orderTotal > 0) {
          ordersWithValues++;
          totalValue += orderTotal;
        } else {
          ordersWithoutValues++;
        }
        
        totalDiscount += orderDiscount;
        
        // عرض أول 10 طلبات كعينة
        if (index < 10) {
          console.log('الطلب ' + (index + 1) + ':');
          console.log('  ID: ' + order._id);
          console.log('  الحالة: ' + status);
          console.log('  الإجمالي: ' + orderTotal + ' ريال');
          console.log('  الخصم: ' + orderDiscount + ' ريال');
          console.log('  النادل: ' + (order.assignedWaiter || 'غير محدد'));
          console.log('  رقم الطاولة: ' + (order.tableNumber || 'غير محدد'));
          console.log('  التاريخ: ' + (order.createdAt || 'غير محدد'));
          
          if (order.items && order.items.length > 0) {
            console.log('  العناصر: ' + order.items.length + ' عنصر');
            order.items.forEach((item, i) => {
              if (i < 3) { // عرض أول 3 عناصر
                console.log('    - ' + (item.name || 'غير محدد') + ' x' + (item.quantity || 1) + ' = ' + (item.price || 0) + ' ريال');
              }
            });
          }
          console.log('  ---');
        }
      });
      
      console.log('\n📊 ملخص الطلبات:');
      console.log('• طلبات بقيم مالية: ' + ordersWithValues);
      console.log('• طلبات بدون قيم: ' + ordersWithoutValues);
      console.log('• إجمالي القيم: ' + totalValue.toFixed(2) + ' ريال');
      console.log('• إجمالي الخصومات: ' + totalDiscount.toFixed(2) + ' ريال');
      
      console.log('\n📋 توزيع الحالات:');
      Object.entries(statusCounts).forEach(([status, count]) => {
        const percentage = ((count / orders.length) * 100).toFixed(1);
        console.log('• ' + status + ': ' + count + ' (' + percentage + '%)');
      });
    }
    
    // جلب وتحليل المستخدمين
    console.log('\n👥 تحليل المستخدمين:');
    const users = await db.collection('users').find({}).toArray();
    console.log('إجمالي المستخدمين: ' + users.length);
    
    if (users.length > 0) {
      const roleStats = {};
      console.log('\n📝 قائمة المستخدمين:');
      
      users.forEach((user, index) => {
        const role = user.role || 'غير محدد';
        roleStats[role] = (roleStats[role] || 0) + 1;
        
        if (index < 15) { // عرض أول 15 مستخدم
          console.log((index + 1) + '. ' + (user.username || 'بدون اسم'));
          console.log('   الدور: ' + role);
          console.log('   الإيميل: ' + (user.email || 'غير محدد'));
          console.log('   تاريخ الإنشاء: ' + (user.createdAt || 'غير محدد'));
          console.log('   ---');
        }
      });
      
      console.log('\n📊 توزيع الأدوار:');
      Object.entries(roleStats).forEach(([role, count]) => {
        console.log('• ' + role + ': ' + count + ' مستخدم');
      });
    }
    
    // جلب المنتجات إن وجدت
    console.log('\n🛍️ تحليل المنتجات:');
    const products = await db.collection('products').find({}).toArray();
    console.log('إجمالي المنتجات: ' + products.length);
    
    if (products.length > 0) {
      console.log('\n📝 عينة من المنتجات:');
      products.slice(0, 5).forEach((product, index) => {
        console.log((index + 1) + '. ' + (product.name || 'بدون اسم'));
        console.log('   السعر: ' + (product.price || 0) + ' ريال');
        console.log('   الفئة: ' + (product.category || 'غير محدد'));
        console.log('   متوفر: ' + (product.available ? 'نعم' : 'لا'));
        console.log('   ---');
      });
    }
    
    // جلب الطاولات إن وجدت
    console.log('\n🪑 تحليل الطاولات:');
    const tables = await db.collection('tables').find({}).toArray();
    console.log('إجمالي الطاولات: ' + tables.length);
    
    if (tables.length > 0) {
      const tableStatusStats = {};
      tables.forEach(table => {
        const status = table.status || 'غير محدد';
        tableStatusStats[status] = (tableStatusStats[status] || 0) + 1;
      });
      
      console.log('\n📊 حالة الطاولات:');
      Object.entries(tableStatusStats).forEach(([status, count]) => {
        console.log('• ' + status + ': ' + count + ' طاولة');
      });
    }
    
    // حساب الإحصائيات المالية للطلبات المكتملة فقط
    console.log('\n💰 الإحصائيات المالية (الطلبات المكتملة فقط):');
    const completedOrders = orders.filter(order => order.status === 'completed');
    console.log('الطلبات المكتملة: ' + completedOrders.length);
    
    if (completedOrders.length > 0) {
      let completedTotal = 0;
      let completedDiscount = 0;
      const waiterSales = {};
      
      completedOrders.forEach(order => {
        const orderTotal = parseFloat(order.total) || 0;
        const orderDiscount = parseFloat(order.discount) || 0;
        
        completedTotal += orderTotal;
        completedDiscount += orderDiscount;
        
        // تجميع مبيعات النُدل
        const waiterId = order.assignedWaiter || 'غير محدد';
        if (!waiterSales[waiterId]) {
          waiterSales[waiterId] = {
            orders: 0,
            total: 0,
            discount: 0
          };
        }
        
        waiterSales[waiterId].orders++;
        waiterSales[waiterId].total += orderTotal;
        waiterSales[waiterId].discount += orderDiscount;
      });
      
      console.log('• إجمالي مبيعات الطلبات المكتملة: ' + completedTotal.toFixed(2) + ' ريال');
      console.log('• إجمالي خصومات الطلبات المكتملة: ' + completedDiscount.toFixed(2) + ' ريال');
      console.log('• صافي المبيعات: ' + (completedTotal - completedDiscount).toFixed(2) + ' ريال');
      
      if (completedTotal > 0) {
        console.log('• متوسط قيمة الطلب: ' + (completedTotal / completedOrders.length).toFixed(2) + ' ريال');
      }
      
      console.log('\n👥 مبيعات النُدل (الطلبات المكتملة):');
      Object.entries(waiterSales)
        .sort((a, b) => b[1].total - a[1].total)
        .forEach(([waiterId, stats], index) => {
          let waiterName = 'غير محدد';
          if (waiterId !== 'غير محدد') {
            const waiter = users.find(u => u._id.toString() === waiterId.toString());
            waiterName = waiter ? waiter.username : 'نادل (' + waiterId + ')';
          }
          
          console.log((index + 1) + '. ' + waiterName);
          console.log('   الطلبات: ' + stats.orders);
          console.log('   المبيعات: ' + stats.total.toFixed(2) + ' ريال');
          console.log('   الخصومات: ' + stats.discount.toFixed(2) + ' ريال');
          console.log('   الصافي: ' + (stats.total - stats.discount).toFixed(2) + ' ريال');
          console.log('   ---');
        });
    }
    
    console.log('\n' + '='.repeat(80));
    console.log('📅 تاريخ التقرير: ' + new Date().toLocaleString('ar-SA'));
    console.log('🌐 مصدر البيانات: قاعدة البيانات المباشرة');
    console.log('✅ انتهى تحليل البيانات الحقيقية');
    console.log('='.repeat(80));
    
    await mongoose.disconnect();
    console.log('\n🔌 تم قطع الاتصال بقاعدة البيانات');
    
  } catch (error) {
    console.error('❌ خطأ في الاتصال أو التحليل:');
    console.error('الرسالة: ' + error.message);
    if (error.code) {
      console.error('الكود: ' + error.code);
    }
  }
}

getRealDatabaseStats();

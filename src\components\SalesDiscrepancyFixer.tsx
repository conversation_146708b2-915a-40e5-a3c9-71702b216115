// مكون إصلاح التباين في مبيعات النُدُل
import React, { useState, useEffect } from 'react';
import { authenticatedGet, authenticatedPost } from '../utils/apiHelpers';

interface SalesDiscrepancy {
  totalSales: number;
  waiterSalesTotal: number;
  discrepancy: number;
  orphanedOrders: number;
  invalidWaiterOrders: number;
  waiterBreakdown: Array<{
    name: string;
    username: string;
    sales: number;
    orders: number;
  }>;
}

interface SalesDiscrepancyFixerProps {
  onClose: () => void;
}

export const SalesDiscrepancyFixer: React.FC<SalesDiscrepancyFixerProps> = ({ onClose }) => {
  const [analysis, setAnalysis] = useState<SalesDiscrepancy | null>(null);
  const [loading, setLoading] = useState(false);
  const [fixing, setFixing] = useState(false);
  const [fixResult, setFixResult] = useState<any>(null);

  const analyzeSalesDiscrepancy = async () => {
    setLoading(true);
    try {
      const response = await authenticatedGet('/api/v1/sales/analyze-discrepancy');
      if (response.success) {
        setAnalysis(response.data);
      }
    } catch (error) {
      console.error('خطأ في تحليل التباين:', error);
    } finally {
      setLoading(false);
    }
  };

  const fixSalesDiscrepancy = async () => {
    setFixing(true);
    try {
      const response = await authenticatedPost('/api/v1/sales/fix-discrepancy', {});
      if (response.success) {
        setFixResult(response.data);
        // إعادة تحليل بعد الإصلاح
        await analyzeSalesDiscrepancy();
      }
    } catch (error) {
      console.error('خطأ في إصلاح التباين:', error);
    } finally {
      setFixing(false);
    }
  };

  useEffect(() => {
    analyzeSalesDiscrepancy();
  }, []);

  return (
    <div className="sales-discrepancy-fixer">
      <div className="modal-header">
        <h2>🔧 إصلاح التباين في مبيعات النُدُل</h2>
        <button onClick={onClose} className="close-btn">×</button>
      </div>

      <div className="modal-content">
        {loading ? (
          <div className="loading">⏳ جاري التحليل...</div>
        ) : analysis ? (
          <div className="analysis-results">
            <div className="summary-cards">
              <div className="summary-card">
                <h3>💰 إجمالي المبيعات</h3>
                <div className="amount">{analysis.totalSales.toFixed(2)} جنيه</div>
              </div>
              
              <div className="summary-card">
                <h3>👥 مجموع مبيعات النُدُل</h3>
                <div className="amount">{analysis.waiterSalesTotal.toFixed(2)} جنيه</div>
              </div>
              
              <div className={`summary-card ${Math.abs(analysis.discrepancy) > 1 ? 'error' : 'success'}`}>
                <h3>📊 التباين</h3>
                <div className="amount">{analysis.discrepancy.toFixed(2)} جنيه</div>
              </div>
            </div>

            {Math.abs(analysis.discrepancy) > 1 && (
              <div className="issues-section">
                <h3>🚨 المشاكل المكتشفة</h3>
                
                {analysis.orphanedOrders > 0 && (
                  <div className="issue-item">
                    <span className="issue-icon">👻</span>
                    <span>طلبات بدون نادل: {analysis.orphanedOrders}</span>
                  </div>
                )}
                
                {analysis.invalidWaiterOrders > 0 && (
                  <div className="issue-item">
                    <span className="issue-icon">❌</span>
                    <span>طلبات بنُدُل غير موجودين: {analysis.invalidWaiterOrders}</span>
                  </div>
                )}
              </div>
            )}

            <div className="waiters-breakdown">
              <h3>👥 تفصيل مبيعات النُدُل</h3>
              <div className="waiters-list">
                {analysis.waiterBreakdown.map((waiter, index) => (
                  <div key={index} className="waiter-item">
                    <span className="waiter-name">{waiter.name}</span>
                    <span className="waiter-sales">{waiter.sales.toFixed(2)} جنيه</span>
                    <span className="waiter-orders">({waiter.orders} طلب)</span>
                  </div>
                ))}
              </div>
            </div>

            {Math.abs(analysis.discrepancy) > 1 && (
              <div className="fix-section">
                <button 
                  onClick={fixSalesDiscrepancy}
                  disabled={fixing}
                  className="fix-btn"
                >
                  {fixing ? '⏳ جاري الإصلاح...' : '🔧 إصلاح التباين'}
                </button>
              </div>
            )}

            {fixResult && (
              <div className="fix-result">
                <h3>✅ نتائج الإصلاح</h3>
                <div className="result-item">
                  <span>طلبات تم إصلاحها: {fixResult.fixedOrders}</span>
                </div>
                <div className="result-item">
                  <span>قيمة المبيعات المُصلحة: {fixResult.totalValueFixed.toFixed(2)} جنيه</span>
                </div>
                <div className="result-item">
                  <span>التباين الجديد: {fixResult.discrepancyAfter.toFixed(2)} جنيه</span>
                </div>
                <div className={`result-status ${fixResult.status === 'success' ? 'success' : 'warning'}`}>
                  {fixResult.status === 'success' ? '✅ تم الإصلاح بنجاح' : '⚠️ إصلاح جزئي'}
                </div>
              </div>
            )}
          </div>
        ) : (
          <div className="error">❌ فشل في تحليل البيانات</div>
        )}
      </div>
    </div>
  );
};

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>فحص Console للواجهة الأمامية</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            padding: 20px;
            direction: rtl;
            text-align: right;
            background: #f0f0f0;
        }
        .test-container {
            max-width: 1000px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .console-output {
            background: #1e1e1e;
            color: #ffffff;
            padding: 15px;
            border-radius: 5px;
            font-family: 'Courier New', monospace;
            font-size: 12px;
            height: 400px;
            overflow-y: auto;
            white-space: pre-wrap;
            direction: ltr;
            text-align: left;
        }
        button {
            padding: 10px 20px;
            margin: 10px 5px;
            border: none;
            border-radius: 3px;
            cursor: pointer;
            background-color: #007bff;
            color: white;
        }
        button:hover {
            background-color: #0056b3;
        }
        .info-box {
            padding: 15px;
            margin: 10px 0;
            border: 1px solid #ddd;
            border-radius: 5px;
            background: #f8f9fa;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>🔍 فحص Console للواجهة الأمامية</h1>
        
        <div class="info-box">
            <p>هذه الصفحة تُظهر جميع console.log messages من الواجهة الأمامية الأصلية</p>
            <button onclick="openMainApp()">🚀 فتح التطبيق الأصلي</button>
            <button onclick="clearConsole()">🧹 مسح Console</button>
            <button onclick="testDirectAPI()">🧪 اختبار API مباشر</button>
        </div>

        <h3>Console Output:</h3>
        <div id="console" class="console-output">
            جاري تحميل Console...
        </div>
    </div>

    <script>
        let consoleElement = document.getElementById('console');
        let originalConsole = {
            log: console.log,
            error: console.error,
            warn: console.warn,
            info: console.info
        };

        function formatTime() {
            return new Date().toLocaleTimeString();
        }

        function addToConsole(type, args) {
            const timestamp = formatTime();
            const message = args.map(arg => {
                if (typeof arg === 'object') {
                    return JSON.stringify(arg, null, 2);
                }
                return String(arg);
            }).join(' ');
            
            const color = {
                log: '#ffffff',
                error: '#ff6b6b',
                warn: '#ffd93d',
                info: '#74c0fc'
            }[type] || '#ffffff';
            
            consoleElement.innerHTML += `<span style="color: ${color}">[${timestamp}] ${type.toUpperCase()}: ${message}</span>\n`;
            consoleElement.scrollTop = consoleElement.scrollHeight;
        }

        // اعتراض جميع console methods
        console.log = function(...args) {
            originalConsole.log.apply(console, args);
            addToConsole('log', args);
        };

        console.error = function(...args) {
            originalConsole.error.apply(console, args);
            addToConsole('error', args);
        };

        console.warn = function(...args) {
            originalConsole.warn.apply(console, args);
            addToConsole('warn', args);
        };

        console.info = function(...args) {
            originalConsole.info.apply(console, args);
            addToConsole('info', args);
        };

        function clearConsole() {
            consoleElement.innerHTML = '';
        }

        function openMainApp() {
            window.open('http://localhost:3000', '_blank');
            addToConsole('info', ['فتح التطبيق الأصلي في نافذة جديدة']);
        }

        async function testDirectAPI() {
            addToConsole('info', ['بدء اختبار API مباشر...']);
            
            try {
                const response = await fetch('http://localhost:5000/health', {
                    method: 'GET',
                    headers: {
                        'Content-Type': 'application/json',
                        'Accept': 'application/json'
                    }
                });

                addToConsole('log', ['Response status:', response.status]);
                addToConsole('log', ['Response ok:', response.ok]);
                
                if (response.ok) {
                    const data = await response.json();
                    addToConsole('log', ['Response data:', data]);
                    
                    // محاكاة نفس منطق ConnectionStatus
                    const isServerHealthy = response.ok && data?.status === 'healthy';
                    const isDatabaseConnected = response.ok && data?.database?.connected === true;
                    
                    addToConsole('log', ['isServerHealthy:', isServerHealthy]);
                    addToConsole('log', ['isDatabaseConnected:', isDatabaseConnected]);
                    addToConsole('log', ['Overall connected:', isServerHealthy && isDatabaseConnected]);
                } else {
                    addToConsole('error', ['Response not ok:', response.statusText]);
                }
            } catch (error) {
                addToConsole('error', ['API Test failed:', error.message]);
            }
        }

        // رسالة ترحيب
        addToConsole('info', ['Console monitor started']);
        addToConsole('info', ['Monitoring all console.log, console.error, console.warn, console.info']);
        
        // محاولة الاتصال بالواجهة الأمامية كل 5 ثوان لعرض الرسائل
        setInterval(async () => {
            try {
                // محاولة للحصول على logs من LocalStorage أو SessionStorage إذا تم حفظها
                const logs = localStorage.getItem('debugLogs');
                if (logs) {
                    addToConsole('info', ['Found stored logs:', logs]);
                    localStorage.removeItem('debugLogs'); // مسح بعد القراءة
                }
            } catch (e) {
                // لا توجد logs محفوظة
            }
        }, 5000);
    </script>
</body>
</html>

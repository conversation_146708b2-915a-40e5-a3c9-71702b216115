const mongoose = require('mongoose');
require('dotenv').config();

const DiscountRequest = require('./backend/models/DiscountRequest');
const User = require('./backend/models/User');

const DB_URI = process.env.MONGODB_URI || 'mongodb+srv://besomustafa:<EMAIL>/deshacoffee?retryWrites=true&w=majority&appName=MyCoffeChop';

console.log('🔗 Connecting to database:', DB_URI.replace(/\/\/[^:]+:[^@]+@/, '//***:***@'));

async function updateDiscountRequestNames() {
  try {
    await mongoose.connect(DB_URI);
    console.log('✅ Connected to database');

    // جلب جميع طلبات الخصم
    const discountRequests = await DiscountRequest.find({});
    console.log(`📋 Found ${discountRequests.length} discount requests`);

    let updatedCount = 0;

    for (const request of discountRequests) {
      let needsUpdate = false;
      const updateData = {};

      // تحديث اسم النادل
      if (request.requestedBy && !request.waiterName) {
        try {
          let waiter = await User.findOne({ username: request.requestedBy });
          if (!waiter) {
            waiter = await User.findById(request.requestedBy);
          }
          if (waiter) {
            updateData.waiterName = waiter.name;
            updateData.waiterUsername = waiter.username;
            needsUpdate = true;
            console.log(`💫 Found waiter: ${waiter.name} for request ${request._id}`);
          }
        } catch (err) {
          console.warn(`⚠️ Error finding waiter for request ${request._id}:`, err.message);
        }
      }

      // تحديث اسم الموافق
      if (request.approvedBy && !request.approvedByName) {
        try {
          let approver = await User.findOne({ username: request.approvedBy });
          if (!approver) {
            approver = await User.findById(request.approvedBy);
          }
          if (approver) {
            updateData.approvedByName = approver.name;
            updateData.approvedByUsername = approver.username;
            needsUpdate = true;
            console.log(`✅ Found approver: ${approver.name} for request ${request._id}`);
          }
        } catch (err) {
          console.warn(`⚠️ Error finding approver for request ${request._id}:`, err.message);
        }
      }

      // تحديث اسم الرافض (إذا كان موجود)
      if (request.rejectedBy && !request.rejectedByName) {
        try {
          let rejecter = await User.findOne({ username: request.rejectedBy });
          if (!rejecter) {
            rejecter = await User.findById(request.rejectedBy);
          }
          if (rejecter) {
            updateData.rejectedByName = rejecter.name;
            updateData.rejectedByUsername = rejecter.username;
            needsUpdate = true;
            console.log(`❌ Found rejecter: ${rejecter.name} for request ${request._id}`);
          }
        } catch (err) {
          console.warn(`⚠️ Error finding rejecter for request ${request._id}:`, err.message);
        }
      }

      // تطبيق التحديث إذا كان مطلوب
      if (needsUpdate) {
        await DiscountRequest.findByIdAndUpdate(request._id, updateData);
        updatedCount++;
        console.log(`🔄 Updated request ${request._id}:`, updateData);
      }
    }

    console.log(`✅ Successfully updated ${updatedCount} discount requests with user names`);
    
    // عرض عينة من التحديثات
    const updatedRequests = await DiscountRequest.find({}).limit(5);
    console.log('\n📋 Sample updated requests:');
    updatedRequests.forEach(req => {
      console.log(`- ${req._id}: waiter=${req.waiterName}, approvedBy=${req.approvedByName}, status=${req.status}`);
    });

  } catch (error) {
    console.error('❌ Error updating discount requests:', error);
  } finally {
    await mongoose.disconnect();
    console.log('🔌 Disconnected from database');
  }
}

// تشغيل الأداة
updateDiscountRequestNames();

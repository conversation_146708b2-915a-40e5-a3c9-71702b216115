# Railway Deployment Configuration
# ملف تكوين خاص بنشر Railway

[build]
builder = "nixpacks"

[deploy]
startCommand = "npm start"
restartPolicyType = "ON_FAILURE"
restartPolicyMaxRetries = 10

[variables]
NODE_ENV = "production"
HOST = "0.0.0.0"
RAILWAY_STATIC_URL = "https://deshacoffee-production.up.railway.app"

[services.web]
source = "."
startCommand = "npm start"

[networking]
serviceDomain = "deshacoffee-production.up.railway.app"

[health]
httpPath = "/health"
initialDelaySeconds = 30
periodSeconds = 30
timeoutSeconds = 10
failureThreshold = 3

[scaling]
autoscaling = true
minReplicas = 1
maxReplicas = 3

[resources]
memory = "1Gi"
cpu = "0.5"

const fetch = require('node-fetch');

class FixValidationTest {
  constructor() {
    this.baseURL = 'https://deshacoffee-production.up.railway.app';
    this.frontendURL = 'https://desha-coffee.vercel.app';
    this.waiterId = '************************';
    this.username = '<PERSON><PERSON>';
    this.password = '253040';
    this.targetTables = ['1', '2', '29'];
  }

  async testFixedLogic() {
    console.log('🧪 اختبار منطق الإصلاح...\n');
    console.log('='.repeat(60));

    // تسجيل الدخول
    const authResult = await this.login();
    if (!authResult.success) return;

    console.log('\n' + '-'.repeat(40) + '\n');

    // محاكاة منطق الإصلاح
    await this.simulateFixedFetchTableAccounts(authResult.token);
  }

  async login() {
    console.log('🔐 تسجيل الدخول...');
    try {
      const response = await fetch(`${this.baseURL}/api/auth/login`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          username: this.username,
          password: this.password,
          role: 'نادل'
        })
      });

      const result = await response.json();
      if (response.ok && result.token) {
        console.log('✅ تم تسجيل الدخول بنجاح');
        return { success: true, token: result.token };
      } else {
        console.log('❌ فشل تسجيل الدخول');
        return { success: false };
      }
    } catch (error) {
      console.log('❌ خطأ في تسجيل الدخول:', error.message);
      return { success: false };
    }
  }

  async simulateFixedFetchTableAccounts(token) {
    console.log('🔧 محاكاة منطق الإصلاح المحدث...');
    
    try {
      // المرحلة 1: جلب الطلبات
      console.log('\n📋 المرحلة 1: جلب الطلبات...');
      
      const ordersResponse = await fetch(`${this.baseURL}/api/v1/orders`, {
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        }
      });

      const ordersResult = await ordersResponse.json();
      const allOrders = Array.isArray(ordersResult) ? ordersResult : (ordersResult.data || []);
      
      // فلترة طلبات بوسي
      const allOrdersForTables = allOrders.filter(order => {
        return order.waiterId === this.waiterId || 
               order.waiterName === 'بوسي' ||
               order.waiterName === 'Bosy';
      });

      console.log(`✅ تم جلب ${allOrdersForTables.length} طلب للنادلة بوسي`);

      // المرحلة 2: جلب الطاولات
      console.log('\n🏓 المرحلة 2: جلب الطاولات...');
      
      const tablesResponse = await fetch(`${this.baseURL}/api/v1/table-accounts`, {
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        }
      });

      const tablesResult = await tablesResponse.json();
      let tableAccountsData = Array.isArray(tablesResult) ? tablesResult : (tablesResult.data || []);
      
      console.log(`✅ تم جلب ${tableAccountsData.length} طاولة من API`);

      // المرحلة 3: ربط الطلبات بالطاولات
      console.log('\n🔗 المرحلة 3: ربط الطلبات بالطاولات...');
      
      const enrichedTableAccounts = tableAccountsData.map(account => {
        // البحث عن طلبات هذه الطاولة
        const tableOrders = allOrdersForTables.filter(order => {
          const orderTableStr = String(order.tableNumber || '').trim();
          const accountTableStr = String(account.tableNumber || '').trim();
          const tableMatch = orderTableStr === accountTableStr;

          // مطابقة النادل
          let waiterMatch = false;
          if (this.waiterId && order.waiterId) {
            waiterMatch = order.waiterId === this.waiterId;
          } else if (order.waiterName === 'بوسي' || order.waiterName === 'Bosy') {
            waiterMatch = true;
          }

          return tableMatch && waiterMatch;
        });

        // حساب المبلغ الإجمالي
        const calculatedTotal = tableOrders.reduce((sum, order) => {
          return sum + (order.totalPrice || 0);
        }, 0);

        console.log(`  طاولة ${account.tableNumber}: ${tableOrders.length} طلب، ${calculatedTotal.toFixed(2)} جنيه`);

        return {
          ...account,
          orders: tableOrders,
          totalAmount: calculatedTotal > 0 ? calculatedTotal : account.totalAmount || 0,
          ordersCount: tableOrders.length
        };
      });

      // المرحلة 4: فلترة الطاولات النشطة للنادل
      console.log('\n🎯 المرحلة 4: فلترة الطاولات النشطة...');
      
      const currentWaiterActiveTables = enrichedTableAccounts.filter(account => {
        const isCorrectWaiter = (
          account.waiterId === this.waiterId ||
          account.waiterName === 'Bosy' ||
          account.waiterName === 'بوسي'
        );
        
        const isOpenTable = account.isOpen === true && account.status === 'active';
        
        return isCorrectWaiter && isOpenTable;
      });

      console.log(`✅ النتيجة النهائية: ${currentWaiterActiveTables.length} طاولة نشطة`);

      // المرحلة 5: عرض النتائج المتوقعة
      console.log('\n📊 النتائج المتوقعة بعد الإصلاح:');
      
      let totalOrders = 0;
      let totalAmount = 0;
      
      currentWaiterActiveTables.forEach(table => {
        console.log(`🏓 طاولة ${table.tableNumber}:`);
        console.log(`   - عدد الطلبات: ${table.ordersCount}`);
        console.log(`   - إجمالي المبلغ: ${table.totalAmount.toFixed(2)} جنيه`);
        console.log(`   - الحالة: ${table.status} (${table.isOpen ? 'مفتوحة' : 'مغلقة'})`);
        
        if (table.orders && table.orders.length > 0) {
          console.log(`   - تفاصيل الطلبات:`);
          table.orders.forEach(order => {
            console.log(`     * طلب #${order.orderNumber}: ${order.status}, ${order.totalPrice} جنيه`);
          });
        }
        
        totalOrders += table.ordersCount;
        totalAmount += table.totalAmount;
        console.log('');
      });

      console.log('📈 الإحصائيات العامة بعد الإصلاح:');
      console.log(`   - إجمالي الطاولات النشطة: ${currentWaiterActiveTables.length}`);
      console.log(`   - إجمالي الطلبات: ${totalOrders}`);
      console.log(`   - إجمالي المبيعات: ${totalAmount.toFixed(2)} جنيه`);

      // المقارنة مع الحالة الحالية
      console.log('\n🔄 مقارنة مع الحالة الحالية:');
      console.log('   قبل الإصلاح: 3 طاولات، 0 طلب، 0.00 جنيه');
      console.log(`   بعد الإصلاح: ${currentWaiterActiveTables.length} طاولات، ${totalOrders} طلب، ${totalAmount.toFixed(2)} جنيه`);
      
      if (totalOrders > 0 && totalAmount > 0) {
        console.log('\n🎉 الإصلاح سيعمل بنجاح! ستظهر الطلبات والمبالغ بشكل صحيح.');
      } else {
        console.log('\n⚠️ قد تحتاج مراجعة إضافية للإصلاح.');
      }

    } catch (error) {
      console.error('❌ خطأ في محاكاة الإصلاح:', error.message);
    }
  }
}

async function main() {
  const tester = new FixValidationTest();
  await tester.testFixedLogic();
}

main().catch(console.error);

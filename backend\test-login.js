const axios = require('axios');

async function testLogin() {
  try {
    console.log('🔐 Testing login for Beso...');
    
    const loginData = {
      username: '<PERSON><PERSON>',
      password: 'MOHAMEDmostafa123'
    };

    console.log('📤 Sending login request with data:', loginData);

    const response = await axios.post('http://localhost:5001/api/v1/auth/login', loginData, {
      headers: {
        'Content-Type': 'application/json'
      }
    });

    console.log('✅ Login successful!');
    console.log('Response status:', response.status);
    console.log('Response data:', JSON.stringify(response.data, null, 2));

  } catch (error) {
    console.error('❌ Login failed!');
    if (error.response) {
      console.error('Status:', error.response.status);
      console.error('Response:', JSON.stringify(error.response.data, null, 2));
    } else {
      console.error('Error:', error.message);
    }
  }
}

testLogin();

const mongoose = require('mongoose');
require('dotenv').config();

const Order = require('./backend/models/Order');
const User = require('./backend/models/User');

/**
 * تحليل شامل لحساب مبيعات النادل
 * فحص عدم وجود فقدان في الطلبات
 */
class WaiterSalesAnalyzer {
  constructor() {
    this.reports = {
      totalOrders: 0,
      matchedOrders: 0,
      unmatchedOrders: 0,
      waiterStats: new Map(),
      issues: []
    };
  }

  async connect() {
    try {
      await mongoose.connect(process.env.MONGODB_URI);
      console.log('🔗 اتصال بقاعدة البيانات...');
    } catch (error) {
      console.error('❌ خطأ في الاتصال:', error);
      throw error;
    }
  }

  async analyzeWaiterSales() {
    console.log('🔍 تحليل مبيعات النادل الشامل...\n');

    // 1. جلب جميع النُدل
    const waiters = await User.find({ role: 'waiter' }).select('_id username name');
    console.log(`👥 وجد ${waiters.length} نادل في النظام`);

    // 2. جلب جميع الطلبات
    const allOrders = await Order.find({}).populate('staff.waiter', 'name username');
    this.reports.totalOrders = allOrders.length;
    console.log(`📋 وجد ${allOrders.length} طلب إجمالي`);

    // 3. تحليل كل نادل
    for (const waiter of waiters) {
      await this.analyzeWaiterOrders(waiter, allOrders);
    }

    // 4. البحث عن طلبات غير مربوطة
    await this.findUnmatchedOrders(allOrders, waiters);

    // 5. عرض التقرير النهائي
    this.generateReport();
  }

  async analyzeWaiterOrders(waiter, allOrders) {
    const waiterStats = {
      waiterId: waiter._id,
      waiterName: waiter.username,
      waiterFullName: waiter.name,
      ordersSources: {
        byStaffWaiter: 0,
        byWaiterName: 0,
        byWaiterId: 0,
        total: 0
      },
      salesByMethod: {
        staffWaiter: 0,
        waiterName: 0,
        waiterId: 0,
        total: 0
      },
      statusBreakdown: {},
      unmatchedOrders: []
    };

    // البحث بطرق مختلفة
    const ordersByStaffWaiter = allOrders.filter(order => 
      order.staff?.waiter && order.staff.waiter._id.toString() === waiter._id.toString()
    );

    const ordersByWaiterName = allOrders.filter(order => 
      order.waiterName === waiter.username || order.waiterName === waiter.name
    );

    const ordersByWaiterId = allOrders.filter(order => 
      order.waiterId && order.waiterId.toString() === waiter._id.toString()
    );

    // تجميع الطلبات (إزالة التكرار)
    const allWaiterOrders = new Set();
    
    ordersByStaffWaiter.forEach(order => {
      allWaiterOrders.add(order._id.toString());
      waiterStats.ordersSources.byStaffWaiter++;
      waiterStats.salesByMethod.staffWaiter += this.getOrderTotal(order);
    });

    ordersByWaiterName.forEach(order => {
      if (!allWaiterOrders.has(order._id.toString())) {
        waiterStats.ordersSources.byWaiterName++;
        waiterStats.salesByMethod.waiterName += this.getOrderTotal(order);
      }
      allWaiterOrders.add(order._id.toString());
    });

    ordersByWaiterId.forEach(order => {
      if (!allWaiterOrders.has(order._id.toString())) {
        waiterStats.ordersSources.byWaiterId++;
        waiterStats.salesByMethod.waiterId += this.getOrderTotal(order);
      }
      allWaiterOrders.add(order._id.toString());
    });

    waiterStats.ordersSources.total = allWaiterOrders.size;
    waiterStats.salesByMethod.total = waiterStats.salesByMethod.staffWaiter + 
                                      waiterStats.salesByMethod.waiterName + 
                                      waiterStats.salesByMethod.waiterId;

    // تحليل الحالات
    Array.from(allWaiterOrders).forEach(orderId => {
      const order = allOrders.find(o => o._id.toString() === orderId);
      if (order) {
        waiterStats.statusBreakdown[order.status] = 
          (waiterStats.statusBreakdown[order.status] || 0) + 1;
      }
    });

    this.reports.waiterStats.set(waiter._id.toString(), waiterStats);
    this.reports.matchedOrders += waiterStats.ordersSources.total;

    console.log(`✅ ${waiter.username}: ${waiterStats.ordersSources.total} طلب، ${waiterStats.salesByMethod.total.toFixed(2)} جنيه`);
  }

  async findUnmatchedOrders(allOrders, waiters) {
    const waiterIds = new Set(waiters.map(w => w._id.toString()));
    const unmatchedOrders = [];

    allOrders.forEach(order => {
      let isMatched = false;

      // فحص staff.waiter
      if (order.staff?.waiter && waiterIds.has(order.staff.waiter._id.toString())) {
        isMatched = true;
      }

      // فحص waiterId
      if (order.waiterId && waiterIds.has(order.waiterId.toString())) {
        isMatched = true;
      }

      // فحص waiterName
      if (order.waiterName) {
        const matchingWaiter = waiters.find(w => 
          w.username === order.waiterName || w.name === order.waiterName
        );
        if (matchingWaiter) {
          isMatched = true;
        }
      }

      if (!isMatched) {
        unmatchedOrders.push({
          orderId: order._id,
          orderNumber: order.orderNumber,
          waiterName: order.waiterName,
          waiterId: order.waiterId,
          staffWaiter: order.staff?.waiter?._id,
          total: this.getOrderTotal(order),
          status: order.status,
          createdAt: order.createdAt
        });
      }
    });

    this.reports.unmatchedOrders = unmatchedOrders.length;
    
    if (unmatchedOrders.length > 0) {
      console.log(`\n⚠️  وجد ${unmatchedOrders.length} طلب غير مربوط بنادل:`);
      unmatchedOrders.slice(0, 5).forEach(order => {
        console.log(`   - طلب ${order.orderNumber}: ${order.total} جنيه، النادل: ${order.waiterName || 'غير محدد'}`);
      });
      if (unmatchedOrders.length > 5) {
        console.log(`   ... و ${unmatchedOrders.length - 5} طلب آخر`);
      }
    }
  }

  getOrderTotal(order) {
    return order.totals?.total || 
           order.totalPrice || 
           order.totalAmount || 
           order.pricing?.total || 0;
  }

  generateReport() {
    console.log('\n' + '='.repeat(60));
    console.log('📊 تقرير تحليل مبيعات النادل النهائي');
    console.log('='.repeat(60));

    console.log(`\n📈 إحصائيات عامة:`);
    console.log(`   إجمالي الطلبات: ${this.reports.totalOrders}`);
    console.log(`   الطلبات المربوطة: ${this.reports.matchedOrders}`);
    console.log(`   الطلبات غير المربوطة: ${this.reports.unmatchedOrders}`);
    console.log(`   نسبة الربط: ${((this.reports.matchedOrders / this.reports.totalOrders) * 100).toFixed(1)}%`);

    console.log(`\n👥 تفاصيل النُدل:`);
    let totalSales = 0;
    
    this.reports.waiterStats.forEach((stats, waiterId) => {
      console.log(`\n📋 ${stats.waiterName} (${stats.waiterFullName}):`);
      console.log(`   إجمالي الطلبات: ${stats.ordersSources.total}`);
      console.log(`   طريقة الربط:`);
      console.log(`     - staff.waiter: ${stats.ordersSources.byStaffWaiter} طلب (${stats.salesByMethod.staffWaiter.toFixed(2)} جنيه)`);
      console.log(`     - waiterName: ${stats.ordersSources.byWaiterName} طلب (${stats.salesByMethod.waiterName.toFixed(2)} جنيه)`);
      console.log(`     - waiterId: ${stats.ordersSources.byWaiterId} طلب (${stats.salesByMethod.waiterId.toFixed(2)} جنيه)`);
      console.log(`   إجمالي المبيعات: ${stats.salesByMethod.total.toFixed(2)} جنيه`);
      
      if (Object.keys(stats.statusBreakdown).length > 0) {
        console.log(`   توزيع الحالات: ${JSON.stringify(stats.statusBreakdown)}`);
      }
      
      totalSales += stats.salesByMethod.total;
    });

    console.log(`\n💰 إجمالي مبيعات جميع النُدل: ${totalSales.toFixed(2)} جنيه`);

    // تحليل المشاكل
    if (this.reports.unmatchedOrders > 0) {
      console.log(`\n⚠️  مشاكل مكتشفة:`);
      console.log(`   - ${this.reports.unmatchedOrders} طلب غير مربوط بنادل`);
      console.log(`   - قد يؤدي إلى فقدان في حساب المبيعات`);
      console.log(`   - يُنصح بمراجعة هذه الطلبات وربطها بالنُدل المناسبين`);
    } else {
      console.log(`\n✅ لا توجد مشاكل في ربط الطلبات بالنُدل`);
    }
  }
}

// تشغيل التحليل
async function main() {
  const analyzer = new WaiterSalesAnalyzer();
  
  try {
    await analyzer.connect();
    await analyzer.analyzeWaiterSales();
  } catch (error) {
    console.error('❌ خطأ في التحليل:', error);
  } finally {
    await mongoose.disconnect();
    console.log('\n🔌 تم قطع الاتصال بقاعدة البيانات');
  }
}

main();

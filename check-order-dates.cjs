const fetch = require('node-fetch');

async function checkOrderDates() {
  try {
    console.log('🔍 فحص تواريخ الطلبات في قاعدة البيانات...');
    
    const response = await fetch('https://deshacoffee-production.up.railway.app/api/v1/orders');
    const result = await response.json();
    const orders = result.data || result;
    
    console.log('📊 استجابة API:', result);
    console.log(`📋 إجمالي الطلبات: ${Array.isArray(orders) ? orders.length : 'غير صحيح'}`);
    
    if (!Array.isArray(orders)) {
      console.log('❌ البيانات المستلمة ليست array:', orders);
      return;
    }
    
    const now = Date.now();
    const dayInMs = 24 * 60 * 60 * 1000;
    
    orders.forEach(order => {
      const orderDate = new Date(order.createdAt);
      const ageInDays = Math.round((now - orderDate.getTime()) / dayInMs);
      
      console.log(`📅 طلب ${order._id.slice(-6)}: ${order.tableNumber} - عمر ${ageInDays} يوم - ${orderDate.toISOString()}`);
      console.log(`   النادل: ${order.waiterName} (ID: ${order.waiterId})`);
      console.log(`   الحالة: ${order.status} - السعر: ${order.totalPrice}`);
      console.log('');
    });
    
    // فحص الطلبات خلال آخر 7 أيام
    const recentOrders = orders.filter(order => {
      const orderDate = new Date(order.createdAt);
      return (now - orderDate.getTime()) < (7 * dayInMs);
    });
    
    console.log(`✅ طلبات آخر 7 أيام: ${recentOrders.length} من ${orders.length}`);
    
    // فحص طلبات Bosy
    const bosyOrders = orders.filter(order => 
      order.waiterId === '684c864e558dd1359d2380f7' || 
      order.waiterName === 'بوسي'
    );
    
    console.log(`👤 طلبات النادلة Bosy: ${bosyOrders.length}`);
    bosyOrders.forEach(order => {
      const orderDate = new Date(order.createdAt);
      const ageInDays = Math.round((now - orderDate.getTime()) / dayInMs);
      console.log(`  - طاولة ${order.tableNumber}: عمر ${ageInDays} يوم`);
    });
    
  } catch (error) {
    console.error('❌ خطأ في فحص التواريخ:', error);
  }
}

checkOrderDates();

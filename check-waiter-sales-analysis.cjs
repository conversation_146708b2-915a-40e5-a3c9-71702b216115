const mongoose = require('mongoose');
require('dotenv').config();

const Order = require('./backend/models/Order');
const User = require('./backend/models/User');

async function analyzeWaiterSales() {
  try {
    console.log('🔗 الاتصال بقاعدة البيانات...');
    
    const mongoUri = process.env.MONGODB_URI;
    if (!mongoUri) {
      throw new Error('لم يتم العثور على MONGODB_URI في متغيرات البيئة');
    }
    
    await mongoose.connect(mongoUri, {
      serverSelectionTimeoutMS: 5000,
      socketTimeoutMS: 45000,
    });
    console.log('✅ تم الاتصال بقاعدة البيانات بنجاح');

    console.log('\n🔍 جلب بيانات النُدل والطلبات...');
    
    // جلب جميع النُدل
    const waiters = await User.find({ role: 'waiter' }).select('_id username name email isActive');
    console.log(`👥 وجد ${waiters.length} نادل في النظام`);
    
    if (waiters.length === 0) {
      console.log('❌ لا يوجد نُدل في النظام');
      return;
    }

    // جلب جميع الطلبات
    const allOrders = await Order.find({}).select('_id orderNumber status waiterName waiterId staff totalPrice totalAmount totals pricing createdAt');
    console.log(`📋 وجد ${allOrders.length} طلب إجمالي في النظام`);
    
    if (allOrders.length === 0) {
      console.log('❌ لا توجد طلبات في النظام');
      return;
    }

    console.log('\n' + '='.repeat(70));
    console.log('📊 تحليل كيفية حساب إجمالي المبيعات للنادل');
    console.log('='.repeat(70));

    let totalMatchedOrders = 0;
    let totalUnmatchedOrders = 0;
    let totalSalesAmount = 0;

    // تحليل كل نادل
    for (const waiter of waiters) {
      console.log(`\n🔍 تحليل النادل: ${waiter.username} (${waiter.name || 'بدون اسم'})`);
      console.log(`   الحالة: ${waiter.isActive ? 'نشط' : 'غير نشط'}`);
      
      // البحث عن الطلبات بطرق مختلفة
      const ordersByMethods = {
        staffWaiter: [],
        waiterName: [],
        waiterId: []
      };

      // 1. البحث عبر staff.waiter
      ordersByMethods.staffWaiter = allOrders.filter(order => 
        order.staff?.waiter && order.staff.waiter.toString() === waiter._id.toString()
      );

      // 2. البحث عبر waiterName
      ordersByMethods.waiterName = allOrders.filter(order => 
        order.waiterName === waiter.username || order.waiterName === waiter.name
      );

      // 3. البحث عبر waiterId
      ordersByMethods.waiterId = allOrders.filter(order => 
        order.waiterId && order.waiterId.toString() === waiter._id.toString()
      );

      // دمج الطلبات (إزالة التكرار)
      const allWaiterOrderIds = new Set();
      
      ordersByMethods.staffWaiter.forEach(order => allWaiterOrderIds.add(order._id.toString()));
      ordersByMethods.waiterName.forEach(order => allWaiterOrderIds.add(order._id.toString()));
      ordersByMethods.waiterId.forEach(order => allWaiterOrderIds.add(order._id.toString()));

      const waiterOrders = Array.from(allWaiterOrderIds).map(id => 
        allOrders.find(order => order._id.toString() === id)
      );

      console.log(`   📝 طرق الربط:`);
      console.log(`      staff.waiter: ${ordersByMethods.staffWaiter.length} طلب`);
      console.log(`      waiterName: ${ordersByMethods.waiterName.length} طلب`);
      console.log(`      waiterId: ${ordersByMethods.waiterId.length} طلب`);
      console.log(`   📊 إجمالي الطلبات (بعد إزالة التكرار): ${waiterOrders.length}`);

      if (waiterOrders.length > 0) {
        totalMatchedOrders += waiterOrders.length;
        
        // حساب المبيعات
        let waiterTotalSales = 0;
        let completedOrdersCount = 0;
        const statusBreakdown = {};

        waiterOrders.forEach(order => {
          // حساب قيمة الطلب - استخدام نفس المنطق المستخدم في النظام
          let orderAmount = 0;
          if (order.totals && order.totals.total) {
            orderAmount = order.totals.total;
          } else if (order.totalPrice) {
            orderAmount = order.totalPrice;
          } else if (order.totalAmount) {
            orderAmount = order.totalAmount;
          } else if (order.pricing && order.pricing.total) {
            orderAmount = order.pricing.total;
          }
          
          waiterTotalSales += orderAmount;
          
          // تحليل الحالات
          const status = order.status || 'غير محدد';
          statusBreakdown[status] = (statusBreakdown[status] || 0) + 1;
          
          if (status === 'completed' || status === 'delivered' || status === 'served') {
            completedOrdersCount++;
          }
        });

        totalSalesAmount += waiterTotalSales;

        console.log(`   💰 إجمالي المبيعات: ${waiterTotalSales.toFixed(2)} جنيه`);
        console.log(`   ✅ الطلبات المكتملة: ${completedOrdersCount}`);
        console.log(`   📋 توزيع الحالات: ${JSON.stringify(statusBreakdown)}`);
        
        if (completedOrdersCount > 0) {
          console.log(`   📈 متوسط قيمة الطلب: ${(waiterTotalSales / waiterOrders.length).toFixed(2)} جنيه`);
        }
      } else {
        console.log(`   ❌ لا توجد طلبات مربوطة بهذا النادل`);
      }
    }

    // فحص الطلبات غير المربوطة
    console.log('\n' + '='.repeat(70));
    console.log('🔍 فحص الطلبات غير المربوطة بأي نادل');
    console.log('='.repeat(70));

    const unmatchedOrders = [];
    
    allOrders.forEach(order => {
      let isMatched = false;
      
      // فحص جميع طرق الربط
      for (const waiter of waiters) {
        if (order.staff?.waiter && order.staff.waiter.toString() === waiter._id.toString()) {
          isMatched = true;
          break;
        }
        if (order.waiterName === waiter.username || order.waiterName === waiter.name) {
          isMatched = true;
          break;
        }
        if (order.waiterId && order.waiterId.toString() === waiter._id.toString()) {
          isMatched = true;
          break;
        }
      }
      
      if (!isMatched) {
        const orderAmount = order.totals?.total || order.totalPrice || order.totalAmount || order.pricing?.total || 0;
        unmatchedOrders.push({
          id: order._id,
          orderNumber: order.orderNumber,
          amount: orderAmount,
          status: order.status,
          waiterName: order.waiterName,
          waiterId: order.waiterId,
          createdAt: order.createdAt
        });
      }
    });

    totalUnmatchedOrders = unmatchedOrders.length;

    console.log(`📊 إجمالي الطلبات المربوطة: ${totalMatchedOrders}`);
    console.log(`❌ إجمالي الطلبات غير المربوطة: ${totalUnmatchedOrders}`);
    console.log(`📈 نسبة الربط: ${((totalMatchedOrders / allOrders.length) * 100).toFixed(1)}%`);

    if (unmatchedOrders.length > 0) {
      console.log('\n⚠️  الطلبات غير المربوطة (عينة من أول 10):');
      unmatchedOrders.slice(0, 10).forEach((order, index) => {
        console.log(`   ${index + 1}. طلب ${order.orderNumber}: ${order.amount} جنيه، النادل: ${order.waiterName || 'غير محدد'}, الحالة: ${order.status}`);
      });
      
      const unmatchedSales = unmatchedOrders.reduce((sum, order) => sum + order.amount, 0);
      console.log(`   💸 إجمالي المبيعات المفقودة: ${unmatchedSales.toFixed(2)} جنيه`);
    }

    console.log('\n' + '='.repeat(70));
    console.log('📋 ملخص التحليل النهائي');
    console.log('='.repeat(70));
    console.log(`إجمالي الطلبات في النظام: ${allOrders.length}`);
    console.log(`الطلبات المربوطة بنُدل: ${totalMatchedOrders}`);
    console.log(`الطلبات غير المربوطة: ${totalUnmatchedOrders}`);
    console.log(`نسبة الربط: ${((totalMatchedOrders / allOrders.length) * 100).toFixed(1)}%`);
    console.log(`إجمالي مبيعات النُدل: ${totalSalesAmount.toFixed(2)} جنيه`);

    if (totalUnmatchedOrders > 0) {
      console.log('\n⚠️  تحذير: يوجد فقدان في حساب المبيعات!');
      console.log('   الأسباب المحتملة:');
      console.log('   1. طلبات تم إنشاؤها بدون ربط نادل صحيح');
      console.log('   2. نُدل تم حذفهم من النظام بعد إنشاء طلبات');
      console.log('   3. أخطاء في تخزين معرف النادل');
      console.log('\n💡 التوصيات:');
      console.log('   1. مراجعة الطلبات غير المربوطة وربطها بالنُدل المناسبين');
      console.log('   2. التأكد من أن جميع الطلبات الجديدة تحتوي على معرف نادل صحيح');
      console.log('   3. تحديث منطق إنشاء الطلبات لضمان الربط الصحيح');
    } else {
      console.log('\n✅ ممتاز! جميع الطلبات مربوطة بنُدل ولا يوجد فقدان في المبيعات');
    }

  } catch (error) {
    console.error('❌ خطأ في التحليل:', error.message);
  } finally {
    await mongoose.disconnect();
    console.log('\n🔌 تم قطع الاتصال بقاعدة البيانات');
  }
}

analyzeWaiterSales();

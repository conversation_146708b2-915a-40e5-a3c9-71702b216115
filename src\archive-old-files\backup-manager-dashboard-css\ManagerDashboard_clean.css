/* Manager Dashboard Styles */

/* تحسينات عامة للتخطيط */
.manager-dashboard {
  display: flex;
  flex-direction: column;
  min-height: 100vh;
  background: var(--light-bg, #f8f9fa);
  font-family: 'Se<PERSON>e UI', Tahoma, Geneva, Verdana, sans-serif;
  direction: rtl;
  overflow-x: hidden; /* منع التمرير الأفقي */
}

/* Header */
.manager-header {
  background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
  color: white;
  padding: 1rem 2rem;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  position: sticky;
  top: 0;
  z-index: 1000; /* أعلى من القائمة الجانبية */
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.header-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  max-width: 1400px;
  margin: 0 auto;
}

.header-left {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.sidebar-toggle {
  background: none;
  border: none;
  color: white;
  font-size: 1.5rem;
  cursor: pointer;
  padding: 0.5rem;
  border-radius: 8px;
  transition: background-color 0.3s ease;
  display: none; /* مخفي في الشاشات الكبيرة */
}

.sidebar-toggle:hover {
  background-color: rgba(255, 255, 255, 0.1);
}

/* إظهار زر الـ toggle في الهاتف فقط */
@media (max-width: 768px) {
  .sidebar-toggle {
    display: block !important;
  }
  
  .dashboard-content {
    position: relative;
  }
  
  .manager-sidebar {
    position: fixed;
    top: 80px;
    right: 0;
    transform: translateX(100%);
    transition: transform 0.3s ease-in-out;
    z-index: 999;
  }
  
  .manager-sidebar.open {
    transform: translateX(0);
  }
  
  .manager-main {
    padding: 1rem;
    width: 100%;
  }
  
  .sidebar-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.5);
    z-index: 998;
    -webkit-backdrop-filter: blur(2px);
    backdrop-filter: blur(2px);
    display: block;
  }
}

/* في الشاشات الكبيرة: القائمة الجانبية مفتوحة دائماً */

/* زر إصلاح المبيعات */
.sales-fix-btn {
  background: linear-gradient(135deg, #e74c3c 0%, #c0392b 100%) !important;
  color: white !important;
  border: 2px solid #c0392b !important;
  position: relative;
  overflow: hidden;
}

.sales-fix-btn:hover {
  background: linear-gradient(135deg, #c0392b 0%, #a93226 100%) !important;
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(231, 76, 60, 0.3);
}

.sales-fix-btn:before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
  transition: left 0.5s;
}

.sales-fix-btn:hover:before {
  left: 100%;
}

.sales-fix-btn i {
  margin-left: 8px;
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0% { transform: scale(1); }
  50% { transform: scale(1.1); }
  100% { transform: scale(1); }
}
@media (min-width: 769px) {
  .manager-sidebar {
    position: relative;
    transform: translateX(0);
    transition: none;
  }
  
  .sidebar-overlay {
    display: none;
  }
  
  .sidebar-toggle {
    display: none;
  }
}

.header-left h1 {
  margin: 0;
  font-size: 1.5rem;
  font-weight: 600;
}

.header-right {
  display: flex;
  align-items: center;
  gap: 1rem;
}

/* Reset Buttons */
.reset-buttons {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  margin-right: 1rem;
  flex-wrap: wrap;
}

.reset-btn {
  background: rgba(255, 255, 255, 0.1);
  color: white;
  border: 1px solid rgba(255, 255, 255, 0.2);
  padding: 0.4rem 0.8rem;
  border-radius: 6px;
  cursor: pointer;
  font-size: 0.85rem;
  display: flex;
  align-items: center;
  gap: 0.3rem;
  transition: all 0.3s ease;
  white-space: nowrap;
}

.reset-btn:hover {
  background: rgba(255, 255, 255, 0.2);
  border-color: rgba(255, 255, 255, 0.4);
  transform: translateY(-1px);
}

.reset-btn.reset-orders {
  background: rgba(52, 152, 219, 0.8);
  border-color: #3498db;
}

.reset-btn.reset-orders:hover {
  background: rgba(52, 152, 219, 1);
}

.reset-btn.reset-tables {
  background: rgba(46, 204, 113, 0.8);
  border-color: #2ecc71;
}

.reset-btn.reset-tables:hover {
  background: rgba(46, 204, 113, 1);
}

.reset-btn.reset-discounts {
  background: rgba(241, 196, 15, 0.8);
  border-color: #f1c40f;
}

.reset-btn.reset-discounts:hover {
  background: rgba(241, 196, 15, 1);
}

.reset-btn.reset-all {
  background: rgba(231, 76, 60, 0.8);
  border-color: #e74c3c;
}

.reset-btn.reset-all:hover {
  background: rgba(231, 76, 60, 1);
}

.reset-btn i {
  font-size: 0.8rem;
}

/* Dashboard Content */
.dashboard-content {
  display: flex;
  flex: 1;
  max-width: 1400px;
  margin: 0 auto;
  width: 100%;
  position: relative;
  min-height: calc(100vh - 80px);
  gap: 0; /* إزالة أي مسافات غير مرغوبة */
}

/* Sidebar */
.manager-sidebar {
  width: 280px;
  background: white;
  box-shadow: 2px 0 10px rgba(0, 0, 0, 0.1);
  height: calc(100vh - 80px);
  z-index: 500;
  overflow-y: auto;
  transition: all 0.3s ease;
  /* Modern scrollbar for Firefox - fallback for older browsers */
  scrollbar-width: thin; /* غير مدعوم في Chrome < 121, Safari */
  scrollbar-color: #cbd5e0 #f7fafc; /* غير مدعوم في Chrome < 121, Safari */
  /* Webkit fallback for Chrome, Edge, Safari */
}

.manager-sidebar::-webkit-scrollbar {
  width: 6px;
}

.manager-sidebar::-webkit-scrollbar-thumb {
  background: #cbd5e0;
  border-radius: 4px;
}

.manager-sidebar::-webkit-scrollbar-track {
  background: #f7fafc;
}

/* تحسين شكل الـ scrollbar */
.manager-sidebar::-webkit-scrollbar {
  width: 6px;
}

.manager-sidebar::-webkit-scrollbar-track {
  background: #f7fafc;
}

.manager-sidebar::-webkit-scrollbar-thumb {
  background: #cbd5e0;
  border-radius: 3px;
}

.manager-sidebar::-webkit-scrollbar-thumb:hover {
  background: #a0aec0;
}

/* في الحالة المغلقة (للهاتف فقط) */
@media (max-width: 768px) {
  .manager-sidebar:not(.open) {
    transform: translateX(100%);
  }
}

/* في الشاشات الكبيرة */
@media (min-width: 769px) {
  .manager-sidebar:not(.open) {
    transform: translateX(0);
  }
}

.sidebar-content {
  padding: 2rem 1rem;
}

.manager-profile {
  text-align: center;
  margin-bottom: 2rem;
  padding-bottom: 2rem;
  border-bottom: 1px solid #eee;
}

.manager-avatar {
  width: 80px;
  height: 80px;
  border-radius: 50%;
  background: linear-gradient(135deg, #3498db, #2980b9);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 2rem;
  margin: 0 auto 1rem;
  box-shadow: 0 4px 15px rgba(52, 152, 219, 0.3);
}

.manager-profile h3 {
  margin: 0 0 0.5rem 0;
  color: #2c3e50;
  font-size: 1.2rem;
}

.manager-profile p {
  margin: 0;
  color: #7f8c8d;
  font-size: 0.9rem;
}

/* Navigation */
.manager-nav {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.nav-btn {
  display: flex;
  align-items: center;
  gap: 1rem;
  padding: 1rem;
  background: none;
  border: none;
  border-radius: 12px;
  cursor: pointer;
  transition: all 0.3s ease;
  text-align: right;
  font-size: 1rem;
  color: #2c3e50;
}

.nav-btn:hover {
  background: #f8f9fa;
  transform: translateX(-5px);
}

.nav-btn.active {
  background: linear-gradient(135deg, #3498db, #2980b9);
  color: white;
  box-shadow: 0 4px 15px rgba(52, 152, 219, 0.3);
}

.nav-btn i {
  font-size: 1.2rem;
  width: 20px;
  text-align: center;
}

/* Main Content */
.manager-main {
  flex: 1;
  padding: 2rem;
  min-height: calc(100vh - 80px);
  overflow-x: auto;
  width: 100%;
  box-sizing: border-box;
}

/* تحسين smooth scrolling */
.manager-sidebar,
.manager-main {
  scroll-behavior: smooth;
}

/* منع تداخل المحتوى */
.manager-main * {
  box-sizing: border-box;
}

/* تحسين عرض الكروت والجداول */
.stats-grid,
.summary-cards,
.data-table {
  width: 100%;
  max-width: 100%;
  overflow-x: auto;
}

/* Sidebar Overlay for mobile */
.sidebar-overlay {
  display: none;
}

/* تحسينات للتابلت */
@media (min-width: 769px) and (max-width: 1024px) {
  .manager-sidebar {
    width: 260px;
  }
  
  .manager-main {
    padding: 1.5rem;
  }
  
  .stats-grid {
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1rem;
  }
}

/* تحسينات للشاشات الكبيرة جداً */
@media (min-width: 1400px) {
  .dashboard-content {
    max-width: 1600px;
  }
  
  .manager-sidebar {
    width: 320px;
  }
  
  .manager-main {
    padding: 3rem;
  }
}

/* Loading */
.loading {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 60vh;
  gap: 1rem;
  color: #2c3e50;
}

.loading i {
  font-size: 2rem;
  color: #3498db;
}

/* Manager Home */
.manager-home {
  max-width: 1200px;
  margin: 0 auto;
}

.manager-header .welcome-section {
  text-align: center;
  margin-bottom: 2rem;
}

.manager-header .welcome-section h1 {
  margin: 0 0 0.5rem 0;
  color: #2c3e50;
  font-size: 2.5rem;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 1rem;
}

.role-badge {
  background: linear-gradient(135deg, #3498db, #2980b9);
  color: white;
  padding: 0.5rem 1rem;
  border-radius: 20px;
  font-size: 0.9rem;
  font-weight: 500;
}

/* Stats Grid */
.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 1.5rem;
  margin-bottom: 3rem;
}

.stat-card {
  background: white;
  padding: 2rem;
  border-radius: 16px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
  display: flex;
  align-items: center;
  gap: 1.5rem;
  transition: all 0.3s ease;
}

.stat-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 8px 30px rgba(0, 0, 0, 0.15);
}

.stat-icon {
  width: 60px;
  height: 60px;
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.5rem;
  color: white;
}

.stat-card.orders .stat-icon {
  background: linear-gradient(135deg, #e74c3c, #c0392b);
}

.stat-card.sales .stat-icon {
  background: linear-gradient(135deg, #27ae60, #229954);
}

.stat-card.employees .stat-icon {
  background: linear-gradient(135deg, #f39c12, #e67e22);
}

.stat-card.tables .stat-icon {
  background: linear-gradient(135deg, #9b59b6, #8e44ad);
}

.stat-content h3 {
  margin: 0 0 0.5rem 0;
  font-size: 2rem;
  font-weight: 700;
  color: #2c3e50;
}

.stat-content p {
  margin: 0;
  color: #7f8c8d;
  font-size: 0.9rem;
}

/* Orders Stats */
.orders-stats {
  background: white;
  padding: 2rem;
  border-radius: 16px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
}

.orders-stats h2 {
  margin: 0 0 2rem 0;
  color: #2c3e50;
  text-align: center;
  font-size: 1.5rem;
}

.orders-stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 1.5rem;
}

.order-stat {
  text-align: center;
  padding: 1.5rem;
  border-radius: 12px;
  transition: all 0.3s ease;
}

.order-stat:hover {
  transform: translateY(-3px);
}

.order-stat.pending {
  background: linear-gradient(135deg, #f39c12, #e67e22);
  color: white;
}

.order-stat.preparing {
  background: linear-gradient(135deg, #e74c3c, #c0392b);
  color: white;
}

.order-stat.ready {
  background: linear-gradient(135deg, #27ae60, #229954);
  color: white;
}

.order-stat.completed {
  background: linear-gradient(135deg, #3498db, #2980b9);
  color: white;
}

.order-stat i {
  font-size: 2rem;
  margin-bottom: 1rem;
  display: block;
}

.order-stat .count {
  display: block;
  font-size: 2rem;
  font-weight: 700;
  margin-bottom: 0.5rem;
}

.order-stat .label {
  font-size: 0.9rem;
  opacity: 0.9;
}

/* Coming Soon */
.coming-soon {
  text-align: center;
  padding: 4rem 2rem;
  color: #7f8c8d;
  font-size: 1.2rem;
}

/* Orders Screen */
.orders-screen {
  padding: 2rem;
}

.orders-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 2rem;
  flex-wrap: wrap;
  gap: 1rem;
}

.orders-header h1 {
  margin: 0;
  color: #2c3e50;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.orders-filters {
  display: flex;
  gap: 1rem;
  flex-wrap: wrap;
}

.filter-select {
  padding: 0.5rem 1rem;
  border: 1px solid #ddd;
  border-radius: 8px;
  background: white;
  font-size: 0.9rem;
  min-width: 150px;
}

.waiter-stats {
  margin-bottom: 2rem;
}

.waiter-stats h2 {
  margin-bottom: 1rem;
  color: #2c3e50;
}

.waiter-stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 1rem;
}

.waiter-stat-card {
  background: white;
  padding: 1.5rem;
  border-radius: 12px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.waiter-info {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.waiter-info i {
  font-size: 2rem;
  color: #3498db;
}

.waiter-info h3 {
  margin: 0;
  color: #2c3e50;
}

.waiter-numbers {
  display: flex;
  gap: 1rem;
}

.waiter-numbers .stat {
  text-align: center;
}

.waiter-numbers .number {
  display: block;
  font-size: 1.5rem;
  font-weight: bold;
  color: #2c3e50;
}

.waiter-numbers .label {
  font-size: 0.8rem;
  color: #7f8c8d;
}

.discount-requests {
  margin-bottom: 2rem;
}

.discount-requests h2 {
  margin-bottom: 1rem;
  color: #e74c3c;
}

.discount-requests-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 1rem;
}

.discount-request-card {
  background: white;
  padding: 1.5rem;
  border-radius: 12px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  border-left: 4px solid #e74c3c;
}

.request-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1rem;
}

.order-number {
  font-weight: bold;
  color: #2c3e50;
}

.waiter-name {
  color: #7f8c8d;
  font-size: 0.9rem;
}

.request-details {
  margin-bottom: 1rem;
}

.amount {
  font-size: 1.1rem;
  font-weight: bold;
  color: #e74c3c;
  margin-bottom: 0.5rem;
}

.reason {
  color: #7f8c8d;
  font-size: 0.9rem;
}

.request-actions {
  display: flex;
  gap: 0.5rem;
}

.approve-btn, .reject-btn {
  padding: 0.5rem 1rem;
  border: none;
  border-radius: 6px;
  cursor: pointer;
  font-size: 0.9rem;
  transition: all 0.3s ease;
}

.approve-btn {
  background: #27ae60;
  color: white;
}

.approve-btn:hover {
  background: #229954;
}

.reject-btn {
  background: #e74c3c;
  color: white;
}

.reject-btn:hover {
  background: #c0392b;
}

.orders-list h2 {
  margin-bottom: 1rem;
  color: #2c3e50;
}

.orders-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 1rem;
}

.order-card {
  background: white;
  padding: 1.5rem;
  border-radius: 12px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
}

.order-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
}

.order-card.pending {
  border-left: 4px solid #f39c12;
}

.order-card.preparing {
  border-left: 4px solid #e74c3c;
}

.order-card.ready {
  border-left: 4px solid #27ae60;
}

.order-card.completed {
  border-left: 4px solid #3498db;
}

.order-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1rem;
}

.order-number {
  font-weight: bold;
  color: #2c3e50;
}

.order-status {
  padding: 0.25rem 0.75rem;
  border-radius: 20px;
  font-size: 0.8rem;
  font-weight: 500;
}

.order-status.pending {
  background: #f39c12;
  color: white;
}

.order-status.preparing {
  background: #e74c3c;
  color: white;
}

.order-status.ready {
  background: #27ae60;
  color: white;
}

.order-status.completed {
  background: #3498db;
  color: white;
}

.order-info {
  margin-bottom: 1rem;
}

.order-detail {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  margin-bottom: 0.5rem;
  color: #7f8c8d;
  font-size: 0.9rem;
}

.order-detail i {
  width: 16px;
  text-align: center;
}

.order-summary {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1rem;
  padding: 0.75rem;
  background: #f8f9fa;
  border-radius: 8px;
}

.items-count {
  color: #7f8c8d;
  font-size: 0.9rem;
}

.total-amount {
  font-weight: bold;
  color: #2c3e50;
  font-size: 1.1rem;
}

.order-time {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  color: #7f8c8d;
  font-size: 0.8rem;
  margin-bottom: 1rem;
}

.details-btn {
  width: 100%;
  padding: 0.75rem;
  background: #3498db;
  color: white;
  border: none;
  border-radius: 8px;
  cursor: pointer;
  font-size: 0.9rem;
  transition: all 0.3s ease;
}

.details-btn:hover {
  background: #2980b9;
}

/* Employees Screen */
.employees-screen {
  padding: 2rem;
}

.employees-header h1 {
  margin: 0 0 2rem 0;
  color: #2c3e50;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.active-shifts-section, .inactive-employees-section, .performance-stats {
  margin-bottom: 3rem;
}

.active-shifts-section h2, .inactive-employees-section h2, .performance-stats h2 {
  margin-bottom: 1.5rem;
  color: #2c3e50;
}

.shifts-grid, .employees-grid, .performance-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
  gap: 1.5rem;
}

.shift-card, .employee-card, .performance-card {
  background: white;
  padding: 2rem;
  border-radius: 16px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
}

.shift-card:hover, .employee-card:hover, .performance-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 8px 30px rgba(0, 0, 0, 0.15);
}

.shift-card.active {
  border-left: 4px solid #27ae60;
}

.employee-info {
  display: flex;
  align-items: center;
  gap: 1rem;
  margin-bottom: 1.5rem;
}

.employee-avatar {
  width: 60px;
  height: 60px;
  border-radius: 50%;
  background: linear-gradient(135deg, #3498db, #2980b9);
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 1.5rem;
}

.employee-details h3 {
  margin: 0 0 0.25rem 0;
  color: #2c3e50;
}

.employee-details .role {
  color: #7f8c8d;
  font-size: 0.9rem;
  display: block;
  margin-bottom: 0.25rem;
}

.employee-details .status {
  padding: 0.25rem 0.5rem;
  border-radius: 12px;
  font-size: 0.8rem;
  font-weight: 500;
}

.status.active {
  background: #d5f4e6;
  color: #27ae60;
}

.status.inactive {
  background: #fadbd8;
  color: #e74c3c;
}

.shift-stats {
  margin-bottom: 1.5rem;
}

.shift-stats .stat {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 0.75rem;
  padding: 0.5rem;
  background: #f8f9fa;
  border-radius: 8px;
}

.shift-stats .label {
  color: #7f8c8d;
  font-size: 0.9rem;
}

.shift-stats .value {
  font-weight: bold;
  color: #2c3e50;
}

.shift-actions, .employee-actions {
  display: flex;
  gap: 0.5rem;
}

.start-shift-btn, .end-shift-btn {
  flex: 1;
  padding: 0.75rem;
  border: none;
  border-radius: 8px;
  cursor: pointer;
  font-size: 0.9rem;
  transition: all 0.3s ease;
}

.start-shift-btn {
  background: #27ae60;
  color: white;
}

.start-shift-btn:hover:not(:disabled) {
  background: #229954;
}

.start-shift-btn:disabled {
  background: #bdc3c7;
  cursor: not-allowed;
}

.end-shift-btn {
  background: #e74c3c;
  color: white;
}

.end-shift-btn:hover {
  background: #c0392b;
}

.performance-card .employee-header {
  display: flex;
  align-items: center;
  gap: 1rem;
  margin-bottom: 1.5rem;
}

.performance-card .employee-header i {
  font-size: 2rem;
  color: #3498db;
}

.performance-card .employee-header h3 {
  margin: 0;
  color: #2c3e50;
}

.performance-card .employee-header .role {
  color: #7f8c8d;
  font-size: 0.9rem;
}

.performance-stats-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 1rem;
}

.perf-stat {
  text-align: center;
  padding: 1rem;
  background: #f8f9fa;
  border-radius: 8px;
}

.perf-stat .number {
  display: block;
  font-size: 1.5rem;
  font-weight: bold;
  color: #2c3e50;
  margin-bottom: 0.25rem;
}

.perf-stat .label {
  color: #7f8c8d;
  font-size: 0.8rem;
}

/* Socket.IO Connection Status Indicator */
.connection-status {
  display: flex;
  align-items: center;
  margin-right: 1rem;
  transition: all 0.3s ease;
}

.socket-indicator {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.4rem 0.8rem;
  border-radius: 20px;
  background: rgba(255, 255, 255, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
  transition: all 0.3s ease;
}

.socket-indicator.connected {
  background: rgba(46, 204, 113, 0.2);
  border-color: rgba(46, 204, 113, 0.5);
}

.socket-indicator.disconnected {
  background: rgba(231, 76, 60, 0.2);
  border-color: rgba(231, 76, 60, 0.5);
}

.socket-dot {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background: #e74c3c;
  animation: pulse-red 2s infinite;
}

.socket-indicator.connected .socket-dot {
  background: #2ecc71;
  animation: pulse-green 2s infinite;
}

.socket-text {
  font-size: 0.8rem;
  font-weight: 500;
  color: white;
}

@keyframes pulse-green {
  0%, 100% {
    opacity: 1;
    transform: scale(1);
  }
  50% {
    opacity: 0.7;
    transform: scale(1.1);
  }
}

@keyframes pulse-red {
  0%, 100% {
    opacity: 1;
    transform: scale(1);
  }
  50% {
    opacity: 0.7;
    transform: scale(1.1);
  }
}

.manager-name {
  font-weight: 500;
  color: white;
}

/* Responsive Design */
@media (max-width: 768px) {
  .manager-sidebar {
    width: 100%;
    top: 70px;
    height: calc(100vh - 70px);
    transform: translateX(-100%); /* مخفية بشكل افتراضي في الهاتف */
    z-index: 999; /* أعلى من الهيدر */
  }

  .manager-sidebar.open {
    transform: translateX(0); /* تظهر عند الفتح */
  }

  .manager-main {
    margin-right: 0; /* لا مساحة للقائمة في الهاتف */
    padding: 1rem;
  }

  .manager-sidebar.open ~ .manager-main {
    margin-right: 0; /* المحتوى لا يتأثر في الهاتف */
  }

  .header-content {
    padding: 0 1rem;
  }

  .stats-grid {
    grid-template-columns: 1fr;
    gap: 1rem;
  }

  .orders-stats-grid {
    grid-template-columns: repeat(2, 1fr);
    gap: 1rem;
  }

  .orders-header {
    flex-direction: column;
    align-items: stretch;
  }

  .orders-filters {
    justify-content: center;
  }

  .filter-select {
    min-width: auto;
    flex: 1;
  }

  .waiter-stats-grid, .discount-requests-grid, .orders-grid {
    grid-template-columns: 1fr;
  }

  .shifts-grid, .employees-grid, .performance-grid {
    grid-template-columns: 1fr;
  }

  .performance-stats-grid {
    grid-template-columns: repeat(2, 1fr);
  }

  /* Manager Header Control Buttons */
  .manager-header {
    flex-direction: column;
  }

  .manager-header .welcome-section {
    text-align: center;
  }

  .control-buttons {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 1rem;
    flex-wrap: wrap;
  }

  .btn-refresh {
    width: 100%;
    justify-content: center;
  }

  .connection-status,
  .last-update {
    width: 100%;
    justify-content: center;
  }
}

/* تحسينات للتابلت */
@media (min-width: 769px) and (max-width: 1024px) {
  .manager-sidebar {
    width: 260px; /* عرض أصغر للتابلت */
  }
  
  .manager-main {
    padding: 1.5rem;
  }
  
  .stats-grid {
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1rem;
  }
}

/* تحسينات للشاشات الكبيرة جداً */
@media (min-width: 1400px) {
  .dashboard-content {
    max-width: 1600px;
  }
  
  .manager-sidebar {
    width: 320px; /* عرض أكبر للشاشات الكبيرة */
  }
  
  .manager-main {
    padding: 3rem;
  }
}

/* Loading */
.loading {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 60vh;
  gap: 1rem;
  color: #2c3e50;
}

.loading i {
  font-size: 2rem;
  color: #3498db;
}

/* Manager Home */
.manager-home {
  max-width: 1200px;
  margin: 0 auto;
}

.manager-header .welcome-section {
  text-align: center;
  margin-bottom: 2rem;
}

.manager-header .welcome-section h1 {
  margin: 0 0 0.5rem 0;
  color: #2c3e50;
  font-size: 2.5rem;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 1rem;
}

.role-badge {
  background: linear-gradient(135deg, #3498db, #2980b9);
  color: white;
  padding: 0.5rem 1rem;
  border-radius: 20px;
  font-size: 0.9rem;
  font-weight: 500;
}

/* Stats Grid */
.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 1.5rem;
  margin-bottom: 3rem;
}

.stat-card {
  background: white;
  padding: 2rem;
  border-radius: 16px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
  display: flex;
  align-items: center;
  gap: 1.5rem;
  transition: all 0.3s ease;
}

.stat-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 8px 30px rgba(0, 0, 0, 0.15);
}

.stat-icon {
  width: 60px;
  height: 60px;
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.5rem;
  color: white;
}

.stat-card.orders .stat-icon {
  background: linear-gradient(135deg, #e74c3c, #c0392b);
}

.stat-card.sales .stat-icon {
  background: linear-gradient(135deg, #27ae60, #229954);
}

.stat-card.employees .stat-icon {
  background: linear-gradient(135deg, #f39c12, #e67e22);
}

.stat-card.tables .stat-icon {
  background: linear-gradient(135deg, #9b59b6, #8e44ad);
}

.stat-content h3 {
  margin: 0 0 0.5rem 0;
  font-size: 2rem;
  font-weight: 700;
  color: #2c3e50;
}

.stat-content p {
  margin: 0;
  color: #7f8c8d;
  font-size: 0.9rem;
}

/* Orders Stats */
.orders-stats {
  background: white;
  padding: 2rem;
  border-radius: 16px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
}

.orders-stats h2 {
  margin: 0 0 2rem 0;
  color: #2c3e50;
  text-align: center;
  font-size: 1.5rem;
}

.orders-stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 1.5rem;
}

.order-stat {
  text-align: center;
  padding: 1.5rem;
  border-radius: 12px;
  transition: all 0.3s ease;
}

.order-stat:hover {
  transform: translateY(-3px);
}

.order-stat.pending {
  background: linear-gradient(135deg, #f39c12, #e67e22);
  color: white;
}

.order-stat.preparing {
  background: linear-gradient(135deg, #e74c3c, #c0392b);
  color: white;
}

.order-stat.ready {
  background: linear-gradient(135deg, #27ae60, #229954);
  color: white;
}

.order-stat.completed {
  background: linear-gradient(135deg, #3498db, #2980b9);
  color: white;
}

.order-stat i {
  font-size: 2rem;
  margin-bottom: 1rem;
  display: block;
}

.order-stat .count {
  display: block;
  font-size: 2rem;
  font-weight: 700;
  margin-bottom: 0.5rem;
}

.order-stat .label {
  font-size: 0.9rem;
  opacity: 0.9;
}

/* Coming Soon */
.coming-soon {
  text-align: center;
  padding: 4rem 2rem;
  color: #7f8c8d;
  font-size: 1.2rem;
}

/* Orders Screen */
.orders-screen {
  padding: 2rem;
}

.orders-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 2rem;
  flex-wrap: wrap;
  gap: 1rem;
}

.orders-header h1 {
  margin: 0;
  color: #2c3e50;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.orders-filters {
  display: flex;
  gap: 1rem;
  flex-wrap: wrap;
}

.filter-select {
  padding: 0.5rem 1rem;
  border: 1px solid #ddd;
  border-radius: 8px;
  background: white;
  font-size: 0.9rem;
  min-width: 150px;
}

.waiter-stats {
  margin-bottom: 2rem;
}

.waiter-stats h2 {
  margin-bottom: 1rem;
  color: #2c3e50;
}

.waiter-stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 1rem;
}

.waiter-stat-card {
  background: white;
  padding: 1.5rem;
  border-radius: 12px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.waiter-info {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.waiter-info i {
  font-size: 2rem;
  color: #3498db;
}

.waiter-info h3 {
  margin: 0;
  color: #2c3e50;
}

.waiter-numbers {
  display: flex;
  gap: 1rem;
}

.waiter-numbers .stat {
  text-align: center;
}

.waiter-numbers .number {
  display: block;
  font-size: 1.5rem;
  font-weight: bold;
  color: #2c3e50;
}

.waiter-numbers .label {
  font-size: 0.8rem;
  color: #7f8c8d;
}

.discount-requests {
  margin-bottom: 2rem;
}

.discount-requests h2 {
  margin-bottom: 1rem;
  color: #e74c3c;
}

.discount-requests-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 1rem;
}

.discount-request-card {
  background: white;
  padding: 1.5rem;
  border-radius: 12px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  border-left: 4px solid #e74c3c;
}

.request-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1rem;
}

.order-number {
  font-weight: bold;
  color: #2c3e50;
}

.waiter-name {
  color: #7f8c8d;
  font-size: 0.9rem;
}

.request-details {
  margin-bottom: 1rem;
}

.amount {
  font-size: 1.1rem;
  font-weight: bold;
  color: #e74c3c;
  margin-bottom: 0.5rem;
}

.reason {
  color: #7f8c8d;
  font-size: 0.9rem;
}

.request-actions {
  display: flex;
  gap: 0.5rem;
}

.approve-btn, .reject-btn {
  padding: 0.5rem 1rem;
  border: none;
  border-radius: 6px;
  cursor: pointer;
  font-size: 0.9rem;
  transition: all 0.3s ease;
}

.approve-btn {
  background: #27ae60;
  color: white;
}

.approve-btn:hover {
  background: #229954;
}

.reject-btn {
  background: #e74c3c;
  color: white;
}

.reject-btn:hover {
  background: #c0392b;
}

.orders-list h2 {
  margin-bottom: 1rem;
  color: #2c3e50;
}

.orders-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 1rem;
}

.order-card {
  background: white;
  padding: 1.5rem;
  border-radius: 12px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
}

.order-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
}

.order-card.pending {
  border-left: 4px solid #f39c12;
}

.order-card.preparing {
  border-left: 4px solid #e74c3c;
}

.order-card.ready {
  border-left: 4px solid #27ae60;
}

.order-card.completed {
  border-left: 4px solid #3498db;
}

.order-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1rem;
}

.order-number {
  font-weight: bold;
  color: #2c3e50;
}

.order-status {
  padding: 0.25rem 0.75rem;
  border-radius: 20px;
  font-size: 0.8rem;
  font-weight: 500;
}

.order-status.pending {
  background: #f39c12;
  color: white;
}

.order-status.preparing {
  background: #e74c3c;
  color: white;
}

.order-status.ready {
  background: #27ae60;
  color: white;
}

.order-status.completed {
  background: #3498db;
  color: white;
}

.order-info {
  margin-bottom: 1rem;
}

.order-detail {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  margin-bottom: 0.5rem;
  color: #7f8c8d;
  font-size: 0.9rem;
}

.order-detail i {
  width: 16px;
  text-align: center;
}

.order-summary {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1rem;
  padding: 0.75rem;
  background: #f8f9fa;
  border-radius: 8px;
}

.items-count {
  color: #7f8c8d;
  font-size: 0.9rem;
}

.total-amount {
  font-weight: bold;
  color: #2c3e50;
  font-size: 1.1rem;
}

.order-time {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  color: #7f8c8d;
  font-size: 0.8rem;
  margin-bottom: 1rem;
}

.details-btn {
  width: 100%;
  padding: 0.75rem;
  background: #3498db;
  color: white;
  border: none;
  border-radius: 8px;
  cursor: pointer;
  font-size: 0.9rem;
  transition: all 0.3s ease;
}

.details-btn:hover {
  background: #2980b9;
}

/* Employees Screen */
.employees-screen {
  padding: 2rem;
}

.employees-header h1 {
  margin: 0 0 2rem 0;
  color: #2c3e50;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.active-shifts-section, .inactive-employees-section, .performance-stats {
  margin-bottom: 3rem;
}

.active-shifts-section h2, .inactive-employees-section h2, .performance-stats h2 {
  margin-bottom: 1.5rem;
  color: #2c3e50;
}

.shifts-grid, .employees-grid, .performance-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
  gap: 1.5rem;
}

.shift-card, .employee-card, .performance-card {
  background: white;
  padding: 2rem;
  border-radius: 16px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
}

.shift-card:hover, .employee-card:hover, .performance-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 8px 30px rgba(0, 0, 0, 0.15);
}

.shift-card.active {
  border-left: 4px solid #27ae60;
}

.employee-info {
  display: flex;
  align-items: center;
  gap: 1rem;
  margin-bottom: 1.5rem;
}

.employee-avatar {
  width: 60px;
  height: 60px;
  border-radius: 50%;
  background: linear-gradient(135deg, #3498db, #2980b9);
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 1.5rem;
}

.employee-details h3 {
  margin: 0 0 0.25rem 0;
  color: #2c3e50;
}

.employee-details .role {
  color: #7f8c8d;
  font-size: 0.9rem;
  display: block;
  margin-bottom: 0.25rem;
}

.employee-details .status {
  padding: 0.25rem 0.5rem;
  border-radius: 12px;
  font-size: 0.8rem;
  font-weight: 500;
}

.status.active {
  background: #d5f4e6;
  color: #27ae60;
}

.status.inactive {
  background: #fadbd8;
  color: #e74c3c;
}

.shift-stats {
  margin-bottom: 1.5rem;
}

.shift-stats .stat {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 0.75rem;
  padding: 0.5rem;
  background: #f8f9fa;
  border-radius: 8px;
}

.shift-stats .label {
  color: #7f8c8d;
  font-size: 0.9rem;
}

.shift-stats .value {
  font-weight: bold;
  color: #2c3e50;
}

.shift-actions, .employee-actions {
  display: flex;
  gap: 0.5rem;
}

.start-shift-btn, .end-shift-btn {
  flex: 1;
  padding: 0.75rem;
  border: none;
  border-radius: 8px;
  cursor: pointer;
  font-size: 0.9rem;
  transition: all 0.3s ease;
}

.start-shift-btn {
  background: #27ae60;
  color: white;
}

.start-shift-btn:hover:not(:disabled) {
  background: #229954;
}

.start-shift-btn:disabled {
  background: #bdc3c7;
  cursor: not-allowed;
}

.end-shift-btn {
  background: #e74c3c;
  color: white;
}

.end-shift-btn:hover {
  background: #c0392b;
}

.performance-card .employee-header {
  display: flex;
  align-items: center;
  gap: 1rem;
  margin-bottom: 1.5rem;
}

.performance-card .employee-header i {
  font-size: 2rem;
  color: #3498db;
}

.performance-card .employee-header h3 {
  margin: 0;
  color: #2c3e50;
}

.performance-card .employee-header .role {
  color: #7f8c8d;
  font-size: 0.9rem;
}

.performance-stats-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 1rem;
}

.perf-stat {
  text-align: center;
  padding: 1rem;
  background: #f8f9fa;
  border-radius: 8px;
}

.perf-stat .number {
  display: block;
  font-size: 1.5rem;
  font-weight: bold;
  color: #2c3e50;
  margin-bottom: 0.25rem;
}

.perf-stat .label {
  color: #7f8c8d;
  font-size: 0.8rem;
}

/* Socket.IO Connection Status Indicator */
.connection-status {
  display: flex;
  align-items: center;
  margin-right: 1rem;
  transition: all 0.3s ease;
}

.socket-indicator {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.4rem 0.8rem;
  border-radius: 20px;
  background: rgba(255, 255, 255, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
  transition: all 0.3s ease;
}

.socket-indicator.connected {
  background: rgba(46, 204, 113, 0.2);
  border-color: rgba(46, 204, 113, 0.5);
}

.socket-indicator.disconnected {
  background: rgba(231, 76, 60, 0.2);
  border-color: rgba(231, 76, 60, 0.5);
}

.socket-dot {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background: #e74c3c;
  animation: pulse-red 2s infinite;
}

.socket-indicator.connected .socket-dot {
  background: #2ecc71;
  animation: pulse-green 2s infinite;
}

.socket-text {
  font-size: 0.8rem;
  font-weight: 500;
  color: white;
}

@keyframes pulse-green {
  0%, 100% {
    opacity: 1;
    transform: scale(1);
  }
  50% {
    opacity: 0.7;
    transform: scale(1.1);
  }
}

@keyframes pulse-red {
  0%, 100% {
    opacity: 1;
    transform: scale(1);
  }
  50% {
    opacity: 0.7;
    transform: scale(1.1);
  }
}

.manager-name {
  font-weight: 500;
  color: white;
}

/* Responsive Design */
@media (max-width: 768px) {
  .manager-sidebar {
    width: 100%;
    top: 70px;
    height: calc(100vh - 70px);
    transform: translateX(-100%); /* مخفية بشكل افتراضي في الهاتف */
    z-index: 999; /* أعلى من الهيدر */
  }

  .manager-sidebar.open {
    transform: translateX(0); /* تظهر عند الفتح */
  }

  .manager-main {
    margin-right: 0; /* لا مساحة للقائمة في الهاتف */
    padding: 1rem;
  }

  .manager-sidebar.open ~ .manager-main {
    margin-right: 0; /* المحتوى لا يتأثر في الهاتف */
  }

  .header-content {
    padding: 0 1rem;
  }

  .stats-grid {
    grid-template-columns: 1fr;
    gap: 1rem;
  }

  .orders-stats-grid {
    grid-template-columns: repeat(2, 1fr);
    gap: 1rem;
  }

  .orders-header {
    flex-direction: column;
    align-items: stretch;
  }

  .orders-filters {
    justify-content: center;
  }

  .filter-select {
    min-width: auto;
    flex: 1;
  }

  .waiter-stats-grid, .discount-requests-grid, .orders-grid {
    grid-template-columns: 1fr;
  }

  .shifts-grid, .employees-grid, .performance-grid {
    grid-template-columns: 1fr;
  }

  .performance-stats-grid {
    grid-template-columns: repeat(2, 1fr);
  }

  /* Manager Header Control Buttons */
  .manager-header {
    flex-direction: column;
  }

  .manager-header .welcome-section {
    text-align: center;
  }

  .control-buttons {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 1rem;
    flex-wrap: wrap;
  }

  .btn-refresh {
    width: 100%;
    justify-content: center;
  }

  .connection-status,
  .last-update {
    width: 100%;
    justify-content: center;
  }
}

/* تحسينات للتابلت */
@media (min-width: 769px) and (max-width: 1024px) {
  .manager-sidebar {
    width: 260px; /* عرض أصغر للتابلت */
  }
  
  .manager-main {
    padding: 1.5rem;
  }
  
  .stats-grid {
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1rem;
  }
}

/* تحسينات للشاشات الكبيرة جداً */
@media (min-width: 1400px) {
  .dashboard-content {
    max-width: 1600px;
  }
  
  .manager-sidebar {
    width: 320px; /* عرض أكبر للشاشات الكبيرة */
  }
  
  .manager-main {
    padding: 3rem;
  }
}

/* Loading */
.loading {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 60vh;
  gap: 1rem;
  color: #2c3e50;
}

.loading i {
  font-size: 2rem;
  color: #3498db;
}

/* Manager Home */
.manager-home {
  max-width: 1200px;
  margin: 0 auto;
}

.manager-header .welcome-section {
  text-align: center;
  margin-bottom: 2rem;
}

.manager-header .welcome-section h1 {
  margin: 0 0 0.5rem 0;
  color: #2c3e50;
  font-size: 2.5rem;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 1rem;
}

.role-badge {
  background: linear-gradient(135deg, #3498db, #2980b9);
  color: white;
  padding: 0.5rem 1rem;
  border-radius: 20px;
  font-size: 0.9rem;
  font-weight: 500;
}

/* Stats Grid */
.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 1.5rem;
  margin-bottom: 3rem;
}

.stat-card {
  background: white;
  padding: 2rem;
  border-radius: 16px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
  display: flex;
  align-items: center;
  gap: 1.5rem;
  transition: all 0.3s ease;
}

.stat-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 8px 30px rgba(0, 0, 0, 0.15);
}

.stat-icon {
  width: 60px;
  height: 60px;
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.5rem;
  color: white;
}

.stat-card.orders .stat-icon {
  background: linear-gradient(135deg, #e74c3c, #c0392b);
}

.stat-card.sales .stat-icon {
  background: linear-gradient(135deg, #27ae60, #229954);
}

.stat-card.employees .stat-icon {
  background: linear-gradient(135deg, #f39c12, #e67e22);
}

.stat-card.tables .stat-icon {
  background: linear-gradient(135deg, #9b59b6, #8e44ad);
}

.stat-content h3 {
  margin: 0 0 0.5rem 0;
  font-size: 2rem;
  font-weight: 700;
  color: #2c3e50;
}

.stat-content p {
  margin: 0;
  color: #7f8c8d;
  font-size: 0.9rem;
}

/* Orders Stats */
.orders-stats {
  background: white;
  padding: 2rem;
  border-radius: 16px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
}

.orders-stats h2 {
  margin: 0 0 2rem 0;
  color: #2c3e50;
  text-align: center;
  font-size: 1.5rem;
}

.orders-stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 1.5rem;
}

.order-stat {
  text-align: center;
  padding: 1.5rem;
  border-radius: 12px;
  transition: all 0.3s ease;
}

.order-stat:hover {
  transform: translateY(-3px);
}

.order-stat.pending {
  background: linear-gradient(135deg, #f39c12, #e67e22);
  color: white;
}

.order-stat.preparing {
  background: linear-gradient(135deg, #e74c3c, #c0392b);
  color: white;
}

.order-stat.ready {
  background: linear-gradient(135deg, #27ae60, #229954);
  color: white;
}

.order-stat.completed {
  background: linear-gradient(135deg, #3498db, #2980b9);
  color: white;
}

.order-stat i {
  font-size: 2rem;
  margin-bottom: 1rem;
  display: block;
}

.order-stat .count {
  display: block;
  font-size: 2rem;
  font-weight: 700;
  margin-bottom: 0.5rem;
}

.order-stat .label {
  font-size: 0.9rem;
  opacity: 0.9;
}

/* Coming Soon */
.coming-soon {
  text-align: center;
  padding: 4rem 2rem;
  color: #7f8c8d;
  font-size: 1.2rem;
}

/* Orders Screen */
.orders-screen {
  padding: 2rem;
}

.orders-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 2rem;
  flex-wrap: wrap;
  gap: 1rem;
}

.orders-header h1 {
  margin: 0;
  color: #2c3e50;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.orders-filters {
  display: flex;
  gap: 1rem;
  flex-wrap: wrap;
}

.filter-select {
  padding: 0.5rem 1rem;
  border: 1px solid #ddd;
  border-radius: 8px;
  background: white;
  font-size: 0.9rem;
  min-width: 150px;
}

.waiter-stats {
  margin-bottom: 2rem;
}

.waiter-stats h2 {
  margin-bottom: 1rem;
  color: #2c3e50;
}

.waiter-stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 1rem;
}

.waiter-stat-card {
  background: white;
  padding: 1.5rem;
  border-radius: 12px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.waiter-info {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.waiter-info i {
  font-size: 2rem;
  color: #3498db;
}

.waiter-info h3 {
  margin: 0;
  color: #2c3e50;
}

.waiter-numbers {
  display: flex;
  gap: 1rem;
}

.waiter-numbers .stat {
  text-align: center;
}

.waiter-numbers .number {
  display: block;
  font-size: 1.5rem;
  font-weight: bold;
  color: #2c3e50;
}

.waiter-numbers .label {
  font-size: 0.8rem;
  color: #7f8c8d;
}

.discount-requests {
  margin-bottom: 2rem;
}

.discount-requests h2 {
  margin-bottom: 1rem;
  color: #e74c3c;
}

.discount-requests-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 1rem;
}

.discount-request-card {
  background: white;
  padding: 1.5rem;
  border-radius: 12px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  border-left: 4px solid #e74c3c;
}

.request-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1rem;
}

.order-number {
  font-weight: bold;
  color: #2c3e50;
}

.waiter-name {
  color: #7f8c8d;
  font-size: 0.9rem;
}

.request-details {
  margin-bottom: 1rem;
}

.amount {
  font-size: 1.1rem;
  font-weight: bold;
  color: #e74c3c;
  margin-bottom: 0.5rem;
}

.reason {
  color: #7f8c8d;
  font-size: 0.9rem;
}

.request-actions {
  display: flex;
  gap: 0.5rem;
}

.approve-btn, .reject-btn {
  padding: 0.5rem 1rem;
  border: none;
  border-radius: 6px;
  cursor: pointer;
  font-size: 0.9rem;
  transition: all 0.3s ease;
}

.approve-btn {
  background: #27ae60;
  color: white;
}

.approve-btn:hover {
  background: #229954;
}

.reject-btn {
  background: #e74c3c;
  color: white;
}

.reject-btn:hover {
  background: #c0392b;
}

.orders-list h2 {
  margin-bottom: 1rem;
  color: #2c3e50;
}

.orders-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 1rem;
}

.order-card {
  background: white;
  padding: 1.5rem;
  border-radius: 12px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
}

.order-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
}

.order-card.pending {
  border-left: 4px solid #f39c12;
}

.order-card.preparing {
  border-left: 4px solid #e74c3c;
}

.order-card.ready {
  border-left: 4px solid #27ae60;
}

.order-card.completed {
  border-left: 4px solid #3498db;
}

.order-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1rem;
}

.order-number {
  font-weight: bold;
  color: #2c3e50;
}

.order-status {
  padding: 0.25rem 0.75rem;
  border-radius: 20px;
  font-size: 0.8rem;
  font-weight: 500;
}

.order-status.pending {
  background: #f39c12;
  color: white;
}

.order-status.preparing {
  background: #e74c3c;
  color: white;
}

.order-status.ready {
  background: #27ae60;
  color: white;
}

.order-status.completed {
  background: #3498db;
  color: white;
}

.order-info {
  margin-bottom: 1rem;
}

.order-detail {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  margin-bottom: 0.5rem;
  color: #7f8c8d;
  font-size: 0.9rem;
}

.order-detail i {
  width: 16px;
  text-align: center;
}

.order-summary {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1rem;
  padding: 0.75rem;
  background: #f8f9fa;
  border-radius: 8px;
}

.items-count {
  color: #7f8c8d;
  font-size: 0.9rem;
}

.total-amount {
  font-weight: bold;
  color: #2c3e50;
  font-size: 1.1rem;
}

.order-time {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  color: #7f8c8d;
  font-size: 0.8rem;
  margin-bottom: 1rem;
}

.details-btn {
  width: 100%;
  padding: 0.75rem;
  background: #3498db;
  color: white;
  border: none;
  border-radius: 8px;
  cursor: pointer;
  font-size: 0.9rem;
  transition: all 0.3s ease;
}

.details-btn:hover {
  background: #2980b9;
}

/* Employees Screen */
.employees-screen {
  padding: 2rem;
}

.employees-header h1 {
  margin: 0 0 2rem 0;
  color: #2c3e50;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.active-shifts-section, .inactive-employees-section, .performance-stats {
  margin-bottom: 3rem;
}

.active-shifts-section h2, .inactive-employees-section h2, .performance-stats h2 {
  margin-bottom: 1.5rem;
  color: #2c3e50;
}

.shifts-grid, .employees-grid, .performance-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
  gap: 1.5rem;
}

.shift-card, .employee-card, .performance-card {
  background: white;
  padding: 2rem;
  border-radius: 16px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
}

.shift-card:hover, .employee-card:hover, .performance-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 8px 30px rgba(0, 0, 0, 0.15);
}

.shift-card.active {
  border-left: 4px solid #27ae60;
}

.employee-info {
  display: flex;
  align-items: center;
  gap: 1rem;
  margin-bottom: 1.5rem;
}

.employee-avatar {
  width: 60px;
  height: 60px;
  border-radius: 50%;
  background: linear-gradient(135deg, #3498db, #2980b9);
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 1.5rem;
}

.employee-details h3 {
  margin: 0 0 0.25rem 0;
  color: #2c3e50;
}

.employee-details .role {
  color: #7f8c8d;
  font-size: 0.9rem;
  display: block;
  margin-bottom: 0.25rem;
}

.employee-details .status {
  padding: 0.25rem 0.5rem;
  border-radius: 12px;
  font-size: 0.8rem;
  font-weight: 500;
}

.status.active {
  background: #d5f4e6;
  color: #27ae60;
}

.status.inactive {
  background: #fadbd8;
  color: #e74c3c;
}

.shift-stats {
  margin-bottom: 1.5rem;
}

.shift-stats .stat {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 0.75rem;
  padding: 0.5rem;
  background: #f8f9fa;
  border-radius: 8px;
}

.shift-stats .label {
  color: #7f8c8d;
  font-size: 0.9rem;
}

.shift-stats .value {
  font-weight: bold;
  color: #2c3e50;
}

.shift-actions, .employee-actions {
  display: flex;
  gap: 0.5rem;
}

.start-shift-btn, .end-shift-btn {
  flex: 1;
  padding: 0.75rem;
  border: none;
  border-radius: 8px;
  cursor: pointer;
  font-size: 0.9rem;
  transition: all 0.3s ease;
}

.start-shift-btn {
  background: #27ae60;
  color: white;
}

.start-shift-btn:hover:not(:disabled) {
  background: #229954;
}

.start-shift-btn:disabled {
  background: #bdc3c7;
  cursor: not-allowed;
}

.end-shift-btn {
  background: #e74c3c;
  color: white;
}

.end-shift-btn:hover {
  background: #c0392b;
}

.performance-card .employee-header {
  display: flex;
  align-items: center;
  gap: 1rem;
  margin-bottom: 1.5rem;
}

.performance-card .employee-header i {
  font-size: 2rem;
  color: #3498db;
}

.performance-card .employee-header h3 {
  margin: 0;
  color: #2c3e50;
}

.performance-card .employee-header .role {
  color: #7f8c8d;
  font-size: 0.9rem;
}

.performance-stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
  gap: 1rem;
}

.start-shift-btn {
  flex: 1;
  padding: 0.75rem;
  border: none;
  border-radius: 8px;
  cursor: pointer;
  font-size: 0.9rem;
  transition: all 0.3s ease;
  background: #27ae60;
  color: white;
}

.start-shift-btn:hover:not(:disabled) {
  background: #229954;
}

.start-shift-btn:disabled {
  background: #bdc3c7;
  cursor: not-allowed;
}

.end-shift-btn {
  background: #e74c3c;
  color: white;
}

.end-shift-btn:hover {
  background: #c0392b;
}

.performance-card .employee-header {
  display: flex;
  align-items: center;
  gap: 1rem;
  margin-bottom: 1.5rem;
}

.performance-card .employee-header i {
  font-size: 2rem;
  color: #3498db;
}

.performance-card .employee-header h3 {
  margin: 0;
  color: #2c3e50;
}

.performance-card .employee-header .role {
  color: #7f8c8d;

.performance-stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
  gap: 1rem;
}

.start-shift-btn {
  flex: 1;
  padding: 0.75rem;
  border: none;
  border-radius: 8px;
  cursor: pointer;
  font-size: 0.9rem;
  transition: all 0.3s ease;
  background: #27ae60;
  color: white;
}

.start-shift-btn:hover:not(:disabled) {
  background: #229954;
  transform: translateY(-1px);
}

.end-shift-btn {
  flex: 1;
  padding: 0.75rem;
  border: none;
  border-radius: 8px;
  cursor: pointer;
  font-size: 0.9rem;
  transition: all 0.3s ease;
  background: #e74c3c;
  color: white;
}

.end-shift-btn:hover:not(:disabled) {
  background: #c0392b;
  transform: translateY(-1px);
}

.shift-btn:disabled {
  background: #95a5a6;
  cursor: not-allowed;
  transform: none;
}

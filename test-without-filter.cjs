const mongoose = require('mongoose');

async function testWithoutFilter() {
  try {
    console.log('🔗 الاتصال بقاعدة البيانات...');
    
    const mongoUri = 'mongodb+srv://besomustafa:<EMAIL>/deshacoffee?retryWrites=true&w=majority&appName=MyCoffeChop';
    
    await mongoose.connect(mongoUri, {
      serverSelectionTimeoutMS: 5000,
      connectTimeoutMS: 10000,
    });
    
    console.log('✅ تم الاتصال بنجاح');
    
    console.log('\n' + '='.repeat(80));
    console.log('📊 النتائج بعد إزالة الفلتر - حساب جميع الطلبات');
    console.log('='.repeat(80));
    
    // جلب جميع الطلبات
    const orders = await mongoose.connection.db.collection('orders').find({}).toArray();
    console.log(`\n📋 إجمالي الطلبات: ${orders.length}`);
    
    // جلب جميع النُدل
    const waiters = await mongoose.connection.db.collection('users').find({ role: 'waiter' }).toArray();
    console.log(`👥 إجمالي النُدل: ${waiters.length}`);
    
    // حساب المبيعات بدون فلتر
    let totalSalesAllOrders = 0;
    const statusBreakdown = {};
    
    orders.forEach(order => {
      const status = order.status || 'غير محدد';
      const amount = order.totals?.total || order.totalPrice || order.totalAmount || order.pricing?.total || 0;
      
      totalSalesAllOrders += amount;
      statusBreakdown[status] = (statusBreakdown[status] || 0) + 1;
    });
    
    console.log(`\n💰 إجمالي المبيعات (جميع الطلبات): ${totalSalesAllOrders.toFixed(2)} جنيه`);
    
    console.log('\n📊 توزيع الطلبات حسب الحالة:');
    Object.entries(statusBreakdown)
      .sort((a, b) => b[1] - a[1])
      .forEach(([status, count]) => {
        const percentage = ((count / orders.length) * 100).toFixed(1);
        console.log(`   ${status}: ${count} طلب (${percentage}%)`);
      });
    
    // حساب مبيعات كل نادل
    console.log('\n👥 مبيعات النُدل (جميع الطلبات):');
    for (const waiter of waiters) {
      const waiterOrderIds = new Set();
      
      // البحث بجميع الطرق
      orders.forEach(order => {
        if (order.staff?.waiter && order.staff.waiter.toString() === waiter._id.toString()) {
          waiterOrderIds.add(order._id.toString());
        }
        if (order.waiterName === waiter.username || order.waiterName === waiter.name) {
          waiterOrderIds.add(order._id.toString());
        }
        if (order.waiterId && order.waiterId.toString() === waiter._id.toString()) {
          waiterOrderIds.add(order._id.toString());
        }
      });
      
      // حساب المبيعات
      let waiterSales = 0;
      waiterOrderIds.forEach(orderId => {
        const order = orders.find(o => o._id.toString() === orderId);
        if (order) {
          const amount = order.totals?.total || order.totalPrice || order.totalAmount || order.pricing?.total || 0;
          waiterSales += amount;
        }
      });
      
      console.log(`   ${waiter.username}: ${waiterOrderIds.size} طلب، ${waiterSales.toFixed(2)} جنيه`);
    }
    
    console.log('\n' + '='.repeat(80));
    console.log('✅ النتائج بعد إزالة الفلتر');
    console.log('='.repeat(80));
    console.log(`🔸 لا يوجد فقدان في الطلبات - جميع الطلبات محسوبة`);
    console.log(`🔸 إجمالي المبيعات: ${totalSalesAllOrders.toFixed(2)} جنيه`);
    console.log(`🔸 يتضمن طلبات من جميع الحالات: ${Object.keys(statusBreakdown).join(', ')}`);
    console.log(`🔸 تم تطبيق التغيير على:`);
    console.log(`   - src/ManagerDashboard.tsx`);
    console.log(`   - backend/routes/waiter-stats.js`);
    console.log(`   - backend/routes/reports.js`);
    
  } catch (error) {
    console.error('❌ خطأ في التحليل:', error.message);
  } finally {
    await mongoose.disconnect();
    console.log('\n🔌 تم قطع الاتصال بقاعدة البيانات');
  }
}

testWithoutFilter();

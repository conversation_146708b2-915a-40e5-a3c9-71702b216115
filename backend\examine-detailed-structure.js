const mongoose = require('mongoose');
require('dotenv').config();

async function examineDetailedStructure() {
  try {
    console.log('🔌 Connecting to MongoDB Atlas...');
    await mongoose.connect(process.env.MONGODB_URI);
    console.log('✅ Connected successfully!');

    const ordersCollection = mongoose.connection.db.collection('orders');
    const firstOrder = await ordersCollection.findOne();
    
    if (firstOrder) {
      console.log('\n📋 Detailed Structure Analysis:');
      
      console.log('\n🏪 Customer object:');
      console.log(JSON.stringify(firstOrder.customer, null, 2));
      
      console.log('\n💰 Totals object:');
      console.log(JSON.stringify(firstOrder.totals, null, 2));
      
      console.log('\n🏓 Table object:');
      console.log(JSON.stringify(firstOrder.table, null, 2));
      
      console.log('\n👥 Staff object:');
      console.log(JSON.stringify(firstOrder.staff, null, 2));
      
      console.log('\n🍽️ Items structure:');
      if (firstOrder.items && firstOrder.items.length > 0) {
        console.log(JSON.stringify(firstOrder.items[0], null, 2));
      }

      // Check users collection to see the structure of waiters
      console.log('\n👤 Checking Users Collection:');
      const usersCollection = mongoose.connection.db.collection('users');
      const waiters = await usersCollection.find({ role: 'waiter' }).toArray();
      
      if (waiters.length > 0) {
        console.log('\n🧑‍💼 Waiter structure:');
        console.log(JSON.stringify(waiters[0], null, 2));
      }

      // Check tables collection
      console.log('\n🏓 Checking Tables Collection:');
      const tablesCollection = mongoose.connection.db.collection('tables');
      const tables = await tablesCollection.find().limit(3).toArray();
      
      if (tables.length > 0) {
        console.log('\n🏓 Table structure:');
        console.log(JSON.stringify(tables[0], null, 2));
      }
    }

  } catch (error) {
    console.error('❌ Error:', error.message);
  } finally {
    await mongoose.connection.close();
    console.log('\n🔐 Database connection closed.');
  }
}

examineDetailedStructure();

import type { FC } from 'react';
import './Loading.css';

interface LoadingProps {
  size?: 'sm' | 'md' | 'lg';
  text?: string;
  overlay?: boolean;
  color?: 'primary' | 'secondary' | 'white';
}

const Loading: FC<LoadingProps> = ({
  size = 'md',
  text = 'جاري التحميل...',
  overlay = false,
  color = 'primary'
}) => {
  const loadingContent = (
    <div className={`loading-container ${overlay ? 'loading-overlay' : ''}`}>
      <div className={`loading-spinner loading-${size} loading-${color}`}>
        <div className="spinner"></div>
      </div>
      {text && <p className="loading-text">{text}</p>}
    </div>
  );

  return loadingContent;
};

export default Loading;

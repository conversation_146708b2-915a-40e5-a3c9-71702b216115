/* ====================================
   REPORTS MANAGER SCREEN - ENHANCED DESIGN
   شاشة تقارير المدير - تصميم محسن
   ==================================== */

/* Container */
.reports-manager-screen {
  padding: 2.5rem;
  background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
  min-height: 100vh;
  direction: rtl;
  font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
}

/* Header */
.reports-manager-screen-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 3rem;
  padding: 2rem;
  background: linear-gradient(135deg, #1e40af 0%, #3b82f6 100%);
  border-radius: 20px;
  color: white;
  box-shadow: 0 8px 32px rgba(30, 64, 175, 0.3);
}

.reports-manager-screen-title h2 {
  font-size: 2.2rem;
  font-weight: 800;
  margin: 0 0 0.5rem 0;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

.reports-manager-screen-title p {
  font-size: 1.1rem;
  opacity: 0.9;
  margin: 0;
}

.reports-manager-screen-filters {
  display: flex;
  gap: 1rem;
}

.reports-manager-screen-period-filter {
  padding: 1rem 1.5rem;
  border: 2px solid rgba(255, 255, 255, 0.3);
  border-radius: 12px;
  background: rgba(255, 255, 255, 0.1);
  color: white;
  font-size: 1rem;
  font-weight: 600;
  -webkit-backdrop-filter: blur(10px);
  backdrop-filter: blur(10px);
  cursor: pointer;
  transition: all 0.3s ease;
}

.reports-manager-screen-period-filter:focus {
  outline: none;
  border-color: rgba(255, 255, 255, 0.6);
  background: rgba(255, 255, 255, 0.2);
}

.reports-manager-screen-period-filter option {
  background: #1e40af;
  color: white;
  padding: 0.5rem;
}

/* Overview Section */
.reports-manager-screen-overview {
  margin-bottom: 3rem;
}

.reports-manager-screen-overview h3 {
  font-size: 1.8rem;
  color: #1e293b;
  margin-bottom: 2rem;
  font-weight: 700;
  display: flex;
  align-items: center;
  gap: 0.75rem;
}

.reports-manager-screen-overview h3::before {
  content: '📊';
  font-size: 1.5rem;
}

/* Enhanced Overview Grid */
.reports-manager-screen-overview-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(320px, 1fr));
  gap: 2rem;
  margin-bottom: 2rem;
}

/* Enhanced Overview Cards */
.reports-manager-screen-overview-card {
  background: linear-gradient(145deg, #ffffff 0%, #f8fafc 100%);
  border-radius: 24px;
  padding: 2.5rem;
  box-shadow: 
    0 12px 40px rgba(0, 0, 0, 0.08),
    0 4px 12px rgba(0, 0, 0, 0.04),
    inset 0 1px 0 rgba(255, 255, 255, 0.8);
  border: 1px solid rgba(226, 232, 240, 0.8);
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  overflow: hidden;
  min-height: 180px;
  display: flex;
  align-items: center;
  gap: 2rem;
}

.reports-manager-screen-overview-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 6px;
  background: linear-gradient(90deg, #3b82f6, #10b981, #f59e0b);
  transition: all 0.3s ease;
  border-radius: 24px 24px 0 0;
}

.reports-manager-screen-overview-card::after {
  content: '';
  position: absolute;
  top: -50%;
  right: -50%;
  width: 100%;
  height: 100%;
  background: radial-gradient(circle, rgba(59, 130, 246, 0.05) 0%, transparent 70%);
  opacity: 0;
  transition: opacity 0.3s ease;
}

.reports-manager-screen-overview-card:hover {
  transform: translateY(-8px) scale(1.02);
  box-shadow: 
    0 20px 60px rgba(0, 0, 0, 0.12),
    0 8px 20px rgba(0, 0, 0, 0.08),
    inset 0 1px 0 rgba(255, 255, 255, 0.9);
}

.reports-manager-screen-overview-card:hover::before {
  height: 8px;
  box-shadow: 0 0 20px rgba(59, 130, 246, 0.4);
}

.reports-manager-screen-overview-card:hover::after {
  opacity: 1;
}

/* Enhanced Card Icons */
.reports-manager-screen-overview-icon {
  width: 100px;
  height: 100px;
  border-radius: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 2.5rem;
  position: relative;
  overflow: hidden;
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
  flex-shrink: 0;
}

.reports-manager-screen-overview-icon::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(45deg, rgba(255, 255, 255, 0.3) 0%, transparent 50%);
  animation: shimmer 3s infinite;
}

/* Revenue Card */
.reports-manager-screen-overview-card.revenue .reports-manager-screen-overview-icon {
  background: linear-gradient(135deg, #10b981 0%, #059669 50%, #047857 100%);
  color: white;
  box-shadow: 0 8px 25px rgba(16, 185, 129, 0.4);
}

/* Orders Card */
.reports-manager-screen-overview-card.orders .reports-manager-screen-overview-icon {
  background: linear-gradient(135deg, #3b82f6 0%, #2563eb 50%, #1d4ed8 100%);
  color: white;
  box-shadow: 0 8px 25px rgba(59, 130, 246, 0.4);
}

/* Average Card */
.reports-manager-screen-overview-card.average .reports-manager-screen-overview-icon {
  background: linear-gradient(135deg, #f59e0b 0%, #d97706 50%, #92400e 100%);
  color: white;
  box-shadow: 0 8px 25px rgba(245, 158, 11, 0.4);
}

/* Completed Card */
.reports-manager-screen-overview-card.completed .reports-manager-screen-overview-icon {
  background: linear-gradient(135deg, #8b5cf6 0%, #7c3aed 50%, #6d28d9 100%);
  color: white;
  box-shadow: 0 8px 25px rgba(139, 92, 246, 0.4);
}

/* Enhanced Card Content */
.reports-manager-screen-overview-content {
  flex: 1;
}

.reports-manager-screen-overview-content h4 {
  font-size: 2.8rem;
  font-weight: 900;
  color: #1e293b;
  margin: 0 0 0.5rem 0;
  line-height: 1;
  letter-spacing: -1px;
  background: linear-gradient(135deg, #1e293b 0%, #475569 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.reports-manager-screen-overview-content p {
  color: #64748b;
  font-size: 1.2rem;
  font-weight: 600;
  margin: 0;
  letter-spacing: 0.25px;
}

/* Order Status Section */
.reports-manager-screen-order-status {
  margin-bottom: 3rem;
}

.reports-manager-screen-order-status h3 {
  font-size: 1.8rem;
  color: #1e293b;
  margin-bottom: 2rem;
  font-weight: 700;
  display: flex;
  align-items: center;
  gap: 0.75rem;
}

.reports-manager-screen-order-status h3::before {
  content: '📋';
  font-size: 1.5rem;
}

.reports-manager-screen-status-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 1.5rem;
}

.reports-manager-screen-status-card {
  background: white;
  border-radius: 16px;
  padding: 2rem;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.06);
  border: 1px solid #f1f3f4;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  gap: 1.5rem;
}

.reports-manager-screen-status-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 8px 30px rgba(0, 0, 0, 0.1);
}

.reports-manager-screen-status-icon {
  width: 60px;
  height: 60px;
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.5rem;
  color: white;
}

.reports-manager-screen-status-card.pending .reports-manager-screen-status-icon {
  background: linear-gradient(135deg, #f59e0b, #d97706);
}

.reports-manager-screen-status-card.preparing .reports-manager-screen-status-icon {
  background: linear-gradient(135deg, #ef4444, #dc2626);
}

.reports-manager-screen-status-card.ready .reports-manager-screen-status-icon {
  background: linear-gradient(135deg, #10b981, #059669);
}

.reports-manager-screen-status-card.completed .reports-manager-screen-status-icon {
  background: linear-gradient(135deg, #8b5cf6, #7c3aed);
}

.reports-manager-screen-status-content h4 {
  font-size: 2rem;
  font-weight: 700;
  color: #1e293b;
  margin: 0 0 0.25rem 0;
}

.reports-manager-screen-status-content p {
  color: #64748b;
  font-size: 1rem;
  margin: 0;
  font-weight: 500;
}

/* Additional Sections Styling */
.reports-manager-screen-charts,
.reports-manager-screen-details {
  background: white;
  border-radius: 20px;
  padding: 2.5rem;
  margin-bottom: 2rem;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.06);
  border: 1px solid #f1f3f4;
}

.reports-manager-screen-charts h3,
.reports-manager-screen-details h3 {
  color: #1e293b;
  margin-bottom: 2rem;
  font-weight: 700;
  font-size: 1.6rem;
}

/* Loading States */
.reports-manager-screen-loading {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 4rem;
  color: #64748b;
  font-size: 1.2rem;
}

.reports-manager-screen-loading i {
  margin-left: 1rem;
  font-size: 1.5rem;
  animation: spin 1s linear infinite;
}

/* Animations */
@keyframes shimmer {
  0% {
    transform: translateX(-100%) rotate(45deg);
  }
  100% {
    transform: translateX(200%) rotate(45deg);
  }
}

@keyframes spin {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

/* Responsive Design */
@media (max-width: 768px) {
  .reports-manager-screen {
    padding: 1.5rem;
  }

  .reports-manager-screen-header {
    flex-direction: column;
    gap: 1.5rem;
    text-align: center;
  }

  .reports-manager-screen-overview-grid {
    grid-template-columns: 1fr;
  }

  .reports-manager-screen-overview-card {
    flex-direction: column;
    text-align: center;
    min-height: 160px;
    padding: 2rem;
  }

  .reports-manager-screen-overview-icon {
    width: 80px;
    height: 80px;
  }

  .reports-manager-screen-overview-content h4 {
    font-size: 2.2rem;
  }

  .reports-manager-screen-status-grid {
    grid-template-columns: 1fr;
  }
}

@media (max-width: 480px) {
  .reports-manager-screen-overview-card {
    padding: 1.5rem;
  }

  .reports-manager-screen-overview-content h4 {
    font-size: 2rem;
  }

  .reports-manager-screen-title h2 {
    font-size: 1.8rem;
  }
}

/* ====================================
   BOOTSTRAP COMPATIBILITY - ENHANCED DESIGN
   توافق Bootstrap - تصميم محسن
   ==================================== */

/* Reports Bootstrap Container */
.reports-bootstrap-container {
  background: linear-gradient(135deg, #1e40af 0%, #3b82f6 100%) !important;
  min-height: 100vh;
  direction: rtl;
  font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif !important;
}

/* Enhanced Bootstrap Cards */
.reports-overview-card {
  background: linear-gradient(145deg, #ffffff 0%, #f8fafc 100%) !important;
  border: none !important;
  border-radius: 24px !important;
  box-shadow: 
    0 12px 40px rgba(0, 0, 0, 0.08),
    0 4px 12px rgba(0, 0, 0, 0.04),
    inset 0 1px 0 rgba(255, 255, 255, 0.8) !important;
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1) !important;
  position: relative;
  overflow: hidden;
  min-height: 200px !important;
}

.reports-overview-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 6px;
  background: linear-gradient(90deg, #3b82f6, #10b981, #f59e0b);
  transition: all 0.3s ease;
}

.reports-overview-card:hover {
  transform: translateY(-8px) scale(1.02) !important;
  box-shadow: 
    0 20px 60px rgba(0, 0, 0, 0.12),
    0 8px 20px rgba(0, 0, 0, 0.08) !important;
}

.reports-overview-card:hover::before {
  height: 8px;
  box-shadow: 0 0 20px rgba(59, 130, 246, 0.4);
}

/* Enhanced Filter Select */
.reports-filter-select {
  background: rgba(255, 255, 255, 0.1) !important;
  border: 2px solid rgba(255, 255, 255, 0.3) !important;
  color: white !important;
  border-radius: 12px !important;
  padding: 1rem 1.5rem !important;
  font-weight: 600 !important;
  -webkit-backdrop-filter: blur(10px);
  backdrop-filter: blur(10px);
}

.reports-filter-select:focus {
  background: rgba(255, 255, 255, 0.2) !important;
  border-color: rgba(255, 255, 255, 0.6) !important;
  box-shadow: none !important;
  color: white !important;
}

.reports-filter-select option {
  background: #1e40af !important;
  color: white !important;
}

/* Inventory Bootstrap Styles */
.inventory-bootstrap-container {
  background: linear-gradient(135deg, #f0f4f8 0%, #e2e8f0 100%) !important;
  min-height: 100vh;
  direction: rtl;
  font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif !important;
}

.inventory-item-card {
  background: linear-gradient(145deg, #ffffff 0%, #f8fafc 100%) !important;
  border: none !important;
  border-radius: 24px !important;
  box-shadow: 
    0 12px 40px rgba(0, 0, 0, 0.08),
    0 4px 12px rgba(0, 0, 0, 0.04) !important;
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1) !important;
  position: relative;
  overflow: hidden;
  min-height: 300px !important;
}

.inventory-item-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 6px;
  background: linear-gradient(90deg, #3b82f6, #10b981, #f59e0b);
}

.inventory-item-card:hover {
  transform: translateY(-8px) scale(1.02) !important;
  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.12) !important;
}

.inventory-item-card:hover::before {
  height: 8px;
  box-shadow: 0 0 20px rgba(59, 130, 246, 0.4);
}

/* Menu Bootstrap Styles */
.menu-bootstrap-container {
  background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%) !important;
  min-height: 100vh;
  direction: rtl;
  font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif !important;
}

.menu-item-card {
  background: linear-gradient(145deg, #ffffff 0%, #f8fafc 100%) !important;
  border: none !important;
  border-radius: 24px !important;
  box-shadow: 
    0 12px 40px rgba(0, 0, 0, 0.08),
    0 4px 12px rgba(0, 0, 0, 0.04) !important;
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1) !important;
  position: relative;
  overflow: hidden;
  min-height: 350px !important;
}

.menu-item-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 6px;
  background: linear-gradient(90deg, #7c3aed, #8b5cf6, #a855f7);
}

.menu-item-card:hover {
  transform: translateY(-8px) scale(1.02) !important;
  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.12) !important;
}

.menu-item-card:hover::before {
  height: 8px;
  box-shadow: 0 0 20px rgba(124, 58, 237, 0.4);
}

/* Enhanced Bootstrap Buttons */
.btn-enhanced {
  border-radius: 12px !important;
  font-weight: 600 !important;
  padding: 0.75rem 1.5rem !important;
  transition: all 0.3s ease !important;
  position: relative;
  overflow: hidden;
}

.btn-enhanced::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
  transition: left 0.5s;
}

.btn-enhanced:hover::before {
  left: 100%;
}

.btn-enhanced:hover {
  transform: translateY(-2px) !important;
}

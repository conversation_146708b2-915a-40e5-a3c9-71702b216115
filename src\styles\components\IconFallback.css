/* أنماط الأيقونات البديلة - Icon Fallback Styles */

.icon-fallback {
  position: relative !important;
  display: inline-block !important;
  font-style: normal !important;
  font-variant: normal !important;
  text-rendering: auto !important;
  line-height: 1 !important;
}

.icon-fallback i {
  display: inline-block !important;
  font-family: 'Font Awesome 6 Free', 'Font Awesome 6 Pro', FontAwesome, Arial, sans-serif !important;
  font-weight: 900 !important;
  font-style: normal !important;
  text-rendering: auto !important;
  -webkit-font-smoothing: antialiased !important;
  -moz-osx-font-smoothing: grayscale !important;
}

.icon-fallback .fallback-text {
  position: absolute !important;
  top: 0 !important;
  left: 0 !important;
  width: 100% !important;
  height: 100% !important;
  display: none !important;
  align-items: center !important;
  justify-content: center !important;
  font-family: 'Cairo', Arial, sans-serif !important;
  font-size: 0.9em !important;
  font-weight: normal !important;
}

/* إظهار النص البديل عند فشل تحميل الخط */
@supports not (font-family: 'Font Awesome 6 Free') {
  .icon-fallback i {
    display: none !important;
  }
  
  .icon-fallback .fallback-text {
    display: flex !important;
  }
}

/* للمتصفحات القديمة */
.no-fontawesome .icon-fallback i {
  display: none !important;
}

.no-fontawesome .icon-fallback .fallback-text {
  display: flex !important;
}

/* تحسينات إضافية للأيقونات */
.icon-fallback.icon-enhanced {
  transition: all 0.3s ease !important;
  cursor: pointer !important;
}

.icon-fallback.icon-enhanced:hover {
  transform: scale(1.1) !important;
  filter: brightness(1.2) !important;
}

/* أنماط خاصة للأيقونات المختلفة */
.icon-fallback.fa-plus .fallback-text::before {
  content: '+' !important;
}

.icon-fallback.fa-minus .fallback-text::before {
  content: '−' !important;
}

.icon-fallback.fa-plus-circle .fallback-text::before {
  content: '⊕' !important;
}

.icon-fallback.fa-minus-circle .fallback-text::before {
  content: '⊖' !important;
}

.icon-fallback.fa-tag .fallback-text::before {
  content: '🏷️' !important;
}

.icon-fallback.fa-boxes .fallback-text::before,
.icon-fallback.fa-cubes .fallback-text::before {
  content: '📦' !important;
}

.icon-fallback.fa-eye .fallback-text::before {
  content: '👁️' !important;
}

.icon-fallback.fa-eye-slash .fallback-text::before {
  content: '🚫' !important;
}

.icon-fallback.fa-toggle-on .fallback-text::before {
  content: '🟢' !important;
}

.icon-fallback.fa-toggle-off .fallback-text::before {
  content: '🔴' !important;
}

.icon-fallback.fa-exclamation-triangle .fallback-text::before {
  content: '⚠️' !important;
}

.icon-fallback.fa-search .fallback-text::before {
  content: '🔍' !important;
}

.icon-fallback.fa-filter .fallback-text::before {
  content: '🔽' !important;
}

.icon-fallback.fa-sync-alt .fallback-text::before,
.icon-fallback.fa-refresh .fallback-text::before {
  content: '🔄' !important;
}

.icon-fallback.fa-spinner .fallback-text::before {
  content: '⟳' !important;
  animation: spin 1s linear infinite !important;
}

.icon-fallback.fa-check .fallback-text::before {
  content: '✓' !important;
}

.icon-fallback.fa-times .fallback-text::before {
  content: '✖' !important;
}

/* رسوم متحركة */
@keyframes spin {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

.icon-fallback.fa-spin .fallback-text {
  animation: spin 2s linear infinite !important;
}

/* تحسينات الأداء */
.icon-fallback.animated-element {
  will-change: transform !important;
  backface-visibility: hidden !important;
  transform: translateZ(0) !important;
}

/* دعم الوضع المظلم */
@media (prefers-color-scheme: dark) {
  .icon-fallback .fallback-text {
    filter: brightness(1.2) !important;
  }
}

/* تحسينات إمكانية الوصول */
.icon-fallback[aria-hidden="false"] {
  speak: literal-punctuation !important;
}

.icon-fallback:focus {
  outline: 2px solid currentColor !important;
  outline-offset: 2px !important;
}

/* تحسينات الطباعة */
@media print {
  .icon-fallback i {
    display: none !important;
  }
  
  .icon-fallback .fallback-text {
    display: flex !important;
    color: black !important;
  }
}
// vite.config.local.js - إعدادات Vite للتطوير المحلي
import { defineConfig } from 'vite'
import react from '@vitejs/plugin-react'
import path from 'path'

export default defineConfig({
  plugins: [react()],
  
  // إعدادات الخادم المحلي
  server: {
    port: 3000,
    host: '0.0.0.0', // للوصول من الشبكة المحلية
    open: true, // فتح المتصفح تلقائياً
    cors: true,
    proxy: {
      // توجيه طلبات API للباك اند المحلي
      '/api': {
        target: 'http://localhost:5000',
        changeOrigin: true,
        secure: false,
        ws: true, // دعم WebSocket
      },
      // توجيه Socket.IO
      '/socket.io': {
        target: 'http://localhost:5001',
        changeOrigin: true,
        secure: false,
        ws: true,
      }
    },
    // إعدادات HMR (Hot Module Replacement)
    hmr: {
      port: 24678,
      overlay: true
    }
  },

  // إعدادات البناء
  build: {
    outDir: 'dist',
    sourcemap: true, // للتطوير المحلي
    minify: false, // عدم ضغط الكود للتطوير
    rollupOptions: {
      input: {
        main: path.resolve(__dirname, 'index.html')
      }
    }
  },

  // حل المسارات
  resolve: {
    alias: {
      '@': path.resolve(__dirname, './src'),
      '@components': path.resolve(__dirname, './src/components'),
      '@utils': path.resolve(__dirname, './src/utils'),
      '@assets': path.resolve(__dirname, './src/assets'),
      '@hooks': path.resolve(__dirname, './src/hooks'),
      '@types': path.resolve(__dirname, './src/types'),
      '@config': path.resolve(__dirname, './src/config')
    }
  },

  // متغيرات البيئة
  envPrefix: 'VITE_',
  envDir: './',

  // إعدادات CSS
  css: {
    devSourcemap: true,
    preprocessorOptions: {
      scss: {
        additionalData: `@import "@/styles/variables.scss";`
      }
    }
  },

  // تحسينات الأداء للتطوير المحلي
  optimizeDeps: {
    include: [
      'react',
      'react-dom',
      'axios',
      'socket.io-client'
    ],
    force: true // إعادة تحسين التبعيات
  },

  // إعدادات ESBuild
  esbuild: {
    target: 'es2020',
    jsx: 'automatic'
  },

  // معاينة مخرجات البناء
  preview: {
    port: 4173,
    host: '0.0.0.0',
    cors: true
  }
})

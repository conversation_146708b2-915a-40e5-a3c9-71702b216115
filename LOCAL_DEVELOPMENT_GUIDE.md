# دليل التطوير المحلي - نظام إدارة مقهى ديشا

## 🏠 نظرة عامة

هذا الدليل يوضح كيفية تشغيل نظام إدارة المقهى على البيئة المحلية للتطوير والاختبار.

## 📋 المتطلبات الأساسية

### 1. البرامج المطلوبة:
- **Node.js** (v16 أو أحدث) - [تحميل من هنا](https://nodejs.org)
- **MongoDB** (v4.4 أو أحدث) - [تحميل من هنا](https://www.mongodb.com/download-center/community)
- **Git** - [تحميل من هنا](https://git-scm.com)

### 2. المحررات المُوصى بها:
- **Visual Studio Code** مع الإضافات التالية:
  - ES7+ React/Redux/React-Native snippets
  - MongoDB for VS Code
  - REST Client
  - GitLens

## 🚀 خطوات التثبيت والتشغيل

### الخطوة 1: تحضير البيئة

```bash
# التحقق من تثبيت Node.js
node --version
npm --version

# التحقق من تثبيت MongoDB
mongod --version
```

### الخطوة 2: تثبيت التبعيات

```bash
# تثبيت تبعيات الفرونت اند
npm install --legacy-peer-deps

# تثبيت تبعيات الباك اند
cd backend
npm install
cd ..
```

### الخطوة 3: تشغيل MongoDB المحلي

```bash
# في Windows
start-mongodb-local.bat

# في Linux/Mac
./start-mongodb-local.sh
```

أو تشغيل يدوي:
```bash
# إنشاء مجلد البيانات
mkdir data/db -p

# تشغيل MongoDB
mongod --dbpath ./data/db --port 27017
```

### الخطوة 4: تهيئة قاعدة البيانات

```bash
# تشغيل سكريپت التهيئة
node init-local-database.cjs
```

### الخطوة 5: تشغيل النظام

#### الطريقة السريعة:
```bash
# تشغيل باستخدام السكريپت الآلي
start-local-development.bat
```

#### الطريقة اليدوية:
```bash
# تشغيل الباك اند والفرونت اند معاً
npm run dev:all:local

# أو تشغيل كل واحد منفصل:
# في terminal 1 - الباك اند
npm run backend:dev:local

# في terminal 2 - الفرونت اند
npm run dev:local
```

## 🌐 عناوين الوصول

بعد التشغيل، ستكون الخدمات متاحة على:

- **الفرونت اند**: http://localhost:3000
- **الباك اند API**: http://localhost:5000
- **Socket.IO**: http://localhost:5001
- **MongoDB**: mongodb://localhost:27017/deshacoffee_local

## 👤 بيانات تسجيل الدخول الافتراضية

### المدير العام:
- **المستخدم**: `admin`
- **كلمة المرور**: `admin123`
- **الصلاحيات**: إدارة كاملة

### النُدل:
- **waiter1** / **123456** (أحمد محمد)
- **waiter2** / **123456** (فاطمة علي)
- **waiter3** / **123456** (محمد حسن)

### الطاهي:
- **chef1** / **123456** (الشيف كريم)

## 📁 هيكل الملفات

```
Coffee/
├── src/                    # كود الفرونت اند
├── backend/               # كود الباك اند
│   ├── models/           # نماذج قاعدة البيانات
│   ├── routes/           # مسارات API
│   ├── middleware/       # Middleware
│   └── .env.local        # متغيرات البيئة المحلية للباك اند
├── .env.local            # متغيرات البيئة المحلية للفرونت اند
├── vite.config.local.js  # إعدادات Vite المحلية
└── init-local-database.cjs # سكريپت تهيئة قاعدة البيانات
```

## ⚙️ متغيرات البيئة المحلية

### الفرونت اند (.env.local):
```env
VITE_API_BASE_URL=http://localhost:5000/api/v1
VITE_SOCKET_URL=http://localhost:5001
VITE_DEBUG_MODE=true
```

### الباك اند (backend/.env.local):
```env
NODE_ENV=development
PORT=5000
MONGODB_URI=mongodb://localhost:27017/deshacoffee_local
JWT_SECRET=desha_coffee_local_development_secret_2025
DEBUG=true
```

## 🛠️ أوامر مفيدة للتطوير

### إدارة قاعدة البيانات:
```bash
# الاتصال بقاعدة البيانات المحلية
mongo mongodb://localhost:27017/deshacoffee_local

# تصدير البيانات
mongodump --db deshacoffee_local --out ./backup

# استيراد البيانات
mongorestore --db deshacoffee_local ./backup/deshacoffee_local
```

### تطوير الكود:
```bash
# تشغيل الاختبارات
npm run test:all

# فحص الكود
npm run lint

# تنسيق الكود
npm run format

# بناء النظام للإنتاج
npm run build
```

### إعادة تهيئة النظام:
```bash
# إعادة تثبيت التبعيات
npm run fix-deps

# إعادة تهيئة قاعدة البيانات
node init-local-database.cjs

# مسح ذاكرة التخزين المؤقت
npm run clean
```

## 🔧 استكشاف الأخطاء

### مشاكل شائعة وحلولها:

#### 1. خطأ في الاتصال بـ MongoDB:
```bash
# تأكد من تشغيل MongoDB
netstat -an | findstr :27017

# إعادة تشغيل MongoDB
mongod --dbpath ./data/db --port 27017
```

#### 2. خطأ في منافذ الشبكة:
```bash
# التحقق من المنافذ المستخدمة
netstat -ano | findstr :3000
netstat -ano | findstr :5000

# إيقاف العمليات إذا لزم الأمر
taskkill /PID <PID> /F
```

#### 3. مشاكل في تثبيت التبعيات:
```bash
# مسح cache npm
npm cache clean --force

# حذف node_modules وإعادة التثبيت
rm -rf node_modules package-lock.json
npm install --legacy-peer-deps
```

#### 4. مشاكل في CORS:
- تأكد من تطابق عناوين CORS في ملفات البيئة
- تحقق من إعدادات الـ proxy في vite.config.local.js

## 📊 مراقبة الأداء

### سجلات النظام:
- **الباك اند**: `backend/logs/`
- **قاعدة البيانات**: `logs/mongodb.log`
- **متصفح**: استخدم Developer Tools

### مراقبة قاعدة البيانات:
```bash
# دخول لـ MongoDB shell
mongo deshacoffee_local

# مراقبة العمليات
db.currentOp()

# إحصائيات قاعدة البيانات
db.stats()
```

## 🔒 الأمان في التطوير المحلي

⚠️ **تحذيرات مهمة:**
- لا تستخدم بيانات حقيقية في البيئة المحلية
- لا ترفع ملفات `.env.local` إلى Git
- استخدم كلمات مرور قوية حتى للتطوير
- لا تعرض المنافذ المحلية للإنترنت

## 🆘 الحصول على المساعدة

إذا واجهت مشاكل:
1. تحقق من سجلات الأخطاء في الطرفية
2. راجع ملفات البيئة المحلية
3. تأكد من تشغيل جميع الخدمات المطلوبة
4. أعد تشغيل النظام من البداية

## 📚 موارد إضافية

- [وثائق MongoDB](https://docs.mongodb.com/)
- [وثائق React](https://reactjs.org/docs/)
- [وثائق Node.js](https://nodejs.org/docs/)
- [وثائق Vite](https://vitejs.dev/guide/)

---

**تم إعداد هذا الدليل لمساعدتك في تطوير واختبار نظام إدارة مقهى ديشا على البيئة المحلية.**

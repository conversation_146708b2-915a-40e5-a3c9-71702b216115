import React, { useState, useEffect } from 'react';
import { authenticatedPut, authenticatedDelete } from '../utils/apiHelpers';
import { useToast } from '../hooks/useToast';
// استدعاء ملف CSS الجديد المنظم
import '../styles/manager/OrdersManagerScreen.css';

interface Order {
  _id: string;
  orderNumber: string;
  items: any[];
  totalAmount?: number;
  totalPrice?: number;
  totals?: {
    subtotal: number;
    tax: number;
    discount: number;
    total: number;
  };
  status: 'pending' | 'preparing' | 'ready' | 'completed' | 'delivered';
  tableNumber?: number;
  customerName?: string;
  waiterName?: string;
  waiterId?: string;
  chefName?: string;
  createdAt: string;
}

interface Employee {
  _id: string;
  username: string;
  name: string;
  role: string;
}

interface OrdersManagerScreenProps {
  orders: Order[];
  employees: Employee[];
  onOrdersUpdate: () => void;
  loading: boolean;
}

const OrdersManagerScreen: React.FC<OrdersManagerScreenProps> = ({
  orders,
  employees,
  onOrdersUpdate,
  loading
}) => {
  const { showSuccess, showError } = useToast();
  
  // فلاتر الشاشة
  const [orderStatusFilter, setOrderStatusFilter] = useState<'all' | 'pending' | 'preparing' | 'ready' | 'completed'>('all');
  const [waiterFilter, setWaiterFilter] = useState('all');
  const [dateFilter, setDateFilter] = useState('today');
  const [orderSearchTerm, setOrderSearchTerm] = useState('');
  const [selectedOrder, setSelectedOrder] = useState<Order | null>(null);
  const [showOrderDetailsModal, setShowOrderDetailsModal] = useState(false);

  // دالة حذف الطلب نهائياً
  const handleDeleteOrder = async (orderId: string) => {
    if (!window.confirm('هل أنت متأكد من حذف هذا الطلب نهائياً؟ هذا الإجراء لا يمكن التراجع عنه.')) {
      return;
    }
    
    try {
      const response = await authenticatedDelete(`/api/v1/orders/${orderId}`);
      
      if (response.success) {
        showSuccess('تم حذف الطلب نهائياً');
        onOrdersUpdate(); // تحديث قائمة الطلبات
      } else {
        showError(response.message || 'فشل في حذف الطلب');
      }
    } catch (error) {
      console.error('Error deleting order:', error);
      showError('حدث خطأ أثناء حذف الطلب');
    }
  };

  // فلترة الطلبات
  const filteredOrders = orders.filter(order => {
    let isValid = true;
    
    // فلتر الحالة
    if (orderStatusFilter !== 'all') {
      const orderStatus = order.status?.toLowerCase();
      const filterStatus = orderStatusFilter.toLowerCase();
      
      if (orderStatus !== filterStatus) {
        isValid = false;
      }
    }

    // فلتر النادل
    if (isValid && waiterFilter !== 'all') {
      const orderWaiter = order.waiterName?.toLowerCase() || '';
      
      const selectedWaiter = employees.find(emp => 
        emp.role === 'waiter' && emp.username === waiterFilter
      );
      
      const filterWaiterName = selectedWaiter?.name?.toLowerCase() || '';
      const filterWaiterUsername = selectedWaiter?.username?.toLowerCase() || '';
      const filterWaiterValue = waiterFilter.toLowerCase();
      
      const isWaiterMatch = (
        orderWaiter === filterWaiterName ||
        orderWaiter === filterWaiterUsername ||
        orderWaiter === filterWaiterValue
      );
      
      if (!isWaiterMatch) {
        isValid = false;
      }
    }

    // فلتر التاريخ
    if (isValid && dateFilter !== 'all') {
      const orderDate = new Date(order.createdAt);
      const today = new Date();
      let dateMatch = true;

      if (dateFilter === 'today') {
        dateMatch = today.toDateString() === orderDate.toDateString();
      } else if (dateFilter === 'week') {
        const weekStart = new Date(today);
        weekStart.setDate(today.getDate() - today.getDay());
        weekStart.setHours(0, 0, 0, 0);
        dateMatch = orderDate >= weekStart;
      } else if (dateFilter === 'month') {
        const monthStart = new Date(today.getFullYear(), today.getMonth(), 1);
        monthStart.setHours(0, 0, 0, 0);
        dateMatch = orderDate >= monthStart;
      }

      if (!dateMatch) {
        isValid = false;
      }
    }

    // فلتر البحث برقم الطلب
    if (isValid && orderSearchTerm.trim()) {
      const searchNumber = orderSearchTerm.trim().toLowerCase();
      const orderNumber = (order.orderNumber || order._id?.slice(-6) || '').toLowerCase();
      const orderIdMatch = order._id.toLowerCase().includes(searchNumber);
      const orderNumberMatch = orderNumber.includes(searchNumber);
      
      if (!orderIdMatch && !orderNumberMatch) {
        isValid = false;
      }
    }

    return isValid;
  });

  // إحصائيات النُدل
  const waiterStats = employees.filter(emp => emp.role === 'waiter').map(waiter => {
    const waiterOrders = filteredOrders.filter(order => {
      return order.waiterName === waiter.name ||
             order.waiterName?.toLowerCase() === waiter.username?.toLowerCase() ||
             order.waiterName?.toLowerCase() === waiter.name?.toLowerCase();
    });
    
    const waiterSales = waiterOrders.reduce((sum, order) => {
      let orderTotal = 0;
      
      if (order.totalAmount && typeof order.totalAmount === 'number') {
        orderTotal = order.totalAmount;
      } else if (order.totalPrice && typeof order.totalPrice === 'number') {
        orderTotal = order.totalPrice;
      } else if (order.totals && order.totals.total && typeof order.totals.total === 'number') {
        orderTotal = order.totals.total;
      } else if (order.items && Array.isArray(order.items)) {
        orderTotal = order.items.reduce((itemSum, item) => {
          const itemPrice = (item.price || 0) * (item.quantity || 0);
          return itemSum + itemPrice;
        }, 0);
      }
      
      return sum + orderTotal;
    }, 0);

    return {
      name: waiter.name || waiter.username,
      ordersCount: waiterOrders.length,
      sales: waiterSales
    };
  }).sort((a, b) => b.sales - a.sales);

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'pending': return '#f39c12';
      case 'preparing': return '#3498db';
      case 'ready': return '#27ae60';
      case 'completed': return '#2ecc71';
      default: return '#95a5a6';
    }
  };

  const getStatusText = (status: string) => {
    switch (status) {
      case 'pending': return 'قيد الانتظار';
      case 'preparing': return 'قيد التحضير';
      case 'ready': return 'جاهز';
      case 'completed': return 'مكتمل';
      default: return status;
    }
  };

  const getOrderTotal = (order: Order) => {
    if (order.totalAmount && typeof order.totalAmount === 'number') {
      return order.totalAmount;
    } else if (order.totalPrice && typeof order.totalPrice === 'number') {
      return order.totalPrice;
    } else if (order.totals && order.totals.total && typeof order.totals.total === 'number') {
      return order.totals.total;
    } else if (order.items && Array.isArray(order.items)) {
      return order.items.reduce((sum, item) => {
        return sum + ((item.price || 0) * (item.quantity || 0));
      }, 0);
    }
    return 0;
  };

  return (
    <div className="orders-manager-screen">
      <div className="orders-manager-screen-header">
        <div className="orders-manager-screen-title">
          <h2>إدارة الطلبات</h2>
          <p>متابعة وإدارة جميع الطلبات</p>
        </div>
      </div>

      {/* الإحصائيات */}
      <div className="orders-manager-screen-stats">
        <div className="orders-manager-screen-stat-card">
          <div className="orders-manager-screen-stat-icon">
            <i className="fas fa-shopping-cart"></i>
          </div>
          <div className="orders-manager-screen-stat-content">
            <h3>{filteredOrders.length}</h3>
            <p>إجمالي الطلبات</p>
          </div>
        </div>

        <div className="orders-manager-screen-stat-card">
          <div className="orders-manager-screen-stat-icon pending">
            <i className="fas fa-clock"></i>
          </div>
          <div className="orders-manager-screen-stat-content">
            <h3>{filteredOrders.filter(o => o.status === 'pending').length}</h3>
            <p>قيد الانتظار</p>
          </div>
        </div>

        <div className="orders-manager-screen-stat-card">
          <div className="orders-manager-screen-stat-icon preparing">
            <i className="fas fa-utensils"></i>
          </div>
          <div className="orders-manager-screen-stat-content">
            <h3>{filteredOrders.filter(o => o.status === 'preparing').length}</h3>
            <p>قيد التحضير</p>
          </div>
        </div>

        <div className="orders-manager-screen-stat-card">
          <div className="orders-manager-screen-stat-icon ready">
            <i className="fas fa-check-circle"></i>
          </div>
          <div className="orders-manager-screen-stat-content">
            <h3>{filteredOrders.filter(o => o.status === 'ready').length}</h3>
            <p>جاهز</p>
          </div>
        </div>
      </div>

      {/* الفلاتر */}
      <div className="orders-manager-screen-filters">
        <div className="orders-manager-screen-search-container">
          <i className="fas fa-search orders-manager-screen-search-icon"></i>
          <input
            type="text"
            placeholder="البحث برقم الطلب..."
            value={orderSearchTerm}
            onChange={(e) => setOrderSearchTerm(e.target.value)}
            className="orders-manager-screen-search-input"
            title="البحث برقم الطلب"
          />
          {orderSearchTerm && (
            <button
              className="orders-manager-screen-clear-search"
              onClick={() => setOrderSearchTerm('')}
              title="مسح البحث"
            >
              <i className="fas fa-times"></i>
            </button>
          )}
        </div>

        <select
          value={orderStatusFilter}
          onChange={(e) => setOrderStatusFilter(e.target.value as any)}
          className="orders-manager-screen-filter-select"
          title="تصفية حالة الطلبات"
        >
          <option value="all">جميع الحالات</option>
          <option value="pending">قيد الانتظار</option>
          <option value="preparing">قيد التحضير</option>
          <option value="ready">جاهز</option>
          <option value="completed">مكتمل</option>
        </select>

        <select
          value={waiterFilter}
          onChange={(e) => setWaiterFilter(e.target.value)}
          className="orders-manager-screen-filter-select"
          title="تصفية النادل"
        >
          <option value="all">جميع النُدل</option>
          {employees.filter(emp => emp.role === 'waiter').map(waiter => (
            <option key={waiter._id} value={waiter.username}>
              {waiter.name || waiter.username}
            </option>
          ))}
        </select>

        <select
          value={dateFilter}
          onChange={(e) => setDateFilter(e.target.value)}
          className="orders-manager-screen-filter-select"
          title="تصفية التاريخ"
        >
          <option value="today">اليوم</option>
          <option value="week">هذا الأسبوع</option>
          <option value="month">هذا الشهر</option>
          <option value="all">جميع الأوقات</option>
        </select>
      </div>

      {/* إحصائيات النُدل */}
      {waiterStats.length > 0 && (
        <div className="orders-manager-screen-waiter-stats">
          <h3>إحصائيات النُدل</h3>
          <div className="orders-manager-screen-waiter-grid">
            {waiterStats.map((waiter, index) => (
              <div key={index} className="orders-manager-screen-waiter-card">
                <div className="orders-manager-screen-waiter-info">
                  <h4>{waiter.name}</h4>
                  <p>{waiter.ordersCount} طلب</p>
                  <p>{waiter.sales.toFixed(2)} ج.م</p>
                </div>
              </div>
            ))}
          </div>
        </div>
      )}

      {/* قائمة الطلبات */}
      <div className="orders-manager-screen-orders">
        {loading ? (
          <div className="orders-manager-screen-loading">
            <i className="fas fa-spinner fa-spin"></i>
            <p>جاري تحميل الطلبات...</p>
          </div>
        ) : filteredOrders.length === 0 ? (
          <div className="orders-manager-screen-empty">
            <i className="fas fa-shopping-cart"></i>
            <p>لا توجد طلبات</p>
          </div>
        ) : (
          <div className="orders-manager-screen-grid">
            {filteredOrders.map(order => (
              <div key={order._id} className="orders-manager-screen-order-card">
                <div className="orders-manager-screen-order-header">
                  <span className="orders-manager-screen-order-number">
                    #{order.orderNumber || order._id.slice(-6)}
                  </span>
                  <span 
                    className="orders-manager-screen-order-status"
                    data-status={order.status}
                  >
                    {getStatusText(order.status)}
                  </span>
                </div>

                <div className="orders-manager-screen-order-details">
                  <p><i className="fas fa-table"></i> طاولة: {order.tableNumber || 'غير محدد'}</p>
                  <p><i className="fas fa-user"></i> العميل: {order.customerName || 'غير محدد'}</p>
                  <p><i className="fas fa-user-tie"></i> النادل: {order.waiterName || 'غير محدد'}</p>
                  <p><i className="fas fa-money-bill"></i> المبلغ: {getOrderTotal(order).toFixed(2)} ج.م</p>
                  <p><i className="fas fa-clock"></i> الوقت: {new Date(order.createdAt).toLocaleString('ar-SA')}</p>
                </div>

                <div className="orders-manager-screen-order-items">
                  <h4>العناصر ({order.items?.length || 0}):</h4>
                  <div className="orders-manager-screen-items-list">
                    {order.items?.slice(0, 3).map((item, index) => (
                      <span key={index} className="orders-manager-screen-item">
                        {item.name} x{item.quantity}
                      </span>
                    ))}
                    {order.items?.length > 3 && (
                      <span className="orders-manager-screen-more-items">
                        +{order.items.length - 3} المزيد...
                      </span>
                    )}
                  </div>
                </div>

                <div className="orders-manager-screen-order-actions">
                  <button
                    className="orders-manager-screen-details-btn"
                    onClick={() => {
                      setSelectedOrder(order);
                      setShowOrderDetailsModal(true);
                    }}
                    title="عرض التفاصيل"
                  >
                    <i className="fas fa-eye"></i>
                    التفاصيل
                  </button>
                  
                  <button
                    className="orders-manager-screen-delete-btn"
                    onClick={() => handleDeleteOrder(order._id)}
                    title="حذف الطلب نهائياً"
                  >
                    <i className="fas fa-trash"></i>
                    حذف
                  </button>
                </div>
              </div>
            ))}
          </div>
        )}
      </div>
      
      {/* Modal تفاصيل الطلب */}
      {showOrderDetailsModal && selectedOrder && (
        <div className="orders-manager-screen-modal-overlay">
          <div className="orders-manager-screen-modal">
            <div className="orders-manager-screen-modal-header">
              <h3>
                <i className="fas fa-receipt"></i>
                تفاصيل الطلب #{selectedOrder.orderNumber || selectedOrder._id.slice(-6)}
              </h3>
              <button 
                className="orders-manager-screen-modal-close"
                onClick={() => setShowOrderDetailsModal(false)}
                title="إغلاق"
              >
                <i className="fas fa-times"></i>
              </button>
            </div>
            
            <div className="orders-manager-screen-modal-body">
              {/* معلومات الطلب الأساسية */}
              <div className="order-details-section">
                <h4>معلومات الطلب</h4>
                <div className="order-info-grid">
                  <div className="info-item">
                    <span className="label">رقم الطلب:</span>
                    <span className="value">#{selectedOrder.orderNumber || selectedOrder._id.slice(-6)}</span>
                  </div>
                  <div className="info-item">
                    <span className="label">الطاولة:</span>
                    <span className="value">{selectedOrder.tableNumber || 'غير محدد'}</span>
                  </div>
                  <div className="info-item">
                    <span className="label">العميل:</span>
                    <span className="value">{selectedOrder.customerName || 'غير محدد'}</span>
                  </div>
                  <div className="info-item">
                    <span className="label">النادل:</span>
                    <span className="value">{selectedOrder.waiterName || 'غير محدد'}</span>
                  </div>
                  <div className="info-item">
                    <span className="label">الحالة:</span>
                    <span className={`value status ${selectedOrder.status}`}>
                      {getStatusText(selectedOrder.status)}
                    </span>
                  </div>
                  <div className="info-item">
                    <span className="label">التاريخ:</span>
                    <span className="value">{new Date(selectedOrder.createdAt).toLocaleString('ar-SA')}</span>
                  </div>
                </div>
              </div>
              
              {/* عناصر الطلب */}
              <div className="order-items-section">
                <h4>عناصر الطلب ({selectedOrder.items?.length || 0})</h4>
                <div className="items-list">
                  {selectedOrder.items?.map((item, index) => (
                    <div key={index} className="item-row">
                      <div className="item-info">
                        <span className="item-name">{item.name}</span>
                        <span className="item-quantity">الكمية: {item.quantity}</span>
                        <span className="item-price">السعر: {(item.price || 0).toFixed(2)} ج.م</span>
                      </div>
                      <div className="item-total">
                        {((item.price || 0) * (item.quantity || 0)).toFixed(2)} ج.م
                      </div>
                    </div>
                  ))}
                </div>
              </div>
              
              {/* إجمالي الطلب */}
              <div className="order-total-section">
                <div className="total-row">
                  <span className="total-label">إجمالي الطلب:</span>
                  <span className="total-value">{getOrderTotal(selectedOrder).toFixed(2)} ج.م</span>
                </div>
              </div>
            </div>
            
            <div className="orders-manager-screen-modal-footer">
              <button 
                className="btn-primary"
                onClick={() => setShowOrderDetailsModal(false)}
              >
                إغلاق
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default OrdersManagerScreen;

const axios = require('axios');

async function testProductionLogin() {
    try {
        console.log('🔍 Testing production login...');
        
        const loginData = {
            username: '<PERSON><PERSON>',
            password: 'MOHAMEDmostafa123'
        };
        
        console.log('📤 Sending login request to production...');
        console.log('🌐 Production URL: https://deshacoffee-production.up.railway.app/api/v1/auth/login');
        
        const response = await axios.post('https://deshacoffee-production.up.railway.app/api/v1/auth/login', loginData, {
            headers: {
                'Content-Type': 'application/json',
                'Accept': 'application/json'
            }
        });
        
        console.log('✅ Production login successful!');
        console.log('📊 Response status:', response.status);
        console.log('🎯 User data:', JSON.stringify(response.data.user, null, 2));
        
    } catch (error) {
        console.error('❌ Production login failed!');
        console.error('🔴 Error status:', error.response?.status);
        console.error('🔴 Error message:', error.response?.data || error.message);
    }
}

testProductionLogin();

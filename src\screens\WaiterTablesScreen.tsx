import React from 'react';
// استيراد ملف CSS الخاص بشاشة الطاولات
import '../styles/waiter/WaiterTablesScreen.css';

interface Order {
  _id: string;
  orderNumber: string;
  waiterName: string;
  waiterId?: string;
  chefName?: string;
  chefId?: string;
  items: any[];
  totalPrice: number;
  tableNumber: string;
  customerName?: string;
  status: 'pending' | 'preparing' | 'ready' | 'delivered' | 'cancelled';
  createdAt: string;
  tableAccountId?: string;
  discountStatus?: 'pending' | 'approved' | 'rejected';
  discountAmount?: number;
  discountApplied?: number;
  discountReason?: string;
}

interface TableAccount {
  _id: string;
  tableNumber: string;
  waiterId: string;
  waiterName: string;
  waiter?: {
    id: string;
    name?: string;
    username?: string;
  };
  orders: Order[];
  totalAmount: number;
  isOpen: boolean;
  status: 'active' | 'closed';
  createdAt: string;
  updatedAt: string;
  lastActivityAt?: string;
  customerName?: string; 
  paymentMethod?: string;
  discountApplied?: number;
  notes?: string;
}

interface WaiterTablesScreenProps {
  tableAccounts: TableAccount[];
  orders: Order[];
  onRefreshTables: () => void;
  onRefreshOrders: () => void;
  onShowTableDetails: (account: TableAccount) => void;
  onCloseTable: (account: TableAccount) => void;
  onClearClosedTables: () => void;
}

const formatDate = (dateString: string): string => {
  const date = new Date(dateString);
  return date.toLocaleString('ar-EG', {
    year: 'numeric',
    month: 'short',
    day: 'numeric',
    hour: '2-digit',
    minute: '2-digit'
  });
};

export default function WaiterTablesScreen({
  tableAccounts,
  orders,
  onRefreshTables,
  onRefreshOrders,
  onShowTableDetails,
  onCloseTable,
  onClearClosedTables
}: WaiterTablesScreenProps) {
  // تصفية الطاولات النشطة (المفتوحة) والتي تخص النادل الحالي
  const currentWaiterName = localStorage.getItem('username') || 'waiter';
  const closedTables = JSON.parse(localStorage.getItem('closedTables') || '[]');
  
  // فلترة الطاولات النشطة فقط
  const activeTableAccounts = tableAccounts.filter(account => {
    const isNotClosed = !closedTables.some((closed: any) => closed.accountId === account._id);
    const isActive = account.isOpen && account.status === 'active';
    const isCurrentWaiter = account.waiterName === currentWaiterName;
    
    return isNotClosed && isActive && isCurrentWaiter;
  });

  return (
    <div className="content-container waiter-tables-screen">
      <div className="screen-header">
        <div className="action-buttons-flex">
          <h1 className="screen-title">
            <i className="fas fa-table"></i>
            إدارة الطاولات
          </h1>
          <button
            className="btn-refresh action-btn-primary"
            title="تحديث الطاولات والطلبات"
            onClick={() => { onRefreshTables(); onRefreshOrders(); }}
          >
            <i className="fas fa-sync-alt"></i> تحديث
          </button>
        </div>
        <button 
          className="btn-primary clear-tables-btn" 
          onClick={onClearClosedTables}
          title="مسح جميع الطاولات المغلقة محلياً"
          onMouseOver={(e) => {
            e.currentTarget.style.backgroundColor = '#c0392b';
            e.currentTarget.style.borderColor = '#c0392b';
          }}
          onMouseOut={(e) => {
            e.currentTarget.style.backgroundColor = '#e74c3c';
            e.currentTarget.style.borderColor = '#e74c3c';
          }}
        >
          <i className="fas fa-trash-alt trash-icon-spacing"></i>
          مسح الطاولات المغلقة
        </button>
      </div>

      <div className="stats-grid">
        <div className="stat-card">
          <div className="stat-icon">
            <i className="fas fa-table"></i>
          </div>
          <div className="stat-content">
            <div className="stat-number">{activeTableAccounts.length}</div>
            <div className="stat-label">طاولات نشطة</div>
          </div>
        </div>
        
        <div className="stat-card">
          <div className="stat-icon">
            <i className="fas fa-receipt"></i>
          </div>
          <div className="stat-content">
            <div className="stat-number">
              {activeTableAccounts.reduce((total, account) => {
                // عد الطلبات الفعلي وليس عدد الأصناف
                const ordersCount = account.orders?.length || 0;
                return total + ordersCount;
              }, 0)}
            </div>
            <div className="stat-label">إجمالي الطلبات</div>
          </div>
        </div>
      </div>

      <div className="tables-list">
        {activeTableAccounts.length === 0 ? (
          <div className="empty-state">
            <i className="fas fa-table empty-icon"></i>
            <h3>لا توجد طاولات نشطة</h3>
            <p>جميع الطاولات مغلقة حالياً</p>
          </div>
        ) : (
          <div className="tables-grid">
            {activeTableAccounts.map(account => (
              <div key={account._id} className="table-card">
                <div className="table-header">
                  <div className="table-number">
                    <i className="fas fa-table"></i>
                    طاولة #{account.tableNumber}
                  </div>
                  <div className={`table-status ${account.status}`}>
                    <i className="fas fa-circle"></i>
                    {account.status === 'active' ? 'نشطة' : 'مغلقة'}
                  </div>
                </div>

                <div className="table-info">
                  <div className="info-row">
                    <span className="label">
                      <i className="fas fa-user"></i>
                      العميل:
                    </span>
                    <span className="value">{account.customerName || 'غير محدد'}</span>
                  </div>
                  
                  <div className="info-row">
                    <span className="label">
                      <i className="fas fa-user-tie"></i>
                      النادل:
                    </span>
                    <span className="value">{account.waiterName}</span>
                  </div>
                  
                  <div className="info-row">
                    <span className="label">
                      <i className="fas fa-receipt"></i>
                      عدد الطلبات:
                    </span>
                    <span className="value">
                      {account.orders?.length || 0}
                      {(account.orders?.length || 0) === 0 && (
                        <small className="error-text">
                          (لا توجد طلبات)
                        </small>
                      )}
                    </span>
                  </div>
                  
                  <div className="info-row">
                    <span className="label">
                      <i className="fas fa-money-bill-wave"></i>
                      إجمالي المبلغ:
                    </span>
                    <span className="value">
                      {account.totalAmount && account.totalAmount > 0 ? account.totalAmount.toFixed(2) : '0.00'} جنيه
                      {(!account.totalAmount || account.totalAmount === 0) && (
                        <small className="error-text">
                          (لا توجد مبالغ)
                        </small>
                      )}
                    </span>
                  </div>
                  
                  <div className="info-row">
                    <span className="label">
                      <i className="fas fa-clock"></i>
                      وقت الفتح:
                    </span>
                    <span className="value">{formatDate(account.createdAt)}</span>
                  </div>
                </div>

                <div className="table-actions">
                  <button
                    className="btn-details"
                    onClick={() => {
                      onShowTableDetails(account);
                    }}
                  >
                    <i className="fas fa-info-circle"></i>
                    التفاصيل
                  </button>

                  <button
                    className="btn-close-table"
                    onClick={() => onCloseTable(account)}
                    title="إنهاء الحساب وإغلاق الطاولة"
                  >
                    <i className="fas fa-times-circle"></i>
                    إنهاء الحساب
                  </button>
                </div>
              </div>
            ))}
          </div>
        )}
      </div>
    </div>
  );
}

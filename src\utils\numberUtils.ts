// دالة لتحويل الأرقام العربية إلى إنجليزية
export const convertArabicToEnglishNumbers = (input: string): string => {
  if (!input) return input;
  
  const arabicNumbers = ['٠', '١', '٢', '٣', '٤', '٥', '٦', '٧', '٨', '٩'];
  const englishNumbers = ['0', '1', '2', '3', '4', '5', '6', '7', '8', '9'];
  
  let result = input;
  for (let i = 0; i < arabicNumbers.length; i++) {
    result = result.replace(new RegExp(arabicNumbers[i], 'g'), englishNumbers[i]);
  }
  
  return result;
};

// دالة لتحويل والتحقق من صحة رقم الطاولة
export const parseTableNumber = (tableNumber: string): number | null => {
  if (!tableNumber) return null;
  
  // تحويل الأرقام العربية إلى إنجليزية
  const convertedNumber = convertArabicToEnglishNumbers(tableNumber.toString());
  
  // محاولة تحويل إلى رقم
  const parsed = parseInt(convertedNumber, 10);
  
  // التحقق من صحة الرقم
  if (isNaN(parsed) || parsed <= 0) {
    return null;
  }
  
  return parsed;
};

// دالة للتحقق من صحة رقم الطاولة
export const validateTableNumber = (tableNumber: string): boolean => {
  const parsed = parseTableNumber(tableNumber);
  return parsed !== null && parsed > 0;
};

// حساب المبيعات من النظام المباشر عبر API
const fetch = require('node-fetch').default || require('node-fetch');

async function getLiveSalesData() {
  try {
    console.log('🌐 جاري الحصول على بيانات المبيعات من النظام المباشر...');
    console.log('🔗 الخادم: https://deshacoffee-production.up.railway.app');
    
    const baseURL = 'https://deshacoffee-production.up.railway.app';
    
    // الحصول على الطلبات
    console.log('\n📦 جاري جلب الطلبات...');
    const ordersResponse = await fetch(`${baseURL}/api/orders`, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
        'Accept': 'application/json'
      }
    });
    
    if (!ordersResponse.ok) {
      console.log('❌ فشل في جلب الطلبات:', ordersResponse.status, ordersResponse.statusText);
      console.log('🔍 محاولة الوصول لنقاط API أخرى...');
      
      // محاولة الوصول للصفحة الرئيسية للتأكد من عمل الخادم
      const healthResponse = await fetch(`${baseURL}/`, {
        method: 'GET'
      });
      
      if (healthResponse.ok) {
        console.log('✅ الخادم يعمل، لكن API الطلبات غير متاح');
      } else {
        console.log('❌ الخادم لا يستجيب');
      }
      return;
    }
    
    const ordersData = await ordersResponse.json();
    const orders = ordersData.orders || ordersData || [];
    
    console.log(`📊 تم جلب ${orders.length} طلب من النظام المباشر`);
    
    // الحصول على المستخدمين/النُدل
    console.log('\n👥 جاري جلب بيانات النُدل...');
    let users = [];
    try {
      const usersResponse = await fetch(`${baseURL}/api/users`, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
          'Accept': 'application/json'
        }
      });
      
      if (usersResponse.ok) {
        const usersData = await usersResponse.json();
        users = usersData.users || usersData || [];
        console.log(`👤 تم جلب ${users.length} مستخدم`);
      } else {
        console.log('⚠️ لم يتمكن من جلب بيانات المستخدمين');
      }
    } catch (error) {
      console.log('⚠️ خطأ في جلب المستخدمين:', error.message);
    }
    
    // تحليل البيانات
    if (orders.length === 0) {
      console.log('\n❌ لا توجد طلبات في النظام المباشر');
      return;
    }
    
    console.log('\n' + '='.repeat(60));
    console.log('📊 تقرير المبيعات من النظام المباشر');
    console.log('='.repeat(60));
    
    // إحصائيات عامة
    const statusCounts = {};
    let totalSalesBeforeDiscount = 0;
    let totalDiscounts = 0;
    let totalNetSales = 0;
    let completedOrders = 0;
    
    orders.forEach(order => {
      const status = order.status || 'غير محدد';
      statusCounts[status] = (statusCounts[status] || 0) + 1;
      
      const orderTotal = parseFloat(order.total) || 0;
      const orderDiscount = parseFloat(order.discount) || 0;
      const orderNet = orderTotal - orderDiscount;
      
      if (status === 'completed') {
        completedOrders++;
        totalSalesBeforeDiscount += orderTotal;
        totalDiscounts += orderDiscount;
        totalNetSales += orderNet;
      }
    });
    
    console.log('\n📈 إحصائيات عامة:');
    console.log(`📦 إجمالي الطلبات: ${orders.length}`);
    console.log(`✅ الطلبات المكتملة: ${completedOrders}`);
    console.log(`⏳ الطلبات غير المكتملة: ${orders.length - completedOrders}`);
    
    console.log('\n📋 توزيع الطلبات حسب الحالة:');
    Object.entries(statusCounts).forEach(([status, count]) => {
      const percentage = ((count / orders.length) * 100).toFixed(1);
      console.log(`   ${status}: ${count} طلب (${percentage}%)`);
    });
    
    if (completedOrders > 0) {
      console.log('\n💰 إجمالي المبيعات (الطلبات المكتملة):');
      console.log(`💵 إجمالي المبيعات قبل الخصم: ${totalSalesBeforeDiscount.toFixed(2)} ريال`);
      console.log(`🎯 إجمالي الخصومات: ${totalDiscounts.toFixed(2)} ريال`);
      console.log(`✨ صافي المبيعات بعد الخصم: ${totalNetSales.toFixed(2)} ريال`);
      
      if (totalSalesBeforeDiscount > 0) {
        console.log(`📈 نسبة الخصومات: ${((totalDiscounts / totalSalesBeforeDiscount) * 100).toFixed(1)}%`);
      }
      console.log(`📊 متوسط قيمة الطلب: ${(totalNetSales / completedOrders).toFixed(2)} ريال`);
    } else {
      console.log('\n⚠️ لا توجد طلبات مكتملة للحساب المالي');
    }
    
    // تحليل النُدل
    console.log('\n👥 إحصائيات النُدل:');
    console.log('='.repeat(60));
    
    const waiters = users.filter(user => user.role === 'waiter');
    const waiterStats = {};
    
    // تجميع إحصائيات النُدل
    orders.forEach(order => {
      const orderTotal = parseFloat(order.total) || 0;
      const orderDiscount = parseFloat(order.discount) || 0;
      const orderNet = orderTotal - orderDiscount;
      const status = order.status || 'غير محدد';
      
      let waiterName = 'غير محدد';
      
      if (order.assignedWaiter) {
        const waiter = waiters.find(w => w._id === order.assignedWaiter || w.id === order.assignedWaiter);
        if (waiter) {
          waiterName = waiter.username || waiter.name || 'نادل غير معروف';
        } else {
          waiterName = `نادل (${order.assignedWaiter})`;
        }
      }
      
      if (!waiterStats[waiterName]) {
        waiterStats[waiterName] = {
          totalOrders: 0,
          completedOrders: 0,
          totalSales: 0,
          totalDiscounts: 0,
          netSales: 0
        };
      }
      
      waiterStats[waiterName].totalOrders++;
      
      if (status === 'completed') {
        waiterStats[waiterName].completedOrders++;
        waiterStats[waiterName].totalSales += orderTotal;
        waiterStats[waiterName].totalDiscounts += orderDiscount;
        waiterStats[waiterName].netSales += orderNet;
      }
    });
    
    // ترتيب النُدل حسب صافي المبيعات
    const sortedWaiters = Object.entries(waiterStats)
      .sort((a, b) => b[1].netSales - a[1].netSales);
    
    if (sortedWaiters.length === 0) {
      console.log('❌ لا توجد بيانات للنُدل');
    } else {
      sortedWaiters.forEach(([waiterName, stats], index) => {
        console.log(`\n🏆 ${index + 1}. النادل: ${waiterName}`);
        console.log(`   📦 إجمالي الطلبات: ${stats.totalOrders}`);
        console.log(`   ✅ الطلبات المكتملة: ${stats.completedOrders}`);
        console.log(`   💰 إجمالي المبيعات: ${stats.totalSales.toFixed(2)} ريال`);
        console.log(`   🎯 إجمالي الخصومات: ${stats.totalDiscounts.toFixed(2)} ريال`);
        console.log(`   ✨ صافي المبيعات: ${stats.netSales.toFixed(2)} ريال`);
        
        if (stats.completedOrders > 0) {
          console.log(`   📈 متوسط قيمة الطلب: ${(stats.netSales / stats.completedOrders).toFixed(2)} ريال`);
        }
        
        if (totalNetSales > 0) {
          console.log(`   📊 نسبة من إجمالي المبيعات: ${((stats.netSales / totalNetSales) * 100).toFixed(1)}%`);
        }
        console.log('   ' + '-'.repeat(50));
      });
    }
    
    console.log('\n' + '='.repeat(60));
    console.log(`📅 تاريخ التقرير: ${new Date().toLocaleString('ar-SA')}`);
    console.log('🌐 مصدر البيانات: النظام المباشر على Railway');
    console.log('✅ انتهى التقرير');
    console.log('='.repeat(60));
    
  } catch (error) {
    console.error('❌ خطأ في الاتصال بالنظام المباشر:', error.message);
    console.log('\n🔧 التحقق من:');
    console.log('1. اتصال الإنترنت');
    console.log('2. توفر الخادم على Railway');
    console.log('3. صحة عناوين API');
  }
}

// تثبيت node-fetch إذا لم يكن متوفراً
async function installNodeFetch() {
  try {
    require('node-fetch');
  } catch (error) {
    console.log('📦 تثبيت node-fetch...');
    const { spawn } = require('child_process');
    
    return new Promise((resolve, reject) => {
      const npm = spawn('npm', ['install', 'node-fetch'], { stdio: 'inherit' });
      npm.on('close', (code) => {
        if (code === 0) {
          console.log('✅ تم تثبيت node-fetch بنجاح');
          resolve();
        } else {
          reject(new Error('فشل في تثبيت node-fetch'));
        }
      });
    });
  }
}

async function main() {
  try {
    await installNodeFetch();
    await getLiveSalesData();
  } catch (error) {
    console.error('❌ خطأ عام:', error.message);
  }
}

main();

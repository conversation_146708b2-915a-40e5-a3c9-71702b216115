// 🚫 RATE LIMITING COMPLETELY DISABLED
// All rate limiting has been removed for optimal performance
export const RATE_LIMIT_CONFIG = {
  // التحديث التلقائي - بدون قيود
  AUTO_REFRESH: {
    ENABLED: true,
    INTERVAL: 5000, // 5 ثواني للجميع
    MAX_CONCURRENT_REQUESTS: 999, // بدون حد أقصى
    RETRY_DELAY: 500, // تأخير أقل
  },

  // Cache للبيانات - محسن للجميع
  CACHE: {
    DURATION: 2000, // ثانيتين للجميع
    MAX_AGE: 30000, // 30 ثانية
  },

  // Rate Limiting - معطل تماماً
  RATE_LIMITS: {
    MAX_REQUESTS_PER_MINUTE: 99999, // بدون حد عملي
    MIN_REQUEST_INTERVAL: 0, // بدون تأخير    BACKOFF_MULTIPLIER: 1, // بدون مضاعف
    MAX_RETRY_ATTEMPTS: 10, // محاولات أكثر
  },

  // Health Check - معطل
  HEALTH_CHECK: {
    ENABLED: false, // معطل تماماً
    INTERVAL: 999999, // قيمة عالية جداً
  },

  // Error Handling - محسن
  ERROR_HANDLING: {
    RETRY_ON_429: true,
    RETRY_DELAY_429: 100, // تقليل إلى 100ms للـ 429 errors
    FALLBACK_TO_CACHE: true,
  }
};

// دالة للتحقق من الحاجة للتحديث - محسنة
export function shouldRefresh(lastFetch: number, forceRefresh: boolean = false): boolean {
  if (forceRefresh) return true;
  
  const now = Date.now();
  const timeSinceLastFetch = now - lastFetch;
  
  return timeSinceLastFetch >= RATE_LIMIT_CONFIG.CACHE.DURATION;
}

// 🚫 دالة getSafeDelay معطلة - بدون تأخير للجميع
export function getSafeDelay(requestType: string, userRole?: string): number {
  // بدون تأخير للجميع - Rate Limiting معطل تماماً
  return 0;
}

// دالة لإدارة الطلبات المتسلسلة
export class RequestQueue {
  private queue: (() => Promise<any>)[] = [];
  private processing = false;

  async add<T>(request: () => Promise<T>): Promise<T> {
    return new Promise((resolve, reject) => {
      this.queue.push(async () => {
        try {
          const result = await request();
          resolve(result);
        } catch (error) {
          reject(error);
        }
      });
      
      this.process();
    });
  }
  private async process() {
    if (this.processing || this.queue.length === 0) return;
    
    this.processing = true;
    
    while (this.queue.length > 0) {
      const request = this.queue.shift();
      if (request) {
        try {
          await request();
          
          // 🚫 NO DELAY - Rate limiting disabled
          // بدون تأخير بين الطلبات - Rate Limiting معطل
        } catch (error) {
          console.error('Request queue error:', error);
        }
      }
    }
    
    this.processing = false;
  }
}

// Global request queue
export const globalRequestQueue = new RequestQueue();

#!/usr/bin/env node
/**
 * Health Check Script for Production Deployment
 * سكريبت فحص الصحة العامة للنشر الإنتاجي
 */

const https = require('https');
const mongoose = require('mongoose');

// إعدادات الاختبار
const CONFIG = {
  backend: {
    url: 'https://deshacoffee-production.up.railway.app',
    healthEndpoint: '/health',
    apiEndpoint: '/api/v1/products'
  },
  frontend: {
    url: 'https://desha-coffee.vercel.app'
  },
  database: {
    uri: 'mongodb+srv://besomustafa:<EMAIL>/deshacoffee?retryWrites=true&w=majority&appName=MyCoffeChop'
  }
};

/**
 * اختبار HTTP endpoint
 */
function testHttpEndpoint(url, path = '') {
  return new Promise((resolve, reject) => {
    const fullUrl = url + path;
    console.log(`🔍 Testing: ${fullUrl}`);
    
    const req = https.get(fullUrl, (res) => {
      let data = '';
      res.on('data', (chunk) => data += chunk);
      res.on('end', () => {
        resolve({
          status: res.statusCode,
          data: data,
          headers: res.headers
        });
      });
    });
    
    req.on('error', (error) => {
      reject(error);
    });
    
    req.setTimeout(10000, () => {
      req.destroy();
      reject(new Error('Request timeout'));
    });
  });
}

/**
 * اختبار MongoDB
 */
async function testMongoConnection() {
  try {
    console.log('🔍 Testing MongoDB connection...');
    await mongoose.connect(CONFIG.database.uri, {
      maxPoolSize: 2,
      serverSelectionTimeoutMS: 10000,
      socketTimeoutMS: 10000
    });
    
    // اختبار بسيط للقراءة
    const db = mongoose.connection.db;
    const collections = await db.listCollections().toArray();
    
    await mongoose.disconnect();
    
    return {
      success: true,
      collections: collections.length,
      database: db.databaseName
    };
  } catch (error) {
    return {
      success: false,
      error: error.message
    };
  }
}

/**
 * تشغيل جميع الاختبارات
 */
async function runHealthCheck() {
  console.log('🚀 Starting Production Health Check...\n');
  
  const results = {
    backend: { status: 'unknown' },
    frontend: { status: 'unknown' },
    database: { status: 'unknown' }
  };
  
  // اختبار Backend Health
  try {
    const healthResponse = await testHttpEndpoint(CONFIG.backend.url, CONFIG.backend.healthEndpoint);
    results.backend = {
      status: healthResponse.status === 200 ? 'healthy' : 'unhealthy',
      httpStatus: healthResponse.status,
      response: JSON.parse(healthResponse.data)
    };
    console.log('✅ Backend Health:', results.backend.status);
  } catch (error) {
    results.backend = {
      status: 'error',
      error: error.message
    };
    console.log('❌ Backend Health:', error.message);
  }
  
  // اختبار Backend API
  try {
    const apiResponse = await testHttpEndpoint(CONFIG.backend.url, CONFIG.backend.apiEndpoint);
    results.backendAPI = {
      status: apiResponse.status === 200 ? 'working' : 'failed',
      httpStatus: apiResponse.status
    };
    console.log('✅ Backend API:', results.backendAPI.status);
  } catch (error) {
    results.backendAPI = {
      status: 'error',
      error: error.message
    };
    console.log('❌ Backend API:', error.message);
  }
  
  // اختبار Frontend
  try {
    const frontendResponse = await testHttpEndpoint(CONFIG.frontend.url);
    results.frontend = {
      status: frontendResponse.status === 200 ? 'accessible' : 'inaccessible',
      httpStatus: frontendResponse.status
    };
    console.log('✅ Frontend:', results.frontend.status);
  } catch (error) {
    results.frontend = {
      status: 'error',
      error: error.message
    };
    console.log('❌ Frontend:', error.message);
  }
  
  // اختبار Database
  const dbResult = await testMongoConnection();
  results.database = dbResult;
  console.log(dbResult.success ? '✅ Database: connected' : '❌ Database: ' + dbResult.error);
  
  // ملخص النتائج
  console.log('\n📊 Health Check Summary:');
  console.log('========================');
  console.log(`Backend Health: ${results.backend.status}`);
  console.log(`Backend API: ${results.backendAPI?.status || 'not tested'}`);
  console.log(`Frontend: ${results.frontend.status}`);
  console.log(`Database: ${results.database.success ? 'connected' : 'failed'}`);
  
  if (results.database.success) {
    console.log(`Database Collections: ${results.database.collections}`);
  }
  
  // رموز العودة
  const allHealthy = (
    results.backend.status === 'healthy' &&
    results.backendAPI?.status === 'working' &&
    results.frontend.status === 'accessible' &&
    results.database.success
  );
  
  console.log(`\n${allHealthy ? '🎉 All systems operational!' : '⚠️ Some systems need attention'}`);
  
  return results;
}

// تشغيل الاختبار
if (require.main === module) {
  runHealthCheck()
    .then(results => {
      const hasErrors = Object.values(results).some(result => 
        result.status === 'error' || result.status === 'failed' || result.success === false
      );
      process.exit(hasErrors ? 1 : 0);
    })
    .catch(error => {
      console.error('❌ Health check failed:', error);
      process.exit(1);
    });
}

module.exports = { runHealthCheck, testHttpEndpoint, testMongoConnection };

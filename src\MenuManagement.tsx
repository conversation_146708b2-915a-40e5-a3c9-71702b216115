import React, { useState, useEffect } from 'react';
import { useToast } from './hooks/useToast';
import { ToastContainer } from './components/Toast';
import Button from './components/Button';
import Modal from './components/Modal';
import { getApiUrl } from './config/app.config';
import { authenticatedGet, authenticatedPost, authenticatedPut, authenticatedDelete, handleApiError } from './utils/apiHelpers';

interface Category {
  _id: string;
  name: string;
  description?: string;
  active: boolean;
  order: number;
  color: string;
}

interface MenuItem {
  _id?: string;
  name: string;
  price: number;
  category?: string; // ObjectId reference
  categories?: string[]; // Array of category IDs for backward compatibility
  categoryName?: string;
  categoryDetails?: Category[];
  description?: string;
  available: boolean;
  featured?: boolean;
  image?: string;
  images?: string[];
  ingredients?: string[];
  allergens?: string[];
  preparationTime?: number;
  tags?: string[];
  order?: number;
  stock?: {
    quantity: number;
    unit: string;
    lowStockAlert: number;
  };
  nutritionInfo?: {
    calories?: number;
    protein?: number;
    carbs?: number;
    fat?: number;
    sugar?: number;
  };
  rating?: {
    average: number;
    count: number;
  };
  sales?: {
    totalSold: number;
    revenue: number;
  };
  status?: 'active' | 'inactive' | 'deleted';
}

export default function MenuManagement() {
  const [menuItems, setMenuItems] = useState<MenuItem[]>([]);
  const [categories, setCategories] = useState<Category[]>([]);
  const [loading, setLoading] = useState(false);
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [editingItem, setEditingItem] = useState<MenuItem | null>(null);
  const [isDetailsModalOpen, setIsDetailsModalOpen] = useState(false);
  const [viewingItem, setViewingItem] = useState<MenuItem | null>(null);
  const [formData, setFormData] = useState<MenuItem>({
    name: '',
    price: 0,
    category: '',
    categories: [],
    description: '',
    available: true,
    featured: false,
    image: '',
    preparationTime: 10,
    stock: {
      quantity: 100,
      unit: 'piece',
      lowStockAlert: 5
    },
    order: 0
  });
  const { toasts, removeToast, showSuccess, showError } = useToast();

  // جلب قائمة المشروبات والفئات من السيرفر
  useEffect(() => {
    fetchMenuItems();
    fetchCategories();
  }, []);

  const fetchMenuItems = async () => {
    try {
      setLoading(true);
      const response = await authenticatedGet('/api/v1/products');
      const data = response?.data || response;
      const menuItems = Array.isArray(data) ? data : [];
      setMenuItems(menuItems.sort((a: MenuItem, b: MenuItem) => (a.order || 0) - (b.order || 0)));
    } catch (error) {
      console.error('Error fetching menu items:', error);
      const errorMessage = handleApiError(error);
      showError(errorMessage);
    } finally {
      setLoading(false);
    }
  };

  const fetchCategories = async () => {
    try {
      const response = await authenticatedGet('/api/v1/categories');
      const data = response?.data || response;
      const categories = Array.isArray(data) ? data : [];
      setCategories(categories.filter((cat: Category) => cat.active));
    } catch (error) {
      console.error('Error fetching categories:', error);
      const errorMessage = handleApiError(error);
      showError(errorMessage);
    }
  };

  // فتح نافذة إضافة/تعديل
  const openModal = (item?: MenuItem) => {
    if (item) {
      setEditingItem(item);
      setFormData({
        ...item,
        category: item.category || '',
        categories: item.categories || [],
        stock: item.stock || { quantity: 100, unit: 'piece', lowStockAlert: 5 },
        featured: item.featured || false,
        image: item.image || '',
        preparationTime: item.preparationTime || 10
      });
    } else {
      setEditingItem(null);
      setFormData({
        name: '',
        price: 0,
        category: '',
        categories: [],
        description: '',
        available: true,
        featured: false,
        image: '',
        preparationTime: 10,
        stock: {
          quantity: 100,
          unit: 'piece',
          lowStockAlert: 5
        },
        order: menuItems.length
      });
    }
    setIsModalOpen(true);
  };

  // إغلاق النافذة
  const closeModal = () => {
    setIsModalOpen(false);
    setEditingItem(null);
    setFormData({
      name: '',
      price: 0,
      category: '',
      categories: [],
      description: '',
      available: true,
      featured: false,
      image: '',
      preparationTime: 10,
      stock: {
        quantity: 100,
        unit: 'piece',
        lowStockAlert: 5
      },
      order: 0
    });
  };

  // فتح نافذة التفاصيل
  const openDetailsModal = (item: MenuItem) => {
    setViewingItem(item);
    setIsDetailsModalOpen(true);
  };

  // إغلاق نافذة التفاصيل
  const closeDetailsModal = () => {
    setIsDetailsModalOpen(false);
    setViewingItem(null);
  };

  // حفظ العنصر
  const handleSave = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!formData.name || formData.price <= 0) {
      showError('يرجى ملء جميع الحقول المطلوبة');
      return;
    }

    // Validate category selection - now supporting single category like backend expects
    if (!formData.category && (!formData.categories || formData.categories.length === 0)) {
      showError('يرجى اختيار فئة واحدة على الأقل');
      return;
    }

    try {
      setLoading(true);
      
      // Prepare data for backend (convert to backend format)
      const backendData = {
        name: formData.name,
        description: formData.description || '',
        price: formData.price,
        // Use single category (take first one if multiple selected)
        category: formData.category || (formData.categories && formData.categories[0]) || '',
        categoryName: categories.find(cat => 
          cat._id === (formData.category || (formData.categories && formData.categories[0]))
        )?.name || '',
        available: formData.available,
        featured: formData.featured || false,
        image: formData.image || '/images/default-product.jpg',
        preparationTime: formData.preparationTime || 10,
        stock: formData.stock || { quantity: 100, unit: 'piece', lowStockAlert: 5 }
      };
      
      if (editingItem) {
        await authenticatedPut(`/api/v1/products/${editingItem._id}`, backendData);
        showSuccess('تم تحديث العنصر بنجاح');
      } else {
        await authenticatedPost('/api/v1/products', backendData);
        showSuccess('تم إضافة العنصر بنجاح');
      }
      
      closeModal();
      fetchMenuItems();
    } catch (error) {
      console.error('Error saving menu item:', error);
      const errorMessage = handleApiError(error);
      showError(errorMessage);
    } finally {
      setLoading(false);
    }
  };

  // حذف عنصر
  const handleDelete = async (id: string) => {
    if (!confirm('هل أنت متأكد من حذف هذا العنصر؟')) return;

    try {
      setLoading(true);
      await authenticatedDelete(`/api/v1/products/${id}`);
      showSuccess('تم حذف العنصر بنجاح');
      fetchMenuItems();
    } catch (error) {
      console.error('Error deleting menu item:', error);
      const errorMessage = handleApiError(error);
      showError(errorMessage);
    } finally {
      setLoading(false);
    }
  };

  // تغيير حالة التوفر
  const toggleAvailability = async (item: MenuItem) => {
    try {
      // Use PATCH request for toggle operation
      const response = await fetch(`${getApiUrl(`/api/v1/products/${item._id}/toggle-availability`)}`, {
        method: 'PATCH',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${localStorage.getItem('token')}`
        }
      });
      
      if (!response.ok) {
        throw new Error('فشل في تغيير حالة المنتج');
      }
      
      showSuccess(`تم ${!item.available ? 'تفعيل' : 'إلغاء تفعيل'} العنصر`);
      fetchMenuItems();
    } catch (error) {
      console.error('Error toggling availability:', error);
      const errorMessage = handleApiError(error);
      showError(errorMessage);
    }
  };

  // إصلاح حالة توفر جميع المنتجات
  const fixProductsAvailability = async () => {
    if (!window.confirm('هل تريد إصلاح حالة توفر جميع المنتجات؟ سيتم تحديث المخزون وجعل المنتجات متوفرة.')) {
      return;
    }

    try {
      setLoading(true);
      const response = await authenticatedPost('/api/v1/products/fix-availability', {});
      
      if (response.success) {
        showSuccess(`${response.message} - تم إصلاح ${response.data.fixedProducts} من ${response.data.totalProducts} منتج`);
        fetchMenuItems(); // Refresh the list
      } else {
        showError('فشل في إصلاح المنتجات');
      }
    } catch (error) {
      console.error('Error fixing products availability:', error);
      const errorMessage = handleApiError(error);
      showError(errorMessage);
    } finally {
      setLoading(false);
    }
  };

  // تجميع المشروبات حسب الفئة (مع دعم الفئة الواحدة الجديدة)
  const groupedItems = menuItems.reduce((groups, item) => {
    // Support both old multiple categories and new single category structure
    let categoryName = 'غير مصنف';
    
    if (item.categoryDetails && item.categoryDetails.length > 0) {
      // Old structure with multiple categories
      item.categoryDetails.forEach(category => {
        if (!groups[category.name]) {
          groups[category.name] = [];
        }
        groups[category.name].push(item);
      });
      return groups;
    } else if (item.categoryName) {
      // New structure with single category
      categoryName = item.categoryName;
    }
    
    if (!groups[categoryName]) {
      groups[categoryName] = [];
    }
    groups[categoryName].push(item);
    return groups;
  }, {} as Record<string, MenuItem[]>);

  return (
    <div style={{ direction: 'rtl', padding: '1rem' }}>
      <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: '2rem' }}>
        <h2 style={{ margin: 0, color: '#6d4c41' }}>إدارة قائمة المشروبات</h2>
        <div style={{ display: 'flex', gap: '1rem' }}>
          <Button
            onClick={fixProductsAvailability}
            variant="secondary"
            disabled={loading}
          >
            <i className="fas fa-tools" style={{ marginLeft: '0.5rem' }}></i>
            إصلاح التوفر
          </Button>
          <Button
            onClick={() => openModal()}
            variant="primary"
            disabled={loading}
          >
            <i className="fas fa-plus" style={{ marginLeft: '0.5rem' }}></i>
            إضافة عنصر جديد
          </Button>
        </div>
      </div>

      {loading && <div style={{ textAlign: 'center', padding: '2rem' }}>جاري التحميل...</div>}

      {Object.keys(groupedItems).map(category => (
        <div key={category} style={{ marginBottom: '2rem' }}>
          <h3 style={{
            background: '#f5f5f5',
            padding: '0.75rem 1rem',
            margin: '0 0 1rem 0',
            borderRadius: '8px',
            color: '#6d4c41'
          }}>
            {category} ({groupedItems[category].length} عنصر)
          </h3>

          <div style={{
            display: 'grid',
            gridTemplateColumns: 'repeat(auto-fill, minmax(300px, 1fr))',
            gap: '1rem'
          }}>
            {groupedItems[category].map(item => (
              <div key={item._id} style={{
                background: '#fff',
                border: '1px solid #ddd',
                borderRadius: '8px',
                padding: '1rem',
                boxShadow: '0 2px 4px rgba(0,0,0,0.1)',
                opacity: item.available ? 1 : 0.6
              }}>
                <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'flex-start' }}>
                  <div style={{ flex: 1 }}>
                    <h4 style={{ margin: '0 0 0.5rem 0', color: '#333' }}>{item.name}</h4>
                    <p style={{ margin: '0 0 0.5rem 0', color: '#666', fontSize: '0.9rem' }}>
                      {item.description || 'لا يوجد وصف'}
                    </p>

                    {/* عرض الفئات */}
                    {item.categoryDetails && item.categoryDetails.length > 0 && (
                      <div style={{ margin: '0.5rem 0', display: 'flex', flexWrap: 'wrap', gap: '0.25rem' }}>
                        {item.categoryDetails.map(category => (
                          <span
                            key={category._id}
                            style={{
                              display: 'inline-flex',
                              alignItems: 'center',
                              gap: '0.25rem',
                              background: `${category.color}20`,
                              color: category.color,
                              padding: '0.25rem 0.5rem',
                              borderRadius: '12px',
                              fontSize: '0.75rem',
                              fontWeight: '500',
                              border: `1px solid ${category.color}40`
                            }}
                          >
                            <div style={{
                              width: '6px',
                              height: '6px',
                              borderRadius: '50%',
                              background: category.color
                            }} />
                            {category.name}
                          </span>
                        ))}
                      </div>
                    )}

                    <div style={{ display: 'flex', alignItems: 'center', gap: '1rem' }}>
                      <span style={{ fontWeight: 'bold', color: '#795548' }}>
                        {item.price} جنيه
                      </span>
                      <span style={{
                        fontSize: '0.8rem',
                        color: item.available ? '#4caf50' : '#f44336',
                        fontWeight: 'bold'
                      }}>
                        {item.available ? '✓ متوفر' : '✗ غير متوفر'}
                      </span>
                    </div>
                  </div>

                  <div style={{ display: 'flex', flexDirection: 'column', gap: '0.5rem' }}>
                    <Button
                      onClick={() => openDetailsModal(item)}
                      variant="primary"
                      size="sm"
                    >
                      تفاصيل
                    </Button>
                    <Button
                      onClick={() => openModal(item)}
                      variant="secondary"
                      size="sm"
                    >
                      تعديل
                    </Button>
                    <Button
                      onClick={() => toggleAvailability(item)}
                      variant={item.available ? "error" : "success"}
                      size="sm"
                    >
                      {item.available ? 'إلغاء' : 'تفعيل'}
                    </Button>
                    <Button
                      onClick={() => handleDelete(item._id!)}
                      variant="error"
                      size="sm"
                    >
                      حذف
                    </Button>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>
      ))}

      {menuItems.length === 0 && !loading && (
        <div style={{
          textAlign: 'center',
          padding: '3rem',
          color: '#666',
          background: '#f9f9f9',
          borderRadius: '8px'
        }}>
          <i className="fas fa-coffee" style={{ fontSize: '3rem', marginBottom: '1rem', color: '#ccc' }}></i>
          <p>لا توجد عناصر في القائمة</p>
          <Button onClick={() => openModal()} variant="primary">
            إضافة أول عنصر
          </Button>
        </div>
      )}

      {/* نافذة عرض التفاصيل */}
      <Modal
        isOpen={isDetailsModalOpen}
        onClose={closeDetailsModal}
        title={`تفاصيل ${viewingItem?.name || 'المنتج'}`}
      >
        {viewingItem && (
          <div style={{ display: 'flex', flexDirection: 'column', gap: '1.5rem' }}>
            {/* معلومات أساسية */}
            <div style={{ 
              background: '#f8f9fa', 
              padding: '1rem', 
              borderRadius: '8px',
              border: '1px solid #e9ecef'
            }}>
              <h4 style={{ margin: '0 0 1rem 0', color: '#495057', borderBottom: '2px solid #dee2e6', paddingBottom: '0.5rem' }}>
                المعلومات الأساسية
              </h4>
              <div style={{ display: 'grid', gridTemplateColumns: '1fr 1fr', gap: '1rem' }}>
                <div>
                  <strong style={{ color: '#6f42c1' }}>اسم المنتج:</strong>
                  <p style={{ margin: '0.25rem 0 0 0', color: '#212529' }}>{viewingItem.name}</p>
                </div>
                <div>
                  <strong style={{ color: '#6f42c1' }}>السعر:</strong>
                  <p style={{ margin: '0.25rem 0 0 0', color: '#212529', fontWeight: 'bold' }}>{viewingItem.price} جنيه</p>
                </div>
                <div>
                  <strong style={{ color: '#6f42c1' }}>الفئة:</strong>
                  <p style={{ margin: '0.25rem 0 0 0', color: '#212529' }}>
                    {viewingItem.categoryName || viewingItem.categoryDetails?.[0]?.name || 'غير محدد'}
                  </p>
                </div>
                <div>
                  <strong style={{ color: '#6f42c1' }}>الحالة:</strong>
                  <p style={{ 
                    margin: '0.25rem 0 0 0', 
                    color: viewingItem.available ? '#28a745' : '#dc3545',
                    fontWeight: 'bold'
                  }}>
                    {viewingItem.available ? '✓ متوفر' : '✗ غير متوفر'}
                  </p>
                </div>
              </div>
            </div>

            {/* الوصف */}
            {viewingItem.description && (
              <div style={{ 
                background: '#f8f9fa', 
                padding: '1rem', 
                borderRadius: '8px',
                border: '1px solid #e9ecef'
              }}>
                <h4 style={{ margin: '0 0 1rem 0', color: '#495057', borderBottom: '2px solid #dee2e6', paddingBottom: '0.5rem' }}>
                  الوصف
                </h4>
                <p style={{ margin: 0, color: '#212529', lineHeight: '1.6' }}>{viewingItem.description}</p>
              </div>
            )}

            {/* معلومات التحضير والمخزون */}
            <div style={{ 
              background: '#f8f9fa', 
              padding: '1rem', 
              borderRadius: '8px',
              border: '1px solid #e9ecef'
            }}>
              <h4 style={{ margin: '0 0 1rem 0', color: '#495057', borderBottom: '2px solid #dee2e6', paddingBottom: '0.5rem' }}>
                تفاصيل إضافية
              </h4>
              <div style={{ display: 'grid', gridTemplateColumns: '1fr 1fr', gap: '1rem' }}>
                <div>
                  <strong style={{ color: '#6f42c1' }}>وقت التحضير:</strong>
                  <p style={{ margin: '0.25rem 0 0 0', color: '#212529' }}>
                    {viewingItem.preparationTime || 10} دقيقة
                  </p>
                </div>
                <div>
                  <strong style={{ color: '#6f42c1' }}>ترتيب العرض:</strong>
                  <p style={{ margin: '0.25rem 0 0 0', color: '#212529' }}>
                    {viewingItem.order || 0}
                  </p>
                </div>
                {viewingItem.stock && (
                  <>
                    <div>
                      <strong style={{ color: '#6f42c1' }}>الكمية المتاحة:</strong>
                      <p style={{ margin: '0.25rem 0 0 0', color: '#212529' }}>
                        {viewingItem.stock.quantity} {viewingItem.stock.unit}
                      </p>
                    </div>
                    <div>
                      <strong style={{ color: '#6f42c1' }}>تنبيه المخزون المنخفض:</strong>
                      <p style={{ margin: '0.25rem 0 0 0', color: '#212529' }}>
                        {viewingItem.stock.lowStockAlert} {viewingItem.stock.unit}
                      </p>
                    </div>
                  </>
                )}
              </div>
            </div>

            {/* الفئات (للمنتجات القديمة ذات الفئات المتعددة) */}
            {viewingItem.categoryDetails && viewingItem.categoryDetails.length > 0 && (
              <div style={{ 
                background: '#f8f9fa', 
                padding: '1rem', 
                borderRadius: '8px',
                border: '1px solid #e9ecef'
              }}>
                <h4 style={{ margin: '0 0 1rem 0', color: '#495057', borderBottom: '2px solid #dee2e6', paddingBottom: '0.5rem' }}>
                  الفئات
                </h4>
                <div style={{ display: 'flex', flexWrap: 'wrap', gap: '0.5rem' }}>
                  {viewingItem.categoryDetails.map(category => (
                    <span
                      key={category._id}
                      style={{
                        display: 'inline-flex',
                        alignItems: 'center',
                        gap: '0.5rem',
                        background: `${category.color}20`,
                        color: category.color,
                        padding: '0.5rem 1rem',
                        borderRadius: '20px',
                        fontSize: '0.9rem',
                        fontWeight: '500',
                        border: `2px solid ${category.color}40`
                      }}
                    >
                      <div style={{
                        width: '10px',
                        height: '10px',
                        borderRadius: '50%',
                        background: category.color
                      }} />
                      {category.name}
                    </span>
                  ))}
                </div>
              </div>
            )}

            {/* معلومات التقييم والمبيعات إذا توفرت */}
            {(viewingItem.rating || viewingItem.sales) && (
              <div style={{ 
                background: '#f8f9fa', 
                padding: '1rem', 
                borderRadius: '8px',
                border: '1px solid #e9ecef'
              }}>
                <h4 style={{ margin: '0 0 1rem 0', color: '#495057', borderBottom: '2px solid #dee2e6', paddingBottom: '0.5rem' }}>
                  الإحصائيات
                </h4>
                <div style={{ display: 'grid', gridTemplateColumns: '1fr 1fr', gap: '1rem' }}>
                  {viewingItem.rating && (
                    <div>
                      <strong style={{ color: '#6f42c1' }}>التقييم:</strong>
                      <p style={{ margin: '0.25rem 0 0 0', color: '#212529' }}>
                        ⭐ {viewingItem.rating.average}/5 ({viewingItem.rating.count} تقييم)
                      </p>
                    </div>
                  )}
                  {viewingItem.sales && (
                    <div>
                      <strong style={{ color: '#6f42c1' }}>المبيعات:</strong>
                      <p style={{ margin: '0.25rem 0 0 0', color: '#212529' }}>
                        {viewingItem.sales.totalSold} مبيعة - {viewingItem.sales.revenue} جنيه
                      </p>
                    </div>
                  )}
                </div>
              </div>
            )}

            {/* أزرار الإجراءات */}
            <div style={{ 
              display: 'flex', 
              gap: '1rem', 
              justifyContent: 'flex-end', 
              borderTop: '1px solid #dee2e6',
              paddingTop: '1rem'
            }}>
              <Button
                onClick={() => {
                  closeDetailsModal();
                  openModal(viewingItem);
                }}
                variant="primary"
              >
                <i className="fas fa-edit" style={{ marginLeft: '0.5rem' }}></i>
                تعديل المنتج
              </Button>
              <Button
                onClick={closeDetailsModal}
                variant="secondary"
              >
                إغلاق
              </Button>
            </div>
          </div>
        )}
      </Modal>

      {/* نافذة إضافة/تعديل */}
      <Modal
        isOpen={isModalOpen}
        onClose={closeModal}
        title={editingItem ? 'تعديل عنصر القائمة' : 'إضافة عنصر جديد'}
      >
        <form onSubmit={handleSave} style={{ display: 'flex', flexDirection: 'column', gap: '1rem' }}>
          <div>
            <label style={{ display: 'block', marginBottom: '0.5rem', fontWeight: 'bold' }}>
              اسم المشروب *
            </label>
            <input
              type="text"
              value={formData.name}
              onChange={(e) => setFormData({ ...formData, name: e.target.value })}
              required
              style={{
                width: '100%',
                padding: '0.75rem',
                border: '1px solid #ddd',
                borderRadius: '4px',
                fontSize: '1rem'
              }}
              placeholder="مثال: قهوة تركية"
            />
          </div>

          <div>
            <label style={{ display: 'block', marginBottom: '0.5rem', fontWeight: 'bold' }}>
              السعر (جنيه) *
            </label>
            <input
              type="number"
              min="0"
              step="0.5"
              value={formData.price}
              onChange={(e) => setFormData({ ...formData, price: parseFloat(e.target.value) || 0 })}
              required
              style={{
                width: '100%',
                padding: '0.75rem',
                border: '1px solid #ddd',
                borderRadius: '4px',
                fontSize: '1rem'
              }}
              placeholder="25.00"
            />
          </div>

          <div>
            <label style={{ display: 'block', marginBottom: '0.5rem', fontWeight: 'bold' }}>
              الفئة *
            </label>
            <select
              value={formData.category || ''}
              onChange={(e) => setFormData({ ...formData, category: e.target.value })}
              required
              style={{
                width: '100%',
                padding: '0.75rem',
                border: '1px solid #ddd',
                borderRadius: '4px',
                fontSize: '1rem',
                background: 'white'
              }}
            >
              <option value="">اختر فئة...</option>
              {categories.map(category => (
                <option key={category._id} value={category._id}>
                  {category.name}
                </option>
              ))}
            </select>
            {categories.length === 0 && (
              <div style={{ marginTop: '0.5rem', fontSize: '0.9rem', color: '#f44336' }}>
                لا توجد فئات متاحة. يرجى إضافة فئات أولاً.
              </div>
            )}
          </div>

          <div>
            <label style={{ display: 'block', marginBottom: '0.5rem', fontWeight: 'bold' }}>
              الوصف
            </label>
            <textarea
              value={formData.description}
              onChange={(e) => setFormData({ ...formData, description: e.target.value })}
              rows={3}
              style={{
                width: '100%',
                padding: '0.75rem',
                border: '1px solid #ddd',
                borderRadius: '4px',
                fontSize: '1rem',
                resize: 'vertical'
              }}
              placeholder="وصف المشروب (اختياري)"
            />
          </div>

          <div>
            <label style={{ display: 'block', marginBottom: '0.5rem', fontWeight: 'bold' }}>
              ترتيب العرض
            </label>
            <input
              type="number"
              min="0"
              value={formData.order}
              onChange={(e) => setFormData({ ...formData, order: parseInt(e.target.value) || 0 })}
              style={{
                width: '100%',
                padding: '0.75rem',
                border: '1px solid #ddd',
                borderRadius: '4px',
                fontSize: '1rem'
              }}
              placeholder="0"
            />
          </div>

          <div style={{ display: 'flex', alignItems: 'center', gap: '0.5rem' }}>
            <input
              type="checkbox"
              id="available"
              checked={formData.available}
              onChange={(e) => setFormData({ ...formData, available: e.target.checked })}
            />
            <label htmlFor="available" style={{ fontWeight: 'bold' }}>
              متوفر للطلب
            </label>
          </div>

          <div style={{ display: 'flex', gap: '1rem', justifyContent: 'flex-end', marginTop: '1rem' }}>
            <Button
              type="button"
              onClick={closeModal}
              variant="secondary"
            >
              إلغاء
            </Button>
            <Button
              type="submit"
              variant="primary"
              disabled={loading}
            >
              {loading ? 'جاري الحفظ...' : (editingItem ? 'تحديث' : 'إضافة')}
            </Button>
          </div>
        </form>
      </Modal>

      <ToastContainer toasts={toasts} onRemove={removeToast} />
    </div>
  );
}

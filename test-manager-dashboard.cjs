const fetch = require('node-fetch');

const backEndURL = 'https://deshacoffee-production.up.railway.app';

async function testManagerDashboard() {
  try {
    console.log('🔐 تسجيل الدخول...');
    
    // تسجيل الدخول بحساب المدير
    const loginResponse = await fetch(`${backEndURL}/api/v1/auth/login`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ username: 'Be<PERSON>', password: 'MOHAMEDmostafa123' })
    });
    
    const loginResult = await loginResponse.json();
    const token = loginResult.token;
    console.log('✅ تم تسجيل الدخول بنجاح');
    
    // جلب جميع البيانات المطلوبة للوحة المدير
    console.log('\n📋 جلب البيانات للوحة المدير...');
    
    // 1. جلب جميع الطلبات (بدون limit)
    const ordersResponse = await fetch(`${backEndURL}/api/v1/orders?limit=999999`, {
      headers: { 'Authorization': `Bearer ${token}` }
    });
    const ordersData = await ordersResponse.json();
    let allOrders = [];
    if (Array.isArray(ordersData)) {
      allOrders = ordersData;
    } else if (ordersData.success && Array.isArray(ordersData.data)) {
      allOrders = ordersData.data;
    }
    
    // 2. جلب جميع المستخدمين
    const usersResponse = await fetch(`${backEndURL}/api/v1/users`, {
      headers: { 'Authorization': `Bearer ${token}` }
    });
    const usersData = await usersResponse.json();
    let allUsers = [];
    if (Array.isArray(usersData)) {
      allUsers = usersData;
    } else if (usersData.success && Array.isArray(usersData.data)) {
      allUsers = usersData.data;
    } else if (usersData.users) {
      allUsers = usersData.users;
    }
    
    console.log(`📊 تم جلب ${allOrders.length} طلب و ${allUsers.length} مستخدم`);
    
    // حساب الإحصائيات مثل الـ frontend
    const waiters = allUsers.filter(user => user.role === 'waiter');
    console.log(`👥 عدد النادلين: ${waiters.length}`);
    
    // حساب مبيعات كل نادل
    const waiterStats = waiters.map(waiter => {
      const waiterOrders = allOrders.filter(order => {
        return (
          order.waiterName === waiter.username || 
          order.waiterName === waiter.name ||
          order.waiterId === waiter._id ||
          (order.staff && order.staff.waiter === waiter._id)
        );
      });
      
      const totalOrders = waiterOrders.length;
      const totalSales = waiterOrders.reduce((sum, order) => {
        return sum + (order.totals?.total || order.totalPrice || order.totalAmount || 0);
      }, 0);
      
      const pendingOrders = waiterOrders.filter(order => order.status === 'pending').length;
      const completedOrders = waiterOrders.filter(order => 
        order.status === 'completed' || order.status === 'delivered'
      ).length;
      
      return {
        waiterId: waiter._id,
        waiterName: waiter.name || waiter.username,
        totalOrders,
        totalSales,
        pendingOrders,
        completedOrders
      };
    }).sort((a, b) => b.totalSales - a.totalSales);
    
    // عرض النتائج
    console.log('\n📊 إحصائيات النادلين (مرتبة حسب المبيعات):');
    waiterStats.forEach(waiter => {
      console.log(`👤 ${waiter.waiterName}:`);
      console.log(`   - إجمالي الطلبات: ${waiter.totalOrders}`);
      console.log(`   - إجمالي المبيعات: ${waiter.totalSales.toFixed(2)} جنيه`);
      console.log(`   - طلبات معلقة: ${waiter.pendingOrders}`);
      console.log(`   - طلبات مكتملة: ${waiter.completedOrders}`);
      console.log(`   ---`);
    });
    
    // حساب الإحصائيات الإجمالية
    const totalOrders = allOrders.length;
    const totalSales = allOrders.reduce((sum, order) => {
      return sum + (order.totals?.total || order.totalPrice || order.totalAmount || 0);
    }, 0);
    
    console.log('\n📈 الإحصائيات الإجمالية:');
    console.log(`📋 إجمالي الطلبات: ${totalOrders}`);
    console.log(`💰 إجمالي المبيعات: ${totalSales.toFixed(2)} جنيه`);
    console.log(`👥 عدد النادلين النشطين: ${waiters.length}`);
    
    // التحقق من صحة البيانات
    const waiterSalesSum = waiterStats.reduce((sum, waiter) => sum + waiter.totalSales, 0);
    console.log(`🔍 مجموع مبيعات النادلين: ${waiterSalesSum.toFixed(2)} جنيه`);
    
    if (Math.abs(waiterSalesSum - totalSales) < 1) {
      console.log('✅ البيانات متطابقة - حساب المبيعات صحيح!');
    } else {
      console.log('⚠️ هناك فرق في الحسابات:');
      console.log(`   - إجمالي المبيعات: ${totalSales.toFixed(2)}`);
      console.log(`   - مجموع مبيعات النادلين: ${waiterSalesSum.toFixed(2)}`);
      console.log(`   - الفرق: ${(totalSales - waiterSalesSum).toFixed(2)}`);
    }
    
  } catch (error) {
    console.error('❌ خطأ:', error);
  }
}

testManagerDashboard();

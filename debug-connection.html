<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>تشخيص الاتصال - نظام المقهى</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            padding: 20px;
            direction: rtl;
            text-align: right;
        }
        .test-section {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .result {
            margin: 10px 0;
            padding: 10px;
            border-radius: 3px;
        }
        .success {
            background-color: #d4edda;
            border: 1px solid #c3e6cb;
        }
        .error {
            background-color: #f8d7da;
            border: 1px solid #f5c6cb;
        }
        button {
            padding: 10px 20px;
            margin: 5px;
            border: none;
            border-radius: 3px;
            cursor: pointer;
            background-color: #007bff;
            color: white;
        }
        button:hover {
            background-color: #0056b3;
        }
        pre {
            background-color: #f8f9fa;
            padding: 10px;
            border-radius: 3px;
            overflow-x: auto;
        }
    </style>
</head>
<body>
    <h1>تشخيص الاتصال - نظام المقهى</h1>
    
    <div class="test-section">
        <h3>اختبار الاتصال بالخادم</h3>
        <button onclick="testServerConnection()">اختبار الاتصال</button>
        <div id="server-result"></div>
    </div>

    <div class="test-section">
        <h3>اختبار Health Check</h3>
        <button onclick="testHealthCheck()">اختبار Health Check</button>
        <div id="health-result"></div>
    </div>

    <div class="test-section">
        <h3>معلومات البيئة</h3>
        <div id="env-info"></div>
    </div>

    <script>
        // إعدادات الـ API مثل التطبيق الأصلي
        const API_BASE_URL = 'http://localhost:5000';
        const REQUEST_TIMEOUT = 30000;

        function displayResult(elementId, isSuccess, message, data = null) {
            const element = document.getElementById(elementId);
            element.innerHTML = `
                <div class="result ${isSuccess ? 'success' : 'error'}">
                    <strong>${isSuccess ? '✅ نجح' : '❌ فشل'}</strong>: ${message}
                    ${data ? `<pre>${JSON.stringify(data, null, 2)}</pre>` : ''}
                </div>
            `;
        }

        // makeRequest function مثل التطبيق الأصلي
        const makeRequest = async (endpoint, options = {}) => {
            const controller = new AbortController();
            const timeoutId = setTimeout(() => controller.abort(), REQUEST_TIMEOUT);

            try {
                const token = localStorage.getItem('token') || localStorage.getItem('authToken');

                const response = await fetch(`${API_BASE_URL}${endpoint.startsWith('/') ? endpoint : '/' + endpoint}`, {
                    ...options,
                    signal: controller.signal,
                    headers: {
                        'Content-Type': 'application/json',
                        'Accept': 'application/json',
                        'x-request-id': Math.random().toString(36).substring(7),
                        ...(token && { 'Authorization': `Bearer ${token}` }),
                        ...options.headers,
                    },
                });

                clearTimeout(timeoutId);

                if (!response.ok) {
                    let errorMessage = `خطأ HTTP ${response.status}`;
                    try {
                        const errorData = await response.json();
                        errorMessage = errorData.message || errorData.error || errorMessage;
                    } catch (e) {}
                    
                    return {
                        success: false,
                        error: errorMessage,
                        status: response.status
                    };
                }

                const data = await response.json();
                return {
                    success: true,
                    data
                };
            } catch (error) {
                clearTimeout(timeoutId);
                if (error.name === 'AbortError') {
                    return {
                        success: false,
                        error: 'انتهت مهلة الطلب (Timeout)'
                    };
                }
                return {
                    success: false,
                    error: error.message || 'فشل في الاتصال'
                };
            }
        };

        async function testServerConnection() {
            displayResult('server-result', false, 'جاري الاختبار...');
            
            try {
                const response = await fetch('http://localhost:5000', {
                    method: 'GET',
                    headers: {
                        'Accept': 'application/json'
                    }
                });
                
                const isSuccess = response.ok;
                const statusText = response.statusText || 'بدون رسالة';
                
                displayResult('server-result', isSuccess, 
                    `استجاب الخادم بكود ${response.status} (${statusText})`);
            } catch (error) {
                displayResult('server-result', false, 
                    `فشل الاتصال: ${error.message}`);
            }
        }

        async function testHealthCheck() {
            displayResult('health-result', false, 'جاري اختبار Health Check...');
            
            try {
                const result = await makeRequest('/health');
                
                if (result.success) {
                    const healthData = result.data;
                    const isServerHealthy = healthData?.status === 'healthy';
                    const isDatabaseConnected = healthData?.database?.connected === true;
                    
                    displayResult('health-result', true, 
                        `Health Check نجح. الخادم: ${isServerHealthy ? 'صحي' : 'غير صحي'}، قاعدة البيانات: ${isDatabaseConnected ? 'متصلة' : 'غير متصلة'}`, 
                        healthData);
                } else {
                    displayResult('health-result', false, 
                        `Health Check فشل: ${result.error}`, result);
                }
            } catch (error) {
                displayResult('health-result', false, 
                    `خطأ في Health Check: ${error.message}`);
            }
        }

        // إظهار معلومات البيئة
        function showEnvInfo() {
            const envInfo = {
                'User Agent': navigator.userAgent,
                'الرابط الحالي': window.location.href,
                'API Base URL': API_BASE_URL,
                'Local Storage': {
                    'token': localStorage.getItem('token') ? 'موجود' : 'غير موجود',
                    'authToken': localStorage.getItem('authToken') ? 'موجود' : 'غير موجود'
                }
            };
            
            document.getElementById('env-info').innerHTML = `<pre>${JSON.stringify(envInfo, null, 2)}</pre>`;
        }

        // تشغيل الاختبارات عند تحميل الصفحة
        window.onload = function() {
            showEnvInfo();
            testServerConnection();
            setTimeout(() => testHealthCheck(), 1000);
        };
    </script>
</body>
</html>

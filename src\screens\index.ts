/* ====================================
   Waiter Screens Components Export Index
   ==================================== */

// تصدير جميع مكونات شاشات النادل
export { default as WaiterDrinksScreen } from './WaiterDrinksScreen';
export { default as WaiterOrdersScreen } from './WaiterOrdersScreen';
export { default as WaiterTablesScreen } from './WaiterTablesScreen';
export { default as WaiterCartScreen } from './WaiterCartScreen';
export { default as WaiterDiscountsScreen } from './WaiterDiscountsScreen';

// استيراد جميع ملفات CSS في مكان واحد
import '../styles/waiter/index.css';

/* ====================================
   Types Export (إذا كان هناك أنواع مشتركة)
   ==================================== */

// يمكن إضافة تصدير للأنواع المشتركة هنا إذا لزم الأمر
// export type { WaiterScreenProps } from './types';

/* ====================================
   Constants (الثوابت المشتركة)
   ==================================== */

// الشاشات المتاحة
export const WAITER_SCREENS = {
  DRINKS: 'drinks',
  ORDERS: 'orders', 
  TABLES: 'tables',
  CART: 'cart',
  DISCOUNTS: 'discounts'
} as const;

// أنواع الشاشات
export type WaiterScreenType = typeof WAITER_SCREENS[keyof typeof WAITER_SCREENS];

// خريطة عناوين الشاشات
export const WAITER_SCREEN_TITLES = {
  [WAITER_SCREENS.DRINKS]: 'قائمة المشروبات',
  [WAITER_SCREENS.ORDERS]: 'إدارة الطلبات', 
  [WAITER_SCREENS.TABLES]: 'إدارة الطاولات',
  [WAITER_SCREENS.CART]: 'سلة المشتريات',
  [WAITER_SCREENS.DISCOUNTS]: 'طلبات الخصم'
} as const;

// خريطة أيقونات الشاشات
export const WAITER_SCREEN_ICONS = {
  [WAITER_SCREENS.DRINKS]: 'fa-coffee',
  [WAITER_SCREENS.ORDERS]: 'fa-receipt',
  [WAITER_SCREENS.TABLES]: 'fa-table', 
  [WAITER_SCREENS.CART]: 'fa-shopping-cart',
  [WAITER_SCREENS.DISCOUNTS]: 'fa-percentage'
} as const;

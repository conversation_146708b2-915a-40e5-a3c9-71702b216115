const mongoose = require('mongoose');
require('dotenv').config();

async function checkProducts() {
  try {
    console.log('🔌 Connecting to MongoDB Atlas...');
    await mongoose.connect(process.env.MONGODB_URI);
    console.log('✅ Connected successfully!');

    const productsCollection = mongoose.connection.db.collection('products');
    const allProducts = await productsCollection.find().toArray();
    
    console.log(`\n📦 Total products: ${allProducts.length}`);
    
    if (allProducts.length > 0) {
      console.log('\n🍽️ Sample products:');
      allProducts.slice(0, 5).forEach((product, index) => {
        console.log(`${index + 1}. ${product.name} - ${product.price} EGP (Active: ${product.isActive})`);
      });

      // Check active products
      const activeProducts = allProducts.filter(p => p.isActive !== false);
      console.log(`\n✅ Active products: ${activeProducts.length}`);
    } else {
      console.log('❌ No products found in database');
    }

  } catch (error) {
    console.error('❌ Error:', error.message);
  } finally {
    await mongoose.connection.close();
    console.log('\n🔐 Database connection closed.');
  }
}

checkProducts();

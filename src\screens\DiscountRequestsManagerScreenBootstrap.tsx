import React, { useState, useMemo, useEffect } from 'react';
import { authenticatedPut } from '../utils/apiHelpers';
import ResponsiveGrid from '../components/ResponsiveGrid';
import ResponsiveCard from '../components/ResponsiveCard';
import socket from '../socket';
import '../styles/screens/DiscountRequestsScreenIsolated.css';

interface OrderItem {
  _id?: string;
  name?: string;
  productName?: string;
  quantity?: number;
  price?: number;
  size?: string;
  category?: string;
  notes?: string;
  product?: string;
}

interface Order {
  _id: string;
  orderNumber: string;
  items: OrderItem[];
  totalAmount?: number;
  totalPrice?: number;
  totals?: {
    subtotal: number;
    tax: number;
    discount: number;
    total: number;
  };
  status: 'pending' | 'preparing' | 'ready' | 'completed' | 'delivered';
  tableNumber?: number;
  customerName?: string;
  waiterName?: string;
  waiterId?: string;
  chefName?: string;
  staff?: {
    waiter?: string;
    chef?: string;
  };
  createdAt: string;
  updatedAt: string;
}

interface DiscountRequest {
  _id: string;
  orderId: string;
  orderNumber: string;
  waiterName: string;
  amount?: number;
  originalAmount?: number;
  requestedDiscount?: number;
  reason?: string;
  status: 'pending' | 'approved' | 'rejected';
  createdAt: string;
  updatedAt?: string;
  order?: Order;
  formattedAmount?: string;
  formattedPercentage?: string;
  discountPercentage?: string;
  tableNumber?: number;
  approvedBy?: string;
  approvedByName?: string;
  rejectedBy?: string;
  rejectedByName?: string;
  waiterUsername?: string;
  approvedByUsername?: string;
  rejectedByUsername?: string;
}

interface DiscountRequestsManagerScreenBootstrapProps {
  discountRequests: DiscountRequest[];
  managerId: string;
  selectedDiscountForDetails: DiscountRequest | null;
  showDiscountDetailsModal: boolean;
  setSelectedDiscountForDetails: (request: DiscountRequest | null) => void;
  setShowDiscountDetailsModal: (show: boolean) => void;
  fetchDiscountRequests: () => void;
  showSuccess: (message: string) => void;
  showError: (message: string) => void;
}

const DiscountRequestsManagerScreenBootstrap: React.FC<DiscountRequestsManagerScreenBootstrapProps> = ({
  discountRequests,
  managerId,
  selectedDiscountForDetails,
  showDiscountDetailsModal,
  setSelectedDiscountForDetails,
  setShowDiscountDetailsModal,
  fetchDiscountRequests,
  showSuccess,
  showError
}) => {
  
  const [loadingActions, setLoadingActions] = useState<{ [key: string]: boolean }>({});
  const [statusFilter, setStatusFilter] = useState<'all' | 'pending' | 'approved' | 'rejected'>('all');
  const [waiterFilter, setWaiterFilter] = useState('all');
  const [dateFilter, setDateFilter] = useState('today');
  const [searchTerm, setSearchTerm] = useState('');

  // Socket.IO event listeners للتحديثات الفورية
  useEffect(() => {
    const handleDiscountRequestStatus = (discountUpdate: any) => {
      console.log('💰 تحديث طلب الخصم:', discountUpdate);
      fetchDiscountRequests();
    };

    const handleNewDiscountRequest = (newRequest: any) => {
      console.log('💸 طلب خصم جديد:', newRequest);
      fetchDiscountRequests();
    };

    const handleDiscountProcessed = (processedRequest: any) => {
      console.log('✅ تم معالجة طلب الخصم:', processedRequest);
      fetchDiscountRequests();
    };

    // إضافة event listeners
    socket.on('discount-request-status', handleDiscountRequestStatus);
    socket.on('newDiscountRequest', handleNewDiscountRequest);
    socket.on('discountRequestProcessed', handleDiscountProcessed);

    // تنظيف event listeners
    return () => {
      socket.off('discount-request-status', handleDiscountRequestStatus);
      socket.off('newDiscountRequest', handleNewDiscountRequest);
      socket.off('discountRequestProcessed', handleDiscountProcessed);
    };
  }, [fetchDiscountRequests]);

  // دالة مساعدة للتحقق من نفس اليوم
  const isSameDay = (date1: Date, date2: Date) => {
    return date1.getDate() === date2.getDate() &&
           date1.getMonth() === date2.getMonth() &&
           date1.getFullYear() === date2.getFullYear();
  };

  // فلترة الطلبات
  const filteredRequests = useMemo(() => {
    return discountRequests.filter(request => {
      // فلتر الحالة
      if (statusFilter !== 'all' && request.status !== statusFilter) {
        return false;
      }

      // فلتر النادل
      if (waiterFilter !== 'all' && request.waiterName !== waiterFilter) {
        return false;
      }

      // فلتر البحث
      if (searchTerm && !request.orderNumber.toLowerCase().includes(searchTerm.toLowerCase()) 
          && !request.waiterName?.toLowerCase().includes(searchTerm.toLowerCase())
          && !request.reason?.toLowerCase().includes(searchTerm.toLowerCase())) {
        return false;
      }

      // فلتر التاريخ
      if (dateFilter !== 'all') {
        const requestDate = new Date(request.createdAt);
        const today = new Date();
        const yesterday = new Date(today);
        yesterday.setDate(yesterday.getDate() - 1);
        const weekAgo = new Date(today);
        weekAgo.setDate(weekAgo.getDate() - 7);

        switch (dateFilter) {
          case 'today':
            if (!isSameDay(requestDate, today)) return false;
            break;
          case 'yesterday':
            if (!isSameDay(requestDate, yesterday)) return false;
            break;
          case 'week':
            if (requestDate < weekAgo) return false;
            break;
        }
      }

      return true;
    });
  }, [discountRequests, statusFilter, waiterFilter, searchTerm, dateFilter]);

  // قائمة النُدل الفريدة
  const uniqueWaiters = useMemo(() => {
    const waiters = Array.from(new Set(discountRequests.map(r => r.waiterName).filter(Boolean)));
    return waiters.sort();
  }, [discountRequests]);

  // إحصائيات طلبات الخصم المفلترة
  const discountStats = useMemo(() => {
    const requests = filteredRequests;
    return {
      total: requests.length,
      pending: requests.filter(r => r.status === 'pending').length,
      approved: requests.filter(r => r.status === 'approved').length,
      rejected: requests.filter(r => r.status === 'rejected').length,
      totalAmount: requests.reduce((sum, r) => {
        const amount = r.amount != null ? Number(r.amount) : 0;
        return sum + (isNaN(amount) ? 0 : amount);
      }, 0),
      approvedAmount: requests.filter(r => r.status === 'approved').reduce((sum, r) => {
        const amount = r.amount != null ? Number(r.amount) : 0;
        return sum + (isNaN(amount) ? 0 : amount);
      }, 0)
    };
  }, [filteredRequests]);

  // Stats cards data
  const statsCards = [
    {
      title: 'إجمالي الطلبات',
      value: discountStats.total,
      icon: 'fas fa-list',
      color: 'primary',
      bgClass: 'bg-primary',
      description: `من أصل ${discountRequests.length} طلب`
    },
    {
      title: 'قيد الانتظار',
      value: discountStats.pending,
      icon: 'fas fa-clock',
      color: 'warning',
      bgClass: 'bg-warning',
      description: 'يحتاج إجراء'
    },
    {
      title: 'مقبول',
      value: discountStats.approved,
      icon: 'fas fa-check-circle',
      color: 'success',
      bgClass: 'bg-success',
      description: `${discountStats.approvedAmount.toFixed(2)} ج.م`
    },
    {
      title: 'مرفوض',
      value: discountStats.rejected,
      icon: 'fas fa-times-circle',
      color: 'danger',
      bgClass: 'bg-danger',
      description: 'تم رفضه'
    }
  ];

  const handleApproveRequest = async (request: DiscountRequest) => {
    const confirmApproval = window.confirm(`هل أنت متأكد من قبول طلب الخصم للطلب #${request.orderNumber}؟`);
    if (!confirmApproval) return;

    setLoadingActions(prev => ({ ...prev, [request._id]: true }));

    try {
      console.log('Approving discount request:', request._id);
      const response = await authenticatedPut(`/api/v1/discount-requests/${request._id}`, {
        status: 'approved',
        approvedBy: managerId,
        approvedAt: new Date().toISOString()
      });
      
      if (response.success) {
        showSuccess('تم قبول طلب الخصم بنجاح');
        fetchDiscountRequests();
      } else {
        console.error('Failed to approve discount:', response);
        showError('فشل في قبول طلب الخصم');
      }
    } catch (error) {
      console.error('Error approving discount:', error);
      showError('حدث خطأ في قبول طلب الخصم');
    } finally {
      setLoadingActions(prev => ({ ...prev, [request._id]: false }));
    }
  };

  const handleRejectRequest = async (request: DiscountRequest) => {
    const confirmRejection = window.confirm(`هل أنت متأكد من رفض طلب الخصم للطلب #${request.orderNumber}؟`);
    if (!confirmRejection) return;

    setLoadingActions(prev => ({ ...prev, [request._id]: true }));

    try {
      console.log('Rejecting discount request:', request._id);
      const response = await authenticatedPut(`/api/v1/discount-requests/${request._id}`, {
        status: 'rejected',
        rejectedBy: managerId,
        rejectedAt: new Date().toISOString()
      });
      
      if (response.success) {
        showSuccess('تم رفض طلب الخصم بنجاح');
        fetchDiscountRequests();
      } else {
        console.error('Failed to reject discount:', response);
        showError('فشل في رفض طلب الخصم');
      }
    } catch (error) {
      console.error('Error rejecting discount:', error);
      showError('حدث خطأ في رفض طلب الخصم');
    } finally {
      setLoadingActions(prev => ({ ...prev, [request._id]: false }));
    }
  };

  const getAmount = (request: DiscountRequest) => {
    return request.amount != null 
      ? Number(request.amount).toFixed(2) 
      : (request.requestedDiscount != null 
        ? Number(request.requestedDiscount).toFixed(2)
        : (request.formattedAmount || 
          (request.order?.totals?.total && Number(request.order.totals.total).toFixed(2)) ||
          (request.order?.totalAmount && Number(request.order.totalAmount).toFixed(2)) ||
          '0.00'));
  };

  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'pending':
        return { class: 'bg-warning text-dark', icon: 'fas fa-clock', text: 'قيد الانتظار' };
      case 'approved':
        return { class: 'bg-success', icon: 'fas fa-check-circle', text: 'مقبول' };
      case 'rejected':
        return { class: 'bg-danger', icon: 'fas fa-times-circle', text: 'مرفوض' };
      default:
        return { class: 'bg-secondary', icon: 'fas fa-question', text: status };
    }
  };

  return (
    <div className="discount-requests-bootstrap-container container-fluid py-4">
      {/* Header */}
      <div className="text-center mb-5">
        <h1 className="display-4 text-white mb-2">
          <i className="fas fa-percentage me-3"></i>
          طلبات الخصم
        </h1>
        <p className="lead text-white-50">إدارة ومراجعة طلبات الخصم من النُدُل</p>
      </div>

      {/* Statistics */}
      <div className="mb-5">
        <ResponsiveGrid
          cols={{ xs: 1, sm: 2, md: 2, lg: 4, xl: 4, xxl: 4 }}
          gap={3}
        >
          {statsCards.map((stat, index) => (
            <ResponsiveCard key={index} className="discount-requests-stat-card">
              <div className="card-body d-flex align-items-center">
                <div className={`discount-requests-stat-icon me-3 ${stat.bgClass} text-white`}>
                  <i className={stat.icon}></i>
                </div>
                <div className="flex-grow-1">
                  <h3 className="h2 mb-1 fw-bold text-dark">{stat.value}</h3>
                  <p className="mb-0 text-muted fw-medium">{stat.title}</p>
                  {stat.description && (
                    <small className="text-muted d-block">{stat.description}</small>
                  )}
                </div>
              </div>
            </ResponsiveCard>
          ))}
        </ResponsiveGrid>
      </div>

      {/* Filters Section */}
      <div className="mb-4">
        <ResponsiveCard className="discount-requests-filters-card">
          <div className="card-header">
            <h5 className="mb-0">
              <i className="fas fa-filter me-2"></i>
              فلترة وبحث طلبات الخصم
            </h5>
          </div>
          <div className="card-body">
            <div className="row g-3">
              <div className="col-md-3">
                <label className="form-label">البحث</label>
                <div className="input-group">
                  <span className="input-group-text">
                    <i className="fas fa-search"></i>
                  </span>
                  <input
                    type="text"
                    className="form-control"
                    placeholder="رقم الطلب، النادل، أو السبب..."
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                  />
                </div>
              </div>
              <div className="col-md-2">
                <label className="form-label">الحالة</label>
                <select
                  className="form-select"
                  value={statusFilter}
                  onChange={(e) => setStatusFilter(e.target.value as any)}
                  aria-label="فلترة حسب الحالة"
                >
                  <option value="all">جميع الحالات</option>
                  <option value="pending">قيد الانتظار</option>
                  <option value="approved">مقبول</option>
                  <option value="rejected">مرفوض</option>
                </select>
              </div>
              <div className="col-md-3">
                <label className="form-label">النادل</label>
                <select
                  className="form-select"
                  value={waiterFilter}
                  onChange={(e) => setWaiterFilter(e.target.value)}
                  aria-label="فلترة حسب النادل"
                >
                  <option value="all">جميع النُدل</option>
                  {uniqueWaiters.map(waiter => (
                    <option key={waiter} value={waiter}>{waiter}</option>
                  ))}
                </select>
              </div>
              <div className="col-md-2">
                <label className="form-label">التاريخ</label>
                <select
                  className="form-select"
                  value={dateFilter}
                  onChange={(e) => setDateFilter(e.target.value)}
                  aria-label="فلترة حسب التاريخ"
                >
                  <option value="all">جميع التواريخ</option>
                  <option value="today">اليوم</option>
                  <option value="yesterday">أمس</option>
                  <option value="week">هذا الأسبوع</option>
                </select>
              </div>
              <div className="col-md-2 d-flex align-items-end">
                <button
                  className="btn btn-outline-secondary w-100"
                  onClick={() => {
                    setStatusFilter('all');
                    setWaiterFilter('all');
                    setDateFilter('today');
                    setSearchTerm('');
                  }}
                >
                  <i className="fas fa-eraser me-1"></i>
                  مسح
                </button>
              </div>
            </div>
          </div>
        </ResponsiveCard>
      </div>

      {/* Discount Requests Section */}
      <div className="mb-4">
        <h2 className="text-white text-center mb-4">
          <i className="fas fa-percentage me-2"></i>
          طلبات الخصم ({filteredRequests.length} من {discountRequests.length})
        </h2>

        {filteredRequests.length === 0 ? (
          <div className="text-center py-5">
            <ResponsiveCard className="discount-requests-empty-card mx-auto">
              <div className="card-body text-center py-5">
                <div className="mb-4">
                  <i className="fas fa-search display-1 text-muted"></i>
                </div>
                <h3 className="card-title text-dark">
                  {discountRequests.length === 0 ? 'لا توجد طلبات خصم' : 'لا توجد نتائج'}
                </h3>
                <p className="card-text text-muted">
                  {discountRequests.length === 0 
                    ? 'لم يتم إنشاء أي طلبات خصم بعد' 
                    : 'جرب تغيير معايير البحث أو الفلترة'}
                </p>
                {discountRequests.length > 0 && (
                  <button
                    className="btn btn-outline-primary mt-3"
                    onClick={() => {
                      setStatusFilter('all');
                      setWaiterFilter('all');
                      setDateFilter('all');
                      setSearchTerm('');
                    }}
                  >
                    <i className="fas fa-eraser me-1"></i>
                    مسح جميع الفلاتر
                  </button>
                )}
              </div>
            </ResponsiveCard>
          </div>
        ) : (
          <ResponsiveGrid
            cols={{ xs: 1, sm: 1, md: 1, lg: 2, xl: 2, xxl: 3 }}
            gap={4}
          >
            {filteredRequests.map((request) => {
              const statusBadge = getStatusBadge(request.status);
              
              return (
                <ResponsiveCard
                  key={request._id}
                  className={`discount-card-premium discount-status-${request.status}`}
                >
                  {/* Status indicator bar */}
                  <div className={`discount-status-bar ${request.status}`}></div>

                  {/* Floating discount indicator */}
                  <div className="floating-discount-indicator">
                    <i className="fas fa-percentage"></i>
                    <span>{(() => {
                      const original = Number(request.order?.totals?.total || 0);
                      const discount = Number(getAmount(request));
                      const percentage = original > 0 ? ((discount / original) * 100).toFixed(0) : '0';
                      return `${percentage}%`;
                    })()}</span>
                  </div>

                  <div className="discount-card-content">
                    {/* Header */}
                    <div className="discount-card-header">
                      <div className="discount-order-icon">
                        <i className="fas fa-receipt"></i>
                      </div>
                      <h5 className="discount-order-number">طلب #{request.orderNumber}</h5>
                      <span className={`discount-status-badge ${request.status}`}>
                        <i className={`${statusBadge.icon}`}></i>
                        <span>{statusBadge.text}</span>
                      </span>
                      <div className="discount-timestamp">
                        <i className="fas fa-clock"></i>
                        <span>{new Date(request.createdAt).toLocaleDateString('ar-SA', {
                          month: 'short',
                          day: 'numeric',
                          hour: '2-digit',
                          minute: '2-digit'
                        })}</span>
                      </div>
                    </div>

                    {/* Discount amount section */}
                    <div className="discount-amount-section">
                      <div className="amount-display">
                        <div className="amount-icon">
                          <i className="fas fa-tag"></i>
                        </div>
                        <div className="amount-content">
                          <span className="amount-label">مبلغ الخصم</span>
                          <span className="amount-value">{getAmount(request)} ج.م</span>
                        </div>
                      </div>
                      <div className="amount-breakdown">
                        <div className="breakdown-item original">
                          <span className="breakdown-label">المبلغ الأصلي</span>
                          <span className="breakdown-value">
                            {request.order?.totals?.total
                              ? `${Number(request.order.totals.total).toFixed(2)} ج.م`
                              : 'غير محدد'}
                          </span>
                        </div>
                        <div className="breakdown-item final">
                          <span className="breakdown-label">المبلغ النهائي</span>
                          <span className="breakdown-value final-amount">
                            {(() => {
                              const original = Number(request.order?.totals?.total || 0);
                              const discount = Number(getAmount(request));
                              return `${(original - discount).toFixed(2)} ج.م`;
                            })()}
                          </span>
                        </div>
                      </div>
                    </div>

                    {/* Waiter info section */}
                    <div className="discount-waiter-section">
                      <div className="waiter-info">
                        <div className="waiter-icon">
                          <i className="fas fa-user-tie"></i>
                        </div>
                        <div className="waiter-content">
                          <span className="waiter-label">النادل المسؤول</span>
                          <span className="waiter-name">{request.waiterName || 'غير محدد'}</span>
                        </div>
                      </div>
                    </div>

                    {/* Reason section */}
                    {request.reason && (
                      <div className="discount-reason-section">
                        <div className="reason-header">
                          <i className="fas fa-comment-dots"></i>
                          <span>سبب طلب الخصم</span>
                        </div>
                        <div className="reason-content">
                          <p>{request.reason}</p>
                        </div>
                      </div>
                    )}

                    {/* Action buttons */}
                    <div className="discount-actions">
                      <button
                        className="discount-action-btn details"
                        onClick={() => {
                          console.log('🔍 Discount request details clicked:', request);
                          setSelectedDiscountForDetails(request);
                          setShowDiscountDetailsModal(true);
                        }}
                        title="عرض التفاصيل"
                      >
                        <i className="fas fa-eye"></i>
                        <span>التفاصيل</span>
                      </button>

                      {request.status === 'pending' && (
                        <>
                          <button
                            className="discount-action-btn approve"
                            onClick={() => handleApproveRequest(request)}
                            title="قبول طلب الخصم"
                            disabled={loadingActions[request._id]}
                          >
                            {loadingActions[request._id] ? (
                              <>
                                <i className="fas fa-spinner fa-spin"></i>
                                <span>جاري القبول...</span>
                              </>
                            ) : (
                              <>
                                <i className="fas fa-check"></i>
                                <span>قبول</span>
                              </>
                            )}
                          </button>

                          <button
                            className="discount-action-btn reject"
                            onClick={() => handleRejectRequest(request)}
                            title="رفض طلب الخصم"
                            disabled={loadingActions[request._id]}
                          >
                            {loadingActions[request._id] ? (
                              <>
                                <i className="fas fa-spinner fa-spin"></i>
                                <span>جاري الرفض...</span>
                              </>
                            ) : (
                              <>
                                <i className="fas fa-times"></i>
                                <span>رفض</span>
                              </>
                            )}
                          </button>
                        </>
                      )}

                      {request.status !== 'pending' && (
                        <div className={`discount-status-message ${request.status}`}>
                          <div className="status-icon">
                            <i className={`fas ${request.status === 'approved' ? 'fa-check-circle' : 'fa-times-circle'}`}></i>
                          </div>
                          <div className="status-content">
                            <span className="status-text">
                              {request.status === 'approved' ? 'تم قبول الطلب' : 'تم رفض الطلب'}
                            </span>
                            <span className="status-date">
                              {request.updatedAt && new Date(request.updatedAt).toLocaleDateString('ar-SA', {
                                month: 'short',
                                day: 'numeric',
                                hour: '2-digit',
                                minute: '2-digit'
                              })}
                            </span>
                          </div>
                        </div>
                      )}
                    </div>
                  </div>
                  
                  {/* Status border indicator */}
                  <div className={`discount-requests-card-border ${statusBadge.class}`}></div>
                </ResponsiveCard>
              );
            })}
          </ResponsiveGrid>
        )}
      </div>
    </div>
  );
};

export default DiscountRequestsManagerScreenBootstrap;

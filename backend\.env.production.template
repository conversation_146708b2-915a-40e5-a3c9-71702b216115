# Deployment Configuration for Cloud Platforms
# تكوين النشر للمنصات السحابية

# Railway Backend Configuration
RAILWAY_PROJECT_NAME=deshacoffee-backend
RAILWAY_SERVICE_NAME=deshacoffee-production
RAILWAY_ENVIRONMENT=production

# Vercel Frontend Configuration
VERCEL_PROJECT_NAME=desha-coffee
VERCEL_ENVIRONMENT=production

# MongoDB Atlas Configuration
MONGODB_CLUSTER=mycoffechop
MONGODB_DATABASE=deshacoffee
MONGODB_REGION=Middle-East

# Health Check Endpoints
HEALTH_CHECK_ENDPOINT=/health
HEALTH_CHECK_INTERVAL=30

# Monitoring Configuration
LOG_LEVEL=info
MONITORING_ENABLED=true
PERFORMANCE_MONITORING=true

# Security Configuration for Production
HELMET_ENABLED=true
COMPRESSION_ENABLED=true
RATE_LIMITING_ENABLED=true

# CORS Configuration for Production
CORS_STRICT_MODE=false
CORS_MOBILE_SUPPORT=true
CORS_DEVELOPMENT_SUPPORT=false

# API Configuration
API_VERSION=v1
API_RATE_LIMIT=200
API_TIMEOUT=30000

# Socket.IO Configuration
SOCKETIO_ENABLED=true
SOCKETIO_CORS_ORIGIN=https://desha-coffee.vercel.app
SOCKETIO_TRANSPORTS=websocket,polling

# File Upload Configuration
UPLOAD_MAX_SIZE=10MB
UPLOAD_ALLOWED_TYPES=image/jpeg,image/png,image/gif,image/webp

# Cache Configuration
CACHE_ENABLED=true
CACHE_TTL=3600
CACHE_MAX_SIZE=100MB

# Database Connection Pool
DB_CONNECTION_POOL_MIN=2
DB_CONNECTION_POOL_MAX=10
DB_CONNECTION_TIMEOUT=10000

# Error Handling
ERROR_REPORTING=true
ERROR_STACK_TRACE=false
ERROR_SENSITIVE_DATA=false

const fetch = require('node-fetch');

const backEndURL = 'https://deshacoffee-production.up.railway.app';

async function debugSaraLogin() {
  try {
    console.log('🔐 تسجيل الدخول كنادلة sara...');
    
    const loginResponse = await fetch(`${backEndURL}/api/v1/auth/login`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ username: 'sara', password: '253040' })
    });
    
    const loginResult = await loginResponse.json();
    console.log('📄 نتيجة تسجيل الدخول الكاملة:', JSON.stringify(loginResult, null, 2));
    
  } catch (error) {
    console.error('❌ خطأ:', error);
  }
}

debugSaraLogin();

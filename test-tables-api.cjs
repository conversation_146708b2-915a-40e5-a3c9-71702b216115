const axios = require('axios');

// تكوين API
const API_BASE_URL = 'http://localhost:5000';

// معلومات تسجيل الدخول للاختبار
const TEST_USERS = [
  { username: '<PERSON><PERSON>', password: 'admin123' },
  { username: 'beso', password: 'admin123' },
  { username: 'azza', password: 'admin123' },
  { username: 'mohamed', password: 'admin123' },
  { username: 'hany', password: 'admin123' }
];

let authToken = '';

async function login() {
  for (const testUser of TEST_USERS) {
    try {
      console.log(`🔑 محاولة تسجيل الدخول باسم: ${testUser.username}...`);
      const response = await axios.post(`${API_BASE_URL}/api/v1/auth/login`, testUser);
      
      if (response.data.success && response.data.token) {
        authToken = response.data.token;
        console.log(`✅ تم تسجيل الدخول بنجاح باسم: ${testUser.username}`);
        return true;
      }
    } catch (error) {
      console.log(`❌ فشل تسجيل الدخول لـ ${testUser.username}:`, error.response?.data?.message || error.message);
    }
  }
  
  console.log('❌ فشل في تسجيل الدخول بجميع المحاولات');
  return false;
}

async function testTableAccountsAPI() {
  try {
    console.log('📊 اختبار API الطاولات...');
    
    const response = await axios.get(`${API_BASE_URL}/api/v1/table-accounts`, {
      headers: {
        'Authorization': `Bearer ${authToken}`,
        'Content-Type': 'application/json'
      }
    });

    console.log('✅ استجابة API الطاولات:');
    console.log('Status Code:', response.status);
    console.log('Success:', response.data.success);
    console.log('عدد الطاولات:', response.data.data?.length || 0);
    
    if (response.data.data && response.data.data.length > 0) {
      console.log('📋 عينة من الطاولات:');
      response.data.data.slice(0, 3).forEach((table, index) => {
        console.log(`  ${index + 1}. طاولة رقم: ${table.tableNumber || table.number}`);
        console.log(`     الحالة: ${table.isOpen ? 'مفتوحة' : 'مغلقة'}`);
        console.log(`     النادل: ${table.waiterName || 'غير محدد'}`);
        console.log(`     المبلغ: ${table.totalAmount || 0} ج.م`);
        console.log(`     عدد الطلبات: ${table.orders?.length || 0}`);
        console.log('     ---');
      });
    } else {
      console.log('❌ لا توجد طاولات في الاستجابة');
    }

  } catch (error) {
    console.log('❌ خطأ في API الطاولات:', error.response?.data?.message || error.message);
    if (error.response?.status === 401) {
      console.log('🔑 مشكلة في المصادقة - قد تحتاج إلى تسجيل دخول جديد');
    }
  }
}

async function testTablesAPI() {
  try {
    console.log('🏢 اختبار API الطاولات الأساسية...');
    
    const response = await axios.get(`${API_BASE_URL}/api/v1/tables`, {
      headers: {
        'Authorization': `Bearer ${authToken}`,
        'Content-Type': 'application/json'
      }
    });

    console.log('✅ استجابة API الطاولات الأساسية:');
    console.log('Status Code:', response.status);
    console.log('Success:', response.data.success);
    console.log('عدد الطاولات:', response.data.data?.length || 0);
    
    if (response.data.data && response.data.data.length > 0) {
      console.log('📋 عينة من الطاولات الأساسية:');
      response.data.data.slice(0, 3).forEach((table, index) => {
        console.log(`  ${index + 1}. طاولة رقم: ${table.number}`);
        console.log(`     الاسم: ${table.name || 'غير محدد'}`);
        console.log(`     القسم: ${table.section || 'غير محدد'}`);
        console.log(`     نشطة: ${table.isActive ? 'نعم' : 'لا'}`);
        console.log(`     مشغولة: ${table.isOccupied ? 'نعم' : 'لا'}`);
        console.log('     ---');
      });
    }

  } catch (error) {
    console.log('❌ خطأ في API الطاولات الأساسية:', error.response?.data?.message || error.message);
  }
}

async function testOrdersAPI() {
  try {
    console.log('📋 اختبار API الطلبات...');
    
    const response = await axios.get(`${API_BASE_URL}/api/v1/orders`, {
      headers: {
        'Authorization': `Bearer ${authToken}`,
        'Content-Type': 'application/json'
      }
    });

    console.log('✅ استجابة API الطلبات:');
    console.log('Status Code:', response.status);
    console.log('Success:', response.data.success);
    console.log('عدد الطلبات:', response.data.data?.length || 0);
    
    if (response.data.data && response.data.data.length > 0) {
      console.log('📋 عينة من الطلبات:');
      response.data.data.slice(0, 3).forEach((order, index) => {
        console.log(`  ${index + 1}. طلب رقم: ${order.orderNumber}`);
        console.log(`     الطاولة: ${order.tableNumber || order.table?.number || 'غير محدد'}`);
        console.log(`     النادل: ${order.waiterName || 'غير محدد'}`);
        console.log(`     الحالة: ${order.status}`);
        console.log(`     المبلغ: ${order.totals?.total || order.totalPrice || 0} ج.م`);
        console.log('     ---');
      });
    }

  } catch (error) {
    console.log('❌ خطأ في API الطلبات:', error.response?.data?.message || error.message);
  }
}

async function runTests() {
  console.log('🚀 بدء اختبار APIs...');
  console.log('================================');
  
  // تسجيل الدخول أولاً
  const loginSuccess = await login();
  if (!loginSuccess) {
    console.log('❌ لا يمكن المتابعة بدون تسجيل دخول');
    return;
  }

  console.log('================================');
  
  // اختبار APIs
  await testTableAccountsAPI();
  console.log('================================');
  
  await testTablesAPI();
  console.log('================================');
  
  await testOrdersAPI();
  console.log('================================');
  
  console.log('✅ انتهاء الاختبارات');
}

// تشغيل الاختبارات
runTests().catch(error => {
  console.error('❌ خطأ عام في الاختبار:', error.message);
});

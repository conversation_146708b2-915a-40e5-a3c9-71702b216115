# مجلد النسخ الاحتياطية للـ CSS القديمة

## الغرض من هذا المجلد:
يحتوي هذا المجلد على ملفات CSS القديمة التي تم نقلها من المجلد الجذر لمنع التعارض مع نظام CSS الجديد المنظم.

## الملفات المنقولة:

### 1. `App_old.css`
- **الملف الأصلي**: `src/App.css`
- **الحالة**: تم إزالة استيراده من `App.tsx` و `main.tsx`
- **السبب**: كان يحتوي على تنسيقات عامة قديمة تتعارض مع النظام الجديد

### 2. `WaiterDashboard_old.css`
- **الملف الأصلي**: `src/WaiterDashboard.css`
- **الحالة**: كان فارغاً، تم نقله احتياطياً
- **السبب**: استبدال بـ `styles/screens/WaiterDashboardScreen.css`

### 3. `ChefDashboard_old.css`
- **الملف الأصلي**: `src/ChefDashboard.css`
- **الحالة**: تم إزالة استيراده من `ChefDashboard.tsx` و `main.tsx`
- **السبب**: يجب إنشاء ملف CSS منظم جديد للشيف في `styles/screens/`

### 4. `bootstrap-native-grid_old.css`
- **الملف الأصلي**: `src/bootstrap-native-grid.css`
- **الحالة**: تم إزالة استيراده من `main.tsx`
- **السبب**: استبدال بنظام CSS Grid مخصص في الملفات الجديدة

### 5. `bootstrap-responsive-screens_old.css`
- **الملف الأصلي**: `src/bootstrap-responsive-screens.css`
- **الحالة**: تم إزالة استيراده من `main.tsx`
- **السبب**: تم دمج الاستجابة في ملفات CSS الجديدة لكل شاشة

## نظام CSS الجديد:
- **المكونات**: `src/styles/components/`
- **الشاشات**: `src/styles/screens/`
- **التخطيط**: `src/styles/layout/`

## ملاحظات:
- لا تحذف هذه الملفات نهائياً إلا بعد التأكد من عمل النظام الجديد بشكل مثالي
- يمكن الرجوع إليها إذا احتجت لاسترجاع أي تنسيقات مفقودة
- تأكد من إنشاء ملفات CSS جديدة منظمة للمكونات التي لا تملك تنسيقات حالياً

## تاريخ النقل:
${new Date().toISOString().split('T')[0]}

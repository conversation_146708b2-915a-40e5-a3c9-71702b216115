/* Bootstrap Responsive Styles للصفحات المحدثة */

/* Modal Backdrop Styles */
.modal-backdrop {
  position: fixed;
  top: 0;
  left: 0;
  z-index: 1050;
  width: 100vw;
  min-height: 100vh;
  background-color: rgba(0, 0, 0, 0.5);
}

/* Responsive Grid Enhancements */
@media (max-width: 576px) {
  /* Mobile optimizations */
  .container-fluid {
    padding: 1rem !important;
  }
  
  .card-body {
    padding: 1rem !important;
  }
  
  .row.g-3 {
    --bs-gutter-x: 0.5rem;
    --bs-gutter-y: 0.5rem;
  }
  
  .row.g-4 {
    --bs-gutter-x: 0.75rem;
    --bs-gutter-y: 0.75rem;
  }
  
  /* <PERSON>ack filters vertically on mobile */
  .col-lg-2,
  .col-lg-3,
  .col-md-4,
  .col-md-6 {
    margin-bottom: 0.5rem;
  }
  
  /* Full width buttons on mobile */
  .btn-group {
    width: 100% !important;
  }
  
  .btn-group .btn {
    flex: 1;
  }
  
  /* Responsive table */
  .table-responsive {
    font-size: 0.875rem;
  }
  
  .table td,
  .table th {
    padding: 0.5rem;
    vertical-align: middle;
  }
  
  /* Smaller modal on mobile */
  .modal-dialog {
    margin: 1rem;
    max-width: calc(100% - 2rem);
  }
  
  .modal-lg {
    max-width: calc(100% - 2rem);
  }
}

@media (min-width: 577px) and (max-width: 768px) {
  /* Tablet optimizations */
  .container-fluid {
    padding: 2rem !important;
  }
  
  .row.g-3 {
    --bs-gutter-x: 1rem;
    --bs-gutter-y: 1rem;
  }
  
  .row.g-4 {
    --bs-gutter-x: 1.5rem;
    --bs-gutter-y: 1.5rem;
  }
}

@media (min-width: 1200px) {
  /* Large screen optimizations */
  .container-fluid {
    max-width: 1400px;
    margin: 0 auto;
  }
}

@media (min-width: 1400px) {
  /* Extra large screen optimizations */
  .container-fluid {
    max-width: 1600px;
    margin: 0 auto;
  }
  
  .row.g-4 {
    --bs-gutter-x: 2rem;
    --bs-gutter-y: 2rem;
  }
}

/* Card hover effects */
.card {
  transition: transform 0.2s ease, box-shadow 0.2s ease;
}

.card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1) !important;
}

.cursor-pointer {
  cursor: pointer;
}

/* Badge styles */
.badge {
  font-weight: 500;
}

.badge.fs-6 {
  font-size: 0.875rem !important;
}

/* Button group responsive */
.btn-group-sm .btn {
  padding: 0.25rem 0.5rem;
  font-size: 0.75rem;
}

/* Search input enhancements */
.form-control:focus {
  border-color: var(--bs-primary);
  box-shadow: 0 0 0 0.2rem rgba(var(--bs-primary-rgb), 0.25);
}

/* Custom scrollbar for tables */
.table-responsive::-webkit-scrollbar {
  height: 8px;
}

.table-responsive::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 4px;
}

.table-responsive::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 4px;
}

.table-responsive::-webkit-scrollbar-thumb:hover {
  background: #a1a1a1;
}

/* RTL Improvements */
[dir="rtl"] .position-absolute.end-0 {
  left: 1rem !important;
  right: auto !important;
}

[dir="rtl"] .position-absolute.start-0 {
  right: 1rem !important;
  left: auto !important;
}

[dir="rtl"] .me-1,
[dir="rtl"] .me-2,
[dir="rtl"] .me-3 {
  margin-left: var(--bs-gutter-x) !important;
  margin-right: 0 !important;
}

[dir="rtl"] .ms-2,
[dir="rtl"] .ms-auto {
  margin-right: var(--bs-gutter-x) !important;
  margin-left: 0 !important;
}

/* Dropdown RTL fix */
[dir="rtl"] .dropdown-menu {
  right: 0;
  left: auto;
}

/* Loading states */
.spinner-border {
  width: 3rem;
  height: 3rem;
}

/* Empty state styles */
.text-center.py-5 {
  padding: 3rem 1rem !important;
}

.text-center.py-5 i {
  opacity: 0.5;
}

/* Responsive spacing utilities */
@media (max-width: 576px) {
  .py-5 {
    padding: 2rem 1rem !important;
  }
  
  .mb-4 {
    margin-bottom: 1.5rem !important;
  }
  
  .p-4 {
    padding: 1rem !important;
  }
}

/* Print styles */
@media print {
  .btn,
  .dropdown,
  .modal {
    display: none !important;
  }
  
  .card {
    border: 1px solid #000 !important;
    box-shadow: none !important;
  }
  
  .container-fluid {
    max-width: 100% !important;
    padding: 0 !important;
  }
}

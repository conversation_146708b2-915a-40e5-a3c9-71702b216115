.btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: var(--spacing-sm);
  border: none;
  border-radius: var(--border-radius-md);
  font-family: var(--font-family);
  font-weight: var(--font-weight-medium);
  cursor: pointer;
  transition: all var(--transition-speed) ease;
  position: relative;
  overflow: hidden;
}

/* أحجام الأزرار */
.btn-sm {
  padding: var(--spacing-xs) var(--spacing-md);
  font-size: var(--font-size-sm);
}

.btn-md {
  padding: var(--spacing-sm) var(--spacing-lg);
  font-size: var(--font-size-md);
}

.btn-lg {
  padding: var(--spacing-md) var(--spacing-xl);
  font-size: var(--font-size-lg);
}

/* أنواع الأزرار */
.btn-primary {
  background-color: var(--primary);
  color: white;
}

.btn-primary:hover:not(:disabled) {
  background-color: var(--primary-dark);
}

.btn-secondary {
  background-color: transparent;
  color: var(--primary);
  border: 1px solid var(--primary);
}

.btn-secondary:hover:not(:disabled) {
  background-color: rgba(121, 85, 72, 0.1);
}

.btn-accent {
  background-color: var(--accent);
  color: white;
}

.btn-accent:hover:not(:disabled) {
  background-color: var(--accent-dark);
}

.btn-success {
  background-color: var(--success);
  color: white;
}

.btn-success:hover:not(:disabled) {
  background-color: #388e3c;
}

.btn-warning {
  background-color: var(--warning);
  color: white;
}

.btn-warning:hover:not(:disabled) {
  background-color: #f57c00;
}

.btn-error {
  background-color: var(--error);
  color: white;
}

.btn-error:hover:not(:disabled) {
  background-color: #d32f2f;
}

.btn-info {
  background-color: var(--info);
  color: white;
}

.btn-info:hover:not(:disabled) {
  background-color: #1976d2;
}

/* تأثيرات الزر */
.btn:hover:not(:disabled) {
  transform: translateY(-2px);
  box-shadow: var(--shadow-md);
}

.btn:active:not(:disabled) {
  transform: translateY(0);
}

.btn:disabled {
  opacity: 0.7;
  cursor: not-allowed;
}

/* زر بعرض كامل */
.btn-full-width {
  width: 100%;
}

/* زر في حالة التحميل */
.btn-loading {
  color: transparent !important;
}

.btn-spinner {
  position: absolute;
  width: 20px;
  height: 20px;
  border: 2px solid rgba(255, 255, 255, 0.3);
  border-radius: 50%;
  border-top-color: white;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  to { transform: rotate(360deg); }
}

/* أيقونة الزر */
.btn-icon {
  font-size: 1.1em;
}

/* تنسيق خاص للوضع المظلم */
[data-theme="dark"] .btn-secondary {
  border-color: var(--primary-light);
  color: var(--primary-light);
}

[data-theme="dark"] .btn-secondary:hover:not(:disabled) {
  background-color: rgba(121, 85, 72, 0.2);
}

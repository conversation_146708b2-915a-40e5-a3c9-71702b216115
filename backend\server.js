const express = require('express');
const cors = require('cors');
const helmet = require('helmet');
const compression = require('compression');
// const rateLimit = require('express-rate-limit'); // 🚫 DISABLED - No more rate limiting
const mongoose = require('mongoose');
const { createServer } = require('http');
const { Server } = require('socket.io');
const { connectDB, getConnectionInfo, isConnected } = require('./config/database');
const config = require('./config/environment');
const SocketHandlers = require('./sockets/socketHandlers');
const MonitoringService = require('./services/monitoringService');
const mobileCompatibility = require('./middleware/mobileCompatibility');
const { enhancedMobileCompatibility, mobileErrorHandler } = require('./middleware/enhancedMobileCompatibility');
const apiManager = require('./middleware/unifiedAPIManager');

// Import unified routes (now the main files)
const productsRoutes = require('./routes/products');
const ordersRoutes = require('./routes/orders');
const categoriesRoutes = require('./routes/categories');

// Import other routes (will be converted gradually)
const authRoutes = require('./routes/auth');
const usersRoutes = require('./routes/users');
const employeesRoutes = require('./routes/employees');
const inventoryRoutes = require('./routes/inventory');
const discountRequestRoutes = require('./routes/discount-requests');
const tableAccountRoutes = require('./routes/table-accounts');
const tablesRoutes = require('./routes/tables');
const settingsRoutes = require('./routes/settings');
const reportsRoutes = require('./routes/reports');
const waiterStatsRoutes = require('./routes/waiter-stats');
const salesDiscrepancyRoutes = require('./routes/salesDiscrepancy');

const app = express();
const server = createServer(app);
const io = new Server(server, {
  cors: {
    origin: config.frontend.allowedOrigins,
    credentials: true
  }
});
const PORT = config.server.port;

// Make io accessible to routes
app.set('io', io);

// Tell Express to trust the first proxy (Railway, Vercel, Nginx, Heroku)
app.set('trust proxy', 1);

// Railway specific configuration
if (process.env.RAILWAY_PROJECT_ID) {
  app.set('trust proxy', true);
  console.log('🚂 Railway deployment detected');
}

// Vercel integration check
if (process.env.VERCEL) {
  console.log('▲ Vercel frontend integration enabled');
}

// Security middleware
app.use(helmet({
  crossOriginEmbedderPolicy: false,
  contentSecurityPolicy: {
    directives: {
      defaultSrc: ["'self'"],
      styleSrc: ["'self'", "'unsafe-inline'"],
      scriptSrc: ["'self'"],
      imgSrc: ["'self'", "data:", "https:"],
      connectSrc: ["'self'", "ws:", "wss:"]
    }
  }
}));

app.use(compression());

// Enhanced CORS configuration for mobile compatibility
app.use(cors({
  origin: function (origin, callback) {
    // Allow requests with no origin (mobile apps, Postman, etc.)
    if (!origin) return callback(null, true);
    
    const allowedOrigins = config.frontend.allowedOrigins;
    
    const isAllowed = allowedOrigins.some(allowedOrigin => {
      if (typeof allowedOrigin === 'string') {
        return origin === allowedOrigin;
      } else if (allowedOrigin instanceof RegExp) {
        return allowedOrigin.test(origin);
      } else if (allowedOrigin === true) {
        return true;
      }
      return false;
    });

    if (isAllowed) {
      callback(null, true);
    } else {
      console.warn(`❌ CORS blocked request from origin: ${origin}`);
      callback(new Error('Not allowed by CORS'));
    }
  },  credentials: true,
  methods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS', 'PATCH'],
  allowedHeaders: [
    'Origin', 
    'X-Requested-With', 
    'Content-Type', 
    'Accept', 
    'Authorization',
    'x-device-type',
    'x-request-id',
    'x-app-version',
    // Add custom headers for user identification (CORS COMPATIBLE)
    'X-User-ID',
    'X-User-Role',
    'X-Chef-ID',
    'X-Request-Type'
  ],
  exposedHeaders: [
    'x-total-count',
    'x-page-count',
    'x-current-page',
    'x-api-version'
  ],
  maxAge: 86400 // 24 hours
}));

// 🚫 RATE LIMITING COMPLETELY DISABLED
// Rate limiting function disabled for optimal performance
// All users (including chefs, waiters, managers) now have unlimited access

// 🚫 Rate Limiting DISABLED - Removed all rate limiting for better performance
// Previously had: authLimiter, generalLimiter, chefLimiter, strictLimiter
// All rate limiting has been disabled for optimal user experience

// No rate limiting applied - removed app.use('/api/', generalLimiter);

// Body parsing middleware
app.use(express.json({ 
  limit: '10mb',
  verify: (req, res, buf) => {
    req.rawBody = buf;
  }
}));
app.use(express.urlencoded({ 
  extended: true, 
  limit: '10mb' 
}));

// Enhanced Mobile compatibility middleware
app.use(enhancedMobileCompatibility);

// Request logging middleware
app.use((req, res, next) => {
  const userAgent = req.headers['user-agent'] || '';
  const isMobile = /Mobile|Android|iPhone|iPad/i.test(userAgent);
  
  if (process.env.NODE_ENV === 'development' || isMobile) {
    console.log(`📊 ${new Date().toISOString()} - ${req.method} ${req.path}`, {
      device: isMobile ? 'Mobile' : 'Desktop',
      ip: req.ip,
      userAgent: isMobile ? userAgent.substring(0, 50) + '...' : 'Desktop'
    });
  }
  
  next();
});

// Register unified routes with the API manager
console.log('🔄 Registering unified routes...');

// Core unified routes (now the main files) - NO RATE LIMITING
apiManager.registerRoute('/products', productsRoutes, { version: 'v1' });
apiManager.registerRoute('/orders', ordersRoutes, { version: 'v1' });
apiManager.registerRoute('/categories', categoriesRoutes, { version: 'v1' });

// Legacy routes (to be converted) - NO RATE LIMITING
apiManager.registerRoute('/auth', authRoutes, { version: 'v1' });
apiManager.registerRoute('/users', usersRoutes, { version: 'v1' });
apiManager.registerRoute('/employees', employeesRoutes, { version: 'v1' });
apiManager.registerRoute('/inventory', inventoryRoutes, { version: 'v1' });
apiManager.registerRoute('/discount-requests', discountRequestRoutes, { version: 'v1' });
apiManager.registerRoute('/table-accounts', tableAccountRoutes, { version: 'v1' });
apiManager.registerRoute('/tables', tablesRoutes, { version: 'v1' });
apiManager.registerRoute('/settings', settingsRoutes, { version: 'v1' });
apiManager.registerRoute('/reports', reportsRoutes, { version: 'v1' });
apiManager.registerRoute('/waiter-stats', waiterStatsRoutes, { version: 'v1' });

// Legacy routes for backward compatibility (deprecated) - MUST come before unified API manager
console.log('⚠️ Setting up legacy routes for backward compatibility...');
app.use('/api/auth', (req, res, next) => {
  console.log('🔄 Legacy route accessed: /api/auth -> /api/v1/auth');
  res.header('X-API-Deprecated', 'true');
  res.header('X-API-Migration', 'Use /api/v1/auth instead');
  next();
}, authRoutes);

app.use('/api/products', (req, res, next) => {
  console.log('🔄 Legacy route accessed: /api/products -> /api/v1/products');
  res.header('X-API-Deprecated', 'true');
  res.header('X-API-Migration', 'Use /api/v1/products instead');
  next();
}, productsRoutes);

app.use('/api/orders', (req, res, next) => {
  console.log('🔄 Legacy route accessed: /api/orders -> /api/v1/orders');
  res.header('X-API-Deprecated', 'true');
  res.header('X-API-Migration', 'Use /api/v1/orders instead');
  next();
}, ordersRoutes);

app.use('/api/categories', (req, res, next) => {
  console.log('🔄 Legacy route accessed: /api/categories -> /api/v1/categories');
  res.header('X-API-Deprecated', 'true');
  res.header('X-API-Migration', 'Use /api/v1/categories instead');
  next();
}, categoriesRoutes);

app.use('/api/users', (req, res, next) => {
  console.log('🔄 Legacy route accessed: /api/users -> /api/v1/users');
  res.header('X-API-Deprecated', 'true');
  res.header('X-API-Migration', 'Use /api/v1/users instead');
  next();
}, usersRoutes);

app.use('/api/discount-requests', (req, res, next) => {
  console.log('🔄 Legacy route accessed: /api/discount-requests -> /api/v1/discount-requests');
  res.header('X-API-Deprecated', 'true');
  res.header('X-API-Migration', 'Use /api/v1/discount-requests instead');
  next();
}, discountRequestRoutes);

app.use('/api/table-accounts', (req, res, next) => {
  console.log('🔄 Legacy route accessed: /api/table-accounts -> /api/v1/table-accounts');
  res.header('X-API-Deprecated', 'true');
  res.header('X-API-Migration', 'Use /api/v1/table-accounts instead');
  next();
}, tableAccountRoutes);

app.use('/api/inventory', (req, res, next) => {
  console.log('🔄 Legacy route accessed: /api/inventory -> /api/v1/inventory');
  res.header('X-API-Deprecated', 'true');
  res.header('X-API-Migration', 'Use /api/v1/inventory instead');
  next();
}, inventoryRoutes);

app.use('/api/tables', (req, res, next) => {
  console.log('🔄 Legacy route accessed: /api/tables -> /api/v1/tables');
  res.header('X-API-Deprecated', 'true');
  res.header('X-API-Migration', 'Use /api/v1/tables instead');
  next();
}, tablesRoutes);

app.use('/api/settings', (req, res, next) => {
  console.log('🔄 Legacy route accessed: /api/settings -> /api/v1/settings');
  res.header('X-API-Deprecated', 'true');
  res.header('X-API-Migration', 'Use /api/v1/settings instead');
  next();
}, settingsRoutes);

app.use('/api/reports', (req, res, next) => {
  console.log('🔄 Legacy route accessed: /api/reports -> /api/v1/reports');
  res.header('X-API-Deprecated', 'true');
  res.header('X-API-Migration', 'Use /api/v1/reports instead');
  next();
}, reportsRoutes);

app.use('/api/waiter-stats', (req, res, next) => {
  console.log('🔄 Legacy route accessed: /api/waiter-stats -> /api/v1/waiter-stats');
  res.header('X-API-Deprecated', 'true');
  res.header('X-API-Migration', 'Use /api/v1/waiter-stats instead');
  next();
}, waiterStatsRoutes);

// Sales discrepancy analysis routes
app.use('/api/v1/sales', (req, res, next) => {
  console.log('💰 Sales discrepancy route accessed:', req.method, req.path);
  next();
}, salesDiscrepancyRoutes);

// Use the unified API manager (this should come after legacy routes)
app.use(apiManager.getRouter());

// Enhanced mobile error handler
app.use(mobileErrorHandler);

// Socket.IO setup
const socketHandlers = new SocketHandlers(io);
io.on('connection', (socket) => {
  console.log('👤 Client connected:', socket.id);
  
  // Handle socket events
  socketHandlers.handleConnection(socket);
  
  socket.on('disconnect', () => {
    console.log('👤 Client disconnected:', socket.id);
  });
});

// Global error handler
app.use((error, req, res, next) => {
  console.error('🚨 Global Server Error:', {
    path: req.path,
    method: req.method,
    error: error.message,
    stack: process.env.NODE_ENV === 'development' ? error.stack : undefined
  });

  // Handle specific error types
  if (error.name === 'ValidationError') {
    return res.status(400).json({
      success: false,
      message: 'خطأ في التحقق من البيانات',
      error: 'VALIDATION_ERROR',
      timestamp: new Date().toISOString()
    });
  }

  if (error.name === 'MongoError' || error.name === 'MongooseError') {
    return res.status(500).json({
      success: false,
      message: 'خطأ في قاعدة البيانات',
      error: 'DATABASE_ERROR',
      timestamp: new Date().toISOString()
    });
  }

  if (error.message === 'Not allowed by CORS') {
    return res.status(403).json({
      success: false,
      message: 'غير مسموح بالوصول من هذا المصدر',
      error: 'CORS_ERROR',
      timestamp: new Date().toISOString()
    });
  }

  // Generic error response
  res.status(error.status || 500).json({
    success: false,
    message: error.message || 'خطأ داخلي في الخادم',
    error: 'INTERNAL_ERROR',
    timestamp: new Date().toISOString()
  });
});

// 404 handler
app.use('*', (req, res) => {
  console.log(`❌ 404 - Route not found: ${req.method} ${req.originalUrl}`);
  res.status(404).json({
    success: false,
    message: 'المسار غير موجود',
    path: req.originalUrl,
    method: req.method,
    availableEndpoints: {
      health: '/health',
      apiDocs: '/api/docs',
      v1: {
        auth: '/api/v1/auth',
        users: '/api/v1/users',
        products: '/api/v1/products',
        orders: '/api/v1/orders',
        categories: '/api/v1/categories',
        inventory: '/api/v1/inventory',
        tables: '/api/v1/tables'
      }
    },
    timestamp: new Date().toISOString()
  });
});

// Database connection and server startup
async function startServer() {
  try {
    console.log('🚀 Starting Coffee Shop Management Server...');
    
    // Connect to database
    console.log('📡 Connecting to database...');
    await connectDB();
    console.log('✅ Database connected successfully');
    
    // Initialize monitoring service
    if (MonitoringService) {
      try {
        global.monitoringService = new MonitoringService();
        await global.monitoringService.initialize();
        console.log('✅ Monitoring service initialized');
      } catch (monitoringError) {
        console.error('⚠️ Monitoring service failed to initialize:', monitoringError.message);
      }
    }
    
    // Start server
    server.listen(PORT, '0.0.0.0', () => {
      console.log('✅ Server started successfully!');
      console.log(`🌐 Server running on: http://0.0.0.0:${PORT}`);
      console.log(`📱 Mobile optimized: ✅`);
      console.log(`🔒 Security enabled: ✅`);
      console.log(`🚀 Environment: ${process.env.NODE_ENV || 'development'}`);
      console.log(`📊 API Documentation: http://0.0.0.0:${PORT}/api/docs`);
      console.log(`❤️ Health Check: http://0.0.0.0:${PORT}/health`);
      
      // Log registered routes
      const routes = apiManager.getRouteInfo();
      console.log(`📋 Registered ${routes.length} API routes:`);
      routes.forEach(route => {
        console.log(`   ${route.deprecated ? '⚠️' : '✅'} ${route.path}`);
      });
    });
    
  } catch (error) {
    console.error('❌ Failed to start server:', error);
    process.exit(1);
  }
}

// Handle graceful shutdown
process.on('SIGTERM', async () => {
  console.log('🛑 SIGTERM received, shutting down gracefully...');
  
  try {
    // Close server
    server.close(() => {
      console.log('✅ HTTP server closed');
    });
    
    // Close database connection
    await mongoose.connection.close();
    console.log('✅ Database connection closed');
    
    process.exit(0);
  } catch (error) {
    console.error('❌ Error during shutdown:', error);
    process.exit(1);
  }
});

process.on('SIGINT', async () => {
  console.log('🛑 SIGINT received, shutting down gracefully...');
  
  try {
    server.close(() => {
      console.log('✅ HTTP server closed');
    });
    
    await mongoose.connection.close();
    console.log('✅ Database connection closed');
    
    process.exit(0);
  } catch (error) {
    console.error('❌ Error during shutdown:', error);
    process.exit(1);
  }
});

// Handle uncaught exceptions
process.on('uncaughtException', (error) => {
  console.error('💥 Uncaught Exception:', error);
  process.exit(1);
});

process.on('unhandledRejection', (reason, promise) => {
  console.error('💥 Unhandled Rejection at:', promise, 'reason:', reason);
  process.exit(1);
});

// Start the server
startServer();

module.exports = { app, server, io };

const mongoose = require('mongoose');
require('dotenv').config();

async function checkAndCreateManagerUser() {
  try {
    console.log('🔌 Connecting to MongoDB Atlas...');
    await mongoose.connect(process.env.MONGODB_URI);
    console.log('✅ Connected successfully!');

    const usersCollection = mongoose.connection.db.collection('users');
    
    // Check existing users
    console.log('\n👥 Current Users in Database:');
    const allUsers = await usersCollection.find().toArray();
    
    allUsers.forEach(user => {
      console.log(`- ${user.name} (${user.username}) - Role: ${user.role} - Status: ${user.status}`);
    });

    // Check if Beso exists
    const existingUser = await usersCollection.findOne({ username: 'Be<PERSON>' });
    
    if (existingUser) {
      console.log(`\n👤 User 'Beso' found: ${existingUser.name} - Role: ${existingUser.role}`);
      console.log('Password hash:', existingUser.password.substring(0, 20) + '...');
    } else {
      console.log(`\n❌ User 'Beso' not found! Creating new manager account...`);
      
      // Create new manager user
      const bcrypt = require('bcrypt');
      const hashedPassword = await bcrypt.hash('MOHAMEDmostafa123', 12);
      
      const newManager = {
        username: 'Beso',
        password: hashedPassword,
        email: '<EMAIL>',
        name: 'محمد مصطفى',
        role: 'manager',
        status: 'active',
        phone: '***********',
        createdAt: new Date(),
        updatedAt: new Date(),
        loginAttempts: 0,
        lockUntil: null
      };

      await usersCollection.insertOne(newManager);
      console.log('✅ Manager account created successfully!');
      console.log(`- Username: Beso`);
      console.log(`- Password: MOHAMEDmostafa123`);
      console.log(`- Role: manager`);
      console.log(`- Name: محمد مصطفى`);
    }

    // Also check for other manager accounts
    const managers = await usersCollection.find({ role: 'manager' }).toArray();
    console.log(`\n🏢 Manager Accounts (${managers.length} total):`);
    managers.forEach(manager => {
      console.log(`- ${manager.name} (${manager.username}) - Status: ${manager.status}`);
    });

    // Test password verification for Beso
    const besoUser = await usersCollection.findOne({ username: 'Beso' });
    if (besoUser) {
      const bcrypt = require('bcrypt');
      const isPasswordCorrect = await bcrypt.compare('MOHAMEDmostafa123', besoUser.password);
      console.log(`\n🔐 Password verification for 'Beso': ${isPasswordCorrect ? '✅ Correct' : '❌ Incorrect'}`);
    }

  } catch (error) {
    console.error('❌ Error:', error.message);
  } finally {
    await mongoose.connection.close();
    console.log('\n🔐 Database connection closed.');
  }
}

checkAndCreateManagerUser();

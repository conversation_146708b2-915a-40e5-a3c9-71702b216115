import React from 'react';

interface ResponsiveModalProps {
  show: boolean;
  onHide: () => void;
  title: string;
  size?: 'sm' | 'lg' | 'xl';
  children: React.ReactNode;
  footer?: React.ReactNode;
  centered?: boolean;
  scrollable?: boolean;
  backdrop?: boolean | 'static';
  keyboard?: boolean;
}

const ResponsiveModal: React.FC<ResponsiveModalProps> = ({
  show,
  onHide,
  title,
  size,
  children,
  footer,
  centered = true,
  scrollable = true,
  backdrop = true,
  keyboard = true
}) => {
  const handleBackdropClick = (e: React.MouseEvent) => {
    if (e.target === e.currentTarget && backdrop !== 'static') {
      onHide();
    }
  };

  const handleKeyDown = (e: KeyboardEvent) => {
    if (e.key === 'Escape' && keyboard) {
      onHide();
    }
  };

  React.useEffect(() => {
    if (show && keyboard) {
      document.addEventListener('keydown', handleKeyDown);
      return () => document.removeEventListener('keydown', handleKeyDown);
    }
  }, [show, keyboard]);

  const getModalClasses = () => {
    const classes = ['modal-dialog'];
    
    if (size) classes.push(`modal-${size}`);
    if (centered) classes.push('modal-dialog-centered');
    if (scrollable) classes.push('modal-dialog-scrollable');
    
    return classes.join(' ');
  };

  if (!show) return null;

  return (
    <div className="modal-backdrop">
      <div 
        className="modal fade show d-block" 
        tabIndex={-1}
        onClick={handleBackdropClick}
        role="dialog"
        aria-labelledby="modalTitle"
      >
        <div className={getModalClasses()}>
          <div className="modal-content">
            <div className="modal-header">
              <h5 className="modal-title" id="modalTitle">
                {title}
              </h5>
              <button
                type="button"
                className="btn-close"
                onClick={onHide}
                title="إغلاق النافذة"
                aria-label="إغلاق النافذة"
              ></button>
            </div>
            <div className="modal-body">
              {children}
            </div>
            {footer && (
              <div className="modal-footer">
                {footer}
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  );
};

export default ResponsiveModal;

﻿/* Tables Screen Isolated Styles */
@import '../variables/tables-variables.css';

/* ØªØ®Ø·ÙŠØ· Ø§Ù„Ø·Ø§ÙˆÙ„Ø§Øª */
.tables-screen {
  padding: var(--tables-spacing-lg);
  max-width: 1400px;
  margin: 0 auto;
  overflow-x: hidden;
}

/* Ø±Ø£Ø³ Ø§Ù„Ø´Ø§Ø´Ø© */
.tables-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: var(--tables-spacing-lg);
  flex-wrap: wrap;
  gap: var(--tables-spacing-md);
}

.tables-title {
  font-size: var(--tables-font-size-xl);
  font-weight: 700;
  color: var(--tables-text-primary);
  margin: 0;
}

/* Ø£Ø¯ÙˆØ§Øª Ø§Ù„ØªØ­ÙƒÙ… ÙÙŠ Ø§Ù„Ø·Ø§ÙˆÙ„Ø§Øª */
.tables-controls {
  display: flex;
  gap: var(--tables-spacing-md);
  align-items: center;
  flex-wrap: wrap;
}

.tables-search {
  padding: var(--tables-spacing-sm) var(--tables-spacing-md);
  border: 1px solid var(--tables-border-color);
  border-radius: var(--tables-border-radius);
  font-size: var(--tables-font-size-sm);
  background: var(--tables-bg-primary);
  color: var(--tables-text-primary);
  min-width: 200px;
}

.tables-filter {
  padding: var(--tables-spacing-sm) var(--tables-spacing-md);
  border: 1px solid var(--tables-border-color);
  border-radius: var(--tables-border-radius);
  font-size: var(--tables-font-size-sm);
  background: var(--tables-bg-primary);
  color: var(--tables-text-primary);
  min-width: 120px;
}

/* Ø²Ø± Ø¥Ø¶Ø§ÙØ© Ø·Ø§ÙˆÙ„Ø© */
.add-table-btn {
  padding: var(--tables-spacing-sm) var(--tables-spacing-lg);
  background: var(--tables-primary-color);
  color: white;
  border: none;
  border-radius: var(--tables-border-radius);
  cursor: pointer;
  font-weight: 500;
  transition: var(--tables-transition-medium);
  white-space: nowrap;
}

.add-table-btn:hover {
  background: var(--tables-primary-hover);
  transform: translateY(-1px);
}

/* Ø¨Ø·Ø§Ù‚Ø§Øª Ø§Ù„Ø·Ø§ÙˆÙ„Ø§Øª */
.tables-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: var(--tables-spacing-lg);
  margin-top: var(--tables-spacing-lg);
}

.table-card {
  background: var(--tables-bg-primary);
  border: 1px solid var(--tables-border-color);
  border-radius: var(--tables-border-radius-lg);
  padding: var(--tables-spacing-lg);
  position: relative;
  transition: all 0.3s ease;
  box-shadow: var(--tables-shadow-sm);
  word-break: break-word;
  overflow: visible;
}

.table-card:hover {
  transform: translateY(-2px);
  box-shadow: var(--tables-shadow-md);
}

.table-card.active {
  border-color: var(--tables-success-color);
  box-shadow: 0 0 0 2px rgba(34, 197, 94, 0.1);
}

.table-card.closed {
  opacity: 0.7;
  border-color: var(--tables-error-color);
}

/* Ø±Ø£Ø³ Ø¨Ø·Ø§Ù‚Ø© Ø§Ù„Ø·Ø§ÙˆÙ„Ø© */
.table-card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: var(--tables-spacing-md);
}

.table-number {
  font-size: var(--tables-font-size-lg);
  font-weight: 700;
  color: var(--tables-text-primary);
}

.table-status {
  padding: 4px 8px;
  border-radius: 12px;
  font-size: var(--tables-font-size-xs);
  font-weight: 500;
  text-transform: uppercase;
}

.table-status.active {
  background: var(--tables-success-light);
  color: var(--tables-success-color);
}

.table-status.closed {
  background: var(--tables-error-light);
  color: var(--tables-error-color);
}

/* Ù…Ø¹Ù„ÙˆÙ…Ø§Øª Ø§Ù„Ø·Ø§ÙˆÙ„Ø© */
.table-info {
  margin-bottom: var(--tables-spacing-md);
}

.table-info-item {
  display: flex;
  justify-content: space-between;
  margin-bottom: var(--tables-spacing-xs);
  font-size: var(--tables-font-size-sm);
}

.table-info-label {
  color: var(--tables-text-secondary);
}

.table-info-value {
  color: var(--tables-text-primary);
  font-weight: 500;
}

.table-total-amount {
  color: var(--tables-primary-color);
  font-weight: 700;
  font-size: var(--tables-font-size-md);
}

/* Ø£Ø²Ø±Ø§Ø± Ø§Ù„ØªØ­ÙƒÙ… ÙÙŠ Ø§Ù„Ø·Ø§ÙˆÙ„Ø© */
.table-actions {
  display: flex;
  gap: var(--tables-spacing-sm);
  margin-top: var(--tables-spacing-md);
  flex-wrap: wrap;
}

.table-action-btn {
  padding: var(--tables-spacing-xs) var(--tables-spacing-sm);
  border: none;
  border-radius: var(--tables-border-radius);
  cursor: pointer;
  font-size: var(--tables-font-size-xs);
  font-weight: 500;
  transition: all 0.3s ease;
  flex: 1;
  min-width: 60px;
}

.table-action-btn.view {
  background: var(--tables-info-light);
  color: var(--tables-info-color);
}

.table-action-btn.view:hover {
  background: var(--tables-info-color);
  color: white;
}

.table-action-btn.close {
  background: var(--tables-error-light);
  color: var(--tables-error-color);
}

.table-action-btn.close:hover {
  background: var(--tables-error-color);
  color: white;
}

.table-action-btn.edit {
  background: var(--tables-warning-light);
  color: var(--tables-warning-color);
}

.table-action-btn.edit:hover {
  background: var(--tables-warning-color);
  color: white;
}

/* Ø­Ø§Ù„Ø© Ø§Ù„ØªØ­Ù…ÙŠÙ„ */
.tables-loading {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 300px;
  font-size: var(--tables-font-size-lg);
  color: var(--tables-text-secondary);
}

/* Ø§Ù„Ø¥Ø­ØµØ§Ø¦ÙŠØ§Øª Ø§Ù„Ø³Ø±ÙŠØ¹Ø© */
.tables-stats {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: var(--tables-spacing-md);
  margin-bottom: var(--tables-spacing-lg);
}

.table-stat-card {
  background: var(--tables-bg-primary);
  border: 1px solid var(--tables-border-color);
  border-radius: var(--tables-border-radius);
  padding: var(--tables-spacing-md);
  text-align: center;
}

.table-stat-number {
  font-size: var(--tables-font-size-xl);
  font-weight: 700;
  color: var(--tables-primary-color);
  margin-bottom: var(--tables-spacing-xs);
}

.table-stat-label {
  font-size: var(--tables-font-size-sm);
  color: var(--tables-text-secondary);
}

/* ØªØ¬Ø§ÙˆØ¨ Ø§Ù„Ø´Ø§Ø´Ø© */
@media (max-width: 768px) {
  .tables-screen {
    padding: var(--tables-spacing-md);
  }
  
  .tables-header {
    flex-direction: column;
    align-items: stretch;
  }
  
  .tables-controls {
    flex-direction: column;
  }
  
  .tables-search,
  .tables-filter {
    width: 100%;
  }
  
  .tables-grid {
    grid-template-columns: 1fr;
    gap: var(--tables-spacing-md);
  }
  
  .tables-stats {
    grid-template-columns: repeat(2, 1fr);
  }
}

@media (max-width: 480px) {
  .table-actions {
    flex-direction: column;
  }
  
  .table-action-btn {
    flex: none;
  }
  
  .tables-stats {
    grid-template-columns: 1fr;
  }
}



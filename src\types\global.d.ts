// Global type definitions for the project

declare global {
  interface Window {
    matchMedia: (query: string) => MediaQueryList;
  }

  namespace jest {
    interface Matchers<R> {
      toBeInTheDocument(): R;
      toHaveClass(className: string): R;
      toHaveTextContent(text: string): R;
    }
  }
}

// Socket.IO event types
export interface SocketEvents {
  // User registration
  register: (data: { role: string; name: string }) => void;
  registered: (data: any) => void;

  // Order events
  newOrder: (data: any) => void;
  orderUpdate: (data: any) => void;
  simulateOrderAccepted: (data: any) => void;
  simulateOrderReady: (data: any) => void;

  // Table events
  tableAccountClosed: (data: any) => void;

  // Connection events
  connect: () => void;
  disconnect: () => void;
}

export {};

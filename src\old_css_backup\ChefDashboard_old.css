/* Chef Dashboard CSS */
/* =================================== */
/* نمط شاشة المطبخ والطاهي */
/* =================================== */

/* Main container */
.chef-dashboard {
  height: 100vh;
  display: flex;
  flex-direction: column;
  background: var(--bs-body-bg);
  direction: rtl;
}

/* Header styles */
.chef-header {
  background: var(--bs-primary);
  color: white;
  padding: 1rem 2rem;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  z-index: 1000;
}

.header-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  max-width: 1400px;
  margin: 0 auto;
}

.header-left {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.sidebar-toggle {
  background: none;
  border: none;
  color: white;
  font-size: 1.5rem;
  cursor: pointer;
  padding: 0.5rem;
  border-radius: 6px;
  transition: background-color 0.3s ease;
}

.sidebar-toggle:hover {
  background: rgba(255, 255, 255, 0.1);
}

.header-right {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.chef-name {
  font-weight: 600;
  font-size: 1.1rem;
}

.logout-btn {
  background: rgba(255, 255, 255, 0.2);
  border: none;
  color: white;
  padding: 0.5rem 1rem;
  border-radius: 6px;
  cursor: pointer;
  transition: all 0.3s ease;
}

.logout-btn:hover {
  background: rgba(255, 255, 255, 0.3);
}

/* Dashboard content */
.dashboard-content {
  flex: 1;
  display: flex;
  overflow: hidden;
}

/* Sidebar styles */
.chef-sidebar {
  width: 300px;
  background: white;
  box-shadow: 2px 0 8px rgba(0, 0, 0, 0.1);
  transform: translateX(-100%);
  transition: transform 0.3s ease;
  overflow-y: auto;
  z-index: 999;
}

.chef-sidebar.open {
  transform: translateX(0);
}

.sidebar-content {
  padding: 1.5rem;
}

.chef-profile {
  text-align: center;
  margin-bottom: 2rem;
  padding-bottom: 1.5rem;
  border-bottom: 2px solid var(--bs-border-color);
}

.chef-avatar {
  font-size: 4rem;
  margin-bottom: 1rem;
}

/* Filter navigation */
.filter-nav {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.filter-btn {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 1rem;
  background: var(--bs-light);
  border: 2px solid transparent;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.3s ease;
  text-decoration: none;
  color: var(--bs-dark);
}

.filter-btn:hover {
  background: var(--bs-primary-subtle);
  border-color: var(--bs-primary);
}

.filter-btn.active {
  background: var(--bs-primary);
  color: white;
  border-color: var(--bs-primary);
}

.filter-btn .filter-icon {
  font-size: 1.5rem;
}

.filter-btn .filter-text {
  flex: 1;
  margin: 0 1rem;
  font-weight: 600;
}

.filter-btn .filter-count {
  background: rgba(0, 0, 0, 0.1);
  color: inherit;
  padding: 0.25rem 0.75rem;
  border-radius: 12px;
  font-size: 0.9rem;
  font-weight: 700;
}

.filter-btn.active .filter-count {
  background: rgba(255, 255, 255, 0.2);
}

/* Sidebar overlay */
.sidebar-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  z-index: 998;
  display: none;
}

.sidebar-overlay.show {
  display: block;
}

/* Main content */
.chef-main {
  flex: 1;
  padding: 2rem;
  overflow-y: auto;
  background: var(--bs-light);
}

/* Orders section */
.orders-section {
  max-width: 1400px;
  margin: 0 auto;
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 2rem;
}

.section-header h2 {
  color: var(--bs-dark);
  font-size: 2rem;
  font-weight: 700;
  margin: 0;
}

.orders-count {
  background: var(--bs-primary);
  color: white;
  padding: 0.5rem 1rem;
  border-radius: 12px;
  font-weight: 600;
  font-size: 1.1rem;
}

/* Loading styles */
.loading {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 3rem;
}

.loading-spinner {
  width: 3rem;
  height: 3rem;
  border: 4px solid var(--bs-border-color);
  border-top-color: var(--bs-primary);
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}

/* No orders state */
.no-orders {
  text-align: center;
  padding: 3rem;
  color: var(--bs-secondary);
}

.no-orders-icon {
  font-size: 4rem;
  margin-bottom: 1rem;
}

/* Orders grid */
.orders-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
  gap: 1.5rem;
}

/* Order card styles */
.order-card {
  background: white;
  border-radius: 12px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  overflow: hidden;
  transition: all 0.3s ease;
  border-left: 4px solid var(--bs-secondary);
}

.order-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.15);
}

.order-card.pending {
  border-left-color: var(--bs-warning);
}

.order-card.preparing {
  border-left-color: var(--bs-info);
}

.order-card.ready {
  border-left-color: var(--bs-success);
}

/* Order header */
.order-header {
  background: var(--bs-light);
  padding: 1rem 1.5rem;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.order-number {
  font-size: 1.2rem;
  font-weight: 700;
  color: var(--bs-dark);
}

.order-status {
  padding: 0.25rem 0.75rem;
  border-radius: 12px;
  font-size: 0.8rem;
  font-weight: 600;
  text-transform: uppercase;
}

.order-status.pending {
  background: rgba(255, 193, 7, 0.1);
  color: var(--bs-warning);
}

.order-status.preparing {
  background: rgba(13, 202, 240, 0.1);
  color: var(--bs-info);
}

.order-status.ready {
  background: rgba(25, 135, 84, 0.1);
  color: var(--bs-success);
}

/* Order info */
.order-info {
  padding: 1rem 1.5rem;
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.order-table,
.order-customer,
.order-waiter {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-size: 0.9rem;
  color: var(--bs-secondary);
}

.order-info .icon {
  font-size: 1.1rem;
}

/* Order items */
.order-items {
  padding: 0 1.5rem 1rem;
}

.items-count {
  font-size: 0.9rem;
  color: var(--bs-secondary);
  margin-bottom: 0.5rem;
}

.items-preview {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.item-preview {
  background: var(--bs-light);
  padding: 0.75rem;
  border-radius: 6px;
}

.item-name {
  font-weight: 600;
  color: var(--bs-dark);
}

.item-special-note {
  font-size: 0.8rem;
  color: var(--bs-warning);
  font-style: italic;
  margin-top: 0.25rem;
}

.more-items {
  font-size: 0.8rem;
  color: var(--bs-secondary);
  font-style: italic;
}

/* Order time */
.order-time {
  padding: 1rem 1.5rem;
  border-top: 1px solid var(--bs-border-color);
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-size: 0.9rem;
  color: var(--bs-secondary);
  background: var(--bs-light);
}

/* Order actions */
.order-actions {
  padding: 1rem 1.5rem;
  display: flex;
  gap: 0.5rem;
  background: var(--bs-light);
}

.action-btn {
  flex: 1;
  padding: 0.75rem;
  border: none;
  border-radius: 6px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  font-size: 0.9rem;
}

.btn-start {
  background: var(--bs-info);
  color: white;
}

.btn-start:hover {
  background: #0fa2bb;
}

.btn-finish {
  background: var(--bs-success);
  color: white;
}

.btn-finish:hover {
  background: #157347;
}

.btn-details {
  background: var(--bs-primary);
  color: white;
}

.btn-details:hover {
  background: #0056b3;
}

/* Responsive design */
@media (max-width: 768px) {
  .chef-header {
    padding: 1rem;
  }
  
  .chef-main {
    padding: 1rem;
  }
  
  .chef-sidebar {
    width: 280px;
    position: fixed;
    top: 0;
    right: 0;
    height: 100vh;
  }
  
  .orders-grid {
    grid-template-columns: 1fr;
    gap: 1rem;
  }
  
  .section-header {
    flex-direction: column;
    gap: 1rem;
    align-items: stretch;
  }
  
  .order-actions {
    flex-direction: column;
  }
}

@media (max-width: 576px) {
  .chef-header {
    padding: 0.75rem;
  }
  
  .chef-main {
    padding: 0.75rem;
  }
  
  .order-card {
    border-radius: 8px;
  }
  
  .order-header,
  .order-info,
  .order-items,
  .order-time,
  .order-actions {
    padding: 0.75rem 1rem;
  }
  
  .chef-sidebar {
    width: 100%;
  }
}

/* ====================================
   LAYOUT SPACING FIX - إصلاح مسافات التخطيط
   إصلاح تداخل المحتوى مع القائمة الجانبية
   ==================================== */

/* Main Content Container - المحتوى الرئيسي */
.manager-main {
  flex: 1 !important;
  min-height: 100vh !important;
  background: #f8f9fa !important;
  transition: all 0.4s cubic-bezier(0.25, 0.8, 0.25, 1) !important;
  position: relative !important;
  overflow-x: hidden !important;
  
  /* Essential padding to prevent sidebar overlap */
  padding: 2rem !important;
  margin-right: 300px !important; /* Space for sidebar when open */
}

/* When sidebar is closed */
.manager-main:not(.sidebar-open) {
  margin-right: 70px !important; /* Space for collapsed sidebar */
}

/* Screen Content Wrapper */
.screen-content {
  width: 100% !important;
  max-width: none !important;
  padding: 0 !important; /* Remove padding since we handle it in parent */
  margin: 0 !important;
  min-height: calc(100vh - 4rem) !important;
  position: relative !important;
  background: transparent !important;
}

/* Dashboard Content Container */
.dashboard-content {
  display: flex !important;
  flex: 1 !important;
  position: relative !important;
  min-height: calc(100vh - 80px) !important; /* Account for header */
  background: #f8f9fa !important;
}

/* Home Screen Specific Spacing */
.homeScreen {
  padding: 0 !important; /* Remove extra padding */
  width: 100% !important;
  min-height: 0 !important;
}

/* Ensure all screen components have proper spacing */
.employees-screen,
.orders-screen,
.tables-screen,
.reports-screen,
.menu-screen,
.categories-screen,
.settings-screen,
.discount-requests-screen,
.inventory-screen {
  padding: 0 !important; /* Let parent handle padding */
  width: 100% !important;
  min-height: 0 !important;
}

/* Bootstrap containers should not have extra margin */
.container-fluid {
  padding-left: 0 !important;
  padding-right: 0 !important;
  margin-left: 0 !important;
  margin-right: 0 !important;
  max-width: none !important;
}

/* Specific fixes for Bootstrap screen containers with padding */
.reports-bootstrap-container.container-fluid,
.discount-requests-bootstrap-container.container-fluid,
.categories-bootstrap-container.container-fluid,
.settings-bootstrap-container.container-fluid {
  padding-left: 0 !important;
  padding-right: 0 !important;
  padding-top: 0 !important;
  padding-bottom: 0 !important;
  margin: 0 !important;
  max-width: none !important;
}

/* Remove Bootstrap py-* classes that add vertical padding */
.py-1, .py-2, .py-3, .py-4, .py-5,
.pt-1, .pt-2, .pt-3, .pt-4, .pt-5,
.pb-1, .pb-2, .pb-3, .pb-4, .pb-5 {
  padding-top: 0 !important;
  padding-bottom: 0 !important;
}

/* Text center elements should maintain their styles */
.text-center {
  text-align: center !important;
}

/* Display elements should maintain their styles */
.display-1, .display-2, .display-3, .display-4, .display-5, .display-6 {
  font-weight: 300 !important;
  line-height: 1.2 !important;
}

/* Responsive grid components should fit properly */
.responsive-grid-container {
  width: 100% !important;
  padding: 0 !important;
  margin: 0 !important;
}

/* Cards within grids should have proper spacing */
.card-body {
  padding: 1.5rem !important;
}

/* Headers in screens should have reduced top margin */
.container-fluid h1,
.container-fluid h2,
.container-fluid h3 {
  margin-top: 0 !important;
}

/* Lead text should maintain proper spacing */
.lead {
  margin-bottom: 1rem !important;
}

/* Filter selects should be properly positioned */
.form-select {
  margin: 0 !important;
}

/* Row elements should not have negative margins */
.row {
  --bs-gutter-x: 1.5rem !important;
  --bs-gutter-y: 0 !important;
}

/* Justify content center elements */
.justify-content-center {
  justify-content: center !important;
}

/* Auto columns */
.col-auto {
  flex: 0 0 auto !important;
  width: auto !important;
}

/* Loading state should center properly */
.loading {
  display: flex !important;
  flex-direction: column !important;
  align-items: center !important;
  justify-content: center !important;
  min-height: 50vh !important;
  text-align: center !important;
  color: #6c757d !important;
  font-size: 1.1rem !important;
  gap: 1rem !important;
}

.loading i {
  font-size: 2rem !important;
  color: #007bff !important;
}

/* Fix sidebar positioning to prevent overlap */
.manager-sidebar {
  position: fixed !important;
  top: 80px !important; /* Below header */
  right: 0 !important;
  height: calc(100vh - 80px) !important;
  z-index: 1000 !important;
  transform: translateX(0) !important;
}

.manager-sidebar.closed {
  transform: translateX(0) !important; /* Keep in place, just narrower */
}

/* Header should stay on top */
.app-header {
  position: fixed !important;
  top: 0 !important;
  left: 0 !important;
  right: 0 !important;
  z-index: 1001 !important;
  height: 80px !important;
}

/* Body should account for fixed header */
.manager-dashboard {
  padding-top: 80px !important;
}

/* Ensure no horizontal scrolling */
body,
html {
  overflow-x: hidden !important;
}

/* Additional fixes for modal positioning */
.modal {
  z-index: 1050 !important;
}

.modal-backdrop {
  z-index: 1040 !important;
}

/* Tooltip positioning */
.tooltip {
  z-index: 1070 !important;
}

/* Dropdown positioning */
.dropdown-menu {
  z-index: 1000 !important;
}

/* Enhanced fixes for better spacing and layout */

/* Ensure main container uses flexbox properly */
.dashboard-content.sidebar-closed .manager-main {
  margin-right: 70px !important;
  transition: margin-right 0.4s cubic-bezier(0.25, 0.8, 0.25, 1) !important;
}

/* Better mobile handling */
@media (max-width: 768px) {
  .manager-sidebar {
    transform: translateX(100%) !important;
    position: fixed !important;
    top: 80px !important;
    right: 0 !important;
    width: 280px !important;
    height: calc(100vh - 80px) !important;
    z-index: 1000 !important;
  }
  
  .manager-sidebar.open {
    transform: translateX(0) !important;
  }
  
  .dashboard-content {
    margin-right: 0 !important;
  }
}

/* Fix for screen headers that might have extra spacing */
.screen-header,
.page-header {
  margin-top: 0 !important;
  padding-top: 0 !important;
}

/* Ensure proper text colors for dark backgrounds */
.text-white {
  color: #ffffff !important;
}

.text-white-50 {
  color: rgba(255, 255, 255, 0.5) !important;
}

/* Button spacing fixes */
.btn {
  margin: 0.25rem !important;
}

.btn-group {
  margin: 0 !important;
}

/* Card spacing improvements */
.card {
  margin-bottom: 1rem !important;
}

/* Table responsive fixes */
.table-responsive {
  border: none !important;
  padding: 0 !important;
  margin: 0 !important;
}

/* Form controls should have proper spacing */
.form-control,
.form-select {
  margin-bottom: 0.5rem !important;
}

/* Alert messages should be properly spaced */
.alert {
  margin-bottom: 1rem !important;
}

/* Badge positioning fixes */
.badge {
  font-size: 0.75em !important;
}

/* Progress bars should be contained */
.progress {
  margin: 0.5rem 0 !important;
}

/* Navbar and navigation fixes */
.nav-tabs,
.nav-pills {
  margin-bottom: 1rem !important;
}

/* List group spacing */
.list-group {
  margin-bottom: 1rem !important;
}

/* Modal content should be properly spaced */
.modal-content {
  border-radius: 0.5rem !important;
}

.modal-body {
  padding: 1.5rem !important;
}

/* Breadcrumb navigation */
.breadcrumb {
  margin-bottom: 1rem !important;
  padding: 0.5rem 0 !important;
  background: transparent !important;
}

/* Icon spacing in text */
.fas,
.far,
.fab {
  margin-left: 0.25rem !important;
  margin-right: 0.25rem !important;
}

/* Responsive image fixes */
.img-fluid {
  max-width: 100% !important;
  height: auto !important;
}

/* Pagination fixes */
.pagination {
  margin: 1rem 0 !important;
  justify-content: center !important;
}

/* Input group fixes */
.input-group {
  margin-bottom: 1rem !important;
}

/* Figure and caption spacing */
.figure {
  margin-bottom: 1rem !important;
}

/* Carousel fixes */
.carousel {
  margin-bottom: 1rem !important;
}

/* NoHeaderLayout.css - Scoped styles for No Header Layout */

/* CSS Custom Properties (Variables) */
/* ?? ????? ????????? ??????? ?????? ??? ????? */

/* Main layout container */
.noHeaderLayout {
  height: 100vh;
  display: flex;
  flex-direction: column;
  font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
}

/* Dashboard content area */
.noHeaderLayout__content {
  flex: 1;
  display: flex;
  height: 100vh;
  overflow: hidden;
}

/* Sidebar navigation */
.noHeaderLayout__sidebar {
  width: 280px;
  background: white;
  color: #333;
  position: fixed;
  top: 0;
  right: 0;
  height: 100vh;
  transition: transform 0.3s ease;
  z-index: 1000;
  display: flex;
  flex-direction: column;
  box-shadow: -2px 0 10px rgba(0, 0, 0, 0.1);
  border-left: 1px solid #e0e0e0;
  overflow-y: auto !important;
  overflow-x: hidden !important;
}

/* Custom scrollbar for sidebar */
.noHeaderLayout__sidebar::-webkit-scrollbar {
  width: 6px;
}

.noHeaderLayout__sidebar::-webkit-scrollbar-thumb {
  background: #ccc;
  border-radius: 3px;
}

.noHeaderLayout__sidebar::-webkit-scrollbar-thumb:hover {
  background: #999;
}

/* Sidebar header */
.noHeaderLayout__sidebar-header {
  padding: 1.5rem 1rem;
  background: #007bff;
  color: white;
  text-align: center;
  position: sticky;
  top: 0;
  z-index: 10;
}

.noHeaderLayout__sidebar-title {
  font-size: 1.2rem;
  font-weight: 600;
  margin: 0;
}

.noHeaderLayout__sidebar-subtitle {
  font-size: 0.9rem;
  opacity: 0.9;
  margin: 0.25rem 0 0 0;
}

/* Sidebar navigation list */
.noHeaderLayout__sidebar-nav {
  flex: 1;
  padding: 1rem 0;
}

.noHeaderLayout__nav-list {
  list-style: none;
  padding: 0;
  margin: 0;
}

.noHeaderLayout__nav-item {
  margin-bottom: 0.25rem;
}

.noHeaderLayout__nav-link {
  display: flex;
  align-items: center;
  padding: 0.75rem 1rem;
  color: #333;
  text-decoration: none;
  transition: all 0.2s ease;
  border-right: 3px solid transparent;
  gap: 0.75rem;
}

.noHeaderLayout__nav-link:hover {
  background: rgba(0, 123, 255, 0.1);
  color: #007bff;
  border-right-color: #007bff;
}

.noHeaderLayout__nav-link--active {
  background: rgba(0, 123, 255, 0.15);
  color: #007bff;
  border-right-color: #007bff;
  font-weight: 500;
}

.noHeaderLayout__nav-icon {
  font-size: 1.1rem;
  width: 20px;
  text-align: center;
}

.noHeaderLayout__nav-text {
  flex: 1;
  font-size: 0.95rem;
}

/* Badge for notifications */
.noHeaderLayout__nav-badge {
  background: #007bff;
  color: white;
  border-radius: 50%;
  width: 18px;
  height: 18px;
  font-size: 0.7rem;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 600;
}

.noHeaderLayout__nav-badge--danger {
  background: #dc3545;
}

.noHeaderLayout__nav-badge--warning {
  background: #ffc107;
  color: #333;
}

.noHeaderLayout__nav-badge--success {
  background: #28a745;
}

/* Sidebar footer */
.noHeaderLayout__sidebar-footer {
  padding: 1rem;
  border-top: 1px solid #e0e0e0;
  background: #f8f9fa;
}

.noHeaderLayout__user-info {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  margin-bottom: 1rem;
}

.noHeaderLayout__user-avatar {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background: #007bff;
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 600;
  font-size: 1.1rem;
}

.noHeaderLayout__user-details {
  flex: 1;
}

.noHeaderLayout__user-name {
  font-size: 0.9rem;
  font-weight: 500;
  color: #333;
  margin: 0;
}

.noHeaderLayout__user-role {
  font-size: 0.8rem;
  color: #6c757d;
  margin: 0;
}

.noHeaderLayout__logout-button {
  width: 100%;
  padding: 0.75rem;
  border: none;
  border-radius: 6px;
  background: #dc3545;
  color: white;
  font-size: 0.9rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
}

.noHeaderLayout__logout-button:hover {
  background: #c82333;
  transform: translateY(-1px);
}

/* Main content area */
.noHeaderLayout__main {
  flex: 1;
  margin-right: 280px;
  background: #f8f9fa;
  overflow-y: auto;
  overflow-x: hidden;
}

/* Mobile sidebar toggle */
.noHeaderLayout__mobile-toggle {
  display: none;
  position: fixed;
  top: 1rem;
  right: 1rem;
  width: 50px;
  height: 50px;
  border-radius: 50%;
  background: #007bff;
  color: white;
  border: none;
  font-size: 1.2rem;
  cursor: pointer;
  z-index: 1001;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.2);
  transition: all 0.2s ease;
}

.noHeaderLayout__mobile-toggle:hover {
  transform: scale(1.05);
}

/* Mobile sidebar overlay */
.noHeaderLayout__overlay {
  display: none;
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.5);
  z-index: 999;
}

.noHeaderLayout__overlay--active {
  display: block;
}

/* Responsive design */
@media (max-width: 768px) {
  .noHeaderLayout__sidebar {
    transform: translateX(100%);
    width: 100%;
    max-width: 320px;
  }
  
  .noHeaderLayout__sidebar--open {
    transform: translateX(0);
  }
  
  .noHeaderLayout__main {
    margin-right: 0;
  }
  
  .noHeaderLayout__mobile-toggle {
    display: flex;
    align-items: center;
    justify-content: center;
  }
  
  .noHeaderLayout__nav-link {
    padding: 1rem 1rem;
    font-size: 1rem;
  }
  
  .noHeaderLayout__nav-icon {
    font-size: 1.2rem;
  }
}

/* Animation for sidebar toggle */
@media (max-width: 768px) {
  .noHeaderLayout__sidebar {
    transition: transform 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  }
}

/* Focus styles for accessibility */
.noHeaderLayout__nav-link:focus,
.noHeaderLayout__logout-button:focus,
.noHeaderLayout__mobile-toggle:focus {
  outline: 2px solid #007bff;
  outline-offset: 2px;
}

/* High contrast mode support */
@media (prefers-contrast: high) {
  .noHeaderLayout__sidebar {
    border-left: 2px solid #333;
  }
  
  .noHeaderLayout__nav-link--active {
    background: #333;
    color: white;
  }
}

/* Reduced motion support */
@media (prefers-reduced-motion: reduce) {
  .noHeaderLayout__sidebar,
  .noHeaderLayout__nav-link,
  .noHeaderLayout__logout-button,
  .noHeaderLayout__mobile-toggle {
    transition: none;
  }
}


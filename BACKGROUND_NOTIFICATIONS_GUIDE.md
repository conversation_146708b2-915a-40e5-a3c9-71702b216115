# دليل تفعيل الإشعارات للصفحات غير النشطة 📱🔔

## نظرة عامة

تم تحسين نظام القهوة ليدعم **الإشعارات حتى لو كانت الصفحة غير نشطة**! هذا يعني أن النادلين والطباخين سيحصلون على الإشعارات والأصوات حتى لو كانوا يستخدمون تطبيقات أخرى أو كانت الشاشة مقفلة.

## المزايا الجديدة ✨

### 1. **Service Worker المحسن**
- عمل في الخلفية حتى لو كانت الصفحة مغلقة
- دعم Push Notifications الأصلية
- استراتيجيات متعددة لضمان وصول الإشعارات

### 2. **خدمة الصوت المطورة**
- **Wake Lock**: منع الجهاز من الدخول في وضع السكون
- **AudioContext**: تشغيل الصوت بطرق متقدمة
- **استراتيجيات متعددة**: HTML Audio، AudioContext، Service Worker
- **تشغيل ذكي**: كشف حالة الصفحة وتكييف طريقة التشغيل

### 3. **إشعارات ذكية**
- إشعارات مرئية عند عدم نشاط الصفحة
- أصوات متكررة للحالات العاجلة
- اهتزاز للهواتف المحمولة
- تنبيهات مخصصة حسب نوع الإشعار

## كيفية التفعيل 🚀

### الخطوة 1: منح الإذونات
عند فتح التطبيق لأول مرة، ستظهر رسائل طلب الإذونات:

1. **إذن الإشعارات** - اضغط "السماح" أو "Allow"
2. **إذن تثبيت Service Worker** - اضغط "موافق"
3. **إذن منع السكون** (اختياري) - اضغط "السماح"

### الخطوة 2: اختبار النظام
- بعد تسجيل الدخول، ستحصل على إشعار اختبار يؤكد عمل النظام
- جرب فتح تطبيق آخر أو قفل الشاشة
- اطلب من زميل إرسال طلب لاختبار وصول الإشعارات

### الخطوة 3: الاستخدام اليومي
- **للنادلين**: ستصل إشعارات عند جهوزية الطلبات
- **للطباخين**: ستصل إشعارات عند وصول طلبات جديدة
- **للمديرين**: ستصل إشعارات الأحداث المهمة

## أنواع الإشعارات 📋

### للنادلين 👨‍💼
| الحالة | الصوت | التكرار | الاهتزاز | الإشعار المرئي |
|--------|-------|---------|----------|----------------|
| طلب جاهز | عالي | 2-3 مرات | قوي | ✅ |
| بدء التحضير | متوسط | مرة واحدة | خفيف | ✅ |
| انقطاع الاتصال | عالي | مرة واحدة | متوسط | ✅ |

### للطباخين 👨‍🍳
| الحالة | الصوت | التكرار | الاهتزاز | الإشعار المرئي |
|--------|-------|---------|----------|----------------|
| طلب جديد | عالي | 2-3 مرات | قوي | ✅ |
| تحديث حالة | منخفض | مرة واحدة | - | ✅ |
| انقطاع الاتصال | عالي | مرة واحدة | متوسط | ✅ |

## استراتيجيات التشغيل 🛠️

النظام يستخدم **4 استراتيجيات** لضمان تشغيل الصوت:

### 1. HTML Audio (الأساسية)
```javascript
audio.play() // الطريقة التقليدية
```

### 2. AudioContext (المتقدمة)
```javascript
AudioContext.createBufferSource() // تحكم متقدم
```

### 3. Service Worker (الخلفية)
```javascript
serviceWorker.postMessage() // عمل في الخلفية
```

### 4. Visibility Trick (الحيلة الذكية)
```javascript
// خداع المتصفح مؤقتاً
document.hidden = false
```

## المتصفحات المدعومة 🌐

| المتصفح | Push Notifications | Audio في الخلفية | Service Worker | Wake Lock |
|---------|-------------------|-------------------|----------------|-----------|
| Chrome | ✅ | ✅ | ✅ | ✅ |
| Firefox | ✅ | ✅ | ✅ | ❌ |
| Safari | ✅ | ⚠️ | ✅ | ❌ |
| Edge | ✅ | ✅ | ✅ | ✅ |

> **ملاحظة**: Safari يتطلب إعدادات خاصة للإشعارات

## استكشاف الأخطاء وإصلاحها 🔧

### المشكلة: لا تصل الإشعارات
**الحلول:**
1. تحقق من منح إذن الإشعارات في إعدادات المتصفح
2. تأكد من تفعيل JavaScript
3. جرب إعادة تحميل الصفحة
4. تحقق من اتصال الإنترنت

### المشكلة: لا يعمل الصوت
**الحلول:**
1. تفاعل مع الصفحة أولاً (اضغط أي زر)
2. تحقق من مستوى الصوت في الجهاز
3. تأكد من عدم كتم الصوت في المتصفح
4. جرب متصفح آخر

### المشكلة: الإشعارات تتوقف عند قفل الشاشة
**الحلول:**
1. تفعيل "البقاء متيقظاً" في إعدادات الهاتف
2. إضافة الموقع للتطبيقات المسموحة في الخلفية
3. تعطيل "وضع توفير البطارية"

## الإعدادات المتقدمة ⚙️

### تخصيص مستوى الصوت
```javascript
notificationSound.setVolume(0.8); // 80%
```

### تخصيص عدد التكرارات
```javascript
notificationSound.playNotificationAdvanced({
  volume: 0.9,
  repeat: 3,
  urgent: true
});
```

### إيقاف/تشغيل الصوت
```javascript
notificationSound.setSoundEnabled(false); // إيقاف
notificationSound.setSoundEnabled(true);  // تشغيل
```

## نصائح لأفضل تجربة 💡

### للهواتف المحمولة 📱
1. **أضف الموقع للشاشة الرئيسية** كتطبيق PWA
2. **فعّل الاهتزاز** في إعدادات الهاتف
3. **اتركاشة مفتوحة** حتى لو استخدمت تطبيقات أخرى

### للحاسوب 💻
1. **اترك نافذة المتصفح مفتوحة** (يمكن تصغيرها)
2. **فعّل الإشعارات** في إعدادات النظام
3. **استخدم سماعات** للحصول على أفضل جودة صوت

### للفرق 👥
1. **اختبر النظام يومياً** في بداية كل وردية
2. **أبلغ عن أي مشاكل** فوراً للدعم التقني
3. **تأكد من شحن الهواتف** لتجنب انقطاع الخدمة

## الدعم والمساعدة 🆘

إذا واجهت أي مشاكل:

1. **راجع هذا الدليل** للحلول الشائعة
2. **جرب إعادة تحميل الصفحة**
3. **تحقق من console.log** في أدوات المطور
4. **اتصل بالدعم التقني** مع تفاصيل المشكلة

---

## الخلاصة ✅

✅ **إشعارات تعمل حتى لو كانت الصفحة غير نشطة**  
✅ **أصوات متقدمة مع استراتيجيات متعددة**  
✅ **دعم جميع المتصفحات الحديثة**  
✅ **تحسينات خاصة للهواتف المحمولة**  
✅ **إعدادات قابلة للتخصيص**  

**نظام القهوة الآن جاهز للعمل 24/7 بإشعارات لا تتوقف!** ☕🔔

// مساعد لتحسين الأداء - Debouncing والتحكم في الطلبات المتكررة

export class PerformanceOptimizer {
  private debounceTimers: Map<string, NodeJS.Timeout> = new Map();
  private lastExecutionTimes: Map<string, number> = new Map();
  
  /**
   * Debounce function - تنفيذ الدالة مرة واحدة فقط بعد انتهاء فترة الانتظار
   */
  debounce<T extends (...args: any[]) => any>(
    key: string,
    func: T,
    wait: number = 1000
  ): (...args: Parameters<T>) => void {
    return (...args: Parameters<T>) => {
      // إلغاء المؤقت السابق
      const existingTimer = this.debounceTimers.get(key);
      if (existingTimer) {
        clearTimeout(existingTimer);
      }

      // إنشاء مؤقت جديد
      const timer = setTimeout(() => {
        func(...args);
        this.debounceTimers.delete(key);
      }, wait);

      this.debounceTimers.set(key, timer);
    };
  }

  /**
   * Throttle function - تحديد معدل تنفيذ الدالة
   */
  throttle<T extends (...args: any[]) => any>(
    key: string,
    func: T,
    limit: number = 2000
  ): (...args: Parameters<T>) => void {
    return (...args: Parameters<T>) => {
      const now = Date.now();
      const lastExecution = this.lastExecutionTimes.get(key) || 0;

      if (now - lastExecution >= limit) {
        func(...args);
        this.lastExecutionTimes.set(key, now);
      }
    };
  }

  /**
   * تنظيف المؤقتات
   */
  cleanup(): void {
    this.debounceTimers.forEach((timer) => clearTimeout(timer));
    this.debounceTimers.clear();
    this.lastExecutionTimes.clear();
  }
}

// Global instance
export const globalPerformanceOptimizer = new PerformanceOptimizer();

/**
 * Hook لاستخدام Debouncing في React components
 */
export function useDebounce<T>(value: T, delay: number): T {
  const [debouncedValue, setDebouncedValue] = React.useState<T>(value);

  React.useEffect(() => {
    const handler = setTimeout(() => {
      setDebouncedValue(value);
    }, delay);

    return () => {
      clearTimeout(handler);
    };
  }, [value, delay]);

  return debouncedValue;
}

/**
 * معالج طلبات API محسن مع Debouncing
 */
export class OptimizedAPIHandler {
  private pendingRequests: Map<string, Promise<any>> = new Map();
  private performanceOptimizer = new PerformanceOptimizer();

  /**
   * تنفيذ طلب API مع منع التكرار
   */
  async makeRequest<T>(
    key: string,
    requestFn: () => Promise<T>,
    options: {
      debounceMs?: number;
      cacheMs?: number;
      preventDuplicate?: boolean;
    } = {}
  ): Promise<T> {
    const {
      debounceMs = 1000,
      cacheMs = 30000,
      preventDuplicate = true
    } = options;

    // منع الطلبات المكررة
    if (preventDuplicate && this.pendingRequests.has(key)) {
      console.log(`🔄 Reusing pending request: ${key}`);
      return this.pendingRequests.get(key)!;
    }

    // إنشاء الطلب
    const requestPromise = this.performanceOptimizer.throttle(
      key,
      async () => {
        try {
          console.log(`📡 Making optimized API request: ${key}`);
          const result = await requestFn();
          return result;
        } finally {
          // إزالة من الطلبات المعلقة
          this.pendingRequests.delete(key);
        }
      },
      debounceMs
    );

    // حفظ الطلب في المعلقة
    if (preventDuplicate) {
      this.pendingRequests.set(key, requestPromise());
      return this.pendingRequests.get(key)!;
    }

    return requestPromise();
  }

  /**
   * تنظيف الذاكرة
   */
  cleanup(): void {
    this.pendingRequests.clear();
    this.performanceOptimizer.cleanup();
  }
}

// Global instance
export const globalAPIHandler = new OptimizedAPIHandler();

/**
 * مساعد لتحسين Socket.IO events
 */
export class SocketEventOptimizer {
  private eventDebounces: Map<string, NodeJS.Timeout> = new Map();
  private eventCounts: Map<string, number> = new Map();

  /**
   * معالج مُحسن لأحداث Socket.IO
   */
  optimizedHandler(
    eventName: string,
    handler: (...args: any[]) => void,
    options: {
      debounceMs?: number;
      maxEventsPerSecond?: number;
    } = {}
  ): (...args: any[]) => void {
    const { debounceMs = 500, maxEventsPerSecond = 5 } = options;

    return (...args: any[]) => {
      // عد الأحداث
      const currentCount = this.eventCounts.get(eventName) || 0;
      this.eventCounts.set(eventName, currentCount + 1);

      // إعادة تعيين العداد كل ثانية
      setTimeout(() => {
        this.eventCounts.set(eventName, 0);
      }, 1000);

      // تحديد معدل الأحداث
      if (currentCount >= maxEventsPerSecond) {
        console.warn(`⚠️ Too many ${eventName} events per second: ${currentCount}`);
        return;
      }

      // Debouncing
      const existingTimeout = this.eventDebounces.get(eventName);
      if (existingTimeout) {
        clearTimeout(existingTimeout);
      }

      const timeout = setTimeout(() => {
        handler(...args);
        this.eventDebounces.delete(eventName);
      }, debounceMs);

      this.eventDebounces.set(eventName, timeout);
    };
  }

  /**
   * تنظيف المؤقتات
   */
  cleanup(): void {
    this.eventDebounces.forEach((timeout) => clearTimeout(timeout));
    this.eventDebounces.clear();
    this.eventCounts.clear();
  }
}

// Global instance
export const globalSocketOptimizer = new SocketEventOptimizer();

import React from 'react';

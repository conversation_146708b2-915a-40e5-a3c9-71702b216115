import React, { useState, useEffect, useCallback, useMemo } from 'react';
import { authenticatedGet, authenticatedPost, authenticatedPut, authenticatedDelete } from '../utils/apiHelpers';
import { useToast } from '../hooks/useToast';
// استدعاء ملف CSS الجديد المنظم
import '../styles/manager/TablesManagerScreen.css';

interface Order {
  _id: string;
  orderNumber: string;
  items: any[];
  totalAmount?: number;
  totalPrice?: number;
  totals?: {
    subtotal: number;
    tax: number;
    discount: number;
    total: number;
  };
  status: 'pending' | 'preparing' | 'ready' | 'completed' | 'delivered';
  tableNumber?: number;
  customerName?: string;
  waiterName?: string;
  waiterId?: string;
  chefName?: string;
  staff?: {
    waiter?: string;
    chef?: string;
  };
  createdAt: string;
  updatedAt: string;
}

interface TableAccount {
  _id: string;
  tableNumber: string;
  waiterName: string;
  totalAmount: number;
  isOpen: boolean;
  status: 'active' | 'closed';
  orders: Order[];
  createdAt: string;
  ordersCount?: number;
  activeOrdersCount?: number;
  totalSales?: number;
  capacity?: number;
  section?: string;
  lastOrderTime?: string;
  avgOrderTime?: number;
}

interface TablesManagerScreenProps {
  tableAccounts: TableAccount[];
  orders: Order[];
  onTablesUpdate: (tables: TableAccount[]) => void;
  onShowTableDetails: (table: TableAccount) => void;
  loading: boolean;
}

const TablesManagerScreen: React.FC<TablesManagerScreenProps> = ({
  tableAccounts,
  orders,
  onTablesUpdate,
  onShowTableDetails,
  loading
}) => {
  const [tablesScreenSearchTerm, setTablesScreenSearchTerm] = useState('');
  const [tablesScreenStatusFilter, setTablesScreenStatusFilter] = useState<'all' | 'active' | 'closed'>('all');
  const [tablesScreenWaiterFilter, setTablesScreenWaiterFilter] = useState<string>('all');
  const [sortBy, setSortBy] = useState<'tableNumber' | 'totalAmount' | 'ordersCount' | 'lastActivity'>('tableNumber');
  const [sortOrder, setSortOrder] = useState<'asc' | 'desc'>('asc');
  const [viewMode, setViewMode] = useState<'grid' | 'list'>('grid');
  const [autoRefresh, setAutoRefresh] = useState(false);
  const [refreshInterval, setRefreshInterval] = useState<NodeJS.Timeout | null>(null);

  const toast = useToast();

  // Auto refresh functionality
  useEffect(() => {
    if (autoRefresh) {
      const interval = setInterval(() => {
        // Trigger refresh of data
        window.location.reload();
      }, 30000); // Refresh every 30 seconds
      setRefreshInterval(interval);
    } else {
      if (refreshInterval) {
        clearInterval(refreshInterval);
        setRefreshInterval(null);
      }
    }

    return () => {
      if (refreshInterval) {
        clearInterval(refreshInterval);
      }
    };
  }, [autoRefresh, refreshInterval]);

  // Get unique waiters from table accounts - مُحسن بـ useMemo
  const waiters = useMemo(() => 
    Array.from(new Set(
      tableAccounts
        .map(table => table.waiterName)
        .filter(name => name && name.trim() !== '')
    )), [tableAccounts]
  );

  // Helper functions - محسنة بـ useCallback
  const getTableTotalAmount = useCallback((table: TableAccount) => {
    return table.totalSales || table.totalAmount || 0;
  }, []);

  const getTableOrdersCount = useCallback((table: TableAccount) => {
    return table.ordersCount || table.orders?.length || 0;
  }, []);

  // التعامل مع إغلاق وإعادة فتح الطاولات

  const handleTablesScreenCloseTable = async (tableId: string) => {
    if (!window.confirm('هل أنت متأكد من إغلاق هذه الطاولة؟')) return;
    
    try {
      const response = await authenticatedPut(`/api/table-accounts/${tableId}/close`, {});
      if (response.success) {
        const updatedTables = tableAccounts.map(table => 
          table._id === tableId ? { ...table, isOpen: false, status: 'closed' as const } : table
        );
        onTablesUpdate(updatedTables);
        toast.showSuccess('تم إغلاق الطاولة بنجاح');
      }
    } catch (error) {
      toast.showError('حدث خطأ أثناء إغلاق الطاولة');
    }
  };

  const handleTablesScreenReopenTable = async (tableId: string) => {
    if (!window.confirm('هل أنت متأكد من إعادة فتح هذه الطاولة؟')) return;
    
    try {
      const response = await authenticatedPut(`/api/table-accounts/${tableId}/reopen`, {});
      if (response.success) {
        const updatedTables = tableAccounts.map(table => 
          table._id === tableId ? { ...table, isOpen: true, status: 'active' as const } : table
        );
        onTablesUpdate(updatedTables);
        toast.showSuccess('تم إعادة فتح الطاولة بنجاح');
      }
    } catch (error) {
      toast.showError('حدث خطأ أثناء إعادة فتح الطاولة');
    }
  };

  const getTableActiveOrdersCount = useCallback((table: TableAccount) => {
    if (table.activeOrdersCount !== undefined) {
      return table.activeOrdersCount;
    }
    
    // Calculate active orders from orders array
    const tableOrders = orders.filter(order => 
      order.tableNumber?.toString() === table.tableNumber.toString() &&
      ['pending', 'preparing', 'ready'].includes(order.status)
    );
    
    return tableOrders.length;
  }, [orders]);

  // Sort and filter tables - مُحسن بـ useMemo
  const filteredTables = useMemo(() => {
    let filtered = tableAccounts.filter(table => {
      const matchesSearch = table.tableNumber?.toString().includes(tablesScreenSearchTerm) ||
                           table.waiterName?.toLowerCase().includes(tablesScreenSearchTerm.toLowerCase());
      const matchesStatus = tablesScreenStatusFilter === 'all' ||
                           (tablesScreenStatusFilter === 'active' && table.isOpen) ||
                           (tablesScreenStatusFilter === 'closed' && !table.isOpen);
      const matchesWaiter = tablesScreenWaiterFilter === 'all' || table.waiterName === tablesScreenWaiterFilter;
      
      return matchesSearch && matchesStatus && matchesWaiter;
    });

    // Sort tables
    filtered.sort((a, b) => {
      let aValue, bValue;
      
      switch (sortBy) {
        case 'tableNumber':
          aValue = Number(a.tableNumber);
          bValue = Number(b.tableNumber);
          break;
        case 'totalAmount':
          aValue = getTableTotalAmount(a);
          bValue = getTableTotalAmount(b);
          break;
        case 'ordersCount':
          aValue = getTableOrdersCount(a);
          bValue = getTableOrdersCount(b);
          break;
        case 'lastActivity':
          aValue = new Date(a.lastOrderTime || a.createdAt).getTime();
          bValue = new Date(b.lastOrderTime || b.createdAt).getTime();
          break;
        default:
          aValue = Number(a.tableNumber);
          bValue = Number(b.tableNumber);
      }

      if (sortOrder === 'asc') {
        return aValue < bValue ? -1 : aValue > bValue ? 1 : 0;
      } else {
        return aValue > bValue ? -1 : aValue < bValue ? 1 : 0;
      }
    });

    return filtered;
  }, [tableAccounts, tablesScreenSearchTerm, tablesScreenStatusFilter, tablesScreenWaiterFilter, sortBy, sortOrder, getTableTotalAmount, getTableOrdersCount]);

  const getTableLastActivity = useCallback((table: TableAccount) => {
    if (table.lastOrderTime) {
      return new Date(table.lastOrderTime);
    }
    return new Date(table.createdAt);
  }, []);

  const getTableDuration = useCallback((table: TableAccount) => {
    if (!table.isOpen) return null;
    
    const start = new Date(table.createdAt);
    const now = new Date();
    const duration = now.getTime() - start.getTime();
    
    const hours = Math.floor(duration / (1000 * 60 * 60));
    const minutes = Math.floor((duration % (1000 * 60 * 60)) / (1000 * 60));
    
    if (hours > 0) {
      return `${hours}س ${minutes}د`;
    }
    return `${minutes}د`;
  }, []);

  const getTablePriority = useCallback((table: TableAccount) => {
    const activeOrders = getTableActiveOrdersCount(table);
    const duration = table.isOpen ? (new Date().getTime() - new Date(table.createdAt).getTime()) / (1000 * 60) : 0;
    
    if (activeOrders >= 3 || duration > 120) return 'high';
    if (activeOrders >= 2 || duration > 60) return 'medium';
    return 'low';
  }, [getTableActiveOrdersCount]);

  const exportTableData = useCallback(() => {
    const csvContent = [
      ['رقم الطاولة', 'النادل', 'الحالة', 'عدد الطلبات', 'إجمالي المبلغ', 'تاريخ الإنشاء'],
      ...filteredTables.map(table => [
        table.tableNumber,
        table.waiterName || 'غير محدد',
        table.isOpen ? 'نشطة' : 'مغلقة',
        getTableOrdersCount(table),
        getTableTotalAmount(table).toFixed(2),
        new Date(table.createdAt).toLocaleDateString('ar-EG')
      ])
    ].map(row => row.join(',')).join('\n');
    
    const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
    const link = document.createElement('a');
    link.href = URL.createObjectURL(blob);
    link.download = `tables_report_${new Date().toISOString().split('T')[0]}.csv`;
    link.click();
    toast.showSuccess('تم تصدير البيانات بنجاح');
  }, [filteredTables, getTableOrdersCount, getTableTotalAmount, toast]);

  return (
    <div className="tables-manager-screen">
      <div className="tables-manager-screen-header">
        <div className="tables-manager-screen-title">
          <h2>إدارة الطاولات</h2>
          <p>متابعة وإدارة حسابات الطاولات</p>
        </div>
        
        {/* Quick Actions */}
        <div className="quick-actions">
          <div className="quick-action-item">
            <span className="quick-label">طاولات تحتاج انتباه:</span>
            <span className="quick-value high-priority">
              {filteredTables.filter(table => getTablePriority(table) === 'high').length}
            </span>
          </div>
          <div className="quick-action-item">
            <span className="quick-label">متوسط وقت الخدمة:</span>
            <span className="quick-value">
              {tableAccounts.filter(table => table.avgOrderTime).length > 0
                ? `${Math.round(tableAccounts.reduce((sum, table) => sum + (table.avgOrderTime || 0), 0) / tableAccounts.filter(table => table.avgOrderTime).length)}د`
                : 'غير متاح'
              }
            </span>
          </div>
          {autoRefresh && (
            <div className="quick-action-item auto-refresh-status">
              <span className="quick-label">التحديث التلقائي:</span>
              <span className="quick-value active">
                <i className="fas fa-sync-alt fa-spin"></i>
                نشط
              </span>
            </div>
          )}
        </div>
      </div>

      {/* Enhanced Filters and Controls */}
      <div className="tables-manager-screen-controls">
        <div className="tables-manager-screen-filters">
          <div className="tables-manager-screen-search">
            <i className="fas fa-search"></i>
            <input
              type="text"
              placeholder="البحث عن طاولة أو نادل..."
              value={tablesScreenSearchTerm}
              onChange={(e) => setTablesScreenSearchTerm(e.target.value)}
            />
            {tablesScreenSearchTerm && (
              <button 
                className="clear-search"
                onClick={() => setTablesScreenSearchTerm('')}
                title="مسح البحث"
              >
                <i className="fas fa-times"></i>
              </button>
            )}
          </div>
          
          <div className="tables-manager-screen-filter-group">
            <select
              value={tablesScreenStatusFilter}
              onChange={(e) => setTablesScreenStatusFilter(e.target.value as any)}
              title="تصفية حسب حالة الطاولة"
            >
              <option value="all">جميع الحالات</option>
              <option value="active">نشطة</option>
              <option value="closed">مغلقة</option>
            </select>
            
            <select
              value={tablesScreenWaiterFilter}
              onChange={(e) => setTablesScreenWaiterFilter(e.target.value)}
              title="تصفية حسب النادل"
            >
              <option value="all">جميع النُدُل</option>
              {waiters.map(waiter => (
                <option key={waiter} value={waiter}>{waiter}</option>
              ))}
            </select>

            <select
              value={sortBy}
              onChange={(e) => setSortBy(e.target.value as any)}
              title="ترتيب حسب"
            >
              <option value="tableNumber">رقم الطاولة</option>
              <option value="totalAmount">المبلغ الإجمالي</option>
              <option value="ordersCount">عدد الطلبات</option>
              <option value="lastActivity">آخر نشاط</option>
            </select>

            <button
              className={`sort-order-btn ${sortOrder === 'asc' ? 'asc' : 'desc'}`}
              onClick={() => setSortOrder(sortOrder === 'asc' ? 'desc' : 'asc')}
              title={sortOrder === 'asc' ? 'ترتيب تصاعدي' : 'ترتيب تنازلي'}
            >
              <i className={`fas fa-sort-${sortOrder === 'asc' ? 'up' : 'down'}`}></i>
            </button>
          </div>
        </div>

        <div className="tables-manager-screen-view-controls">
          <div className="view-mode-toggle">
            <button
              className={`view-btn ${viewMode === 'grid' ? 'active' : ''}`}
              onClick={() => setViewMode('grid')}
              title="عرض شبكي"
            >
              <i className="fas fa-th"></i>
            </button>
            <button
              className={`view-btn ${viewMode === 'list' ? 'active' : ''}`}
              onClick={() => setViewMode('list')}
              title="عرض قائمة"
            >
              <i className="fas fa-list"></i>
            </button>
          </div>

          <div className="auto-refresh-toggle">
            <label className="toggle-switch">
              <input
                type="checkbox"
                checked={autoRefresh}
                onChange={(e) => setAutoRefresh(e.target.checked)}
                aria-label="تفعيل التحديث التلقائي"
              />
              <span className="toggle-slider">
                <i className="fas fa-sync-alt"></i>
              </span>
            </label>
            <span className="toggle-label">تحديث تلقائي</span>
          </div>

          <div className="action-buttons">
            <button
              className="export-btn"
              onClick={exportTableData}
              title="تصدير البيانات"
            >
              <i className="fas fa-download"></i>
              تصدير
            </button>
            
            <button
              className="refresh-btn"
              onClick={() => window.location.reload()}
              title="تحديث البيانات"
            >
              <i className="fas fa-refresh"></i>
              تحديث
            </button>
          </div>
        </div>
      </div>

      {/* Enhanced Statistics Cards */}
      <div className="tables-manager-screen-stats">
        <div className="tables-manager-screen-stat-card total">
          <div className="tables-manager-screen-stat-icon">
            <i className="fas fa-table"></i>
          </div>
          <div className="tables-manager-screen-stat-content">
            <h3>{tableAccounts.length}</h3>
            <p>إجمالي الطاولات</p>
            <div className="stat-trend">
              <small>الكل متاح</small>
            </div>
          </div>
        </div>
        
        <div className="tables-manager-screen-stat-card active">
          <div className="tables-manager-screen-stat-icon">
            <i className="fas fa-door-open"></i>
          </div>
          <div className="tables-manager-screen-stat-content">
            <h3>{tableAccounts.filter(table => table.isOpen).length}</h3>
            <p>الطاولات النشطة</p>
            <div className="stat-trend">
              <small>
                {tableAccounts.length > 0 
                  ? `${((tableAccounts.filter(table => table.isOpen).length / tableAccounts.length) * 100).toFixed(1)}% من الإجمالي`
                  : 'لا توجد طاولات'
                }
              </small>
            </div>
          </div>
        </div>
        
        <div className="tables-manager-screen-stat-card closed">
          <div className="tables-manager-screen-stat-icon">
            <i className="fas fa-door-closed"></i>
          </div>
          <div className="tables-manager-screen-stat-content">
            <h3>{tableAccounts.filter(table => !table.isOpen).length}</h3>
            <p>الطاولات المغلقة</p>
            <div className="stat-trend">
              <small>
                {tableAccounts.length > 0 
                  ? `${((tableAccounts.filter(table => !table.isOpen).length / tableAccounts.length) * 100).toFixed(1)}% من الإجمالي`
                  : 'لا توجد طاولات'
                }
              </small>
            </div>
          </div>
        </div>
        
        <div className="tables-manager-screen-stat-card revenue">
          <div className="tables-manager-screen-stat-icon">
            <i className="fas fa-money-bill-wave"></i>
          </div>
          <div className="tables-manager-screen-stat-content">
            <h3>{tableAccounts.reduce((sum, table) => sum + getTableTotalAmount(table), 0).toFixed(2)}</h3>
            <p>إجمالي المبيعات (ج.م)</p>
            <div className="stat-trend">
              <small>
                متوسط الطاولة: {tableAccounts.length > 0 
                  ? (tableAccounts.reduce((sum, table) => sum + getTableTotalAmount(table), 0) / tableAccounts.length).toFixed(2)
                  : '0.00'
                } ج.م
              </small>
            </div>
          </div>
        </div>

        <div className="tables-manager-screen-stat-card orders">
          <div className="tables-manager-screen-stat-icon">
            <i className="fas fa-receipt"></i>
          </div>
          <div className="tables-manager-screen-stat-content">
            <h3>{tableAccounts.reduce((sum, table) => sum + getTableActiveOrdersCount(table), 0)}</h3>
            <p>الطلبات النشطة</p>
            <div className="stat-trend">
              <small>
                إجمالي الطلبات: {tableAccounts.reduce((sum, table) => sum + getTableOrdersCount(table), 0)}
              </small>
            </div>
          </div>
        </div>

        <div className="tables-manager-screen-stat-card waiters">
          <div className="tables-manager-screen-stat-icon">
            <i className="fas fa-users"></i>
          </div>
          <div className="tables-manager-screen-stat-content">
            <h3>{waiters.length}</h3>
            <p>النُدُل النشطون</p>
            <div className="stat-trend">
              <small>يخدمون الطاولات</small>
            </div>
          </div>
        </div>
      </div>

      {/* Tables Grid/List */}
      <div className={`tables-manager-screen-grid ${viewMode}`}>
        {loading ? (
          <div className="tables-manager-screen-loading">
            <div className="loading-spinner">
              <i className="fas fa-spinner fa-spin"></i>
            </div>
            <p>جاري تحميل بيانات الطاولات...</p>
          </div>
        ) : tableAccounts.length === 0 ? (
          <div className="tables-manager-screen-empty-state">
            <div className="empty-icon">
              <i className="fas fa-table"></i>
            </div>
            <h3>لا توجد طاولات</h3>
            <p>لم يتم إنشاء أي طاولات بعد. سيتم إنشاء الطاولات تلقائياً عند إنشاء أول طلب.</p>
            <div className="debug-info">
              <small>
                <strong>معلومات التشخيص:</strong><br/>
                عدد الطاولات: {tableAccounts.length}<br/>
                عدد الطلبات: {orders.length}<br/>
                حالة التحميل: {loading ? 'جاري التحميل' : 'تم التحميل'}
              </small>
            </div>
          </div>
        ) : filteredTables.length === 0 ? (
          <div className="tables-manager-screen-no-data">
            <i className="fas fa-search"></i>
            <h3>لا توجد نتائج</h3>
            <p>لا توجد طاولات مطابقة لمعايير البحث والفلترة الحالية</p>
            <div className="filter-summary">
              <small>
                المرشحات النشطة:<br/>
                الحالة: {tablesScreenStatusFilter === 'all' ? 'الكل' : tablesScreenStatusFilter}<br/>
                النادل: {tablesScreenWaiterFilter === 'all' ? 'الكل' : tablesScreenWaiterFilter}<br/>
                البحث: {tablesScreenSearchTerm || 'بدون بحث'}
              </small>
            </div>
          </div>
        ) : (
          <>
            {filteredTables.map(table => {
              const priority = getTablePriority(table);
              const duration = getTableDuration(table);
              const lastActivity = getTableLastActivity(table);
              
              return (
                <div 
                  key={table._id}
                  className={`tables-manager-screen-table-card ${table.isOpen ? 'active' : 'closed'} priority-${priority}`}
                >
                  <div className="tables-manager-screen-table-header">
                    <div className="tables-manager-screen-table-number">
                      <i className="fas fa-table"></i>
                      <span>طاولة {table.tableNumber}</span>
                      {table.capacity && (
                        <span className="table-capacity">
                          <i className="fas fa-users"></i>
                          {table.capacity}
                        </span>
                      )}
                    </div>
                    <div className="table-status-row">
                      <div className={`tables-manager-screen-table-status ${table.isOpen ? 'active' : 'closed'}`}>
                        <i className={`fas ${table.isOpen ? 'fa-circle' : 'fa-circle'}`}></i>
                        {table.isOpen ? 'نشطة' : 'مغلقة'}
                      </div>
                      {priority !== 'low' && (
                        <div className={`priority-indicator priority-${priority}`}>
                          <i className="fas fa-exclamation-triangle"></i>
                          {priority === 'high' ? 'عالي' : 'متوسط'}
                        </div>
                      )}
                    </div>
                  </div>

                  <div className="tables-manager-screen-table-info">
                    <div className="tables-manager-screen-table-waiter">
                      <i className="fas fa-user"></i>
                      <span>{table.waiterName || 'غير محدد'}</span>
                      {table.section && (
                        <span className="table-section">
                          <i className="fas fa-building"></i>
                          {table.section}
                        </span>
                      )}
                    </div>
                    
                    <div className="tables-manager-screen-table-stats">
                      <div className="tables-manager-screen-table-stat">
                        <span className="tables-manager-screen-stat-value">{getTableOrdersCount(table)}</span>
                        <span className="tables-manager-screen-stat-label">إجمالي الطلبات</span>
                      </div>
                      
                      <div className="tables-manager-screen-table-stat">
                        <span className="tables-manager-screen-stat-value active-orders">{getTableActiveOrdersCount(table)}</span>
                        <span className="tables-manager-screen-stat-label">طلبات نشطة</span>
                      </div>
                    </div>
                    
                    <div className="tables-manager-screen-table-amount">
                      <span className="tables-manager-screen-amount-value">
                        {getTableTotalAmount(table).toFixed(2)} ج.م
                      </span>
                      <span className="tables-manager-screen-amount-label">إجمالي المبلغ</span>
                    </div>
                    
                    <div className="table-timing-info">
                      <div className="tables-manager-screen-table-date">
                        <i className="fas fa-calendar"></i>
                        <span>{new Date(table.createdAt).toLocaleDateString('ar-EG')}</span>
                      </div>
                      {duration && (
                        <div className="table-duration">
                          <i className="fas fa-clock"></i>
                          <span>مفتوحة منذ: {duration}</span>
                        </div>
                      )}
                      <div className="table-last-activity">
                        <i className="fas fa-history"></i>
                        <span>آخر نشاط: {lastActivity.toLocaleTimeString('ar-EG', { hour: '2-digit', minute: '2-digit' })}</span>
                      </div>
                    </div>
                  </div>

                  <div className="tables-manager-screen-table-actions">
                    <button
                      className="tables-manager-screen-action-btn details"
                      onClick={() => onShowTableDetails(table)}
                      title="عرض التفاصيل"
                    >
                      <i className="fas fa-eye"></i>
                      التفاصيل
                    </button>
                    
                    {table.isOpen ? (
                      <button
                        className="tables-manager-screen-action-btn close"
                        onClick={() => handleTablesScreenCloseTable(table._id)}
                        title="إغلاق الطاولة"
                      >
                        <i className="fas fa-door-closed"></i>
                        إغلاق
                      </button>
                    ) : (
                      <button
                        className="tables-manager-screen-action-btn reopen"
                        onClick={() => handleTablesScreenReopenTable(table._id)}
                        title="إعادة فتح"
                      >
                        <i className="fas fa-door-open"></i>
                        إعادة فتح
                      </button>
                    )}
                  </div>
                </div>
              );
            })}
          </>
        )}
      </div>
    </div>
  );
};

export default TablesManagerScreen;

const fetch = require('node-fetch');

class TablesScreenTester {
  constructor() {
    this.baseURL = 'https://deshacoffee-production.up.railway.app';
    this.frontendURL = 'https://desha-coffee.vercel.app';
    this.waiterId = '************************'; // Bosy
    this.waiterName = 'بوسي';
    this.testResults = [];
    
    // معلومات المصادقة للاختبار
    this.authHeaders = {
      'Content-Type': 'application/json',
      'Authorization': 'Bearer test-token-for-bosy-waiter'
    };
  }

  // إضافة نتيجة اختبار
  addResult(testName, passed, details = '', expected = '', actual = '') {
    const result = {
      test: testName,
      status: passed ? '✅ نجح' : '❌ فشل',
      details,
      expected,
      actual,
      timestamp: new Date().toLocaleString('ar-EG')
    };
    this.testResults.push(result);
    console.log(`${result.status} ${testName}`);
    if (details) console.log(`   📝 ${details}`);
    if (!passed && expected) console.log(`   🎯 المتوقع: ${expected}`);
    if (!passed && actual) console.log(`   📊 الفعلي: ${actual}`);
    console.log('');
  }

  // فحص اتصال الـ Backend
  async testBackendConnection() {
    try {
      const response = await fetch(`${this.baseURL}/api/health`);
      const health = await response.json();
      
      console.log('🔍 استجابة health check:', health);
      
      const isHealthy = health.status === 'healthy' && health.database?.connected === true;
      this.addResult(
        'اتصال Backend', 
        isHealthy,
        `الحالة: ${health.status}, قاعدة البيانات: ${health.database?.connected}`,
        'خادم وقاعدة بيانات متصلان',
        `الحالة: ${health.status}, قاعدة البيانات: ${health.database?.connected}`
      );
      
      return isHealthy;
    } catch (error) {
      this.addResult('اتصال Backend', false, `خطأ في الاتصال: ${error.message}`);
      return false;
    }
  }

  // فحص جلب الطاولات
  async testFetchTables() {
    try {
      // جرب بدون token أولاً
      let response = await fetch(`${this.baseURL}/api/v1/table-accounts?waiterId=${this.waiterId}`);
      let result = await response.json();
      
      // إذا كان يحتاج مصادقة، جرب مع token بسيط
      if (result.message && result.message.includes('رمز الوصول')) {
        console.log('🔐 المحاولة مع token...');
        response = await fetch(`${this.baseURL}/api/v1/table-accounts?waiterId=${this.waiterId}`, {
          headers: this.authHeaders
        });
        result = await response.json();
      }
      
      // إذا ما زال يفشل، جرب بدون waiterId
      if (result.message && result.message.includes('رمز الوصول')) {
        console.log('🔍 المحاولة بدون فلترة...');
        response = await fetch(`${this.baseURL}/api/v1/table-accounts`);
        result = await response.json();
      }
      
      const tables = result.data || result;
      
      console.log('🔍 استجابة الطاولات:', result);
      
      const isValidResponse = Array.isArray(tables);
      if (!isValidResponse) {
        this.addResult('جلب الطاولات', false, `البيانات ليست مصفوفة: ${result.message || typeof tables}`);
        return { success: false, tables: [] };
      }
      
      const hasExpectedTables = tables.length >= 3;
      const targetTables = tables.filter(t => ['1', '2', '29'].includes(String(t.tableNumber)));
      const hasTargetTables = targetTables.length > 0;
      
      this.addResult(
        'جلب الطاولات',
        isValidResponse && hasExpectedTables && hasTargetTables,
        `تم جلب ${tables.length} طاولة، الطاولات المستهدفة: ${targetTables.length}`,
        'مصفوفة من الطاولات تحتوي على طاولات 1، 2، 29',
        `${tables.length} طاولة، طاولات مستهدفة: ${targetTables.length}`
      );

      // فحص تفاصيل الطاولات المستهدفة
      console.log('🏓 الطاولات المستهدفة:');
      targetTables.forEach(table => {
        console.log(`   طاولة ${table.tableNumber}: النادل ${table.waiterName} (${table.waiterId})`);
      });
      
      return { success: isValidResponse && hasExpectedTables && hasTargetTables, tables };
    } catch (error) {
      this.addResult('جلب الطاولات', false, `خطأ: ${error.message}`);
      return { success: false, tables: [] };
    }
  }

  // فحص جلب الطلبات
  async testFetchOrders() {
    try {
      const response = await fetch(`${this.baseURL}/api/v1/orders`);
      const result = await response.json();
      const orders = result.data || result;
      
      const isValidResponse = Array.isArray(orders);
      const bosyOrders = orders.filter(order => 
        order.waiterId === this.waiterId || order.waiterName === this.waiterName
      );
      
      const hasExpectedOrders = bosyOrders.length >= 3;
      const hasTargetTablesOrders = bosyOrders.some(o => ['1', '2', '29'].includes(String(o.tableNumber)));
      
      this.addResult(
        'جلب الطلبات',
        isValidResponse && hasExpectedOrders,
        `إجمالي: ${orders.length}، للنادلة Bosy: ${bosyOrders.length}، للطاولات المستهدفة: ${hasTargetTablesOrders}`,
        'طلبات للنادلة Bosy في الطاولات 1، 2، 29',
        `${bosyOrders.length} طلب للنادلة Bosy`
      );

      // فحص تفاصيل الطلبات
      console.log('📋 طلبات النادلة Bosy:');
      bosyOrders.forEach(order => {
        const orderAge = Math.round((Date.now() - new Date(order.createdAt).getTime()) / (60 * 60 * 1000));
        console.log(`   طاولة ${order.tableNumber}: ${order.totalPrice} جنيه، ${order.status}، عمر ${orderAge} ساعة`);
      });
      
      return { success: isValidResponse && hasExpectedOrders, orders: bosyOrders };
    } catch (error) {
      this.addResult('جلب الطلبات', false, `خطأ: ${error.message}`);
      return { success: false, orders: [] };
    }
  }

  // فحص ربط الطلبات بالطاولات
  async testOrderTableMatching() {
    try {
      const tablesResult = await this.testFetchTables();
      const ordersResult = await this.testFetchOrders();
      
      if (!tablesResult.success || !ordersResult.success) {
        this.addResult('ربط الطلبات بالطاولات', false, 'فشل في جلب البيانات الأساسية');
        return false;
      }

      const { tables } = tablesResult;
      const { orders } = ordersResult;
      
      let matchingSuccess = true;
      let matchingDetails = [];

      // فحص كل طاولة
      ['1', '2', '29'].forEach(tableNumber => {
        const table = tables.find(t => String(t.tableNumber) === tableNumber);
        const tableOrders = orders.filter(o => String(o.tableNumber) === tableNumber);
        
        if (table && tableOrders.length > 0) {
          matchingDetails.push(`طاولة ${tableNumber}: ✅ ${tableOrders.length} طلب`);
        } else if (table && tableOrders.length === 0) {
          matchingDetails.push(`طاولة ${tableNumber}: ⚠️ لا توجد طلبات`);
          matchingSuccess = false;
        } else {
          matchingDetails.push(`طاولة ${tableNumber}: ❌ لا توجد`);
          matchingSuccess = false;
        }
      });

      this.addResult(
        'ربط الطلبات بالطاولات',
        matchingSuccess,
        matchingDetails.join(', '),
        'كل طاولة لديها طلبات مرتبطة',
        matchingDetails.join(', ')
      );

      return matchingSuccess;
    } catch (error) {
      this.addResult('ربط الطلبات بالطاولات', false, `خطأ: ${error.message}`);
      return false;
    }
  }

  // فحص أداء الاستجابة
  async testResponseTimes() {
    const tests = [
      { name: 'الطاولات', url: `${this.baseURL}/api/v1/table-accounts?waiterId=${this.waiterId}` },
      { name: 'الطلبات', url: `${this.baseURL}/api/v1/orders` },
      { name: 'الفئات', url: `${this.baseURL}/api/v1/categories` },
      { name: 'المنتجات', url: `${this.baseURL}/api/v1/products` }
    ];

    for (const test of tests) {
      const startTime = Date.now();
      try {
        const response = await fetch(test.url);
        const endTime = Date.now();
        const responseTime = endTime - startTime;
        
        const isGoodPerformance = responseTime < 2000; // أقل من 2 ثانية
        this.addResult(
          `أداء ${test.name}`,
          isGoodPerformance,
          `${responseTime}ms`,
          'أقل من 2000ms',
          `${responseTime}ms`
        );
      } catch (error) {
        this.addResult(`أداء ${test.name}`, false, `خطأ: ${error.message}`);
      }
    }
  }

  // فحص حالة Frontend
  async testFrontendStatus() {
    try {
      const response = await fetch(this.frontendURL);
      const isAccessible = response.status === 200;
      
      this.addResult(
        'حالة Frontend',
        isAccessible,
        `كود الاستجابة: ${response.status}`,
        '200 (متاح)',
        String(response.status)
      );

      return isAccessible;
    } catch (error) {
      this.addResult('حالة Frontend', false, `خطأ: ${error.message}`);
      return false;
    }
  }

  // فحص بيانات الجلسة المطلوبة
  testSessionData() {
    const requiredData = {
      'معرف النادل': this.waiterId,
      'اسم النادل': this.waiterName,
      'URL الخلفي': this.baseURL,
      'URL الأمامي': this.frontendURL
    };

    let allValid = true;
    let details = [];

    Object.entries(requiredData).forEach(([key, value]) => {
      const isValid = value && value.trim().length > 0;
      details.push(`${key}: ${isValid ? '✅' : '❌'} ${value}`);
      if (!isValid) allValid = false;
    });

    this.addResult(
      'بيانات الجلسة',
      allValid,
      details.join(', '),
      'جميع البيانات متوفرة',
      details.join(', ')
    );

    return allValid;
  }

  // تشغيل جميع الاختبارات
  async runAllTests() {
    console.log('🧪 بدء اختبار شاشة الطاولات...\n');
    console.log('='.repeat(60));
    
    // اختبارات أساسية
    await this.testBackendConnection();
    await this.testFrontendStatus();
    this.testSessionData();
    
    // اختبارات البيانات
    await this.testFetchTables();
    await this.testFetchOrders();
    await this.testOrderTableMatching();
    
    // اختبارات الأداء
    await this.testResponseTimes();
    
    // ملخص النتائج
    this.generateSummary();
  }

  // إنشاء ملخص النتائج
  generateSummary() {
    console.log('='.repeat(60));
    console.log('📊 ملخص نتائج الاختبار\n');
    
    const passedTests = this.testResults.filter(r => r.status.includes('✅')).length;
    const totalTests = this.testResults.length;
    const successRate = Math.round((passedTests / totalTests) * 100);
    
    console.log(`📈 معدل النجاح: ${successRate}% (${passedTests}/${totalTests})`);
    console.log('');
    
    // اختبارات ناجحة
    const passed = this.testResults.filter(r => r.status.includes('✅'));
    if (passed.length > 0) {
      console.log('✅ الاختبارات الناجحة:');
      passed.forEach(test => console.log(`   • ${test.test}`));
      console.log('');
    }
    
    // اختبارات فاشلة
    const failed = this.testResults.filter(r => r.status.includes('❌'));
    if (failed.length > 0) {
      console.log('❌ الاختبارات الفاشلة:');
      failed.forEach(test => {
        console.log(`   • ${test.test}`);
        if (test.details) console.log(`     التفاصيل: ${test.details}`);
      });
      console.log('');
    }
    
    // توصيات
    this.generateRecommendations(successRate, failed);
    
    // حالة النظام العامة
    if (successRate >= 80) {
      console.log('🎉 حالة النظام: ممتاز - جاهز للاستخدام');
    } else if (successRate >= 60) {
      console.log('⚠️ حالة النظام: جيد مع بعض التحسينات المطلوبة');
    } else {
      console.log('🚨 حالة النظام: يحتاج إصلاحات قبل الاستخدام');
    }
    
    console.log('');
    console.log(`📅 تاريخ الاختبار: ${new Date().toLocaleString('ar-EG')}`);
  }

  // إنشاء توصيات
  generateRecommendations(successRate, failedTests) {
    console.log('💡 التوصيات:');
    
    if (successRate === 100) {
      console.log('   🎯 ممتاز! النظام يعمل بكفاءة عالية');
    } else {
      if (failedTests.some(t => t.test.includes('Backend'))) {
        console.log('   🔧 فحص اتصال قاعدة البيانات والخادم');
      }
      if (failedTests.some(t => t.test.includes('الطاولات'))) {
        console.log('   🏓 التأكد من API الطاولات وبيانات النادل');
      }
      if (failedTests.some(t => t.test.includes('الطلبات'))) {
        console.log('   📋 فحص فلترة الطلبات ومطابقة النادل');
      }
      if (failedTests.some(t => t.test.includes('الأداء'))) {
        console.log('   ⚡ تحسين أداء الخادم وتقليل زمن الاستجابة');
      }
    }
    console.log('');
  }
}

// تشغيل الاختبار
async function main() {
  const tester = new TablesScreenTester();
  await tester.runAllTests();
}

main().catch(console.error);

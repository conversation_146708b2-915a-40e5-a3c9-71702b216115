<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار نظام القهوة</title>
    <style>
        body { 
            font-family: Arial, sans-serif; 
            padding: 20px; 
            background: #f5f5f5;
        }
        .test-container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .test-section {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background: #0056b3;
        }
        .status {
            padding: 10px;
            margin: 10px 0;
            border-radius: 5px;
        }
        .success { background: #d4edda; color: #155724; }
        .error { background: #f8d7da; color: #721c24; }
        .info { background: #d1ecf1; color: #0c5460; }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>🧪 اختبار نظام القهوة المطور</h1>
        
        <div class="test-section">
            <h3>📱 اختبار خدمة Worker والإشعارات</h3>
            <button onclick="testServiceWorker()">اختبار Service Worker</button>
            <button onclick="testNotifications()">اختبار الإشعارات</button>
            <button onclick="testBackgroundSound()">اختبار الصوت في الخلفية</button>
            <div id="sw-status" class="status"></div>
        </div>

        <div class="test-section">
            <h3>🔗 روابط سريعة للنظام</h3>
            <button onclick="openManagerDashboard()">لوحة المدير</button>
            <button onclick="openWaiterDashboard()">لوحة النادل</button>
            <button onclick="openChefDashboard()">لوحة الطباخ</button>
        </div>

        <div class="test-section">
            <h3>📊 حالة النظام</h3>
            <button onclick="checkSystemStatus()">فحص حالة النظام</button>
            <div id="system-status" class="status"></div>
        </div>

        <div class="test-section">
            <h3>🔧 أدوات التطوير</h3>
            <button onclick="clearCache()">مسح الكاش</button>
            <button onclick="reloadServiceWorker()">إعادة تحميل Service Worker</button>
            <button onclick="showConsoleLog()">عرض سجل وحدة التحكم</button>
        </div>
    </div>

    <script>
        let logs = [];

        function log(message, type = 'info') {
            const timestamp = new Date().toLocaleTimeString('ar-SA');
            logs.push(`[${timestamp}] ${message}`);
            console.log(message);
        }

        async function testServiceWorker() {
            const statusDiv = document.getElementById('sw-status');
            statusDiv.innerHTML = 'جاري اختبار Service Worker...';
            statusDiv.className = 'status info';

            try {
                if ('serviceWorker' in navigator) {
                    const registration = await navigator.serviceWorker.register('/sw.js');
                    const isActive = registration.active ? 'نشط' : 'غير نشط';
                    statusDiv.innerHTML = `✅ Service Worker ${isActive}<br>Scope: ${registration.scope}`;
                    statusDiv.className = 'status success';
                    log('Service Worker تم تسجيله بنجاح');
                } else {
                    statusDiv.innerHTML = '❌ المتصفح لا يدعم Service Worker';
                    statusDiv.className = 'status error';
                }
            } catch (error) {
                statusDiv.innerHTML = `❌ خطأ: ${error.message}`;
                statusDiv.className = 'status error';
                log(`خطأ في Service Worker: ${error.message}`, 'error');
            }
        }

        async function testNotifications() {
            const statusDiv = document.getElementById('sw-status');
            
            try {
                if ('Notification' in window) {
                    const permission = await Notification.requestPermission();
                    if (permission === 'granted') {
                        new Notification('🧪 اختبار الإشعار', {
                            body: 'تم تفعيل الإشعارات بنجاح!',
                            icon: '/coffee-logo.svg',
                            tag: 'test-notification'
                        });
                        statusDiv.innerHTML = '✅ تم إرسال إشعار تجريبي';
                        statusDiv.className = 'status success';
                        log('تم إرسال إشعار تجريبي');
                    } else {
                        statusDiv.innerHTML = '❌ لم يتم منح إذن الإشعارات';
                        statusDiv.className = 'status error';
                    }
                } else {
                    statusDiv.innerHTML = '❌ المتصفح لا يدعم الإشعارات';
                    statusDiv.className = 'status error';
                }
            } catch (error) {
                statusDiv.innerHTML = `❌ خطأ في الإشعارات: ${error.message}`;
                statusDiv.className = 'status error';
                log(`خطأ في الإشعارات: ${error.message}`, 'error');
            }
        }

        async function testBackgroundSound() {
            const statusDiv = document.getElementById('sw-status');
            
            try {
                // إرسال رسالة للـ Service Worker لتشغيل الصوت
                if (navigator.serviceWorker && navigator.serviceWorker.controller) {
                    navigator.serviceWorker.controller.postMessage({
                        type: 'PLAY_NOTIFICATION_SOUND'
                    });
                    statusDiv.innerHTML = '🔊 تم إرسال طلب تشغيل الصوت للـ Service Worker';
                    statusDiv.className = 'status info';
                    log('تم إرسال طلب تشغيل الصوت');
                } else {
                    statusDiv.innerHTML = '❌ لا يوجد Service Worker نشط';
                    statusDiv.className = 'status error';
                }
            } catch (error) {
                statusDiv.innerHTML = `❌ خطأ في تشغيل الصوت: ${error.message}`;
                statusDiv.className = 'status error';
                log(`خطأ في تشغيل الصوت: ${error.message}`, 'error');
            }
        }

        function openManagerDashboard() {
            window.open('http://localhost:5173', '_blank');
        }

        function openWaiterDashboard() {
            window.open('http://localhost:5173', '_blank');
        }

        function openChefDashboard() {
            window.open('http://localhost:5173', '_blank');
        }

        async function checkSystemStatus() {
            const statusDiv = document.getElementById('system-status');
            statusDiv.innerHTML = 'جاري فحص النظام...';
            statusDiv.className = 'status info';

            try {
                // فحص الخادم الخلفي
                const backendResponse = await fetch('http://localhost:3001/api/health');
                const backendStatus = backendResponse.ok ? '✅ يعمل' : '❌ متوقف';
                
                // فحص الخادم الأمامي
                const frontendResponse = await fetch('http://localhost:5173');
                const frontendStatus = frontendResponse.ok ? '✅ يعمل' : '❌ متوقف';

                statusDiv.innerHTML = `
                    الخادم الخلفي (Backend): ${backendStatus}<br>
                    الخادم الأمامي (Frontend): ${frontendStatus}<br>
                    Service Worker: ${navigator.serviceWorker ? '✅ مدعوم' : '❌ غير مدعوم'}
                `;
                statusDiv.className = 'status success';
                log('تم فحص حالة النظام');
            } catch (error) {
                statusDiv.innerHTML = `❌ خطأ في فحص النظام: ${error.message}`;
                statusDiv.className = 'status error';
                log(`خطأ في فحص النظام: ${error.message}`, 'error');
            }
        }

        async function clearCache() {
            try {
                if ('caches' in window) {
                    const cacheNames = await caches.keys();
                    await Promise.all(cacheNames.map(name => caches.delete(name)));
                    alert('✅ تم مسح جميع الكاش');
                    log('تم مسح الكاش');
                }
            } catch (error) {
                alert(`❌ خطأ في مسح الكاش: ${error.message}`);
                log(`خطأ في مسح الكاش: ${error.message}`, 'error');
            }
        }

        async function reloadServiceWorker() {
            try {
                if ('serviceWorker' in navigator) {
                    const registration = await navigator.serviceWorker.getRegistration();
                    if (registration) {
                        await registration.unregister();
                        await navigator.serviceWorker.register('/sw.js');
                        alert('✅ تم إعادة تحميل Service Worker');
                        log('تم إعادة تحميل Service Worker');
                    }
                }
            } catch (error) {
                alert(`❌ خطأ في إعادة تحميل Service Worker: ${error.message}`);
                log(`خطأ في إعادة تحميل Service Worker: ${error.message}`, 'error');
            }
        }

        function showConsoleLog() {
            const logWindow = window.open('', '_blank', 'width=800,height=600');
            logWindow.document.write(`
                <html dir="rtl">
                <head>
                    <title>سجل وحدة التحكم</title>
                    <style>
                        body { font-family: monospace; padding: 20px; }
                        .log-entry { margin: 5px 0; padding: 5px; border-radius: 3px; }
                        .info { background: #e3f2fd; }
                        .error { background: #ffebee; }
                    </style>
                </head>
                <body>
                    <h2>📝 سجل وحدة التحكم</h2>
                    ${logs.map(log => `<div class="log-entry info">${log}</div>`).join('')}
                </body>
                </html>
            `);
        }

        // تهيئة أولية
        window.addEventListener('load', () => {
            log('تم تحميل صفحة الاختبار');
            testServiceWorker();
        });

        // الاستماع لرسائل Service Worker
        if ('serviceWorker' in navigator) {
            navigator.serviceWorker.addEventListener('message', (event) => {
                log(`رسالة من Service Worker: ${JSON.stringify(event.data)}`);
            });
        }
    </script>
</body>
</html>

import React, { useState, useMemo, useEffect } from 'react';
import { authenticatedGet, authenticatedPost, authenticatedPut, authenticatedDelete } from '../utils/apiHelpers';
import { useToast } from '../hooks/useToast';
import socket from '../socket';
import '../styles/screens/MenuScreenIsolated.css';
import '../styles/components/EnhancedMenuCard.css';
import ResponsiveGrid from '../components/ResponsiveGrid';
import ResponsiveCard from '../components/ResponsiveCard';
import ResponsiveModal from '../components/ResponsiveModal';

interface MenuItem {
  _id: string;
  name: string;
  price: number;
  category: string | any;
  categoryId?: string;
  description?: string;
  isAvailable: boolean;
  available?: boolean;
  image?: string;
  preparationTime?: number;
  ingredients?: string[];
  calories?: number;
  createdAt: string;
  updatedAt: string;
}

interface Category {
  _id: string;
  name: string;
  description?: string;
  isActive: boolean;
}

interface MenuManagerScreenProps {
  menuItems: MenuItem[];
  categories: Category[];
  onMenuItemsUpdate: () => void;
  loading: boolean;
}

const MenuManagerScreenBootstrap: React.FC<MenuManagerScreenProps> = ({
  menuItems,
  categories,
  onMenuItemsUpdate,
  loading
}) => {
  const { showSuccess, showError } = useToast();

  // Debugging: Log categories data
  React.useEffect(() => {
    console.log('🔍 MenuManagerScreen - Categories data:', categories);
    console.log('🔍 MenuManagerScreen - Categories length:', categories?.length || 0);
    console.log('🔍 MenuManagerScreen - Active categories:', categories?.filter(cat => cat?.isActive) || []);
    console.log('🔍 MenuManagerScreen - All categories:', categories);
  }, [categories]);

  // Request categories reload if they're not loaded
  React.useEffect(() => {
    if (!categories || !Array.isArray(categories) || categories.length === 0) {
      console.log('🔄 Categories not loaded, requesting reload...');
      onMenuItemsUpdate(); // This will trigger fetchMenuItems and fetchCategories in parent
    }
  }, [categories, onMenuItemsUpdate]);

  // Socket.IO event listeners for real-time updates
  useEffect(() => {
    const handleMenuUpdate = () => {
      console.log('📡 Real-time menu update received');
      onMenuItemsUpdate();
    };

    const handleCategoryUpdate = () => {
      console.log('📡 Real-time category update received');
      onMenuItemsUpdate();
    };

    const handleInventoryUpdate = () => {
      console.log('📡 Real-time inventory update received');
      onMenuItemsUpdate();
    };

    // Add event listeners
    socket.on('menuItemAdded', handleMenuUpdate);
    socket.on('menuItemUpdated', handleMenuUpdate);
    socket.on('menuItemDeleted', handleMenuUpdate);
    socket.on('categoryAdded', handleCategoryUpdate);
    socket.on('categoryUpdated', handleCategoryUpdate);
    socket.on('categoryDeleted', handleCategoryUpdate);
    socket.on('inventoryUpdated', handleInventoryUpdate);
    socket.on('stockAlert', handleInventoryUpdate);

    // Cleanup function
    return () => {
      socket.off('menuItemAdded', handleMenuUpdate);
      socket.off('menuItemUpdated', handleMenuUpdate);
      socket.off('menuItemDeleted', handleMenuUpdate);
      socket.off('categoryAdded', handleCategoryUpdate);
      socket.off('categoryUpdated', handleCategoryUpdate);
      socket.off('categoryDeleted', handleCategoryUpdate);
      socket.off('inventoryUpdated', handleInventoryUpdate);
      socket.off('stockAlert', handleInventoryUpdate);
    };
  }, [onMenuItemsUpdate]);
  
  const [menuScreenSearchTerm, setMenuScreenSearchTerm] = useState('');
  const [menuScreenCategoryFilter, setMenuScreenCategoryFilter] = useState<string>('all');
  const [menuScreenAvailabilityFilter, setMenuScreenAvailabilityFilter] = useState<'all' | 'available' | 'unavailable'>('all');
  const [menuScreenShowModal, setMenuScreenShowModal] = useState(false);
  const [menuScreenEditingItem, setMenuScreenEditingItem] = useState<MenuItem | null>(null);
  const [viewMode, setViewMode] = useState<'grid' | 'list'>('grid');
  const [sortBy, setSortBy] = useState<'name' | 'price' | 'category'>('name');
  const [sortOrder, setSortOrder] = useState<'asc' | 'desc'>('asc');
  const [menuScreenFormData, setMenuScreenFormData] = useState({
    name: '',
    price: 0,
    category: '',
    categoryId: '',
    description: '',
    isAvailable: true,
    preparationTime: 0,
    ingredients: [] as string[],
    calories: 0
  });
  const [menuScreenIngredientInput, setMenuScreenIngredientInput] = useState('');

  const getItemAvailability = (item: MenuItem): boolean => {
    if (item.isAvailable !== undefined) {
      return item.isAvailable;
    }
    if (item.available !== undefined) {
      return item.available;
    }
    return true;
  };

  const getCategoryName = (categoryData: string | any): string => {
    if (typeof categoryData === 'string') {
      const category = categories.find(cat => cat._id === categoryData);
      return category?.name || categoryData;
    }
    if (typeof categoryData === 'object' && categoryData) {
      return categoryData.name || categoryData._id || 'غير محدد';
    }
    return 'غير محدد';
  };

  const getCategoryIcon = (categoryData: string | any): string => {
    const categoryName = getCategoryName(categoryData).toLowerCase();
    if (categoryName.includes('قهوة') || categoryName.includes('coffee')) return 'fa-coffee';
    if (categoryName.includes('شاي') || categoryName.includes('tea')) return 'fa-leaf';
    if (categoryName.includes('عصير') || categoryName.includes('juice')) return 'fa-glass-whiskey';
    if (categoryName.includes('حلويات') || categoryName.includes('dessert')) return 'fa-birthday-cake';
    if (categoryName.includes('مشروبات') || categoryName.includes('drink')) return 'fa-glass-martini';
    if (categoryName.includes('طعام') || categoryName.includes('food')) return 'fa-hamburger';
    return 'fa-utensils';
  };

  // Filter and sort menu items
  const filteredAndSortedMenuItems = useMemo(() => {
    let filtered = menuItems.filter(item => {
      const matchesSearch = item.name.toLowerCase().includes(menuScreenSearchTerm.toLowerCase());
      
      const itemCategoryId = item.categoryId || (typeof item.category === 'object' ? item.category?._id : item.category);
      const matchesCategory = menuScreenCategoryFilter === 'all' || itemCategoryId === menuScreenCategoryFilter;
      
      const isItemAvailable = getItemAvailability(item);
      const matchesAvailability = menuScreenAvailabilityFilter === 'all' || 
        (menuScreenAvailabilityFilter === 'available' && isItemAvailable) ||
        (menuScreenAvailabilityFilter === 'unavailable' && !isItemAvailable);
      
      return matchesSearch && matchesCategory && matchesAvailability;
    });

    // Sort items
    filtered.sort((a, b) => {
      let aValue: any, bValue: any;
      
      switch (sortBy) {
        case 'name':
          aValue = a.name.toLowerCase();
          bValue = b.name.toLowerCase();
          break;
        case 'price':
          aValue = a.price;
          bValue = b.price;
          break;
        case 'category':
          aValue = getCategoryName(a.category).toLowerCase();
          bValue = getCategoryName(b.category).toLowerCase();
          break;
        default:
          aValue = a.name.toLowerCase();
          bValue = b.name.toLowerCase();
      }

      if (sortOrder === 'asc') {
        return aValue < bValue ? -1 : aValue > bValue ? 1 : 0;
      } else {
        return aValue > bValue ? -1 : aValue < bValue ? 1 : 0;
      }
    });

    return filtered;
  }, [menuItems, menuScreenSearchTerm, menuScreenCategoryFilter, menuScreenAvailabilityFilter, sortBy, sortOrder]);

  const handleMenuScreenSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    try {
      const menuItemData = {
        ...menuScreenFormData,
        price: Number(menuScreenFormData.price),
        preparationTime: Number(menuScreenFormData.preparationTime) || undefined,
        calories: Number(menuScreenFormData.calories) || undefined,
        categoryId: menuScreenFormData.categoryId || menuScreenFormData.category,
        category: menuScreenFormData.categoryId || menuScreenFormData.category
      };

      if (menuScreenEditingItem) {
        const response = await authenticatedPut(`/api/v1/products/${menuScreenEditingItem._id}`, menuItemData);
        if (response.success) {
          showSuccess('تم تحديث عنصر القائمة بنجاح');
          onMenuItemsUpdate();
          handleMenuScreenCloseModal();
        }
      } else {
        const response = await authenticatedPost('/api/v1/products', menuItemData);
        if (response.success) {
          showSuccess('تم إضافة عنصر جديد للقائمة');
          onMenuItemsUpdate();
          handleMenuScreenCloseModal();
        }
      }
    } catch (error) {
      showError('حدث خطأ أثناء حفظ عنصر القائمة');
    }
  };

  const handleMenuScreenEdit = (item: MenuItem) => {
    setMenuScreenEditingItem(item);
    setMenuScreenFormData({
      name: item.name,
      price: item.price,
      category: typeof item.category === 'string' ? item.category : item.category?._id || '',
      categoryId: item.categoryId || (typeof item.category === 'object' ? item.category?._id : item.category) || '',
      description: item.description || '',
      isAvailable: getItemAvailability(item),
      preparationTime: item.preparationTime || 0,
      ingredients: item.ingredients || [],
      calories: item.calories || 0
    });
    setMenuScreenShowModal(true);
  };

  const handleMenuScreenDelete = async (itemId: string) => {
    if (!window.confirm('هل أنت متأكد من حذف هذا العنصر؟')) return;
    
    try {
      const response = await authenticatedDelete(`/api/v1/products/${itemId}`);
      if (response.success) {
        showSuccess('تم حذف عنصر القائمة بنجاح');
        onMenuItemsUpdate();
      }
    } catch (error) {
      showError('حدث خطأ أثناء حذف عنصر القائمة');
    }
  };

  const handleMenuScreenToggleAvailability = async (item: MenuItem) => {
    try {
      const currentAvailability = getItemAvailability(item);
      const response = await authenticatedPut(`/api/v1/products/${item._id}`, {
        isAvailable: !currentAvailability,
        available: !currentAvailability
      });
      
      if (response.success) {
        showSuccess(`تم ${!currentAvailability ? 'إتاحة' : 'إخفاء'} العنصر`);
        onMenuItemsUpdate();
      }
    } catch (error) {
      showError('حدث خطأ أثناء تغيير حالة العنصر');
    }
  };

  const handleMenuScreenCloseModal = () => {
    setMenuScreenShowModal(false);
    setMenuScreenEditingItem(null);
    setMenuScreenFormData({
      name: '',
      price: 0,
      category: '',
      categoryId: '',
      description: '',
      isAvailable: true,
      preparationTime: 0,
      ingredients: [],
      calories: 0
    });
    setMenuScreenIngredientInput('');
  };

  const handleMenuScreenAddIngredient = () => {
    if (menuScreenIngredientInput.trim() && !menuScreenFormData.ingredients.includes(menuScreenIngredientInput.trim())) {
      setMenuScreenFormData(prev => ({
        ...prev,
        ingredients: [...prev.ingredients, menuScreenIngredientInput.trim()]
      }));
      setMenuScreenIngredientInput('');
    }
  };

  const handleMenuScreenRemoveIngredient = (index: number) => {
    setMenuScreenFormData(prev => ({
      ...prev,
      ingredients: prev.ingredients.filter((_, i) => i !== index)
    }));
  };

  // Statistics
  const stats = useMemo(() => {
    return {
      total: menuItems.length,
      available: menuItems.filter(item => getItemAvailability(item)).length,
      unavailable: menuItems.filter(item => !getItemAvailability(item)).length,
      avgPrice: menuItems.length > 0 ? menuItems.reduce((sum, item) => sum + item.price, 0) / menuItems.length : 0,
      categoriesCount: new Set(menuItems.map(item => item.categoryId || (typeof item.category === 'object' ? item.category?._id : item.category))).size
    };
  }, [menuItems]);

  return (
    <div className="container-fluid p-4" dir="rtl">
      {/* Header Section */}
      <div className="row mb-4">
        <div className="col-12">
          <div className="card border-0 shadow-sm bg-gradient">
            <div className="card-body p-4">
              <div className="row align-items-center">
                <div className="col-lg-6">
                  <h2 className="h3 fw-bold text-dark mb-2">إدارة القائمة</h2>
                  <p className="text-muted mb-0">إدارة أصناف وأسعار قائمة الطعام</p>
                  {(!categories || !Array.isArray(categories) || categories.length === 0) && (
                    <div className="alert alert-warning mt-2 mb-0 py-2">
                      <small>
                        <i className="fas fa-exclamation-triangle me-1"></i>
                        لم يتم تحميل الفئات. 
                        <button 
                          className="btn btn-link btn-sm p-0 ms-1"
                          onClick={onMenuItemsUpdate}
                        >
                          إعادة تحميل
                        </button>
                      </small>
                    </div>
                  )}
                </div>
                <div className="col-lg-6">
                  <div className="row g-2">
                    <div className="col-sm-6">
                      <div className="d-flex align-items-center">
                        <span className="me-2 text-muted">متوسط السعر:</span>
                        <span className="badge bg-success fs-6">
                          {stats.avgPrice.toFixed(2)} ج.م
                        </span>
                      </div>
                    </div>
                    <div className="col-sm-6">
                      <div className="d-flex justify-content-lg-end justify-content-start">
                        <button
                          className="btn btn-primary"
                          onClick={() => setMenuScreenShowModal(true)}
                        >
                          <i className="fas fa-plus me-2"></i>
                          إضافة صنف جديد
                        </button>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Statistics Cards */}
      <div className="row g-4 mb-4">
        <div className="col-xl-2 col-lg-4 col-md-6">
          <div className="card border-0 shadow-sm h-100 bg-primary bg-gradient text-white">
            <div className="card-body text-center">
              <i className="fas fa-utensils fa-2x mb-2"></i>
              <h4 className="fw-bold">{stats.total}</h4>
              <p className="mb-0 small">إجمالي الأصناف</p>
            </div>
          </div>
        </div>
        
        <div className="col-xl-2 col-lg-4 col-md-6">
          <div className="card border-0 shadow-sm h-100 bg-success bg-gradient text-white">
            <div className="card-body text-center">
              <i className="fas fa-check-circle fa-2x mb-2"></i>
              <h4 className="fw-bold">{stats.available}</h4>
              <p className="mb-0 small">متاح</p>
            </div>
          </div>
        </div>
        
        <div className="col-xl-2 col-lg-4 col-md-6">
          <div className="card border-0 shadow-sm h-100 bg-warning bg-gradient text-white">
            <div className="card-body text-center">
              <i className="fas fa-times-circle fa-2x mb-2"></i>
              <h4 className="fw-bold">{stats.unavailable}</h4>
              <p className="mb-0 small">غير متاح</p>
            </div>
          </div>
        </div>
        
        <div className="col-xl-2 col-lg-4 col-md-6">
          <div className="card border-0 shadow-sm h-100 bg-info bg-gradient text-white">
            <div className="card-body text-center">
              <i className="fas fa-money-bill-wave fa-2x mb-2"></i>
              <h4 className="fw-bold">{stats.avgPrice.toFixed(2)}</h4>
              <p className="mb-0 small">متوسط السعر (ج.م)</p>
            </div>
          </div>
        </div>

        <div className="col-xl-2 col-lg-4 col-md-6">
          <div className="card border-0 shadow-sm h-100 bg-secondary bg-gradient text-white">
            <div className="card-body text-center">
              <i className="fas fa-list fa-2x mb-2"></i>
              <h4 className="fw-bold">{stats.categoriesCount}</h4>
              <p className="mb-0 small">الفئات</p>
            </div>
          </div>
        </div>

        <div className="col-xl-2 col-lg-4 col-md-6">
          <div className="card border-0 shadow-sm h-100 bg-dark bg-gradient text-white">
            <div className="card-body text-center">
              <i className="fas fa-percentage fa-2x mb-2"></i>
              <h4 className="fw-bold">{stats.total > 0 ? ((stats.available / stats.total) * 100).toFixed(1) : 0}%</h4>
              <p className="mb-0 small">نسبة التوفر</p>
            </div>
          </div>
        </div>
      </div>

      {/* Filters and Controls */}
      <div className="row mb-4">
        <div className="col-12">
          <div className="card border-0 shadow-sm">
            <div className="card-body p-4">
              <div className="row g-3 align-items-center">
                {/* Search */}
                <div className="col-lg-3">
                  <div className="position-relative">
                    <input
                      type="text"
                      className="form-control form-control-lg pe-5"
                      placeholder="البحث عن صنف..."
                      value={menuScreenSearchTerm}
                      onChange={(e) => setMenuScreenSearchTerm(e.target.value)}
                    />
                    <i className="fas fa-search position-absolute top-50 end-0 translate-middle-y me-3 text-muted"></i>
                    {menuScreenSearchTerm && (
                      <button
                        className="btn btn-link position-absolute top-50 start-0 translate-middle-y text-danger"
                        onClick={() => setMenuScreenSearchTerm('')}
                        title="مسح البحث"
                      >
                        <i className="fas fa-times"></i>
                      </button>
                    )}
                  </div>
                </div>

                {/* Category Filter */}
                <div className="col-lg-2 col-md-4">
                  <select
                    className="form-select form-select-lg"
                    value={menuScreenCategoryFilter}
                    onChange={(e) => setMenuScreenCategoryFilter(e.target.value)}
                    title="تصفية حسب الفئة"
                    aria-label="تصفية حسب الفئة"
                  >
                    <option value="all">جميع الفئات</option>
                    {categories && Array.isArray(categories) && categories.length > 0 ? (
                      categories.filter(cat => cat && (cat.isActive !== false)).map(category => (
                        <option key={category._id} value={category._id}>
                          {category.name}
                        </option>
                      ))
                    ) : (
                      <option disabled>لا توجد فئات</option>
                    )}
                  </select>
                </div>

                {/* Availability Filter */}
                <div className="col-lg-2 col-md-4">
                  <select
                    className="form-select form-select-lg"
                    value={menuScreenAvailabilityFilter}
                    onChange={(e) => setMenuScreenAvailabilityFilter(e.target.value as any)}
                    title="تصفية حسب التوفر"
                    aria-label="تصفية حسب التوفر"
                  >
                    <option value="all">جميع الحالات</option>
                    <option value="available">متاح</option>
                    <option value="unavailable">غير متاح</option>
                  </select>
                </div>

                {/* Sort By */}
                <div className="col-lg-2 col-md-4">
                  <select
                    className="form-select form-select-lg"
                    value={sortBy}
                    onChange={(e) => setSortBy(e.target.value as any)}
                    title="ترتيب حسب"
                    aria-label="ترتيب حسب"
                  >
                    <option value="name">الاسم</option>
                    <option value="price">السعر</option>
                    <option value="category">الفئة</option>
                  </select>
                </div>

                {/* View Mode and Sort Order */}
                <div className="col-lg-3">
                  <div className="btn-group w-100" role="group">
                    <button
                      className={`btn ${viewMode === 'grid' ? 'btn-primary' : 'btn-outline-primary'}`}
                      onClick={() => setViewMode('grid')}
                      title="عرض شبكي"
                    >
                      <i className="fas fa-th"></i>
                    </button>
                    <button
                      className={`btn ${viewMode === 'list' ? 'btn-primary' : 'btn-outline-primary'}`}
                      onClick={() => setViewMode('list')}
                      title="عرض قائمة"
                    >
                      <i className="fas fa-list"></i>
                    </button>
                    <button
                      className={`btn btn-outline-secondary ${sortOrder === 'asc' ? 'active' : ''}`}
                      onClick={() => setSortOrder(sortOrder === 'asc' ? 'desc' : 'asc')}
                      title={`ترتيب ${sortOrder === 'asc' ? 'تنازلي' : 'تصاعدي'}`}
                    >
                      <i className={`fas fa-sort-${sortOrder === 'asc' ? 'up' : 'down'}`}></i>
                    </button>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Menu Items Content */}
      <div className="row">
        <div className="col-12">
          {loading ? (
            <div className="text-center py-5">
              <div className="spinner-border text-primary" role="status">
                <span className="visually-hidden">جاري التحميل...</span>
              </div>
              <p className="mt-3 text-muted">جاري تحميل أصناف القائمة...</p>
            </div>
          ) : filteredAndSortedMenuItems.length === 0 ? (
            <div className="text-center py-5">
              <i className="fas fa-utensils fa-4x text-muted mb-3"></i>
              <h5 className="text-muted">لا توجد أصناف</h5>
              <p className="text-muted">لا توجد أصناف مطابقة لمعايير البحث والفلترة الحالية</p>
              <button
                className="btn btn-primary"
                onClick={() => setMenuScreenShowModal(true)}
              >
                <i className="fas fa-plus me-2"></i>
                إضافة صنف جديد
              </button>
            </div>
          ) : (
            <>
              {viewMode === 'grid' ? (
                <ResponsiveGrid cols={{ xs: 1, sm: 2, md: 2, lg: 3, xl: 4, xxl: 5 }} gap={3}>
                  {filteredAndSortedMenuItems.map(item => {
                    const isAvailable = getItemAvailability(item);
                    return (
                      <div
                        key={item._id}
                        className={`menu-item-card-enhanced ${!isAvailable ? 'unavailable' : ''}`}
                      >
                        {/* Status indicator bar */}
                        <div className={`menu-status-bar ${isAvailable ? 'available' : 'unavailable'}`}></div>

                        {/* Floating price indicator */}
                        <div className={`floating-price-indicator ${!isAvailable ? 'unavailable' : ''}`}>
                          <span>{item.price.toFixed(2)} ج.م</span>
                        </div>

                        {/* Header */}
                        <div className="menu-card-header">
                          <div className="menu-item-icon">
                            <i className={`fas ${getCategoryIcon(item.category)}`}></i>
                          </div>
                          <h5 className="menu-item-name">{item.name}</h5>
                          <span className="menu-category-badge">{getCategoryName(item.category)}</span>
                        </div>

                        <div className="menu-card-body">
                          {/* Status Only */}
                          <div className="menu-status-section">
                            <span className={`availability-badge ${isAvailable ? 'available' : 'unavailable'}`}>
                              <i className={`fas ${isAvailable ? 'fa-check-circle' : 'fa-times-circle'}`}></i>
                              <span>{isAvailable ? 'متاح' : 'غير متاح'}</span>
                            </span>
                          </div>

                          {/* Description */}
                          {item.description && (
                            <div className="menu-description">
                              <p>{item.description}</p>
                            </div>
                          )}

                          {/* Details Grid */}
                          <div className="menu-details-grid">
                            {item.preparationTime && (
                              <div className="menu-detail-item">
                                <div className="detail-icon time">
                                  <i className="fas fa-clock"></i>
                                </div>
                                <div className="detail-content">
                                  <span className="detail-label">وقت التحضير</span>
                                  <span className="detail-value">{item.preparationTime} د</span>
                                </div>
                              </div>
                            )}

                            {item.calories && (
                              <div className="menu-detail-item">
                                <div className="detail-icon calories">
                                  <i className="fas fa-fire"></i>
                                </div>
                                <div className="detail-content">
                                  <span className="detail-label">السعرات</span>
                                  <span className="detail-value">{item.calories} kcal</span>
                                </div>
                              </div>
                            )}
                          </div>

                          {/* Ingredients */}
                          {item.ingredients && item.ingredients.length > 0 && (
                            <div className="menu-ingredients">
                              <div className="ingredients-header">
                                <i className="fas fa-list"></i>
                                <span>المكونات</span>
                              </div>
                              <div className="ingredients-list">
                                {item.ingredients.slice(0, 3).map((ingredient, index) => (
                                  <span key={index} className="ingredient-tag">{ingredient}</span>
                                ))}
                                {item.ingredients.length > 3 && (
                                  <span className="ingredient-tag more">+{item.ingredients.length - 3}</span>
                                )}
                              </div>
                            </div>
                          )}

                          {/* Action Buttons */}
                          <div className="menu-actions">
                            <button
                              className="menu-action-btn edit"
                              onClick={() => handleMenuScreenEdit(item)}
                              title="تعديل الصنف"
                            >
                              <i className="fas fa-edit"></i>
                              <span>تعديل</span>
                            </button>

                            <button
                              className={`menu-action-btn toggle ${isAvailable ? 'hide' : 'show'}`}
                              onClick={() => handleMenuScreenToggleAvailability(item)}
                              title={isAvailable ? 'إخفاء الصنف' : 'إظهار الصنف'}
                            >
                              <i className={`fas ${isAvailable ? 'fa-eye-slash' : 'fa-eye'}`}></i>
                              <span>{isAvailable ? 'إخفاء' : 'إظهار'}</span>
                            </button>

                            <button
                              className="menu-action-btn delete"
                              onClick={() => handleMenuScreenDelete(item._id)}
                              title="حذف الصنف"
                            >
                              <i className="fas fa-trash"></i>
                              <span>حذف</span>
                            </button>
                          </div>
                        </div>
                      </div>
                    );
                  })}
                </ResponsiveGrid>
              ) : (
                <div className="card border-0 shadow-sm">
                  <div className="card-header bg-transparent">
                    <h5 className="card-title mb-0">قائمة الأصناف</h5>
                  </div>
                  <div className="card-body p-0">
                    <div className="table-responsive">
                      <table className="table table-hover mb-0">
                        <thead className="table-light">
                          <tr>
                            <th scope="col" className="text-center">الصنف</th>
                            <th scope="col" className="text-center">الفئة</th>
                            <th scope="col" className="text-center">السعر</th>
                            <th scope="col" className="text-center">وقت التحضير</th>
                            <th scope="col" className="text-center">السعرات</th>
                            <th scope="col" className="text-center">الحالة</th>
                            <th scope="col" className="text-center">الإجراءات</th>
                          </tr>
                        </thead>
                        <tbody>
                          {filteredAndSortedMenuItems.map(item => {
                            const isAvailable = getItemAvailability(item);
                            return (
                              <tr key={item._id} className={!isAvailable ? 'opacity-75' : ''}>
                                <td className="text-center">
                                  <div>
                                    <div className="fw-bold">{item.name}</div>
                                    {item.description && (
                                      <small className="text-muted">{item.description}</small>
                                    )}
                                  </div>
                                </td>
                                <td className="text-center">
                                  <span className="badge bg-info">
                                    {getCategoryName(item.category)}
                                  </span>
                                </td>
                                <td className="text-center">
                                  <span className="fw-bold text-success">
                                    {item.price.toFixed(2)} ج.م
                                  </span>
                                </td>
                                <td className="text-center">
                                  {item.preparationTime ? (
                                    <span className="text-muted">
                                      <i className="fas fa-clock me-1"></i>
                                      {item.preparationTime} د
                                    </span>
                                  ) : (
                                    <span className="text-muted">-</span>
                                  )}
                                </td>
                                <td className="text-center">
                                  {item.calories ? (
                                    <span className="text-muted">
                                      <i className="fas fa-fire me-1"></i>
                                      {item.calories} kcal
                                    </span>
                                  ) : (
                                    <span className="text-muted">-</span>
                                  )}
                                </td>
                                <td className="text-center">
                                  <span className={`badge ${isAvailable ? 'bg-success' : 'bg-warning'}`}>
                                    <i className={`fas ${isAvailable ? 'fa-check' : 'fa-times'} me-1`}></i>
                                    {isAvailable ? 'متاح' : 'غير متاح'}
                                  </span>
                                </td>
                                <td className="text-center">
                                  <div className="btn-group btn-group-sm" role="group">
                                    <button
                                      className="btn btn-outline-primary"
                                      onClick={() => handleMenuScreenEdit(item)}
                                      title="تعديل"
                                    >
                                      <i className="fas fa-edit"></i>
                                    </button>
                                    
                                    <button
                                      className={`btn ${isAvailable ? 'btn-outline-warning' : 'btn-outline-success'}`}
                                      onClick={() => handleMenuScreenToggleAvailability(item)}
                                      title={isAvailable ? 'إخفاء' : 'إظهار'}
                                    >
                                      <i className={`fas ${isAvailable ? 'fa-eye-slash' : 'fa-eye'}`}></i>
                                    </button>
                                    
                                    <button
                                      className="btn btn-outline-danger"
                                      onClick={() => handleMenuScreenDelete(item._id)}
                                      title="حذف"
                                    >
                                      <i className="fas fa-trash"></i>
                                    </button>
                                  </div>
                                </td>
                              </tr>
                            );
                          })}
                        </tbody>
                      </table>
                    </div>
                  </div>
                </div>
              )}
            </>
          )}
        </div>
      </div>

      {/* Add/Edit Modal */}
      <ResponsiveModal
        show={menuScreenShowModal}
        onHide={handleMenuScreenCloseModal}
        title={menuScreenEditingItem ? 'تعديل صنف' : 'إضافة صنف جديد'}
        size="lg"
        footer={
          <div className="d-flex gap-2">
            <button
              type="button"
              className="btn btn-secondary"
              onClick={handleMenuScreenCloseModal}
            >
              إلغاء
            </button>
            <button
              type="submit"
              form="menuItemForm"
              className="btn btn-primary"
            >
              {menuScreenEditingItem ? 'تحديث' : 'إضافة'}
            </button>
          </div>
        }
      >
        <form id="menuItemForm" onSubmit={handleMenuScreenSubmit}>
          <div className="row g-3">
            <div className="col-md-6">
              <label htmlFor="itemName" className="form-label">اسم الصنف</label>
              <input
                type="text"
                className="form-control"
                id="itemName"
                value={menuScreenFormData.name}
                onChange={(e) => setMenuScreenFormData({
                  ...menuScreenFormData,
                  name: e.target.value
                })}
                required
              />
            </div>
            
            <div className="col-md-6">
              <label htmlFor="itemPrice" className="form-label">السعر (ج.م)</label>
              <input
                type="number"
                step="0.01"
                className="form-control"
                id="itemPrice"
                value={menuScreenFormData.price}
                onChange={(e) => setMenuScreenFormData({
                  ...menuScreenFormData,
                  price: Number(e.target.value)
                })}
                required
              />
            </div>
            
            <div className="col-md-6">
              <label htmlFor="itemCategory" className="form-label">الفئة</label>
              <select
                className="form-select"
                id="itemCategory"
                value={menuScreenFormData.categoryId || menuScreenFormData.category}
                onChange={(e) => setMenuScreenFormData({
                  ...menuScreenFormData,
                  categoryId: e.target.value,
                  category: e.target.value
                })}
                required
              >
                <option value="">اختر الفئة</option>
                {categories && Array.isArray(categories) && categories.length > 0 ? (
                  categories.filter(cat => cat && (cat.isActive !== false)).map(category => (
                    <option key={category._id} value={category._id}>
                      {category.name}
                    </option>
                  ))
                ) : (
                  <option disabled>لا توجد فئات متاحة</option>
                )}
              </select>
              {(!categories || !Array.isArray(categories) || categories.length === 0) && (
                <div className="text-warning mt-1">
                  <small>⚠️ لم يتم تحميل الفئات بعد. يرجى الانتظار أو إعادة تحميل الصفحة.</small>
                </div>
              )}
              {categories && Array.isArray(categories) && categories.length > 0 && categories.filter(cat => cat && (cat.isActive !== false)).length === 0 && (
                <div className="text-warning mt-1">
                  <small>⚠️ لا توجد فئات نشطة. يرجى الذهاب إلى 
                    <strong> إدارة الفئات </strong>
                    وتفعيل فئة واحدة على الأقل.
                  </small>
                </div>
              )}
            </div>
            
            <div className="col-md-6">
              <label htmlFor="itemAvailability" className="form-label">حالة التوفر</label>
              <select
                className="form-select"
                id="itemAvailability"
                value={menuScreenFormData.isAvailable ? 'available' : 'unavailable'}
                onChange={(e) => setMenuScreenFormData({
                  ...menuScreenFormData,
                  isAvailable: e.target.value === 'available'
                })}
              >
                <option value="available">متاح</option>
                <option value="unavailable">غير متاح</option>
              </select>
            </div>
            
            <div className="col-md-6">
              <label htmlFor="itemPreparationTime" className="form-label">وقت التحضير (دقيقة)</label>
              <input
                type="number"
                className="form-control"
                id="itemPreparationTime"
                value={menuScreenFormData.preparationTime}
                onChange={(e) => setMenuScreenFormData({
                  ...menuScreenFormData,
                  preparationTime: Number(e.target.value)
                })}
              />
            </div>
            
            <div className="col-md-6">
              <label htmlFor="itemCalories" className="form-label">السعرات الحرارية</label>
              <input
                type="number"
                className="form-control"
                id="itemCalories"
                value={menuScreenFormData.calories}
                onChange={(e) => setMenuScreenFormData({
                  ...menuScreenFormData,
                  calories: Number(e.target.value)
                })}
              />
            </div>
            
            <div className="col-12">
              <label htmlFor="itemDescription" className="form-label">الوصف (اختياري)</label>
              <textarea
                className="form-control"
                id="itemDescription"
                rows={3}
                value={menuScreenFormData.description}
                onChange={(e) => setMenuScreenFormData({
                  ...menuScreenFormData,
                  description: e.target.value
                })}
              />
            </div>
            
            <div className="col-12">
              <label className="form-label">المكونات</label>
              <div className="input-group mb-2">
                <input
                  type="text"
                  className="form-control"
                  placeholder="إضافة مكون..."
                  value={menuScreenIngredientInput}
                  onChange={(e) => setMenuScreenIngredientInput(e.target.value)}
                  onKeyPress={(e) => {
                    if (e.key === 'Enter') {
                      e.preventDefault();
                      handleMenuScreenAddIngredient();
                    }
                  }}
                />
                <button
                  type="button"
                  className="btn btn-outline-primary"
                  onClick={handleMenuScreenAddIngredient}
                  aria-label="إضافة مكون"
                  title="إضافة مكون"
                >
                  <i className="fas fa-plus"></i>
                </button>
              </div>
              
              {menuScreenFormData.ingredients.length > 0 && (
                <div className="border rounded p-2">
                  {menuScreenFormData.ingredients.map((ingredient, index) => (
                    <span key={index} className="badge bg-secondary me-1 mb-1">
                      {ingredient}
                      <button
                        type="button"
                        className="btn-close btn-close-white btn-close-small ms-1"
                        onClick={() => handleMenuScreenRemoveIngredient(index)}
                        title="إزالة المكون"
                        aria-label="إزالة المكون"
                      ></button>
                    </span>
                  ))}
                </div>
              )}
            </div>
          </div>
        </form>
      </ResponsiveModal>
    </div>
  );
};

export default MenuManagerScreenBootstrap;

<!DOCTYPE html>
<html>
<head>
    <title>مسح الكاش</title>
</head>
<body>
    <h2>مسح الكاش المحلي</h2>
    <button onclick="clearAll()">مسح كل البيانات المحلية</button>
    <div id="result"></div>
    
    <script>
        function clearAll() {
            // مسح Local Storage
            localStorage.clear();
            
            // مسح Session Storage
            sessionStorage.clear();
            
            // مسح الكاش إذا كان متاحاً
            if ('caches' in window) {
                caches.keys().then(names => {
                    names.forEach(name => {
                        caches.delete(name);
                    });
                });
            }
            
            document.getElementById('result').innerHTML = '<p style="color: green;">تم مسح جميع البيانات المحلية بنجاح!</p>';
            
            // إعادة تحميل الصفحة بعد ثانيتين
            setTimeout(() => {
                window.location.href = 'http://localhost:3000';
            }, 2000);
        }
        
        // عرض محتويات الكاش الحالية
        window.onload = () => {
            const localStorageContent = {};
            for (let i = 0; i < localStorage.length; i++) {
                const key = localStorage.key(i);
                localStorageContent[key] = localStorage.getItem(key);
            }
            
            const info = document.createElement('div');
            info.innerHTML = `
                <h3>محتويات Local Storage الحالية:</h3>
                <pre>${JSON.stringify(localStorageContent, null, 2)}</pre>
                <h3>محتويات Session Storage الحالية:</h3>
                <pre>${JSON.stringify(sessionStorage, null, 2)}</pre>
            `;
            document.body.appendChild(info);
        };
    </script>
</body>
</html>

import React from 'react';

interface InventoryItem {
  _id: string;
  name: string;
  price: number;
  isAvailable: boolean;
  available?: boolean; // Legacy field
  description?: string;
  stock?: number | { quantity: number };
}

interface InventoryManagerScreenProps {
  menuItems: InventoryItem[];
  isLoading: boolean;
  loadingStates: {
    inventory: boolean;
  };
  onUpdateStock?: (itemId: string, newStock: number) => Promise<void>;
}

const InventoryManagerScreen: React.FC<InventoryManagerScreenProps> = ({
  menuItems,
  isLoading,
  loadingStates,
  onUpdateStock
}) => {
  const handleStockChange = async (itemId: string, change: number) => {
    const item = menuItems.find(i => i._id === itemId);
    if (!item || !onUpdateStock) return;
    
    const currentStock = typeof item.stock === 'number' 
      ? item.stock 
      : item.stock?.quantity || 0;
    
    const newStock = Math.max(0, currentStock + change);
    
    try {
      console.log(`🔄 تحديث المخزون لـ ${item.name}: ${currentStock} → ${newStock}`);
      await onUpdateStock(itemId, newStock);
      console.log(`✅ تم تحديث المخزون بنجاح`);
    } catch (error) {
      console.error('❌ خطأ في تحديث المخزون:', error);
    }
  };
  return (
    <div>
      <div>
        <div>
          <h1>
            <i aria-hidden="true"></i>
            إدارة المخزون
          </h1>
        </div>
      </div>

      {loadingStates.inventory ? (
        <div role="status" aria-label="جاري التحميل">
          <i aria-hidden="true"></i>
          <span>جاري تحميل بيانات المخزون...</span>
        </div>
      ) : (
        <div>
          {menuItems.length === 0 ? (
            <div>
              <div>
                <i aria-hidden="true"></i>
              </div>
              <h3>لا توجد عناصر في المخزون</h3>
              <p>يرجى إضافة عناصر إلى القائمة أولاً</p>
            </div>
          ) : (
            menuItems.map(item => {
              const stockQuantity = typeof item.stock === 'number' 
                ? item.stock 
                : item.stock?.quantity || 0;
              const isLowStock = stockQuantity < 10;
              const isAvailable = item.isAvailable !== undefined ? item.isAvailable : item.available;

              return (
                <div 
                  key={item._id}
                  role="article"
                  aria-label={`عنصر المخزون: ${item.name}`}
                >
                  <div>
                    <h3>{item.name}</h3>
                    <span 
                      role="status"
                      aria-label={`الحالة: ${isAvailable ? 'متوفر' : 'غير متوفر'}`}
                    >
                      {isAvailable ? 'متوفر' : 'غير متوفر'}
                    </span>
                  </div>

                  <div>
                    <div aria-label={`السعر: ${item.price} جنيه مصري`}>
                      {item.price} ج.م
                    </div>
                    <div 
                      aria-label={`المخزون: ${stockQuantity} قطعة${isLowStock ? ' - مخزون منخفض' : ''}`}
                    >
                      المخزون: {stockQuantity}
                      {isLowStock && (
                        <i 
                          aria-hidden="true"
                          title="مخزون منخفض"
                        ></i>
                      )}
                    </div>
                  </div>

                  {onUpdateStock && (
                    <div>
                      <div>
                        <i></i>
                        تحكم في المخزون
                      </div>
                      <div>
                        <button
                          onClick={() => handleStockChange(item._id, -1)}
                          title="تقليل الكمية"
                          aria-label="تقليل الكمية بواحد"
                          disabled={stockQuantity <= 0}
                        >
                          <i aria-hidden="true"></i>
                        </button>
                        <button
                          onClick={() => handleStockChange(item._id, -5)}
                          title="تقليل 5 قطع"
                          aria-label="تقليل الكمية بخمس قطع"
                          disabled={stockQuantity < 5}
                        >
                          -5
                        </button>
                        <button
                          onClick={() => handleStockChange(item._id, 5)}
                          title="إضافة 5 قطع"
                          aria-label="إضافة خمس قطع للكمية"
                        >
                          +5
                        </button>
                        <button
                          onClick={() => handleStockChange(item._id, 1)}
                          title="إضافة كمية"
                          aria-label="إضافة قطعة واحدة للكمية"
                        >
                          <i aria-hidden="true"></i>
                        </button>
                      </div>
                    </div>
                  )}

                  {item.description && (
                    <div title={item.description}>
                      {item.description}
                    </div>
                  )}
                </div>
              );
            })
          )}
        </div>
      )}
    </div>
  );
};

export default InventoryManagerScreen;

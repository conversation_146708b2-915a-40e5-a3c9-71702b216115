const mongoose = require('mongoose');

// اتصال بقاعدة البيانات (MongoDB Atlas Production)
const mongoUri = 'mongodb+srv://besomustafa:<EMAIL>/deshacoffee?retryWrites=true&w=majority&appName=MyCoffeChop';

mongoose.connect(mongoUri, {
  serverSelectionTimeoutMS: 10000,
  socketTimeoutMS: 45000,
  family: 4
});

// تعريف النماذج
const discountRequestSchema = new mongoose.Schema({
  // ... باقي الحقول
  waiterName: String,
  requestedBy: String
}, { strict: false });

const userSchema = new mongoose.Schema({
  name: String,
  username: String,
  // ... باقي الحقول
}, { strict: false });

const DiscountRequest = mongoose.model('DiscountRequest', discountRequestSchema);
const User = mongoose.model('User', userSchema);

async function fixDiscountRequestWaiterNames() {
  try {
    console.log('🔧 بدء تصحيح أسماء النادلين في طلبات الخصم...');

    // جلب جميع طلبات الخصم التي قد تحتوي على معرفات بدلاً من أسماء
    const discountRequests = await DiscountRequest.find({
      $or: [
        { waiterName: { $regex: /^[0-9a-fA-F]{24}$/ } }, // معرف MongoDB
        { waiterName: { $regex: /_/ } }, // يحتوي على underscore (اسم مستخدم)
        { waiterName: { $not: { $regex: / / } } }, // لا يحتوي على مسافة (قد يكون اسم مستخدم)
        { waiterName: null },
        { waiterName: '' },
        { waiterName: { $exists: false } }
      ]
    });

    console.log(`📊 تم العثور على ${discountRequests.length} طلب خصم يحتاج إلى تصحيح`);

    let updatedCount = 0;
    let errors = 0;

    for (const request of discountRequests) {
      try {
        let newWaiterName = null;

        // محاولة الحصول على الاسم من requestedBy
        if (request.requestedBy) {
          // أولاً، حاول البحث بالـ username
          let user = await User.findOne({ username: request.requestedBy });
          if (!user && request.requestedBy.length === 24) {
            // إذا لم يوجد وكان يبدو كمعرف، ابحث بالـ _id
            user = await User.findById(request.requestedBy);
          }
          if (user) {
            newWaiterName = user.name;
          }
        }

        // إذا كان waiterName موجود وبحاجة إلى تصحيح
        if (request.waiterName) {
          if (request.waiterName.length === 24) {
            // يبدو كمعرف MongoDB
            const user = await User.findById(request.waiterName);
            if (user) {
              newWaiterName = user.name;
            }
          } else if (request.waiterName.includes('_')) {
            // يبدو كاسم مستخدم
            const user = await User.findOne({ username: request.waiterName });
            if (user) {
              newWaiterName = user.name;
            }
          }
        }

        // تحديث طلب الخصم إذا تم العثور على اسم صحيح
        if (newWaiterName && newWaiterName !== request.waiterName) {
          await DiscountRequest.findByIdAndUpdate(request._id, {
            waiterName: newWaiterName
          });
          
          console.log(`✅ تم تحديث طلب الخصم ${request._id}:`);
          console.log(`   من: "${request.waiterName || 'غير محدد'}" إلى: "${newWaiterName}"`);
          updatedCount++;
        }

      } catch (error) {
        console.error(`❌ خطأ في تحديث طلب الخصم ${request._id}:`, error.message);
        errors++;
      }
    }

    console.log('\n📈 نتائج التصحيح:');
    console.log(`✅ تم تحديث: ${updatedCount} طلب خصم`);
    console.log(`❌ أخطاء: ${errors} طلب خصم`);
    console.log(`📊 المجموع: ${discountRequests.length} طلب خصم تم فحصه`);

  } catch (error) {
    console.error('❌ خطأ عام:', error);
  } finally {
    await mongoose.disconnect();
    console.log('📝 تم قطع الاتصال بقاعدة البيانات');
  }
}

// تشغيل الدالة
fixDiscountRequestWaiterNames().catch(console.error);

import React, { useState, useEffect, useCallback, useMemo } from 'react';
import { authenticatedGet, authenticatedPost, authenticatedPut, authenticatedDelete } from '../utils/apiHelpers';
import { useToast } from '../hooks/useToast';
import socket from '../socket';
import '../styles/screens/TablesScreenIsolated.css';

interface Order {
  _id: string;
  orderNumber: string;
  items: any[];
  totalAmount?: number;
  totalPrice?: number;
  totals?: {
    subtotal: number;
    tax: number;
    discount: number;
    total: number;
  };
  status: 'pending' | 'preparing' | 'ready' | 'completed' | 'delivered';
  tableNumber?: number;
  customerName?: string;
  waiterName?: string;
  waiterId?: string;
  chefName?: string;
  staff?: {
    waiter?: string;
    chef?: string;
  };
  createdAt: string;
  updatedAt: string;
}

interface TableAccount {
  _id: string;
  tableNumber: string;
  waiterName: string;
  totalAmount: number;
  isOpen: boolean;
  status: 'active' | 'closed';
  orders: Order[];
  createdAt: string;
  ordersCount?: number;
  activeOrdersCount?: number;
  totalSales?: number;
  capacity?: number;
  section?: string;
  lastOrderTime?: string;
  avgOrderTime?: number;
}

interface TablesManagerScreenProps {
  tableAccounts: TableAccount[];
  orders: Order[];
  onTablesUpdate: (tables: TableAccount[]) => void;
  onShowTableDetails: (table: TableAccount) => void;
  loading: boolean;
}

const TablesManagerScreenBootstrap: React.FC<TablesManagerScreenProps> = ({
  tableAccounts,
  orders,
  onTablesUpdate,
  onShowTableDetails,
  loading
}) => {
  const [tablesScreenSearchTerm, setTablesScreenSearchTerm] = useState('');
  const [tablesScreenStatusFilter, setTablesScreenStatusFilter] = useState<'all' | 'active' | 'closed'>('all');
  const [tablesScreenWaiterFilter, setTablesScreenWaiterFilter] = useState<string>('all');
  const [sortBy, setSortBy] = useState<'tableNumber' | 'totalAmount' | 'ordersCount' | 'lastActivity'>('tableNumber');
  const [sortOrder, setSortOrder] = useState<'asc' | 'desc'>('asc');
  const [viewMode, setViewMode] = useState<'grid' | 'list'>('grid');
  const [autoRefresh, setAutoRefresh] = useState(false);
  const [refreshInterval, setRefreshInterval] = useState<NodeJS.Timeout | null>(null);

  const toast = useToast();

  // Socket.IO event listeners للتحديثات الفورية
  useEffect(() => {
    const handleTableUpdate = (tableUpdate: any) => {
      console.log('🪑 تحديث حساب الطاولة:', tableUpdate);
      // يمكن تحديث الطاولة المحددة محلياً أو إعادة تحميل البيانات
      window.location.reload();
    };

    const handleTableClosed = (closedTable: any) => {
      console.log('🚪 إغلاق طاولة:', closedTable);
      window.location.reload();
    };

    const handleNewOrder = (orderData: any) => {
      console.log('📦 طلب جديد للطاولة:', orderData);
      if (orderData.tableNumber) {
        window.location.reload();
      }
    };

    const handleOrderStatusUpdate = (orderUpdate: any) => {
      console.log('🔄 تحديث حالة طلب الطاولة:', orderUpdate);
      if (orderUpdate.tableNumber) {
        window.location.reload();
      }
    };

    // إضافة event listeners
    socket.on('table-account-updated', handleTableUpdate);
    socket.on('table-account-closed', handleTableClosed);
    socket.on('new-order-notification', handleNewOrder);
    socket.on('order-status-update', handleOrderStatusUpdate);

    // تنظيف event listeners
    return () => {
      socket.off('table-account-updated', handleTableUpdate);
      socket.off('table-account-closed', handleTableClosed);
      socket.off('new-order-notification', handleNewOrder);
      socket.off('order-status-update', handleOrderStatusUpdate);
    };
  }, []);

  // Auto refresh functionality
  useEffect(() => {
    if (autoRefresh) {
      const interval = setInterval(() => {
        window.location.reload();
      }, 30000);
      setRefreshInterval(interval);
    } else {
      if (refreshInterval) {
        clearInterval(refreshInterval);
        setRefreshInterval(null);
      }
    }

    return () => {
      if (refreshInterval) {
        clearInterval(refreshInterval);
      }
    };
  }, [autoRefresh, refreshInterval]);

  // Get unique waiters from table accounts
  const waiters = useMemo(() => 
    Array.from(new Set(
      tableAccounts
        .map(table => table.waiterName)
        .filter(name => name && name.trim() !== '')
    )), [tableAccounts]
  );

  // Helper functions
  const getTableTotalAmount = useCallback((table: TableAccount) => {
    return table.totalSales || table.totalAmount || 0;
  }, []);

  const getTableOrdersCount = useCallback((table: TableAccount) => {
    return table.ordersCount || table.orders?.length || 0;
  }, []);

  const handleTablesScreenCloseTable = async (tableId: string) => {
    if (!window.confirm('هل أنت متأكد من إغلاق هذه الطاولة؟')) return;
    
    try {
      const response = await authenticatedPut(`/api/table-accounts/${tableId}/close`, {});
      if (response.success) {
        const updatedTables = tableAccounts.map(table => 
          table._id === tableId ? { ...table, isOpen: false, status: 'closed' as const } : table
        );
        onTablesUpdate(updatedTables);
        toast.showSuccess('تم إغلاق الطاولة بنجاح');
      }
    } catch (error) {
      toast.showError('حدث خطأ أثناء إغلاق الطاولة');
    }
  };

  const handleTablesScreenReopenTable = async (tableId: string) => {
    if (!window.confirm('هل أنت متأكد من إعادة فتح هذه الطاولة؟')) return;
    
    try {
      const response = await authenticatedPut(`/api/table-accounts/${tableId}/reopen`, {});
      if (response.success) {
        const updatedTables = tableAccounts.map(table => 
          table._id === tableId ? { ...table, isOpen: true, status: 'active' as const } : table
        );
        onTablesUpdate(updatedTables);
        toast.showSuccess('تم إعادة فتح الطاولة بنجاح');
      }
    } catch (error) {
      toast.showError('حدث خطأ أثناء إعادة فتح الطاولة');
    }
  };

  const getTableActiveOrdersCount = useCallback((table: TableAccount) => {
    if (table.activeOrdersCount !== undefined) {
      return table.activeOrdersCount;
    }
    
    const tableOrders = orders.filter(order => 
      order.tableNumber?.toString() === table.tableNumber.toString() &&
      ['pending', 'preparing', 'ready'].includes(order.status)
    );
    
    return tableOrders.length;
  }, [orders]);

  // Sort and filter tables
  const filteredTables = useMemo(() => {
    let filtered = tableAccounts.filter(table => {
      const matchesSearch = table.tableNumber?.toString().includes(tablesScreenSearchTerm) ||
                           table.waiterName?.toLowerCase().includes(tablesScreenSearchTerm.toLowerCase());
      const matchesStatus = tablesScreenStatusFilter === 'all' ||
                           (tablesScreenStatusFilter === 'active' && table.isOpen) ||
                           (tablesScreenStatusFilter === 'closed' && !table.isOpen);
      const matchesWaiter = tablesScreenWaiterFilter === 'all' || table.waiterName === tablesScreenWaiterFilter;
      
      return matchesSearch && matchesStatus && matchesWaiter;
    });

    filtered.sort((a, b) => {
      let aValue, bValue;
      
      switch (sortBy) {
        case 'tableNumber':
          aValue = Number(a.tableNumber);
          bValue = Number(b.tableNumber);
          break;
        case 'totalAmount':
          aValue = getTableTotalAmount(a);
          bValue = getTableTotalAmount(b);
          break;
        case 'ordersCount':
          aValue = getTableOrdersCount(a);
          bValue = getTableOrdersCount(b);
          break;
        case 'lastActivity':
          aValue = new Date(a.lastOrderTime || a.createdAt).getTime();
          bValue = new Date(b.lastOrderTime || b.createdAt).getTime();
          break;
        default:
          aValue = Number(a.tableNumber);
          bValue = Number(b.tableNumber);
      }

      if (sortOrder === 'asc') {
        return aValue < bValue ? -1 : aValue > bValue ? 1 : 0;
      } else {
        return aValue > bValue ? -1 : aValue < bValue ? 1 : 0;
      }
    });

    return filtered;
  }, [tableAccounts, tablesScreenSearchTerm, tablesScreenStatusFilter, tablesScreenWaiterFilter, sortBy, sortOrder, getTableTotalAmount, getTableOrdersCount]);

  const getTableLastActivity = useCallback((table: TableAccount) => {
    if (table.lastOrderTime) {
      return new Date(table.lastOrderTime);
    }
    return new Date(table.createdAt);
  }, []);

  const getTableDuration = useCallback((table: TableAccount) => {
    if (!table.isOpen) return null;
    
    const start = new Date(table.createdAt);
    const now = new Date();
    const duration = now.getTime() - start.getTime();
    
    const hours = Math.floor(duration / (1000 * 60 * 60));
    const minutes = Math.floor((duration % (1000 * 60 * 60)) / (1000 * 60));
    
    if (hours > 0) {
      return `${hours}س ${minutes}د`;
    }
    return `${minutes}د`;
  }, []);

  const getTablePriority = useCallback((table: TableAccount) => {
    const activeOrders = getTableActiveOrdersCount(table);
    const duration = table.isOpen ? (new Date().getTime() - new Date(table.createdAt).getTime()) / (1000 * 60) : 0;
    
    if (activeOrders >= 3 || duration > 120) return 'high';
    if (activeOrders >= 2 || duration > 60) return 'medium';
    return 'low';
  }, [getTableActiveOrdersCount]);

  const exportTableData = useCallback(() => {
    const csvContent = [
      ['رقم الطاولة', 'النادل', 'الحالة', 'عدد الطلبات', 'إجمالي المبلغ', 'تاريخ الإنشاء'],
      ...filteredTables.map(table => [
        table.tableNumber,
        table.waiterName || 'غير محدد',
        table.isOpen ? 'نشطة' : 'مغلقة',
        getTableOrdersCount(table),
        getTableTotalAmount(table).toFixed(2),
        new Date(table.createdAt).toLocaleDateString('ar-EG')
      ])
    ].map(row => row.join(',')).join('\n');
    
    const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
    const link = document.createElement('a');
    link.href = URL.createObjectURL(blob);
    link.download = `tables_report_${new Date().toISOString().split('T')[0]}.csv`;
    link.click();
    toast.showSuccess('تم تصدير البيانات بنجاح');
  }, [filteredTables, getTableOrdersCount, getTableTotalAmount, toast]);

  return (
    <div className="container-fluid p-4" dir="rtl">
      {/* Header Section */}
      <div className="row mb-4">
        <div className="col-12">
          <div className="card border-0 shadow-sm bg-gradient">
            <div className="card-body p-4">
              <div className="row align-items-center">
                <div className="col-lg-6">
                  <h2 className="h3 fw-bold text-dark mb-2">إدارة الطاولات</h2>
                  <p className="text-muted mb-0">متابعة وإدارة حسابات الطاولات</p>
                </div>
                <div className="col-lg-6">
                  <div className="row g-3">
                    <div className="col-sm-6">
                      <div className="d-flex align-items-center">
                        <span className="me-2 text-muted">طاولات تحتاج انتباه:</span>
                        <span className="badge bg-danger fs-6">
                          {filteredTables.filter(table => getTablePriority(table) === 'high').length}
                        </span>
                      </div>
                    </div>
                    <div className="col-sm-6">
                      <div className="d-flex align-items-center">
                        <span className="me-2 text-muted">متوسط وقت الخدمة:</span>
                        <span className="badge bg-info fs-6">
                          {tableAccounts.filter(table => table.avgOrderTime).length > 0
                            ? `${Math.round(tableAccounts.reduce((sum, table) => sum + (table.avgOrderTime || 0), 0) / tableAccounts.filter(table => table.avgOrderTime).length)}د`
                            : 'غير متاح'
                          }
                        </span>
                      </div>
                    </div>
                    {autoRefresh && (
                      <div className="col-12">
                        <div className="d-flex align-items-center">
                          <span className="me-2 text-muted">التحديث التلقائي:</span>
                          <span className="badge bg-success fs-6">
                            <i className="fas fa-sync-alt fa-spin me-1"></i>
                            نشط
                          </span>
                        </div>
                      </div>
                    )}
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Controls Section */}
      <div className="row mb-4">
        <div className="col-12">
          <div className="card border-0 shadow-sm">
            <div className="card-body p-4">
              {/* Search and Filters Row */}
              <div className="row g-3 mb-3">
                <div className="col-lg-4">
                  <div className="position-relative">
                    <input
                      type="text"
                      className="form-control form-control-lg pe-5"
                      placeholder="البحث عن طاولة أو نادل..."
                      value={tablesScreenSearchTerm}
                      onChange={(e) => setTablesScreenSearchTerm(e.target.value)}
                    />
                    <i className="fas fa-search position-absolute top-50 end-0 translate-middle-y me-3 text-muted"></i>
                    {tablesScreenSearchTerm && (
                      <button 
                        className="btn btn-link position-absolute top-50 start-0 translate-middle-y text-danger"
                        onClick={() => setTablesScreenSearchTerm('')}
                        title="مسح البحث"
                      >
                        <i className="fas fa-times"></i>
                      </button>
                    )}
                  </div>
                </div>
                
                <div className="col-lg-2 col-md-4">
                  <select
                    className="form-select form-select-lg"
                    value={tablesScreenStatusFilter}
                    onChange={(e) => setTablesScreenStatusFilter(e.target.value as any)}
                    title="تصفية حسب حالة الطاولة"
                  >
                    <option value="all">جميع الحالات</option>
                    <option value="active">نشطة</option>
                    <option value="closed">مغلقة</option>
                  </select>
                </div>
                
                <div className="col-lg-2 col-md-4">
                  <select
                    className="form-select form-select-lg"
                    value={tablesScreenWaiterFilter}
                    onChange={(e) => setTablesScreenWaiterFilter(e.target.value)}
                    title="تصفية حسب النادل"
                  >
                    <option value="all">جميع النُدُل</option>
                    {waiters.map(waiter => (
                      <option key={waiter} value={waiter}>{waiter}</option>
                    ))}
                  </select>
                </div>

                <div className="col-lg-2 col-md-4">
                  <select
                    className="form-select form-select-lg"
                    value={sortBy}
                    onChange={(e) => setSortBy(e.target.value as any)}
                    title="ترتيب حسب"
                  >
                    <option value="tableNumber">رقم الطاولة</option>
                    <option value="totalAmount">المبلغ الإجمالي</option>
                    <option value="ordersCount">عدد الطلبات</option>
                    <option value="lastActivity">آخر نشاط</option>
                  </select>
                </div>

                <div className="col-lg-2">
                  <div className="btn-group w-100" role="group">
                    <button
                      className={`btn btn-outline-primary ${sortOrder === 'asc' ? 'active' : ''}`}
                      onClick={() => setSortOrder('asc')}
                      title="ترتيب تصاعدي"
                    >
                      <i className="fas fa-sort-up"></i>
                    </button>
                    <button
                      className={`btn btn-outline-primary ${sortOrder === 'desc' ? 'active' : ''}`}
                      onClick={() => setSortOrder('desc')}
                      title="ترتيب تنازلي"
                    >
                      <i className="fas fa-sort-down"></i>
                    </button>
                  </div>
                </div>
              </div>

              {/* View Controls Row */}
              <div className="row g-3 align-items-center">
                <div className="col-lg-4">
                  <div className="btn-group" role="group">
                    <button
                      className={`btn ${viewMode === 'grid' ? 'btn-primary' : 'btn-outline-primary'}`}
                      onClick={() => setViewMode('grid')}
                      title="عرض شبكي"
                    >
                      <i className="fas fa-th me-1"></i>
                      شبكي
                    </button>
                    <button
                      className={`btn ${viewMode === 'list' ? 'btn-primary' : 'btn-outline-primary'}`}
                      onClick={() => setViewMode('list')}
                      title="عرض قائمة"
                    >
                      <i className="fas fa-list me-1"></i>
                      قائمة
                    </button>
                  </div>
                </div>

                <div className="col-lg-4">
                  <div className="form-check form-switch">
                    <input
                      className="form-check-input"
                      type="checkbox"
                      id="autoRefresh"
                      checked={autoRefresh}
                      onChange={(e) => setAutoRefresh(e.target.checked)}
                    />
                    <label className="form-check-label" htmlFor="autoRefresh">
                      <i className="fas fa-sync-alt me-1"></i>
                      تحديث تلقائي
                    </label>
                  </div>
                </div>

                <div className="col-lg-4">
                  <div className="btn-group w-100" role="group">
                    <button
                      className="btn btn-outline-success"
                      onClick={exportTableData}
                      title="تصدير البيانات"
                    >
                      <i className="fas fa-download me-1"></i>
                      تصدير
                    </button>
                    
                    <button
                      className="btn btn-outline-info"
                      onClick={() => window.location.reload()}
                      title="تحديث البيانات"
                    >
                      <i className="fas fa-refresh me-1"></i>
                      تحديث
                    </button>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Statistics Cards */}
      <div className="row g-4 mb-4">
        <div className="col-xl-2 col-lg-4 col-md-6">
          <div className="card border-0 shadow-sm h-100 bg-primary bg-gradient text-white">
            <div className="card-body text-center">
              <i className="fas fa-table fa-2x mb-2"></i>
              <h4 className="fw-bold">{tableAccounts.length}</h4>
              <p className="mb-0 small">إجمالي الطاولات</p>
            </div>
          </div>
        </div>
        
        <div className="col-xl-2 col-lg-4 col-md-6">
          <div className="card border-0 shadow-sm h-100 bg-success bg-gradient text-white">
            <div className="card-body text-center">
              <i className="fas fa-door-open fa-2x mb-2"></i>
              <h4 className="fw-bold">{tableAccounts.filter(table => table.isOpen).length}</h4>
              <p className="mb-0 small">الطاولات النشطة</p>
            </div>
          </div>
        </div>
        
        <div className="col-xl-2 col-lg-4 col-md-6">
          <div className="card border-0 shadow-sm h-100 bg-secondary bg-gradient text-white">
            <div className="card-body text-center">
              <i className="fas fa-door-closed fa-2x mb-2"></i>
              <h4 className="fw-bold">{tableAccounts.filter(table => !table.isOpen).length}</h4>
              <p className="mb-0 small">الطاولات المغلقة</p>
            </div>
          </div>
        </div>
        
        <div className="col-xl-2 col-lg-4 col-md-6">
          <div className="card border-0 shadow-sm h-100 bg-warning bg-gradient text-white">
            <div className="card-body text-center">
              <i className="fas fa-money-bill-wave fa-2x mb-2"></i>
              <h4 className="fw-bold">{tableAccounts.reduce((sum, table) => sum + getTableTotalAmount(table), 0).toFixed(2)}</h4>
              <p className="mb-0 small">إجمالي المبيعات (ج.م)</p>
            </div>
          </div>
        </div>

        <div className="col-xl-2 col-lg-4 col-md-6">
          <div className="card border-0 shadow-sm h-100 bg-info bg-gradient text-white">
            <div className="card-body text-center">
              <i className="fas fa-receipt fa-2x mb-2"></i>
              <h4 className="fw-bold">{tableAccounts.reduce((sum, table) => sum + getTableActiveOrdersCount(table), 0)}</h4>
              <p className="mb-0 small">الطلبات النشطة</p>
            </div>
          </div>
        </div>

        <div className="col-xl-2 col-lg-4 col-md-6">
          <div className="card border-0 shadow-sm h-100 bg-dark bg-gradient text-white">
            <div className="card-body text-center">
              <i className="fas fa-users fa-2x mb-2"></i>
              <h4 className="fw-bold">{waiters.length}</h4>
              <p className="mb-0 small">النُدُل النشطون</p>
            </div>
          </div>
        </div>
      </div>

      {/* Tables Grid/List */}
      <div className="row">
        <div className="col-12">
          {loading ? (
            <div className="text-center py-5">
              <div className="spinner-border text-primary" role="status">
                <span className="visually-hidden">جاري التحميل...</span>
              </div>
              <p className="mt-3 text-muted">جاري تحميل بيانات الطاولات...</p>
            </div>
          ) : tableAccounts.length === 0 ? (
            <div className="text-center py-5">
              <i className="fas fa-table fa-4x text-muted mb-3"></i>
              <h3 className="text-muted">لا توجد طاولات</h3>
              <p className="text-muted">لم يتم إنشاء أي طاولات بعد. سيتم إنشاء الطاولات تلقائياً عند إنشاء أول طلب.</p>
              <div className="alert alert-info">
                <small>
                  <strong>معلومات التشخيص:</strong><br/>
                  عدد الطاولات: {tableAccounts.length}<br/>
                  عدد الطلبات: {orders.length}<br/>
                  حالة التحميل: {loading ? 'جاري التحميل' : 'تم التحميل'}
                </small>
              </div>
            </div>
          ) : filteredTables.length === 0 ? (
            <div className="text-center py-5">
              <i className="fas fa-search fa-4x text-muted mb-3"></i>
              <h3 className="text-muted">لا توجد نتائج</h3>
              <p className="text-muted">لا توجد طاولات مطابقة لمعايير البحث والفلترة الحالية</p>
              <div className="alert alert-warning">
                <small>
                  المرشحات النشطة:<br/>
                  الحالة: {tablesScreenStatusFilter === 'all' ? 'الكل' : tablesScreenStatusFilter}<br/>
                  النادل: {tablesScreenWaiterFilter === 'all' ? 'الكل' : tablesScreenWaiterFilter}<br/>
                  البحث: {tablesScreenSearchTerm || 'بدون بحث'}
                </small>
              </div>
            </div>
          ) : (
            <div className={viewMode === 'grid' ? 'row g-4' : ''}>
              {filteredTables.map(table => {
                const priority = getTablePriority(table);
                const duration = getTableDuration(table);
                const lastActivity = getTableLastActivity(table);
                
                return (
                  <div 
                    key={table._id}
                    className={viewMode === 'grid' ? 'col-xl-4 col-lg-6 col-md-12' : 'col-12 mb-3'}
                  >
                    <div className={`card border-0 shadow-sm h-100 ${table.isOpen ? 'border-start border-success border-4' : 'border-start border-secondary border-4'}`}>
                      <div className="card-header bg-transparent border-0 pb-0">
                        <div className="d-flex justify-content-between align-items-start">
                          <div className="d-flex align-items-center">
                            <i className="fas fa-table text-primary me-2"></i>
                            <span className="fw-bold">طاولة {table.tableNumber}</span>
                            {table.capacity && (
                              <span className="badge bg-light text-dark ms-2">
                                <i className="fas fa-users me-1"></i>
                                {table.capacity}
                              </span>
                            )}
                          </div>
                          <div className="d-flex align-items-center gap-2">
                            <span className={`badge ${table.isOpen ? 'bg-success' : 'bg-secondary'}`}>
                              <i className={`fas ${table.isOpen ? 'fa-circle' : 'fa-circle'} me-1`}></i>
                              {table.isOpen ? 'نشطة' : 'مغلقة'}
                            </span>
                            {priority !== 'low' && (
                              <span className={`badge ${priority === 'high' ? 'bg-danger' : 'bg-warning'}`}>
                                <i className="fas fa-exclamation-triangle me-1"></i>
                                {priority === 'high' ? 'عالي' : 'متوسط'}
                              </span>
                            )}
                          </div>
                        </div>
                      </div>

                      <div className="card-body">
                        <div className="row g-3">
                          <div className="col-12">
                            <div className="d-flex align-items-center">
                              <i className="fas fa-user text-muted me-2"></i>
                              <span>{table.waiterName || 'غير محدد'}</span>
                              {table.section && (
                                <span className="badge bg-light text-dark ms-auto">
                                  <i className="fas fa-building me-1"></i>
                                  {table.section}
                                </span>
                              )}
                            </div>
                          </div>
                          
                          <div className="col-6">
                            <div className="text-center">
                              <div className="h5 mb-1 text-primary">{getTableOrdersCount(table)}</div>
                              <small className="text-muted">إجمالي الطلبات</small>
                            </div>
                          </div>
                          
                          <div className="col-6">
                            <div className="text-center">
                              <div className="h5 mb-1 text-warning">{getTableActiveOrdersCount(table)}</div>
                              <small className="text-muted">طلبات نشطة</small>
                            </div>
                          </div>
                          
                          <div className="col-12">
                            <div className="text-center p-3 bg-light rounded">
                              <div className="h4 mb-1 text-success">{getTableTotalAmount(table).toFixed(2)} ج.م</div>
                              <small className="text-muted">إجمالي المبلغ</small>
                            </div>
                          </div>
                          
                          <div className="col-12">
                            <div className="small text-muted">
                              <div className="d-flex align-items-center mb-1">
                                <i className="fas fa-calendar me-2"></i>
                                <span>{new Date(table.createdAt).toLocaleDateString('ar-EG')}</span>
                              </div>
                              {duration && (
                                <div className="d-flex align-items-center mb-1">
                                  <i className="fas fa-clock me-2"></i>
                                  <span>مفتوحة منذ: {duration}</span>
                                </div>
                              )}
                              <div className="d-flex align-items-center">
                                <i className="fas fa-history me-2"></i>
                                <span>آخر نشاط: {lastActivity.toLocaleTimeString('ar-EG', { hour: '2-digit', minute: '2-digit' })}</span>
                              </div>
                            </div>
                          </div>
                        </div>
                      </div>

                      <div className="card-footer bg-transparent border-0">
                        <div className="d-grid gap-2 d-md-flex">
                          <button
                            className="btn btn-primary flex-fill"
                            onClick={() => onShowTableDetails(table)}
                            title="عرض التفاصيل"
                          >
                            <i className="fas fa-eye me-1"></i>
                            التفاصيل
                          </button>
                          
                          {table.isOpen ? (
                            <button
                              className="btn btn-outline-danger flex-fill"
                              onClick={() => handleTablesScreenCloseTable(table._id)}
                              title="إغلاق الطاولة"
                            >
                              <i className="fas fa-door-closed me-1"></i>
                              إغلاق
                            </button>
                          ) : (
                            <button
                              className="btn btn-outline-success flex-fill"
                              onClick={() => handleTablesScreenReopenTable(table._id)}
                              title="إعادة فتح"
                            >
                              <i className="fas fa-door-open me-1"></i>
                              إعادة فتح
                            </button>
                          )}
                        </div>
                      </div>
                    </div>
                  </div>
                );
              })}
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default TablesManagerScreenBootstrap;

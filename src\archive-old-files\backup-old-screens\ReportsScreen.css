/* تنسيقات شاشة التقارير - تصميم محسن ومعاصر */

/* الشاشة الأساسية */
.reports-screen {
  padding: 2rem;
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
  min-height: 100vh;
  direction: rtl;
}

/* رأس القسم */
.reports-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 2rem;
  background: linear-gradient(135deg, #9b59b6 0%, #8e44ad 100%);
  color: white;
  padding: 2rem;
  border-radius: 20px;
  box-shadow: 0 10px 30px rgba(155, 89, 182, 0.3);
  border: none;
  position: relative;
  overflow: hidden;
}

.reports-header::before {
  content: '';
  position: absolute;
  top: -50%;
  left: -50%;
  width: 200%;
  height: 200%;
  background: linear-gradient(45deg, transparent, rgba(255,255,255,0.1), transparent);
  transform: rotate(45deg);
  animation: shimmer 3s infinite;
}

.reports-header h1 {
  color: white;
  margin: 0;
  font-size: 2.2rem;
  display: flex;
  align-items: center;
  gap: 1rem;
  font-weight: 700;
  text-shadow: 0 2px 4px rgba(0,0,0,0.3);
  position: relative;
  z-index: 1;
}

.reports-header h1 i {
  font-size: 2.5rem;
  background: linear-gradient(45deg, #ffffff, #ecf0f1);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  filter: drop-shadow(0 2px 4px rgba(0,0,0,0.3));
}

/* فلاتر التقارير */
.reports-filters {
  background: white;
  padding: 1.5rem;
  border-radius: 12px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  margin-bottom: 2rem;
}

.filters-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 1rem;
  align-items: end;
}

.filter-group {
  display: flex;
  flex-direction: column;
}

.filter-group label {
  color: #495057;
  font-weight: 600;
  margin-bottom: 0.5rem;
  font-size: 0.9rem;
}

.filter-group input,
.filter-group select {
  padding: 0.8rem;
  border: 2px solid #e9ecef;
  border-radius: 8px;
  font-size: 0.9rem;
  transition: border-color 0.3s ease;
}

.filter-group input:focus,
.filter-group select:focus {
  outline: none;
  border-color: #9b59b6;
  box-shadow: 0 0 0 3px rgba(155, 89, 182, 0.1);
}

.generate-report-btn {
  background: linear-gradient(135deg, #9b59b6 0%, #8e44ad 100%);
  color: white;
  border: none;
  padding: 0.8rem 1.5rem;
  border-radius: 8px;
  cursor: pointer;
  font-size: 0.9rem;
  font-weight: 600;
  transition: all 0.3s ease;
  height: fit-content;
}

.generate-report-btn:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 15px rgba(155, 89, 182, 0.3);
}

/* أنواع التقارير */
.reports-types {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 1.5rem;
  margin-bottom: 2rem;
}

.report-type-card {
  background: white;
  border-radius: 12px;
  padding: 1.5rem;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
  border: 1px solid #e9ecef;
  cursor: pointer;
}

.report-type-card:hover {
  transform: translateY(-3px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

.report-type-card.active {
  border-color: #9b59b6;
  background: linear-gradient(135deg, #ffffff, #f8f6fb);
}

.report-card-header {
  display: flex;
  align-items: center;
  gap: 1rem;
  margin-bottom: 1rem;
}

.report-icon {
  width: 50px;
  height: 50px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.5rem;
  color: white;
}

.report-icon.sales {
  background: linear-gradient(135deg, #27ae60, #2ecc71);
}

.report-icon.orders {
  background: linear-gradient(135deg, #3498db, #5dade2);
}

.report-icon.employees {
  background: linear-gradient(135deg, #f39c12, #f1c40f);
}

.report-icon.inventory {
  background: linear-gradient(135deg, #e74c3c, #ec7063);
}

.report-card-title {
  color: #2c3e50;
  font-size: 1.2rem;
  font-weight: 600;
  margin: 0;
}

.report-card-description {
  color: #6c757d;
  font-size: 0.9rem;
  line-height: 1.5;
  margin-bottom: 1rem;
}

.report-metrics {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.report-metric {
  text-align: center;
}

.metric-value {
  font-size: 1.4rem;
  font-weight: bold;
  color: #2c3e50;
}

.metric-label {
  font-size: 0.8rem;
  color: #6c757d;
}

/* نتائج التقارير */
.reports-results {
  background: white;
  border-radius: 12px;
  padding: 1.5rem;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  margin-bottom: 2rem;
}

.results-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1.5rem;
  padding-bottom: 1rem;
  border-bottom: 2px solid #f1f3f4;
}

.results-title {
  color: #2c3e50;
  font-size: 1.3rem;
  font-weight: 600;
  margin: 0;
}

.export-buttons {
  display: flex;
  gap: 0.5rem;
}

.export-btn {
  padding: 0.6rem 1rem;
  border: none;
  border-radius: 6px;
  cursor: pointer;
  font-size: 0.8rem;
  font-weight: 600;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  gap: 0.4rem;
}

.export-btn.excel {
  background: #198754;
  color: white;
}

.export-btn.pdf {
  background: #dc3545;
  color: white;
}

.export-btn.print {
  background: #6c757d;
  color: white;
}

.export-btn:hover {
  transform: translateY(-1px);
  opacity: 0.9;
}

/* جدول النتائج */
.results-table {
  width: 100%;
  border-collapse: collapse;
  margin-top: 1rem;
}

.results-table th {
  background: #f8f9fa;
  padding: 1rem;
  text-align: right;
  font-weight: 600;
  color: #495057;
  border-bottom: 2px solid #dee2e6;
  font-size: 0.9rem;
}

.results-table td {
  padding: 0.8rem 1rem;
  border-bottom: 1px solid #e9ecef;
  color: #495057;
  font-size: 0.9rem;
}

.results-table tr:hover {
  background: #f8f9fa;
}

/* مخططات التقارير */
.reports-charts {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
  gap: 1.5rem;
  margin-bottom: 2rem;
}

.chart-container {
  background: white;
  border-radius: 12px;
  padding: 1.5rem;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
}

.chart-header {
  margin-bottom: 1rem;
  padding-bottom: 0.5rem;
  border-bottom: 1px solid #e9ecef;
}

.chart-title {
  color: #2c3e50;
  font-size: 1.1rem;
  font-weight: 600;
  margin: 0;
}

.chart-placeholder {
  height: 300px;
  background: #f8f9fa;
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #6c757d;
  font-size: 1rem;
}

/* ملخص التقرير */
.report-summary {
  background: linear-gradient(135deg, #9b59b6, #8e44ad);
  color: white;
  padding: 2rem;
  border-radius: 12px;
  margin-bottom: 2rem;
}

.summary-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 1.5rem;
}

.summary-item {
  text-align: center;
}

.summary-value {
  font-size: 2.5rem;
  font-weight: bold;
  margin-bottom: 0.5rem;
}

.summary-label {
  font-size: 1rem;
  opacity: 0.9;
}

.summary-change {
  font-size: 0.9rem;
  margin-top: 0.5rem;
}

.summary-change.positive {
  color: #2ecc71;
}

.summary-change.negative {
  color: #e74c3c;
}

/* حالة التحميل */
.loading-reports {
  text-align: center;
  padding: 3rem;
  color: #6c757d;
}

.loading-spinner {
  width: 50px;
  height: 50px;
  border: 4px solid #f3f3f3;
  border-top: 4px solid #9b59b6;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin: 0 auto 1rem;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* تنسيقات الإحصائيات العامة للتقارير */
.reports-screen .stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 1.5rem;
  margin-bottom: 3rem;
  width: 100%;
  max-width: 100%;
  overflow-x: auto;
}

/* تنسيقات البحث للتقارير */
.reports-screen .search-container {
  position: relative;
  display: flex;
  align-items: center;
  background: white;
  border: 2px solid #e9ecef;
  border-radius: 12px;
  overflow: hidden;
  transition: all 0.3s ease;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

.reports-screen .search-container:hover {
  border-color: #9b59b6;
  box-shadow: 0 4px 15px rgba(155, 89, 182, 0.15);
}

.reports-screen .search-container:focus-within {
  border-color: #9b59b6;
  box-shadow: 0 0 0 3px rgba(155, 89, 182, 0.1);
}

.reports-screen .search-icon {
  padding: 0.7rem 1rem;
  color: #6c757d;
  background: #f8f9fa;
  border-right: 1px solid #e9ecef;
}

.reports-screen .search-input {
  border: none;
  outline: none;
  padding: 0.7rem 1rem;
  font-size: 0.9rem;
  min-width: 200px;
  background: transparent;
}

.reports-screen .search-input::placeholder {
  color: #adb5bd;
  font-style: italic;
}

.reports-screen .clear-search {
  padding: 0.5rem;
  background: none;
  border: none;
  color: #6c757d;
  cursor: pointer;
  transition: color 0.2s ease;
}

.reports-screen .clear-search:hover {
  color: #e74c3c;
}

/* تنسيقات المرشحات للتقارير */
.reports-screen .filter-select {
  padding: 0.7rem 1.2rem;
  border: 2px solid #e9ecef;
  border-radius: 12px;
  background: white;
  font-size: 0.9rem;
  font-weight: 500;
  min-width: 150px;
  color: #495057;
  transition: all 0.3s ease;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

.reports-screen .filter-select:hover {
  border-color: #9b59b6;
  box-shadow: 0 4px 15px rgba(155, 89, 182, 0.15);
  transform: translateY(-1px);
}

.reports-screen .filter-select:focus {
  outline: none;
  border-color: #9b59b6;
  box-shadow: 0 0 0 3px rgba(155, 89, 182, 0.1);
}

/* شبكة التقارير */
.reports-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: 2rem;
  margin-bottom: 2rem;
}

/* بطاقة التقرير المحسنة */
.report-card {
  background: linear-gradient(145deg, #ffffff 0%, #f8f9fa 100%);
  border-radius: 20px;
  padding: 2rem;
  box-shadow: 0 10px 30px rgba(155, 89, 182, 0.15);
  transition: all 0.4s ease;
  border: none;
  position: relative;
  overflow: hidden;
  cursor: pointer;
}

.report-card::before {
  content: '';
  position: absolute;
  top: 0;
  right: 0;
  left: 0;
  height: 5px;
  background: linear-gradient(90deg, #9b59b6 0%, #8e44ad 50%, #663399 100%);
  border-radius: 20px 20px 0 0;
}

.report-card:hover {
  transform: translateY(-8px) scale(1.02);
  box-shadow: 0 20px 50px rgba(155, 89, 182, 0.3);
}

.report-card-icon {
  width: 80px;
  height: 80px;
  border-radius: 50%;
  background: linear-gradient(135deg, #9b59b6 0%, #8e44ad 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 2rem;
  margin: 0 auto 1.5rem;
  box-shadow: 0 8px 25px rgba(155, 89, 182, 0.4);
}

/* التصميم المتجاوب */
@media (max-width: 768px) {
  .reports-screen {
    padding: 1rem;
  }
  
  .reports-header {
    flex-direction: column;
    gap: 1rem;
    text-align: center;
  }
  
  .reports-header h1 {
    font-size: 1.5rem;
  }
  
  .filters-grid {
    grid-template-columns: 1fr;
  }
  
  .reports-types {
    grid-template-columns: 1fr;
  }
  
  .report-metrics {
    flex-direction: column;
    gap: 1rem;
  }
  
  .results-header {
    flex-direction: column;
    gap: 1rem;
    align-items: flex-start;
  }
  
  .export-buttons {
    width: 100%;
    justify-content: space-between;
  }
  
  .reports-charts {
    grid-template-columns: 1fr;
  }
  
  .summary-grid {
    grid-template-columns: repeat(2, 1fr);
  }
}

@media (max-width: 480px) {
  .summary-grid {
    grid-template-columns: 1fr;
  }
  
  .export-buttons {
    flex-direction: column;
  }
  
  .results-table {
    font-size: 0.8rem;
  }
  
  .results-table th,
  .results-table td {
    padding: 0.5rem;
  }
}

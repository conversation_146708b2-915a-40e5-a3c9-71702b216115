const fetch = require('node-fetch');

async function testTableOrdersAfterFix() {
  console.log('🧪 اختبار الطاولات والطلبات بعد الإصلاح...\n');
  
  try {
    // تسجيل الدخول
    const loginResponse = await fetch('https://deshacoffee-production.up.railway.app/api/auth/login', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        username: '<PERSON><PERSON>',
        password: '253040',
        role: 'نادل'
      })
    });
    
    const loginResult = await loginResponse.json();
    if (!loginResult.token) {
      console.log('❌ فشل في تسجيل الدخول');
      return;
    }
    
    console.log('✅ تم تسجيل الدخول بنجاح');
    const token = loginResult.token;
    const waiterId = loginResult.user.id;
    
    // جلب الطلبات
    console.log('\n📋 جلب الطلبات...');
    const ordersResponse = await fetch('https://deshacoffee-production.up.railway.app/api/v1/orders', {
      headers: { 'Authorization': `Bearer ${token}` }
    });
    
    const ordersData = await ordersResponse.json();
    const allOrders = ordersData.data || ordersData;
    
    if (!Array.isArray(allOrders)) {
      console.log('❌ خطأ في جلب الطلبات');
      return;
    }
    
    // فلترة طلبات بوسي
    const bosyOrders = allOrders.filter(order => {
      return order.waiterId === waiterId || 
             order.waiterName === 'بوسي' ||
             order.waiterName === 'Bosy';
    });
    
    console.log(`📊 إجمالي الطلبات: ${allOrders.length}`);
    console.log(`👤 طلبات بوسي: ${bosyOrders.length}`);
    
    // تجميع الطلبات حسب الطاولة
    const ordersByTable = {};
    bosyOrders.forEach(order => {
      const tableNum = String(order.tableNumber);
      if (!ordersByTable[tableNum]) {
        ordersByTable[tableNum] = [];
      }
      ordersByTable[tableNum].push(order);
    });
    
    console.log('\n🏓 الطلبات مجمعة حسب الطاولة:');
    Object.entries(ordersByTable).forEach(([tableNum, orders]) => {
      const totalAmount = orders.reduce((sum, order) => sum + (order.totalPrice || 0), 0);
      console.log(`  طاولة ${tableNum}: ${orders.length} طلب، إجمالي ${totalAmount.toFixed(2)} جنيه`);
      
      // عرض تفاصيل كل طلب
      orders.forEach(order => {
        console.log(`    - طلب #${order.orderNumber || order._id?.slice(-6)}: ${order.status}, ${order.totalPrice} جنيه`);
      });
    });
    
    // محاولة جلب الطاولات
    console.log('\n🔗 محاولة جلب الطاولات...');
    const tablesResponse = await fetch(`https://deshacoffee-production.up.railway.app/api/v1/table-accounts?waiterId=${waiterId}`, {
      headers: { 'Authorization': `Bearer ${token}` }
    });
    
    const tablesData = await tablesResponse.json();
    let tables = tablesData.data || tablesData;
    
    if (!Array.isArray(tables)) {
      tables = [];
    }
    
    console.log(`📈 طاولات من API: ${tables.length}`);
    
    // إنشاء طاولات من الطلبات إذا لم توجد
    if (tables.length === 0 && Object.keys(ordersByTable).length > 0) {
      console.log('🔄 إنشاء طاولات من الطلبات...');
      
      tables = Object.entries(ordersByTable).map(([tableNum, orders]) => {
        const totalAmount = orders.reduce((sum, order) => sum + (order.totalPrice || 0), 0);
        return {
          _id: `generated-table-${tableNum}-${waiterId}`,
          tableNumber: parseInt(tableNum) || tableNum,
          waiterId: waiterId,
          waiterName: 'بوسي',
          status: 'active',
          isOpen: true,
          orders: orders,
          totalAmount: totalAmount,
          ordersCount: orders.length,
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString(),
          customerName: 'عميل'
        };
      });
      
      console.log(`✅ تم إنشاء ${tables.length} طاولة`);
    }
    
    // عرض النتيجة النهائية
    console.log('\n🎯 النتيجة النهائية:');
    tables.forEach(table => {
      console.log(`📋 طاولة ${table.tableNumber}:`);
      console.log(`   - عدد الطلبات: ${table.ordersCount || table.orders?.length || 0}`);
      console.log(`   - إجمالي المبلغ: ${(table.totalAmount || 0).toFixed(2)} جنيه`);
      console.log(`   - الحالة: ${table.status}`);
    });
    
    // تحقق من الطاولات المستهدفة
    const targetTables = ['1', '2', '29'];
    console.log('\n🎯 الطاولات المستهدفة:');
    targetTables.forEach(tableNum => {
      const table = tables.find(t => String(t.tableNumber) === tableNum);
      if (table) {
        console.log(`✅ طاولة ${tableNum}: ${table.ordersCount || 0} طلب، ${(table.totalAmount || 0).toFixed(2)} جنيه`);
      } else {
        console.log(`❌ طاولة ${tableNum}: غير موجودة`);
      }
    });
    
  } catch (error) {
    console.error('❌ خطأ في الاختبار:', error.message);
  }
}

testTableOrdersAfterFix();

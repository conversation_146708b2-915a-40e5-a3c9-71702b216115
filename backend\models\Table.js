const mongoose = require('mongoose');

const tableSchema = new mongoose.Schema({
  number: {
    type: Number,
    required: [true, 'رقم الطاولة مطلوب'],
    unique: true,
    min: [1, 'رقم الطاولة يجب أن يكون 1 على الأقل'],
    max: [100, 'رقم الطاولة لا يمكن أن يزيد عن 100']
  },
  section: {
    type: String,
    required: [true, 'قسم الطاولة مطلوب'],
    trim: true,
    enum: ['أ', 'ب', 'ج', 'د', 'VIP'],
    default: 'أ'
  },
  capacity: {
    type: Number,
    required: [true, 'سعة الطاولة مطلوبة'],
    min: [1, 'السعة يجب أن تكون 1 على الأقل'],
    max: [12, 'السعة لا يمكن أن تزيد عن 12'],
    default: 4
  },
  status: {
    type: String,
    enum: ['available', 'occupied', 'reserved', 'cleaning', 'maintenance'],
    default: 'available'
  },
  currentOrder: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Order',
    default: null
  },
  assignedWaiter: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    default: null
  },
  features: {
    hasWindow: { type: Boolean, default: false },
    hasTV: { type: Boolean, default: false },
    isVIP: { type: Boolean, default: false },
    hasOutlet: { type: Boolean, default: true },
    isSmokingAllowed: { type: Boolean, default: false }
  },
  location: {
    floor: {
      type: String,
      enum: ['الطابق الأرضي', 'الطابق الأول', 'السطح'],
      default: 'الطابق الأرضي'
    },
    position: {
      x: Number, // موقع على خريطة المقهى
      y: Number
    }
  },
  // إحصائيات
  stats: {
    totalOrders: { type: Number, default: 0 },
    totalRevenue: { type: Number, default: 0 },
    averageOrderValue: { type: Number, default: 0 },
    lastUsed: { type: Date, default: null }
  },
  // معلومات الصيانة
  maintenance: {
    lastCleaned: { type: Date, default: Date.now },
    lastMaintenance: { type: Date, default: Date.now },
    needsMaintenance: { type: Boolean, default: false },
    maintenanceNotes: { type: String, trim: true }
  },
  notes: {
    type: String,
    trim: true,
    maxlength: [500, 'الملاحظات لا يمكن أن تزيد عن 500 حرف']
  },
  isActive: {
    type: Boolean,
    default: true
  },
  createdBy: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User'
  },
  updatedBy: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User'
  }
}, {
  timestamps: true,
  toJSON: { virtuals: true },
  toObject: { virtuals: true }
});

// Indexes
tableSchema.index({ number: 1 });
tableSchema.index({ section: 1 });
tableSchema.index({ status: 1 });
tableSchema.index({ assignedWaiter: 1 });
tableSchema.index({ isActive: 1 });

// Virtual for table display name
tableSchema.virtual('displayName').get(function() {
  return `طاولة ${this.number} - قسم ${this.section}`;
});

// Virtual for availability status
tableSchema.virtual('isAvailable').get(function() {
  return this.status === 'available' && this.isActive;
});

// Virtual for current occupation time
tableSchema.virtual('occupationTime').get(function() {
  if (this.status === 'occupied' && this.stats.lastUsed) {
    return Math.round((Date.now() - this.stats.lastUsed.getTime()) / (1000 * 60)); // in minutes
  }
  return 0;
});

// Static method to get available tables
tableSchema.statics.getAvailable = function(section = null) {
  const query = { 
    status: 'available', 
    isActive: true 
  };
  
  if (section) {
    query.section = section;
  }
  
  return this.find(query)
    .populate('assignedWaiter', 'name username')
    .sort({ section: 1, number: 1 });
};

// Static method to get occupied tables
tableSchema.statics.getOccupied = function() {
  return this.find({ 
    status: 'occupied', 
    isActive: true 
  })
    .populate('assignedWaiter', 'name username')
    .populate('currentOrder')
    .sort({ section: 1, number: 1 });
};

// Static method to get tables by section
tableSchema.statics.getBySection = function(section) {
  return this.find({ 
    section: section, 
    isActive: true 
  })
    .populate('assignedWaiter', 'name username')
    .populate('currentOrder')
    .sort({ number: 1 });
};

// Instance method to occupy table
tableSchema.methods.occupy = function(orderId, waiterId) {
  this.status = 'occupied';
  this.currentOrder = orderId;
  this.assignedWaiter = waiterId;
  this.stats.lastUsed = new Date();
  return this.save();
};

// Instance method to release table
tableSchema.methods.release = function() {
  this.status = 'available';
  this.currentOrder = null;
  this.assignedWaiter = null; // تصفير النادل المرتبط بالطاولة
  this.stats.totalOrders += 1;
  return this.save();
};

// Instance method to reserve table
tableSchema.methods.reserve = function(waiterId) {
  this.status = 'reserved';
  this.assignedWaiter = waiterId;
  return this.save();
};

// Instance method to update stats
tableSchema.methods.updateStats = function(orderValue) {
  this.stats.totalRevenue += orderValue;
  this.stats.averageOrderValue = this.stats.totalRevenue / Math.max(this.stats.totalOrders, 1);
  return this.save();
};

// Pre-save middleware
tableSchema.pre('save', function(next) {
  // تحديث timestamp للتعديل
  if (this.isModified() && !this.isNew) {
    this.updatedAt = new Date();
  }
  next();
});

module.exports = mongoose.model('Table', tableSchema);

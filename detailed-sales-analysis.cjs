// سكريبت مفصل لحساب المبيعات
console.log('بدء تحليل بيانات المبيعات...');

const mongoose = require('mongoose');
const mongoUri = 'mongodb+srv://besomustafa:<EMAIL>/deshacoffee?retryWrites=true&w=majority&appName=MyCoffeChop';

async function analyzeDatabase() {
  try {
    console.log('الاتصال بقاعدة البيانات...');
    await mongoose.connect(mongoUri);
    console.log('تم الاتصال بنجاح');
    
    // تحليل بيانات الطلبات
    const ordersCollection = mongoose.connection.db.collection('orders');
    const usersCollection = mongoose.connection.db.collection('users');
    
    console.log('\n=== تحليل الطلبات ===');
    
    // جلب جميع الطلبات
    const allOrders = await ordersCollection.find({}).toArray();
    console.log('إجمالي الطلبات: ' + allOrders.length);
    
    // تحليل بنية البيانات
    if (allOrders.length > 0) {
      console.log('\nتحليل بنية البيانات:');
      const sampleOrder = allOrders[0];
      console.log('هيكل الطلب الأول:');
      Object.keys(sampleOrder).forEach(key => {
        console.log('  ' + key + ': ' + typeof sampleOrder[key] + ' = ' + sampleOrder[key]);
      });
      
      // تحليل الحالات
      const statusCounts = {};
      let totalValue = 0;
      let totalDiscount = 0;
      
      console.log('\nتحليل جميع الطلبات:');
      allOrders.forEach((order, index) => {
        const status = order.status || 'غير محدد';
        statusCounts[status] = (statusCounts[status] || 0) + 1;
        
        const orderTotal = parseFloat(order.total) || 0;
        const orderDiscount = parseFloat(order.discount) || 0;
        
        totalValue += orderTotal;
        totalDiscount += orderDiscount;
        
        if (index < 10) { // عرض أول 10 طلبات للتحليل
          console.log('الطلب ' + (index + 1) + ':');
          console.log('  الحالة: ' + status);
          console.log('  الإجمالي: ' + orderTotal + ' (نوع: ' + typeof order.total + ')');
          console.log('  الخصم: ' + orderDiscount + ' (نوع: ' + typeof order.discount + ')');
          console.log('  النادل ID: ' + order.assignedWaiter);
          console.log('  العناصر: ' + (order.items ? order.items.length : 'غير موجود'));
          console.log('  ---');
        }
      });
      
      console.log('\nملخص إحصائي:');
      console.log('إجمالي قيمة جميع الطلبات: ' + totalValue.toFixed(2));
      console.log('إجمالي الخصومات: ' + totalDiscount.toFixed(2));
      console.log('صافي القيم: ' + (totalValue - totalDiscount).toFixed(2));
      
      console.log('\nتوزيع الحالات:');
      Object.entries(statusCounts).forEach(([status, count]) => {
        console.log('  ' + status + ': ' + count + ' طلب');
      });
    }
    
    // تحليل بيانات المستخدمين
    console.log('\n=== تحليل المستخدمين ===');
    const allUsers = await usersCollection.find({}).toArray();
    console.log('إجمالي المستخدمين: ' + allUsers.length);
    
    const waiters = allUsers.filter(user => user.role === 'waiter');
    console.log('النُدل: ' + waiters.length);
    
    if (waiters.length > 0) {
      console.log('\nقائمة النُدل:');
      waiters.forEach((waiter, index) => {
        console.log((index + 1) + '. ' + waiter.username + ' (ID: ' + waiter._id + ')');
      });
      
      // حساب مبيعات كل نادل
      console.log('\n=== حساب مبيعات النُدل ===');
      
      for (const waiter of waiters) {
        const waiterOrders = allOrders.filter(order => 
          order.assignedWaiter && order.assignedWaiter.toString() === waiter._id.toString()
        );
        
        let waiterTotalSales = 0;
        let waiterTotalDiscount = 0;
        let completedOrders = 0;
        
        waiterOrders.forEach(order => {
          const orderTotal = parseFloat(order.total) || 0;
          const orderDiscount = parseFloat(order.discount) || 0;
          
          waiterTotalSales += orderTotal;
          waiterTotalDiscount += orderDiscount;
          
          if (order.status === 'completed') {
            completedOrders++;
          }
        });
        
        console.log('\nالنادل: ' + waiter.username);
        console.log('  إجمالي الطلبات: ' + waiterOrders.length);
        console.log('  الطلبات المكتملة: ' + completedOrders);
        console.log('  إجمالي المبيعات: ' + waiterTotalSales.toFixed(2) + ' ريال');
        console.log('  إجمالي الخصومات: ' + waiterTotalDiscount.toFixed(2) + ' ريال');
        console.log('  صافي المبيعات: ' + (waiterTotalSales - waiterTotalDiscount).toFixed(2) + ' ريال');
        
        if (completedOrders > 0) {
          console.log('  متوسط قيمة الطلب المكتمل: ' + ((waiterTotalSales - waiterTotalDiscount) / completedOrders).toFixed(2) + ' ريال');
        }
      }
    }
    
    // طلبات بدون نادل
    const ordersWithoutWaiter = allOrders.filter(order => !order.assignedWaiter);
    if (ordersWithoutWaiter.length > 0) {
      console.log('\n=== طلبات بدون نادل محدد ===');
      console.log('عدد الطلبات: ' + ordersWithoutWaiter.length);
      
      let totalWithoutWaiter = 0;
      let discountWithoutWaiter = 0;
      
      ordersWithoutWaiter.forEach(order => {
        totalWithoutWaiter += (parseFloat(order.total) || 0);
        discountWithoutWaiter += (parseFloat(order.discount) || 0);
      });
      
      console.log('إجمالي المبيعات: ' + totalWithoutWaiter.toFixed(2) + ' ريال');
      console.log('إجمالي الخصومات: ' + discountWithoutWaiter.toFixed(2) + ' ريال');
      console.log('صافي المبيعات: ' + (totalWithoutWaiter - discountWithoutWaiter).toFixed(2) + ' ريال');
    }
    
    await mongoose.disconnect();
    console.log('\nتم إنهاء التحليل');
    
  } catch (error) {
    console.error('خطأ: ' + error.message);
  }
}

analyzeDatabase();

const mongoose = require('mongoose');

const inventorySchema = new mongoose.Schema({
  name: {
    type: String,
    required: [true, 'اسم الصنف مطلوب'],
    trim: true,
    unique: true
  },
  quantity: {
    type: Number,
    required: [true, 'الكمية مطلوبة'],
    min: [0, 'الكمية لا يمكن أن تكون سالبة'],
    default: 0
  },
  min: {
    type: Number,
    required: [true, 'الحد الأدنى مطلوب'],
    min: [1, 'الحد الأدنى يجب أن يكون 1 على الأقل'],
    default: 1
  },
  unit: {
    type: String,
    required: true,
    enum: ['قطعة', 'كيلو', 'لتر', 'حبة', 'علبة', 'كيس'],
    default: 'قطعة'
  },
  price: {
    type: Number,
    min: [0, 'السعر لا يمكن أن يكون سالب'],
    default: 0
  },
  category: {
    type: String,
    trim: true,
    default: 'عام'
  },
  supplier: {
    name: String,
    contact: String
  },
  lastPurchase: {
    date: Date,
    quantity: Number,
    cost: Number
  },
  notes: {
    type: String,
    trim: true
  },
  isActive: {
    type: Boolean,
    default: true
  },
  createdBy: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User'
  },
  updatedBy: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User'
  }
}, {
  timestamps: true,
  toJSON: { virtuals: true },
  toObject: { virtuals: true }
});

// Virtual for last updated (alias for updatedAt)
inventorySchema.virtual('last_updated').get(function() {
  return this.updatedAt;
});

// Virtual for low stock status
inventorySchema.virtual('isLowStock').get(function() {
  return this.quantity <= this.min;
});

// Virtual for out of stock status
inventorySchema.virtual('isOutOfStock').get(function() {
  return this.quantity === 0;
});

// Virtual for stock status
inventorySchema.virtual('stockStatus').get(function() {
  if (this.quantity === 0) {
    return 'out_of_stock';
  } else if (this.quantity <= this.min) {
    return 'low_stock';
  } else if (this.quantity <= this.min * 1.5) {
    return 'warning';
  } else {
    return 'in_stock';
  }
});

// Indexes for better performance
inventorySchema.index({ name: 1 });
inventorySchema.index({ quantity: 1 });
inventorySchema.index({ isActive: 1 });
inventorySchema.index({ category: 1 });

// Static method to get low stock items
inventorySchema.statics.getLowStock = function() {
  return this.find({ 
    $expr: { $lte: ['$quantity', '$min'] },
    isActive: true 
  }).sort({ quantity: 1 });
};

// Static method to get out of stock items
inventorySchema.statics.getOutOfStock = function() {
  return this.find({ 
    quantity: 0,
    isActive: true 
  });
};

// Static method to search inventory
inventorySchema.statics.search = function(query) {
  return this.find({
    $and: [
      { isActive: true },
      {
        $or: [
          { name: { $regex: query, $options: 'i' } },
          { category: { $regex: query, $options: 'i' } },
          { notes: { $regex: query, $options: 'i' } }
        ]
      }
    ]
  });
};

// Instance method to update stock
inventorySchema.methods.updateStock = function(quantity, operation = 'set', userId = null) {
  if (operation === 'add') {
    this.quantity += quantity;
  } else if (operation === 'subtract') {
    this.quantity = Math.max(0, this.quantity - quantity);
  } else {
    this.quantity = quantity;
  }

  if (userId) {
    this.updatedBy = userId;
  }

  return this.save();
};

// Pre-save middleware to update timestamps
inventorySchema.pre('save', function(next) {
  if (this.isModified() && !this.isNew) {
    this.updatedAt = new Date();
  }
  next();
});

module.exports = mongoose.model('Inventory', inventorySchema);

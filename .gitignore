# Dependencies
node_modules/
backend/node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*
pnpm-debug.log*
lerna-debug.log*

# Production build
build/
dist/

# Environment variables
.env
.env.local
.env.development.local
.env.test.local
.env.production.local
backend/.env

# IDE files
.vscode/
.idea/
*.swp
*.swo

# OS files
.DS_Store
Thumbs.db

# Logs
logs
*.log

# Coverage reports
coverage/

# Temporary files
*.tmp
*.temp

# Database
*.db
*.sqlite

# Certificates and keys
*.pem
*.key
*.crt

# Package manager files
yarn.lock
pnpm-lock.yaml

# NPM configuration (keep .npmrc for dependency resolution)
# .npmrc

.vercel

// backend/scripts/seedTables.js
require('dotenv').config({ path: require('path').resolve(__dirname, '../../.env') }); // Adjusted path for .env
const mongoose = require('mongoose');
const Table = require('../models/Table');

// MongoDB Connection URI - Use MONGODB_URI
const mongoURI = process.env.MONGODB_URI;

const seedTables = async () => {
  console.log('Starting table seeding script...');
  if (!mongoURI) {
    console.error('ERROR: MONGODB_URI is not defined. Please ensure it is set in the .env file in the project root (c:\\Users\\<USER>\\OneDrive\\Desktop\\PRINT\\Coffee\\Coffee\\.env).');
    process.exit(1);
  }
  console.log('MONGODB_URI found. Attempting to connect...');

  try {
    await mongoose.connect(mongoURI);
    console.log('MongoDB Connected successfully for seeding.');

    // Optional: Clear existing tables (use with caution in development)
    // console.log('Attempting to delete existing tables...');
    // try {
    //   await Table.deleteMany({});
    //   console.log('Existing tables deleted successfully.');
    // } catch (deleteError) {
    //   console.error('Error deleting existing tables:', deleteError.message);
    //   // Decide if you want to proceed or exit if deletion fails
    // }

    const tablesToCreate = [];
    for (let i = 1; i <= 30; i++) {
      tablesToCreate.push({
        number: i,
        section: 'أ', // Default section
        capacity: 4,   // Default capacity
        status: 'available', // Default status
        isActive: true // Default isActive
      });
    }
    console.log(`Prepared ${tablesToCreate.length} table objects for creation.`);

    console.log('Attempting to insert tables...');
    let insertedCount = 0;

    try {
      const result = await Table.insertMany(tablesToCreate, { ordered: false });
      insertedCount = result.length;
      console.log(`SUCCESS: ${insertedCount} tables were newly created.`);
      
      if (tablesToCreate.length !== insertedCount) {
        console.warn(`INFO: ${tablesToCreate.length - insertedCount} tables were not newly inserted. This is likely because they already existed (due to ordered:false).`);
      }

    } catch (error) {
      console.error('ERROR during Table.insertMany operation:', error.name, error.message);
      if (error.name === 'MongoBulkWriteError' && error.result) {
        insertedCount = error.result.nInserted || (error.result.ok ? error.result.insertedIds?.length : 0) || 0;
        const writeErrors = error.result.getWriteErrors ? error.result.getWriteErrors() : (error.writeErrors || []);
        const numFailed = writeErrors.length;

        console.log(`PARTIAL SUCCESS/INFO: ${insertedCount} tables may have been created before the error or were successfully part of the bulk operation.`);
        if (numFailed > 0) {
            console.error(`DETAILS: ${numFailed} specific table insertions failed within the bulk operation:`);
            writeErrors.forEach(err => {
                const failedNumber = err.op?.number || err.err?.op?.number || 'unknown table number';
                if (err.code === 11000) { // Duplicate key error
                    console.error(`  - Table number ${failedNumber}: Failed - It already exists (Code: ${err.code}).`);
                } else {
                    console.error(`  - Table number ${failedNumber}: Failed - ${err.errmsg} (Code: ${err.code}).`);
                }
            });
        } else if (tablesToCreate.length !== insertedCount) {
             console.warn(`INFO: ${tablesToCreate.length - insertedCount - numFailed} tables were not inserted for other reasons (e.g., already existed and skipped silently by ordered:false, but not reported as specific write errors).`);
        }
      } else {
        console.error('ERROR: A general error occurred during table insertion that was not a MongoBulkWriteError. Details:', error);
      }
      console.log('It is possible some tables were inserted before this error occurred, or none if the error was critical early on.');
    }

  } catch (dbConnectionError) {
    console.error('FATAL ERROR: Could not connect to MongoDB. Please check MONGODB_URI and network connectivity. Details:', dbConnectionError.message);
    console.error('Stack trace for connection error:', dbConnectionError.stack);
  } finally {
    console.log('Attempting to disconnect from MongoDB...');
    if (mongoose.connection && (mongoose.connection.readyState === 1 || mongoose.connection.readyState === 2)) {
        try {
            await mongoose.disconnect();
            console.log('MongoDB Disconnected successfully.');
        } catch (disconnectError) {
            console.error('Error during MongoDB disconnection:', disconnectError.message);
        }
    } else {
        console.log('MongoDB connection was not active or already closed. No disconnection needed.');
    }
    console.log('Table seeding script finished.');
  }
};

seedTables();

const mongoose = require('mongoose');
const config = require('../config/environment');
const Table = require('../models/Table');

const MONGODB_URI = config.database.mongoUri;

// بيانات الطاولات الأساسية
const defaultTables = [
  // قسم أ - منطقة رئيسية
  { number: 1, section: 'أ', capacity: 4, features: { hasWindow: true, hasOutlet: true } },
  { number: 2, section: 'أ', capacity: 4, features: { hasWindow: true, hasOutlet: true } },
  { number: 3, section: 'أ', capacity: 6, features: { hasWindow: false, hasOutlet: true } },
  { number: 4, section: 'أ', capacity: 2, features: { hasWindow: false, hasOutlet: true } },
  { number: 5, section: 'أ', capacity: 4, features: { hasWindow: true, hasOutlet: true } },
  
  // قسم ب - منطقة هادئة
  { number: 6, section: 'ب', capacity: 4, features: { hasWindow: false, hasOutlet: true } },
  { number: 7, section: 'ب', capacity: 2, features: { hasWindow: false, hasOutlet: true } },
  { number: 8, section: 'ب', capacity: 6, features: { hasWindow: true, hasOutlet: true } },
  { number: 9, section: 'ب', capacity: 4, features: { hasWindow: false, hasOutlet: true } },
  { number: 10, section: 'ب', capacity: 4, features: { hasWindow: true, hasOutlet: true } },
  
  // قسم ج - منطقة عائلية
  { number: 11, section: 'ج', capacity: 6, features: { hasWindow: true, hasOutlet: true } },
  { number: 12, section: 'ج', capacity: 8, features: { hasWindow: true, hasOutlet: true, hasTV: true } },
  { number: 13, section: 'ج', capacity: 4, features: { hasWindow: false, hasOutlet: true } },
  { number: 14, section: 'ج', capacity: 6, features: { hasWindow: true, hasOutlet: true } },
  { number: 15, section: 'ج', capacity: 4, features: { hasWindow: false, hasOutlet: true } },
  
  // قسم VIP - منطقة مميزة
  { number: 16, section: 'VIP', capacity: 4, features: { hasWindow: true, hasOutlet: true, hasTV: true, isVIP: true } },
  { number: 17, section: 'VIP', capacity: 6, features: { hasWindow: true, hasOutlet: true, hasTV: true, isVIP: true } },
  { number: 18, section: 'VIP', capacity: 2, features: { hasWindow: true, hasOutlet: true, isVIP: true } }
];

async function setupTables() {
  try {
    console.log('🪑 بدء إعداد جدول الطاولات...');
    
    // الاتصال بقاعدة البيانات
    await mongoose.connect(MONGODB_URI, {
      useNewUrlParser: true,
      useUnifiedTopology: true,
    });
    console.log('✅ تم الاتصال بقاعدة البيانات');

    // التحقق من وجود طاولات
    const existingTablesCount = await Table.countDocuments();
    console.log(`📊 الطاولات الموجودة حالياً: ${existingTablesCount}`);

    if (existingTablesCount === 0) {
      console.log('\n🏗️ إضافة الطاولات الأساسية...');
      
      // إضافة الطاولات
      for (const tableData of defaultTables) {
        try {
          const table = new Table(tableData);
          await table.save();
          console.log(`✅ تم إضافة ${table.displayName}`);
        } catch (error) {
          if (error.code === 11000) {
            console.log(`⚠️ الطاولة رقم ${tableData.number} موجودة مسبقاً`);
          } else {
            console.error(`❌ خطأ في إضافة الطاولة رقم ${tableData.number}:`, error.message);
          }
        }
      }
    } else {
      console.log('ℹ️ الطاولات موجودة مسبقاً، سيتم تحديث البيانات إذا لزم الأمر');
      
      // إضافة الطاولات المفقودة فقط
      for (const tableData of defaultTables) {
        const existingTable = await Table.findOne({ number: tableData.number });
        if (!existingTable) {
          try {
            const table = new Table(tableData);
            await table.save();
            console.log(`✅ تم إضافة الطاولة المفقودة: ${table.displayName}`);
          } catch (error) {
            console.error(`❌ خطأ في إضافة الطاولة رقم ${tableData.number}:`, error.message);
          }
        }
      }
    }

    // إحصائيات نهائية
    console.log('\n📊 إحصائيات الطاولات النهائية:');
    const totalTables = await Table.countDocuments();
    const availableTables = await Table.countDocuments({ status: 'available' });
    const occupiedTables = await Table.countDocuments({ status: 'occupied' });
    
    console.log(`📋 إجمالي الطاولات: ${totalTables}`);
    console.log(`🟢 متاحة: ${availableTables}`);
    console.log(`🔴 مشغولة: ${occupiedTables}`);
    
    // إحصائيات حسب القسم
    console.log('\n📍 توزيع الطاولات حسب القسم:');
    const sections = ['أ', 'ب', 'ج', 'VIP'];
    for (const section of sections) {
      const count = await Table.countDocuments({ section, isActive: true });
      console.log(`   قسم ${section}: ${count} طاولة`);
    }

    console.log('\n🎉 تم إعداد جدول الطاولات بنجاح!');

  } catch (error) {
    console.error('❌ خطأ في إعداد الطاولات:', error);
  } finally {
    await mongoose.connection.close();
    console.log('\n🔌 تم إغلاق الاتصال بقاعدة البيانات');
    process.exit(0);
  }
}

setupTables();

const mongoose = require('mongoose');
require('dotenv').config();

// Connect to MongoDB
const connectDB = async () => {
  try {
    await mongoose.connect(process.env.MONGODB_URI || 'mongodb+srv://besomustafa:<EMAIL>/deshacoffee?retryWrites=true&w=majority');
    console.log('✅ Connected to MongoDB');
  } catch (error) {
    console.error('❌ MongoDB connection error:', error);
    process.exit(1);
  }
};

// Order Schema (simplified for this script)
const orderSchema = new mongoose.Schema({
  orderNumber: { type: String, required: true, unique: true },
  waiterName: { type: String, required: true },
  items: [{
    name: String,
    price: Number,
    quantity: Number,
    total: Number
  }],
  totalAmount: { type: Number, required: true },
  status: { 
    type: String, 
    enum: ['pending', 'preparing', 'ready', 'completed', 'cancelled'],
    default: 'pending'
  },
  tableNumber: { type: Number },
  notes: String,
  createdAt: { type: Date, default: Date.now },
  updatedAt: { type: Date, default: Date.now }
});

const Order = mongoose.model('Order', orderSchema);

// Category Schema
const categorySchema = new mongoose.Schema({
  name: { type: String, required: true },
  color: { type: String, default: '#3498db' },
  icon: String
});

const Category = mongoose.model('Category', categorySchema);

// Product Schema
const productSchema = new mongoose.Schema({
  name: { type: String, required: true },
  price: { type: Number, required: true },
  categories: [{ type: mongoose.Schema.Types.ObjectId, ref: 'Category' }],
  available: { type: Boolean, default: true }
});

const Product = mongoose.model('Product', productSchema);

// Sample drinks and foods data
const beverages = [
  { name: 'قهوة تركي', basePrice: 25 },
  { name: 'قهوة أمريكي', basePrice: 30 },
  { name: 'كابتشينو', basePrice: 35 },
  { name: 'لاتيه', basePrice: 38 },
  { name: 'إسبريسو', basePrice: 20 },
  { name: 'موكا', basePrice: 40 },
  { name: 'شاي أحمر', basePrice: 15 },
  { name: 'شاي أخضر', basePrice: 18 },
  { name: 'شاي بالنعناع', basePrice: 20 },
  { name: 'عصير برتقال', basePrice: 25 },
  { name: 'عصير مانجو', basePrice: 28 },
  { name: 'عصير فراولة', basePrice: 30 },
  { name: 'ماء', basePrice: 5 },
  { name: 'مياه غازية', basePrice: 12 },
  { name: 'كولا', basePrice: 15 },
  { name: 'فانتا', basePrice: 15 },
  { name: 'سبرايت', basePrice: 15 },
  { name: 'ريد بول', basePrice: 45 },
  { name: 'شوكولاتة ساخنة', basePrice: 32 },
  { name: 'آيس كريم فانيليا', basePrice: 35 }
];

const foods = [
  { name: 'كرواسون جبن', basePrice: 45 },
  { name: 'كرواسون شوكولاتة', basePrice: 40 },
  { name: 'ساندويش تونة', basePrice: 55 },
  { name: 'ساندويش جبن', basePrice: 50 },
  { name: 'فطيرة سبانخ', basePrice: 35 },
  { name: 'فطيرة زعتر', basePrice: 30 },
  { name: 'كيك شوكولاتة', basePrice: 60 },
  { name: 'تشيز كيك', basePrice: 65 },
  { name: 'دونات', basePrice: 25 },
  { name: 'ماफن', basePrice: 30 },
  { name: 'بسكويت', basePrice: 20 },
  { name: 'توست جبن', basePrice: 40 },
  { name: 'بيتزا صغيرة', basePrice: 85 },
  { name: 'سلطة يونانية', basePrice: 70 },
  { name: 'سلطة خضراء', basePrice: 45 }
];

const allItems = [...beverages, ...foods];
const orderStatuses = ['pending', 'preparing', 'ready', 'completed', 'cancelled'];
const waiters = [
  { name: 'عزة', orders: 57 },
  { name: 'بوسي', orders: 85 },
  { name: 'سارة', orders: 313 }
];

// Function to generate random order
const generateRandomOrder = (waiterName, orderIndex) => {
  // Generate unique order number with nanoseconds and random suffix
  const now = Date.now();
  const randomSuffix = Math.floor(Math.random() * 100000).toString().padStart(5, '0');
  const orderNumber = `ORD-${now}-${randomSuffix}-${orderIndex}`;
  
  const tableNumber = Math.floor(Math.random() * 20) + 1;
  const status = orderStatuses[Math.floor(Math.random() * orderStatuses.length)];
  
  // Generate 1-5 items per order
  const itemsCount = Math.floor(Math.random() * 5) + 1;
  const orderItems = [];
  let totalAmount = 0;
  
  for (let i = 0; i < itemsCount; i++) {
    const item = allItems[Math.floor(Math.random() * allItems.length)];
    const quantity = Math.floor(Math.random() * 3) + 1;
    // Add price variation ±20%
    const priceVariation = (Math.random() - 0.5) * 0.4; // -20% to +20%
    const price = Math.round(item.basePrice * (1 + priceVariation));
    const itemTotal = price * quantity;
    
    orderItems.push({
      name: item.name,
      price: price,
      quantity: quantity,
      total: itemTotal
    });
    
    totalAmount += itemTotal;
  }
  
  // Generate random creation date within last 30 days
  const currentDate = new Date();
  const thirtyDaysAgo = new Date(currentDate.getTime() - (30 * 24 * 60 * 60 * 1000));
  const randomDate = new Date(thirtyDaysAgo.getTime() + Math.random() * (currentDate.getTime() - thirtyDaysAgo.getTime()));
  
  return {
    orderNumber,
    waiterName,
    items: orderItems,
    totalAmount: Math.round(totalAmount),
    status,
    tableNumber,
    notes: Math.random() > 0.7 ? `ملاحظات الطلب ${orderIndex + 1}` : '',
    createdAt: randomDate,
    updatedAt: randomDate
  };
};

// Function to create sample categories and products
const createSampleData = async () => {
  try {
    // Create categories
    const drinkCategory = await Category.findOneAndUpdate(
      { name: 'مشروبات' },
      { name: 'مشروبات', color: '#3498db', icon: 'coffee' },
      { upsert: true, new: true }
    );
    
    const foodCategory = await Category.findOneAndUpdate(
      { name: 'طعام' },
      { name: 'طعام', color: '#e67e22', icon: 'utensils' },
      { upsert: true, new: true }
    );
    
    console.log('✅ Categories created/updated');
    
    // Create products
    for (const beverage of beverages) {
      await Product.findOneAndUpdate(
        { name: beverage.name },
        {
          name: beverage.name,
          price: beverage.basePrice,
          categories: [drinkCategory._id],
          available: true
        },
        { upsert: true }
      );
    }
    
    for (const food of foods) {
      await Product.findOneAndUpdate(
        { name: food.name },
        {
          name: food.name,
          price: food.basePrice,
          categories: [foodCategory._id],
          available: true
        },
        { upsert: true }
      );
    }
    
    console.log('✅ Products created/updated');
  } catch (error) {
    console.error('❌ Error creating sample data:', error);
  }
};

// Main function to generate orders
const generateOrders = async () => {
  try {
    await connectDB();
    await createSampleData();
    
    console.log('🚀 Starting order generation...');
    
    for (const waiter of waiters) {
      console.log(`\n📝 Generating ${waiter.orders} orders for ${waiter.name}...`);
      
      const orders = [];
      for (let i = 0; i < waiter.orders; i++) {
        // Add small delay to ensure unique timestamps
        await new Promise(resolve => setTimeout(resolve, 1));
        
        const order = generateRandomOrder(waiter.name, i);
        orders.push(order);
        
        if ((i + 1) % 10 === 0) {
          process.stdout.write(`   Progress: ${i + 1}/${waiter.orders} orders\r`);
        }
      }
      
      // Insert orders in batches for better performance
      const batchSize = 50;
      for (let i = 0; i < orders.length; i += batchSize) {
        const batch = orders.slice(i, i + batchSize);
        await Order.insertMany(batch);
      }
      
      console.log(`\n✅ Created ${waiter.orders} orders for ${waiter.name}`);
    }
    
    // Display summary
    console.log('\n📊 Order Generation Summary:');
    console.log('================================');
    
    for (const waiter of waiters) {
      const waiterOrders = await Order.find({ waiterName: waiter.name });
      const totalAmount = waiterOrders.reduce((sum, order) => sum + order.totalAmount, 0);
      
      console.log(`${waiter.name}:`);
      console.log(`   - الطلبات: ${waiterOrders.length}`);
      console.log(`   - إجمالي المبيعات: ${totalAmount.toFixed(2)} جنيه`);
      console.log(`   - متوسط الطلب: ${(totalAmount / waiterOrders.length).toFixed(2)} جنيه`);
      
      // Status breakdown
      const statusCounts = {};
      orderStatuses.forEach(status => {
        statusCounts[status] = waiterOrders.filter(order => order.status === status).length;
      });
      
      console.log(`   - حالات الطلبات:`);
      Object.entries(statusCounts).forEach(([status, count]) => {
        if (count > 0) {
          console.log(`     * ${status}: ${count}`);
        }
      });
      console.log('');
    }
    
    // Overall summary
    const totalOrders = await Order.countDocuments();
    const totalSales = await Order.aggregate([
      { $group: { _id: null, total: { $sum: '$totalAmount' } } }
    ]);
    
    console.log('📈 إجمالي النظام:');
    console.log(`   - إجمالي الطلبات: ${totalOrders}`);
    console.log(`   - إجمالي المبيعات: ${totalSales[0]?.total?.toFixed(2) || 0} جنيه`);
    
    console.log('\n🎉 تم إنشاء جميع الطلبات بنجاح!');
    
  } catch (error) {
    console.error('❌ Error generating orders:', error);
  } finally {
    await mongoose.connection.close();
    console.log('👋 Database connection closed');
  }
};

// Run the script
generateOrders();

/* ====================================
   CSS منفصل لشاشة الفئات
   Categories Screen - Isolated CSS
   ==================================== */

/* استيراد المتغيرات المميزة لشاشة الفئات */
@import '../variables/categories-variables.css';

/* تخطيط الفئات */
.categories-screen {
  padding: var(--categories-spacing-lg);
  max-width: 1400px;
  margin: 0 auto;
  overflow-x: hidden;
}

/* رأس الشاشة */
.categories-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: var(--categories-spacing-lg);
  flex-wrap: wrap;
  gap: var(--categories-spacing-md);
}

.categories-title {
  font-size: var(--categories-font-size-xl);
  font-weight: 700;
  color: var(--categories-text-primary);
  margin: 0;
}

/* أدوات التحكم في الفئات */
.categories-controls {
  display: flex;
  gap: var(--categories-spacing-md);
  align-items: center;
  flex-wrap: wrap;
}

.categories-search {
  padding: var(--categories-spacing-sm) var(--categories-spacing-md);
  border: 1px solid var(--categories-border-color);
  border-radius: var(--categories-border-radius);
  font-size: var(--categories-font-size-sm);
  background: var(--categories-bg-primary);
  color: var(--categories-text-primary);
  min-width: 200px;
}

.categories-filter {
  padding: var(--categories-spacing-sm) var(--categories-spacing-md);
  border: 1px solid var(--categories-border-color);
  border-radius: var(--categories-border-radius);
  font-size: var(--categories-font-size-sm);
  background: var(--categories-bg-primary);
  color: var(--categories-text-primary);
  min-width: 150px;
}

.add-category-btn {
  background: var(--categories-primary);
  color: var(--categories-text-light);
  border: none;
  padding: var(--categories-spacing-sm) var(--categories-spacing-lg);
  border-radius: var(--categories-border-radius);
  font-size: var(--categories-font-size-sm);
  font-weight: 600;
  cursor: pointer;
  transition: var(--categories-transition-base);
  display: flex;
  align-items: center;
  gap: var(--categories-spacing-xs);
}

.add-category-btn:hover {
  background: var(--categories-primary-dark);
  transform: translateY(-2px);
  box-shadow: var(--categories-shadow-md);
}

/* شبكة الفئات */
.categories-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: var(--categories-spacing-lg);
  margin-top: var(--categories-spacing-lg);
}

/* بطاقة الفئة */
.category-card {
  background: var(--categories-bg-primary);
  border-radius: var(--categories-border-radius-lg);
  border: 1px solid var(--categories-border-color);
  overflow: visible;
  word-break: break-word;
  transition: var(--categories-transition-base);
  position: relative;
}

.category-card:hover {
  transform: translateY(-4px);
  box-shadow: var(--categories-shadow-lg);
}

.category-card-header {
  padding: var(--categories-spacing-lg);
  background: linear-gradient(135deg, var(--categories-primary), var(--categories-secondary));
  color: var(--categories-text-light);
  position: relative;
}

.category-card-header::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><circle cx="20" cy="20" r="1" fill="white" opacity="0.1"/><circle cx="80" cy="80" r="1.5" fill="white" opacity="0.1"/><circle cx="60" cy="30" r="0.8" fill="white" opacity="0.1"/></svg>');
  pointer-events: none;
}

.category-name {
  font-size: var(--categories-font-size-lg);
  font-weight: 700;
  margin: 0 0 var(--categories-spacing-xs) 0;
  color: var(--categories-text-light);
}

.category-description {
  font-size: var(--categories-font-size-sm);
  opacity: 0.9;
  margin: 0;
  color: var(--categories-text-light);
}

.category-card-body {
  padding: var(--categories-spacing-lg);
}

.category-stats {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: var(--categories-spacing-md);
}

.category-stat {
  text-align: center;
}

.category-stat-value {
  font-size: var(--categories-font-size-lg);
  font-weight: 700;
  color: var(--categories-primary);
  margin: 0;
}

.category-stat-label {
  font-size: var(--categories-font-size-xs);
  color: var(--categories-text-secondary);
  margin: 0;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.category-actions {
  display: flex;
  gap: var(--categories-spacing-sm);
  margin-top: var(--categories-spacing-md);
}

.category-action-btn {
  flex: 1;
  padding: var(--categories-spacing-sm);
  border: none;
  border-radius: var(--categories-border-radius);
  font-size: var(--categories-font-size-sm);
  font-weight: 600;
  cursor: pointer;
  transition: var(--categories-transition-base);
  display: flex;
  align-items: center;
  justify-content: center;
  gap: var(--categories-spacing-xs);
}

.category-edit-btn {
  background: var(--categories-warning);
  color: var(--categories-text-light);
}

.category-edit-btn:hover {
  background: var(--categories-warning-dark);
}

.category-delete-btn {
  background: var(--categories-danger);
  color: var(--categories-text-light);
}

.category-delete-btn:hover {
  background: var(--categories-danger-dark);
}

.category-view-btn {
  background: var(--categories-info);
  color: var(--categories-text-light);
}

.category-view-btn:hover {
  background: var(--categories-info-dark);
}

/* حالة فارغة */
.categories-empty {
  text-align: center;
  padding: var(--categories-spacing-xl) var(--categories-spacing-lg);
  color: var(--categories-text-secondary);
}

.categories-empty-icon {
  font-size: 4rem;
  color: var(--categories-text-muted);
  margin-bottom: var(--categories-spacing-md);
}

.categories-empty-title {
  font-size: var(--categories-font-size-lg);
  font-weight: 600;
  color: var(--categories-text-secondary);
  margin: 0 0 var(--categories-spacing-sm) 0;
}

.categories-empty-description {
  font-size: var(--categories-font-size-sm);
  color: var(--categories-text-muted);
  margin: 0 0 var(--categories-spacing-lg) 0;
}

/* تحميل */
.categories-loading {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: var(--categories-spacing-xl);
}

.categories-spinner {
  width: 40px;
  height: 40px;
  border: 3px solid var(--categories-border-color);
  border-top: 3px solid var(--categories-primary);
  border-radius: 50%;
  animation: categoriesSpinnerRotate 1s linear infinite;
}

@keyframes categoriesSpinnerRotate {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* استجابة للأجهزة المحمولة */
@media (max-width: 768px) {
  .categories-screen {
    padding: var(--categories-spacing-md);
  }

  .categories-header {
    flex-direction: column;
    align-items: stretch;
    gap: var(--categories-spacing-md);
  }

  .categories-controls {
    flex-direction: column;
    align-items: stretch;
  }

  .categories-search,
  .categories-filter {
    min-width: auto;
  }

  .categories-grid {
    grid-template-columns: 1fr;
    gap: var(--categories-spacing-md);
  }

  .category-actions {
    flex-direction: column;
  }
}

@media (max-width: 480px) {
  .categories-title {
    font-size: var(--categories-font-size-lg);
  }

  .category-card-header {
    padding: var(--categories-spacing-md);
  }

  .category-card-body {
    padding: var(--categories-spacing-md);
  }

  .category-stats {
    flex-direction: column;
    gap: var(--categories-spacing-sm);
  }
}

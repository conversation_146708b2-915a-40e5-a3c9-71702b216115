/* SettingsManagerScreen.css - Scoped styles for Settings Manager Screen */

/* ??????? ????????? ??????? */
@import '../variables/settings-variables.css';

/* Main container */
.settingsManagerScreen {
  flex: 1;
  padding: var(--settings-spacing);
  background-color: var(--settings-background);
  overflow-y: auto;
  font-family: var(--settings-fontFamily);
}

/* Header section */
.settingsManagerScreen__header {
  margin-bottom: 2rem;
  padding-bottom: 1.5rem;
  border-bottom: 2px solid var(--settings-borderColor);
}

.settingsManagerScreen__header-title {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  color: var(--settings-primaryColor);
  font-size: 2rem;
  font-weight: 600;
  margin: 0 0 0.5rem 0;
}

.settingsManagerScreen__header-icon {
  color: var(--settings-secondaryColor);
  font-size: 1.8rem;
}

.settingsManagerScreen__header-subtitle {
  color: var(--settings-textColorLight);
  font-size: 1.1rem;
  margin: 0;
  line-height: 1.5;
}

/* Settings sections */
.settingsManagerScreen__section {
  margin-bottom: 3rem;
  background: var(--settings-cardBackground);
  border-radius: var(--settings-borderRadius);
  border: 1px solid var(--settings-borderColor);
  overflow: hidden;
  box-shadow: 0 2px 4px var(--settings-shadowColor);
}

.settingsManagerScreen__section-header {
  padding: 1.5rem 2rem;
  background-color: var(--settings-background);
  border-bottom: 1px solid var(--settings-borderColor);
}

.settingsManagerScreen__section-title {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  font-size: 1.3rem;
  font-weight: 600;
  color: var(--settings-primaryColor);
  margin: 0 0 0.5rem 0;
}

.settingsManagerScreen__section-icon {
  color: var(--settings-accentColor);
  font-size: 1.2rem;
}

.settingsManagerScreen__section-description {
  color: var(--settings-textColorLight);
  font-size: 0.95rem;
  margin: 0;
  line-height: 1.4;
}

/* Settings content */
.settingsManagerScreen__section-content {
  padding: 2rem;
}

/* Setting item */
.settingsManagerScreen__setting-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1.5rem 0;
  border-bottom: 1px solid #f1f3f4;
  gap: 1rem;
}

.settingsManagerScreen__setting-item:last-child {
  border-bottom: none;
}

.settingsManagerScreen__setting-info {
  flex: 1;
}

.settingsManagerScreen__setting-label {
  font-size: 1rem;
  font-weight: 500;
  color: var(--settings-textColor);
  margin: 0 0 0.25rem 0;
}

.settingsManagerScreen__setting-description {
  font-size: 0.9rem;
  color: var(--settings-textColorLight);
  margin: 0;
  line-height: 1.4;
}

.settingsManagerScreen__setting-control {
  flex-shrink: 0;
}

/* Form controls */
.settingsManagerScreen__input,
.settingsManagerScreen__select,
.settingsManagerScreen__textarea {
  padding: 0.75rem 1rem;
  border: 2px solid var(--settings-borderColor);
  border-radius: 8px;
  font-size: 1rem;
  font-family: inherit;
  transition: border-color 0.2s ease, box-shadow 0.2s ease;
  min-width: 200px;
}

.settingsManagerScreen__input:focus,
.settingsManagerScreen__select:focus,
.settingsManagerScreen__textarea:focus {
  outline: none;
  border-color: var(--settings-accentColor);
  box-shadow: 0 0 0 3px rgba(0, 123, 255, 0.1);
}

.settingsManagerScreen__textarea {
  min-height: 100px;
  resize: vertical;
  font-family: inherit;
}

/* Toggle switch */
.settingsManagerScreen__toggle {
  position: relative;
  display: inline-block;
  width: 60px;
  height: 34px;
}

.settingsManagerScreen__toggle-input {
  opacity: 0;
  width: 0;
  height: 0;
}

.settingsManagerScreen__toggle-slider {
  position: absolute;
  cursor: pointer;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: #ccc;
  transition: 0.3s;
  border-radius: 34px;
}

.settingsManagerScreen__toggle-slider::before {
  position: absolute;
  content: "";
  height: 26px;
  width: 26px;
  left: 4px;
  bottom: 4px;
  background-color: white;
  transition: 0.3s;
  border-radius: 50%;
}

.settingsManagerScreen__toggle-input:checked + .settingsManagerScreen__toggle-slider {
  background-color: var(--settings-accentColor);
}

.settingsManagerScreen__toggle-input:checked + .settingsManagerScreen__toggle-slider::before {
  transform: translateX(26px);
}

/* Buttons */
.settingsManagerScreen__button {
  padding: 0.75rem 1.5rem;
  border: none;
  border-radius: 8px;
  font-size: 1rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  text-decoration: none;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
  min-width: 120px;
}

.settingsManagerScreen__button--primary {
  background: var(--settings-accentColor);
  color: white;
}

.settingsManagerScreen__button--primary:hover {
  background: #0056b3;
  transform: translateY(-1px);
}

.settingsManagerScreen__button--success {
  background: var(--settings-successColor);
  color: white;
}

.settingsManagerScreen__button--success:hover {
  background: #218838;
  transform: translateY(-1px);
}

.settingsManagerScreen__button--danger {
  background: var(--settings-dangerColor);
  color: white;
}

.settingsManagerScreen__button--danger:hover {
  background: #c82333;
  transform: translateY(-1px);
}

.settingsManagerScreen__button--secondary {
  background: var(--settings-secondaryColor);
  color: white;
}

.settingsManagerScreen__button--secondary:hover {
  background: #545b62;
  transform: translateY(-1px);
}

/* Action buttons group */
.settingsManagerScreen__actions {
  display: flex;
  gap: 1rem;
  margin-top: 2rem;
  padding-top: 2rem;
  border-top: 1px solid var(--settings-borderColor);
  justify-content: flex-end;
}

/* Color picker */
.settingsManagerScreen__color-picker {
  width: 50px;
  height: 50px;
  border: 2px solid var(--settings-borderColor);
  border-radius: 8px;
  cursor: pointer;
  transition: border-color 0.2s ease;
}

.settingsManagerScreen__color-picker:hover {
  border-color: var(--settings-accentColor);
}

/* Number input with stepper */
.settingsManagerScreen__number-group {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.settingsManagerScreen__number-input {
  width: 80px;
  text-align: center;
}

.settingsManagerScreen__stepper-button {
  width: 32px;
  height: 32px;
  border: 1px solid var(--settings-borderColor);
  background: var(--settings-cardBackground);
  border-radius: 4px;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s ease;
}

.settingsManagerScreen__stepper-button:hover {
  background: var(--settings-background);
  border-color: var(--settings-accentColor);
}

/* Alert messages */
.settingsManagerScreen__alert {
  padding: 1rem 1.5rem;
  border-radius: 8px;
  margin-bottom: 1.5rem;
  border: 1px solid;
  display: flex;
  align-items: center;
  gap: 0.75rem;
}

.settingsManagerScreen__alert--success {
  background: #d4edda;
  border-color: #c3e6cb;
  color: #155724;
}

.settingsManagerScreen__alert--warning {
  background: #fff3cd;
  border-color: #ffeaa7;
  color: #856404;
}

.settingsManagerScreen__alert--danger {
  background: #f8d7da;
  border-color: #f5c6cb;
  color: #721c24;
}

.settingsManagerScreen__alert--info {
  background: #d1ecf1;
  border-color: #bee5eb;
  color: #0c5460;
}

/* Responsive design */
@media (max-width: 768px) {
  .settingsManagerScreen {
    padding: 1rem;
  }
  
  .settingsManagerScreen__section-header {
    padding: 1rem;
  }
  
  .settingsManagerScreen__section-content {
    padding: 1rem;
  }
  
  .settingsManagerScreen__setting-item {
    flex-direction: column;
    align-items: flex-start;
    gap: 1rem;
  }
  
  .settingsManagerScreen__setting-control {
    width: 100%;
  }
  
  .settingsManagerScreen__input,
  .settingsManagerScreen__select,
  .settingsManagerScreen__textarea {
    min-width: 100%;
  }
  
  .settingsManagerScreen__actions {
    flex-direction: column;
  }
  
  .settingsManagerScreen__button {
    width: 100%;
  }
}

/* Loading state */
.settingsManagerScreen__loading {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 200px;
  color: var(--settings-textColorLight);
  font-size: 1.1rem;
}

.settingsManagerScreen__spinner {
  width: 32px;
  height: 32px;
  border: 3px solid var(--settings-borderColor);
  border-top: 3px solid var(--settings-accentColor);
  border-radius: 50%;
  animation: settingsManagerScreen-spin 1s linear infinite;
  margin-right: 1rem;
}

@keyframes settingsManagerScreen-spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}


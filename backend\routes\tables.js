// Backend route for tables management
// مسارات إدارة الطاولات المباشرة
const express = require('express');
const router = express.Router();
const { authenticateToken } = require('../middleware/auth');
const Table = require('../models/Table');
const User = require('../models/User');

/**
 * @route   PUT /api/tables/:tableNumber/close
 * @desc    Close a table by table number
 * @access  Private
 */
router.put('/:tableNumber/close', authenticateToken, async (req, res) => {
  try {
    const tableNumber = parseInt(req.params.tableNumber);
    
    if (isNaN(tableNumber)) {
      return res.status(400).json({
        success: false,
        message: 'رقم الطاولة غير صحيح'
      });
    }

    // البحث عن الطاولة
    const table = await Table.findOne({ number: tableNumber })
      .populate('currentOrder');

    if (!table) {
      return res.status(404).json({
        success: false,
        message: 'الطاولة غير موجودة'
      });
    }

    // تحديث حالة الطلب إلى مكتمل
    if (table.currentOrder) {
      const order = table.currentOrder;
      order.status = 'completed';
      order.timing.completedAt = new Date();
      await order.save();

      // تحديث إحصائيات الطاولة
      await table.updateStats(order.total || 0);
    }

    // تحرير الطاولة
    await table.release();

    res.json({
      success: true,
      message: 'تم إغلاق الطاولة بنجاح',
      tableNumber: table.number
    });

    // Send Socket notifications
    if (global.socketHandlers) {
      try {
        const waiterUser = await User.findById(table.assignedWaiter);
        const waiterDisplayName = waiterUser ? (waiterUser.name || waiterUser.username) : 'نادل';

        global.socketHandlers.io.emit('table-status-change', {
          tableNumber: table.number,
          isOpen: false,
          waiterName: waiterDisplayName,
          action: 'closed',
          totalAmount: table.currentOrder?.total || 0,
          timestamp: new Date().toISOString()
        });

        console.log(`📡 تم إرسال إشعار إغلاق الطاولة ${table.number} بواسطة ${waiterDisplayName}`);
      } catch (socketError) {
        console.error('خطأ في إرسال إشعار Socket:', socketError);
      }
    }

  } catch (error) {
    console.error('خطأ في إغلاق الطاولة:', error);
    res.status(500).json({
      success: false,
      message: 'خطأ في الخادم أثناء إغلاق الطاولة',
      error: error.message
    });
  }
});

/**
 * @route   GET /api/tables
 * @desc    Get all tables with enhanced statistics
 * @access  Private
 */
router.get('/', authenticateToken, async (req, res) => {
  try {
    const Order = require('../models/Order'); // إضافة استيراد Order
    
    const tables = await Table.find({ isActive: true })
      .populate('assignedWaiter', 'name username')
      .populate('currentOrder')
      .sort({ section: 1, number: 1 });

    // إضافة إحصائيات محسنة لكل طاولة
    const enhancedTables = await Promise.all(tables.map(async (table) => {
      try {
        // جلب عدد الطلبات للطاولة
        const ordersCount = await Order.countDocuments({
          table: table._id,
          status: { $in: ['pending', 'preparing', 'ready', 'served', 'delivered'] }
        });

        // جلب إجمالي المبيعات للطاولة
        const salesData = await Order.aggregate([
          { $match: { table: table._id, status: { $in: ['served', 'delivered'] } } },
          { $group: { _id: null, totalSales: { $sum: '$pricing.total' } } }
        ]);

        const totalSales = salesData.length > 0 ? salesData[0].totalSales : 0;

        return {
          ...table.toObject(),
          statistics: {
            ordersCount: ordersCount,
            totalSales: totalSales,
            isOccupied: table.status === 'occupied',
            waiterName: table.assignedWaiter ? 
              (table.assignedWaiter.name || table.assignedWaiter.username) : null
          }
        };
      } catch (error) {
        console.error(`Error processing table ${table.number}:`, error);
        return {
          ...table.toObject(),
          statistics: {
            ordersCount: 0,
            totalSales: 0,
            isOccupied: table.status === 'occupied',
            waiterName: table.assignedWaiter ? 
              (table.assignedWaiter.name || table.assignedWaiter.username) : null
          }
        };
      }
    }));

    console.log('📋 Tables fetched with statistics:', {
      totalTables: enhancedTables.length,
      occupiedTables: enhancedTables.filter(t => t.status === 'occupied').length,
      availableTables: enhancedTables.filter(t => t.status === 'available').length
    });

    res.json({
      success: true,
      data: enhancedTables,
      meta: {
        total: enhancedTables.length,
        occupied: enhancedTables.filter(t => t.status === 'occupied').length,
        available: enhancedTables.filter(t => t.status === 'available').length
      }
    });
  } catch (error) {
    console.error('خطأ في جلب الطاولات:', error);
    res.status(500).json({
      success: false,
      message: 'خطأ في الخادم أثناء جلب الطاولات',
      error: error.message
    });
  }
});

/**
 * @route   POST /api/tables/free-all
 * @desc    Free all occupied tables (Admin only)
 * @access  Private (Manager/Admin)
 */
router.post('/free-all', authenticateToken, async (req, res) => {
  try {
    // التحقق من صلاحيات المستخدم
    if (req.user.role !== 'manager' && req.user.role !== 'admin') {
      return res.status(403).json({
        success: false,
        message: 'غير مسموح - المدير فقط يمكنه تحرير جميع الطاولات'
      });
    }

    console.log(`🔧 ${req.user.name} يحاول تحرير جميع الطاولات المحجوزة...`);

    // البحث عن جميع الطاولات المحجوزة
    const occupiedTables = await Table.find({ status: 'occupied' })
      .populate('assignedWaiter', 'name username')
      .populate('currentOrder');

    if (occupiedTables.length === 0) {
      return res.json({
        success: true,
        message: 'جميع الطاولات متاحة بالفعل',
        data: {
          freed: 0,
          tables: []
        }
      });
    }

    const freedTables = [];
    let errorCount = 0;

    // تحرير كل طاولة محجوزة
    for (const table of occupiedTables) {
      try {
        const waiterInfo = table.assignedWaiter ? 
          `${table.assignedWaiter.name || table.assignedWaiter.username}` : 
          'غير محدد';

        console.log(`📍 تحرير الطاولة ${table.number} (كانت محجوزة من: ${waiterInfo})`);

        // إذا كان هناك طلب نشط، قم بإكماله
        if (table.currentOrder && table.currentOrder.status !== 'completed') {
          table.currentOrder.status = 'completed';
          table.currentOrder.timing.completedAt = new Date();
          await table.currentOrder.save();
          
          // تحديث إحصائيات الطاولة
          await table.updateStats(table.currentOrder.totals?.total || 0);
        }

        // تحرير الطاولة
        await table.release();

        freedTables.push({
          number: table.number,
          previousWaiter: waiterInfo,
          hadActiveOrder: !!table.currentOrder
        });

      } catch (tableError) {
        console.error(`❌ فشل تحرير الطاولة ${table.number}:`, tableError);
        errorCount++;
      }
    }

    console.log(`✅ تم تحرير ${freedTables.length} طاولة بنجاح (${errorCount} أخطاء)`);

    res.json({
      success: true,
      message: `تم تحرير ${freedTables.length} طاولة بنجاح`,
      data: {
        freed: freedTables.length,
        errors: errorCount,
        tables: freedTables
      }
    });

  } catch (error) {
    console.error('❌ خطأ في تحرير الطاولات:', error);
    res.status(500).json({
      success: false,
      message: 'خطأ في الخادم أثناء تحرير الطاولات',
      error: error.message
    });
  }
});

/**
 * @route   POST /api/tables/release-all
 * @desc    Release all occupied tables (admin only)
 * @access  Private (Admin)
 */
router.post('/release-all', authenticateToken, async (req, res) => {
  try {
    console.log('🔓 محاولة تحرير جميع الطاولات المحجوزة...');
    console.log('👤 المستخدم:', req.user.username, '- الدور:', req.user.role);
    
    // التحقق من صلاحيات المدير
    if (req.user.role !== 'مدير' && req.user.role !== 'manager') {
      return res.status(403).json({
        success: false,
        message: 'هذه العملية مخصصة للمدير فقط'
      });
    }

    // البحث عن جميع الطاولات المحجوزة
    const occupiedTables = await Table.find({ 
      assignedWaiter: { $ne: null } 
    }).populate('assignedWaiter', 'username name');

    console.log(`📊 تم العثور على ${occupiedTables.length} طاولة محجوزة`);

    if (occupiedTables.length === 0) {
      return res.json({
        success: true,
        message: 'جميع الطاولات متاحة بالفعل',
        releasedCount: 0,
        tables: []
      });
    }

    // تحرير جميع الطاولات
    const releasedTables = [];
    let releasedCount = 0;

    for (const table of occupiedTables) {
      try {
        const waiterInfo = table.assignedWaiter;
        console.log(`🔓 تحرير الطاولة ${table.number} (كانت محجوزة للنادل: ${waiterInfo?.username || 'غير محدد'})`);
        
        // تحرير الطاولة
        table.assignedWaiter = null;
        table.status = 'available';
        table.lastReleased = new Date();
        
        await table.save();
        
        releasedTables.push({
          number: table.number,
          previousWaiter: waiterInfo?.username || waiterInfo?.name || 'غير محدد'
        });
        
        releasedCount++;
        
      } catch (tableError) {
        console.error(`❌ خطأ في تحرير الطاولة ${table.number}:`, tableError);
      }
    }

    console.log(`✅ تم تحرير ${releasedCount} طاولة بنجاح`);

    res.json({
      success: true,
      message: `تم تحرير ${releasedCount} طاولة بنجاح`,
      releasedCount: releasedCount,
      tables: releasedTables
    });

  } catch (error) {
    console.error('❌ خطأ في تحرير جميع الطاولات:', error);
    res.status(500).json({
      success: false,
      message: 'خطأ في الخادم أثناء تحرير الطاولات',
      error: error.message
    });
  }
});

/**
 * @route   PUT /api/tables/:tableNumber/release
 * @desc    Release a specific table by number
 * @access  Private
 */
router.put('/:tableNumber/release', authenticateToken, async (req, res) => {
  try {
    const tableNumber = parseInt(req.params.tableNumber);
    
    if (isNaN(tableNumber)) {
      return res.status(400).json({
        success: false,
        message: 'رقم الطاولة غير صحيح'
      });
    }

    console.log(`🔓 محاولة تحرير الطاولة ${tableNumber}...`);
    console.log('👤 المستخدم:', req.user.username, '- الدور:', req.user.role);

    // البحث عن الطاولة
    const table = await Table.findOne({ number: tableNumber })
      .populate('assignedWaiter', 'username name');

    if (!table) {
      return res.status(404).json({
        success: false,
        message: 'الطاولة غير موجودة'
      });
    }

    // التحقق من حالة الطاولة
    if (!table.assignedWaiter) {
      return res.json({
        success: true,
        message: 'الطاولة متاحة بالفعل',
        table: {
          number: table.number,
          status: table.status
        }
      });
    }

    // التحقق من الصلاحيات
    const isManager = req.user.role === 'مدير' || req.user.role === 'manager';
    const isAssignedWaiter = table.assignedWaiter._id.toString() === req.user._id.toString();
    
    if (!isManager && !isAssignedWaiter) {
      return res.status(403).json({
        success: false,
        message: 'لا يمكنك تحرير طاولة محجوزة لنادل آخر'
      });
    }

    const waiterInfo = table.assignedWaiter;
    console.log(`🔓 تحرير الطاولة ${table.number} (كانت محجوزة للنادل: ${waiterInfo.username})`);

    // تحرير الطاولة
    table.assignedWaiter = null;
    table.status = 'available';
    table.lastReleased = new Date();
    
    await table.save();

    console.log(`✅ تم تحرير الطاولة ${table.number} بنجاح`);

    res.json({
      success: true,
      message: `تم تحرير الطاولة ${table.number} بنجاح`,
      table: {
        number: table.number,
        status: table.status,
        previousWaiter: waiterInfo.username || waiterInfo.name
      }
    });

  } catch (error) {
    console.error(`❌ خطأ في تحرير الطاولة:`, error);
    res.status(500).json({
      success: false,
      message: 'خطأ في الخادم أثناء تحرير الطاولة',
      error: error.message
    });
  }
});

module.exports = router;

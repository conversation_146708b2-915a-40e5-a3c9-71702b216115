const mongoose = require('mongoose');

async function analyzeWaiterSales() {
  try {
    console.log('🔗 الاتصال بقاعدة البيانات...');
    
    const mongoUri = 'mongodb+srv://besomustafa:<EMAIL>/deshacoffee?retryWrites=true&w=majority&appName=MyCoffeChop';
    
    await mongoose.connect(mongoUri, {
      serverSelectionTimeoutMS: 5000,
      connectTimeoutMS: 10000,
    });
    
    console.log('✅ تم الاتصال بنجاح');
    
    console.log('\n' + '='.repeat(80));
    console.log('📊 تحليل شامل لحساب إجمالي المبيعات للنادل وفحص فقدان الطلبات');
    console.log('='.repeat(80));
    
    // جلب جميع الطلبات
    const orders = await mongoose.connection.db.collection('orders').find({}).toArray();
    console.log(`\n📋 إجمالي الطلبات في النظام: ${orders.length}`);
    
    // جلب جميع النُدل
    const waiters = await mongoose.connection.db.collection('users').find({ role: 'waiter' }).toArray();
    console.log(`👥 إجمالي النُدل في النظام: ${waiters.length}`);
    
    if (orders.length === 0) {
      console.log('❌ لا توجد طلبات في النظام للتحليل');
      return;
    }
    
    console.log('\n🔍 تحليل طرق حساب المبيعات الحالية:');
    console.log('═'.repeat(60));
    
    // تحليل كيفية حساب المبيعات
    let totalSystemSales = 0;
    let ordersWithAmount = 0;
    let ordersWithoutAmount = 0;
    
    const statusBreakdown = {};
    const amountSources = {
      totalPrice: 0,
      totalAmount: 0,
      totalsTotal: 0,
      pricingTotal: 0,
      noAmount: 0
    };
    
    orders.forEach(order => {
      const status = order.status || 'غير محدد';
      statusBreakdown[status] = (statusBreakdown[status] || 0) + 1;
      
      // تحديد مصدر المبلغ (نفس المنطق المستخدم في النظام)
      let orderAmount = 0;
      let amountSource = 'noAmount';
      
      if (order.totals && order.totals.total) {
        orderAmount = order.totals.total;
        amountSource = 'totalsTotal';
      } else if (order.totalPrice) {
        orderAmount = order.totalPrice;
        amountSource = 'totalPrice';
      } else if (order.totalAmount) {
        orderAmount = order.totalAmount;
        amountSource = 'totalAmount';
      } else if (order.pricing && order.pricing.total) {
        orderAmount = order.pricing.total;
        amountSource = 'pricingTotal';
      }
      
      amountSources[amountSource]++;
      
      if (orderAmount > 0) {
        totalSystemSales += orderAmount;
        ordersWithAmount++;
      } else {
        ordersWithoutAmount++;
      }
    });
    
    console.log('\n💰 تحليل مصادر المبالغ:');
    console.log(`   الطلبات بمبالغ: ${ordersWithAmount}`);
    console.log(`   الطلبات بدون مبالغ: ${ordersWithoutAmount}`);
    console.log(`   مصادر المبالغ:`);
    console.log(`     - totals.total: ${amountSources.totalsTotal} طلب`);
    console.log(`     - totalPrice: ${amountSources.totalPrice} طلب`);
    console.log(`     - totalAmount: ${amountSources.totalAmount} طلب`);
    console.log(`     - pricing.total: ${amountSources.pricingTotal} طلب`);
    console.log(`     - بدون مبلغ: ${amountSources.noAmount} طلب`);
    console.log(`   إجمالي المبيعات في النظام: ${totalSystemSales.toFixed(2)} جنيه`);
    
    console.log('\n📊 توزيع الطلبات حسب الحالة:');
    Object.entries(statusBreakdown).forEach(([status, count]) => {
      const percentage = ((count / orders.length) * 100).toFixed(1);
      console.log(`   ${status}: ${count} طلب (${percentage}%)`);
    });
    
    console.log('\n' + '='.repeat(80));
    console.log('👥 تحليل ربط الطلبات بالنُدل');
    console.log('='.repeat(80));
    
    let totalMatchedOrders = 0;
    let totalUnmatchedOrders = 0;
    let totalWaiterSales = 0;
    
    // تحليل كل نادل
    console.log('\n🔍 تحليل النُدل:');
    waiters.forEach((waiter, index) => {
      console.log(`\n${index + 1}. النادل: ${waiter.username} (${waiter.name || 'بدون اسم'})`);
      console.log(`   الحالة: ${waiter.isActive ? 'نشط' : 'غير نشط'}`);
      console.log(`   معرف النادل: ${waiter._id}`);
      
      // البحث عن الطلبات المربوطة بالنادل بطرق مختلفة
      const waiterOrderIds = new Set();
      let waiterSales = 0;
      
      // طريقة 1: staff.waiter
      const ordersByStaff = orders.filter(order => 
        order.staff?.waiter && order.staff.waiter.toString() === waiter._id.toString()
      );
      console.log(`   📝 طلبات عبر staff.waiter: ${ordersByStaff.length}`);
      
      // طريقة 2: waiterName
      const ordersByName = orders.filter(order => 
        order.waiterName === waiter.username || order.waiterName === waiter.name
      );
      console.log(`   📝 طلبات عبر waiterName: ${ordersByName.length}`);
      
      // طريقة 3: waiterId
      const ordersByWaiterId = orders.filter(order => 
        order.waiterId && order.waiterId.toString() === waiter._id.toString()
      );
      console.log(`   📝 طلبات عبر waiterId: ${ordersByWaiterId.length}`);
      
      // دمج جميع الطلبات (إزالة التكرار)
      [...ordersByStaff, ...ordersByName, ...ordersByWaiterId].forEach(order => {
        waiterOrderIds.add(order._id.toString());
      });
      
      // حساب المبيعات
      waiterOrderIds.forEach(orderId => {
        const order = orders.find(o => o._id.toString() === orderId);
        if (order) {
          let orderAmount = 0;
          if (order.totals && order.totals.total) {
            orderAmount = order.totals.total;
          } else if (order.totalPrice) {
            orderAmount = order.totalPrice;
          } else if (order.totalAmount) {
            orderAmount = order.totalAmount;
          } else if (order.pricing && order.pricing.total) {
            orderAmount = order.pricing.total;
          }
          waiterSales += orderAmount;
        }
      });
      
      totalMatchedOrders += waiterOrderIds.size;
      totalWaiterSales += waiterSales;
      
      console.log(`   📊 إجمالي الطلبات المربوطة: ${waiterOrderIds.size}`);
      console.log(`   💰 إجمالي المبيعات: ${waiterSales.toFixed(2)} جنيه`);
      
      if (waiterOrderIds.size > 0) {
        console.log(`   📈 متوسط قيمة الطلب: ${(waiterSales / waiterOrderIds.size).toFixed(2)} جنيه`);
      }
    });
    
    // فحص الطلبات غير المربوطة
    console.log('\n' + '='.repeat(80));
    console.log('🔍 فحص الطلبات غير المربوطة بأي نادل');
    console.log('='.repeat(80));
    
    const unmatchedOrders = [];
    let unmatchedSales = 0;
    
    orders.forEach(order => {
      let isMatched = false;
      
      // فحص جميع طرق الربط
      for (const waiter of waiters) {
        if (order.staff?.waiter && order.staff.waiter.toString() === waiter._id.toString()) {
          isMatched = true;
          break;
        }
        if (order.waiterName === waiter.username || order.waiterName === waiter.name) {
          isMatched = true;
          break;
        }
        if (order.waiterId && order.waiterId.toString() === waiter._id.toString()) {
          isMatched = true;
          break;
        }
      }
      
      if (!isMatched) {
        const orderAmount = order.totals?.total || order.totalPrice || order.totalAmount || order.pricing?.total || 0;
        unmatchedOrders.push({
          orderNumber: order.orderNumber,
          amount: orderAmount,
          status: order.status,
          waiterName: order.waiterName || 'غير محدد',
          waiterId: order.waiterId || 'غير محدد'
        });
        unmatchedSales += orderAmount;
      }
    });
    
    totalUnmatchedOrders = unmatchedOrders.length;
    
    console.log(`\n📊 الطلبات غير المربوطة: ${totalUnmatchedOrders}`);
    if (unmatchedOrders.length > 0) {
      console.log(`💸 مبيعات مفقودة: ${unmatchedSales.toFixed(2)} جنيه`);
      console.log('\n⚠️  عينة من الطلبات غير المربوطة:');
      unmatchedOrders.slice(0, 5).forEach((order, index) => {
        console.log(`   ${index + 1}. ${order.orderNumber}: ${order.amount} جنيه، النادل: ${order.waiterName}, الحالة: ${order.status}`);
      });
    }
    
    console.log('\n' + '='.repeat(80));
    console.log('📋 الملخص النهائي');
    console.log('='.repeat(80));
    
    console.log(`\n📊 الإحصائيات العامة:`);
    console.log(`   إجمالي الطلبات: ${orders.length}`);
    console.log(`   الطلبات المربوطة بنُدل: ${totalMatchedOrders}`);
    console.log(`   الطلبات غير المربوطة: ${totalUnmatchedOrders}`);
    console.log(`   نسبة الربط: ${((totalMatchedOrders / orders.length) * 100).toFixed(1)}%`);
    
    console.log(`\n💰 المبيعات:`);
    console.log(`   إجمالي المبيعات في النظام: ${totalSystemSales.toFixed(2)} جنيه`);
    console.log(`   مبيعات النُدل المحسوبة: ${totalWaiterSales.toFixed(2)} جنيه`);
    console.log(`   المبيعات المفقودة: ${unmatchedSales.toFixed(2)} جنيه`);
    console.log(`   نسبة المبيعات المحسوبة: ${((totalWaiterSales / totalSystemSales) * 100).toFixed(1)}%`);
    
    console.log(`\n🔍 كيفية حساب المبيعات حالياً:`);
    console.log(`   1. النظام يبحث عن النادل في كل طلب باستخدام:`);
    console.log(`      - staff.waiter (المعرف)`);
    console.log(`      - waiterName (اسم المستخدم أو الاسم الكامل)`);
    console.log(`      - waiterId (معرف النادل)`);
    console.log(`   2. يحسب المبلغ من أفضل مصدر متاح:`);
    console.log(`      - totals.total (الأولوية الأولى)`);
    console.log(`      - totalPrice (الأولوية الثانية)`);
    console.log(`      - totalAmount (الأولوية الثالثة)`);
    console.log(`      - pricing.total (الأولوية الرابعة)`);
    console.log(`   3. يجمع المبالغ للطلبات المكتملة: served, delivered, completed`);
    
    if (unmatchedOrders.length > 0) {
      console.log(`\n⚠️  مشكلة مكتشفة: يوجد فقدان في حساب المبيعات!`);
      console.log(`\n📋 الأسباب المحتملة:`);
      console.log(`   1. طلبات تم إنشاؤها بدون ربط نادل`);
      console.log(`   2. نُدل تم حذفهم من النظام بعد إنشاء طلبات`);
      console.log(`   3. أخطاء في تخزين معرف النادل`);
      console.log(`   4. طلبات أُنشئت بأسماء نُدل غير موجودين`);
      
      console.log(`\n💡 الحلول المقترحة:`);
      console.log(`   1. مراجعة الطلبات غير المربوطة وربطها بالنُدل المناسبين`);
      console.log(`   2. تحديث منطق إنشاء الطلبات لضمان الربط الصحيح`);
      console.log(`   3. إضافة validation للتأكد من وجود النادل قبل إنشاء الطلب`);
      console.log(`   4. إنشاء مهمة دورية لفحص الطلبات غير المربوطة`);
    } else {
      console.log(`\n✅ ممتاز! جميع الطلبات مربوطة بنُدل بشكل صحيح`);
      console.log(`✅ لا يوجد فقدان في حساب المبيعات`);
    }
    
  } catch (error) {
    console.error('❌ خطأ في التحليل:', error.message);
  } finally {
    await mongoose.disconnect();
    console.log('\n🔌 تم قطع الاتصال بقاعدة البيانات');
  }
}

analyzeWaiterSales();

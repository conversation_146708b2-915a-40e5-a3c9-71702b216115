// خدمة Push Notifications والإشعارات حتى في حالة الخمول
export class BackgroundNotificationService {
  private serviceWorkerRegistration: ServiceWorkerRegistration | null = null;
  private isSupported: boolean = false;
  private permissionGranted: boolean = false;
  private isInitialized: boolean = false;

  constructor() {
    this.checkSupport();
  }

  // التحقق من دعم المتصفح للـ Push Notifications
  private checkSupport(): void {
    this.isSupported = 
      'serviceWorker' in navigator &&
      'PushManager' in window &&
      'Notification' in window;
    
    console.log('🔍 دعم Push Notifications:', this.isSupported);
  }

  // تهيئة الخدمة
  public async initialize(): Promise<boolean> {
    if (!this.isSupported) {
      console.warn('⚠️ المتصفح لا يدعم Push Notifications');
      return false;
    }

    try {
      // تسجيل Service Worker
      const registration = await navigator.serviceWorker.register('/sw.js', {
        scope: '/'
      });

      console.log('✅ تم تسجيل Service Worker:', registration);
      this.serviceWorkerRegistration = registration;

      // طلب إذن الإشعارات
      await this.requestNotificationPermission();

      // إعداد الاستماع للرسائل من Service Worker
      this.setupMessageListener();

      // إعداد مراقبة حالة الصفحة
      this.setupPageVisibilityListener();

      this.isInitialized = true;
      console.log('✅ تم تهيئة خدمة الإشعارات بنجاح');
      
      return true;
    } catch (error) {
      console.error('❌ خطأ في تهيئة خدمة الإشعارات:', error);
      return false;
    }
  }

  // طلب إذن الإشعارات
  private async requestNotificationPermission(): Promise<void> {
    if (!('Notification' in window)) {
      throw new Error('هذا المتصفح لا يدعم الإشعارات');
    }

    let permission = Notification.permission;

    if (permission === 'default') {
      permission = await Notification.requestPermission();
    }

    if (permission === 'granted') {
      this.permissionGranted = true;
      console.log('✅ تم منح إذن الإشعارات');
    } else {
      this.permissionGranted = false;
      console.warn('❌ تم رفض إذن الإشعارات');
      throw new Error('إذن الإشعارات مطلوب لعمل هذه الخاصية');
    }
  }

  // إعداد الاستماع للرسائل من Service Worker
  private setupMessageListener(): void {
    if (!navigator.serviceWorker) return;

    navigator.serviceWorker.addEventListener('message', (event) => {
      console.log('💬 رسالة من Service Worker:', event.data);
      
      if (event.data.type === 'NOTIFICATION_CLICKED') {
        // التعامل مع النقر على الإشعار
        this.handleNotificationClick(event.data);
      }
    });
  }

  // إعداد مراقبة حالة الصفحة (نشطة/غير نشطة)
  private setupPageVisibilityListener(): void {
    document.addEventListener('visibilitychange', () => {
      const isHidden = document.hidden;
      console.log(`👁️ حالة الصفحة: ${isHidden ? 'غير نشطة' : 'نشطة'}`);
      
      if (isHidden) {
        // الصفحة أصبحت غير نشطة - تفعيل وضع الخلفية
        this.enableBackgroundMode();
      } else {
        // الصفحة أصبحت نشطة - تعطيل وضع الخلفية
        this.disableBackgroundMode();
      }
    });
  }

  // تفعيل وضع الخلفية
  private enableBackgroundMode(): void {
    console.log('🌙 تفعيل وضع الخلفية - سيتم إرسال الإشعارات عبر Service Worker');
    
    // إرسال رسالة لـ Service Worker لتفعيل وضع الخلفية
    if (this.serviceWorkerRegistration && this.serviceWorkerRegistration.active) {
      this.serviceWorkerRegistration.active.postMessage({
        type: 'ENABLE_BACKGROUND_MODE'
      });
    }
  }

  // تعطيل وضع الخلفية
  private disableBackgroundMode(): void {
    console.log('☀️ تعطيل وضع الخلفية - الصفحة نشطة');
    
    // إرسال رسالة لـ Service Worker لتعطيل وضع الخلفية
    if (this.serviceWorkerRegistration && this.serviceWorkerRegistration.active) {
      this.serviceWorkerRegistration.active.postMessage({
        type: 'DISABLE_BACKGROUND_MODE'
      });
    }
  }

  // إرسال إشعار (يعمل حتى لو كانت الصفحة غير نشطة)
  public async sendNotification(options: {
    title: string;
    body: string;
    icon?: string;
    tag?: string;
    data?: any;
    urgent?: boolean;
    sound?: boolean;
  }): Promise<void> {
    if (!this.isInitialized || !this.permissionGranted) {
      console.warn('⚠️ خدمة الإشعارات غير مهيأة أو الإذن غير ممنوح');
      return;
    }

    const notificationData = {
      title: options.title,
      body: options.body,
      icon: options.icon || '/coffee-logo.svg',
      badge: '/coffee-cup.svg',
      tag: options.tag || 'coffee-notification',
      renotify: true,
      requireInteraction: options.urgent || false,
      silent: !options.sound,
      vibrate: options.urgent ? [200, 100, 200, 100, 200] : [200, 100, 200],
      data: options.data || {},
      timestamp: Date.now()
    };

    try {
      if (document.hidden) {
        // الصفحة غير نشطة - استخدام Service Worker
        console.log('📱 إرسال إشعار عبر Service Worker (الصفحة غير نشطة)');
        
        if (this.serviceWorkerRegistration) {
          this.serviceWorkerRegistration.active?.postMessage({
            type: 'SHOW_NOTIFICATION',
            ...notificationData
          });
        }
      } else {
        // الصفحة نشطة - إشعار مباشر
        console.log('📱 إرسال إشعار مباشر (الصفحة نشطة)');
        
        const notification = new Notification(notificationData.title, notificationData);
        
        // إغلاق الإشعار تلقائياً بعد فترة
        if (!options.urgent) {
          setTimeout(() => {
            notification.close();
          }, 5000);
        }

        // التعامل مع النقر على الإشعار
        notification.onclick = () => {
          window.focus();
          notification.close();
          this.handleNotificationClick(notificationData);
        };
      }
    } catch (error) {
      console.error('❌ خطأ في إرسال الإشعار:', error);
    }
  }

  // التعامل مع النقر على الإشعار
  private handleNotificationClick(data: any): void {
    console.log('👆 تم النقر على الإشعار:', data);
    
    // يمكن إضافة منطق مخصص هنا حسب نوع الإشعار
    if (data.data && data.data.action) {
      this.executeNotificationAction(data.data.action, data.data);
    }
  }

  // تنفيذ إجراء مخصص عند النقر على الإشعار
  private executeNotificationAction(action: string, data: any): void {
    switch (action) {
      case 'navigate_to_orders':
        // الانتقال إلى صفحة الطلبات
        window.location.hash = '#orders';
        break;
      case 'navigate_to_kitchen':
        // الانتقال إلى صفحة المطبخ
        window.location.hash = '#kitchen';
        break;
      case 'play_sound':
        // تشغيل صوت إضافي
        this.playNotificationSound();
        break;
      default:
        console.log('إجراء غير معروف:', action);
    }
  }

  // تشغيل صوت الإشعار
  private playNotificationSound(): void {
    try {
      const audio = new Audio('/notification.wav');
      audio.volume = 0.8;
      audio.play().catch(error => {
        console.error('❌ خطأ في تشغيل صوت الإشعار:', error);
      });
    } catch (error) {
      console.error('❌ خطأ في إنشاء عنصر الصوت:', error);
    }
  }

  // التحقق من حالة الخدمة
  public getStatus(): {
    isSupported: boolean;
    isInitialized: boolean;
    permissionGranted: boolean;
    isPageVisible: boolean;
  } {
    return {
      isSupported: this.isSupported,
      isInitialized: this.isInitialized,
      permissionGranted: this.permissionGranted,
      isPageVisible: !document.hidden
    };
  }

  // تنظيف الموارد
  public cleanup(): void {
    if (this.serviceWorkerRegistration) {
      this.serviceWorkerRegistration.unregister();
    }
  }
}

// إنشاء مثيل واحد للخدمة
export const backgroundNotificationService = new BackgroundNotificationService();

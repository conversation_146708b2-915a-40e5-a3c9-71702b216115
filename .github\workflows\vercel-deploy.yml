name: Deploy to Vercel

on:
  push:
    branches: [ main ]
  workflow_dispatch:

env:
  VERCEL_ORG_ID: ${{ secrets.VERCEL_ORG_ID }}
  VERCEL_PROJECT_ID: ${{ secrets.VERCEL_PROJECT_ID }}

jobs:
  build-and-deploy:
    runs-on: ubuntu-latest
    
    steps:
    - name: Checkout
      uses: actions/checkout@v4

    - name: Setup Node.js
      uses: actions/setup-node@v4
      with:
        node-version: '18'
        cache: 'npm'

    - name: Install dependencies
      run: npm ci --legacy-peer-deps || npm install --legacy-peer-deps

    - name: Build project
      run: npm run build
      env:
        VITE_API_URL: ${{ secrets.VITE_API_URL }}
        VITE_API_TIMEOUT: ${{ secrets.VITE_API_TIMEOUT }}
        VITE_SOCKET_URL: ${{ secrets.VITE_SOCKET_URL }}
        VITE_SOCKET_TIMEOUT: ${{ secrets.VITE_SOCKET_TIMEOUT }}
        VITE_APP_NAME: ${{ secrets.VITE_APP_NAME }}
        VITE_APP_VERSION: ${{ secrets.VITE_APP_VERSION }}
        NODE_ENV: production

    - name: Install Vercel CLI
      run: npm install --global vercel@latest

    - name: Pull Vercel Environment Information
      run: vercel pull --yes --environment=production --token=${{ secrets.VERCEL_TOKEN }}

    - name: Build Project Artifacts
      run: vercel build --prod --token=${{ secrets.VERCEL_TOKEN }}

    - name: Deploy Project Artifacts to Vercel
      run: vercel deploy --prebuilt --prod --token=${{ secrets.VERCEL_TOKEN }}

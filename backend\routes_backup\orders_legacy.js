const express = require('express');
const Order = require('../models/Order');
const Product = require('../models/Product');
const User = require('../models/User');
const Table = require('../models/Table');
const { authenticateToken, requireManager } = require('../middleware/auth');
const orderCompatibilityMiddleware = require('../middleware/orderCompatibility');
const responseMapping = require('../middleware/responseMapping');
const { 
  sendSuccess, 
  sendError, 
  sendNotFound, 
  handleDatabaseError,
  deviceDetection,
  setCorsHeaders,
  requestLogger
} = require('../middleware/unifiedResponse');
const { 
  validateOrder, 
  validateObjectId,
  validatePagination
} = require('../middleware/unifiedValidation');

const router = express.Router();

// Apply unified middleware to all routes
router.use(setCorsHeaders);
router.use(deviceDetection);
router.use(requestLogger);

// Get all orders
router.get('/', async (req, res) => {
  try {
    const { status, orderType, date, waiterName, chefName } = req.query;
    let query = {};

    // Build query filters
    if (status) {
      query.status = status;
    }

    if (orderType) {
      query.orderType = orderType;
    }

    if (waiterName) {
      query['staff.waiter'] = waiterName;
    }

    if (chefName) {
      query['staff.chef'] = chefName;
    }

    if (date) {
      const targetDate = new Date(date);
      const startOfDay = new Date(targetDate.getFullYear(), targetDate.getMonth(), targetDate.getDate());
      const endOfDay = new Date(targetDate.getFullYear(), targetDate.getMonth(), targetDate.getDate() + 1);

      query['timing.orderTime'] = {
        $gte: startOfDay,
        $lt: endOfDay
      };
    }

    // Get orders from database
    const orders = await Order.find(query)
      .populate('staff.waiter', 'name username')
      .populate('staff.chef', 'name username')
      .populate('items.product', 'name price')
      .sort({ 'timing.orderTime': -1 });

    // تحويل البيانات للتوافق مع Frontend
    const mappedOrders = responseMapping.mapOrdersArrayForFrontend(orders);
    res.json(mappedOrders);
  } catch (error) {
    console.error('Get orders error:', error);
    res.status(500).json({
      success: false,
      message: 'خطأ في جلب الطلبات',
      error: error.message
    });
  }
});

// Get order by ID
router.get('/:id', async (req, res) => {
  try {
    const order = await Order.findById(req.params.id)
      .populate('staff.waiter', 'name username')
      .populate('staff.chef', 'name username')
      .populate('items.product', 'name price');

    if (!order) {
      return res.status(404).json({
        success: false,
        message: 'الطلب غير موجود'
      });
    }

    // تحويل البيانات للتوافق مع Frontend
    const mappedOrder = responseMapping.mapOrderForFrontend(order);
    res.json({
      success: true,
      data: {
        ...order.toObject(),
        ...mappedOrder
      }
    });
  } catch (error) {
    console.error('Get order error:', error);
    res.status(500).json({
      success: false,
      message: 'خطأ في جلب الطلب'
    });
  }
});

// Create new order - موحد للحاسوب والهاتف
router.post('/', authenticateToken, orderCompatibilityMiddleware, async (req, res) => {
  try {
    const userAgent = req.headers['user-agent'] || '';
    const deviceType = userAgent.includes('Mobile') ? 'Mobile' : 'Desktop';
    
    console.log(`🛒 إنشاء طلب جديد من ${deviceType}:`);
    console.log('📱 البيانات المستلمة:', JSON.stringify(req.body, null, 2));
    console.log('👤 المستخدم:', { id: req.user?._id, username: req.user?.username, role: req.user?.role });
    
    const { customer, items, orderType, table: tableInfoFromRequest, delivery, payment, totals, waiterName: waiterNameFromRequest } = req.body;

    // التحقق من البيانات المطلوبة - موحد للحاسوب والهاتف
    if (!customer?.name || !items || !Array.isArray(items) || items.length === 0) {
      console.error(`❌ خطأ في بيانات الطلب من ${deviceType}:`, {
        customerName: customer?.name,
        itemsCount: items?.length,
        itemsValid: Array.isArray(items)
      });
      return res.status(400).json({
        success: false,
        message: 'اسم العميل والمنتجات مطلوبة',
        details: `فشل الطلب من ${deviceType}`,
        received: {
          customerNameProvided: !!customer?.name,
          itemsProvided: !!items,
          itemsIsArray: Array.isArray(items),
          itemsLength: items?.length || 0
        }
      });
    }

    // التحقق من رقم الطاولة للطلبات في الصالة
    if (orderType === 'dine-in' && (!tableInfoFromRequest || !tableInfoFromRequest.number)) {
      console.error(`❌ رقم الطاولة مفقود في طلب ${deviceType}`);
      return res.status(400).json({
        success: false,
        message: 'رقم الطاولة مطلوب لطلبات الصالة',
        details: `فشل الطلب من ${deviceType}`
      });
    }

    let assignedWaiterId = null;
    if (req.user && req.user._id) {
      assignedWaiterId = req.user._id.toString(); // Ensure it's a string for comparison
      console.log(`ℹ️ تم تحديد النادل مبدئيًا من التوكن: ${assignedWaiterId} (المستخدم: ${req.user.username})`);
    } else {
      console.warn('⚠️ لم يتم العثور على req.user._id من التوكن. هذا غير متوقع بعد authenticateToken middleware.');
      return res.status(401).json({ success: false, message: 'مصادقة النادل مطلوبة.' });
    }

    if (waiterNameFromRequest) {
      console.log(`🔍 البحث عن النادل المحدد في الطلب (waiterNameFromRequest): ${waiterNameFromRequest}`);
      const waiterFromRequest = await User.findOne({ 
        $or: [
          { username: waiterNameFromRequest },
          { name: waiterNameFromRequest }
        ]
      }).select('_id name username');

      if (waiterFromRequest) {
        assignedWaiterId = waiterFromRequest._id.toString(); // Ensure it's a string
        console.log(`✅ تم العثور على النادل من الطلب وتعيينه: ${waiterFromRequest.username} (ID: ${assignedWaiterId})`);
      } else {
        console.warn(`⚠️ لم يتم العثور على النادل بالاسم/اسم المستخدم '${waiterNameFromRequest}' من الطلب. سيتم استخدام النادل من التوكن (إذا وجد) أو لا شيء.`);
      }
    }
    
    if (!assignedWaiterId) {
        console.warn('⚠️ لم يتم تعيين أي نادل للطلب (assignedWaiterId is null). سيتم حفظ الطلب بدون نادل معين إذا سمح بذلك نموذج الطلب.');
    }

    // Validate and populate items with product data
    const populatedItems = [];
    let calculatedSubtotal = 0;

    for (const item of items) {
      if (!item.product) {
        console.error('❌ خطأ في بيانات المنتج: معرّف المنتج مفقود في العنصر:', item);
        return res.status(400).json({
          success: false,
          message: `معرّف المنتج مفقود لأحد العناصر في الطلب.`,
          itemData: item
        });
      }
      const product = await Product.findById(item.product);
      if (!product) {
        console.error(`❌ خطأ في بيانات المنتج: المنتج غير موجود بالمعرّف: ${item.product}`);
        return res.status(400).json({
          success: false,
          message: `المنتج غير موجود: ${item.product}`,
          itemData: item
        });
      }

      if (!product.available) {
        console.error(`❌ خطأ في بيانات المنتج: المنتج غير متاح: ${product.name} (المعرّف: ${item.product})`);
        return res.status(400).json({
          success: false,
          message: `المنتج غير متاح: ${product.name}`,
          itemData: item
        });
      }

      const itemSubtotal = product.price * item.quantity;
      populatedItems.push({
        product: product._id,
        productName: item.productName || item.name || product.name, // Ensure productName is set
        quantity: item.quantity,
        price: product.price,
        subtotal: itemSubtotal,
        notes: item.notes || ''
      });
      calculatedSubtotal += itemSubtotal;
    }
    console.log('🛍️ المنتجات المجهزة للطلب:', JSON.stringify(populatedItems, null, 2));

    // استخدام الـ totals المحسوبة أو حساب جديد
    // The Order model's pre-save hook will ultimately ensure totals are correct.
    // We pass what we have, and the model will validate/recalculate.
    const finalTotals = totals || { // 'totals' comes from req.body after compatibility middleware
      subtotal: calculatedSubtotal, // Fallback if not provided
      tax: 0, // Default, model might override
      discount: 0, // Default, model might override
      total: calculatedSubtotal // Fallback, model will override
    };
    console.log('💰 الإجماليات المحضرة للطلب (قبل نموذج الطلب):', JSON.stringify(finalTotals, null, 2));


    // قسم التحقق من الطاولة وإدارتها
    const tableNumberRaw = tableInfoFromRequest.number;
    console.log(`🔍 Attempting to find table. Raw Number: ${tableNumberRaw}, Type: ${typeof tableNumberRaw}`);
    const tableNumber = parseInt(tableNumberRaw, 10); // Convert to number
    
    if (isNaN(tableNumber)) {
      console.error(`❌ خطأ: رقم الطاولة غير صالح بعد التحويل: ${tableNumberRaw}`);
      return res.status(400).json({
        success: false,
        message: `رقم الطاولة المستلم غير صالح: ${tableNumberRaw}`
      });
    }

    console.log(`🔢 Converted table number for query: ${tableNumber}, Type: ${typeof tableNumber}`);
    let targetTable = await Table.findOne({ number: tableNumber, isActive: true });

    if (!targetTable) {
      // This means the table exists but is not active (based on the DEBUG log above),
      // or potentially it was active but some race condition/very recent change made it inactive.
      console.warn(`Table not found or not active for number: ${tableNumber} (previously found in DB, its logged isActive status was: ${tableExistsCheck.isActive}). Request by waiter: ${assignedWaiterId}`);
      return res.status(404).json({ message: `الطاولة رقم ${tableNumber} غير موجودة أو غير نشطة` });
    }

    console.log(`ℹ️ حالة الطاولة ${targetTable.number}: ${targetTable.status}, النادل المعين: ${targetTable.assignedWaiter}`);

    let tableWasNewlyOccupiedByThisRequest = false;

    if (targetTable.status === 'available') {
      console.log(`🟢 الطاولة ${targetTable.number} متاحة. سيتم شغلها بواسطة النادل ${assignedWaiterId}.`);
      // سيتم استدعاء occupy لاحقًا بعد حفظ الطلب بنجاح
      tableWasNewlyOccupiedByThisRequest = true;
    } else if (targetTable.status === 'occupied') {
      if (targetTable.assignedWaiter && targetTable.assignedWaiter.toString() === assignedWaiterId) {
        console.log(`🟡 الطاولة ${targetTable.number} مشغولة بالفعل بنفس النادل ${assignedWaiterId}. سيتم إضافة الطلب.`);
        // لا حاجة لتغيير حالة الطاولة، فقط سيتم تحديث الطلب الحالي لاحقًا
      } else {
        console.warn(`🔴 تعارض: الطاولة ${targetTable.number} مشغولة بنادل آخر (${targetTable.assignedWaiter}). النادل الحالي: ${assignedWaiterId}.`);

        // جلب معلومات النادل المخصص للطاولة
        const assignedWaiterInfo = await User.findById(targetTable.assignedWaiter);
        const assignedWaiterName = assignedWaiterInfo ? (assignedWaiterInfo.name || assignedWaiterInfo.username) : 'نادل آخر';

        if (global.socketHandlers && req.user && req.user.socketId) {
          global.socketHandlers.io.to(req.user.socketId).emit('table-access-conflict', {
            tableNumber: targetTable.number,
            assignedWaiterName: assignedWaiterName,
            message: `الطاولة رقم ${targetTable.number} مستخدمة حاليًا من قبل النادل: ${assignedWaiterName}`
          });
        }
        return res.status(409).json({
          success: false,
          message: `الطاولة رقم ${targetTable.number} مستخدمة حاليًا من قبل النادل: ${assignedWaiterName}. لا يمكنك إضافة طلبات لهذه الطاولة حتى يتم إغلاقها.`
        });
      }
    } else if (['reserved', 'cleaning', 'maintenance'].includes(targetTable.status)) {
      console.warn(`🚫 الطاولة ${targetTable.number} ليست متاحة حاليًا (الحالة: ${targetTable.status}).`);
      return res.status(403).json({
        success: false,
        message: `الطاولة رقم ${targetTable.number} ليست متاحة حاليًا (الحالة: ${targetTable.status}). لا يمكن إنشاء طلبات لها.`
      });
    } else {
      console.error(`❓ حالة غير معروفة للطاولة ${targetTable.number}: ${targetTable.status}.`);
      return res.status(500).json({
        success: false,
        message: 'حدث خطأ غير متوقع أثناء التحقق من حالة الطاولة.'
      });
    }

    const orderPayload = {
      customer,
      items: populatedItems,
      totals: finalTotals,
      orderType: orderType || 'dine-in',
      status: 'pending',
      table: { // تعديل حقل الطاولة
        tableId: targetTable._id,
        number: targetTable.number,
        customerName: customer?.name // إضافة اسم العميل إذا متوفر
      },
      delivery,
      payment: payment || { method: 'cash', status: 'pending' },
      staff: {
        waiter: assignedWaiterId
      }
    };

    console.log('📦 بيانات الطلب النهائية قبل إنشاء كائن الطلب:', JSON.stringify(orderPayload, null, 2));

    const newOrder = new Order(orderPayload);

    console.log('💾 محاولة حفظ الطلب الجديد في قاعدة البيانات...');
    await newOrder.save();
    console.log('✅ تم حفظ الطلب الجديد بنجاح في قاعدة البيانات. معرّف الطلب:', newOrder._id);

    // تحديث حالة الطاولة بعد حفظ الطلب بنجاح
    if (tableWasNewlyOccupiedByThisRequest) {
      console.log(`⏳ تحديث حالة الطاولة ${targetTable.number} إلى مشغولة وربطها بالطلب ${newOrder._id}`);
      await targetTable.occupy(newOrder._id, assignedWaiterId);
      console.log(`✅ تم تحديث حالة الطاولة ${targetTable.number} بنجاح.`);
    } else if (targetTable.status === 'occupied' && targetTable.assignedWaiter && targetTable.assignedWaiter.toString() === assignedWaiterId) {
      // إذا كانت الطاولة مشغولة بالفعل بنفس النادل، نحدث الطلب الحالي وتاريخ آخر استخدام
      targetTable.currentOrder = newOrder._id;
      targetTable.stats.lastUsed = new Date();
      await targetTable.save();
      console.log(`🔄 تم تحديث الطلب الحالي للطاولة ${targetTable.number} إلى ${newOrder._id}.`);
    }
    
    // جلب الطلب مع البيانات المملوءة
    const populatedOrder = await Order.findById(newOrder._id)
      .populate('staff.waiter', 'name username')
      .populate('items.product', 'name price');

    console.log('🎉 تم إنشاء الطلب بنجاح (بعد التعبئة):', populatedOrder.orderNumber);

    // Send Socket notifications for new order
    if (global.socketHandlers) {
      try {
        // Get waiter name for notification
        const waiterUser = await User.findById(assignedWaiterId); // assignedWaiterId is already defined
        const waiterDisplayName = waiterUser ? (waiterUser.name || waiterUser.username) : 'نادل';

        // Emit order created event
        global.socketHandlers.io.emit('order-created', {
          orderId: populatedOrder._id,
          orderNumber: populatedOrder.orderNumber,
          tableNumber: populatedOrder.table?.number || targetTable.number || 'غير محدد', // استخدام رقم الطاولة من الطلب أو الطاولة المستهدفة
          waiterName: waiterDisplayName,
          items: populatedOrder.items,
          status: 'pending',
          customer: populatedOrder.customer,
          total: populatedOrder.totals.total,
          message: `طلب جديد من الطاولة رقم ${populatedOrder.table?.number || targetTable.number || 'غير محدد'} للعميل ${populatedOrder.customer?.name || 'غير محدد'}` ,
          timestamp: new Date().toISOString()
        });

        console.log(`📡 تم إرسال إشعار الطلب الجديد عبر Socket.IO`);
        
        // إشعار المدير والشيف
        const notificationDataToRoles = {
            orderId: populatedOrder._id,
            orderNumber: populatedOrder.orderNumber,
            tableNumber: populatedOrder.table?.number || targetTable.number || 'غير محدد',
            waiterName: waiterDisplayName,
            customerName: populatedOrder.customer?.name || 'غير محدد',
            total: populatedOrder.totals.total,
            timestamp: new Date().toISOString()
        };
        global.socketHandlers.sendRoleNotification('chef', `طلب جديد رقم ${populatedOrder.orderNumber} من النادل ${waiterDisplayName}`, { type: 'new-order-to-chef', ...notificationDataToRoles });
        global.socketHandlers.sendRoleNotification('manager', `طلب جديد رقم ${populatedOrder.orderNumber} من النادل ${waiterDisplayName}`, { type: 'new-order-to-manager', ...notificationDataToRoles });
        console.log('📡 تم إرسال إشعارات الطلب الجديد إلى الشيف والمدير.');

      } catch (socketError) {
        console.error('خطأ في إرسال إشعار Socket:', socketError);
      }
    }

    res.status(201).json({
      success: true,
      message: 'تم إنشاء الطلب بنجاح',
      data: populatedOrder
    });
  } catch (error) {
    console.error('❌❌❌ خطأ فادح في إنشاء الطلب (POST /api/orders):', error);
    res.status(500).json({
      success: false,
      message: 'خطأ في الخادم أثناء إنشاء الطلب.',
      error: error.message,
      stack: process.env.NODE_ENV === 'development' ? error.stack : undefined
    });
  }
});

// Update order status
router.put('/:id', authenticateToken, async (req, res) => {
  try {
    const { status } = req.body;

    if (!status) {
      return res.status(400).json({
        success: false,
        message: 'حالة الطلب مطلوبة'
      });
    }

    const order = await Order.findById(req.params.id);

    if (!order) {
      return res.status(404).json({
        success: false,
        message: 'الطلب غير موجود'
      });
    }

    await order.updateStatus(status, req.user._id);

    const updatedOrder = await Order.findById(order._id)
      .populate('staff.waiter', 'name username')
      .populate('staff.chef', 'name username')
      .populate('items.product', 'name price');

    // Send Socket notifications for status update
    if (global.socketHandlers) {
      try {
        const chefUser = await User.findById(req.user._id);
        const chefDisplayName = chefUser ? (chefUser.name || chefUser.username) : 'طباخ';

        // Emit order status update event
        global.socketHandlers.io.emit('order-status-update', {
          orderId: updatedOrder._id,
          orderNumber: updatedOrder.orderNumber,
          newStatus: status,
          chefName: chefDisplayName,
          tableNumber: updatedOrder.table?.number || 'غير محدد',
          customer: updatedOrder.customer,
          items: updatedOrder.items,
          message: status === 'preparing' 
            ? `بدأ تحضير الطلب من الطاولة رقم ${updatedOrder.table?.number || 'غير محدد'} للعميل ${updatedOrder.customer?.name || 'غير محدد'}`
            : status === 'ready'
            ? `الطلب جاهز للتقديم من الطاولة رقم ${updatedOrder.table?.number || 'غير محدد'} للعميل ${updatedOrder.customer?.name || 'غير محدد'}`
            : status === 'served'
            ? `تم تقديم الطلب من الطاولة رقم ${updatedOrder.table?.number || 'غير محدد'} للعميل ${updatedOrder.customer?.name || 'غير محدد'}`
            : `تم تحديث حالة الطلب من الطاولة رقم ${updatedOrder.table?.number || 'غير محدد'} للعميل ${updatedOrder.customer?.name || 'غير محدد'}`,
          timestamp: new Date().toISOString()
        });

        console.log(`📡 تم إرسال إشعار تحديث حالة الطلب ${updatedOrder.orderNumber} إلى ${status}`);
      } catch (socketError) {
        console.error('خطأ في إرسال إشعار Socket:', socketError);
      }
    }

    // تحويل البيانات للتوافق مع Frontend
    const mappedOrder = responseMapping.mapOrderForFrontend(updatedOrder);
    res.json({
      success: true,
      message: 'تم تحديث حالة الطلب بنجاح',
      data: {
        ...updatedOrder.toObject(),
        ...mappedOrder
      }
    });
  } catch (error) {
    console.error('Update order status error:', error);
    res.status(500).json({
      success: false,
      message: 'خطأ في تحديث حالة الطلب'
    });
  }
});

// Reset all orders - للمدير فقط (يجب أن يكون قبل /:id)
router.delete('/reset-all', authenticateToken, requireManager, async (req, res) => {
  try {
    console.log('🔄 طلب إعادة تهيئة جميع الطلبات من المدير:', req.user?.username);
    console.log('🗄️ محاولة الاتصال بقاعدة البيانات لحذف الطلبات...');

    // التحقق من اتصال قاعدة البيانات
    const mongoose = require('mongoose');
    if (mongoose.connection.readyState !== 1) {
      console.log('❌ قاعدة البيانات غير متصلة، المحاولة إعادة الاتصال...');
      await mongoose.connect(process.env.MONGODB_URI);
    }

    // حذف جميع الطلبات مع التحقق من وجود الطلبات أولاً
    const ordersCount = await Order.countDocuments({});
    console.log(`📊 عدد الطلبات الحالية: ${ordersCount}`);

    const deleteResult = await Order.deleteMany({});
    console.log('✅ تم حذف الطلبات:', deleteResult.deletedCount);

    // إرسال إشعار عبر Socket للجميع
    try {
      if (global.socketHandlers && global.socketHandlers.io) {
        global.socketHandlers.io.emit('orders-reset', {
          message: 'تم إعادة تهيئة جميع الطلبات',
          deletedCount: deleteResult.deletedCount,
          timestamp: new Date().toISOString()
        });
        console.log('📡 تم إرسال إشعار Socket لإعادة تهيئة الطلبات');
      }
    } catch (socketError) {
      console.warn('⚠️ خطأ في إرسال إشعار Socket (لا يؤثر على العملية):', socketError.message);
    }

    res.json({
      success: true,
      message: `تم حذف ${deleteResult.deletedCount} طلب بنجاح`,
      deletedCount: deleteResult.deletedCount,
      timestamp: new Date().toISOString()
    });

  } catch (error) {
    console.error('❌ خطأ في إعادة تهيئة الطلبات:', error);
    console.error('❌ تفاصيل الخطأ:', error.stack);
    
    // إرسال استجابة مفصلة للخطأ
    res.status(500).json({
      success: false,
      message: 'خطأ في الخادم أثناء إعادة تهيئة الطلبات',
      error: error.message,
      errorType: error.name,
      timestamp: new Date().toISOString(),
      details: process.env.NODE_ENV === 'development' ? error.stack : undefined
    });
  }
});

// Delete order (cancel if pending)
router.delete('/:id', authenticateToken, async (req, res) => {
  try {
    const orderId = req.params.id;
    const order = await Order.findById(orderId).populate('staff.waiter', '_id username name');

    if (!order) {
      return res.status(404).json({
        success: false,
        message: 'الطلب غير موجود'
      });
    }

    // Check if the user is authorized to cancel (creator or manager)
    const userId = req.user._id.toString();
    const orderWaiterId = order.staff.waiter?._id.toString();
    const userRole = req.user.role;

    if (userRole !== 'manager' && userId !== orderWaiterId) {
      console.warn(`🚫 محاولة إلغاء غير مصرح بها للطلب ${order.orderNumber} بواسطة المستخدم ${req.user.username} (ID: ${userId}). النادل المسؤول: ${orderWaiterId}`);
      return res.status(403).json({
        success: false,
        message: 'غير مصرح لك بإلغاء هذا الطلب.'
      });
    }

    if (order.status !== 'pending') {
      console.warn(`⚠️ لا يمكن إلغاء الطلب ${order.orderNumber} لأنه ليس في حالة "pending". الحالة الحالية: ${order.status}`);
      return res.status(400).json({
        success: false,
        message: `لا يمكن إلغاء الطلب لأنه قيد المعالجة أو مكتمل (الحالة: ${order.status}).`
      });
    }

    // If all checks pass, delete the order
    await Order.findByIdAndDelete(orderId);
    console.log(`🗑️ تم حذف الطلب ${order.orderNumber} (ID: ${orderId}) بنجاح بواسطة ${req.user.username}.`);

    // Also, if this order was associated with a table, we might need to update the table.
    // For simplicity now, we are just deleting the order. If a table holds a reference
    // to currentOrder, that reference might become stale. This could be enhanced.
    // Example: if order.table.tableId is present, find the table and potentially update its currentOrder or orders array.

    // Send Socket notification for order cancellation
    if (global.socketHandlers) {
      try {
        const waiterDisplayName = order.staff.waiter ? (order.staff.waiter.name || order.staff.waiter.username) : 'نادل';
        const notificationPayload = {
          orderId: order._id,
          orderNumber: order.orderNumber,
          waiterName: waiterDisplayName,
          tableNumber: order.table?.number || 'غير محدد',
          message: `تم إلغاء الطلب رقم ${order.orderNumber} من الطاولة ${order.table?.number || 'غير محدد'}.`,
          timestamp: new Date().toISOString()
        };
        
        global.socketHandlers.io.emit('order-cancelled', notificationPayload);
        console.log(`📡 تم إرسال إشعار إلغاء الطلب ${order.orderNumber} عبر Socket.IO`);        // Notify relevant roles (e.g., manager, original waiter if cancelled by manager)
        global.socketHandlers.sendRoleNotification('manager', `تم إلغاء الطلب ${order.orderNumber}`, { type: 'order-cancelled-to-manager', ...notificationPayload });
        if (order.staff.waiter && order.staff.waiter._id) {
          // Notify the specific waiter if they didn't initiate the cancellation
          // This requires knowing the socket ID of that specific waiter.
          // For now, a general event or manager notification is simpler.
        }
        if (global.socketHandlers && order.staff.chef) {
          // If a chef was assigned (though unlikely for a pending order, but good practice)
          // global.socketHandlers.sendUserNotification(order.staff.chef.toString(), `Order ${order.orderNumber} was cancelled.`);
        }


      } catch (socketError) {
        console.error('خطأ في إرسال إشعار Socket لإلغاء الطلب:', socketError);
      }
    }

    res.json({
      success: true,
      message: 'تم إلغاء الطلب بنجاح'
    });
  } catch (error) {
    console.error('❌ خطأ في إلغاء الطلب (DELETE /api/orders/:id):', error);
    res.status(500).json({
      success: false,
      message: 'خطأ في الخادم أثناء إلغاء الطلب.',
      error: error.message,
      stack: process.env.NODE_ENV === 'development' ? error.stack : undefined
    });  }
});

module.exports = router;

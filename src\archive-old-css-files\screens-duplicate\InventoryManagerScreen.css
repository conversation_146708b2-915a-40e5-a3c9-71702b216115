/* ====================================
   INVENTORY MANAGER SCREEN - ENHANCED DESIGN
   شاشة مخزون المدير - تصميم محسن
   ==================================== */

/* Container */
.inventory-screen {
  padding: 2.5rem;
  background: linear-gradient(135deg, #f0f4f8 0%, #e2e8f0 100%);
  min-height: 100vh;
  direction: rtl;
  font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
}

/* Header */
.inventory-header {
  margin-bottom: 3rem;
  text-align: center;
}

.inventory-header h1 {
  font-size: 2.5rem;
  font-weight: 800;
  color: #1e293b;
  margin: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 1rem;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.inventory-header h1 i {
  color: #3b82f6;
  text-shadow: 0 0 20px rgba(59, 130, 246, 0.3);
}

/* Loading State */
.loading-spinner {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 4rem;
  color: #64748b;
  font-size: 1.2rem;
  gap: 1rem;
}

.loading-spinner i {
  font-size: 2rem;
  color: #3b82f6;
}

/* Enhanced Inventory Grid - Optimized for Square Cards */
.inventory-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(320px, 1fr));
  gap: 2rem;
  max-width: 1800px;
  margin: 0 auto;
}

/* Enhanced Inventory Item Cards - Better Content Distribution */
.inventory-item,
.inventory-card-enhanced {
  background: linear-gradient(145deg, #ffffff 0%, #f8fafc 100%) !important;
  border-radius: 20px !important;
  padding: 1rem !important;
  box-shadow: 
    0 12px 40px rgba(0, 0, 0, 0.08),
    0 4px 12px rgba(0, 0, 0, 0.04),
    inset 0 1px 0 rgba(255, 255, 255, 0.8) !important;
  border: 1px solid rgba(226, 232, 240, 0.8) !important;
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1) !important;
  position: relative;
  overflow: hidden;
  /* Square aspect ratio with better height management */
  aspect-ratio: 1 / 1 !important;
  min-height: 320px !important;
  max-height: 380px !important;
  min-width: 320px !important;
  display: flex !important;
  flex-direction: column !important;
}

.inventory-item::before,
.inventory-card-enhanced::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 6px;
  background: linear-gradient(90deg, #3b82f6, #10b981, #f59e0b);
  transition: all 0.3s ease;
  border-radius: 24px 24px 0 0;
}

.inventory-item::after,
.inventory-card-enhanced::after {
  content: '';
  position: absolute;
  top: -50%;
  right: -50%;
  width: 100%;
  height: 100%;
  background: radial-gradient(circle, rgba(59, 130, 246, 0.05) 0%, transparent 70%);
  opacity: 0;
  transition: opacity 0.3s ease;
}

.inventory-item:hover,
.inventory-card-enhanced:hover {
  transform: translateY(-8px) scale(1.02) !important;
  box-shadow: 
    0 20px 60px rgba(0, 0, 0, 0.12),
    0 8px 20px rgba(0, 0, 0, 0.08),
    inset 0 1px 0 rgba(255, 255, 255, 0.9) !important;
}

.inventory-item:hover::before,
.inventory-card-enhanced:hover::before {
  height: 8px;
  box-shadow: 0 0 20px rgba(59, 130, 246, 0.4);
}

.inventory-item:hover::after,
.inventory-card-enhanced:hover::after {
  opacity: 1;
}

.inventory-item.unavailable,
.inventory-card-enhanced.inventory-unavailable {
  opacity: 0.7;
  filter: grayscale(30%);
}

.inventory-item.unavailable::before,
.inventory-card-enhanced.inventory-unavailable::before {
  background: linear-gradient(90deg, #ef4444, #dc2626);
}

/* Enhanced Item Header */
.item-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 2rem;
  padding-bottom: 1rem;
  border-bottom: 2px solid #f1f5f9;
}

.item-header h3 {
  font-size: 1.6rem;
  font-weight: 700;
  color: #1e293b;
  margin: 0;
  line-height: 1.2;
  letter-spacing: -0.5px;
}

.availability {
  padding: 0.75rem 1.5rem;
  border-radius: 20px;
  font-size: 0.9rem;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  border: 2px solid transparent;
  transition: all 0.3s ease;
}

.availability.available {
  background: linear-gradient(135deg, #10b981, #059669);
  color: white;
  box-shadow: 0 4px 15px rgba(16, 185, 129, 0.3);
}

.availability.unavailable {
  background: linear-gradient(135deg, #ef4444, #dc2626);
  color: white;
  box-shadow: 0 4px 15px rgba(239, 68, 68, 0.3);
}

/* Enhanced Item Details */
.item-details {
  margin-bottom: 2rem;
}

.price {
  font-size: 2rem;
  font-weight: 800;
  color: #059669;
  margin-bottom: 1rem;
  text-shadow: 0 2px 4px rgba(5, 150, 105, 0.2);
  background: linear-gradient(135deg, #10b981, #059669);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.stock {
  font-size: 1.3rem;
  font-weight: 600;
  color: #475569;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 1rem;
  background: linear-gradient(135deg, #f8fafc, #e2e8f0);
  border-radius: 12px;
  border: 1px solid #e2e8f0;
}

.stock.low {
  color: #dc2626;
  background: linear-gradient(135deg, #fef2f2, #fee2e2);
  border-color: #fecaca;
}

.stock.low i {
  color: #ef4444;
  animation: warning-pulse 1.5s infinite;
}

/* Enhanced Stock Controls */
.stock-controls {
  background: linear-gradient(135deg, #f1f5f9, #e2e8f0);
  border-radius: 16px;
  padding: 1.5rem;
  border: 1px solid #e2e8f0;
  margin-top: 1rem;
}

.stock-controls-label {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-weight: 600;
  color: #475569;
  margin-bottom: 1rem;
  font-size: 1rem;
}

.stock-controls-label i {
  color: #3b82f6;
}

.stock-buttons {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 0.75rem;
}

.stock-btn {
  padding: 1rem;
  border: none;
  border-radius: 12px;
  font-size: 1rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: 50px;
  position: relative;
  overflow: hidden;
}

.stock-btn::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
  transition: left 0.5s;
}

.stock-btn:hover::before {
  left: 100%;
}

.stock-btn.decrease {
  background: linear-gradient(135deg, #ef4444, #dc2626);
  color: white;
  box-shadow: 0 4px 15px rgba(239, 68, 68, 0.3);
}

.stock-btn.decrease:hover {
  background: linear-gradient(135deg, #dc2626, #b91c1c);
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(239, 68, 68, 0.4);
}

.stock-btn.decrease-five {
  background: linear-gradient(135deg, #f59e0b, #d97706);
  color: white;
  box-shadow: 0 4px 15px rgba(245, 158, 11, 0.3);
}

.stock-btn.decrease-five:hover {
  background: linear-gradient(135deg, #d97706, #b45309);
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(245, 158, 11, 0.4);
}

.stock-btn.increase-five {
  background: linear-gradient(135deg, #3b82f6, #2563eb);
  color: white;
  box-shadow: 0 4px 15px rgba(59, 130, 246, 0.3);
}

.stock-btn.increase-five:hover {
  background: linear-gradient(135deg, #2563eb, #1d4ed8);
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(59, 130, 246, 0.4);
}

.stock-btn.increase {
  background: linear-gradient(135deg, #10b981, #059669);
  color: white;
  box-shadow: 0 4px 15px rgba(16, 185, 129, 0.3);
}

.stock-btn.increase:hover {
  background: linear-gradient(135deg, #059669, #047857);
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(16, 185, 129, 0.4);
}

.stock-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
  transform: none !important;
  box-shadow: none !important;
}

.stock-btn:active {
  transform: translateY(0) scale(0.98);
}

/* Item Description */
.item-description {
  margin-top: 1.5rem;
  padding: 1rem;
  background: linear-gradient(135deg, #f8fafc, #e2e8f0);
  border-radius: 12px;
  color: #64748b;
  font-size: 0.95rem;
  line-height: 1.5;
  border: 1px solid #e2e8f0;
}

/* Empty State */
.empty-state {
  text-align: center;
  padding: 4rem;
  color: #64748b;
  grid-column: 1 / -1;
}

.empty-icon {
  font-size: 4rem;
  color: #cbd5e1;
  margin-bottom: 1.5rem;
}

.empty-state h3 {
  font-size: 1.5rem;
  color: #475569;
  margin: 0 0 1rem 0;
}

.empty-state p {
  font-size: 1.1rem;
  margin: 0;
}

/* ====================================
   BOOTSTRAP COMPATIBILITY - INVENTORY ENHANCED
   توافق Bootstrap - مخزون محسن  
   ==================================== */

/* Inventory Bootstrap Container */
.inventory-bootstrap-container {
  background: linear-gradient(135deg, #f0f4f8 0%, #e2e8f0 100%) !important;
  min-height: 100vh;
  direction: rtl;
  font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif !important;
}

/* New Bootstrap Enhanced Card Styles */
.inventory-card-enhanced {
  background: linear-gradient(145deg, #ffffff 0%, #f8fafc 100%) !important;
  border: none !important;
  border-radius: 20px !important;
  box-shadow: 
    0 12px 40px rgba(0, 0, 0, 0.08),
    0 4px 12px rgba(0, 0, 0, 0.04) !important;
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1) !important;
  position: relative;
  overflow: hidden;
  /* Enhanced Square Design */
  aspect-ratio: 1 / 1 !important;
  min-height: 320px !important;
  max-height: 380px !important;
  min-width: 320px !important;
  display: flex !important;
  flex-direction: column !important;
}

/* Content Distribution Fix */
.inventory-card-enhanced {
  /* Force proper height distribution */
  display: grid !important;
  grid-template-rows: auto 1fr auto !important;
  gap: 0.5rem !important;
}

.inventory-card-enhanced .card-body {
  display: grid !important;
  grid-template-rows: auto 1fr auto !important;
  gap: 0.5rem !important;
  height: 100% !important;
}

/* Status Bar */
.inventory-status-bar {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 6px;
  transition: all 0.3s ease;
  border-radius: 20px 20px 0 0;
}

.inventory-status-bar.available {
  background: linear-gradient(90deg, #10b981, #059669);
}

.inventory-status-bar.unavailable {
  background: linear-gradient(90deg, #ef4444, #dc2626);
}

/* Card Header - Compact Design */
.inventory-card-header {
  display: flex !important;
  flex-direction: column !important;
  align-items: center !important;
  text-align: center !important;
  margin-bottom: 0.75rem !important;
  flex-shrink: 0 !important;
  height: auto !important;
  max-height: 90px !important;
  grid-row: 1 !important;
  overflow: visible !important;
}

.inventory-item-icon {
  width: 40px;
  height: 40px;
  background: linear-gradient(135deg, #3b82f6, #2563eb);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 0.5rem;
  box-shadow: 0 4px 12px rgba(59, 130, 246, 0.3);
  flex-shrink: 0;
}

.inventory-item-icon i {
  font-size: 1.2rem;
  color: white;
}

.inventory-item-name {
  font-size: 1rem !important;
  font-weight: 700 !important;
  color: #1e293b !important;
  margin: 0 0 0.25rem 0 !important;
  text-align: center !important;
  line-height: 1.1 !important;
  max-height: 2.2rem !important;
  overflow: hidden !important;
  text-overflow: ellipsis !important;
  display: -webkit-box !important;
  -webkit-line-clamp: 2 !important;
  line-clamp: 2 !important;
  -webkit-box-orient: vertical !important;
}

.inventory-status-badge {
  padding: 0.25rem 0.6rem;
  border-radius: 12px;
  font-size: 0.6rem;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  border: 2px solid transparent;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
}

.inventory-status-badge.available {
  background: linear-gradient(135deg, #10b981, #059669);
  color: white;
  box-shadow: 0 4px 15px rgba(16, 185, 129, 0.3);
}

.inventory-status-badge.unavailable {
  background: linear-gradient(135deg, #ef4444, #dc2626);
  color: white;
  box-shadow: 0 4px 15px rgba(239, 68, 68, 0.3);
}

/* Details Section - Compact Layout */
.inventory-details-section {
  flex: 1 !important;
  display: flex !important;
  flex-direction: column !important;
  gap: 0.5rem !important;
  margin-bottom: 0.75rem !important;
  overflow: hidden !important;
  grid-row: 2 !important;
  overflow-y: auto !important;
  overflow-x: hidden !important;
}

.inventory-detail-item {
  display: flex !important;
  align-items: center !important;
  gap: 0.75rem !important;
  padding: 0.5rem !important;
  background: linear-gradient(135deg, #f8fafc, #e2e8f0) !important;
  border-radius: 8px !important;
  border: 1px solid #e2e8f0 !important;
  transition: all 0.3s ease !important;
  min-height: 45px !important;
  max-height: 50px !important;
}

.inventory-detail-item:hover {
  background: linear-gradient(135deg, #e2e8f0, #cbd5e1) !important;
}

.detail-icon {
  width: 28px;
  height: 28px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
}

.detail-icon i {
  font-size: 0.75rem;
}

.detail-icon.price {
  background: linear-gradient(135deg, #10b981, #059669);
  color: white;
}

.detail-icon.stock {
  background: linear-gradient(135deg, #3b82f6, #2563eb);
  color: white;
}

.detail-icon.warning {
  background: linear-gradient(135deg, #ef4444, #dc2626);
  color: white;
  animation: warning-pulse 1.5s infinite;
}

.detail-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 0.1rem;
  overflow: hidden;
}

.detail-label {
  font-size: 0.65rem;
  color: #64748b;
  font-weight: 500;
  line-height: 1;
}

.detail-value {
  font-size: 0.8rem;
  font-weight: 700;
  color: #1e293b;
  line-height: 1.1;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.price-value {
  color: #059669 !important;
  background: linear-gradient(135deg, #10b981, #059669);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.stock-value.low {
  color: #dc2626 !important;
}

.low-stock-indicator {
  display: inline-block;
  padding: 0.15rem 0.4rem;
  background: #fef2f2;
  color: #dc2626;
  border-radius: 6px;
  font-size: 0.55rem;
  margin-right: 0.4rem;
  border: 1px solid #fecaca;
  font-weight: 600;
}

/* Enhanced Stock Controls */
.stock-controls {
  background: linear-gradient(135deg, #f1f5f9, #e2e8f0);
  border-radius: 16px;
  padding: 1.5rem;
  border: 1px solid #e2e8f0;
  margin-top: 1rem;
  grid-row: 3 !important;
  overflow: visible !important;
}

.stock-controls-label {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-weight: 600;
  color: #475569;
  margin-bottom: 1rem;
  font-size: 1rem;
}

.stock-controls-label i {
  color: #3b82f6;
}

.stock-buttons {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 0.75rem;
}

.stock-btn {
  padding: 1rem;
  border: none;
  border-radius: 12px;
  font-size: 1rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: 50px;
  position: relative;
  overflow: hidden;
}

.stock-btn::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
  transition: left 0.5s;
}

.stock-btn:hover::before {
  left: 100%;
}

.stock-btn.decrease {
  background: linear-gradient(135deg, #ef4444, #dc2626);
  color: white;
  box-shadow: 0 4px 15px rgba(239, 68, 68, 0.3);
}

.stock-btn.decrease:hover {
  background: linear-gradient(135deg, #dc2626, #b91c1c);
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(239, 68, 68, 0.4);
}

.stock-btn.decrease-five {
  background: linear-gradient(135deg, #f59e0b, #d97706);
  color: white;
  box-shadow: 0 4px 15px rgba(245, 158, 11, 0.3);
}

.stock-btn.decrease-five:hover {
  background: linear-gradient(135deg, #d97706, #b45309);
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(245, 158, 11, 0.4);
}

.stock-btn.increase-five {
  background: linear-gradient(135deg, #3b82f6, #2563eb);
  color: white;
  box-shadow: 0 4px 15px rgba(59, 130, 246, 0.3);
}

.stock-btn.increase-five:hover {
  background: linear-gradient(135deg, #2563eb, #1d4ed8);
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(59, 130, 246, 0.4);
}

.stock-btn.increase {
  background: linear-gradient(135deg, #10b981, #059669);
  color: white;
  box-shadow: 0 4px 15px rgba(16, 185, 129, 0.3);
}

.stock-btn.increase:hover {
  background: linear-gradient(135deg, #059669, #047857);
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(16, 185, 129, 0.4);
}

.stock-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
  transform: none !important;
  box-shadow: none !important;
}

.stock-btn:active {
  transform: translateY(0) scale(0.98);
}

/* Item Description */
.item-description {
  margin-top: 1.5rem;
  padding: 1rem;
  background: linear-gradient(135deg, #f8fafc, #e2e8f0);
  border-radius: 12px;
  color: #64748b;
  font-size: 0.95rem;
  line-height: 1.5;
  border: 1px solid #e2e8f0;
}

/* Empty State */
.empty-state {
  text-align: center;
  padding: 4rem;
  color: #64748b;
  grid-column: 1 / -1;
}

.empty-icon {
  font-size: 4rem;
  color: #cbd5e1;
  margin-bottom: 1.5rem;
}

.empty-state h3 {
  font-size: 1.5rem;
  color: #475569;
  margin: 0 0 1rem 0;
}

.empty-state p {
  font-size: 1.1rem;
  margin: 0;
}

/* ====================================
   BOOTSTRAP COMPATIBILITY - INVENTORY ENHANCED
   توافق Bootstrap - مخزون محسن  
   ==================================== */

/* Inventory Bootstrap Container */
.inventory-bootstrap-container {
  background: linear-gradient(135deg, #f0f4f8 0%, #e2e8f0 100%) !important;
  min-height: 100vh;
  direction: rtl;
  font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif !important;
}

/* New Bootstrap Enhanced Card Styles */
.inventory-card-enhanced {
  background: linear-gradient(145deg, #ffffff 0%, #f8fafc 100%) !important;
  border: none !important;
  border-radius: 20px !important;
  box-shadow: 
    0 12px 40px rgba(0, 0, 0, 0.08),
    0 4px 12px rgba(0, 0, 0, 0.04) !important;
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1) !important;
  position: relative;
  overflow: hidden;
  /* Enhanced Square Design */
  aspect-ratio: 1 / 1 !important;
  min-height: 320px !important;
  max-height: 380px !important;
  min-width: 320px !important;
  display: flex !important;
  flex-direction: column !important;
}

/* Content Distribution Fix */
.inventory-card-enhanced {
  /* Force proper height distribution */
  display: grid !important;
  grid-template-rows: auto 1fr auto !important;
  gap: 0.5rem !important;
}

.inventory-card-enhanced .card-body {
  display: grid !important;
  grid-template-rows: auto 1fr auto !important;
  gap: 0.5rem !important;
  height: 100% !important;
}

/* Status Bar */
.inventory-status-bar {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 6px;
  transition: all 0.3s ease;
  border-radius: 20px 20px 0 0;
}

.inventory-status-bar.available {
  background: linear-gradient(90deg, #10b981, #059669);
}

.inventory-status-bar.unavailable {
  background: linear-gradient(90deg, #ef4444, #dc2626);
}

/* Card Header - Compact Design */
.inventory-card-header {
  display: flex !important;
  flex-direction: column !important;
  align-items: center !important;
  text-align: center !important;
  margin-bottom: 0.75rem !important;
  flex-shrink: 0 !important;
  height: auto !important;
  max-height: 90px !important;
  grid-row: 1 !important;
  overflow: visible !important;
}

.inventory-item-icon {
  width: 40px;
  height: 40px;
  background: linear-gradient(135deg, #3b82f6, #2563eb);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 0.5rem;
  box-shadow: 0 4px 12px rgba(59, 130, 246, 0.3);
  flex-shrink: 0;
}

.inventory-item-icon i {
  font-size: 1.2rem;
  color: white;
}

.inventory-item-name {
  font-size: 1rem !important;
  font-weight: 700 !important;
  color: #1e293b !important;
  margin: 0 0 0.25rem 0 !important;
  text-align: center !important;
  line-height: 1.1 !important;
  max-height: 2.2rem !important;
  overflow: hidden !important;
  text-overflow: ellipsis !important;
  display: -webkit-box !important;
  -webkit-line-clamp: 2 !important;
  line-clamp: 2 !important;
  -webkit-box-orient: vertical !important;
}

.inventory-status-badge {
  padding: 0.25rem 0.6rem;
  border-radius: 12px;
  font-size: 0.6rem;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  border: 2px solid transparent;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
}

.inventory-status-badge.available {
  background: linear-gradient(135deg, #10b981, #059669);
  color: white;
  box-shadow: 0 4px 15px rgba(16, 185, 129, 0.3);
}

.inventory-status-badge.unavailable {
  background: linear-gradient(135deg, #ef4444, #dc2626);
  color: white;
  box-shadow: 0 4px 15px rgba(239, 68, 68, 0.3);
}

/* Details Section - Compact Layout */
.inventory-details-section {
  flex: 1 !important;
  display: flex !important;
  flex-direction: column !important;
  gap: 0.5rem !important;
  margin-bottom: 0.75rem !important;
  overflow: hidden !important;
  grid-row: 2 !important;
  overflow-y: auto !important;
  overflow-x: hidden !important;
}

.inventory-detail-item {
  display: flex !important;
  align-items: center !important;
  gap: 0.75rem !important;
  padding: 0.5rem !important;
  background: linear-gradient(135deg, #f8fafc, #e2e8f0) !important;
  border-radius: 8px !important;
  border: 1px solid #e2e8f0 !important;
  transition: all 0.3s ease !important;
  min-height: 45px !important;
  max-height: 50px !important;
}

.inventory-detail-item:hover {
  background: linear-gradient(135deg, #e2e8f0, #cbd5e1) !important;
}

.detail-icon {
  width: 28px;
  height: 28px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
}

.detail-icon i {
  font-size: 0.75rem;
}

.detail-icon.price {
  background: linear-gradient(135deg, #10b981, #059669);
  color: white;
}

.detail-icon.stock {
  background: linear-gradient(135deg, #3b82f6, #2563eb);
  color: white;
}

.detail-icon.warning {
  background: linear-gradient(135deg, #ef4444, #dc2626);
  color: white;
  animation: warning-pulse 1.5s infinite;
}

.detail-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 0.1rem;
  overflow: hidden;
}

.detail-label {
  font-size: 0.65rem;
  color: #64748b;
  font-weight: 500;
  line-height: 1;
}

.detail-value {
  font-size: 0.8rem;
  font-weight: 700;
  color: #1e293b;
  line-height: 1.1;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.price-value {
  color: #059669 !important;
  background: linear-gradient(135deg, #10b981, #059669);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.stock-value.low {
  color: #dc2626 !important;
}

.low-stock-indicator {
  display: inline-block;
  padding: 0.15rem 0.4rem;
  background: #fef2f2;
  color: #dc2626;
  border-radius: 6px;
  font-size: 0.55rem;
  margin-right: 0.4rem;
  border: 1px solid #fecaca;
  font-weight: 600;
}

/* Enhanced Stock Controls */
.stock-controls {
  background: linear-gradient(135deg, #f1f5f9, #e2e8f0);
  border-radius: 16px;
  padding: 1.5rem;
  border: 1px solid #e2e8f0;
  margin-top: 1rem;
  grid-row: 3 !important;
  overflow: visible !important;
}

.stock-controls-label {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-weight: 600;
  color: #475569;
  margin-bottom: 1rem;
  font-size: 1rem;
}

.stock-controls-label i {
  color: #3b82f6;
}

.stock-buttons {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 0.75rem;
}

.stock-btn {
  padding: 1rem;
  border: none;
  border-radius: 12px;
  font-size: 1rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: 50px;
  position: relative;
  overflow: hidden;
}

.stock-btn::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
  transition: left 0.5s;
}

.stock-btn:hover::before {
  left: 100%;
}

.stock-btn.decrease {
  background: linear-gradient(135deg, #ef4444, #dc2626);
  color: white;
  box-shadow: 0 4px 15px rgba(239, 68, 68, 0.3);
}

.stock-btn.decrease:hover {
  background: linear-gradient(135deg, #dc2626, #b91c1c);
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(239, 68, 68, 0.4);
}

.stock-btn.decrease-five {
  background: linear-gradient(135deg, #f59e0b, #d97706);
  color: white;
  box-shadow: 0 4px 15px rgba(245, 158, 11, 0.3);
}

.stock-btn.decrease-five:hover {
  background: linear-gradient(135deg, #d97706, #b45309);
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(245, 158, 11, 0.4);
}

.stock-btn.increase-five {
  background: linear-gradient(135deg, #3b82f6, #2563eb);
  color: white;
  box-shadow: 0 4px 15px rgba(59, 130, 246, 0.3);
}

.stock-btn.increase-five:hover {
  background: linear-gradient(135deg, #2563eb, #1d4ed8);
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(59, 130, 246, 0.4);
}

.stock-btn.increase {
  background: linear-gradient(135deg, #10b981, #059669);
  color: white;
  box-shadow: 0 4px 15px rgba(16, 185, 129, 0.3);
}

.stock-btn.increase:hover {
  background: linear-gradient(135deg, #059669, #047857);
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(16, 185, 129, 0.4);
}

.stock-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
  transform: none !important;
  box-shadow: none !important;
}

.stock-btn:active {
  transform: translateY(0) scale(0.98);
}

/* Item Description */
.item-description {
  margin-top: 1.5rem;
  padding: 1rem;
  background: linear-gradient(135deg, #f8fafc, #e2e8f0);
  border-radius: 12px;
  color: #64748b;
  font-size: 0.95rem;
  line-height: 1.5;
  border: 1px solid #e2e8f0;
}

/* Empty State */
.empty-state {
  text-align: center;
  padding: 4rem;
  color: #64748b;
  grid-column: 1 / -1;
}

.empty-icon {
  font-size: 4rem;
  color: #cbd5e1;
  margin-bottom: 1.5rem;
}

.empty-state h3 {
  font-size: 1.5rem;
  color: #475569;
  margin: 0 0 1rem 0;
}

.empty-state p {
  font-size: 1.1rem;
  margin: 0;
}

/* ====================================
   BOOTSTRAP COMPATIBILITY - INVENTORY ENHANCED
   توافق Bootstrap - مخزون محسن  
   ==================================== */

/* Inventory Bootstrap Container */
.inventory-bootstrap-container {
  background: linear-gradient(135deg, #f0f4f8 0%, #e2e8f0 100%) !important;
  min-height: 100vh;
  direction: rtl;
  font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif !important;
}

/* New Bootstrap Enhanced Card Styles */
.inventory-card-enhanced {
  background: linear-gradient(145deg, #ffffff 0%, #f8fafc 100%) !important;
  border: none !important;
  border-radius: 20px !important;
  box-shadow: 
    0 12px 40px rgba(0, 0, 0, 0.08),
    0 4px 12px rgba(0, 0, 0, 0.04) !important;
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1) !important;
  position: relative;
  overflow: hidden;
  /* Enhanced Square Design */
  aspect-ratio: 1 / 1 !important;
  min-height: 320px !important;
  max-height: 380px !important;
  min-width: 320px !important;
  display: flex !important;
  flex-direction: column !important;
}

/* Content Distribution Fix */
.inventory-card-enhanced {
  /* Force proper height distribution */
  display: grid !important;
  grid-template-rows: auto 1fr auto !important;
  gap: 0.5rem !important;
}

.inventory-card-enhanced .card-body {
  display: grid !important;
  grid-template-rows: auto 1fr auto !important;
  gap: 0.5rem !important;
  height: 100% !important;
}

/* Status Bar */
.inventory-status-bar {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 6px;
  transition: all 0.3s ease;
  border-radius: 20px 20px 0 0;
}

.inventory-status-bar.available {
  background: linear-gradient(90deg, #10b981, #059669);
}

.inventory-status-bar.unavailable {
  background: linear-gradient(90deg, #ef4444, #dc2626);
}

/* Card Header - Compact Design */
.inventory-card-header {
  display: flex !important;
  flex-direction: column !important;
  align-items: center !important;
  text-align: center !important;
  margin-bottom: 0.75rem !important;
  flex-shrink: 0 !important;
  height: auto !important;
  max-height: 90px !important;
  grid-row: 1 !important;
  overflow: visible !important;
}

.inventory-item-icon {
  width: 40px;
  height: 40px;
  background: linear-gradient(135deg, #3b82f6, #2563eb);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 0.5rem;
  box-shadow: 0 4px 12px rgba(59, 130, 246, 0.3);
  flex-shrink: 0;
}

.inventory-item-icon i {
  font-size: 1.2rem;
  color: white;
}

.inventory-item-name {
  font-size: 1rem !important;
  font-weight: 700 !important;
  color: #1e293b !important;
  margin: 0 0 0.25rem 0 !important;
  text-align: center !important;
  line-height: 1.1 !important;
  max-height: 2.2rem !important;
  overflow: hidden !important;
  text-overflow: ellipsis !important;
  display: -webkit-box !important;
  -webkit-line-clamp: 2 !important;
  line-clamp: 2 !important;
  -webkit-box-orient: vertical !important;
}

.inventory-status-badge {
  padding: 0.25rem 0.6rem;
  border-radius: 12px;
  font-size: 0.6rem;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  border: 2px solid transparent;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
}

.inventory-status-badge.available {
  background: linear-gradient(135deg, #10b981, #059669);
  color: white;
  box-shadow: 0 4px 15px rgba(16, 185, 129, 0.3);
}

.inventory-status-badge.unavailable {
  background: linear-gradient(135deg, #ef4444, #dc2626);
  color: white;
  box-shadow: 0 4px 15px rgba(239, 68, 68, 0.3);
}

/* Details Section - Compact Layout */
.inventory-details-section {
  flex: 1 !important;
  display: flex !important;
  flex-direction: column !important;
  gap: 0.5rem !important;
  margin-bottom: 0.75rem !important;
  overflow: hidden !important;
  grid-row: 2 !important;
  overflow-y: auto !important;
  overflow-x: hidden !important;
}

.inventory-detail-item {
  display: flex !important;
  align-items: center !important;
  gap: 0.75rem !important;
  padding: 0.5rem !important;
  background: linear-gradient(135deg, #f8fafc, #e2e8f0) !important;
  border-radius: 8px !important;
  border: 1px solid #e2e8f0 !important;
  transition: all 0.3s ease !important;
  min-height: 45px !important;
  max-height: 50px !important;
}

.inventory-detail-item:hover {
  background: linear-gradient(135deg, #e2e8f0, #cbd5e1) !important;
}

.detail-icon {
  width: 28px;
  height: 28px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
}

.detail-icon i {
  font-size: 0.75rem;
}

.detail-icon.price {
  background: linear-gradient(135deg, #10b981, #059669);
  color: white;
}

.detail-icon.stock {
  background: linear-gradient(135deg, #3b82f6, #2563eb);
  color: white;
}

.detail-icon.warning {
  background: linear-gradient(135deg, #ef4444, #dc2626);
  color: white;
  animation: warning-pulse 1.5s infinite;
}

.detail-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 0.1rem;
  overflow: hidden;
}

.detail-label {
  font-size: 0.65rem;
  color: #64748b;
  font-weight: 500;
  line-height: 1;
}

.detail-value {
  font-size: 0.8rem;
  font-weight: 700;
  color: #1e293b;
  line-height: 1.1;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.price-value {
  color: #059669 !important;
  background: linear-gradient(135deg, #10b981, #059669);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.stock-value.low {
  color: #dc2626 !important;
}

.low-stock-indicator {
  display: inline-block;
  padding: 0.15rem 0.4rem;
  background: #fef2f2;
  color: #dc2626;
  border-radius: 6px;
  font-size: 0.55rem;
  margin-right: 0.4rem;
  border: 1px solid #fecaca;
  font-weight: 600;
}

/* Enhanced Stock Controls */
.stock-controls {
  background: linear-gradient(135deg, #f1f5f9, #e2e8f0);
  border-radius: 16px;
  padding: 1.5rem;
  border: 1px solid #e2e8f0;
  margin-top: 1rem;
  grid-row: 3 !important;
  overflow: visible !important;
}

.stock-controls-label {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-weight: 600;
  color: #475569;
  margin-bottom: 1rem;
  font-size: 1rem;
}

.stock-controls-label i {
  color: #3b82f6;
}

.stock-buttons {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 0.75rem;
}

.stock-btn {
  padding: 1rem;
  border: none;
  border-radius: 12px;
  font-size: 1rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: 50px;
  position: relative;
  overflow: hidden;
}

.stock-btn::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
  transition: left 0.5s;
}

.stock-btn:hover::before {
  left: 100%;
}

.stock-btn.decrease {
  background: linear-gradient(135deg, #ef4444, #dc2626);
  color: white;
  box-shadow: 0 4px 15px rgba(239, 68, 68, 0.3);
}

.stock-btn.decrease:hover {
  background: linear-gradient(135deg, #dc2626, #b91c1c);
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(239, 68, 68, 0.4);
}

.stock-btn.decrease-five {
  background: linear-gradient(135deg, #f59e0b, #d97706);
  color: white;
  box-shadow: 0 4px 15px rgba(245, 158, 11, 0.3);
}

.stock-btn.decrease-five:hover {
  background: linear-gradient(135deg, #d97706, #b45309);
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(245, 158, 11, 0.4);
}

.stock-btn.increase-five {
  background: linear-gradient(135deg, #3b82f6, #2563eb);
  color: white;
  box-shadow: 0 4px 15px rgba(59, 130, 246, 0.3);
}

.stock-btn.increase-five:hover {
  background: linear-gradient(135deg, #2563eb, #1d4ed8);
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(59, 130, 246, 0.4);
}

.stock-btn.increase {
  background: linear-gradient(135deg, #10b981, #059669);
  color: white;
  box-shadow: 0 4px 15px rgba(16, 185, 129, 0.3);
}

.stock-btn.increase:hover {
  background: linear-gradient(135deg, #059669, #047857);
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(16, 185, 129, 0.4);
}

.stock-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
  transform: none !important;
  box-shadow: none !important;
}

.stock-btn:active {
  transform: translateY(0) scale(0.98);
}

/* Item Description */
.item-description {
  margin-top: 1.5rem;
  padding: 1rem;
  background: linear-gradient(135deg, #f8fafc, #e2e8f0);
  border-radius: 12px;
  color: #64748b;
  font-size: 0.95rem;
  line-height: 1.5;
  border: 1px solid #e2e8f0;
}

/* Empty State */
.empty-state {
  text-align: center;
  padding: 4rem;
  color: #64748b;
  grid-column: 1 / -1;
}

.empty-icon {
  font-size: 4rem;
  color: #cbd5e1;
  margin-bottom: 1.5rem;
}

.empty-state h3 {
  font-size: 1.5rem;
  color: #475569;
  margin: 0 0 1rem 0;
}

.empty-state p {
  font-size: 1.1rem;
  margin: 0;
}

/* ====================================
   BOOTSTRAP COMPATIBILITY - INVENTORY ENHANCED
   توافق Bootstrap - مخزون محسن  
   ==================================== */

/* Inventory Bootstrap Container */
.inventory-bootstrap-container {
  background: linear-gradient(135deg, #f0f4f8 0%, #e2e8f0 100%) !important;
  min-height: 100vh;
  direction: rtl;
  font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif !important;
}

/* New Bootstrap Enhanced Card Styles */
.inventory-card-enhanced {
  background: linear-gradient(145deg, #ffffff 0%, #f8fafc 100%) !important;
  border: none !important;
  border-radius: 20px !important;
  box-shadow: 
    0 12px 40px rgba(0, 0, 0, 0.08),
    0 4px 12px rgba(0, 0, 0, 0.04) !important;
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1) !important;
  position: relative;
  overflow: hidden;
  /* Enhanced Square Design */
  aspect-ratio: 1 / 1 !important;
  min-height: 320px !important;
  max-height: 380px !important;
  min-width: 320px !important;
  display: flex !important;
  flex-direction: column !important;
}

/* Content Distribution Fix */
.inventory-card-enhanced {
  /* Force proper height distribution */
  display: grid !important;
  grid-template-rows: auto 1fr auto !important;
  gap: 0.5rem !important;
}

.inventory-card-enhanced .card-body {
  display: grid !important;
  grid-template-rows: auto 1fr auto !important;
  gap: 0.5rem !important;
  height: 100% !important;
}

/* Status Bar */
.inventory-status-bar {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 6px;
  transition: all 0.3s ease;
  border-radius: 20px 20px 0 0;
}

.inventory-status-bar.available {
  background: linear-gradient(90deg, #10b981, #059669);
}

.inventory-status-bar.unavailable {
  background: linear-gradient(90deg, #ef4444, #dc2626);
}

/* Card Header - Compact Design */
.inventory-card-header {
  display: flex !important;
  flex-direction: column !important;
  align-items: center !important;
  text-align: center !important;
  margin-bottom: 0.75rem !important;
  flex-shrink: 0 !important;
  height: auto !important;
  max-height: 90px !important;
  grid-row: 1 !important;
  overflow: visible !important;
}

.inventory-item-icon {
  width: 40px;
  height: 40px;
  background: linear-gradient(135deg, #3b82f6, #2563eb);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 0.5rem;
  box-shadow: 0 4px 12px rgba(59, 130, 246, 0.3);
  flex-shrink: 0;
}

.inventory-item-icon i {
  font-size: 1.2rem;
  color: white;
}

.inventory-item-name {
  font-size: 1rem !important;
  font-weight: 700 !important;
  color: #1e293b !important;
  margin: 0 0 0.25rem 0 !important;
  text-align: center !important;
  line-height: 1.1 !important;
  max-height: 2.2rem !important;
  overflow: hidden !important;
  text-overflow: ellipsis !important;
  display: -webkit-box !important;
  -webkit-line-clamp: 2 !important;
  line-clamp: 2 !important;
  -webkit-box-orient: vertical !important;
}

.inventory-status-badge {
  padding: 0.25rem 0.6rem;
  border-radius: 12px;
  font-size: 0.6rem;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  border: 2px solid transparent;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
}

.inventory-status-badge.available {
  background: linear-gradient(135deg, #10b981, #059669);
  color: white;
  box-shadow: 0 4px 15px rgba(16, 185, 129, 0.3);
}

.inventory-status-badge.unavailable {
  background: linear-gradient(135deg, #ef4444, #dc2626);
  color: white;
  box-shadow: 0 4px 15px rgba(239, 68, 68, 0.3);
}

/* Details Section - Compact Layout */
.inventory-details-section {
  flex: 1 !important;
  display: flex !important;
  flex-direction: column !important;
  gap: 0.5rem !important;
  margin-bottom: 0.75rem !important;
  overflow: hidden !important;
  grid-row: 2 !important;
  overflow-y: auto !important;
  overflow-x: hidden !important;
}

.inventory-detail-item {
  display: flex !important;
  align-items: center !important;
  gap: 0.75rem !important;
  padding: 0.5rem !important;
  background: linear-gradient(135deg, #f8fafc, #e2e8f0) !important;
  border-radius: 8px !important;
  border: 1px solid #e2e8f0 !important;
  transition: all 0.3s ease !important;
  min-height: 45px !important;
  max-height: 50px !important;
}

.inventory-detail-item:hover {
  background: linear-gradient(135deg, #e2e8f0, #cbd5e1) !important;
}

.detail-icon {
  width: 28px;
  height: 28px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
}

.detail-icon i {
  font-size: 0.75rem;
}

.detail-icon.price {
  background: linear-gradient(135deg, #10b981, #059669);
  color: white;
}

.detail-icon.stock {
  background: linear-gradient(135deg, #3b82f6, #2563eb);
  color: white;
}

.detail-icon.warning {
  background: linear-gradient(135deg, #ef4444, #dc2626);
  color: white;
  animation: warning-pulse 1.5s infinite;
}

.detail-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 0.1rem;
  overflow: hidden;
}

.detail-label {
  font-size: 0.65rem;
  color: #64748b;
  font-weight: 500;
  line-height: 1;
}

.detail-value {
  font-size: 0.8rem;
  font-weight: 700;
  color: #1e293b;
  line-height: 1.1;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.price-value {
  color: #059669 !important;
  background: linear-gradient(135deg, #10b981, #059669);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.stock-value.low {
  color: #dc2626 !important;
}

.low-stock-indicator {
  display: inline-block;
  padding: 0.15rem 0.4rem;
  background: #fef2f2;
  color: #dc2626;
  border-radius: 6px;
  font-size: 0.55rem;
  margin-right: 0.4rem;
  border: 1px solid #fecaca;
  font-weight: 600;
}

/* Enhanced Stock Controls */
.stock-controls {
  background: linear-gradient(135deg, #f1f5f9, #e2e8f0);
  border-radius: 16px;
  padding: 1.5rem;
  border: 1px solid #e2e8f0;
  margin-top: 1rem;
  grid-row: 3 !important;
  overflow: visible !important;
}

.stock-controls-label {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-weight: 600;
  color: #475569;
  margin-bottom: 1rem;
  font-size: 1rem;
}

.stock-controls-label i {
  color: #3b82f6;
}

.stock-buttons {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 0.75rem;
}

.stock-control-btn {
  padding: 0.4rem !important;
  border: none !important;
  border-radius: 6px !important;
  font-size: 0.65rem !important;
  font-weight: 600 !important;
  cursor: pointer !important;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1) !important;
  display: flex !important;
  flex-direction: column !important;
  align-items: center !important;
  justify-content: center !important;
  min-height: 32px !important;
  max-height: 36px !important;
  position: relative !important;
  overflow: hidden !important;
  gap: 0.1rem !important;
  z-index: 1 !important;
}

.stock-control-btn::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
  transition: left 0.5s;
}

.stock-control-btn:hover::before {
  left: 100%;
}

.stock-control-btn.decrease {
  background: linear-gradient(135deg, #ef4444, #dc2626) !important;
  color: white !important;
  box-shadow: 0 2px 8px rgba(239, 68, 68, 0.3) !important;
  z-index: 2 !important;
}

.stock-control-btn.decrease:hover {
  background: linear-gradient(135deg, #dc2626, #b91c1c) !important;
  transform: translateY(-1px) !important;
  box-shadow: 0 4px 12px rgba(239, 68, 68, 0.4) !important;
  z-index: 3 !important;
}

.stock-control-btn.increase {
  background: linear-gradient(135deg, #10b981, #059669) !important;
  color: white !important;
  box-shadow: 0 2px 8px rgba(16, 185, 129, 0.3) !important;
  z-index: 2 !important;
}

.stock-control-btn.increase:hover {
  background: linear-gradient(135deg, #059669, #047857) !important;
  transform: translateY(-1px) !important;
  box-shadow: 0 4px 12px rgba(16, 185, 129, 0.4) !important;
  z-index: 3 !important;
}

.stock-control-btn.multiple {
  background: linear-gradient(135deg, #3b82f6, #2563eb) !important;
  color: white !important;
  box-shadow: 0 2px 8px rgba(59, 130, 246, 0.3) !important;
  z-index: 2 !important;
}

.stock-control-btn.multiple:hover {
  background: linear-gradient(135deg, #2563eb, #1d4ed8) !important;
  transform: translateY(-1px) !important;
  box-shadow: 0 4px 12px rgba(59, 130, 246, 0.4) !important;
  z-index: 3 !important;
}

.stock-control-btn:disabled {
  opacity: 0.5 !important;
  cursor: not-allowed !important;
  transform: none !important;
  box-shadow: none !important;
  background: #94a3b8 !important;
  color: #64748b !important;
}

.stock-control-btn:active {
  transform: translateY(0) scale(0.95) !important;
}

/* Force visibility and proper styling */
.stock-control-btn {
  visibility: visible !important;
  opacity: 1 !important;
  display: flex !important;
  pointer-events: auto !important;
}

.stock-control-btn i {
  font-size: 0.7rem !important;
}

.stock-control-btn span {
  font-size: 0.6rem !important;
  font-weight: 600 !important;
}

/* Top Indicator */
.inventory-top-indicator {
  position: absolute !important;
  top: 0 !important;
  left: 0 !important;
  right: 0 !important;
  height: 6px !important;
  border-radius: 20px 20px 0 0 !important;
  transition: all 0.3s ease !important;
}

/* Legacy Bootstrap Card Support */
.inventory-item-card {
  background: linear-gradient(145deg, #ffffff 0%, #f8fafc 100%) !important;
  border: none !important;
  border-radius: 20px !important;
  box-shadow: 
    0 12px 40px rgba(0, 0, 0, 0.08),
    0 4px 12px rgba(0, 0, 0, 0.04) !important;
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1) !important;
  position: relative;
  overflow: hidden;
  aspect-ratio: 1 / 1 !important;
  min-height: 320px !important;
  max-height: 380px !important;
  min-width: 320px !important;
}

.inventory-item-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 6px;
  background: linear-gradient(90deg, #3b82f6, #10b981, #f59e0b);
  transition: all 0.3s ease;
  border-radius: 20px 20px 0 0;
}

.inventory-item-card:hover {
  transform: translateY(-8px) scale(1.02) !important;
  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.12) !important;
}

.inventory-item-card:hover::before {
  height: 8px;
  box-shadow: 0 0 20px rgba(59, 130, 246, 0.4);
}

/* Enhanced Inventory Bootstrap Buttons */
.inventory-btn-enhanced {
  border-radius: 12px !important;
  font-weight: 600 !important;
  transition: all 0.3s ease !important;
  position: relative;
  overflow: hidden;
  min-height: 50px !important;
}

.inventory-btn-enhanced::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
  transition: left 0.5s;
}

.inventory-btn-enhanced:hover::before {
  left: 100%;
}

.inventory-btn-enhanced:hover {
  transform: translateY(-2px) !important;
}

/* Animations */
@keyframes warning-pulse {
  0%, 100% {
    opacity: 1;
    transform: scale(1);
  }
  50% {
    opacity: 0.7;
    transform: scale(1.1);
  }
}

@keyframes shimmer {
  0% {
    transform: translateX(-100%) rotate(45deg);
  }
  100% {
    transform: translateX(200%) rotate(45deg);
  }
}

/* Responsive Design - Enhanced for Square Cards */
@media (max-width: 1400px) {
  .inventory-grid {
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 1.5rem;
  }
  
  .inventory-card-enhanced {
    min-height: 300px !important;
    max-height: 350px !important;
    min-width: 300px !important;
  }
}

@media (max-width: 1200px) {
  .inventory-grid {
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 1.5rem;
  }
  
  .inventory-card-enhanced {
    min-height: 280px !important;
    max-height: 320px !important;
    min-width: 280px !important;
  }

  .stock-controls-grid {
    grid-template-columns: repeat(2, 1fr) !important;
    gap: 0.5rem !important;
  }
}

@media (max-width: 768px) {
  .inventory-screen {
    padding: 1.5rem;
  }

  .inventory-grid {
    grid-template-columns: 1fr;
    gap: 1.5rem;
  }

  .inventory-item,
  .inventory-card-enhanced {
    padding: 1.5rem !important;
    min-height: 280px !important;
    max-height: 320px !important;
    min-width: auto !important;
    aspect-ratio: 1.2 / 1 !important;
  }

  .item-header {
    flex-direction: column;
    gap: 1rem;
    align-items: flex-start;
  }

  .availability {
    align-self: flex-end;
  }

  .stock-buttons,
  .stock-controls-grid {
    grid-template-columns: repeat(2, 1fr) !important;
    gap: 0.4rem !important;
  }

  .inventory-header h1 {
    font-size: 2rem;
  }
  
  .inventory-item-name {
    font-size: 1.1rem !important;
  }
  
  .inventory-item-icon {
    width: 45px;
    height: 45px;
  }
  
  .inventory-item-icon i {
    font-size: 1.3rem;
  }
  
  .stock-control-btn {
    min-height: 35px !important;
    max-height: 40px !important;
    font-size: 0.7rem !important;
  }
  
  .controls-header {
    font-size: 0.8rem !important;
  }
}

@media (max-width: 480px) {
  .inventory-item,
  .inventory-card-enhanced {
    padding: 1rem !important;
    min-height: 260px !important;
    max-height: 300px !important;
    aspect-ratio: 1.1 / 1 !important;
  }

  .item-header h3,
  .inventory-item-name {
    font-size: 1rem !important;
  }

  .price {
    font-size: 1.4rem;
  }

  .stock {
    font-size: 1rem;
  }

  .stock-btn,
  .stock-control-btn {
    min-height: 32px !important;
    max-height: 36px !important;
    font-size: 0.65rem !important;
    padding: 0.4rem !important;
  }
  
  .inventory-item-icon {
    width: 40px;
    height: 40px;
  }
  
  .inventory-item-icon i {
    font-size: 1.2rem;
  }
  
  .detail-icon {
    width: 30px;
    height: 30px;
  }
  
  .detail-icon i {
    font-size: 0.8rem;
  }
  
  .controls-header {
    font-size: 0.75rem !important;
  }
  
  .detail-label {
/* Additional CSS for Better Content Distribution */

/* Card Body Optimization - No Top Padding */
.inventory-card-enhanced .card-body {
  display: grid !important;
  grid-template-rows: auto 1fr auto !important;
  gap: 0.4rem !important;
  height: 100% !important;
  padding: 0 0.8rem 0.8rem 0.8rem !important; /* No top padding */
}

/* Simplified Header - No Icon */
.inventory-card-header {
  grid-row: 1 !important;
  overflow: visible !important;
  display: flex !important;
  flex-direction: column !important;
  align-items: center !important;
  text-align: center !important;
  margin-bottom: 0.5rem !important;
  max-height: 60px !important; /* Reduced height without icon */
  padding-top: 0.5rem !important; /* Small top padding for the header only */
}

/* Remove icon styling completely */
.inventory-item-icon {
  display: none !important;
}

.inventory-item-name {
  font-size: 1.1rem !important;
  margin: 0 0 0.4rem 0 !important;
  max-height: 2rem !important;
  overflow: hidden !important;
  text-overflow: ellipsis !important;
  display: -webkit-box !important;
  -webkit-line-clamp: 2 !important;
  line-clamp: 2 !important;
  -webkit-box-orient: vertical !important;
}

/* Enhanced Details Section - Horizontal Layout Only */
.inventory-details-section {
  grid-row: 2 !important;
  overflow-y: auto !important;
  overflow-x: hidden !important;
  display: flex !important;
  flex-direction: column !important;
  gap: 0.6rem !important;
  flex: 1 !important;
  min-height: 0 !important;
}

/* Horizontal row for price and stock only */
.inventory-details-row {
  display: flex !important;
  gap: 0.8rem !important;
  width: 100% !important;
  align-items: stretch !important;
  margin-bottom: 0.5rem !important;
}

/* Enhanced detail items for horizontal layout - More Attractive Design */
.inventory-details-row .inventory-detail-item {
  flex: 1 !important;
  min-height: 65px !important;
  max-height: 70px !important;
  padding: 1rem !important;
  gap: 0.8rem !important;
  background: linear-gradient(135deg, #ffffff 0%, #f8fafc 50%, #f0f9ff 100%) !important;
  border: 2px solid transparent !important;
  border-radius: 16px !important;
  box-shadow: 
    0 4px 20px rgba(59, 130, 246, 0.08),
    0 2px 8px rgba(0, 0, 0, 0.04),
    inset 0 1px 0 rgba(255, 255, 255, 0.9) !important;
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1) !important;
  position: relative !important;
  overflow: hidden !important;
}

/* Enhanced price item styling */
.inventory-details-row .inventory-detail-item.price {
  background: linear-gradient(135deg, #ecfdf5 0%, #d1fae5 50%, #a7f3d0 100%) !important;
  border: 2px solid rgba(16, 185, 129, 0.2) !important;
}

.inventory-details-row .inventory-detail-item.price:hover {
  border-color: rgba(16, 185, 129, 0.4) !important;
  box-shadow: 
    0 8px 30px rgba(16, 185, 129, 0.15),
    0 4px 12px rgba(0, 0, 0, 0.08),
    inset 0 1px 0 rgba(255, 255, 255, 1) !important;
  transform: translateY(-3px) scale(1.02) !important;
}

/* Enhanced stock item styling */
.inventory-details-row .inventory-detail-item.stock {
  background: linear-gradient(135deg, #eff6ff 0%, #dbeafe 50%, #bfdbfe 100%) !important;
  border: 2px solid rgba(59, 130, 246, 0.2) !important;
}

.inventory-details-row .inventory-detail-item.stock:hover {
  border-color: rgba(59, 130, 246, 0.4) !important;
  box-shadow: 
    0 8px 30px rgba(59, 130, 246, 0.15),
    0 4px 12px rgba(0, 0, 0, 0.08),
    inset 0 1px 0 rgba(255, 255, 255, 1) !important;
  transform: translateY(-3px) scale(1.02) !important;
}

/* Low stock warning styling */
.inventory-details-row .inventory-detail-item.stock.low-stock {
  background: linear-gradient(135deg, #fef2f2 0%, #fee2e2 50%, #fecaca 100%) !important;
  border: 2px solid rgba(239, 68, 68, 0.3) !important;
  animation: low-stock-pulse 2s infinite !important;
}

.inventory-details-row .inventory-detail-item.stock.low-stock:hover {
  border-color: rgba(239, 68, 68, 0.5) !important;
  box-shadow: 
    0 8px 30px rgba(239, 68, 68, 0.2),
    0 4px 12px rgba(0, 0, 0, 0.08),
    inset 0 1px 0 rgba(255, 255, 255, 1) !important;
}

/* Shimmer effect for cards */
.inventory-details-row .inventory-detail-item::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.6), transparent);
  transition: left 0.6s;
}

.inventory-details-row .inventory-detail-item:hover::before {
  left: 100%;
}

/* Description icon styling */
.detail-icon.description {
  background: linear-gradient(135deg, #8b5cf6, #7c3aed);
  color: white;
}

/* Ensure proper text wrapping in description */
.inventory-description-row .detail-value {
  overflow: hidden !important;
  text-overflow: ellipsis !important;
  white-space: nowrap !important;
  max-width: 100% !important;
}

/* Enhanced Controls Section for Better Integration */
.inventory-controls-section {
  background: linear-gradient(135deg, #f1f5f9, #e2e8f0) !important;
  border-radius: 8px !important;
  padding: 0.6rem !important;
  border: 1px solid #e2e8f0 !important;
  min-height: 65px !important;
  max-height: 75px !important;
  margin-top: 0.5rem !important;
}

.controls-header {
  font-size: 0.7rem !important;
  margin-bottom: 0.4rem !important;
  gap: 0.3rem !important;
}

.stock-controls-grid {
  gap: 0.3rem !important;
  height: 35px !important;
}

.stock-control-btn {
  min-height: 30px !important;
  max-height: 35px !important;
  font-size: 0.65rem !important;
  padding: 0.3rem !important;
  border-radius: 5px !important;
}

.stock-control-btn i {
  font-size: 0.7rem !important;
}

.stock-control-btn span {
  font-size: 0.6rem !important;
}

/* Force consistent spacing */
.inventory-card-enhanced .card-body {
  padding: 0.8rem !important;
  gap: 0.4rem !important;
}

.inventory-card-header {
  margin-bottom: 0.5rem !important;
  max-height: 85px !important;
}

.inventory-item-name {
  font-size: 0.95rem !important;
  margin-bottom: 0.3rem !important;
  max-height: 2rem !important;
}

.inventory-status-badge {
  font-size: 0.55rem !important;
  padding: 0.2rem 0.5rem !important;
}

/* Scrollbar styling for details section */
.inventory-details-section::-webkit-scrollbar {
  width: 3px;
}

.inventory-details-section::-webkit-scrollbar-track {
  background: transparent;
}

.inventory-details-section::-webkit-scrollbar-thumb {
  background: #cbd5e1;
  border-radius: 3px;
}

.inventory-details-section::-webkit-scrollbar-thumb:hover {
  background: #94a3b8;
}

/* Simplified Layout - No Icon, No Description, No Top Padding */

/* Enhanced icon styling with better contrast and visibility */
.inventory-details-row .detail-icon {
  width: 40px !important;
  height: 40px !important;
  border-radius: 50% !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  flex-shrink: 0 !important;
  box-shadow: 
    0 4px 15px rgba(0, 0, 0, 0.2),
    0 2px 6px rgba(0, 0, 0, 0.1),
    inset 0 1px 0 rgba(255, 255, 255, 0.3) !important;
  transition: all 0.3s ease !important;
  position: relative !important;
}

.inventory-details-row .detail-icon::before {
  content: '';
  position: absolute;
  inset: 0;
  border-radius: 50%;
  background: radial-gradient(circle at 30% 30%, rgba(255, 255, 255, 0.4), transparent 60%);
  pointer-events: none;
}

.inventory-details-row .detail-icon i {
  font-size: 1.1rem !important;
  color: white !important;
  text-shadow: 0 1px 3px rgba(0, 0, 0, 0.3) !important;
  z-index: 1 !important;
  position: relative !important;
}

/* Enhanced price icon with luxurious green gradient */
.inventory-details-row .detail-icon.price {
  background: linear-gradient(135deg, #059669 0%, #10b981 30%, #34d399 100%) !important;
  box-shadow: 
    0 4px 15px rgba(16, 185, 129, 0.4),
    0 2px 6px rgba(5, 150, 105, 0.3),
    inset 0 1px 0 rgba(255, 255, 255, 0.3) !important;
}

.inventory-details-row .detail-icon.price:hover {
  transform: scale(1.1) !important;
  box-shadow: 
    0 6px 20px rgba(16, 185, 129, 0.5),
    0 3px 8px rgba(5, 150, 105, 0.4),
    inset 0 1px 0 rgba(255, 255, 255, 0.4) !important;
}

/* Enhanced stock icon with vibrant blue gradient */
.inventory-details-row .detail-icon.stock {
  background: linear-gradient(135deg, #2563eb 0%, #3b82f6 30%, #60a5fa 100%) !important;
  box-shadow: 
    0 4px 15px rgba(59, 130, 246, 0.4),
    0 2px 6px rgba(37, 99, 235, 0.3),
    inset 0 1px 0 rgba(255, 255, 255, 0.3) !important;
}

.inventory-details-row .detail-icon.stock:hover {
  transform: scale(1.1) !important;
  box-shadow: 
    0 6px 20px rgba(59, 130, 246, 0.5),
    0 3px 8px rgba(37, 99, 235, 0.4),
    inset 0 1px 0 rgba(255, 255, 255, 0.4) !important;
}

/* Enhanced warning stock icon with pulsing red gradient */
.inventory-details-row .detail-icon.warning {
  background: linear-gradient(135deg, #dc2626 0%, #ef4444 30%, #f87171 100%) !important;
  box-shadow: 
    0 4px 15px rgba(239, 68, 68, 0.5),
    0 2px 6px rgba(220, 38, 38, 0.4),
    inset 0 1px 0 rgba(255, 255, 255, 0.3) !important;
  animation: warning-icon-pulse 1.5s infinite !important;
}

.inventory-details-row .detail-icon.warning:hover {
  transform: scale(1.15) !important;
  animation: warning-icon-pulse 0.8s infinite !important;
}

/* Enhanced content styling with better typography */
.inventory-details-row .detail-content {
  gap: 0.3rem !important;
  display: flex !important;
  flex-direction: column !important;
  justify-content: center !important;
  flex: 1 !important;
}

.inventory-details-row .detail-label {
  font-size: 0.75rem !important;
  line-height: 1 !important;
  color: #64748b !important;
  font-weight: 500 !important;
  text-transform: uppercase !important;
  letter-spacing: 0.05em !important;
  margin: 0 !important;
}

.inventory-details-row .detail-value {
  font-size: 1rem !important;
  line-height: 1.2 !important;
  font-weight: 700 !important;
  color: #1e293b !important;
  margin: 0 !important;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.05) !important;
  letter-spacing: 0.02em !important;
  font-feature-settings: "tnum" 1 !important; /* Tabular numbers for better alignment */
}

/* Enhanced price value styling */
.inventory-details-row .detail-value.price-value {
  color: #059669 !important;
  background: linear-gradient(135deg, #059669, #10b981) !important;
  -webkit-background-clip: text !important;
  -webkit-text-fill-color: transparent !important;
  background-clip: text !important;
  font-size: 1.1rem !important;
  font-weight: 800 !important;
}

/* Enhanced stock value styling */
.inventory-details-row .detail-value.stock-value {
  color: #2563eb !important;
  font-weight: 700 !important;
}

.inventory-details-row .detail-value.stock-value.low {
  color: #dc2626 !important;
  font-weight: 800 !important;
  animation: low-stock-text-pulse 2s infinite !important;
}

/* Low stock indicator styling */
.low-stock-indicator {
  display: inline-block !important;
  font-size: 0.65rem !important;
  background: linear-gradient(135deg, #ef4444, #dc2626) !important;
  color: white !important;
  padding: 0.15rem 0.4rem !important;
  border-radius: 8px !important;
  margin-right: 0.3rem !important;
  font-weight: 600 !important;
  text-shadow: none !important;
  box-shadow: 0 2px 6px rgba(239, 68, 68, 0.3) !important;
  animation: low-stock-badge-pulse 1.5s infinite !important;
}

/* Status badge adjustments for simplified header */
.inventory-status-badge {
  font-size: 0.7rem !important;
  padding: 0.3rem 0.7rem !important;
  border-radius: 10px !important;
}

/* Enhanced Mobile responsiveness for better user experience */
@media (max-width: 768px) {
  .inventory-card-enhanced .card-body {
    padding: 0 0.8rem 0.8rem 0.8rem !important;
  }
  
  .inventory-details-row {
    gap: 0.5rem !important;
  }
  
  .inventory-details-row .inventory-detail-item {
    min-height: 55px !important;
    max-height: 60px !important;
    padding: 0.8rem !important;
  }
  
  .inventory-details-row .detail-icon {
    width: 35px !important;
    height: 35px !important;
  }
  
  .inventory-details-row .detail-icon i {
    font-size: 1rem !important;
  }
  
  .inventory-details-row .detail-value {
    font-size: 0.95rem !important;
  }
  
  .inventory-details-row .detail-value.price-value {
    font-size: 1rem !important;
  }
  
  .inventory-card-header {
    max-height: 70px !important;
    padding-top: 0.6rem !important;
  }
  
  .inventory-item-name {
    font-size: 1.05rem !important;
  }
}

@media (max-width: 480px) {
  .inventory-details-row {
    flex-direction: column !important;
    gap: 0.4rem !important;
  }
  
  .inventory-details-row .inventory-detail-item {
    flex: none !important;
    min-height: 50px !important;
    max-height: 55px !important;
    padding: 0.7rem !important;
  }
  
  .inventory-details-row .detail-icon {
    width: 32px !important;
    height: 32px !important;
  }
  
  .inventory-details-row .detail-icon i {
    font-size: 0.9rem !important;
  }
  
  .inventory-details-row .detail-value {
    font-size: 0.9rem !important;
  }
  
  .inventory-details-row .detail-value.price-value {
    font-size: 0.95rem !important;
  }
  
  .inventory-card-header {
    max-height: 60px !important;
    padding-top: 0.5rem !important;
  }
  
  .inventory-item-name {
    font-size: 1rem !important;
    margin-bottom: 0.3rem !important;
  }
  
  .low-stock-indicator {
    font-size: 0.6rem !important;
    padding: 0.1rem 0.3rem !important;
  }
}

/* Enhanced Large Screen Optimization */
@media (min-width: 1200px) {
  .inventory-details-row .inventory-detail-item {
    min-height: 75px !important;
    max-height: 80px !important;
    padding: 1.2rem !important;
  }
  
  .inventory-details-row .detail-icon {
    width: 45px !important;
    height: 45px !important;
  }
  
  .inventory-details-row .detail-icon i {
    font-size: 1.2rem !important;
  }
  
  .inventory-details-row .detail-value {
    font-size: 1.1rem !important;
  }
  
  .inventory-details-row .detail-value.price-value {
    font-size: 1.2rem !important;
  }
  
  .inventory-details-row .detail-label {
    font-size: 0.8rem !important;
  }
}

/* Extra Large Screen Optimization */
@media (min-width: 1400px) {
  .inventory-details-row .inventory-detail-item {
    min-height: 80px !important;
    max-height: 85px !important;
    padding: 1.4rem !important;
  }
  
  .inventory-details-row .detail-icon {
    width: 50px !important;
    height: 50px !important;
  }
  
  .inventory-details-row .detail-icon i {
    font-size: 1.3rem !important;
  }
  
  .inventory-details-row .detail-value {
    font-size: 1.2rem !important;
  }
  
  .inventory-details-row .detail-value.price-value {
    font-size: 1.3rem !important;
  }
  
  .inventory-details-row .detail-label {
    font-size: 0.85rem !important;
  }
  
  .low-stock-indicator {
    font-size: 0.7rem !important;
    padding: 0.2rem 0.5rem !important;
  }
}

/* Enhanced Animations for Better Visual Experience */

/* Low stock card pulsing animation */
@keyframes low-stock-pulse {
  0%, 100% {
    border-color: rgba(239, 68, 68, 0.3);
    box-shadow: 
      0 4px 20px rgba(239, 68, 68, 0.1),
      0 2px 8px rgba(0, 0, 0, 0.04),
      inset 0 1px 0 rgba(255, 255, 255, 0.9);
  }
  50% {
    border-color: rgba(239, 68, 68, 0.6);
    box-shadow: 
      0 8px 30px rgba(239, 68, 68, 0.25),
      0 4px 12px rgba(0, 0, 0, 0.08),
      inset 0 1px 0 rgba(255, 255, 255, 1);
  }
}

/* Warning icon pulsing animation */
@keyframes warning-icon-pulse {
  0%, 100% {
    transform: scale(1);
    box-shadow: 
      0 4px 15px rgba(239, 68, 68, 0.5),
      0 2px 6px rgba(220, 38, 38, 0.4),
      inset 0 1px 0 rgba(255, 255, 255, 0.3);
  }
  50% {
    transform: scale(1.05);
    box-shadow: 
      0 6px 20px rgba(239, 68, 68, 0.7),
      0 3px 8px rgba(220, 38, 38, 0.5),
      inset 0 1px 0 rgba(255, 255, 255, 0.4);
  }
}

/* Low stock text pulsing animation */
@keyframes low-stock-text-pulse {
  0%, 100% {
    color: #dc2626;
    text-shadow: 0 1px 2px rgba(220, 38, 38, 0.2);
  }
  50% {
    color: #ef4444;
    text-shadow: 0 2px 4px rgba(239, 68, 68, 0.3);
  }
}

/* Low stock badge pulsing animation */
@keyframes low-stock-badge-pulse {
  0%, 100% {
    background: linear-gradient(135deg, #ef4444, #dc2626);
    box-shadow: 0 2px 6px rgba(239, 68, 68, 0.3);
  }
  50% {
    background: linear-gradient(135deg, #f87171, #ef4444);
    box-shadow: 0 4px 12px rgba(239, 68, 68, 0.5);
  }
}

/* Floating animation for hover effects */
@keyframes float {
  0%, 100% {
    transform: translateY(0);
  }
  50% {
    transform: translateY(-2px);
  }
}

/* Gentle glow animation for focused elements */
@keyframes gentle-glow {
  0%, 100% {
    box-shadow: 
      0 4px 20px rgba(59, 130, 246, 0.08),
      0 2px 8px rgba(0, 0, 0, 0.04),
      inset 0 1px 0 rgba(255, 255, 255, 0.9);
  }
  50% {
    box-shadow: 
      0 8px 30px rgba(59, 130, 246, 0.15),
      0 4px 12px rgba(0, 0, 0, 0.08),
      inset 0 1px 0 rgba(255, 255, 255, 1);
  }
}

/* Success glow for price elements */
@keyframes success-glow {
  0%, 100% {
    box-shadow: 
      0 4px 20px rgba(16, 185, 129, 0.08),
      0 2px 8px rgba(0, 0, 0, 0.04),
      inset 0 1px 0 rgba(255, 255, 255, 0.9);
  }
  50% {
    box-shadow: 
      0 8px 30px rgba(16, 185, 129, 0.15),
      0 4px 12px rgba(0, 0, 0, 0.08),
      inset 0 1px 0 rgba(255, 255, 255, 1);
  }
}

/* Enhanced grid spacing and balance */
.inventory-details-row {
  display: flex !important;
  gap: 0.8rem !important;
  width: 100% !important;
  align-items: stretch !important;
  margin-bottom: 0.5rem !important;
}

/* Focus and accessibility improvements */
.inventory-details-row .inventory-detail-item:focus-within {
  outline: 3px solid rgba(59, 130, 246, 0.3) !important;
  outline-offset: 2px !important;
  border-color: rgba(59, 130, 246, 0.5) !important;
}

/* Enhanced visual hierarchy */
.inventory-details-section {
  grid-row: 2 !important;
  overflow-y: auto !important;
  overflow-x: hidden !important;
  display: flex !important;
  flex-direction: column !important;
  gap: 0.6rem !important;
  flex: 1 !important;
  min-height: 0 !important;
}

/* Improved readability and contrast */
.inventory-details-row .detail-value {
  letter-spacing: 0.02em !important;
  font-feature-settings: "tnum" 1 !important; /* Tabular numbers for better alignment */
}

/* Enhanced spacing for better visual balance */
.inventory-details-row .detail-content {
  min-width: 0 !important; /* Allows text to wrap properly */
  overflow: hidden !important;
}

/* Better text handling for long content */
.inventory-details-row .detail-value {
  word-break: break-word !important;
  -webkit-hyphens: auto !important;
  hyphens: auto !important;
}

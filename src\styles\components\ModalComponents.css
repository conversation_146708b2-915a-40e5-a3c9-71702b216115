/* ModalComponents.css - Scoped styles for Modal Components */

/* تم إزالة المتغيرات المحلية واستبدالها بقيم مباشرة للحفاظ على العزل */

/* Modal overlay - Enhanced */
.modalComponents__overlay {
  position: fixed !important;
  top: 0 !important;
  left: 0 !important;
  right: 0 !important;
  bottom: 0 !important;
  background: rgba(0, 0, 0, 0.6) !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  z-index: 999999 !important;
  padding: 1rem;
  animation: modalComponents-fadeIn 0.3s ease-out;
  -webkit-backdrop-filter: blur(5px);
  backdrop-filter: blur(5px);
}

/* General Modal Overlay Fix */
.modal-overlay {
  position: fixed !important;
  top: 0 !important;
  left: 0 !important;
  right: 0 !important;
  bottom: 0 !important;
  background: rgba(0, 0, 0, 0.6) !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  z-index: 1000000 !important;
  padding: 1rem !important;
  -webkit-backdrop-filter: blur(5px);
  backdrop-filter: blur(5px);
}

/* Base modal container */
.modalComponents__modal {
  position: relative !important;
  z-index: 1000000 !important;
  background: white !important;
  border-radius: 12px !important;
  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3) !important;
  max-width: 900px !important;
  width: 95% !important;
  max-height: 95vh !important;
  overflow-y: auto !important;
  animation: modalComponents-slideIn 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94) !important;
  font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
}

/* Modal header */
.modalComponents__header {
  padding: 1.5rem 2rem;
  border-bottom: 1px solid #dee2e6;
  display: flex;
  justify-content: space-between;
  align-items: center;
  background: #f8f9fa;
  border-radius: 12px 12px 0 0;
}

.modalComponents__title {
  font-size: 1.5rem;
  font-weight: 600;
  color: #333;
  margin: 0;
  display: flex;
  align-items: center;
  gap: 0.75rem;
}

.modalComponents__title-icon {
  font-size: 1.3rem;
  color: #007bff;
}

.modalComponents__close-button {
  background: none;
  border: none;
  font-size: 1.5rem;
  color: #6c757d;
  cursor: pointer;
  padding: 0.5rem;
  border-radius: 50%;
  transition: all 0.2s ease;
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.modalComponents__close-button:hover {
  background: rgba(0, 0, 0, 0.1);
  color: #333;
}

/* Modal body */
.modalComponents__body {
  padding: 2rem;
}

/* Modal footer */
.modalComponents__footer {
  padding: 1.5rem 2rem;
  border-top: 1px solid #dee2e6;
  display: flex;
  justify-content: flex-end;
  gap: 1rem;
  background: #f8f9fa;
  border-radius: 0 0 12px 12px;
}

/* Content sections */
.modalComponents__section {
  margin-bottom: 2rem;
}

.modalComponents__section:last-child {
  margin-bottom: 0;
}

.modalComponents__section-title {
  font-size: 1.2rem;
  font-weight: 600;
  color: #333;
  margin: 0 0 1rem 0;
  padding-bottom: 0.5rem;
  border-bottom: 2px solid #007bff;
}

/* Form elements */
.modalComponents__form-group {
  margin-bottom: 1.5rem;
}

.modalComponents__label {
  display: block;
  font-size: 0.9rem;
  font-weight: 600;
  color: #333;
  margin-bottom: 0.5rem;
}

.modalComponents__input,
.modalComponents__textarea,
.modalComponents__select {
  width: 100%;
  padding: 0.75rem;
  border: 2px solid #dee2e6;
  border-radius: 6px;
  font-size: 1rem;
  font-family: inherit;
  transition: border-color 0.2s ease, box-shadow 0.2s ease;
}

.modalComponents__input:focus,
.modalComponents__textarea:focus,
.modalComponents__select:focus {
  outline: none;
  border-color: #007bff;
  box-shadow: 0 0 0 3px rgba(0, 123, 255, 0.1);
}

.modalComponents__textarea {
  min-height: 100px;
  resize: vertical;
}

/* Buttons */
.modalComponents__button {
  padding: 0.75rem 1.5rem;
  border: none;
  border-radius: 6px;
  font-size: 1rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  text-decoration: none;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
  min-width: 100px;
}

.modalComponents__button--primary {
  background: #007bff;
  color: white;
}

.modalComponents__button--primary:hover {
  background: #0056b3;
  transform: translateY(-1px);
}

.modalComponents__button--secondary {
  background: #6c757d;
  color: white;
}

.modalComponents__button--secondary:hover {
  background: #545b62;
  transform: translateY(-1px);
}

.modalComponents__button--success {
  background: #28a745;
  color: white;
}

.modalComponents__button--success:hover {
  background: #218838;
  transform: translateY(-1px);
}

.modalComponents__button--danger {
  background: #dc3545;
  color: white;
}

.modalComponents__button--danger:hover {
  background: #c82333;
  transform: translateY(-1px);
}

.modalComponents__button--outline {
  background: transparent;
  border: 2px solid #dee2e6;
  color: #333;
}

.modalComponents__button--outline:hover {
  background: #dee2e6;
}

/* Data display */
.modalComponents__data-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 1rem;
  margin-bottom: 1.5rem;
}

.modalComponents__data-item {
  padding: 1rem;
  background: #f8f9fa;
  border-radius: 6px;
  border-left: 4px solid #007bff;
}

.modalComponents__data-label {
  font-size: 0.85rem;
  color: #6c757d;
  margin-bottom: 0.25rem;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  font-weight: 600;
}

.modalComponents__data-value {
  font-size: 1.1rem;
  color: #333;
  font-weight: 500;
}

/* List items */
.modalComponents__list {
  list-style: none;
  padding: 0;
  margin: 0;
}

.modalComponents__list-item {
  padding: 1rem;
  border: 1px solid #dee2e6;
  border-radius: 6px;
  margin-bottom: 0.5rem;
  background: white;
  transition: all 0.2s ease;
}

.modalComponents__list-item:hover {
  background: #f8f9fa;
  border-color: #007bff;
}

.modalComponents__list-item:last-child {
  margin-bottom: 0;
}

/* Status badges */
.modalComponents__badge {
  padding: 0.25rem 0.75rem;
  border-radius: 20px;
  font-size: 0.8rem;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  display: inline-block;
}

.modalComponents__badge--success {
  background: #d4edda;
  color: #155724;
  border: 1px solid #c3e6cb;
}

.modalComponents__badge--warning {
  background: #fff3cd;
  color: #856404;
  border: 1px solid #ffeaa7;
}

.modalComponents__badge--danger {
  background: #f8d7da;
  color: #721c24;
  border: 1px solid #f5c6cb;
}

.modalComponents__badge--info {
  background: #d1ecf1;
  color: #0c5460;
  border: 1px solid #bee5eb;
}

/* Loading state */
.modalComponents__loading {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 3rem;
  color: #6c757d;
  font-size: 1.1rem;
}

.modalComponents__spinner {
  width: 32px;
  height: 32px;
  border: 3px solid #dee2e6;
  border-top: 3px solid #007bff;
  border-radius: 50%;
  animation: modalComponents-spin 1s linear infinite;
  margin-right: 1rem;
}

/* Animations */
@keyframes modalComponents-fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes modalComponents-slideIn {
  from {
    opacity: 0;
    transform: translateY(-30px) scale(0.95);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

@keyframes modalComponents-spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Responsive design */
@media (max-width: 768px) {
  .modalComponents__modal {
    max-width: 95% !important;
    margin: 1rem !important;
  }
  
  .modalComponents__header {
    padding: 1rem 1.5rem;
  }
  
  .modalComponents__body {
    padding: 1.5rem;
  }
  
  .modalComponents__footer {
    padding: 1rem 1.5rem;
    flex-direction: column;
  }
  
  .modalComponents__button {
    width: 100%;
  }
  
  .modalComponents__data-grid {
    grid-template-columns: 1fr;
    gap: 0.75rem;
  }
  
  .modalComponents__title {
    font-size: 1.2rem;
  }
}

@media (max-width: 480px) {
  .modalComponents__overlay {
    padding: 0.5rem;
  }
  
  .modalComponents__modal {
    max-width: 100% !important;
    margin: 0.5rem !important;
  }
  
  .modalComponents__header,
  .modalComponents__body,
  .modalComponents__footer {
    padding: 1rem;
  }
}

/* Accessibility improvements */
.modalComponents__modal:focus {
  outline: 2px solid #007bff;
  outline-offset: 2px;
}

/* High contrast mode support */
@media (prefers-contrast: high) {
  .modalComponents__modal {
    border: 2px solid #333;
  }

  .modalComponents__button {
    border: 2px solid currentColor;
  }
}

/* ============================== */
/* Enhanced Discount Details Modal */
/* ============================== */

/* Discount Details Modal Specific Styles */
.enhanced-modal-overlay {
  position: fixed !important;
  top: 0 !important;
  left: 0 !important;
  right: 0 !important;
  bottom: 0 !important;
  background: rgba(0, 0, 0, 0.7) !important;
  backdrop-filter: blur(8px) !important;
  -webkit-backdrop-filter: blur(8px) !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  z-index: 1000000 !important;
  padding: 1rem !important;
}

.discount-details-modal.enhanced-modal {
  max-width: 1000px !important;
  width: 90% !important;
  max-height: 85vh !important;
  background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%) !important;
  border: none !important;
  box-shadow: 0 25px 80px rgba(0, 0, 0, 0.25) !important;
  border-radius: 12px !important;
  overflow: hidden !important;
  position: relative !important;
  margin: auto !important;
}

/* Enhanced Modal Header */
.enhanced-modal-header {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%) !important;
  color: white !important;
  padding: 1.5rem 2rem !important;
  border-bottom: none !important;
  border-radius: 12px 12px 0 0 !important;
}

.enhanced-modal-header .modal-header-content {
  display: flex;
  align-items: center;
  gap: 1rem;
  flex: 1;
}

.enhanced-modal-header .modal-icon {
  width: 50px;
  height: 50px;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.5rem;
  color: white;
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
}

.enhanced-modal-header .modal-title-section {
  flex: 1;
}

.enhanced-modal-header .modal-title {
  font-size: 1.75rem;
  font-weight: 700;
  margin: 0;
  color: white;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.enhanced-modal-header .modal-subtitle {
  font-size: 1rem;
  margin: 0.25rem 0 0 0;
  color: rgba(255, 255, 255, 0.9);
  font-weight: 400;
}

.enhanced-close-btn {
  background: rgba(255, 255, 255, 0.2) !important;
  border: none !important;
  color: white !important;
  width: 45px !important;
  height: 45px !important;
  border-radius: 50% !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  font-size: 1.25rem !important;
  cursor: pointer !important;
  transition: all 0.3s ease !important;
  backdrop-filter: blur(10px) !important;
  -webkit-backdrop-filter: blur(10px) !important;
}

.enhanced-close-btn:hover {
  background: rgba(255, 255, 255, 0.3) !important;
  transform: scale(1.1) !important;
}

/* Enhanced Modal Body */
.enhanced-modal-body {
  padding: 1.5rem !important;
  background: #ffffff !important;
  max-height: calc(85vh - 120px) !important;
  overflow-y: auto !important;
}

.discount-details-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
  gap: 1.5rem;
  margin-bottom: 2rem;
}

/* Detail Cards */
.detail-card {
  background: #ffffff;
  border: 1px solid #e9ecef;
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
  transition: all 0.3s ease;
}

.detail-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.12);
}

.detail-card .card-header {
  background: linear-gradient(135deg, #f8f9fa, #e9ecef);
  padding: 1rem 1.25rem;
  border-bottom: 1px solid #dee2e6;
  display: flex;
  align-items: center;
  gap: 0.75rem;
  font-weight: 600;
  color: #495057;
}

.detail-card .card-header i {
  font-size: 1.1rem;
  color: #007bff;
}

.detail-card .card-content {
  padding: 1.25rem;
}

/* Status Card */
.status-card .card-header i {
  color: #17a2b8 !important;
}

.status-badge {
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.75rem 1rem;
  border-radius: 8px;
  font-weight: 600;
  font-size: 1rem;
}

.status-badge.status-pending {
  background: linear-gradient(135deg, #fff3cd, #ffeaa7);
  color: #856404;
  border: 1px solid #ffeaa7;
}

.status-badge.status-approved {
  background: linear-gradient(135deg, #d4edda, #c3e6cb);
  color: #155724;
  border: 1px solid #c3e6cb;
}

.status-badge.status-rejected {
  background: linear-gradient(135deg, #f8d7da, #f5c6cb);
  color: #721c24;
  border: 1px solid #f5c6cb;
}

/* Order Info Card */
.order-info-card .card-header i {
  color: #28a745 !important;
}

.info-grid {
  display: grid;
  gap: 0.75rem;
}

.info-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0.5rem 0;
  border-bottom: 1px solid #f8f9fa;
}

.info-item:last-child {
  border-bottom: none;
}

.info-item .label {
  font-weight: 500;
  color: #6c757d;
  font-size: 0.9rem;
}

.info-item .value {
  font-weight: 600;
  color: #2c3e50;
  font-size: 0.95rem;
}

/* Amount Card */
.amount-card .card-header i {
  color: #fd7e14 !important;
}

.amount-breakdown {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
}

.amount-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0.75rem;
  border-radius: 8px;
  font-size: 1rem;
}

.amount-row.original {
  background: linear-gradient(135deg, #e3f2fd, #bbdefb);
  border: 1px solid #bbdefb;
}

.amount-row.discount {
  background: linear-gradient(135deg, #fff3e0, #ffcc02);
  border: 1px solid #ffcc02;
}

.amount-row.final {
  background: linear-gradient(135deg, #e8f5e8, #c8e6c9);
  border: 1px solid #c8e6c9;
  font-weight: 700;
  font-size: 1.1rem;
}

.amount-label {
  font-weight: 600;
  color: #495057;
}

.amount-value {
  font-weight: 700;
  color: #2c3e50;
}

.amount-value.discount-amount {
  color: #e67e22;
}

.amount-value.final-amount {
  color: #27ae60;
  font-size: 1.2rem;
}

.amount-separator {
  height: 2px;
  background: linear-gradient(90deg, #dee2e6, #adb5bd, #dee2e6);
  margin: 0.5rem 0;
  border-radius: 1px;
}

/* Manager Info Section */
.detail-group {
  background: #f8f9fa;
  border: 1px solid #e9ecef;
  border-radius: 12px;
  padding: 1.25rem;
  margin-top: 1.5rem;
}

.detail-group h4 {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  margin: 0 0 1rem 0;
  font-size: 1.1rem;
  font-weight: 600;
  color: #495057;
}

.detail-group h4 i {
  color: #6f42c1;
  font-size: 1rem;
}

.detail-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0.5rem 0;
  border-bottom: 1px solid #e9ecef;
}

.detail-item:last-child {
  border-bottom: none;
}

.detail-item .label {
  font-weight: 500;
  color: #6c757d;
  font-size: 0.9rem;
}

.detail-item .value {
  font-weight: 600;
  color: #2c3e50;
  font-size: 0.95rem;
}

.detail-item .value.manager-name {
  color: #6f42c1;
  font-weight: 700;
}

/* Order Items Section */
.order-items-section {
  grid-column: 1 / -1;
  background: #ffffff;
  border: 1px solid #e9ecef;
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
}

.order-items-section .section-header {
  background: linear-gradient(135deg, #f8f9fa, #e9ecef);
  padding: 1rem 1.25rem;
  border-bottom: 1px solid #dee2e6;
  display: flex;
  align-items: center;
  gap: 0.75rem;
  font-weight: 600;
  color: #495057;
}

.order-items-section .section-header i {
  font-size: 1.1rem;
  color: #dc3545;
}

.order-items-table {
  width: 100%;
  border-collapse: collapse;
}

.order-items-table th,
.order-items-table td {
  padding: 0.75rem 1rem;
  text-align: right;
  border-bottom: 1px solid #f8f9fa;
}

.order-items-table th {
  background: #f8f9fa;
  font-weight: 600;
  color: #495057;
  font-size: 0.9rem;
}

.order-items-table td {
  color: #2c3e50;
  font-size: 0.9rem;
}

.order-items-table tbody tr:hover {
  background: #f8f9fa;
}

.order-items-table .item-name {
  font-weight: 600;
  color: #2c3e50;
}

.order-items-table .item-quantity {
  color: #007bff;
  font-weight: 600;
}

.order-items-table .item-price {
  color: #28a745;
  font-weight: 600;
}

/* Reason Section */
.reason-section {
  grid-column: 1 / -1;
  background: linear-gradient(135deg, #fff3cd, #ffeaa7);
  border: 1px solid #ffeaa7;
  border-radius: 12px;
  padding: 1.25rem;
  margin-top: 1rem;
}

.reason-section .reason-header {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  margin-bottom: 0.75rem;
  font-weight: 600;
  color: #856404;
}

.reason-section .reason-header i {
  color: #f39c12;
}

.reason-text {
  color: #856404;
  font-size: 1rem;
  line-height: 1.5;
  margin: 0;
  font-style: italic;
  background: rgba(255, 255, 255, 0.5);
  padding: 0.75rem;
  border-radius: 8px;
  border-left: 4px solid #f39c12;
}

/* Responsive Design for Discount Modal */
@media (max-width: 1200px) {
  .discount-details-modal.enhanced-modal {
    max-width: 95% !important;
    width: 95% !important;
    max-height: 90vh !important;
  }

  .enhanced-modal-body {
    max-height: calc(90vh - 120px) !important;
  }

  .discount-details-grid {
    grid-template-columns: 1fr;
    gap: 1rem;
  }
}

@media (max-width: 768px) {
  .enhanced-modal-overlay {
    padding: 0.5rem !important;
  }

  .discount-details-modal.enhanced-modal {
    width: 98% !important;
    max-height: 95vh !important;
  }

  .enhanced-modal-header {
    padding: 1rem 1.5rem !important;
  }

  .enhanced-modal-header .modal-title {
    font-size: 1.4rem;
  }

  .enhanced-modal-header .modal-icon {
    width: 40px;
    height: 40px;
    font-size: 1.2rem;
  }

  .enhanced-modal-body {
    padding: 1rem !important;
    max-height: calc(95vh - 100px) !important;
  }

  .detail-card .card-header {
    padding: 0.75rem 1rem;
  }

  .detail-card .card-content {
    padding: 1rem;
  }

  .amount-row {
    padding: 0.5rem;
    font-size: 0.9rem;
  }

  .amount-row.final {
    font-size: 1rem;
  }

  .order-items-table th,
  .order-items-table td {
    padding: 0.5rem 0.75rem;
    font-size: 0.85rem;
  }
}

@media (max-width: 480px) {
  .enhanced-modal-overlay {
    padding: 0.25rem !important;
  }

  .discount-details-modal.enhanced-modal {
    width: 100% !important;
    max-height: 98vh !important;
    margin: 0 !important;
    border-radius: 8px !important;
  }

  .enhanced-modal-header {
    padding: 0.75rem 1rem !important;
  }

  .enhanced-modal-header .modal-title {
    font-size: 1.2rem;
  }

  .enhanced-modal-header .modal-icon {
    width: 35px;
    height: 35px;
    font-size: 1rem;
  }

  .enhanced-modal-body {
    padding: 0.75rem !important;
    max-height: calc(98vh - 80px) !important;
  }

  .discount-details-grid {
    gap: 0.5rem;
  }

  .detail-card .card-header {
    padding: 0.5rem 0.75rem;
    font-size: 0.9rem;
  }

  .detail-card .card-content {
    padding: 0.75rem;
  }

  .status-badge {
    padding: 0.5rem 0.75rem;
    font-size: 0.9rem;
  }

  .amount-row {
    padding: 0.4rem;
    font-size: 0.85rem;
  }

  .amount-value.final-amount {
    font-size: 1rem;
  }

  .order-items-table th,
  .order-items-table td {
    padding: 0.4rem 0.5rem;
    font-size: 0.8rem;
  }
}

/* ============================== */
/* Additional Enhancements */
/* ============================== */

/* Manager Decision Card */
.manager-decision-card .card-header i {
  color: #6f42c1 !important;
}

.action-badge {
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.5rem 0.75rem;
  border-radius: 6px;
  font-weight: 600;
  font-size: 0.9rem;
}

.action-badge.approved {
  background: linear-gradient(135deg, #d4edda, #c3e6cb);
  color: #155724;
  border: 1px solid #c3e6cb;
}

.action-badge.rejected {
  background: linear-gradient(135deg, #f8d7da, #f5c6cb);
  color: #721c24;
  border: 1px solid #f5c6cb;
}

/* Order Items Table Enhancements */
.order-items-table {
  border-radius: 8px;
  overflow: hidden;
}

.order-items-table thead th {
  background: linear-gradient(135deg, #495057, #6c757d);
  color: white;
  font-weight: 600;
  text-align: center;
}

.order-items-table tbody tr {
  transition: background-color 0.2s ease;
}

.order-items-table tbody tr:nth-child(even) {
  background: #f8f9fa;
}

.order-items-table tbody tr:hover {
  background: #e3f2fd !important;
  transform: scale(1.01);
}

.order-items-table tfoot tr {
  background: linear-gradient(135deg, #28a745, #20c997);
  color: white;
  font-weight: 700;
}

.order-items-table tfoot td {
  color: white;
  font-weight: 700;
}

.item-size, .item-notes {
  font-size: 0.8rem;
  color: #6c757d;
  font-style: italic;
  margin-top: 0.25rem;
}

/* Enhanced Animations */
.discount-details-modal.enhanced-modal {
  animation: modalSlideIn 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
}

@keyframes modalSlideIn {
  from {
    opacity: 0;
    transform: translateY(-50px) scale(0.95);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

.detail-card {
  animation: cardFadeIn 0.5s ease-out;
}

@keyframes cardFadeIn {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Enhanced Status Badges */
.status-badge {
  animation: badgePulse 2s infinite;
}

@keyframes badgePulse {
  0%, 100% {
    box-shadow: 0 0 0 0 rgba(0, 123, 255, 0.4);
  }
  50% {
    box-shadow: 0 0 0 10px rgba(0, 123, 255, 0);
  }
}

.status-badge.status-approved {
  animation: badgePulseGreen 2s infinite;
}

@keyframes badgePulseGreen {
  0%, 100% {
    box-shadow: 0 0 0 0 rgba(40, 167, 69, 0.4);
  }
  50% {
    box-shadow: 0 0 0 10px rgba(40, 167, 69, 0);
  }
}

.status-badge.status-rejected {
  animation: badgePulseRed 2s infinite;
}

@keyframes badgePulseRed {
  0%, 100% {
    box-shadow: 0 0 0 0 rgba(220, 53, 69, 0.4);
  }
  50% {
    box-shadow: 0 0 0 10px rgba(220, 53, 69, 0);
  }
}

/* Enhanced Close Button */
.enhanced-close-btn {
  animation: closeButtonFloat 3s ease-in-out infinite;
}

@keyframes closeButtonFloat {
  0%, 100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-3px);
  }
}

/* Print Styles */
@media print {
  .enhanced-modal-overlay {
    background: white !important;
    backdrop-filter: none !important;
  }

  .discount-details-modal.enhanced-modal {
    box-shadow: none !important;
    max-width: 100% !important;
    width: 100% !important;
  }

  .enhanced-close-btn {
    display: none !important;
  }

  .enhanced-modal-header {
    background: #f8f9fa !important;
    color: #495057 !important;
  }

  .detail-card {
    break-inside: avoid;
    page-break-inside: avoid;
  }
}

/* ============================== */
/* Order Details Modal Enhancements */
/* ============================== */

/* Fix Order Modal Positioning */
.modal-backdrop {
  position: fixed !important;
  top: 0 !important;
  left: 0 !important;
  right: 0 !important;
  bottom: 0 !important;
  background: rgba(0, 0, 0, 0.6) !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  z-index: 1000000 !important;
  padding: 1rem !important;
}

.modal.show {
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
}

.modal-dialog {
  max-width: 900px !important;
  width: 90% !important;
  margin: 0 !important;
}

.modal-content {
  max-height: 85vh !important;
  overflow: hidden !important;
  border-radius: 12px !important;
  box-shadow: 0 25px 80px rgba(0, 0, 0, 0.25) !important;
}

.modal-body {
  max-height: calc(85vh - 120px) !important;
  overflow-y: auto !important;
  padding: 1.5rem !important;
}

/* Order Modal Responsive */
@media (max-width: 768px) {
  .modal-backdrop {
    padding: 0.5rem !important;
  }

  .modal-dialog {
    width: 98% !important;
    max-height: 95vh !important;
  }

  .modal-content {
    max-height: 95vh !important;
  }

  .modal-body {
    max-height: calc(95vh - 100px) !important;
    padding: 1rem !important;
  }
}

@media (max-width: 480px) {
  .modal-backdrop {
    padding: 0.25rem !important;
  }

  .modal-dialog {
    width: 100% !important;
    max-height: 98vh !important;
  }

  .modal-content {
    max-height: 98vh !important;
    border-radius: 8px !important;
  }

  .modal-body {
    max-height: calc(98vh - 80px) !important;
    padding: 0.75rem !important;
  }
}



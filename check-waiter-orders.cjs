const mongoose = require('mongoose');
require('dotenv').config();

const orderSchema = new mongoose.Schema({}, { strict: false });
const Order = mongoose.model('Order', orderSchema);

async function checkOlderOrders() {
  try {
    await mongoose.connect(process.env.MONGODB_URI);
    console.log('🔗 اتصال بقاعدة البيانات...');
    
    // البحث عن طلبات أقدم تحتوي على waiterName
    const ordersWithWaiter = await Order.find({ 
      waiterName: { $exists: true, $ne: null, $ne: '' }
    }).limit(10).sort({ createdAt: -1 });
    
    console.log(`📋 عرض آخر 10 طلبات بها اسماء النادلين:`);
    
    if (ordersWithWaiter.length === 0) {
      console.log('❌ لا توجد طلبات تحتوي على أسماء النادلين!');
      
      // دعني أفحص أي شكل آخر من البيانات
      const sampleOrders = await Order.find({}).limit(5);
      console.log('\n🔍 عرض عينة من الطلبات مع كل الحقول:');
      sampleOrders.forEach((order, index) => {
        console.log(`${index + 1}. Order:`, JSON.stringify(order.toObject(), null, 2));
        console.log('---');
      });
    } else {
      ordersWithWaiter.forEach((order, index) => {
        console.log(`${index + 1}. ID: ${order._id}`);
        console.log(`   Status: ${order.status}`);
        console.log(`   Waiter Name: ${order.waiterName}`);
        console.log(`   Waiter ID: ${order.waiterId || 'غير محدد'}`);
        console.log(`   Total: ${order.totalPrice || order.totals?.total || 'غير محدد'}`);
        console.log(`   Created: ${order.createdAt}`);
        console.log('   ---');
      });
    }
    
    process.exit(0);
  } catch (error) {
    console.error('❌ خطأ:', error);
    process.exit(1);
  }
}

checkOlderOrders();

// Unified Validation Middleware
// ميدلوير موحد للتحقق من البيانات

const { sendValidationError } = require('./unifiedResponse');

/**
 * Validate required fields
 */
const validateRequired = (fields) => {
  return (req, res, next) => {
    const missingFields = [];
    
    for (const field of fields) {
      const value = req.body[field];
      
      if (value === undefined || value === null || value === '') {
        missingFields.push(field);
      }
      
      // Check for string fields that are only whitespace
      if (typeof value === 'string' && value.trim() === '') {
        missingFields.push(field);
      }
    }
    
    if (missingFields.length > 0) {
      return res.status(400).json({
        success: false,
        message: `الحقول التالية مطلوبة: ${missingFields.join(', ')}`,
        error: 'MISSING_REQUIRED_FIELDS',
        details: { missingFields },
        timestamp: new Date().toISOString()
      });
    }
    
    next();
  };
};

/**
 * Validate product data (mobile-friendly)
 */
const validateProduct = (req, res, next) => {
  const { name, price, category } = req.body;
  
  // Validate name (flexible for mobile)
  const trimmedName = typeof name === 'string' ? name.trim() : '';
  if (!trimmedName) {
    return sendValidationError(res, 'اسم المنتج', name, 'نص غير فارغ');
  }
  
  // Validate price (flexible for mobile input)
  let numericPrice;
  if (typeof price === 'string') {
    // Remove non-numeric characters except dots for mobile inputs
    numericPrice = parseFloat(price.replace(/[^\d.]/g, ''));
  } else {
    numericPrice = parseFloat(price);
  }
  
  if (!numericPrice || isNaN(numericPrice) || numericPrice <= 0) {
    return sendValidationError(res, 'السعر', price, 'رقم أكبر من صفر');
  }
  
  // Validate category
  const categoryId = typeof category === 'string' ? category.trim() : category;
  if (!categoryId) {
    return sendValidationError(res, 'الفئة', category, 'معرف فئة صحيح');
  }
  
  // Add cleaned data to request
  req.validatedData = {
    name: trimmedName,
    price: numericPrice,
    category: categoryId,
    description: req.body.description || '',
    image: req.body.image || '/images/default-product.jpg'
  };
  
  next();
};

/**
 * Validate order data
 */
const validateOrder = (req, res, next) => {
  const { items, orderType, tableNumber } = req.body;
  
  // Validate items
  if (!Array.isArray(items) || items.length === 0) {
    return sendValidationError(res, 'عناصر الطلب', items, 'مصفوفة غير فارغة');
  }
  
  // Validate each item
  for (let i = 0; i < items.length; i++) {
    const item = items[i];
    
    if (!item.productId) {
      return sendValidationError(res, `المنتج في العنصر ${i + 1}`, item.productId, 'معرف منتج');
    }
    
    const quantity = parseInt(item.quantity);
    if (!quantity || quantity <= 0) {
      return sendValidationError(res, `الكمية في العنصر ${i + 1}`, item.quantity, 'رقم أكبر من صفر');
    }
    
    // Update with parsed quantity
    items[i].quantity = quantity;
  }
  
  // Validate order type
  const validOrderTypes = ['dine-in', 'takeaway', 'delivery'];
  if (!validOrderTypes.includes(orderType)) {
    return sendValidationError(res, 'نوع الطلب', orderType, 'dine-in, takeaway, أو delivery');
  }
  
  // Validate table number for dine-in orders
  if (orderType === 'dine-in') {
    const tableNum = parseInt(tableNumber);
    if (!tableNum || tableNum <= 0) {
      return sendValidationError(res, 'رقم الطاولة', tableNumber, 'رقم أكبر من صفر');
    }
    req.body.tableNumber = tableNum;
  }
  
  next();
};

/**
 * Validate category data
 */
const validateCategory = (req, res, next) => {
  const { name, icon, color } = req.body;
  
  // Validate name
  const trimmedName = typeof name === 'string' ? name.trim() : '';
  if (!trimmedName) {
    return sendValidationError(res, 'اسم الفئة', name, 'نص غير فارغ');
  }
  
  // Validate icon (optional but if provided should be string)
  if (icon && typeof icon !== 'string') {
    return sendValidationError(res, 'أيقونة الفئة', icon, 'نص');
  }
  
  // Validate color (optional but if provided should be valid hex)
  if (color) {
    const hexColorRegex = /^#([A-Fa-f0-9]{6}|[A-Fa-f0-9]{3})$/;
    if (!hexColorRegex.test(color)) {
      return sendValidationError(res, 'لون الفئة', color, 'لون hex صحيح مثل #FF5733');
    }
  }
  
  // Add cleaned data
  req.validatedData = {
    name: trimmedName,
    icon: icon || 'fas fa-utensils',
    color: color || '#007bff'
  };
  
  next();
};

/**
 * Validate user registration data
 */
const validateUserRegistration = (req, res, next) => {
  const { name, username, password, role } = req.body;
  
  // Validate name
  const trimmedName = typeof name === 'string' ? name.trim() : '';
  if (!trimmedName || trimmedName.length < 2) {
    return sendValidationError(res, 'الاسم', name, 'نص لا يقل عن حرفين');
  }
  
  // Validate username
  const trimmedUsername = typeof username === 'string' ? username.trim() : '';
  if (!trimmedUsername || trimmedUsername.length < 3) {
    return sendValidationError(res, 'اسم المستخدم', username, 'نص لا يقل عن 3 أحرف');
  }
  
  // Validate password
  if (!password || password.length < 6) {
    return sendValidationError(res, 'كلمة المرور', '***', 'نص لا يقل عن 6 أحرف');
  }
  
  // Validate role
  const validRoles = ['admin', 'manager', 'waiter', 'chef'];
  if (!validRoles.includes(role)) {
    return sendValidationError(res, 'الدور', role, 'admin, manager, waiter, أو chef');
  }
  
  // Add cleaned data
  req.validatedData = {
    name: trimmedName,
    username: trimmedUsername,
    password: password,
    role: role
  };
  
  next();
};

/**
 * Validate MongoDB ObjectId
 */
const validateObjectId = (paramName = 'id') => {
  return (req, res, next) => {
    const id = req.params[paramName];
    const objectIdRegex = /^[0-9a-fA-F]{24}$/;
    
    if (!objectIdRegex.test(id)) {
      return sendValidationError(res, 'المعرف', id, 'معرف MongoDB صحيح');
    }
    
    next();
  };
};

/**
 * Validate pagination parameters
 */
const validatePagination = (req, res, next) => {
  const page = parseInt(req.query.page) || 1;
  const limit = parseInt(req.query.limit) || 10;
  
  // Ensure reasonable limits
  const finalPage = Math.max(1, page);
  const finalLimit = Math.min(Math.max(1, limit), 999999); // إزالة حد الـ 100 عنصر - لا حدود للصفحات
  
  req.pagination = {
    page: finalPage,
    limit: finalLimit,
    skip: (finalPage - 1) * finalLimit
  };
  
  next();
};

module.exports = {
  validateRequired,
  validateProduct,
  validateOrder,
  validateCategory,
  validateUserRegistration,
  validateObjectId,
  validatePagination
};

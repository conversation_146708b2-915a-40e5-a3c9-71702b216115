import { APP_CONFIG } from './config/app.config';
import { authenticatedGet, authenticatedPost, authenticatedPut, getCurrentUser } from './utils/apiHelpers';
import { convertArabicToEnglishNumbers, parseTableNumber, validateTableNumber } from './utils/numberUtils';
import { useToast } from './hooks/useToast';
import { getOrderFinalPrice } from './utils/orderHelpers';
import { globalPerformanceOptimizer, globalAPIHandler } from './utils/performanceOptimizer';
import { notificationSound } from './utils/notificationSound';
import { backgroundNotificationService } from './utils/backgroundNotifications';
import { smartMultiFieldSearch, smartSearchAndSort } from './utils/arabicSearch';
import React, { useState, useEffect, useCallback, useRef } from 'react';
// استدعاء ملفات CSS الجديدة المنظمة فقط
import './styles/waiter/WaiterDashboardScreen.css';
import './styles/components/NavigationBarComponent.css';
import './styles/waiter/DiscountModal.css';
import './styles/waiter/WaiterOrderDetailsModal.css';
// استيراد جميع ملفات CSS الخاصة بشاشات النادل
import './styles/waiter/index.css';
import socket from './socket';

// استيراد الشاشات المنفصلة للنادل
import {
  WaiterDrinksScreen,
  WaiterOrdersScreen,
  WaiterTablesScreen,
  WaiterCartScreen,
  WaiterDiscountsScreen
} from './screens';

// Define interfaces used in the component
interface MenuItem {
  _id: string;
  name: string;
  price: number;
  description?: string;
  categories?: string[];
  categoryDetails?: Category[];
  available: boolean;
  notes?: string; 
}

interface CartItem extends MenuItem {
  quantity: number;
}

interface Category {
  _id: string;
  name: string;
  color: string;
  icon?: string;
}

interface OrderItem {
  product: string;
  name: string;
  quantity: number;
  price: number;
  notes?: string;
  id?: string;
}

interface Order {
  _id: string;
  orderNumber: string;
  waiterName: string;
  waiterId?: string;
  chefName?: string;
  chefId?: string;
  items: OrderItem[];
  totalPrice: number;
  tableNumber: string;
  customerName?: string;
  status: 'pending' | 'preparing' | 'ready' | 'delivered' | 'cancelled';
  createdAt: string;
  tableAccountId?: string;
  discountStatus?: 'pending' | 'approved' | 'rejected';
  discountAmount?: number;
  discountApplied?: number;
  discountReason?: string;
}

interface TableAccount {
  _id: string;
  tableNumber: string;
  waiterId: string;
  waiterName: string;
  waiter?: {
    id: string;
    name?: string;
    username?: string;
  };
  orders: Order[];
  totalAmount: number;
  isOpen: boolean;
  status: 'active' | 'closed';
  createdAt: string;
  updatedAt: string;
  lastActivityAt?: string;
  customerName?: string; 
  paymentMethod?: string;
  discountApplied?: number;
  notes?: string;
}

export default function WaiterDashboard() {  // State definitions
  const [menuItems, setMenuItems] = useState<MenuItem[]>([]);
  const [cart, setCart] = useState<CartItem[]>([]);
  const [tableNumber, setTableNumber] = useState('');
  const [customerName, setCustomerName] = useState('');
  const [loading, setLoading] = useState(false);
  const [loadingMenuItems, setLoadingMenuItems] = useState(false);
  const [categories, setCategories] = useState<Category[]>([]);
  const [selectedCategory, setSelectedCategory] = useState<string>('all');
  const [searchTerm, setSearchTerm] = useState('');
  
  // Toast notifications
  const {
    toasts,
    removeToast,
    showSuccess,
    showError,
    showInfo
  } = useToast();

  // Current user
  const currentUser = getCurrentUser();
  
    // Orders state
  const [orders, setOrders] = useState<Order[]>([]);
  const [orderStatusFilter, setOrderStatusFilter] = useState<'all' | 'pending' | 'preparing' | 'ready' | 'delivered'>('all');
  const [orderSearchTerm, setOrderSearchTerm] = useState('');    // Screen state
  const [currentScreen, setCurrentScreen] = useState<'drinks' | 'orders' | 'tables' | 'cart' | 'discounts' | 'statistics'>('drinks');
    // Loading states for each screen
  const [screenLoadingStates, setScreenLoadingStates] = useState({
    drinks: false,
    orders: false,
    tables: false,
    cart: false,
    discounts: false,
    statistics: false
  });
  
  // Data loaded states to avoid re-fetching
  const [dataLoaded, setDataLoaded] = useState({
    menuItems: false,
    categories: false,
    orders: false,
    tableAccounts: false,
    discountRequests: false
  });

  // Discount request states
  const [showDiscountModal, setShowDiscountModal] = useState(false);
  const [selectedOrderForDiscount, setSelectedOrderForDiscount] = useState<Order | null>(null);
  const [discountAmount, setDiscountAmount] = useState('');
  const [discountReason, setDiscountReason] = useState('');

  // Discount requests screen states
  const [discountRequests, setDiscountRequests] = useState<any[]>([]);
  const [discountStatusFilter, setDiscountStatusFilter] = useState<'all' | 'pending' | 'approved' | 'rejected'>('all');
  const [loadingDiscountRequests, setLoadingDiscountRequests] = useState(false);

  // Table account states
  const [showTableAccountModal, setShowTableAccountModal] = useState(false);
  const [tableAccounts, setTableAccounts] = useState<TableAccount[]>([]);
  const [existingTableAccount, setExistingTableAccount] = useState<any>(null);

  // Order details modal states
  const [showOrderDetailsModal, setShowOrderDetailsModal] = useState(false);
  const [selectedOrderDetails, setSelectedOrderDetails] = useState<Order | null>(null);

  // Table details modal states
  const [showTableDetailsModal, setShowTableDetailsModal] = useState(false);
  const [selectedTableAccountDetails, setSelectedTableAccountDetails] = useState<TableAccount | null>(null);  // إزالة cache للبيانات الديناميكية - التحديث الفوري فقط
  // Cache سيستخدم فقط للملفات الثابتة (CSS، الصور، المكتبات)
  const [lastFetch, setLastFetch] = useState({
    orders: 0,
    tableAccounts: 0,
    categories: 0,
    menuItems: 0
  });

  // إلغاء CACHE_DURATION للبيانات - التحديث الفوري دائماً

  // Rate limiting refs
  const ordersFetching = useRef(false);
  const tableAccountsFetching = useRef(false);

  // Sidebar states
  const [sidebarOpen, setSidebarOpen] = useState(window.innerWidth > 1024);
  const [isMobile, setIsMobile] = useState(window.innerWidth <= 1024);

  // States for item notes
  const [showNotesModal, setShowNotesModal] = useState(false);
  const [selectedItemForNotes, setSelectedItemForNotes] = useState<MenuItem | null>(null);
  const [itemNotes, setItemNotes] = useState('');  // Socket.IO state for real-time updates - استخدام الـ socket المستورد
  const [isConnected, setIsConnected] = useState(false);
  const [connectionError, setConnectionError] = useState<string | null>(null);

  // Add error boundary for API calls
  const handleAPIError = useCallback((error: any, context: string) => {
    console.error(`❌ خطأ في ${context}:`, error);
    setConnectionError(`خطأ في ${context}`);
    
    // Clear error after 5 seconds
    setTimeout(() => {
      setConnectionError(null);
    }, 5000);
    
    // Handle specific error types
    if (error?.message?.includes('message port closed')) {
      console.log('🔄 إعادة محاولة الاتصال بعد خطأ message port...');
      // Retry after short delay
      setTimeout(() => {
        if (context.includes('طلبات')) {
          fetchOrders(true);
        } else if (context.includes('طاولات')) {
          fetchTableAccounts(true);
        }
      }, 2000);
    }  }, []);

  // تنظيف localStorage من البيانات القديمة - إبقاء cache للملفات الثابتة فقط
  useEffect(() => {
    // حذف cache البيانات الديناميكية من localStorage
    const itemsToRemove = [
      'waiter_menu_items',
      'waiter_menu_items_timestamp',
      'waiter_categories',
      'waiter_categories_timestamp',
      'waiter_orders_cache',
      'waiter_tables_cache'
    ];
    
    itemsToRemove.forEach(item => {
      if (localStorage.getItem(item)) {
        localStorage.removeItem(item);
        console.log(`🧹 تم حذف cache قديم: ${item}`);
      }
    });
    
    console.log('✅ تم تنظيف localStorage - البيانات ستُجلب فوراً من الخادم');
  }, []);

  // تطبيق الألوان الديناميكية على العناصر
  useEffect(() => {
    // تطبيق ألوان حدود أزرار الفئات
    document.querySelectorAll('[data-border-color]').forEach((button) => {
      const borderColor = button.getAttribute('data-border-color');
      if (borderColor) (button as HTMLElement).style.borderColor = borderColor;
    });

    // تطبيق ألوان خلفية علامات الفئات
    document.querySelectorAll('[data-bg-color]').forEach((tag) => {
      const bgColor = tag.getAttribute('data-bg-color');
      if (bgColor) (tag as HTMLElement).style.backgroundColor = bgColor;
    });
  }, [categories, menuItems]);

  // تهيئة خدمة الإشعارات للصفحات غير النشطة
  useEffect(() => {
    const initializeNotifications = async () => {
      try {
        console.log('🔔 تهيئة خدمة الإشعارات للصفحات غير النشطة...');
        const success = await backgroundNotificationService.initialize();
        
        if (success) {
          console.log('✅ تم تهيئة خدمة الإشعارات بنجاح');
          
          // اختبار الخدمة
          setTimeout(() => {
            backgroundNotificationService.sendNotification({
              title: 'نظام القهوة جاهز',
              body: 'تم تفعيل الإشعارات للصفحات غير النشطة',
              icon: '/coffee-logo.svg',
              tag: 'system-ready',
              sound: true
            });
          }, 2000);
        } else {
          console.warn('⚠️ فشل في تهيئة خدمة الإشعارات');
        }
      } catch (error) {
        console.error('❌ خطأ في تهيئة خدمة الإشعارات:', error);
      }
    };

    initializeNotifications();

    // تنظيف عند إلغاء المكون
    return () => {
      backgroundNotificationService.cleanup();
    };
  }, []);

  // Handle resize effect
  useEffect(() => {
    const handleResize = () => {
      const mobile = window.innerWidth <= 1024;
      setIsMobile(mobile);
      if (!mobile) setSidebarOpen(true);
    };
    window.addEventListener('resize', handleResize);
    return () => window.removeEventListener('resize', handleResize);
  }, []);

  // Sidebar control functions
  const openSidebar = () => {
    setSidebarOpen(true);
    if (isMobile) {
      document.body.classList.add('sidebar-open');
    }
  };
  
  const closeSidebar = () => {
    setSidebarOpen(false);
    if (isMobile) {
      document.body.classList.remove('sidebar-open');
    }
  };
  
  const toggleSidebar = () => {
    const newState = !sidebarOpen;
    setSidebarOpen(newState);
    if (isMobile) {
      if (newState) {
        document.body.classList.add('sidebar-open');
      } else {
        document.body.classList.remove('sidebar-open');
      }
    }
  };

  // Session management functions
  const saveCartToSession = () => {
    const sessionData = {
      cart,
      tableNumber,
      customerName,
      timestamp: new Date().toISOString()
    };
    localStorage.setItem('waiterSession', JSON.stringify(sessionData));
    localStorage.setItem('waiterCart', JSON.stringify(cart));
  };

  const loadCartFromSession = () => {
    try {
      const savedCart = localStorage.getItem('waiterCart');
      if (savedCart) {
        const cartData = JSON.parse(savedCart);
        if (cartData && cartData.length > 0) {
          setCart(cartData);
        }
      }

      const sessionData = localStorage.getItem('waiterSession');
      if (sessionData) {
        const { cart: savedCart, tableNumber: savedTable, customerName: savedCustomer, timestamp } = JSON.parse(sessionData);
        const sessionAge = new Date().getTime() - new Date(timestamp).getTime();
        const maxAge = 4 * 60 * 60 * 1000; // 4 hours

        if (sessionAge < maxAge) {
          if (!savedCart || savedCart.length === 0) {
            if (savedCart && savedCart.length > 0 && (!cart || cart.length === 0)) {
              setCart(savedCart);
            }
          }
          setTableNumber(savedTable || '');
          setCustomerName(savedCustomer || '');
          return true;
        }
      }
    } catch (error) {
      console.error('Error loading session:', error);
    }
    return false;
  };  // Fetch functions مع تحديث فوري - بدون cache للبيانات الديناميكية
  const fetchOrders = useCallback(async (forceRefresh = false) => {
    return globalAPIHandler.makeRequest(
      'fetch-orders',
      async () => {
        // إزالة logic cache - جلب البيانات فوراً دائماً
        console.log('� جلب الطلبات فوراً من الخادم...');

        // حماية من الطلبات المتكررة
        if (ordersFetching.current) {
          console.log('⏳ طلب آخر قيد التنفيذ للطلبات');
          return;
        }// الحصول على معرف النادل الثابت من مصادر موثوقة
        const userData = JSON.parse(localStorage.getItem('user') || '{}');
        const storedWaiterId = localStorage.getItem('waiterId');
        const waiterId = storedWaiterId || userData._id || userData.id;
        
        console.log('👤 معرف النادل المستخدم للفلترة:', { 
          storedWaiterId,
          userDataId: userData._id,
          userDataId2: userData.id,
          finalWaiterId: waiterId,
          userDataFull: userData
        });
        
        if (!waiterId) {
          console.error('❌ لا يمكن تحديد معرف النادل - لن يتم عرض أي طلبات');
          console.error('❌ معلومات التخزين المحلي:', {
            user: localStorage.getItem('user'),
            waiterId: localStorage.getItem('waiterId'),
            username: localStorage.getItem('username'),
            waiterName: localStorage.getItem('waiterName')
          });
          setOrders([]);
          return;
        }try {
          ordersFetching.current = true;
          setLoading(true);
            console.log('👤 جلب جميع الطلبات للفلترة محلياً:', { 
            waiterId: waiterId
          });
          
          // تقليل وقت الانتظار أكثر
          await new Promise(resolve => setTimeout(resolve, 100));
          
          // جلب جميع الطلبات بدون limit لضمان عدم فقدان أي طلب
          const apiUrl = '/api/v1/orders?limit=999999';
            console.log('🔗 جلب جميع الطلبات من:', apiUrl);
            const response = await authenticatedGet(apiUrl);
            const data = response?.data || response;
          
          // الحصول على معرف النادل من مصادر متعددة للمقارنة
          const currentWaiterName = localStorage.getItem('username') || 'waiter';
          const currentWaiterUsername = localStorage.getItem('username');
          
          // فلترة محلية بالاعتماد على waiterId و waiterName للتأكد من أن الطلبات للنادل الحالي
          const waiterOrders = Array.isArray(data) ? data.filter(order => {
            // الفلترة بطرق متعددة لضمان عدم فقدان الطلبات
            const isCurrentWaiterByid = order.waiterId === waiterId;
            const isCurrentWaiterByName = order.waiterName === currentWaiterName;
            const isCurrentWaiterByUsername = order.waiterName === currentWaiterUsername;
            
            const isCurrentWaiterOrder = isCurrentWaiterByid || isCurrentWaiterByName || isCurrentWaiterByUsername;
            
            console.log('🔍 فحص طلب:', {
              orderId: order._id,
              orderWaiterId: order.waiterId,
              orderWaiterName: order.waiterName,
              currentWaiterId: waiterId,
              currentWaiterName: currentWaiterName,
              isMatchById: isCurrentWaiterByid,
              isMatchByName: isCurrentWaiterByName,
              isMatchByUsername: isCurrentWaiterByUsername,
              finalMatch: isCurrentWaiterOrder
            });
            
            // فلترة إضافية للطلبات الحديثة فقط (آخر 7 أيام لتجنب فقدان طلبات)
            const orderDate = new Date(order.createdAt);
            const isRecent = (Date.now() - orderDate.getTime()) < (7 * 24 * 60 * 60 * 1000); // آخر 7 أيام
            
            console.log('📅 فحص تاريخ الطلب:', {
              orderId: order._id?.slice(-6),
              orderDate: orderDate.toISOString(),
              orderAgeHours: Math.round((Date.now() - orderDate.getTime()) / (60 * 60 * 1000)),
              isRecent,
              finalMatch: isCurrentWaiterOrder && isRecent
            });
            
            return isCurrentWaiterOrder && isRecent;
          }) : [];
          
          // ترتيب الطلبات حسب الحالة والوقت
          waiterOrders.sort((a, b) => {
            // الطلبات المعلقة أولاً
            if (a.status === 'pending' && b.status !== 'pending') return -1;
            if (b.status === 'pending' && a.status !== 'pending') return 1;
            
            // ثم الطلبات قيد التحضير
            if (a.status === 'preparing' && b.status !== 'preparing' && b.status !== 'pending') return -1;
            if (b.status === 'preparing' && a.status !== 'preparing' && a.status !== 'pending') return 1;
            
            // ثم حسب الوقت (الأحدث أولاً)
            return new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime();
          });
          
          // إزالة التحديد لعرض جميع طلبات النادل
          // const limitedOrders = waiterOrders.slice(0, 50); // تم إزالة التحديد
          const limitedOrders = waiterOrders; // عرض جميع الطلبات
          
          console.log(`📊 تم جلب ${data.length} طلب، فلترة ${waiterOrders.length} طلب، عرض ${limitedOrders.length} طلب للنادل ${waiterId}`);
          
          // إضافة معلومات إضافية للتشخيص
          if (data.length > 0 && waiterOrders.length === 0) {
            console.log('⚠️ تحليل سبب عدم عرض الطلبات:');
            console.log('📋 عينة من الطلبات المستلمة:', data.slice(0, 3).map(order => ({
              orderId: order._id,
              waiterId: order.waiterId,
              waiterName: order.waiterName,
              status: order.status,
              createdAt: order.createdAt
            })));
            console.log('👤 معلومات النادل الحالي:', {
              waiterId: waiterId,
              username: currentWaiterName,
              storedUsername: localStorage.getItem('username')
            });
          }
            setOrders(limitedOrders);
          setLastFetch(prev => ({ ...prev, orders: Date.now() }));} catch (error) {
          console.error('❌ خطأ في جلب الطلبات:', error);
          handleAPIError(error, 'جلب الطلبات');
          const errorMessage = (error as any)?.message || 'حدث خطأ في جلب الطلبات';// في حالة خطأ 500، حاول استعلام أبسط
          if (error && typeof error === 'object' && 'message' in error && 
              typeof (error as any).message === 'string' &&
              ((error as any).message.includes('500') || (error as any).message.includes('Internal Server Error'))) {
            console.log('🔄 محاولة استعلام مبسط للطلبات...');
              try {
              // استعلام أساسي جداً بدون معاملات
              const fallbackResponse = await authenticatedGet('/api/v1/orders');
              const fallbackData = fallbackResponse?.data || fallbackResponse;
                if (Array.isArray(fallbackData)) {
                // فلترة محلية بطرق متعددة لضمان عدم فقدان الطلبات
                const currentWaiterName = localStorage.getItem('username') || 'waiter';
                const fallbackOrders = fallbackData.filter(order => 
                  order.waiterId === waiterId || 
                  order.waiterName === currentWaiterName
                ); // إزالة قيد الـ 30 طلب - عرض جميع الطلبات
                
                console.log(`📊 تم جلب ${fallbackOrders.length} طلب بالاستعلام المبسط`);
                setOrders(fallbackOrders);                setLastFetch(prev => ({ ...prev, orders: Date.now() }));
                return; // نجح الاستعلام المبسط
              }
            } catch (fallbackError) {
              console.error('❌ فشل الاستعلام المبسط أيضاً:', fallbackError);
            }
          }
          
          showError(errorMessage);
        } finally {
          ordersFetching.current = false;
          setLoading(false);
        }      },
      {
        debounceMs: 800, // تقليل وقت منع الطلبات المتكررة أكثر
        preventDuplicate: true
      }
    );
  }, []); // إزالة dependencies لمنع re-render لا نهائي

  const fetchTableAccounts = useCallback(async (forceRefresh = false) => {
    // إزالة logic cache - جلب البيانات فوراً دائماً
    console.log('� جلب الطاولات فوراً من الخادم...');

    // حماية من الطلبات المتكررة
    if (tableAccountsFetching.current) {
      console.log('⏳ طلب آخر قيد التنفيذ للطاولات');
      return;
    }

    // الحصول على معرف النادل الحالي خارج try block
    const currentWaiterId = localStorage.getItem('waiterId');
    const currentWaiterUsername = localStorage.getItem('username');
    const userData = JSON.parse(localStorage.getItem('user') || '{}');
    const waiterId = currentWaiterId || userData._id || userData.id;

    try {
      tableAccountsFetching.current = true;
      
      console.log('👤 معلومات النادل الحالي:', { 
        waiterId, 
        waiterUsername: currentWaiterUsername,
        userData: userData 
      });
      // الخطوة 1: جلب الطلبات أولاً لربطها بالطاولات
      console.log('📋 جلب الطلبات أولاً لربطها بالطاولات...');
      
      let allOrdersForTables: any[] = [];
      try {
        const ordersResponse = await authenticatedGet('/api/v1/orders');
        const ordersData = ordersResponse?.data || ordersResponse;
        
        if (Array.isArray(ordersData)) {
          // فلترة طلبات النادل الحالي
          // فلترة الطلبات للنادل الحالي باستخدام جميع المطابقات الممكنة
          allOrdersForTables = ordersData.filter(order => {
            const isCurrentWaiterByid = order.waiterId === waiterId;
            const isCurrentWaiterByName = order.waiterName === currentWaiterUsername;
            // إضافة مطابقة بالاسم الكامل إذا كان متوفر
            const isCurrentWaiterByFullName = order.waiterName === currentUser?.name;
            
            return isCurrentWaiterByid || isCurrentWaiterByName || isCurrentWaiterByFullName;
          });
          
          console.log(`📊 تم جلب ${ordersData.length} طلب، فلترة ${allOrdersForTables.length} طلب للنادل`);
          
          // تحديث الطلبات في state أيضاً لضمان التزامن
          setOrders(allOrdersForTables);
        }
      } catch (ordersError) {
        console.error('❌ خطأ في جلب الطلبات:', ordersError);
      }
      
      // تبسيط استعلام الطاولات لتجنب أخطاء الخادم
      let apiUrl = '/api/v1/table-accounts';
      
      // استخدام استعلام بسيط أولاً
      if (waiterId) {
        apiUrl += `?waiterId=${waiterId}`;
      } else if (currentWaiterUsername) {
        apiUrl += `?waiterName=${encodeURIComponent(currentWaiterUsername)}`;
      }
      
      console.log('🔗 جلب الطاولات من:', apiUrl);
      
      const response = await authenticatedGet(apiUrl);

      console.log('📊 استجابة API للطاولات:', response);

      let tableAccountsData: any[] = [];

      // تحسين معالجة الاستجابة
      if (Array.isArray(response)) {
        tableAccountsData = response;
      } else if (response.success && Array.isArray(response.data)) {
        tableAccountsData = response.data;
      } else if (response.data && Array.isArray(response.data)) {
        tableAccountsData = response.data;
      } else {
        console.error('❌ خطأ في تنسيق بيانات الطاولات:', response);
        setTableAccounts([]);
        return;
      }

      // إذا لم نحصل على بيانات طاولات من الAPI، ننشئ الطاولات من الطلبات
      if (tableAccountsData.length === 0 && allOrdersForTables.length > 0) {
        console.log('🔄 لا توجد بيانات طاولات من API، سنقوم بإنشاء الطاولات من الطلبات...');
        
        // إنشاء طاولات من الطلبات الموجودة
        const tablesFromOrders = new Map();
        
        allOrdersForTables.forEach(order => {
          if (order.tableNumber) {
            const tableNum = String(order.tableNumber);
            
            if (!tablesFromOrders.has(tableNum)) {
              tablesFromOrders.set(tableNum, {
                _id: `generated-table-${tableNum}-${waiterId || currentWaiterUsername}`,
                tableNumber: parseInt(order.tableNumber) || order.tableNumber,
                waiterName: order.waiterName || currentWaiterUsername,
                waiterId: order.waiterId || waiterId,
                customerName: order.customerName || 'عميل',
                status: 'active',
                isOpen: true,
                orders: [],
                totalAmount: 0,
                createdAt: order.createdAt,
                updatedAt: order.createdAt,
                isGenerated: true
              });
            }
            
            const table = tablesFromOrders.get(tableNum);
            table.orders.push(order);
            table.totalAmount += order.totalPrice || 0;
            
            // تحديث آخر وقت تحديث
            if (new Date(order.createdAt) > new Date(table.updatedAt)) {
              table.updatedAt = order.createdAt;
            }
          }
        });
        
        tableAccountsData = Array.from(tablesFromOrders.values());
        console.log(`✅ تم إنشاء ${tableAccountsData.length} طاولة من الطلبات:`, 
          tableAccountsData.map(t => `طاولة ${t.tableNumber}: ${t.orders.length} طلب، ${t.totalAmount} جنيه`)
        );
      }

      // فلترة محسنة ومحلية للتأكد من أن الطاولات للنادل الحالي فقط
      const currentWaiterTables = tableAccountsData.filter(account => {
        const isCorrectWaiter = (
          (waiterId && account.waiterId === waiterId) ||
          (currentWaiterUsername && account.waiterName === currentWaiterUsername) ||
          (account.waiter?.username === currentWaiterUsername) ||
          (account.waiter?.id === waiterId)
        );
        
        const isOpenTable = account.isOpen === true && account.status === 'active';
        
        return isCorrectWaiter && isOpenTable;
      });

      console.log(`🏓 تم فلترة ${currentWaiterTables.length} طاولة مفتوحة من أصل ${tableAccountsData.length}`);
      
      // إضافة فحص للطلبات المتاحة
      console.log('📊 حالة الطلبات المتاحة:', {
        totalOrders: orders.length,
        ordersForWaiter: orders.filter(o => o.waiterId === waiterId || o.waiterName === currentWaiterUsername).length,
        sampleOrders: orders.slice(0, 3).map(o => ({
          id: o._id,
          tableNumber: o.tableNumber,
          waiterId: o.waiterId,
          waiterName: o.waiterName,
          status: o.status
        }))
      });

      // تحسين معالجة الطاولات - استخدام معالجة متوازية
      const enrichedTableAccounts = await Promise.all(
        currentWaiterTables.map(async (account) => {
          try {
            // دالة توحيد القيم للمقارنة
            const normalize = (val) => (val ? String(val).trim().toLowerCase() : '');
            
            // استخدام الطلبات المجلبة حديثاً بدلاً من الطلبات القديمة المحملة مسبقاً
            const tableOrders = allOrdersForTables.filter(order => {
              // مطابقة رقم الطاولة (تحويل كلا القيمتين إلى string للمقارنة)
              const orderTableStr = String(order.tableNumber || '').trim();
              const accountTableStr = String(account.tableNumber || '').trim();
              const tableMatch = orderTableStr === accountTableStr;

              // مطابقة النادل بطرق متعددة
              let waiterMatch = false;
              
              // الطريقة الأولى: مطابقة بمعرف النادل
              if (waiterId && order.waiterId) {
                waiterMatch = order.waiterId === waiterId;
              }
              
              // الطريقة الثانية: إذا لم تنجح، حاول بمعرف الطاولة
              if (!waiterMatch && account.waiterId && order.waiterId) {
                waiterMatch = order.waiterId === account.waiterId;
              }
              
              // الطريقة الثالثة: كخيار أخير، استخدم اسم النادل
              if (!waiterMatch && currentWaiterUsername && order.waiterName) {
                waiterMatch = order.waiterName === currentWaiterUsername;
              }

              // الطريقة الرابعة: مطابقة بالاسم الكامل
              if (!waiterMatch && currentUser?.name && order.waiterName === currentUser.name) {
                waiterMatch = true;
              }

              const finalMatch = tableMatch && waiterMatch;
              
              if (finalMatch) {
                console.log(`✅ ربط طلب ${order._id?.slice(-6)} بطاولة ${account.tableNumber}`);
              }

              return finalMatch;
            });

            console.log(`🏓 طاولة ${account.tableNumber}: وجد ${tableOrders.length} طلب من أصل ${allOrdersForTables.length} طلب كلي`);
            
            if (tableOrders.length > 0) {
              console.log(`� تفاصيل الطلبات للطاولة ${account.tableNumber}:`, tableOrders.map(o => ({
                id: o._id,
                orderNumber: o.orderNumber,
                tableNumber: o.tableNumber,
                waiterName: o.waiterName,
                waiterId: o.waiterId,
                status: o.status,
                totalPrice: o.totalPrice,
                createdAt: o.createdAt
              })));
            } else {
              console.log(`⚠️ لم يتم العثور على طلبات للطاولة ${account.tableNumber}`);
              console.log('🔍 جميع الطلبات المتاحة:', allOrdersForTables.map(o => ({
                id: o._id,
                tableNumber: o.tableNumber,
                waiterId: o.waiterId,
                waiterName: o.waiterName
              })));
            }

            // حساب محسن للمبلغ الإجمالي
            const calculatedTotal = tableOrders.reduce((sum, order) => {
              const orderTotal = order.totalPrice || 0;
              return sum + orderTotal;
            }, 0);

            // تحسين بيانات الطاولة
            const enrichedAccount = {
              ...account,
              orders: tableOrders,
              totalAmount: calculatedTotal > 0 ? calculatedTotal : account.totalAmount || 0,
              ordersCount: tableOrders.length,
              lastOrderTime: tableOrders.length > 0 ? 
                Math.max(...tableOrders.map(o => new Date(o.createdAt).getTime())) : 
                new Date(account.updatedAt).getTime()
            };

            return enrichedAccount;
          } catch (error) {
            console.error(`❌ خطأ في معالجة طاولة ${account.tableNumber}:`, error);
            return {
              ...account,
              orders: [],
              totalAmount: account.totalAmount || 0,
              ordersCount: 0,
              lastOrderTime: new Date(account.updatedAt).getTime()
            };
          }
        })
      );

      // ترتيب الطاولات حسب آخر نشاط
      enrichedTableAccounts.sort((a, b) => b.lastOrderTime - a.lastOrderTime);

      // فلترة الطاولات النشطة للنادل الحالي فقط
      const currentWaiterActiveTables = enrichedTableAccounts.filter(account => {
        const isCorrectWaiter = (
          (waiterId && account.waiterId === waiterId) ||
          (currentWaiterUsername && account.waiterName === currentWaiterUsername) ||
          (account.waiter?.username === currentWaiterUsername) ||
          (account.waiter?.id === waiterId) ||
          (currentUser?.name && account.waiterName === currentUser.name)
        );
        
        const isOpenTable = account.isOpen === true && account.status === 'active';
        
        return isCorrectWaiter && isOpenTable;
      });

      console.log(`🎯 النتيجة النهائية: ${currentWaiterActiveTables.length} طاولة نشطة من أصل ${enrichedTableAccounts.length}`);
      
      // عرض ملخص للطاولات النشطة
      currentWaiterActiveTables.forEach(table => {
        console.log(`📋 طاولة ${table.tableNumber}: ${table.ordersCount} طلب، ${(table.totalAmount || 0).toFixed(2)} جنيه`);
      });

      setTableAccounts(currentWaiterActiveTables);
      setOrders(allOrdersForTables); // تحديث الطلبات أيضاً
      setLastFetch(prev => ({ ...prev, tableAccounts: Date.now() }));
      console.log('✅ تم جلب وإثراء الطاولات بنجاح:', currentWaiterActiveTables.length, 'طاولة نشطة');} catch (error) {      console.error('❌ خطأ في جلب حسابات الطاولات:', error);
      handleAPIError(error, 'جلب الطاولات');
      const errorMessage = (error as any)?.message || 'حدث خطأ في جلب حسابات الطاولات';
        // في حالة خطأ 500، حاول استعلام أبسط
      if (error && typeof error === 'object' && 'message' in error && 
          typeof (error as any).message === 'string' &&
          ((error as any).message.includes('500') || (error as any).message.includes('Internal Server Error'))) {
        console.log('🔄 محاولة استعلام مبسط للطاولات...');
          try {
          // استعلام أساسي جداً
          const fallbackResponse = await authenticatedGet('/api/v1/table-accounts');
          const fallbackData = fallbackResponse?.data || fallbackResponse;
          
          let fallbackTableData: any[] = [];
          if (Array.isArray(fallbackData)) {
            fallbackTableData = fallbackData;
          } else if (fallbackData.data && Array.isArray(fallbackData.data)) {
            fallbackTableData = fallbackData.data;
          }
          
          if (fallbackTableData.length > 0) {
            // فلترة محلية للنادل الحالي والطاولات المفتوحة
            const fallbackTables = fallbackTableData.filter((account: any) => {
              const isCorrectWaiter = (
                (waiterId && account.waiterId === waiterId) ||
                (currentWaiterUsername && account.waiterName === currentWaiterUsername)
              );
              const isOpenTable = account.isOpen === true && account.status === 'active';
              return isCorrectWaiter && isOpenTable;
            }); // إزالة قيد الـ 20 طاولة - عرض جميع الطاولات
            
            console.log(`📊 تم جلب ${fallbackTables.length} طاولة بالاستعلام المبسط`);
            setTableAccounts(fallbackTables);
            setLastFetch(prev => ({ ...prev, tableAccounts: Date.now() }));
            return; // نجح الاستعلام المبسط
          }
        } catch (fallbackError) {
          console.error('❌ فشل الاستعلام المبسط للطاولات أيضاً:', fallbackError);
        }
      }
      
      showError(errorMessage);
      setTableAccounts([]);
    } finally {
      tableAccountsFetching.current = false;
    }
  }, []); // إزالة dependencies لمنع re-render لا نهائي

  const fetchMenuItems = useCallback(async (forceRefresh = false) => {
    // إزالة جميع logic cache - جلب البيانات فوراً دائماً من الخادم
    console.log('� جلب المشروبات فوراً من الخادم...');

    try {
      setLoadingMenuItems(true);
      console.log('🍹 بدء تحميل المشروبات من الخادم...');
      const startTime = Date.now();
      
      const response = await authenticatedGet('/api/v1/products');
        // تحسين معالجة البيانات
      const data = response?.data || response;
      let products: MenuItem[] = [];
      
      if (Array.isArray(data)) {
        products = data;
      } else if (data?.products && Array.isArray(data.products)) {
        products = data.products;
      }
      
      // فلترة المنتجات المتاحة فقط
      const availableProducts = products.filter((item: MenuItem) => item.available !== false);
      
      // ربط الفئات بالمنتجات (إضافة categoryDetails)
      const productsWithCategoryDetails = availableProducts.map(product => {
        if (product.categories && Array.isArray(product.categories) && categories.length > 0) {
          const categoryDetails = product.categories
            .map(categoryId => categories.find(cat => cat._id === categoryId))
            .filter(Boolean) as Category[];
          
          return {
            ...product,
            categoryDetails
          };
        }
        return product;
      });
      
      setMenuItems(productsWithCategoryDetails);
      setLastFetch(prev => ({ ...prev, menuItems: Date.now() }));
      
      // لا نحفظ في localStorage - بيانات فورية فقط
      
      const loadTime = Date.now() - startTime;
      console.log(`✅ تم تحميل ${availableProducts.length} مشروب في ${loadTime}ms (فوري من الخادم)`);
      
    } catch (error) {
      console.error('❌ خطأ في جلب المشروبات:', error);
      showError('فشل في تحميل المشروبات. يرجى المحاولة مرة أخرى.');
    } finally {
      setLoadingMenuItems(false);
    }
  }, []); // إزالة dependencies لمنع re-render لا نهائي

const fetchCategories = useCallback(async (forceRefresh = false) => {
    // إزالة logic cache - جلب البيانات فوراً دائماً من الخادم
    console.log('� جلب الفئات فوراً من الخادم...');

    try {
      console.log('📂 بدء تحميل الفئات...');
      const startTime = Date.now();
      
      const response = await authenticatedGet('/api/v1/categories');
        // تحسين معالجة البيانات
      const data = response?.data || response;
      let categories: Category[] = [];
      
      if (Array.isArray(data)) {
        categories = data;
      } else if (data?.categories && Array.isArray(data.categories)) {
        categories = data.categories;
      }
        setCategories(categories);
      setLastFetch(prev => ({ ...prev, categories: Date.now() }));
      
      const loadTime = Date.now() - startTime;
      console.log(`✅ تم تحميل ${categories.length} فئة في ${loadTime}ms (فوري من الخادم)`);
      
    } catch (error) {
      console.error('❌ خطأ في جلب الفئات:', error);
      // إظهار رسالة خطأ للمستخدم
      showError('فشل في تحميل فئات المشروبات. يرجى المحاولة مرة أخرى.');
    }
  }, []); // إزالة dependencies لمنع re-render لا نهائي

  // Fetch discount requests function
  const fetchDiscountRequests = useCallback(async (forceRefresh = false) => {
    console.log('💰 جلب طلبات الخصم فوراً من الخادم...');

    try {
      setLoadingDiscountRequests(true);
      console.log('💰 بدء تحميل طلبات الخصم من الخادم...');
      const startTime = Date.now();
      
      // بناء URL مع فلتر الحالة
      let apiUrl = '/api/v1/discount-requests';
      if (discountStatusFilter !== 'all') {
        apiUrl += `?status=${discountStatusFilter}`;
      }
      
      const response = await authenticatedGet(apiUrl);
      
      // تحسين معالجة البيانات
      const data = response?.data || response;
      let requests: any[] = [];
      
      if (Array.isArray(data)) {
        requests = data;
      } else if (data?.discountRequests && Array.isArray(data.discountRequests)) {
        requests = data.discountRequests;
      }
      
      // فلترة طلبات النادل الحالي فقط
      const currentWaiterId = localStorage.getItem('waiterId');
      const currentWaiterUsername = localStorage.getItem('username');
      
      const waiterRequests = requests.filter(request => {
        return request.requestedBy === currentWaiterId ||
               request.waiterName === currentWaiterUsername ||
               request.requestedBy === currentWaiterUsername;
      });
      
      setDiscountRequests(waiterRequests);
      setLastFetch(prev => ({ ...prev, discountRequests: Date.now() }));
      
      const loadTime = Date.now() - startTime;
      console.log(`✅ تم تحميل ${waiterRequests.length} طلب خصم في ${loadTime}ms (فوري من الخادم)`);
      
    } catch (error) {
      console.error('❌ خطأ في جلب طلبات الخصم:', error);
      showError('فشل في تحميل طلبات الخصم. يرجى المحاولة مرة أخرى.');
      setDiscountRequests([]);
    } finally {
      setLoadingDiscountRequests(false);
    }
  }, [discountStatusFilter]); // إضافة discountStatusFilter للتحديث عند تغيير الفلتر

const checkExistingTableAccount = async (tableNumber: string) => {
    try {
      console.log(`🔍 فحص حالة الطاولة ${tableNumber}...`);
      
      // أولاً: البحث في الطاولات المحملة محلياً (cache)
      const cachedTableAccount = tableAccounts.find(account => 
        account.tableNumber === tableNumber && account.isOpen
      );
      
      if (cachedTableAccount) {
        console.log(`✅ تم العثور على الطاولة ${tableNumber} في الcache`);
        const currentWaiterUsername = localStorage.getItem('username');
        
        return {
          exists: true,
          account: cachedTableAccount,
          tableStatus: 'occupied',
          waiterName: cachedTableAccount.waiterName,
          waiterUsername: cachedTableAccount.waiterName,
          isCurrentWaiter: cachedTableAccount.waiterName === currentWaiterUsername
        };
      }
      
      // ثانياً: البحث عبر API إذا لم توجد في الcache
      console.log(`🌐 البحث عن الطاولة ${tableNumber} عبر API...`);
        // تقليل وقت الانتظار بشكل كبير
      await new Promise(resolve => setTimeout(resolve, 200));
      
      const response = await authenticatedGet(`/api/v1/table-accounts/check?tableNumber=${tableNumber}`);
      const data = response?.data || response;
      
      console.log(`📊 نتيجة فحص الطاولة ${tableNumber}:`, data);
      
      return data;
    } catch (error) {
      console.error(`❌ خطأ في فحص الطاولة ${tableNumber}:`, error);
      let errorMessage = 'خطأ في الشبكة أثناء فحص الطاولة. يرجى المحاولة مرة أخرى.';
      
      if (error instanceof Error && error.message.startsWith('HTTP')) {
        errorMessage = `خطأ في فحص الطاولة: ${error.message}`;
      } else if (typeof error === 'object' && error !== null && 'message' in error) {
        errorMessage = (error as {message: string}).message;
      }
      
      showError(errorMessage);
      return { 
        exists: false, 
        account: null, 
        tableStatus: 'unknown', 
        waiterName: null, 
        waiterUsername: null,
        isCurrentWaiter: false
      };
    }
  };

  // Cart functions
  const addToCart = (item: MenuItem, notes?: string) => {
    const existingItem = cart.find(cartItem => cartItem._id === item._id && cartItem.notes === notes);
    let updatedCart;
    if (existingItem) {
      updatedCart = cart.map(cartItem =>
        cartItem._id === item._id && cartItem.notes === notes
          ? { ...cartItem, quantity: cartItem.quantity + 1 }
          : cartItem
      );
    } else {
      updatedCart = [...cart, { ...item, quantity: 1, notes: notes || '' }];
    }
    setCart(updatedCart);
    localStorage.setItem('waiterCart', JSON.stringify(updatedCart));
    showSuccess(`تم إضافة ${item.name} إلى السلة${notes ? ' مع ملاحظات' : ''}`);
  };

  const updateCartQuantity = (itemId: string, quantity: number) => {
    let updatedCart;
    if (quantity <= 0) {
      updatedCart = cart.filter(item => item._id !== itemId);
    } else {
      updatedCart = cart.map(item =>
        item._id === itemId ? { ...item, quantity } : item
      );
    }
    setCart(updatedCart);
    localStorage.setItem('waiterCart', JSON.stringify(updatedCart));
  };

  const clearCart = () => {
    setCart([]);
    setTableNumber('');
    setCustomerName('');
    localStorage.removeItem('waiterCart');
    localStorage.removeItem('waiterSession');
  };

  // دالة لفتح مودال الملاحظات
  const openNotesModal = (item: MenuItem) => {
    setSelectedItemForNotes(item);
    setItemNotes('');
    setShowNotesModal(true);
  };

  // دالة لإضافة العنصر مع الملاحظات
  const addItemWithNotes = () => {
    if (selectedItemForNotes) {
      addToCart(selectedItemForNotes, itemNotes);
      setShowNotesModal(false);
      setSelectedItemForNotes(null);
      setItemNotes('');
    }
  };

  // دالة لإعادة محاولة اتصال Socket.IO
  const retrySocketConnection = async () => {
    try {
      // أولاً تحقق من حالة الخادم
      showInfo('🔍 فحص حالة الخادم...');

      const controller = new AbortController();
      const timeoutId = setTimeout(() => controller.abort(), 5000);

      const response = await fetch(APP_CONFIG.API.BASE_URL + '/health', {
        method: 'GET',
        signal: controller.signal
      });

      clearTimeout(timeoutId);

      if (response.ok) {
        console.log('✅ الخادم يعمل بشكل طبيعي');
        showSuccess('الخادم متاح - محاولة إعادة الاتصال...');

        if (socket && typeof socket.connect === 'function') {
          console.log('🔄 محاولة إعادة اتصال Socket.IO...');
          socket.connect();
        } else {
          showError('Socket.IO غير متوفر');
        }
      } else {
        showError('الخادم غير متاح حالياً');
      }
    } catch (error) {
      console.error('❌ خطأ في فحص حالة الخادم:', error);
      showError('لا يمكن الوصول للخادم');

      // محاولة الاتصال رغم الخطأ
      if (socket && typeof socket.connect === 'function') {
        console.log('🔄 محاولة إعادة اتصال Socket.IO رغم الخطأ...');
        socket.connect();
      }
    }
  };

  // Order submission function
  const submitOrder = async () => {
    const waiterName = localStorage.getItem('waiterName');
    const waiterId = localStorage.getItem('waiterId');
    const waiterUsername = localStorage.getItem('username');

    if (!waiterName || !waiterId || !waiterUsername) {
      showError('تفاصيل النادل غير موجودة. يرجى تسجيل الخروج ثم الدخول مرة أخرى.');
      return;
    }    if (cart.length === 0) {
      showError('السلة فارغة. يرجى إضافة أصناف قبل إرسال الطلب.');
      return;
    }    if (!tableNumber) {
      showError('يرجى تحديد رقم الطاولة.');
      return;
    }    const parsedTableNumber = parseTableNumber(tableNumber);
    
    if (!parsedTableNumber) {
      showError('رقم الطاولة غير صحيح. يرجى إدخال رقم صحيح.');
      return;
    }

    setLoading(true);
    try {
      const existingAccountResponse = await checkExistingTableAccount(parsedTableNumber.toString());
      const existingAccount = existingAccountResponse.account;
      const tableIsAvailable = !existingAccountResponse.exists || !existingAccount?.isOpen;
      const currentTableWaiterUsername = existingAccountResponse.waiterUsername;

      if (!tableIsAvailable && currentTableWaiterUsername !== waiterUsername) {
        showError(
          `الطاولة رقم ${tableNumber} مُدارة حاليًا بواسطة نادل آخر: ${existingAccountResponse.waiterName || 'غير معروف'}. لا يمكنك إضافة طلبات لهذه الطاولة.`
        );
        setLoading(false);
        return;
      }      // تحضير بيانات الطلب بشكل موحد للحاسوب والهاتف
      const orderData = {
        // بيانات العميل
        customer: {
          name: customerName?.trim() || 'عميل',
          phone: '',
          email: ''
        },
        customerName: customerName?.trim() || 'عميل', // للتوافق مع النظام القديم
          // بيانات الطاولة
        table: {
          number: parsedTableNumber, // استخدام الرقم المحول
          section: 'القسم الرئيسي'
        },
        tableNumber: parsedTableNumber.toString(), // تحويل إلى string للتوافق
        
        // بيانات النادل
        waiterName: localStorage.getItem('username') || 'waiter',
        waiterId: localStorage.getItem('waiterId'),
        waiterUsername: localStorage.getItem('username') || 'waiter',
          // بيانات المنتجات
        items: cart.map(item => ({
          product: item._id, // معرف المنتج
          productId: item._id, // للتوافق مع النظام القديم
          name: item.name,
          productName: item.name, // للتوافق مع النظام القديم
          quantity: item.quantity,
          price: item.price,
          subtotal: item.price * item.quantity,
          notes: item.notes || '',
          specialRequests: item.notes || '', // إضافة specialRequests للطباخ
          modifications: []
        })),
        
        // الإجماليات
        totalPrice: cart.reduce((sum, item) => sum + (item.price * item.quantity), 0),
        totals: {
          subtotal: cart.reduce((sum, item) => sum + (item.price * item.quantity), 0),
          tax: 0,
          discount: 0,
          total: cart.reduce((sum, item) => sum + (item.price * item.quantity), 0)
        },
        
        // تفاصيل إضافية
        orderType: 'dine-in',
        status: 'pending',
        payment: {
          method: 'cash',
          status: 'pending'
        }
      };      console.log('📱 إرسال الطلب - معلومات الجهاز:', {
        userAgent: navigator.userAgent,
        isMobile: /Mobi|Android/i.test(navigator.userAgent),
        screenWidth: window.screen.width,
        deviceType: /Mobi|Android/i.test(navigator.userAgent) ? 'Mobile' : 'Desktop'
      });
      
      console.log('📊 بيانات الطلب قبل الإرسال:', JSON.stringify(orderData, null, 2));
      
      const result = await authenticatedPost('/api/v1/orders', orderData);
      
      console.log('✅ نتيجة إنشاء الطلب:', result);

      // خصم الكميات من المخزون
      try {
        for (const item of cart) {
          await authenticatedPut(`/api/v1/products/${item._id}/reduce-stock`, {
            quantity: item.quantity
          });
        }
        console.log('✅ تم خصم الكميات من المخزون بنجاح');
      } catch (stockError) {
        console.warn('⚠️ خطأ في خصم المخزون:', stockError);
        // لا نريد إيقاف العملية إذا فشل خصم المخزون
      }

      // إرسال إشعار Socket.IO للطباخين والإدارة
      if (socket && socket.connected) {
        const orderNotification = {
          orderId: result._id || `ORD-${Date.now()}`,
          orderNumber: result.orderNumber || `ORD-${Date.now()}`,
          tableNumber: tableNumber,
          waiterName: localStorage.getItem('username') || 'waiter',
          items: cart.map(item => ({
            name: item.name,
            quantity: item.quantity,
            price: item.price,
            notes: item.notes || ''
          })),
          status: 'pending',
          customer: {
            name: customerName || 'عميل',
            tableNumber: tableNumber
          },
          timestamp: new Date().toISOString()
        };

        // إرسال للطباخين
        socket.emit('new-order-notification', orderNotification);
        console.log('📤 تم إرسال إشعار الطلب الجديد للطباخين');
        
        // إرسال للمديرين أيضاً
        socket.emit('order-created', orderNotification);
        console.log('📤 تم إرسال إشعار الطلب للإدارة');
      }

      if (existingAccount) {
        showSuccess(`تم إضافة الطلب إلى حساب الطاولة ${tableNumber} بنجاح!`);
      } else {
        showSuccess(`تم إرسال الطلب وفتح حساب جديد للطاولة ${tableNumber} بنجاح!`);
      }

      clearCart();
      changeScreen('orders');
      // فرض تحديث البيانات بعد إرسال طلب جديد
      setTimeout(() => {
        fetchOrders(true);
        fetchTableAccounts(true);
      }, 1000);    } catch (error) {
      console.error('خطأ في إرسال الطلب:', error);
      
      // معالجة خاصة للخطأ 409 (طاولة محجوزة)
      if ((error as any)?.status === 409 || (error as any)?.response?.status === 409) {
        const errorData = (error as any)?.response?.data || (error as any);
        
        if (errorData?.error === 'TABLE_OCCUPIED_BY_OTHER_WAITER') {
          // استخراج المعلومات الإضافية من الـ backend
          const occupiedByWaiter = errorData?.occupiedByWaiter || 'نادل آخر';
          const currentWaiter = errorData?.currentWaiter || localStorage.getItem('username') || 'أنت';
          const availableTables = errorData?.availableTables || [2, 4, 5, 6, 7, 9, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 24, 25, 26, 27, 28];
          
          // رسالة مخصصة مع تفاصيل أكثر
          showError(
            `🚫 الطاولة رقم ${tableNumber} مفتوحة حالياً\n\n` +
            `👤 النادل المسؤول: ${occupiedByWaiter}\n` +
            `👤 النادل الحالي: ${currentWaiter}\n\n` +
            `💡 خيارات متاحة:\n` +
            `• اختيار طاولة أخرى متاحة\n` +
            `• التواصل مع المدير لتحرير الطاولة\n` +
            `• التحقق من قائمة الطاولات المتاحة`
          );
          
          // عرض قائمة الطاولات المتاحة بعد ثانيتين
          setTimeout(() => {
            if (availableTables.length > 0) {
              showInfo(
                `✅ طاولات متاحة للاستخدام:\n\n` +
                `📋 الأرقام: ${availableTables.join(', ')}\n\n` +
                `💡 نصيحة: الطاولات الخضراء في واجهة النادل متاحة للاستخدام`
              );
            } else {
              showInfo(
                `⚠️ لا توجد طاولات متاحة حالياً\n\n` +
                `💡 يرجى:\n` +
                `• التواصل مع المدير\n` +
                `• انتظار تحرير طاولة\n` +
                `• التحقق من حالة الطاولات`
              );
            }
          }, 2500);
          
        } else {
          // رسالة عامة للخطأ 409
          const errorMessage = errorData?.message || 'هناك تضارب في البيانات';
          showError(`⚠️ تنبيه: ${errorMessage}`);
        }
      } else {
        // معالجة الأخطاء الأخرى
        const errorMessage = (error as any)?.message || 'حدث خطأ في إرسال الطلب';
        showError(errorMessage);
      }
    }
    setLoading(false);
  };

  // Order status update functions
  const handleMarkOrderDelivered = async (orderId: string) => {
    try {
      await authenticatedPut(`/api/v1/orders/${orderId}`, { status: 'delivered' });

      // Update local orders list
      setOrders(prevOrders => 
        prevOrders.map(order =>
          order._id === orderId 
            ? { ...order, status: 'delivered' as const }
            : order
        )
      );

      const deliveredOrder = orders.find(order => order._id === orderId);
      
      showSuccess(`تم تسليم الطلب من الطاولة رقم ${deliveredOrder?.tableNumber || 'غير محدد'} للعميل ${deliveredOrder?.customerName || 'غير محدد'} بنجاح`);
      fetchOrders();
      fetchTableAccounts();
    } catch (error) {
      console.error('خطأ في تحديث حالة الطلب:', error);
      showError('حدث خطأ أثناء تحديث حالة الطلب');
    }
  };

  // إنهاء حساب الطاولة وإغلاقها
  const handleCloseTable = async (account: any) => {
    const confirmClose = window.confirm(
      `هل أنت متأكد من إنهاء حساب الطاولة #${account.tableNumber}؟\n` +
      `المبلغ الإجمالي: ${account.totalAmount?.toFixed(2) || '0.00'} جنيه\n` +
      `عدد الطلبات: ${account.orders?.length || 0}\n\n` +
      `ملاحظة: سيتم إخفاء الطاولة من قائمتك وإرسال طلب للمدير لإنهاء الحساب نهائياً.`
    );

    if (!confirmClose) return;

    try {
      setLoading(true);

      // محاولة إغلاق حساب الطاولة باستخدام endpoints مختلفة      // استخدام API موحد لإغلاق الطاولة
      let closeSuccess = false;
      
      try {
        // استخدام endpoint المخصص للإغلاق فقط (موحد للحاسوب والهاتف)
        const closeResponse = await authenticatedPost(`/api/v1/table-accounts/${account._id}/close`, {
          closedBy: localStorage.getItem('username') || 'waiter',
          waiterName: localStorage.getItem('username') || 'waiter',
          waiterId: localStorage.getItem('waiterId'),
          closedAt: new Date().toISOString()
        });
        
        console.log('✅ تم إغلاق الطاولة بنجاح:', closeResponse);
        closeSuccess = true;
      } catch (closeError) {
        console.error('❌ فشل في إغلاق الطاولة:', closeError);
          // لوغ تفصيلي للمساعدة في التشخيص
        console.error('تفاصيل الخطأ:', {
          accountId: account._id,
          tableNumber: account.tableNumber,
          waiterName: localStorage.getItem('username'),
          error: closeError instanceof Error ? closeError.message : String(closeError)
        });
      }

      if (closeSuccess) {
        showSuccess(`تم إنهاء حساب الطاولة #${account.tableNumber} بنجاح وإغلاقها`);
      } else {
        // إخفاء الطاولة محلياً وإرسال طلب للمدير
        const closedTables = JSON.parse(localStorage.getItem('closedTables') || '[]');
        closedTables.push({
          accountId: account._id,
          tableNumber: account.tableNumber,
          totalAmount: account.totalAmount,
          ordersCount: account.orders?.length || 0,
          closedBy: localStorage.getItem('username') || 'waiter',
          closedAt: new Date().toISOString()
        });
        localStorage.setItem('closedTables', JSON.stringify(closedTables));

        showSuccess(`تم إخفاء الطاولة #${account.tableNumber} من قائمتك. سيتم إرسال طلب للمدير لإنهاء الحساب نهائياً.`);
      }

      // تحديث قائمة الطاولات
      await fetchTableAccounts(true); // فرض التحديث

    } catch (error: any) {
      console.error('خطأ في إنهاء حساب الطاولة:', error);
      showError('حدث خطأ أثناء محاولة إنهاء الحساب. يرجى الاتصال بالمدير.');
    } finally {
      setLoading(false);
    }
  };

  const handleLogout = () => {
    const confirmLogout = window.confirm('هل أنت متأكد من تسجيل الخروج؟');

    if (confirmLogout) {
      localStorage.removeItem('username');
      localStorage.removeItem('jobTitle');
      localStorage.removeItem('isLoggedIn');
      localStorage.removeItem('user');
      localStorage.removeItem('userRole');
      localStorage.removeItem('waiterCart');
      localStorage.removeItem('waiterSession');

      alert('تم تسجيل الخروج بنجاح!');
      window.location.href = '/';
    }
  };

  // Helper functions
  const getCategoryIcon = (categoryName: string) => {
    switch (categoryName.toLowerCase()) {
      case 'قهوة': case 'coffee': return 'fa-coffee';
      case 'شاي': case 'tea': return 'fa-leaf';
      case 'عصائر': case 'juices': return 'fa-glass-whiskey';
      case 'مشروبات باردة': case 'cold drinks': return 'fa-snowflake';
      case 'مشروبات ساخنة': case 'hot drinks': return 'fa-fire';
      case 'حلويات': case 'desserts': return 'fa-birthday-cake';
      case 'وجبات خفيفة': case 'snacks': return 'fa-cookie-bite';
      default: return 'fa-utensils';
    }
  };

  const getOrderStatusArabic = (status: string) => {
    switch (status) {
      case 'pending': return 'معلق';
      case 'preparing': return 'قيد التحضير';
      case 'ready': return 'جاهز';
      case 'delivered': return 'تم التسليم';
      case 'cancelled': return 'ملغي';
      default: return status;
    }
  };

  const getOrderStatusColor = (status: string) => {
    switch (status) {
      case 'pending': return '#f39c12';
      case 'preparing': return '#3498db';
      case 'ready': return '#27ae60';
      case 'delivered': return '#95a5a6';
      case 'cancelled': return '#e74c3c';
      default: return '#95a5a6';
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'pending': return 'fa-clock';
      case 'preparing': return 'fa-utensils';
      case 'ready': return 'fa-check-circle';
      case 'delivered': return 'fa-truck';
      case 'cancelled': return 'fa-times-circle';
      default: return 'fa-question-circle';
    }
  };

  const getStatusText = (status: string) => {
    switch (status) {
      case 'pending': return 'في الانتظار';
      case 'preparing': return 'قيد التحضير';
      case 'ready': return 'جاهز';
      case 'delivered': return 'تم التسليم';
      case 'cancelled': return 'ملغي';
      default: return 'غير محدد';
    }
  };

  // دالة مساعدة لتنسيق التاريخ بشكل آمن
  const formatDate = (dateString: string | undefined) => {
    if (!dateString) return 'غير محدد';

    try {
      const date = new Date(dateString);
      if (isNaN(date.getTime())) {
        return 'غير محدد';
      }
      return date.toLocaleString('ar-EG', {
        year: 'numeric',
        month: '2-digit',
        day: '2-digit',
        hour: '2-digit',
        minute: '2-digit'
      });
    } catch (error) {
      console.error('خطأ في تنسيق التاريخ:', error);
      return 'غير محدد';
    }
  };
  // Initialize data on component mount - تحميل سريع ومحسن للمشروبات
  useEffect(() => {
    const loadData = async () => {
      try {
        // التحقق من وجود token قبل تحميل البيانات
        const token = localStorage.getItem('token') || localStorage.getItem('authToken');
        if (!token) {
          console.log('⚠️ لا يوجد token - تخطي تحميل البيانات');
          return;
        }

        console.log('🔄 تحميل البيانات الأولية بسرعة...');
        
        // تحميل متوازي للفئات والمنتجات لتوفير الوقت
        const [categoriesResult, menuItemsResult] = await Promise.allSettled([
          fetchCategories(),
          fetchMenuItems()
        ]);

        if (categoriesResult.status === 'fulfilled') {
          console.log('✅ تم تحميل الفئات بنجاح');
        } else {
          console.error('❌ فشل تحميل الفئات:', categoriesResult.reason);
        }

        if (menuItemsResult.status === 'fulfilled') {
          console.log('✅ تم تحميل المشروبات بنجاح');
        } else {
          console.error('❌ فشل تحميل المشروبات:', menuItemsResult.reason);
        }        console.log('✅ تم الانتهاء من تحميل البيانات الأولية');
        setDataLoaded(prev => ({ ...prev, menuItems: true, categories: true }));
      } catch (error) {
        console.error('❌ خطأ في تحميل البيانات:', error);
      }
    };

    loadData();
    loadCartFromSession();
  }, []); // تحميل مرة واحدة فقط
  // دالة التحميل الذكي للبيانات حسب الشاشة النشطة
  const loadScreenData = useCallback(async (screen: string) => {
    console.log(`🔄 تحميل بيانات الشاشة: ${screen}`);
    
    // منع تحميل مضاعف للشاشة نفسها
    if (screenLoadingStates[screen as keyof typeof screenLoadingStates]) {
      console.log(`⏳ الشاشة ${screen} قيد التحميل بالفعل`);
      return;
    }
    
    setScreenLoadingStates(prev => ({ ...prev, [screen]: true }));
    
    try {
      switch (screen) {
        case 'drinks':
          // للمشروبات نحتاج القائمة والفئات
          if (!dataLoaded.menuItems || !dataLoaded.categories) {
            await Promise.all([
              !dataLoaded.menuItems ? fetchMenuItems() : Promise.resolve(),
              !dataLoaded.categories ? fetchCategories() : Promise.resolve()
            ]);
            setDataLoaded(prev => ({ ...prev, menuItems: true, categories: true }));
          }
          break;
          
        case 'orders':
          // للطلبات نحتاج بيانات الطلبات فقط
          await fetchOrders(true); // تحميل Live للحصول على أحدث البيانات
          setDataLoaded(prev => ({ ...prev, orders: true }));
          break;
          
        case 'tables':
          // للطاولات نحتاج بيانات الطلبات أولاً ثم الطاولات
          console.log('🔄 تحميل بيانات شاشة الطاولات...');
          // تحميل الطلبات أولاً
          await fetchOrders(true);
          // ثم تحميل الطاولات مع الطلبات المحدثة
          await fetchTableAccounts(true);
          setDataLoaded(prev => ({ ...prev, orders: true, tableAccounts: true }));
          break;
            case 'cart':
          // للسلة لا نحتاج تحميل بيانات إضافية
          console.log('ℹ️ السلة لا تحتاج تحميل بيانات إضافية');
          break;
          
        default:
          console.log(`ℹ️ لا توجد بيانات محددة للشاشة: ${screen}`);
      }
    } catch (error) {
      console.error(`❌ خطأ في تحميل بيانات الشاشة ${screen}:`, error);
      showError(`فشل في تحميل بيانات ${screen}`);
    } finally {
      setScreenLoadingStates(prev => ({ ...prev, [screen]: false }));
    }
  }, []); // إزالة dependencies لمنع re-render لا نهائي
  // دالة تغيير الشاشة مع تحميل البيانات - تحسين لمنع الاستدعاءات اللا نهائية
  const changeScreen = useCallback(async (newScreen: typeof currentScreen) => {
    if (newScreen === currentScreen) return;
    
    console.log(`🔄 تغيير الشاشة من ${currentScreen} إلى ${newScreen}`);
    setCurrentScreen(newScreen);
  }, [currentScreen]);

  // تحميل البيانات عند تغيير الشاشة - معدل لمنع الاستدعاءات اللا نهائية
  useEffect(() => {
    console.log(`📱 تحميل بيانات الشاشة الحالية: ${currentScreen}`);
    
    // استدعاء loadScreenData مباشرة بدلاً من dependency
    const loadData = async () => {
      console.log(`🔄 تحميل بيانات الشاشة: ${currentScreen}`);
      
      // منع تحميل مضاعف للشاشة نفسها
      if (screenLoadingStates[currentScreen as keyof typeof screenLoadingStates]) {
        console.log(`⏳ الشاشة ${currentScreen} قيد التحميل بالفعل`);
        return;
      }
      
      setScreenLoadingStates(prev => ({ ...prev, [currentScreen]: true }));
      
      try {
        switch (currentScreen) {
          case 'drinks':
            // للمشروبات نحتاج القائمة والفئات
            if (!dataLoaded.menuItems || !dataLoaded.categories) {
              await Promise.all([
                !dataLoaded.menuItems ? fetchMenuItems() : Promise.resolve(),
                !dataLoaded.categories ? fetchCategories() : Promise.resolve()
              ]);
              setDataLoaded(prev => ({ ...prev, menuItems: true, categories: true }));
            }
            break;
            
          case 'orders':
            // للطلبات نحتاج بيانات الطلبات فقط
            await fetchOrders(true);
            setDataLoaded(prev => ({ ...prev, orders: true }));
            break;
            
          case 'tables':
            // للطاولات نحتاج بيانات الطلبات أولاً ثم الطاولات
            console.log('🔄 تحميل بيانات شاشة الطاولات...');
            // تحميل الطلبات أولاً
            await fetchOrders(true);
            // ثم تحميل الطاولات مع الطلبات المحدثة
            await fetchTableAccounts(true);
            setDataLoaded(prev => ({ ...prev, orders: true, tableAccounts: true }));
            break;
              
          case 'cart':
            // للسلة لا نحتاج تحميل بيانات إضافية
            console.log('ℹ️ السلة لا تحتاج تحميل بيانات إضافية');
            break;
            
          case 'discounts':
            // لطلبات الخصم نحتاج تحميل طلبات الخصم
            console.log('🔄 تحميل بيانات شاشة طلبات الخصم...');
            await fetchDiscountRequests(true);
            setDataLoaded(prev => ({ ...prev, discountRequests: true }));
            break;
            
          default:
            console.log(`ℹ️ لا توجد بيانات محددة للشاشة: ${currentScreen}`);
        }
      } catch (error) {
        console.error(`❌ خطأ في تحميل بيانات الشاشة ${currentScreen}:`, error);
        showError(`فشل في تحميل بيانات ${currentScreen}`);
      } finally {
        setScreenLoadingStates(prev => ({ ...prev, [currentScreen]: false }));
      }
    };

    loadData();
  }, [currentScreen]); // فقط currentScreen في dependencies

  // تم إزالة التحديث التلقائي للطاولات لتجنب الطلبات المفرطة
  // سيتم تحديث الطاولات يدوياً عند الحاجة فقط
  // Socket.IO connection for real-time updates
  useEffect(() => {
    console.log('🔗 إعداد اتصال Socket.IO للتحديث الفوري');
    console.log('Socket object:', socket);
    console.log('Socket type:', typeof socket);

    // التحقق من وجود socket قبل الاستخدام
    if (!socket || typeof socket.on !== 'function') {
      console.warn('⚠️ Socket.IO غير متوفر، سيتم تعطيل التحديث الفوري');
      setIsConnected(false);
      return;
    }

    try {
      // التحقق من حالة الاتصال الحالية
      if (socket.connected) {
        console.log('✅ Socket.IO متصل بالفعل');
        setIsConnected(true);
      } else {
        console.log('🔄 Socket.IO غير متصل، سيتم الاتصال تلقائياً...');
        setIsConnected(false);
        
        // محاولة الاتصال إذا لم يكن متصلاً
        if (typeof socket.connect === 'function') {
          socket.connect();
        }
      }socket.on('connect', () => {
        console.log('🔗 متصل بالخادم للتحديث الفوري');
        setIsConnected(true);

        // تسجيل النادل في النظام
        const waiterName = localStorage.getItem('username');
        const waiterId = localStorage.getItem('waiterId') || `waiter-${waiterName}`;
        
        if (waiterName) {
          // تسجيل المستخدم مع الدور والمعلومات
          socket.emit('register-user', {
            userId: waiterId,
            role: 'waiter',
            name: waiterName
          });
          
          console.log(`👤 تسجيل النادل: ${waiterName} (${waiterId})`);
        }
      });      socket.on('connect_error', (error: any) => {
        console.error('❌ خطأ في اتصال Socket.IO:', error);
        setIsConnected(false);
        showError('فشل في الاتصال للتحديث الفوري');
      });

    socket.on('disconnect', () => {
      console.log('❌ انقطع الاتصال مع الخادم');
      setIsConnected(false);
      
      // إشعار انقطاع الاتصال
      backgroundNotificationService.sendNotification({
        title: '⚠️ انقطع الاتصال',
        body: 'تم فقدان الاتصال مع الخادم - قد لا تصل الإشعارات',
        icon: '/coffee-logo.svg',
        tag: 'connection-lost',
        urgent: true,
        sound: true
      });
    });

    // تأكيد تسجيل النادل
    socket.on('registration-confirmed', (data: any) => {
      console.log('✅ تم تسجيل النادل بنجاح:', data);
      showSuccess('🔗 متصل للتحديث الفوري');
    });    // استقبال تحديثات الطلبات الفورية (من الطباخ للنادل) - محسن
    socket.on('order-status-update', (orderUpdate: any) => {
      console.log('📦 تحديث حالة طلب فوري من الطباخ:', orderUpdate);
      
      // تحديث الطلب في القائمة المحلية فوراً
      setOrders(prevOrders =>
        prevOrders.map(order =>
          order._id === orderUpdate.orderId 
            ? { ...order, status: orderUpdate.newStatus }
            : order
        )
      );

      // إشعار فوري للنادل حسب الحالة مع صوت وذبذبة
      if (orderUpdate.newStatus === 'ready') {
        // استخدام الخدمة المحسنة للصوت
        notificationSound.playNotificationAdvanced({
          volume: 0.8,
          repeat: 2,
          urgent: true
        });
        
        // إشعار مرئي للصفحات غير النشطة
        backgroundNotificationService.sendNotification({
          title: '🔔 طلب جاهز للتسليم!',
          body: `الطاولة ${orderUpdate.tableNumber || 'غير محدد'} - الطلب #${orderUpdate.orderNumber}`,
          icon: '/coffee-logo.svg',
          tag: 'order-ready',
          urgent: true,
          sound: true,
          data: {
            action: 'navigate_to_orders',
            orderNumber: orderUpdate.orderNumber,
            tableNumber: orderUpdate.tableNumber
          }
        });
        
        showSuccess(`🔔 طلبك جاهز للتسليم! الطاولة ${orderUpdate.tableNumber || 'غير محدد'}`, 5000);
        
        // اهتزاز إذا كان متوفر
        if ('vibrate' in navigator) {
          navigator.vibrate([200, 100, 200, 100, 200]);
        }
      } else if (orderUpdate.newStatus === 'preparing') {
        // إشعار خفيف للتحضير
        backgroundNotificationService.sendNotification({
          title: '🍳 بدأ تحضير الطلب',
          body: `الطاولة ${orderUpdate.tableNumber || 'غير محدد'} - الطلب #${orderUpdate.orderNumber}`,
          icon: '/coffee-logo.svg',
          tag: 'order-preparing',
          urgent: false,
          sound: false
        });
        
        showInfo(`🍳 بدأ تحضير طلبك - الطاولة ${orderUpdate.tableNumber || 'غير محدد'}`, 3000);
      } else if (orderUpdate.newStatus === 'served' || orderUpdate.newStatus === 'delivered') {
        showSuccess(`✅ تم تسليم الطلب من الطاولة ${orderUpdate.tableNumber || 'غير محدد'}`, 3000);
      }

      // تحديث الطاولات إذا كان في شاشة الطاولات
      if (currentScreen === 'tables') {
        setTimeout(() => fetchTableAccounts(true), 500);
      }
    });

    // استقبال إشعارات الطلبات الجاهزة المخصصة (من الطباخ)
    socket.on('order-ready-notification', (readyNotification: any) => {
      console.log('🔔 إشعار طلب جاهز مخصص:', readyNotification);
      
      // صوت وإشعار قوي للطلبات الجاهزة
      notificationSound.playNotificationAdvanced({
        volume: 0.9,
        repeat: 3,
        urgent: true
      });
      
      // إشعار للصفحات غير النشطة
      backgroundNotificationService.sendNotification({
        title: '🍽️ طلب جاهز من المطبخ!',
        body: `${readyNotification.message || 'طلب جديد جاهز للتسليم'}`,
        icon: '/coffee-logo.svg',
        tag: 'chef-ready-notification',
        urgent: true,
        sound: true,
        data: {
          action: 'navigate_to_kitchen',
          orderNumber: readyNotification.orderNumber,
          tableNumber: readyNotification.tableNumber
        }
      });
      
      // اهتزاز قوي
      if ('vibrate' in navigator) {
        navigator.vibrate([300, 200, 300, 200, 300]);
      }
      
      // إشعار بارز
      showSuccess(
        `🔔 طلب جاهز للتسليم الآن! الطاولة ${readyNotification.tableNumber || 'غير محدد'} - ${readyNotification.chefName || 'الطباخ'}`, 
        7000 // يظهر لمدة 7 ثوانِ
      );
      
      // تحديث القوائم
      if (currentScreen === 'orders') {
        fetchOrders(true);
      } else if (currentScreen === 'tables') {
        fetchTableAccounts(true);
      }
    });    // استقبال تحديثات الطلبات الجديدة (من النادل للطباخ)
    socket.on('new-order-notification', (orderNotification: any) => {
      console.log('🆕 إشعار طلب جديد:', orderNotification);
      
      // تحديث قائمة الطلبات عند الحاجة فقط
      if (currentScreen === 'orders') {
        setTimeout(() => fetchOrders(true), 1000);
      }
      
      showInfo(`طلب جديد #${orderNotification.orderNumber || orderNotification.orderId} تم إضافته`, 3000);
    });

    // استقبال تحديثات الطلبات (تطبيق الخصم وغيرها)
    socket.on('order-updated', (orderUpdate: any) => {
      console.log('💰 تحديث طلب:', orderUpdate);
      
      if (orderUpdate.type === 'discount-applied') {
        // تحديث الطلب في القائمة المحلية فوراً
        setOrders(prevOrders =>
          prevOrders.map(order =>
            order._id === orderUpdate.orderId 
              ? { ...order, totalPrice: orderUpdate.newTotal }
              : order
          )
        );
        
        // إشعار للنادل
        showSuccess(
          `✅ تم تطبيق خصم ${orderUpdate.discountAmount} جنيه على الطلب #${orderUpdate.orderNumber}`, 
          5000
        );
        
        // تحديث البيانات إذا كان في الشاشة المناسبة
        if (currentScreen === 'orders') {
          setTimeout(() => fetchOrders(true), 500);
        } else if (currentScreen === 'tables') {
          setTimeout(() => fetchTableAccounts(true), 500);
        }
        
        // تحديث شاشة طلبات الخصم إذا كانت مفتوحة
        if (currentScreen === 'discounts') {
          setTimeout(() => fetchDiscountRequests(true), 500);
        }
      }
    });

    // استقبال إشعارات حالة طلبات الخصم
    socket.on('discount-request-status', (discountUpdate: any) => {
      console.log('💰 تحديث حالة طلب خصم:', discountUpdate);
      
      // تشغيل صوت الإشعار
      notificationSound.playNotification();
      
      if (discountUpdate.status === 'approved') {
        showSuccess(
          `✅ تم الموافقة على طلب الخصم للطلب #${discountUpdate.orderNumber} بمبلغ ${discountUpdate.discountAmount} جنيه`, 
          7000
        );
      } else if (discountUpdate.status === 'rejected') {
        showError(
          `❌ تم رفض طلب الخصم للطلب #${discountUpdate.orderNumber}. السبب: ${discountUpdate.rejectionReason || 'غير محدد'}`, 
          7000
        );
      }
      
      // تحديث شاشة طلبات الخصم فوراً
      if (currentScreen === 'discounts') {
        setTimeout(() => fetchDiscountRequests(true), 500);
      }
      
      // تحديث الطلبات إذا تم تطبيق الخصم
      if (discountUpdate.status === 'approved') {
        setTimeout(() => {
          if (currentScreen === 'orders') {
            fetchOrders(true);
          } else if (currentScreen === 'tables') {
            fetchTableAccounts(true);
          }
        }, 1000);
      }
    });

    // إشعارات تحديث القائمة والأسعار
    socket.on('menu-item-updated', (update: any) => {
      console.log('🍽️ تحديث عنصر قائمة:', update);
      showInfo(`تم تحديث ${update.itemName} في القائمة`);
      fetchMenuItems(true); // تحديث فوري للقائمة
    });

    socket.on('menu-item-added', (newItem: any) => {
      console.log('🆕 إضافة عنصر جديد للقائمة:', newItem);
      showSuccess(`تم إضافة ${newItem.itemName} للقائمة`);
      fetchMenuItems(true); // تحديث فوري للقائمة
    });

    socket.on('menu-item-removed', (removedItem: any) => {
      console.log('🗑️ حذف عنصر من القائمة:', removedItem);
      showInfo(`تم حذف ${removedItem.itemName} من القائمة`);
      fetchMenuItems(true); // تحديث فوري للقائمة
    });

    socket.on('price-updated', (priceUpdate: any) => {
      console.log('💰 تحديث سعر:', priceUpdate);
      showInfo(`تم تحديث سعر ${priceUpdate.itemName} إلى ${priceUpdate.newPrice} جنيه`);
      fetchMenuItems(true); // تحديث فوري للأسعار
    });

    // تحديثات حسابات الطاولات
    socket.on('table-account-updated', (tableUpdate: any) => {
      console.log('🏪 تحديث حساب طاولة:', tableUpdate);
      if (currentScreen === 'tables') {
        fetchTableAccounts(true); // تحديث فوري للطاولات
      }
    });

    socket.on('table-account-closed', (closedTable: any) => {
      console.log('🔒 إغلاق حساب طاولة:', closedTable);
      showInfo(`تم إغلاق حساب الطاولة ${closedTable.tableNumber}`);
      if (currentScreen === 'tables') {
        fetchTableAccounts(true); // تحديث فوري للطاولات
      }
    });

// استقبال تحديثات حالة الطاولات مع تحسين
    socket.on('table-status-updated', globalPerformanceOptimizer.throttle(
      'table-status-updated',
      (tableUpdate: any) => {
        console.log('🏓 تحديث حالة طاولة فوري:', tableUpdate);
        
        // تحديث قائمة الطاولات عند الحاجة فقط
        if (currentScreen === 'tables') {
          fetchTableAccounts(true);        }
        
        showInfo(tableUpdate.message || `تم تحديث حالة الطاولة ${tableUpdate.tableNumber}`);
      },
      1000 // throttle لثانية واحدة بدلاً من debounce
    ));      return () => {
        console.log('🧹 تنظيف مستمعي Socket.IO...');
        if (socket && typeof socket.off === 'function') {          socket.off('connect');
          socket.off('disconnect');
          socket.off('connect_error');
          socket.off('registration-confirmed');
          socket.off('order-status-update');
          socket.off('order-ready-notification');
          socket.off('new-order-notification'); 
          socket.off('order-updated');
          socket.off('discount-request-status');
          socket.off('table-status-updated');
          socket.off('menu-item-updated');
          socket.off('menu-item-added');
          socket.off('menu-item-removed');
          socket.off('price-updated');
          socket.off('table-account-updated');
          socket.off('table-account-closed');
          console.log('✅ تم تنظيف جميع مستمعي Socket.IO');
        }
      };
    } catch (error) {
      console.error('❌ خطأ في إعداد Socket.IO:', error);
      setIsConnected(false);
    }
  }, [showInfo, fetchTableAccounts, currentScreen, fetchOrders]);
  // إشعار حالة الاتصال
  useEffect(() => {
    if (isConnected) {
      console.log('✅ التحديث الفوري متاح');
    } else {
      showInfo('⚠️ التحديث الفوري غير متوفر - سيتم التحديث يدوياً');
    }  }, [isConnected, showInfo]);
  // تحديث دوري للبيانات مع حماية من Rate Limiting
  useEffect(() => {
    if (!isConnected && (currentScreen === 'orders' || currentScreen === 'tables')) {
      console.log('🔄 بدء التحديث الدوري للبيانات (كل 4 دقائق)');

      const interval = setInterval(() => {
        console.log('🔄 تحديث دوري للبيانات...');
        if (currentScreen === 'orders') {
          fetchOrders();
        } else if (currentScreen === 'tables') {
          fetchTableAccounts();
        }
      }, 240000); // كل 4 دقائق بدلاً من دقيقتين (تحسين إضافي)

      return () => {
        console.log('⏹️ إيقاف التحديث الدوري');
        clearInterval(interval);
      };
    }
  }, [isConnected, currentScreen, fetchOrders, fetchTableAccounts]);

  useEffect(() => {
    saveCartToSession();
  }, [cart, tableNumber, customerName]);

  // ربط الفئات بالمنتجات عند تغيير البيانات
  useEffect(() => {
    if (menuItems.length > 0 && categories.length > 0) {
      console.log('🔗 ربط الفئات بالمنتجات...');
      
      const updatedMenuItems = menuItems.map(product => {
        if (product.categories && Array.isArray(product.categories)) {
          const categoryDetails = product.categories
            .map(categoryId => categories.find(cat => cat._id === categoryId))
            .filter(Boolean) as Category[];
          
          return {
            ...product,
            categoryDetails
          };
        }
        return product;
      });
      
      // تحديث المنتجات فقط إذا كانت مختلفة
      const hasChanges = updatedMenuItems.some((item, index) => {
        const originalItem = menuItems[index];
        return !originalItem.categoryDetails || 
               originalItem.categoryDetails.length !== item.categoryDetails?.length ||
               originalItem.categoryDetails.some((cat, catIndex) => 
                 cat._id !== item.categoryDetails?.[catIndex]?._id
               );
      });
      
      if (hasChanges) {
        console.log('✅ تم ربط الفئات بالمنتجات بنجاح');
        setMenuItems(updatedMenuItems);
      }
    }
  }, [categories]); // فقط عند تغيير الفئات



  // Sidebar Component



  // دالة لمسح الطاولات المغلقة محلياً
  const clearClosedTables = () => {
    try {
      localStorage.removeItem('closedTables');
      showSuccess('تم مسح جميع الطاولات المغلقة محلياً');
      // إعادة جلب الطاولات لتحديث العرض
      fetchTableAccounts(true);
    } catch (error) {
      console.error('خطأ في مسح الطاولات المغلقة:', error);
      showError('فشل في مسح الطاولات المغلقة');
    }
  };





  // Render Discounts Screen


  // Sidebar Component
  const Sidebar = () => (
    <>
      {isMobile && sidebarOpen && (
        <div className={`waiterDashboardScreen__sidebar-overlay${sidebarOpen ? ' waiterDashboardScreen__sidebar-overlay--active' : ''}`} onClick={closeSidebar}></div>
      )}
      <div className={`waiterDashboardScreen__sidebar${sidebarOpen ? ' waiterDashboardScreen__sidebar--visible' : ' waiterDashboardScreen__sidebar--hidden'}`}>
        
        <div className="waiterDashboardScreen__sidebar-header">
          <div className="waiterDashboardScreen__sidebar-logo">
            <i className="fas fa-coffee waiterDashboardScreen__sidebar-logo-icon"></i>
            <span>DeshaCoffee</span>
          </div>
          {isMobile && (
            <button 
              className="waiterDashboardScreen__sidebar-close-btn" 
              onClick={closeSidebar}
              aria-label="إغلاق القائمة الجانبية"
            >
              <i className="fas fa-times"></i>
            </button>
          )}
        </div>

        <nav className="waiterDashboardScreen__sidebar-nav">
          <ul className="waiterDashboardScreen__nav-menu">
            <li className="waiterDashboardScreen__nav-item">
              <button 
                className={`waiterDashboardScreen__nav-link${currentScreen === 'drinks' ? ' waiterDashboardScreen__nav-link--active' : ''}`}
                onClick={() => { changeScreen('drinks'); if(isMobile) closeSidebar(); }}
              >
                <i className="fas fa-coffee waiterDashboardScreen__nav-icon"></i>
                <span className="waiterDashboardScreen__nav-text">المشروبات</span>
              </button>
            </li>
            <li className="waiterDashboardScreen__nav-item">
              <button 
                className={`waiterDashboardScreen__nav-link${currentScreen === 'orders' ? ' waiterDashboardScreen__nav-link--active' : ''}`}
                onClick={() => { changeScreen('orders'); if(isMobile) closeSidebar(); }}
              >
                <i className="fas fa-receipt waiterDashboardScreen__nav-icon"></i>
                <span className="waiterDashboardScreen__nav-text">الطلبات</span>
              </button>
            </li>
            <li className="waiterDashboardScreen__nav-item">
              <button 
                className={`waiterDashboardScreen__nav-link${currentScreen === 'cart' ? ' waiterDashboardScreen__nav-link--active' : ''}`}
                onClick={() => { changeScreen('cart'); if(isMobile) closeSidebar(); }}
              >
                <i className="fas fa-shopping-cart waiterDashboardScreen__nav-icon"></i>
                <span className="waiterDashboardScreen__nav-text">سلة المشتريات ({cart.length})</span>
              </button>
            </li>            <li className="waiterDashboardScreen__nav-item">
              <button 
                className={`waiterDashboardScreen__nav-link${currentScreen === 'tables' ? ' waiterDashboardScreen__nav-link--active' : ''}`}
                onClick={() => { changeScreen('tables'); if(isMobile) closeSidebar(); }}
              >
                <i className="fas fa-table waiterDashboardScreen__nav-icon"></i>
                <span className="waiterDashboardScreen__nav-text">الطاولات</span>
              </button>
            </li>
            <li className="waiterDashboardScreen__nav-item">
              <button 
                className={`waiterDashboardScreen__nav-link${currentScreen === 'discounts' ? ' waiterDashboardScreen__nav-link--active' : ''}`}
                onClick={() => { changeScreen('discounts'); if(isMobile) closeSidebar(); }}
              >
                <i className="fas fa-percentage waiterDashboardScreen__nav-icon"></i>
                <span className="waiterDashboardScreen__nav-text">طلبات الخصم</span>
              </button>
            </li>
          </ul>
        </nav>
        <div className="waiterDashboardScreen__sidebar-stats">
          <div className="waiterDashboardScreen__connection-status-only">
            <div className={`waiterDashboardScreen__connection-status ${isConnected ? 'waiterDashboardScreen__connection-status--connected' : 'waiterDashboardScreen__connection-status--disconnected'}`}>
              <i className={`fas ${isConnected ? 'fa-wifi' : 'fa-wifi-slash'}`}></i>
              <span>{isConnected ? 'متصل' : 'غير متصل'}</span>
              {!isConnected && (
                <button
                  className="waiterDashboardScreen__retry-connection-btn"
                  onClick={retrySocketConnection}
                  title="إعادة محاولة الاتصال"
                >
                  <i className="fas fa-redo"></i>
                </button>
              )}
            </div>
          </div>
        </div>

        <div className="waiterDashboardScreen__sidebar-footer">
          <button className="waiterDashboardScreen__logout-btn" onClick={handleLogout} aria-label="تسجيل الخروج من النظام">
            <i className="fas fa-sign-out-alt"></i>
            <span>تسجيل الخروج</span>
          </button>
        </div>
      </div>
    </>
  );

  // Main component render
  return (
    <div className="waiterDashboardScreen">
      {isMobile && !sidebarOpen && (
        <button 
          className="waiterDashboardScreen__mobile-menu-toggle" 
          onClick={openSidebar}
          aria-label="فتح القائمة الجانبية"
        >
          <i className="fas fa-bars"></i>
        </button>
      )}
      
      <Sidebar />
        <main className="waiterDashboardScreen__main">
        {/* Connection Error Banner */}
        {connectionError && (
          <div className="waiterDashboardScreen__connection-error-banner">
            <i className="fas fa-exclamation-triangle"></i>
            <span>{connectionError}</span>
            <button 
              onClick={() => setConnectionError(null)}
              className="waiterDashboardScreen__error-close-btn"
              aria-label="إغلاق رسالة الخطأ"
            >
              <i className="fas fa-times"></i>
            </button>
          </div>
        )}
          <div className="waiterDashboardScreen__content-card">
          {currentScreen === 'drinks' && (
            <WaiterDrinksScreen
              menuItems={menuItems}
              categories={categories}
              selectedCategory={selectedCategory}
              searchTerm={searchTerm}
              loadingMenuItems={loadingMenuItems}
              onCategoryChange={setSelectedCategory}
              onSearchChange={setSearchTerm}
              onAddToCart={addToCart}
              onOpenNotesModal={openNotesModal}
              onRefreshMenuItems={() => fetchMenuItems(true)}
              onRefreshCategories={() => fetchCategories()}
            />
          )}
          {currentScreen === 'orders' && (
            <WaiterOrdersScreen
              orders={orders}
              orderStatusFilter={orderStatusFilter}
              orderSearchTerm={orderSearchTerm}
              currentWaiterId={localStorage.getItem('waiterId') || JSON.parse(localStorage.getItem('user') || '{}')._id || JSON.parse(localStorage.getItem('user') || '{}').id}
              onStatusFilterChange={setOrderStatusFilter}
              onSearchTermChange={setOrderSearchTerm}
              onOrderDetails={(order) => {
                setSelectedOrderDetails(order);
                setShowOrderDetailsModal(true);
              }}
              onMarkOrderDelivered={handleMarkOrderDelivered}
              onRequestDiscount={(order) => {
                setSelectedOrderForDiscount(order);
                setShowDiscountModal(true);
              }}
              onRefreshOrders={() => fetchOrders(true)}
            />
          )}
          {currentScreen === 'tables' && (
            <WaiterTablesScreen
              tableAccounts={tableAccounts}
              orders={orders}
              onRefreshTables={() => fetchTableAccounts(true)}
              onRefreshOrders={() => fetchOrders(true)}
              onShowTableDetails={setSelectedTableAccountDetails}
              onCloseTable={handleCloseTable}
              onClearClosedTables={clearClosedTables}
            />
          )}
          {currentScreen === 'cart' && (
            <WaiterCartScreen
              cart={cart}
              tableNumber={tableNumber}
              customerName={customerName}
              loading={loading}
              onTableNumberChange={setTableNumber}
              onCustomerNameChange={setCustomerName}
              onUpdateCartQuantity={updateCartQuantity}
              onUpdateCartNotes={(itemIndex, notes) => {
                const updatedCart = cart.map((cartItem, cartIndex) =>
                  cartIndex === itemIndex 
                    ? { ...cartItem, notes: notes }
                    : cartItem
                );
                setCart(updatedCart);
                localStorage.setItem('waiterCart', JSON.stringify(updatedCart));
              }}
              onClearCart={clearCart}
              onSubmitOrder={submitOrder}
              onChangeScreen={changeScreen}
            />
          )}
          {currentScreen === 'discounts' && (
            <WaiterDiscountsScreen
              discountRequests={discountRequests}
              discountStatusFilter={discountStatusFilter}
              loadingDiscountRequests={loadingDiscountRequests}
              onStatusFilterChange={setDiscountStatusFilter}
              onRefreshDiscountRequests={() => fetchDiscountRequests(true)}
            />
          )}
        </div>
        
        {/* Order Details Modal */}
        {showOrderDetailsModal && selectedOrderDetails && (
          <div className="modal-overlay" onClick={() => setShowOrderDetailsModal(false)}>
            <div className="modal-content order-details-modal" onClick={(e) => e.stopPropagation()}>
              <div className="modal-header">
                <h2>
                  <i className="fas fa-receipt"></i>
                  تفاصيل الطلب #{selectedOrderDetails.orderNumber || selectedOrderDetails._id?.slice(-6) || 'غير محدد'}
                </h2>
                <button 
                  className="modal-close"
                  onClick={() => setShowOrderDetailsModal(false)}
                  aria-label="إغلاق نافذة تفاصيل الطلب"
                >
                  <i className="fas fa-times"></i>
                </button>
              </div>
              
              <div className="modal-body">
                <div className="order-info-section">
                  <h3>
                    <i className="fas fa-info-circle"></i>
                    معلومات الطلب
                  </h3>
                  <div className="order-info-grid">
                    <div className="info-item">
                      <span className="label">الطاولة:</span>
                      <span className="value">{selectedOrderDetails.tableNumber}</span>
                    </div>
                    <div className="info-item">
                      <span className="label">العميل:</span>
                      <span className="value">{selectedOrderDetails.customerName || 'غير محدد'}</span>
                    </div>                    <div className="info-item">
                      <span className="label">النادل:</span>
                      <span className="value">{selectedOrderDetails.waiterName}</span>
                    </div>
                    {selectedOrderDetails.chefName && (
                      <div className="info-item">
                        <span className="label">الطباخ:</span>
                        <span className="value chef-name">
                          <i className="fas fa-user-tie"></i>
                          {selectedOrderDetails.chefName}
                        </span>
                      </div>
                    )}
                    <div className="info-item">
                      <span className="label">الحالة:</span>
                      <span className={`status-badge ${selectedOrderDetails.status}`}>
                        <i className={`fas ${getStatusIcon(selectedOrderDetails.status)}`}></i>
                        {getStatusText(selectedOrderDetails.status)}
                      </span>
                    </div>                    <div className="info-item">
                      <span className="label">وقت الطلب:</span>
                      <span className="value">{formatDate(selectedOrderDetails.createdAt)}</span>
                    </div>

                    {/* قسم تفاصيل المبلغ والخصم */}
                    <div className="pricing-section-waiter">
                      <div className="pricing-header">
                        <i className="fas fa-calculator"></i>
                        <span>تفاصيل المبلغ</span>
                      </div>                      <div className="pricing-breakdown-waiter">
                        {/* تحقق من وجود خصم باستخدام discountAmount أو discountApplied */}
                        {((selectedOrderDetails.discountApplied && selectedOrderDetails.discountApplied > 0) || 
                          (selectedOrderDetails.discountAmount && selectedOrderDetails.discountAmount > 0)) ? (
                          <>
                            {/* استخدم القيمة المتاحة للخصم */}
                            {(() => {
                              const discountValue = selectedOrderDetails.discountApplied || selectedOrderDetails.discountAmount || 0;
                              const originalTotal = selectedOrderDetails.totalPrice + discountValue;
                              
                              return (
                                <>
                                  <div className="pricing-line original">
                                    <span className="pricing-label">قيمة الطلب الأصلي:</span>
                                    <span className="pricing-value">
                                      {originalTotal.toFixed(2)} جنيه
                                    </span>
                                  </div>
                                  <div className="pricing-line discount">
                                    <span className="pricing-label">قيمة الخصم:</span>
                                    <span className="pricing-value discount-value">
                                      - {discountValue.toFixed(2)} جنيه
                                    </span>
                                  </div>
                                  <div className="pricing-line final">
                                    <span className="pricing-label">قيمة الطلب بعد الخصم:</span>
                                    <span className="pricing-value final-value">
                                      {selectedOrderDetails.totalPrice.toFixed(2)} جنيه
                                    </span>
                                  </div>
                                </>
                              );
                            })()}
                          </>
                        ) : (
                          <div className="pricing-line total">
                            <span className="pricing-label">المبلغ الإجمالي:</span>
                            <span className="pricing-value total-value">
                              {selectedOrderDetails.totalPrice.toFixed(2)} جنيه
                            </span>
                          </div>
                        )}
                      </div>
                    </div>
                  </div>
                </div>

                <div className="order-items-section">
                  <h3>
                    <i className="fas fa-list"></i>
                    الأصناف المطلوبة ({selectedOrderDetails.items?.length || 0})
                  </h3>
                  <div className="items-list">
                    {(selectedOrderDetails.items || []).map((item, index) => (
                      <div key={index} className="item-card">
                        <div className="item-info">
                          <div className="item-name">{item.name}</div>
                          {item.notes && (
                            <div className="item-notes">
                              <i className="fas fa-sticky-note"></i>
                              ملاحظة: {item.notes}
                            </div>
                          )}
                        </div>
                        <div className="item-details">
                          <div className="item-quantity">
                            <i className="fas fa-times"></i>
                            {item.quantity}
                          </div>                          <div className="item-price">{item.price} جنيه</div>
                          <div className="item-total">{(item.price * item.quantity).toFixed(2)} جنيه</div>
                        </div>
                      </div>
                    ))}
                  </div>
                </div>

                <div className="discount-section">
                  <h3>
                    <i className="fas fa-percentage"></i>
                    إدارة الخصم
                  </h3>
                  {selectedOrderDetails.discountStatus ? (
                    <div className={`discount-status ${selectedOrderDetails.discountStatus}`}>
                      <i className="fas fa-info-circle"></i>                      <span>
                        {selectedOrderDetails.discountStatus === 'pending' && 'طلب خصم قيد المراجعة'}
                        {selectedOrderDetails.discountStatus === 'approved' && 
                          `تم الموافقة على خصم ${(selectedOrderDetails.discountAmount || selectedOrderDetails.discountApplied || 0).toFixed(2)} جنيه`}
                        {selectedOrderDetails.discountStatus === 'rejected' && 'تم رفض طلب الخصم'}
                      </span>
                    </div>
                  ) : (
                    <button 
                      className="btn-discount"
                      onClick={() => {
                        setSelectedOrderForDiscount(selectedOrderDetails);
                        setShowDiscountModal(true);
                        setShowOrderDetailsModal(false);
                      }}
                    >
                      <i className="fas fa-percentage"></i>
                      طلب خصم
                    </button>
                  )}
                </div>
              </div>
              
              <div className="modal-footer">
                {selectedOrderDetails.status === 'ready' && (
                  <button 
                    className="btn-deliver"
                    onClick={() => {
                      handleMarkOrderDelivered(selectedOrderDetails._id);
                      setShowOrderDetailsModal(false);
                    }}
                  >
                    <i className="fas fa-check"></i>
                    تم التسليم
                  </button>
                )}
                <button 
                  className="btn-close"
                  onClick={() => setShowOrderDetailsModal(false)}
                >
                  إغلاق
                </button>
              </div>
            </div>
          </div>
        )}

        {/* Table Details Modal */}
        {showTableDetailsModal && selectedTableAccountDetails && (
          <div className="modal-overlay" onClick={() => setShowTableDetailsModal(false)}>
            <div className="modal-content table-details-modal" onClick={(e) => e.stopPropagation()}>
              <div className="modal-header">
                <h2>
                  <i className="fas fa-table"></i>
                  تفاصيل الطاولة #{selectedTableAccountDetails.tableNumber}
                </h2>
                <button 
                  className="modal-close"
                  onClick={() => setShowTableDetailsModal(false)}
                  aria-label="إغلاق نافذة تفاصيل الطاولة"
                >
                  <i className="fas fa-times"></i>
                </button>
              </div>
              
              <div className="modal-body">
                <div className="table-info-section">
                  <h3>
                    <i className="fas fa-info-circle"></i>
                    معلومات الطاولة
                  </h3>
                  <div className="table-info-grid">
                    <div className="info-item">
                      <span className="label">رقم الطاولة:</span>
                      <span className="value">{selectedTableAccountDetails.tableNumber}</span>
                    </div>
                    <div className="info-item">
                      <span className="label">العميل:</span>
                      <span className="value">{selectedTableAccountDetails.customerName || 'غير محدد'}</span>
                    </div>
                    <div className="info-item">
                      <span className="label">النادل:</span>
                      <span className="value">{selectedTableAccountDetails.waiterName}</span>
                    </div>
                    <div className="info-item">
                      <span className="label">الحالة:</span>
                      <span className={`status-badge ${selectedTableAccountDetails.status}`}>
                        <i className="fas fa-circle"></i>
                        {selectedTableAccountDetails.status === 'active' ? 'نشطة' : 'مغلقة'}
                      </span>
                    </div>
                    <div className="info-item">
                      <span className="label">وقت الفتح:</span>
                      <span className="value">{formatDate(selectedTableAccountDetails.createdAt)}</span>
                    </div>
                    <div className="info-item">
                      <span className="label">آخر نشاط:</span>
                      <span className="value">{formatDate(selectedTableAccountDetails.updatedAt)}</span>
                    </div>
                    <div className="info-item">
                      <span className="label">المبلغ الإجمالي:</span>
                      <span className="value total-amount">
                        {(
                          (selectedTableAccountDetails.orders && selectedTableAccountDetails.orders.length > 0)
                            ? selectedTableAccountDetails.orders.reduce((sum, order) => sum + (order.totalPrice || 0), 0)
                            : (selectedTableAccountDetails.totalAmount || 0)
                        ).toFixed(2)} جنيه
                      </span>
                    </div>
                  </div>
                </div>

                <div className="table-orders-section">
                  <h3>
                    <i className="fas fa-receipt"></i>
                    طلبات الطاولة ({selectedTableAccountDetails.orders?.length || 0})
                  </h3>
                  <div className="orders-list">
                    {(selectedTableAccountDetails.orders && selectedTableAccountDetails.orders.length > 0) ? (
                      selectedTableAccountDetails.orders.map((order, index) => (
                        <div key={index} className="order-summary-card">
                          <div className="order-summary-header">
                            <div className="order-number">طلب #{order.orderNumber || order._id?.slice(-6) || 'غير محدد'}</div>
                            <div className={`order-status ${order.status}`}>
                              <i className={`fas ${getStatusIcon(order.status)}`}></i>
                              {getStatusText(order.status)}
                            </div>
                          </div>
                          <div className="order-summary-info">
                            <div className="summary-item">
                              <span>الأصناف: {order.items?.length || 0}</span>
                            </div>
                            <div className="summary-item">
                              <span>المبلغ: {(order.totalPrice || 0).toFixed(2)} جنيه</span>
                            </div>
                            <div className="summary-item">
                              <span>الوقت: {formatDate(order.createdAt)}</span>
                            </div>
                          </div>
                          <button 
                            className="btn-order-details"
                            onClick={() => {
                              setSelectedOrderDetails(order);
                              setShowTableDetailsModal(false);
                              setShowOrderDetailsModal(true);
                            }}
                          >
                            <i className="fas fa-eye"></i>
                            عرض التفاصيل
                          </button>
                        </div>
                      ))
                    ) : (
                      <div className="no-orders-message">لا توجد طلبات لهذه الطاولة حالياً.</div>
                    )}
                  </div>
                </div>
              </div>
              
              <div className="modal-footer">
                <button 
                  className="btn-close"
                  onClick={() => setShowTableDetailsModal(false)}
                >
                  إغلاق
                </button>
              </div>
            </div>
          </div>
        )}

        {/* Discount Request Modal */}
        {showDiscountModal && selectedOrderForDiscount && (
          <div className="modal-overlay" onClick={() => setShowDiscountModal(false)}>
            <div className="modal-content discount-modal" onClick={(e) => e.stopPropagation()}>
              <div className="modal-header">
                <h2>
                  <i className="fas fa-percentage"></i>
                  طلب خصم للطلب #{selectedOrderForDiscount.orderNumber}
                </h2>
                <button 
                  className="modal-close"
                  onClick={() => setShowDiscountModal(false)}
                  aria-label="إغلاق نافذة طلب الخصم"
                >
                  <i className="fas fa-times"></i>
                </button>
              </div>
              
              <div className="modal-body">
                <div className="order-summary">
                  <h3>ملخص الطلب</h3>
                  <div className="summary-info">
                    <div>
                      <i className="fas fa-table"></i>
                      <span>الطاولة: {selectedOrderForDiscount.tableNumber}</span>
                    </div>
                    <div>
                      <i className="fas fa-user"></i>
                      <span>العميل: {selectedOrderForDiscount.customerName || 'غير محدد'}</span>
                    </div>
                    <div>
                      <i className="fas fa-money-bill-wave"></i>
                      <span>المبلغ الأصلي: {selectedOrderForDiscount.totalPrice} جنيه</span>
                    </div>
                  </div>
                </div>

                <div className="discount-form">
                  <div className="form-group">
                    <label htmlFor="discount-amount">
                      <i className="fas fa-coins"></i>
                      مبلغ الخصم (جنيه)
                    </label>
                    <div className="input-wrapper">
                      <input
                        id="discount-amount"
                        type="number"
                        value={discountAmount}
                        onChange={(e) => setDiscountAmount(e.target.value)}
                        placeholder="أدخل مبلغ الخصم"
                        min="0"
                        max={selectedOrderForDiscount.totalPrice}
                        step="0.1"
                      />
                      <span className="input-suffix">جنيه</span>
                    </div>
                  </div>
                  
                  <div className="form-group">
                    <label htmlFor="discount-reason">
                      <i className="fas fa-comment-alt"></i>
                      سبب الخصم
                    </label>
                    <textarea
                      id="discount-reason"
                      value={discountReason}
                      onChange={(e) => setDiscountReason(e.target.value)}
                      placeholder="أدخل سبب طلب الخصم (مثال: شكوى العميل، تأخير في الخدمة، خطأ في الطلب)"
                      rows={4}
                    />
                  </div>

                  {discountAmount && (
                    <div className="discount-preview">
                      <div className="preview-item">
                        <span>المبلغ الأصلي:</span>
                        <span>{selectedOrderForDiscount.totalPrice} جنيه</span>
                      </div>
                      <div className="preview-item">
                        <span>مبلغ الخصم:</span>
                        <span>-{discountAmount} جنيه</span>
                      </div>
                      <div className="preview-item total">
                        <span>المبلغ بعد الخصم:</span>
                        <span>{(selectedOrderForDiscount.totalPrice - parseFloat(discountAmount || '0')).toFixed(2)} جنيه</span>
                      </div>
                    </div>
                  )}
                </div>
              </div>
              
              <div className="modal-footer">
                <button 
                  className="btn-submit"
                  disabled={!discountAmount || !discountReason}                  onClick={async () => {
                    try {
                      console.log('🔄 إرسال طلب خصم...', {
                        orderId: selectedOrderForDiscount._id,
                        orderNumber: selectedOrderForDiscount.orderNumber,
                        customerName: selectedOrderForDiscount.customerName,
                        originalAmount: selectedOrderForDiscount.totalPrice,
                        requestedDiscount: parseFloat(discountAmount),
                        reason: discountReason,
                        waiterName: localStorage.getItem('username'),
                        tableNumber: selectedOrderForDiscount.tableNumber
                      });

                      const response = await authenticatedPost('/api/v1/discount-requests', {
                        orderId: selectedOrderForDiscount._id,
                        orderNumber: selectedOrderForDiscount.orderNumber,
                        customerName: selectedOrderForDiscount.customerName || 'عميل',
                        originalAmount: selectedOrderForDiscount.totalPrice,
                        requestedDiscount: parseFloat(discountAmount),
                        reason: discountReason,
                        waiterName: localStorage.getItem('username') || 'نادل',
                        tableNumber: selectedOrderForDiscount.tableNumber
                      });

                      console.log('✅ استجابة API طلب الخصم:', response);

                      if (response.success) {
                        showSuccess('تم إرسال طلب الخصم للمراجعة بنجاح');
                        
                        // إرسال تحديث مفصل عبر Socket.IO للمدير
                        socket.emit('discount-request', {
                          orderId: selectedOrderForDiscount._id,
                          orderNumber: selectedOrderForDiscount.orderNumber,
                          waiterName: localStorage.getItem('username') || 'نادل',
                          waiterId: localStorage.getItem('userId') || 'waiter-id',
                          customerName: selectedOrderForDiscount.customerName || 'عميل',
                          tableNumber: selectedOrderForDiscount.tableNumber,
                          originalAmount: selectedOrderForDiscount.totalPrice,
                          requestedDiscount: parseFloat(discountAmount),
                          reason: discountReason,
                          timestamp: new Date().toISOString(),
                          discountRequestId: response.discountRequestId || response.data?.id
                        });
                        
                        console.log('📡 تم إرسال إشعار Socket.IO مفصل للمدير');
                        
                        // إشعار محلي للنادل
                        showInfo(`📤 تم إرسال طلب خصم ${parseFloat(discountAmount)} جنيه للمدير`);
                      } else {
                        showError(response.message || 'فشل في إرسال طلب الخصم');
                      }
                    } catch (error) {
                      console.error('❌ خطأ في إرسال طلب الخصم:', error);
                      showError('حدث خطأ في إرسال طلب الخصم');
                    }
                    
                    setShowDiscountModal(false);
                    setDiscountAmount('');
                    setDiscountReason('');
                    setSelectedOrderForDiscount(null);
                  }}
                >
                  <i className="fas fa-paper-plane"></i>
                  إرسال طلب الخصم
                </button>
                <button 
                  className="btn-cancel"
                  onClick={() => {
                    setShowDiscountModal(false);
                    setDiscountAmount('');
                    setDiscountReason('');
                    setSelectedOrderForDiscount(null);
                  }}
                >
                  إلغاء
                </button>
              </div>
            </div>
          </div>
        )}

        {/* Notes Modal */}
        {showNotesModal && selectedItemForNotes && (
          <div className="modal-overlay" onClick={() => setShowNotesModal(false)}>
            <div className="modal-content notes-modal" onClick={(e) => e.stopPropagation()}>
              <div className="modal-header">
                <h2>
                  <i className="fas fa-sticky-note"></i>
                  إضافة ملاحظات - {selectedItemForNotes.name}
                </h2>
                <button
                  className="modal-close"
                  onClick={() => setShowNotesModal(false)}
                  aria-label="إغلاق نافذة إضافة الملاحظات"
                >
                  <i className="fas fa-times"></i>
                </button>
              </div>

              <div className="modal-body">
                <div className="item-preview">
                  <div className="item-info">
                    <h3>{selectedItemForNotes.name}</h3>
                    <p className="item-price">{selectedItemForNotes.price} ج.م</p>
                    {selectedItemForNotes.description && (
                      <p className="item-description">{selectedItemForNotes.description}</p>
                    )}
                  </div>
                </div>

                <div className="notes-form">
                  <label htmlFor="item-notes">ملاحظات خاصة:</label>
                  <textarea
                    id="item-notes"
                    value={itemNotes}
                    onChange={(e) => setItemNotes(e.target.value)}
                    placeholder="أدخل ملاحظات خاصة للمشروب (مثل: بدون سكر، ساخن جداً، إضافة كريمة...)"
                    rows={4}
                    maxLength={200}
                  />
                  <div className="character-count">
                    {itemNotes.length}/200 حرف
                  </div>
                </div>
              </div>

              <div className="modal-footer">
                <button
                  className="btn-add-with-notes"
                  onClick={addItemWithNotes}
                >
                  <i className="fas fa-plus"></i>
                  إضافة للسلة مع الملاحظات
                </button>
                <button
                  className="btn-cancel"
                  onClick={() => setShowNotesModal(false)}
                >
                  إلغاء
                </button>
              </div>
            </div>
          </div>
        )}

        {/* Toast Notifications */}
        <div className="toast-container">
          {toasts.map(toast => (
            <div 
              key={toast.id} 
              className={`toast toast-${toast.type}`}
              onClick={() => removeToast(toast.id)}
            >
              <i className={`fas ${toast.type === 'success' ? 'fa-check-circle' : 
                                   toast.type === 'error' ? 'fa-exclamation-circle' : 
                                   'fa-info-circle'}`}></i>
              <span>{toast.message}</span>
              <button 
                className="toast-close" 
                onClick={() => removeToast(toast.id)}
                aria-label="إغلاق الإشعار"
              >
                <i className="fas fa-times"></i>
              </button>
            </div>
          ))}
        </div>
      </main>
    </div>
  );
}
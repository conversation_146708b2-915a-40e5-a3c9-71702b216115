import React from 'react';
// استيراد ملف CSS الخاص بشاشة طلبات الخصم
import '../styles/waiter/WaiterDiscountsScreen.css';

interface DiscountRequest {
  _id: string;
  orderNumber?: string;
  tableNumber?: string;
  customerName?: string;
  originalAmount?: number;
  requestedDiscount?: number;
  reason?: string;
  status: 'pending' | 'approved' | 'rejected';
  createdAt: string;
  requestedBy?: string;
  waiterName?: string;
  approvedBy?: string;
}

interface WaiterDiscountsScreenProps {
  discountRequests: DiscountRequest[];
  discountStatusFilter: 'all' | 'pending' | 'approved' | 'rejected';
  loadingDiscountRequests: boolean;
  onStatusFilterChange: (status: 'all' | 'pending' | 'approved' | 'rejected') => void;
  onRefreshDiscountRequests: () => void;
}

const formatDate = (dateString: string): string => {
  const date = new Date(dateString);
  return date.toLocaleString('ar-EG', {
    year: 'numeric',
    month: 'short',
    day: 'numeric',
    hour: '2-digit',
    minute: '2-digit'
  });
};

export default function WaiterDiscountsScreen({
  discountRequests,
  discountStatusFilter,
  loadingDiscountRequests,
  onStatusFilterChange,
  onRefreshDiscountRequests
}: WaiterDiscountsScreenProps) {
  // فلترة طلبات الخصم حسب الحالة
  const filteredRequests = discountStatusFilter === 'all' 
    ? discountRequests 
    : discountRequests.filter(request => request.status === discountStatusFilter);

  return (
    <div className="content-container waiter-discounts-screen">
      <div className="screen-header">
        <div className="action-buttons-flex">
          <h1 className="screen-title">
            <i className="fas fa-percentage"></i>
            طلبات الخصم
          </h1>
          <button
            className="btn-refresh action-btn-primary"
            title="تحديث طلبات الخصم"
            onClick={() => onRefreshDiscountRequests()}
          >
            <i className="fas fa-sync-alt"></i> تحديث
          </button>
        </div>
        <p className="screen-subtitle">متابعة حالة طلبات الخصم المقدمة</p>
      </div>

      {/* Filters */}
      <div className="filters-container">
        <div className="filter-group">
          <label htmlFor="status-filter" className="filter-label">
            <i className="fas fa-filter"></i>
            فلترة حسب الحالة:
          </label>
          <select 
            id="status-filter"
            className="filter-select"
            value={discountStatusFilter} 
            onChange={(e) => onStatusFilterChange(e.target.value as any)}
          >
            <option value="all">جميع الطلبات</option>
            <option value="pending">في الانتظار</option>
            <option value="approved">تم الموافقة</option>
            <option value="rejected">تم الرفض</option>
          </select>
        </div>
      </div>

      {/* Statistics */}
      <div className="stats-grid">
        <div 
          className={`stat-card clickable ${discountStatusFilter === 'all' ? 'active' : ''}`}
          onClick={() => onStatusFilterChange('all')}
          title="عرض جميع طلبات الخصم"
        >
          <div className="stat-icon">
            <i className="fas fa-percentage"></i>
          </div>
          <div className="stat-content">
            <div className="stat-number">{discountRequests.length}</div>
            <div className="stat-label">إجمالي طلبات الخصم</div>
          </div>
        </div>
        
        <div 
          className={`stat-card clickable ${discountStatusFilter === 'pending' ? 'active' : ''}`}
          onClick={() => onStatusFilterChange('pending')}
          title="عرض الطلبات في الانتظار"
        >
          <div className="stat-icon pending">
            <i className="fas fa-clock"></i>
          </div>
          <div className="stat-content">
            <div className="stat-number">
              {discountRequests.filter(req => req.status === 'pending').length}
            </div>
            <div className="stat-label">في الانتظار</div>
          </div>
        </div>
        
        <div 
          className={`stat-card clickable ${discountStatusFilter === 'approved' ? 'active' : ''}`}
          onClick={() => onStatusFilterChange('approved')}
          title="عرض الطلبات المعتمدة"
        >
          <div className="stat-icon approved">
            <i className="fas fa-check-circle"></i>
          </div>
          <div className="stat-content">
            <div className="stat-number">
              {discountRequests.filter(req => req.status === 'approved').length}
            </div>
            <div className="stat-label">تم الموافقة</div>
          </div>
        </div>

        <div 
          className={`stat-card clickable ${discountStatusFilter === 'rejected' ? 'active' : ''}`}
          onClick={() => onStatusFilterChange('rejected')}
          title="عرض الطلبات المرفوضة"
        >
          <div className="stat-icon rejected">
            <i className="fas fa-times-circle"></i>
          </div>
          <div className="stat-content">
            <div className="stat-number">
              {discountRequests.filter(req => req.status === 'rejected').length}
            </div>
            <div className="stat-label">تم الرفض</div>
          </div>
        </div>
      </div>

      {/* Loading State */}
      {loadingDiscountRequests && (
        <div className="loading-container">
          <div className="loading-spinner"></div>
          <p>جاري تحميل طلبات الخصم...</p>
        </div>
      )}

      {/* Discount Requests List */}
      <div className="discount-requests-list">
        {!loadingDiscountRequests && filteredRequests.length === 0 ? (
          <div className="empty-state">
            <i className="fas fa-percentage empty-icon"></i>
            <h3>لا توجد طلبات خصم</h3>
            <p>
              {discountStatusFilter === 'all' 
                ? 'لم تقم بإرسال أي طلبات خصم بعد'
                : `لا توجد طلبات خصم بحالة "${discountStatusFilter === 'pending' ? 'في الانتظار' : discountStatusFilter === 'approved' ? 'تم الموافقة' : 'تم الرفض'}"`
              }
            </p>
          </div>
        ) : (
          <div className="discount-requests-grid">
            {filteredRequests.map(request => (
              <div key={request._id} className="discount-request-card">
                <div className="request-header">
                  <div className="request-number">
                    <i className="fas fa-hashtag"></i>
                    طلب #{request._id?.slice(-6) || 'غير محدد'}
                  </div>
                  <div className={`request-status ${request.status}`}>
                    <i className={`fas ${
                      request.status === 'pending' ? 'fa-clock' :
                      request.status === 'approved' ? 'fa-check-circle' :
                      request.status === 'rejected' ? 'fa-times-circle' : 'fa-question-circle'
                    }`}></i>
                    {request.status === 'pending' ? 'في الانتظار' :
                     request.status === 'approved' ? 'تم الموافقة' :
                     request.status === 'rejected' ? 'تم الرفض' : request.status}
                  </div>
                </div>

                <div className="request-info">
                  <div className="info-row">
                    <span className="label">
                      <i className="fas fa-receipt"></i>
                      رقم الطلب:
                    </span>
                    <span className="value">{request.orderNumber || 'غير محدد'}</span>
                  </div>
                  
                  <div className="info-row">
                    <span className="label">
                      <i className="fas fa-user"></i>
                      العميل:
                    </span>
                    <span className="value">{request.customerName || 'غير محدد'}</span>
                  </div>
                  
                  <div className="info-row">
                    <span className="label">
                      <i className="fas fa-table"></i>
                      الطاولة:
                    </span>
                    <span className="value">{request.tableNumber || 'غير محدد'}</span>
                  </div>
                  
                  <div className="info-row">
                    <span className="label">
                      <i className="fas fa-money-bill-wave"></i>
                      المبلغ الأصلي:
                    </span>
                    <span className="value">{(request.originalAmount || 0).toFixed(2)} جنيه</span>
                  </div>
                  
                  <div className="info-row">
                    <span className="label">
                      <i className="fas fa-percentage"></i>
                      مبلغ الخصم:
                    </span>
                    <span className="value discount-amount">
                      {(request.requestedDiscount || 0).toFixed(2)} جنيه
                    </span>
                  </div>
                  
                  <div className="info-row">
                    <span className="label">
                      <i className="fas fa-comment"></i>
                      السبب:
                    </span>
                    <span className="value reason">{request.reason || 'غير محدد'}</span>
                  </div>
                  
                  <div className="info-row">
                    <span className="label">
                      <i className="fas fa-clock"></i>
                      تاريخ الطلب:
                    </span>
                    <span className="value">{formatDate(request.createdAt)}</span>
                  </div>

                  {request.status === 'approved' && request.approvedBy && (
                    <div className="info-row">
                      <span className="label">
                        <i className="fas fa-user-check"></i>
                        تم الموافقة بواسطة:
                      </span>
                      <span className="value">{request.approvedBy}</span>
                    </div>
                  )}

                  {request.status === 'rejected' && request.approvedBy && (
                    <div className="info-row">
                      <span className="label">
                        <i className="fas fa-user-times"></i>
                        تم الرفض بواسطة:
                      </span>
                      <span className="value">{request.approvedBy}</span>
                    </div>
                  )}
                </div>

                <div className="request-footer">
                  <div className="final-amount">
                    <span className="label">المبلغ بعد الخصم:</span>
                    <span className="amount">
                      {((request.originalAmount || 0) - (request.requestedDiscount || 0)).toFixed(2)} جنيه
                    </span>
                  </div>
                </div>
              </div>
            ))}
          </div>
        )}
      </div>
    </div>
  );
}

import React, { useState, useEffect } from 'react';
import { authenticatedGet } from '../utils/apiHelpers';
import ResponsiveGrid from '../components/ResponsiveGrid';
import ResponsiveCard from '../components/ResponsiveCard';
import socket from '../socket';
import '../styles/screens/ReportsScreenIsolated.css';

interface DashboardStats {
  totalOrders: number;
  totalSales: number;
  activeEmployees: number;
  activeTables: number;
  pendingOrders: number;
  preparingOrders: number;
  readyOrders: number;
  completedOrders: number;
  popularProducts?: ProductStats[];
  waiterStats?: WaiterStats[];
}

interface ProductStats {
  productId: string;
  productName: string;
  totalQuantity: number;
  totalOrders: number;
  category?: string;
}

interface WaiterStats {
  waiterId: string;
  waiterName: string;
  totalOrders: number;
  totalSales: number;
  averageOrderValue: number;
  completedOrders: number;
  pendingOrders: number;
}

interface Order {
  _id: string;
  orderNumber: string;
  items: any[];
  totalAmount?: number;
  totalPrice?: number;
  totals?: {
    subtotal: number;
    tax: number;
    discount: number;
    total: number;
  };
  status: 'pending' | 'preparing' | 'ready' | 'completed' | 'delivered';
  tableNumber?: number;
  customerName?: string;
  waiterName?: string;
  waiterId?: string;
  chefName?: string;
  staff?: {
    waiter?: string;
    chef?: string;
  };
  createdAt: string;
  updatedAt: string;
}

interface ReportsManagerScreenBootstrapProps {
  stats: DashboardStats;
  orders: Order[];
  loading: boolean;
}

const ReportsManagerScreenBootstrap: React.FC<ReportsManagerScreenBootstrapProps> = ({
  stats,
  orders,
  loading
}) => {
  const [dateFilter, setDateFilter] = useState<'today' | 'week' | 'month' | 'all'>('today');
  const [detailedStats, setDetailedStats] = useState<any>(null);
  const [loadingDetailed, setLoadingDetailed] = useState(false);

  useEffect(() => {
    loadDetailedReports();
  }, [dateFilter]);

  // Socket.IO event listeners for real-time updates
  useEffect(() => {
    const handleReportsUpdate = () => {
      console.log('📡 Real-time reports update received');
      loadDetailedReports();
    };

    const handleOrderUpdate = () => {
      console.log('📡 Real-time order update received');
      loadDetailedReports();
    };

    // Add event listeners
    socket.on('orderStatusUpdated', handleOrderUpdate);
    socket.on('orderCompleted', handleOrderUpdate);
    socket.on('newOrder', handleOrderUpdate);
    socket.on('salesUpdated', handleReportsUpdate);
    socket.on('statsUpdated', handleReportsUpdate);

    // Cleanup function
    return () => {
      socket.off('orderStatusUpdated', handleOrderUpdate);
      socket.off('orderCompleted', handleOrderUpdate);
      socket.off('newOrder', handleOrderUpdate);
      socket.off('salesUpdated', handleReportsUpdate);
      socket.off('statsUpdated', handleReportsUpdate);
    };
  }, []);

  const loadDetailedReports = async () => {
    setLoadingDetailed(true);
    try {
      const response = await authenticatedGet(`/api/reports/detailed?period=${dateFilter}`);
      if (response.success) {
        setDetailedStats(response.data);
      }
    } catch (error) {
      console.error('Error loading detailed reports:', error);
    } finally {
      setLoadingDetailed(false);
    }
  };

  const getFilteredOrders = () => {
    const now = new Date();
    const startOfDay = new Date(now.getFullYear(), now.getMonth(), now.getDate());
    const startOfWeek = new Date(startOfDay);
    startOfWeek.setDate(startOfDay.getDate() - startOfDay.getDay());
    const startOfMonth = new Date(now.getFullYear(), now.getMonth(), 1);

    return orders.filter(order => {
      const orderDate = new Date(order.createdAt);
      switch (dateFilter) {
        case 'today':
          return orderDate >= startOfDay;
        case 'week':
          return orderDate >= startOfWeek;
        case 'month':
          return orderDate >= startOfMonth;
        case 'all':
        default:
          return true;
      }
    });
  };

  const filteredOrders = getFilteredOrders();
  const totalRevenue = filteredOrders.reduce((sum, order) => {
    const amount = order.totals?.total || order.totalPrice || order.totalAmount || 0;
    return sum + amount;
  }, 0);

  const avgOrderValue = filteredOrders.length > 0 ? totalRevenue / filteredOrders.length : 0;

  const getOrdersByStatus = (status: string) => {
    return filteredOrders.filter(order => order.status === status).length;
  };

  const getTopProducts = () => {
    const productStats: { [key: string]: { name: string; quantity: number; orders: number } } = {};
    
    filteredOrders.forEach(order => {
      order.items?.forEach((item: any) => {
        const productName = item.name || item.productName || 'منتج غير محدد';
        if (!productStats[productName]) {
          productStats[productName] = { name: productName, quantity: 0, orders: 0 };
        }
        productStats[productName].quantity += item.quantity || 1;
        productStats[productName].orders += 1;
      });
    });

    return Object.values(productStats)
      .sort((a, b) => b.quantity - a.quantity)
      .slice(0, 5);
  };

  const getWaiterPerformance = () => {
    const waiterStats: { [key: string]: { name: string; orders: number; sales: number } } = {};
    
    filteredOrders.forEach(order => {
      const waiterName = order.waiterName || order.staff?.waiter || 'غير محدد';
      if (!waiterStats[waiterName]) {
        waiterStats[waiterName] = { name: waiterName, orders: 0, sales: 0 };
      }
      waiterStats[waiterName].orders += 1;
      const amount = order.totals?.total || order.totalPrice || order.totalAmount || 0;
      waiterStats[waiterName].sales += amount;
    });

    return Object.values(waiterStats)
      .sort((a, b) => b.sales - a.sales)
      .slice(0, 5);
  };

  const getDateFilterLabel = () => {
    switch (dateFilter) {
      case 'today': return 'اليوم';
      case 'week': return 'هذا الأسبوع';
      case 'month': return 'هذا الشهر';
      case 'all': return 'جميع الفترات';
      default: return 'اليوم';
    }
  };

  const topProducts = getTopProducts();
  const waiterPerformance = getWaiterPerformance();

  // Update progress bars after render
  useEffect(() => {
    const productBars = document.querySelectorAll('.reports-product-bar[data-width]');
    productBars.forEach((bar) => {
      const width = bar.getAttribute('data-width');
      if (width) {
        (bar as HTMLElement).style.width = `${Math.round(parseFloat(width))}%`;
      }
    });

    const waiterBars = document.querySelectorAll('.reports-waiter-bar[data-width]');
    waiterBars.forEach((bar) => {
      const width = bar.getAttribute('data-width');
      if (width) {
        (bar as HTMLElement).style.width = `${Math.round(parseFloat(width))}%`;
      }
    });
  }, [topProducts, waiterPerformance]);

  // Overview Cards Data
  const overviewCards = [
    {
      title: 'إجمالي الإيرادات',
      value: `${totalRevenue.toFixed(2)} ج.م`,
      icon: 'fas fa-money-bill-wave',
      color: 'success',
      gradientClass: 'reports-overview-gradient-success'
    },
    {
      title: 'إجمالي الطلبات',
      value: filteredOrders.length,
      icon: 'fas fa-shopping-cart',
      color: 'primary',
      gradientClass: 'reports-overview-gradient-primary'
    },
    {
      title: 'متوسط قيمة الطلب',
      value: `${avgOrderValue.toFixed(2)} ج.م`,
      icon: 'fas fa-chart-line',
      color: 'warning',
      gradientClass: 'reports-overview-gradient-warning'
    },
    {
      title: 'الطلبات المكتملة',
      value: getOrdersByStatus('completed'),
      icon: 'fas fa-check-circle',
      color: 'info',
      gradientClass: 'reports-overview-gradient-info'
    }
  ];

  // Status Cards Data
  const statusCards = [
    {
      title: 'في الانتظار',
      value: getOrdersByStatus('pending'),
      icon: 'fas fa-clock',
      color: 'warning'
    },
    {
      title: 'قيد التحضير',
      value: getOrdersByStatus('preparing'),
      icon: 'fas fa-utensils',
      color: 'primary'
    },
    {
      title: 'جاهز',
      value: getOrdersByStatus('ready'),
      icon: 'fas fa-bell',
      color: 'success'
    },
    {
      title: 'مكتمل',
      value: getOrdersByStatus('completed'),
      icon: 'fas fa-check',
      color: 'info'
    }
  ];

  return (
    <div className="reports-bootstrap-container container-fluid py-4">
      {/* Header */}
      <div className="text-center mb-4">
        <h1 className="display-4 text-white mb-2">
          <i className="fas fa-chart-bar me-3"></i>
          التقارير والإحصائيات
        </h1>
        <p className="lead text-white-50 mb-4">تحليل مفصل لأداء المقهى والمبيعات</p>
        
        {/* Filter */}
        <div className="row justify-content-center">
          <div className="col-auto">
            <select
              value={dateFilter}
              onChange={(e) => setDateFilter(e.target.value as any)}
              className="form-select form-select-lg reports-filter-select"
              title="اختر فترة التقرير"
            >
              <option value="today">اليوم</option>
              <option value="week">هذا الأسبوع</option>
              <option value="month">هذا الشهر</option>
              <option value="all">جميع الفترات</option>
            </select>
          </div>
        </div>
      </div>

      {/* Overview Statistics */}
      <div className="mb-5">
        <h2 className="text-white text-center mb-4">نظرة عامة - {getDateFilterLabel()}</h2>
        <ResponsiveGrid
          cols={{ xs: 1, sm: 2, md: 2, lg: 4, xl: 4, xxl: 4 }}
          gap={3}
        >
          {overviewCards.map((card, index) => (
            <ResponsiveCard key={index} className="reports-overview-card">
              <div className="card-body d-flex align-items-center">
                <div 
                  className={`reports-card-icon me-3 ${card.gradientClass}`}
                >
                  <i className={card.icon}></i>
                </div>
                <div>
                  <h3 className="h2 mb-1 fw-bold text-dark">{card.value}</h3>
                  <p className="mb-0 text-muted fw-medium">{card.title}</p>
                </div>
              </div>
              <div 
                className={`reports-card-border ${card.gradientClass}`}
              ></div>
            </ResponsiveCard>
          ))}
        </ResponsiveGrid>
      </div>

      {/* Order Status Breakdown */}
      <div className="mb-5">
        <h2 className="text-white text-center mb-4">توزيع حالات الطلبات</h2>
        <ResponsiveGrid
          cols={{ xs: 1, sm: 2, md: 2, lg: 4, xl: 4, xxl: 4 }}
          gap={3}
        >
          {statusCards.map((card, index) => (
            <ResponsiveCard key={index} className="reports-status-card text-center">
              <div className="card-body">
                <div className={`reports-status-icon text-${card.color} mb-3`}>
                  <i className={card.icon}></i>
                </div>
                <h3 className={`h1 mb-2 fw-bold text-${card.color}`}>{card.value}</h3>
                <p className="mb-0 text-muted fw-medium">{card.title}</p>
              </div>
            </ResponsiveCard>
          ))}
        </ResponsiveGrid>
      </div>

      {/* Top Products */}
      <div className="row mb-5">
        <div className="col-12 col-lg-6 mb-4">
          <ResponsiveCard className="h-100">
            <div className="card-header bg-transparent border-0">
              <h3 className="mb-0 text-dark">
                <i className="fas fa-trophy text-warning me-2"></i>
                أكثر المنتجات مبيعاً
              </h3>
            </div>
            <div className="card-body">
              {topProducts.length === 0 ? (
                <div className="text-center py-4">
                  <i className="fas fa-box-open display-4 text-muted mb-3"></i>
                  <p className="text-muted">لا توجد بيانات لعرضها</p>
                </div>
              ) : (
                <div className="list-group list-group-flush">
                  {topProducts.map((product, index) => {
                    const percentage = (product.quantity / (topProducts[0]?.quantity || 1)) * 100;
                    return (
                      <div key={product.name} className="list-group-item border-0 px-0">
                        <div className="d-flex justify-content-between align-items-center mb-2">
                          <div className="d-flex align-items-center">
                            <span className="badge bg-primary rounded-pill me-2">#{index + 1}</span>
                            <div>
                              <h6 className="mb-0 fw-bold">{product.name}</h6>
                              <small className="text-muted">{product.quantity} قطعة - {product.orders} طلب</small>
                            </div>
                          </div>
                        </div>
                        <div className="progress reports-product-progress">
                          <div 
                            className="progress-bar bg-gradient reports-product-bar" 
                            role="progressbar"
                            title={`${product.name}: ${percentage.toFixed(1)}%`}
                            data-width={percentage}
                          ></div>
                        </div>
                      </div>
                    );
                  })}
                </div>
              )}
            </div>
          </ResponsiveCard>
        </div>

        {/* Waiter Performance */}
        <div className="col-12 col-lg-6 mb-4">
          <ResponsiveCard className="h-100">
            <div className="card-header bg-transparent border-0">
              <h3 className="mb-0 text-dark">
                <i className="fas fa-users text-info me-2"></i>
                أداء النُدُل
              </h3>
            </div>
            <div className="card-body">
              {waiterPerformance.length === 0 ? (
                <div className="text-center py-4">
                  <i className="fas fa-user-slash display-4 text-muted mb-3"></i>
                  <p className="text-muted">لا توجد بيانات لعرضها</p>
                </div>
              ) : (
                <div className="list-group list-group-flush">
                  {waiterPerformance.map((waiter, index) => {
                    const percentage = (waiter.sales / (waiterPerformance[0]?.sales || 1)) * 100;
                    return (
                      <div key={waiter.name} className="list-group-item border-0 px-0">
                        <div className="d-flex justify-content-between align-items-center mb-2">
                          <div className="d-flex align-items-center">
                            <span className="badge bg-success rounded-pill me-2">#{index + 1}</span>
                            <div>
                              <h6 className="mb-0 fw-bold">{waiter.name}</h6>
                              <small className="text-muted">{waiter.orders} طلب - {waiter.sales.toFixed(2)} ج.م</small>
                            </div>
                          </div>
                        </div>
                        <div className="progress reports-waiter-progress">
                          <div 
                            className="progress-bar bg-success bg-gradient reports-waiter-bar" 
                            role="progressbar"
                            title={`${waiter.name}: ${percentage.toFixed(1)}%`}
                            data-width={percentage}
                          ></div>
                        </div>
                      </div>
                    );
                  })}
                </div>
              )}
            </div>
          </ResponsiveCard>
        </div>
      </div>

      {/* Loading Indicator */}
      {(loading || loadingDetailed) && (
        <div className="text-center py-5">
          <div className="spinner-border text-light reports-loading-spinner mb-3">
            <span className="visually-hidden">جاري التحميل...</span>
          </div>
          <h5 className="text-white">جاري تحميل التقارير...</h5>
        </div>
      )}
    </div>
  );
};

export default ReportsManagerScreenBootstrap;

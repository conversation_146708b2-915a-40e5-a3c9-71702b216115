/* ============================== */
/* Enhanced Discount Request Modal */
/* ============================== */

/* Modal Overlay */
.modal-overlay {
  position: fixed !important;
  top: 0 !important;
  left: 0 !important;
  right: 0 !important;
  bottom: 0 !important;
  background: rgba(0, 0, 0, 0.7) !important;
  backdrop-filter: blur(8px) !important;
  -webkit-backdrop-filter: blur(8px) !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  z-index: 1000000 !important;
  padding: 1rem !important;
  animation: fadeInOverlay 0.3s ease-out;
}

@keyframes fadeInOverlay {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

/* Enhanced Discount Modal */
.modal-content.discount-modal {
  background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%) !important;
  border-radius: 12px !important;
  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.2) !important;
  max-width: 480px !important;
  width: 90% !important;
  max-height: 85vh !important;
  overflow: hidden !important;
  position: relative !important;
  animation: slideInModal 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

@keyframes slideInModal {
  from {
    opacity: 0;
    transform: translateY(-50px) scale(0.9);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

/* Enhanced Modal Header */
.discount-modal .modal-header {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%) !important;
  color: white !important;
  padding: 1.25rem !important;
  border-bottom: none !important;
  border-radius: 12px 12px 0 0 !important;
  position: relative;
  overflow: hidden;
}

.discount-modal .modal-header::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><circle cx="20" cy="20" r="2" fill="rgba(255,255,255,0.1)"/><circle cx="80" cy="40" r="1.5" fill="rgba(255,255,255,0.1)"/><circle cx="40" cy="80" r="1" fill="rgba(255,255,255,0.1)"/></svg>');
  pointer-events: none;
}

.discount-modal .modal-header h2 {
  font-size: 1.4rem !important;
  font-weight: 600 !important;
  margin: 0 !important;
  display: flex !important;
  align-items: center !important;
  gap: 0.75rem !important;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  position: relative;
  z-index: 1;
}

.discount-modal .modal-header h2 i {
  font-size: 1.5rem !important;
  background: rgba(255, 255, 255, 0.2);
  padding: 0.5rem;
  border-radius: 50%;
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
}

.discount-modal .modal-close {
  background: rgba(255, 255, 255, 0.2) !important;
  border: none !important;
  color: white !important;
  width: 45px !important;
  height: 45px !important;
  border-radius: 50% !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  font-size: 1.25rem !important;
  cursor: pointer !important;
  transition: all 0.3s ease !important;
  backdrop-filter: blur(10px) !important;
  -webkit-backdrop-filter: blur(10px) !important;
  position: relative;
  z-index: 1;
}

.discount-modal .modal-close:hover {
  background: rgba(255, 255, 255, 0.3) !important;
  transform: scale(1.1) !important;
}

/* Enhanced Modal Body */
.discount-modal .modal-body {
  padding: 1.25rem !important;
  background: #ffffff !important;
  max-height: calc(85vh - 160px) !important;
  overflow-y: auto !important;
}

/* Order Summary Section */
.order-summary {
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
  border-radius: 8px;
  padding: 1rem;
  margin-bottom: 1.25rem;
  border: 1px solid #dee2e6;
  position: relative;
  overflow: hidden;
}

.order-summary::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 4px;
  height: 100%;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.order-summary h3 {
  font-size: 1.1rem;
  font-weight: 600;
  color: #495057;
  margin: 0 0 0.75rem 0;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.order-summary h3::before {
  content: '📋';
  font-size: 1rem;
}

.summary-info {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(180px, 1fr));
  gap: 0.75rem;
}

.summary-info > div {
  background: white;
  padding: 0.5rem;
  border-radius: 6px;
  border: 1px solid #e9ecef;
  font-weight: 500;
  color: #495057;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.summary-info > div:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.summary-info > div i {
  color: #667eea;
  font-size: 1rem;
  width: 18px;
  text-align: center;
}

.summary-info > div span {
  flex: 1;
  font-size: 0.85rem;
}

/* Discount Form Section */
.discount-form {
  background: white;
  border-radius: 8px;
  padding: 1rem;
  border: 1px solid #e9ecef;
  box-shadow: 0 1px 4px rgba(0, 0, 0, 0.05);
}

.form-group {
  margin-bottom: 1rem;
}

.form-group label {
  display: flex;
  align-items: center;
  gap: 0.4rem;
  font-weight: 600;
  color: #495057;
  margin-bottom: 0.5rem;
  font-size: 0.85rem;
}

.form-group label i {
  color: #667eea;
  font-size: 0.9rem;
}

.input-wrapper {
  position: relative;
  display: flex;
  align-items: center;
}

.input-suffix {
  position: absolute;
  left: 0.75rem;
  color: #6c757d;
  font-weight: 500;
  pointer-events: none;
  background: #f8f9fa;
  padding: 0.2rem 0.4rem;
  border-radius: 4px;
  font-size: 0.8rem;
}

.form-group input,
.form-group textarea {
  width: 100%;
  padding: 0.625rem 0.75rem;
  border: 2px solid #e9ecef;
  border-radius: 6px;
  font-size: 0.85rem;
  transition: all 0.3s ease;
  background: #ffffff;
  color: #495057;
  font-family: inherit;
}

.input-wrapper input {
  padding-left: 2.5rem;
}

.form-group input:focus,
.form-group textarea:focus {
  outline: none;
  border-color: #667eea;
  box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
  transform: translateY(-1px);
}

.form-group textarea {
  resize: vertical;
  min-height: 100px;
}

/* Discount Preview Section */
.discount-preview {
  background: linear-gradient(135deg, #e8f5e8 0%, #f0f8f0 100%);
  border: 2px solid #28a745;
  border-radius: 8px;
  padding: 1rem;
  margin-bottom: 1rem;
  text-align: center;
  position: relative;
  overflow: hidden;
}

.discount-preview::before {
  content: '💰';
  position: absolute;
  top: 1rem;
  left: 1rem;
  font-size: 1.2rem;
  opacity: 0.7;
}

.preview-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0.5rem 0;
  border-bottom: 1px solid rgba(40, 167, 69, 0.2);
  font-size: 0.85rem;
}

.preview-item:last-child {
  border-bottom: none;
}

.preview-item.total {
  font-weight: 700;
  font-size: 0.95rem;
  color: #28a745;
  background: rgba(40, 167, 69, 0.1);
  margin: 0.5rem -1.5rem -1.5rem -1.5rem;
  padding: 0.75rem 1.5rem;
  border-radius: 0 0 12px 12px;
}

/* Enhanced Modal Footer */
.discount-modal .modal-footer {
  padding: 1rem 1.25rem !important;
  background: #f8f9fa !important;
  border-top: 1px solid #dee2e6 !important;
  display: flex !important;
  gap: 0.75rem !important;
  justify-content: flex-end !important;
  border-radius: 0 0 12px 12px !important;
}

.discount-modal .btn-submit {
  background: linear-gradient(135deg, #28a745 0%, #20c997 100%) !important;
  color: white !important;
  border: none !important;
  padding: 0.625rem 1.5rem !important;
  border-radius: 6px !important;
  font-size: 0.85rem !important;
  font-weight: 600 !important;
  cursor: pointer !important;
  transition: all 0.3s ease !important;
  display: flex !important;
  align-items: center !important;
  gap: 0.5rem !important;
  box-shadow: 0 4px 12px rgba(40, 167, 69, 0.3) !important;
}

.discount-modal .btn-submit:hover:not(:disabled) {
  transform: translateY(-2px) !important;
  box-shadow: 0 6px 20px rgba(40, 167, 69, 0.4) !important;
}

.discount-modal .btn-submit:disabled {
  background: #6c757d !important;
  cursor: not-allowed !important;
  transform: none !important;
  box-shadow: none !important;
}

.discount-modal .btn-cancel {
  background: #6c757d !important;
  color: white !important;
  border: none !important;
  padding: 0.625rem 1.5rem !important;
  border-radius: 6px !important;
  font-size: 0.85rem !important;
  font-weight: 600 !important;
  cursor: pointer !important;
  transition: all 0.3s ease !important;
}

.discount-modal .btn-cancel:hover {
  background: #5a6268 !important;
  transform: translateY(-1px) !important;
}

/* Responsive Design */
@media (max-width: 768px) {
  .modal-content.discount-modal {
    max-width: 95% !important;
    margin: 1rem !important;
  }
  
  .discount-modal .modal-header {
    padding: 1.5rem !important;
  }
  
  .discount-modal .modal-header h2 {
    font-size: 1.5rem !important;
  }
  
  .discount-modal .modal-body {
    padding: 1.5rem !important;
  }
  
  .discount-modal .modal-footer {
    padding: 1rem 1.5rem !important;
    flex-direction: column !important;
  }
  
  .discount-modal .btn-submit,
  .discount-modal .btn-cancel {
    width: 100% !important;
    justify-content: center !important;
  }
  
  .summary-info {
    grid-template-columns: 1fr !important;
  }
}

@media (max-width: 480px) {
  .modal-overlay {
    padding: 0.5rem !important;
  }
  
  .discount-modal .modal-header h2 {
    font-size: 1.3rem !important;
    gap: 0.5rem !important;
  }
  
  .discount-modal .modal-header h2 i {
    font-size: 1.5rem !important;
    padding: 0.5rem !important;
  }
}

/* Animation for form validation */
.form-group input.error,
.form-group textarea.error {
  border-color: #dc3545;
  animation: shake 0.5s ease-in-out;
}

@keyframes shake {
  0%, 100% { transform: translateX(0); }
  25% { transform: translateX(-5px); }
  75% { transform: translateX(5px); }
}

/* Loading state for submit button */
.discount-modal .btn-submit.loading {
  position: relative;
  color: transparent !important;
}

.discount-modal .btn-submit.loading::after {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 20px;
  height: 20px;
  border: 2px solid rgba(255, 255, 255, 0.3);
  border-top: 2px solid white;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: translate(-50%, -50%) rotate(0deg); }
  100% { transform: translate(-50%, -50%) rotate(360deg); }
}
/* ??????? ????????? ??????? */
@import '../variables/settings-variables.css';

/* Enhanced Settings Card Styles */

.settings-action-card-enhanced {
  background: linear-gradient(145deg, #ffffff 0%, #f8f9fa 100%);
  border-radius: 16px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  transition: all 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
  border: none;
  position: relative;
  overflow: hidden;
  min-height: 280px;
  cursor: pointer;
}

.settings-action-card-enhanced::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.1), rgba(255, 255, 255, 0.05));
  pointer-events: none;
  transition: opacity 0.3s ease;
}

.settings-action-card-enhanced:hover {
  transform: translateY(-8px) scale(1.02);
  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.15);
}

.settings-action-card-enhanced:hover::before {
  opacity: 0.8;
}

.settings-action-card-enhanced.danger {
  border: 2px solid rgba(220, 53, 69, 0.2);
  animation: dangerPulse 3s infinite;
}

@keyframes dangerPulse {
  0%, 100% {
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  }
  50% {
    box-shadow: 0 8px 32px rgba(220, 53, 69, 0.3);
  }
}

/* Status Bar */
.settings-status-bar {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  border-radius: 16px 16px 0 0;
}

.settings-status-bar.warning {
  background: linear-gradient(90deg, #f39c12, #e67e22);
}

.settings-status-bar.info {
  background: linear-gradient(90deg, #3498db, #2980b9);
}

.settings-status-bar.danger {
  background: linear-gradient(90deg, #e74c3c, #c0392b);
}

/* Header */
.settings-card-header {
  text-align: center;
  padding: 1.5rem 1rem 1rem;
}

.settings-action-icon {
  width: 70px;
  height: 70px;
  margin: 0 auto 1rem;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 2rem;
  color: white;
  transition: all 0.3s ease;
}

.settings-action-icon.warning {
  background: linear-gradient(135deg, #f39c12, #e67e22);
}

.settings-action-icon.info {
  background: linear-gradient(135deg, #3498db, #2980b9);
}

.settings-action-icon.danger {
  background: linear-gradient(135deg, #e74c3c, #c0392b);
}

.settings-action-card-enhanced:hover .settings-action-icon {
  transform: scale(1.1) rotate(5deg);
}

.settings-action-title {
  font-size: 1.2rem;
  font-weight: 700;
  color: #2c3e50;
  margin: 0;
  line-height: 1.3;
}

/* Body */
.settings-card-body {
  padding: 0 1.5rem 1.5rem;
}

.settings-action-description {
  font-size: 0.95rem;
  color: #6c757d;
  line-height: 1.5;
  margin-bottom: 1rem;
  text-align: center;
}

/* Warning Box */
.settings-warning-box {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  padding: 0.75rem 1rem;
  border-radius: 12px;
  margin-bottom: 1.5rem;
  font-weight: 600;
  font-size: 0.9rem;
  border: 2px solid transparent;
  transition: all 0.3s ease;
}

.settings-warning-box.warning {
  background: linear-gradient(135deg, rgba(243, 156, 18, 0.1), rgba(230, 126, 34, 0.05));
  color: #856404;
  border-color: rgba(243, 156, 18, 0.3);
}

.settings-warning-box.info {
  background: linear-gradient(135deg, rgba(52, 152, 219, 0.1), rgba(41, 128, 185, 0.05));
  color: #0c5460;
  border-color: rgba(52, 152, 219, 0.3);
}

.settings-warning-box.danger {
  background: linear-gradient(135deg, rgba(231, 76, 60, 0.1), rgba(192, 57, 43, 0.05));
  color: #721c24;
  border-color: rgba(231, 76, 60, 0.3);
  animation: warningPulse 2s infinite;
}

@keyframes warningPulse {
  0%, 100% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.02);
  }
}

.settings-warning-box i {
  font-size: 1.1rem;
  flex-shrink: 0;
}

/* Action Button */
.settings-action-btn {
  width: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.75rem;
  padding: 1rem 1.5rem;
  border: none;
  border-radius: 12px;
  font-weight: 700;
  font-size: 1rem;
  transition: all 0.3s ease;
  cursor: pointer;
  position: relative;
  overflow: hidden;
}

.settings-action-btn::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left 0.5s ease;
}

.settings-action-btn:hover::before {
  left: 100%;
}

.settings-action-btn.warning {
  background: linear-gradient(135deg, #f39c12, #e67e22);
  color: white;
  box-shadow: 0 4px 15px rgba(243, 156, 18, 0.3);
}

.settings-action-btn.warning:hover {
  background: linear-gradient(135deg, #e67e22, #d35400);
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(243, 156, 18, 0.4);
}

.settings-action-btn.info {
  background: linear-gradient(135deg, #3498db, #2980b9);
  color: white;
  box-shadow: 0 4px 15px rgba(52, 152, 219, 0.3);
}

.settings-action-btn.info:hover {
  background: linear-gradient(135deg, #2980b9, #1f4e79);
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(52, 152, 219, 0.4);
}

.settings-action-btn.danger {
  background: linear-gradient(135deg, #e74c3c, #c0392b);
  color: white;
  box-shadow: 0 4px 15px rgba(231, 76, 60, 0.3);
}

.settings-action-btn.danger:hover {
  background: linear-gradient(135deg, #c0392b, #a93226);
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(231, 76, 60, 0.4);
}

.settings-action-btn i {
  font-size: 1.1rem;
}

.settings-action-btn span {
  font-weight: 700;
}

/* Responsive Design */
@media (max-width: 768px) {
  .settings-action-card-enhanced {
    min-height: 260px;
  }
  
  .settings-card-header {
    padding: 1rem 0.75rem 0.75rem;
  }
  
  .settings-action-icon {
    width: 60px;
    height: 60px;
    font-size: 1.8rem;
  }
  
  .settings-action-title {
    font-size: 1.1rem;
  }
  
  .settings-card-body {
    padding: 0 1rem 1rem;
  }
  
  .settings-action-description {
    font-size: 0.9rem;
  }
  
  .settings-warning-box {
    padding: 0.6rem 0.8rem;
    font-size: 0.85rem;
    gap: 0.5rem;
  }
  
  .settings-action-btn {
    padding: 0.8rem 1.2rem;
    font-size: 0.9rem;
    gap: 0.5rem;
  }
}

@media (max-width: 480px) {
  .settings-action-card-enhanced {
    min-height: 240px;
  }
  
  .settings-card-header {
    padding: 0.75rem 0.5rem 0.5rem;
  }
  
  .settings-action-icon {
    width: 55px;
    height: 55px;
    font-size: 1.6rem;
  }
  
  .settings-action-title {
    font-size: 1rem;
  }
  
  .settings-card-body {
    padding: 0 0.75rem 0.75rem;
  }
  
  .settings-action-description {
    font-size: 0.85rem;
    margin-bottom: 0.75rem;
  }
  
  .settings-warning-box {
    padding: 0.5rem 0.7rem;
    font-size: 0.8rem;
    margin-bottom: 1rem;
  }
  
  .settings-action-btn {
    padding: 0.7rem 1rem;
    font-size: 0.85rem;
  }
  
  .settings-action-btn i {
    font-size: 1rem;
  }
}


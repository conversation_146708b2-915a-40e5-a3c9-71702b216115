const mongoose = require('mongoose');

// الاتصال بقاعدة البيانات
const mongoUri = process.env.MONGODB_URI || 'mongodb+srv://besomustafa:<EMAIL>/deshacoffee?retryWrites=true&w=majority&appName=MyCoffeChop';

mongoose.connect(mongoUri, {
  useNewUrlParser: true,
  useUnifiedTopology: true,
}).then(() => {
  console.log('تم الاتصال بقاعدة البيانات بنجاح');
}).catch(err => {
  console.error('خطأ في الاتصال بقاعدة البيانات:', err);
});

// نماذج البيانات
const orderSchema = new mongoose.Schema({
  total: Number,
  discount: { type: Number, default: 0 },
  status: String,
  assignedWaiter: { type: mongoose.Schema.Types.ObjectId, ref: 'User' },
  createdAt: { type: Date, default: Date.now }
});

const userSchema = new mongoose.Schema({
  username: String,
  role: String
});

const Order = mongoose.model('Order', orderSchema);
const User = mongoose.model('User', userSchema);

async function createDemoDataAndCalculate() {
  try {
    console.log('=== إنشاء بيانات تجريبية ===');
    
    // إنشاء نُدل تجريبيين
    const waiter1 = await User.findOneAndUpdate(
      { username: 'أحمد' },
      { username: 'أحمد', role: 'waiter' },
      { upsert: true, new: true }
    );
    
    const waiter2 = await User.findOneAndUpdate(
      { username: 'محمد' },
      { username: 'محمد', role: 'waiter' },
      { upsert: true, new: true }
    );
    
    const waiter3 = await User.findOneAndUpdate(
      { username: 'سارة' },
      { username: 'سارة', role: 'waiter' },
      { upsert: true, new: true }
    );
    
    console.log('تم إنشاء النُدل التجريبيين');
    
    // إنشاء طلبات تجريبية
    const demoOrders = [
      // طلبات أحمد
      { total: 150, discount: 10, status: 'completed', assignedWaiter: waiter1._id },
      { total: 200, discount: 0, status: 'completed', assignedWaiter: waiter1._id },
      { total: 175, discount: 15, status: 'completed', assignedWaiter: waiter1._id },
      { total: 120, discount: 5, status: 'completed', assignedWaiter: waiter1._id },
      { total: 180, discount: 20, status: 'completed', assignedWaiter: waiter1._id },
      
      // طلبات محمد
      { total: 250, discount: 25, status: 'completed', assignedWaiter: waiter2._id },
      { total: 180, discount: 0, status: 'completed', assignedWaiter: waiter2._id },
      { total: 220, discount: 30, status: 'completed', assignedWaiter: waiter2._id },
      { total: 160, discount: 10, status: 'completed', assignedWaiter: waiter2._id },
      
      // طلبات سارة
      { total: 300, discount: 50, status: 'completed', assignedWaiter: waiter3._id },
      { total: 275, discount: 25, status: 'completed', assignedWaiter: waiter3._id },
      { total: 190, discount: 0, status: 'completed', assignedWaiter: waiter3._id },
      { total: 210, discount: 15, status: 'completed', assignedWaiter: waiter3._id },
      { total: 240, discount: 20, status: 'completed', assignedWaiter: waiter3._id },
      { total: 165, discount: 5, status: 'completed', assignedWaiter: waiter3._id },
      
      // طلبات بدون نادل محدد
      { total: 100, discount: 0, status: 'completed', assignedWaiter: null },
      { total: 85, discount: 5, status: 'completed', assignedWaiter: null },
    ];
    
    await Order.insertMany(demoOrders);
    console.log('تم إنشاء', demoOrders.length, 'طلب تجريبي');
    
    // حساب المبيعات والإحصائيات
    await calculateSalesAndWaiters();
    
  } catch (error) {
    console.error('خطأ في إنشاء البيانات التجريبية:', error);
  }
}

async function calculateSalesAndWaiters() {
  try {
    console.log('\n=== حساب المبيعات وإحصائيات النُدل ===');
    
    const orders = await Order.find({ status: 'completed' }).populate('assignedWaiter', 'username');
    
    if (orders.length === 0) {
      console.log('لا توجد طلبات مكتملة في قاعدة البيانات');
      return;
    }

    console.log('عدد الطلبات المكتملة:', orders.length);
    
    let totalSalesBeforeDiscount = 0;
    let totalDiscounts = 0;
    let totalNetSales = 0;
    
    const waiterStats = {};
    
    orders.forEach(order => {
      const orderTotal = order.total || 0;
      const orderDiscount = order.discount || 0;
      const orderNet = orderTotal - orderDiscount;
      
      totalSalesBeforeDiscount += orderTotal;
      totalDiscounts += orderDiscount;
      totalNetSales += orderNet;
      
      const waiterName = order.assignedWaiter?.username || 'غير محدد';
      
      if (!waiterStats[waiterName]) {
        waiterStats[waiterName] = {
          orderCount: 0,
          totalSales: 0,
          totalDiscounts: 0,
          netSales: 0
        };
      }
      
      waiterStats[waiterName].orderCount++;
      waiterStats[waiterName].totalSales += orderTotal;
      waiterStats[waiterName].totalDiscounts += orderDiscount;
      waiterStats[waiterName].netSales += orderNet;
    });
    
    console.log('\n🏪 إجمالي المبيعات:');
    console.log('💰 إجمالي المبيعات قبل الخصم:', totalSalesBeforeDiscount.toFixed(2), 'ريال');
    console.log('🎯 إجمالي الخصومات:', totalDiscounts.toFixed(2), 'ريال');
    console.log('✅ صافي المبيعات بعد الخصم:', totalNetSales.toFixed(2), 'ريال');
    console.log('📊 نسبة الخصومات:', ((totalDiscounts / totalSalesBeforeDiscount) * 100).toFixed(1) + '%');
    
    console.log('\n👥 إحصائيات النُدل:');
    console.log('='.repeat(60));
    
    // ترتيب النُدل حسب صافي المبيعات
    const sortedWaiters = Object.entries(waiterStats).sort((a, b) => b[1].netSales - a[1].netSales);
    
    sortedWaiters.forEach(([waiterName, stats], index) => {
      console.log(`🏆 ${index + 1}. النادل: ${waiterName}`);
      console.log(`   📦 عدد الطلبات: ${stats.orderCount}`);
      console.log(`   💰 إجمالي المبيعات: ${stats.totalSales.toFixed(2)} ريال`);
      console.log(`   🎯 إجمالي الخصومات: ${stats.totalDiscounts.toFixed(2)} ريال`);
      console.log(`   ✅ صافي المبيعات: ${stats.netSales.toFixed(2)} ريال`);
      console.log(`   📈 متوسط قيمة الطلب: ${(stats.netSales / stats.orderCount).toFixed(2)} ريال`);
      console.log(`   📊 نسبة من الإجمالي: ${((stats.netSales / totalNetSales) * 100).toFixed(1)}%`);
      console.log('   ' + '-'.repeat(40));
    });
    
    // إحصائيات إضافية
    console.log('\n📈 إحصائيات إضافية:');
    console.log('👥 عدد النُدل النشطين:', Object.keys(waiterStats).length);
    console.log('📦 متوسط الطلبات لكل نادل:', (orders.length / Object.keys(waiterStats).length).toFixed(1));
    console.log('💰 متوسط قيمة الطلب الإجمالي:', (totalNetSales / orders.length).toFixed(2), 'ريال');
    
    process.exit(0);
    
  } catch (error) {
    console.error('خطأ في حساب المبيعات:', error);
    process.exit(1);
  }
}

// تشغيل السكريبت
createDemoDataAndCalculate();

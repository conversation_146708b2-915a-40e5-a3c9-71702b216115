const mongoose = require('mongoose');

// اتصال بقاعدة البيانات (MongoDB Atlas Production)
const mongoUri = 'mongodb+srv://besomustafa:<EMAIL>/deshacoffee?retryWrites=true&w=majority&appName=MyCoffeChop';

mongoose.connect(mongoUri, {
  serverSelectionTimeoutMS: 10000,
  socketTimeoutMS: 45000,
  family: 4
});

// تعريف النماذج
const userSchema = new mongoose.Schema({
  name: String,
  username: String,
  password: String,
  role: String
}, { strict: false });

const tableSchema = new mongoose.Schema({
  number: Number,
  name: String,
  section: String,
  isActive: Boolean,
  isOccupied: Boolean,
  assignedWaiter: String
}, { strict: false });

const orderSchema = new mongoose.Schema({
  orderNumber: String,
  tableNumber: Number,
  waiterName: String,
  status: String,
  totals: Object
}, { strict: false });

const User = mongoose.model('User', userSchema);
const Table = mongoose.model('Table', tableSchema);
const Order = mongoose.model('Order', orderSchema);

async function checkDatabase() {
  try {
    console.log('🔍 فحص قاعدة البيانات...');
    
    // فحص المستخدمين
    const users = await User.find({}).select('name username role').limit(10);
    console.log(`👥 عدد المستخدمين: ${users.length}`);
    if (users.length > 0) {
      console.log('📋 المستخدمون المتاحون:');
      users.forEach((user, index) => {
        console.log(`  ${index + 1}. ${user.name || 'بدون اسم'} (${user.username}) - ${user.role || 'بدون دور'}`);
      });
    }
    console.log('---');

    // فحص الطاولات
    const tables = await Table.find({}).limit(10);
    console.log(`🏢 عدد الطاولات: ${tables.length}`);
    if (tables.length > 0) {
      console.log('📋 الطاولات المتاحة:');
      tables.forEach((table, index) => {
        console.log(`  ${index + 1}. طاولة رقم ${table.number} - ${table.name || 'بدون اسم'}`);
        console.log(`     القسم: ${table.section || 'غير محدد'}`);
        console.log(`     نشطة: ${table.isActive ? 'نعم' : 'لا'}`);
        console.log(`     مشغولة: ${table.isOccupied ? 'نعم' : 'لا'}`);
        console.log(`     النادل المخصص: ${table.assignedWaiter || 'غير محدد'}`);
        console.log('     ---');
      });
    }
    console.log('---');

    // فحص الطلبات
    const orders = await Order.find({}).sort({ createdAt: -1 }).limit(5);
    console.log(`📋 عدد الطلبات: ${orders.length}`);
    if (orders.length > 0) {
      console.log('📋 آخر الطلبات:');
      orders.forEach((order, index) => {
        console.log(`  ${index + 1}. ${order.orderNumber || 'بدون رقم'}`);
        console.log(`     الطاولة: ${order.tableNumber || 'غير محدد'}`);
        console.log(`     النادل: ${order.waiterName || 'غير محدد'}`);
        console.log(`     الحالة: ${order.status || 'غير محدد'}`);
        console.log(`     المبلغ: ${order.totals?.total || 'غير محدد'} ج.م`);
        console.log('     ---');
      });
    }

  } catch (error) {
    console.error('❌ خطأ في فحص قاعدة البيانات:', error.message);
  } finally {
    await mongoose.disconnect();
    console.log('📝 تم قطع الاتصال بقاعدة البيانات');
  }
}

// تشغيل الفحص
checkDatabase().catch(console.error);

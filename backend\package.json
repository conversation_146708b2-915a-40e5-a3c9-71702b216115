{"name": "desha-coffee-backend", "version": "1.0.1", "description": "Unified Backend API for Desha Coffee Management System with Mobile Optimization", "main": "server.js", "scripts": {"start": "node server.js", "dev": "nodemon server.js", "dev:local": "cross-env NODE_ENV=development nodemon --env-file=.env.local server.js", "dev:prod": "nodemon --env-file=.env.production server.js", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "seed": "node scripts/seedDatabase.js", "seed:prod": "NODE_ENV=production node scripts/seedDatabase.js", "seed:dev": "NODE_ENV=development node scripts/seedDatabase.js", "lint": "eslint . --ext .js", "lint:fix": "eslint . --ext .js --fix", "validate": "npm run lint && npm run test", "clean": "rm -rf node_modules package-lock.json && npm install", "check-deps": "npm audit && npm outdated", "docs": "node scripts/generateDocs.js", "build": "echo 'No build step required for Node.js backend'", "deploy": "npm run validate && npm start", "deploy:railway": "railway deploy", "deploy:test": "NODE_ENV=test npm start", "health": "curl http://localhost:${PORT:-4010}/health || curl http://localhost:3001/health"}, "keywords": ["coffee", "management", "api", "nodejs", "express", "mobile", "unified", "restaurant", "pos", "orders", "inventory"], "author": "MediaFuture", "license": "MIT", "dependencies": {"bcryptjs": "^2.4.3", "compression": "^1.7.4", "cors": "^2.8.5", "dotenv": "^16.3.1", "express": "^4.18.2", "express-rate-limit": "^6.10.0", "express-validator": "^7.0.1", "helmet": "^7.0.0", "joi": "^17.13.3", "jsonwebtoken": "^9.0.2", "mongoose": "^7.5.0", "multer": "^1.4.5-lts.1", "node-cron": "^3.0.3", "sharp": "^0.32.6", "socket.io": "^4.7.2", "validator": "^13.11.0", "winston": "^3.17.0"}, "devDependencies": {"cross-env": "^7.0.3", "eslint": "^8.45.0", "jest": "^29.6.2", "mongodb-memory-server": "^10.1.4", "node-fetch": "^3.3.2", "nodemon": "^3.0.1", "supertest": "^6.3.3"}, "engines": {"node": ">=18.0.0", "npm": ">=9.0.0"}, "repository": {"type": "git", "url": "git+https://github.com/MediaFuture/desha-coffee.git"}, "bugs": {"url": "https://github.com/MediaFuture/desha-coffee/issues"}, "homepage": "https://github.com/MediaFuture/desha-coffee#readme"}
const mongoose = require('mongoose');
const bcrypt = require('bcrypt');
require('dotenv').config();

const userSchema = new mongoose.Schema({}, { strict: false });
const User = mongoose.model('User', userSchema);

async function testPassword() {
  try {
    await mongoose.connect(process.env.MONGODB_URI);
    console.log('🔗 اتصال بقاعدة البيانات...');
    
    // البحث عن Beso
    const besoUser = await User.findOne({ username: 'Beso' });
    
    if (besoUser) {
      console.log('👤 وجد Beso');
      console.log('Password Hash:', besoUser.password);
      
      // جرب كلمات مرور مختلفة
      const passwords = ['admin123', 'beso123', '123456', 'password', 'Beso123', 'beso'];
      
      for (const pwd of passwords) {
        const match = await bcrypt.compare(pwd, besoUser.password);
        console.log(`${pwd}: ${match ? '✅ صحيح' : '❌ خطأ'}`);
        if (match) break;
      }
      
      // إذا لم تعمل أي كلمة مرور، دعني أحدث كلمة المرور
      console.log('\n🔧 تحديث كلمة المرور...');
      const newHash = await bcrypt.hash('admin123', 10);
      await User.updateOne({ _id: besoUser._id }, { password: newHash });
      console.log('✅ تم تحديث كلمة المرور إلى: admin123');
    }
    
    process.exit(0);
  } catch (error) {
    console.error('❌ خطأ:', error);
    process.exit(1);
  }
}

testPassword();

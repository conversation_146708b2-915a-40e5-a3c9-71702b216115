<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار مودال تفاصيل الخصم المحسن - نهائي</title>
    
    <!-- استيراد ملفات CSS بنفس ترتيب ManagerDashboard -->
    <link rel="stylesheet" href="src/ManagerDashboard.css">
    <link rel="stylesheet" href="src/ManagerDashboard-fix.css">
    <link rel="stylesheet" href="src/OrderDetailsModal.css">
    <link rel="stylesheet" href="src/DiscountRequestsScreen.css">
    <link rel="stylesheet" href="src/popular-products.css">
    <link rel="stylesheet" href="src/ManagerDashboard-additional.css">
    <link rel="stylesheet" href="src/DiscountDetailsModal.css">
    <link rel="stylesheet" href="src/DiscountDetailsModal-override.css">
    
    <style>
        body {
            margin: 0;
            padding: 20px;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: #f5f5f5;
            direction: rtl;
        }
        
        .test-container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            padding: 2rem;
            border-radius: 12px;
            box-shadow: 0 4px 12px rgba(0,0,0,0.1);
        }
        
        .test-header {
            text-align: center;
            margin-bottom: 2rem;
            color: #2c3e50;
        }
        
        .test-btn {
            background: #8e44ad;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 8px;
            cursor: pointer;
            font-size: 16px;
            font-weight: 600;
            transition: all 0.3s ease;
            display: block;
            margin: 20px auto;
        }
        
        .test-btn:hover {
            background: #9b59b6;
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(142, 68, 173, 0.3);
        }
        
        .features-list {
            background: #f8f9fa;
            padding: 1.5rem;
            border-radius: 8px;
            margin: 20px 0;
            border-left: 4px solid #8e44ad;
        }
        
        .features-list h3 {
            color: #8e44ad;
            margin-top: 0;
        }
        
        .features-list ul {
            margin: 0;
            padding-right: 20px;
        }
        
        .features-list li {
            margin: 8px 0;
            color: #495057;
        }
        
        .note {
            background: #d4edda;
            border: 1px solid #c3e6cb;
            color: #155724;
            padding: 12px;
            border-radius: 6px;
            margin: 15px 0;
            font-size: 14px;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <div class="test-header">
            <h1>🧪 اختبار مودال تفاصيل الخصم المحسن - النسخة النهائية</h1>
            <p>اختبار شامل لجميع التحسينات مع حل مشاكل CSS Override</p>
        </div>
        
        <div class="features-list">
            <h3>✅ التحسينات المطبقة:</h3>
            <ul>
                <li>إعادة ترتيب استيراد ملفات CSS لحل التضارب</li>
                <li>إنشاء ملف CSS منفصل بأولوية عالية (DiscountDetailsModal-override.css)</li>
                <li>استخدام Body selector لزيادة specificity</li>
                <li>تطبيق !important على جميع التنسيقات الحرجة</li>
                <li>تخطيط أفقي محسن (عمودان: معلومات + مالية)</li>
                <li>جدول العناصر يمتد بعرض المودال الكامل</li>
                <li>تصميم متجاوب للشاشات الصغيرة</li>
                <li>تحسين الألوان والمسافات</li>
                <li>حل مشاكل override من OrderDetailsModal.css</li>
            </ul>
        </div>
        
        <div class="note">
            <strong>ملاحظة:</strong> تم إنشاء ملف CSS إضافي (DiscountDetailsModal-override.css) خصيصاً للتغلب على تضارب الأنماط مع الملفات الأخرى.
        </div>
        
        <button class="test-btn" onclick="openDiscountModal()">
            🔍 اختبار مودال تفاصيل الخصم
        </button>
    </div>

    <!-- Modal Overlay -->
    <div class="modal-overlay" id="discountModalOverlay" style="display: none;">
        <div class="discount-details-modal">
            <div class="modal-content">
                <!-- Header -->
                <div class="modal-header">
                    <h3>
                        <span>💰</span>
                        تفاصيل طلب الخصم #DC-2024-001
                    </h3>
                    <button class="close-btn" onclick="closeDiscountModal()">×</button>
                </div>

                <!-- Body with horizontal layout -->
                <div class="modal-body">
                    <!-- المعلومات الأساسية -->
                    <div class="basic-info-section">
                        <div class="section-title">
                            <span>📋</span>
                            المعلومات الأساسية
                        </div>
                        
                        <div class="info-item">
                            <span class="info-label">رقم الطلب:</span>
                            <span class="info-value">#ORD-2024-001</span>
                        </div>
                        
                        <div class="info-item">
                            <span class="info-label">رقم الطاولة:</span>
                            <span class="info-value">5</span>
                        </div>
                        
                        <div class="info-item">
                            <span class="info-label">اسم العميل:</span>
                            <span class="info-value">أحمد محمد</span>
                        </div>
                        
                        <div class="info-item">
                            <span class="info-label">النادل:</span>
                            <span class="info-value">سارة أحمد</span>
                        </div>
                        
                        <div class="info-item">
                            <span class="info-label">تاريخ الطلب:</span>
                            <span class="info-value">2024-01-15 14:30</span>
                        </div>
                        
                        <div class="info-item">
                            <span class="info-label">حالة الخصم:</span>
                            <span class="info-value">
                                <span class="status-badge status-approved">موافق عليه</span>
                            </span>
                        </div>
                        
                        <div class="info-item">
                            <span class="info-label">سبب الخصم:</span>
                            <span class="info-value">خدمة متأخرة</span>
                        </div>
                    </div>

                    <!-- المعلومات المالية -->
                    <div class="discount-financial-section">
                        <div class="section-title">
                            <span>💰</span>
                            المعلومات المالية
                        </div>
                        
                        <div class="info-item">
                            <span class="info-label">المجموع الفرعي:</span>
                            <span class="info-value">85.00 ر.س</span>
                        </div>
                        
                        <div class="info-item">
                            <span class="info-label">الضريبة (15%):</span>
                            <span class="info-value">12.75 ر.س</span>
                        </div>
                        
                        <div class="info-item">
                            <span class="info-label">المجموع قبل الخصم:</span>
                            <span class="info-value">97.75 ر.س</span>
                        </div>
                        
                        <div class="info-item">
                            <span class="info-label">نوع الخصم:</span>
                            <span class="info-value">نسبة مئوية</span>
                        </div>
                        
                        <div class="info-item">
                            <span class="info-label">قيمة الخصم:</span>
                            <span class="info-value discount-amount">-14.66 ر.س (15%)</span>
                        </div>
                        
                        <div class="info-item">
                            <span class="info-label">المجموع النهائي:</span>
                            <span class="info-value total-amount">83.09 ر.س</span>
                        </div>
                        
                        <div class="info-item">
                            <span class="info-label">المبلغ المتوفر:</span>
                            <span class="info-value" style="color: #27ae60; font-weight: bold;">14.66 ر.س</span>
                        </div>
                    </div>

                    <!-- العناصر المطلوبة - عرض كامل -->
                    <div class="order-items-section full-width">
                        <div class="section-title">
                            <span>🍽️</span>
                            العناصر المطلوبة
                        </div>
                        
                        <table class="items-table">
                            <thead>
                                <tr>
                                    <th>العنصر</th>
                                    <th>الكمية</th>
                                    <th>السعر الواحد</th>
                                    <th>المجموع</th>
                                    <th>ملاحظات</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td>قهوة أمريكية</td>
                                    <td>2</td>
                                    <td>15.00 ر.س</td>
                                    <td>30.00 ر.س</td>
                                    <td>بدون سكر</td>
                                </tr>
                                <tr>
                                    <td>كابتشينو</td>
                                    <td>1</td>
                                    <td>18.00 ر.س</td>
                                    <td>18.00 ر.س</td>
                                    <td>حليب كامل الدسم</td>
                                </tr>
                                <tr>
                                    <td>كرواسون جبن</td>
                                    <td>2</td>
                                    <td>12.00 ر.س</td>
                                    <td>24.00 ر.س</td>
                                    <td>ساخن</td>
                                </tr>
                                <tr>
                                    <td>عصير برتقال طازج</td>
                                    <td>1</td>
                                    <td>13.00 ر.س</td>
                                    <td>13.00 ر.س</td>
                                    <td>بدون ثلج</td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        function openDiscountModal() {
            document.getElementById('discountModalOverlay').style.display = 'flex';
            document.body.style.overflow = 'hidden';
            
            // إضافة animation للمودال
            const modal = document.querySelector('.discount-details-modal');
            modal.style.transform = 'scale(0.8)';
            modal.style.opacity = '0';
            
            setTimeout(() => {
                modal.style.transition = 'all 0.3s ease';
                modal.style.transform = 'scale(1)';
                modal.style.opacity = '1';
            }, 10);
        }

        function closeDiscountModal() {
            const modal = document.querySelector('.discount-details-modal');
            modal.style.transform = 'scale(0.8)';
            modal.style.opacity = '0';
            
            setTimeout(() => {
                document.getElementById('discountModalOverlay').style.display = 'none';
                document.body.style.overflow = 'auto';
            }, 300);
        }

        // إغلاق المودال عند النقر خارجه
        document.getElementById('discountModalOverlay').addEventListener('click', function(e) {
            if (e.target === this) {
                closeDiscountModal();
            }
        });

        // إغلاق المودال بالضغط على ESC
        document.addEventListener('keydown', function(e) {
            if (e.key === 'Escape') {
                closeDiscountModal();
            }
        });

        // عرض معلومات التحميل
        window.addEventListener('load', function() {
            console.log('✅ تم تحميل جميع ملفات CSS بالترتيب الصحيح');
            console.log('📋 ملفات CSS المحملة:');
            console.log('1. ManagerDashboard.css');
            console.log('2. ManagerDashboard-fix.css'); 
            console.log('3. OrderDetailsModal.css');
            console.log('4. DiscountRequestsScreen.css');
            console.log('5. popular-products.css');
            console.log('6. ManagerDashboard-additional.css');
            console.log('7. DiscountDetailsModal.css');
            console.log('8. DiscountDetailsModal-override.css (أولوية عالية)');
        });
    </script>
</body>
</html>

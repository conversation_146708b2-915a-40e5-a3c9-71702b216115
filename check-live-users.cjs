const fetch = require('node-fetch');

const backEndURL = 'https://deshacoffee-production.up.railway.app';

async function checkLiveUsers() {
  try {
    console.log('🔐 تسجيل الدخول...');
    
    const loginResponse = await fetch(`${backEndURL}/api/v1/auth/login`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        username: '<PERSON><PERSON>',
        password: 'admin123'
      })
    });

    const loginData = await loginResponse.json();
    if (!loginData.token) {
      console.log('❌ فشل تسجيل الدخول:', loginData);
      return;
    }

    const token = loginData.token;
    console.log('✅ تم تسجيل الدخول بنجاح');

    // جلب النادلين
    console.log('\n👥 جلب جميع النادلين...');
    const usersResponse = await fetch(`${backEndURL}/api/v1/users`, {
      headers: {
        'Authorization': `Bearer ${token}`,
        'Content-Type': 'application/json'
      }
    });

    if (usersResponse.ok) {
      const usersData = await usersResponse.json();
      const users = usersData.users || usersData.data || usersData;
      
      console.log(`📊 إجمالي المستخدمين: ${Array.isArray(users) ? users.length : 'غير محدد'}`);
      
      if (Array.isArray(users)) {
        const waiters = users.filter(user => user.role === 'waiter');
        console.log(`👥 عدد النادلين: ${waiters.length}`);
        
        console.log('\n📋 قائمة النادلين:');
        waiters.forEach((waiter, index) => {
          console.log(`${index + 1}. Username: ${waiter.username}`);
          console.log(`   Name: ${waiter.name}`);
          console.log(`   ID: ${waiter._id}`);
          console.log(`   Active: ${waiter.isActive}`);
          console.log('   ---');
        });
        
        // البحث عن sara
        const sara = users.find(user => 
          user.username?.toLowerCase() === 'sara' || 
          user.name?.toLowerCase() === 'sara' ||
          user.name?.toLowerCase() === 'سارة'
        );
        
        if (sara) {
          console.log('\n👤 وجدت sara:');
          console.log(`ID: ${sara._id}`);
          console.log(`Username: ${sara.username}`);
          console.log(`Name: ${sara.name}`);
          console.log(`Role: ${sara.role}`);
        } else {
          console.log('\n❌ لم يتم العثور على sara');
        }
      }
    } else {
      console.log('❌ فشل في جلب النادلين');
      const errorText = await usersResponse.text();
      console.log('Error:', errorText);
    }

  } catch (error) {
    console.error('❌ خطأ:', error);
  }
}

checkLiveUsers();

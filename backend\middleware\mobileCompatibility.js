// Mobile compatibility middleware
// ميدلوير لضمان التوافق مع الأجهزة المحمولة

const mobileCompatibility = (req, res, next) => {
  // إضافة headers للتوافق مع الهواتف - محدث للعمل مع CORS الرئيسي
  if (!res.getHeader('Access-Control-Allow-Origin')) {
    res.header('Access-Control-Allow-Origin', req.headers.origin || '*');
  }
  if (!res.getHeader('Access-Control-Allow-Methods')) {
    res.header('Access-Control-Allow-Methods', 'GET,PUT,POST,DELETE,OPTIONS,PATCH');
  }
  if (!res.getHeader('Access-Control-Allow-Headers')) {
    res.header('Access-Control-Allow-Headers', 
      'Content-Type, Authorization, Content-Length, X-Requested-With, Accept, Origin, x-request-id, X-Request-ID, X-Correlation-ID, X-Session-ID, Cache-Control, Pragma'
    );
  }
  if (!res.getHeader('Access-Control-Allow-Credentials')) {
    res.header('Access-Control-Allow-Credentials', 'true');
  }
  
  // معالجة preflight requests
  if (req.method === 'OPTIONS') {
    return res.status(200).end();
  }
  
  // تسجيل معلومات الطلب للتشخيص
  const userAgent = req.get('User-Agent') || 'Unknown';
  const origin = req.get('Origin') || 'No origin';
  const isMobile = /Mobile|Android|iPhone|iPad/i.test(userAgent);
  
  console.log(`📱 Request from ${isMobile ? 'MOBILE' : 'DESKTOP'} device:`);
  console.log(`   User-Agent: ${userAgent}`);
  console.log(`   Origin: ${origin}`);
  console.log(`   Method: ${req.method}`);
  console.log(`   Path: ${req.path}`);
  
  // إضافة معلومات الجهاز إلى الطلب
  req.isMobile = isMobile;
  req.deviceInfo = {
    userAgent,
    origin,
    isMobile
  };
  
  next();
};

module.exports = mobileCompatibility;

import React from 'react';
import ResponsiveGrid from '../components/ResponsiveGrid';
import ResponsiveCard from '../components/ResponsiveCard';
import socket from '../socket';
import '../styles/inventory.css';


interface InventoryItem {
  _id: string;
  name: string;
  price: number;
  isAvailable: boolean;
  available?: boolean; // Legacy field
  description?: string;
  stock?: number | { quantity: number };
}

interface InventoryManagerScreenBootstrapProps {
  menuItems: InventoryItem[];
  isLoading: boolean;
  loadingStates: {
    inventory: boolean;
  };
  updatingItems?: Set<string>;
  onUpdateStock?: (itemId: string, newStock: number) => Promise<void>;
  onToggleAvailability?: (itemId: string, isAvailable: boolean) => Promise<void>;
}

const InventoryManagerScreenBootstrap: React.FC<InventoryManagerScreenBootstrapProps> = ({
  menuItems,
  isLoading,
  loadingStates,
  updatingItems = new Set(),
  onUpdateStock,
  onToggleAvailability
}) => {
  // Debug: Log props on component mount
  React.useEffect(() => {
    console.log('🔍 InventoryManagerScreenBootstrap Props:', {
      menuItemsCount: menuItems?.length || 0,
      isLoading,
      loadingStates,
      hasOnUpdateStock: !!onUpdateStock,
      hasOnToggleAvailability: !!onToggleAvailability
    });
  }, [menuItems, isLoading, loadingStates, onUpdateStock, onToggleAvailability]);

  // Socket.IO event listeners للتحديثات الفورية
  React.useEffect(() => {
    const handleInventoryUpdate = (inventoryUpdate: any) => {
      console.log('📦 تحديث المخزون الفوري:', inventoryUpdate);
      // يمكن إضافة منطق تحديث محلي هنا إذا لزم الأمر
    };

    const handleMenuItemUpdate = (update: any) => {
      console.log('🍽️ تحديث عنصر القائمة الفوري:', update);
      // يمكن إضافة منطق تحديث محلي هنا إذا لزم الأمر
    };

    const handleStockAlert = (alert: any) => {
      console.log('⚠️ تنبيه المخزون:', alert);
      // يمكن إضافة تنبيهات المخزون المنخفض هنا
    };

    // إضافة event listeners
    socket.on('inventory-updated', handleInventoryUpdate);
    socket.on('menu-item-updated', handleMenuItemUpdate);
    socket.on('menu-item-added', handleMenuItemUpdate);
    socket.on('menu-item-removed', handleMenuItemUpdate);
    socket.on('low-stock-alert', handleStockAlert);
    socket.on('out-of-stock-alert', handleStockAlert);

    // تنظيف event listeners
    return () => {
      socket.off('inventory-updated', handleInventoryUpdate);
      socket.off('menu-item-updated', handleMenuItemUpdate);
      socket.off('menu-item-added', handleMenuItemUpdate);
      socket.off('menu-item-removed', handleMenuItemUpdate);
      socket.off('low-stock-alert', handleStockAlert);
      socket.off('out-of-stock-alert', handleStockAlert);
    };
  }, []);
  const handleStockChange = async (itemId: string, change: number) => {
    console.log('🚀 handleStockChange called with:', { itemId, change, onUpdateStock: !!onUpdateStock });
    
    const item = menuItems.find(i => i._id === itemId);
    if (!item) {
      console.error('❌ Item not found:', itemId);
      return;
    }
    
    if (!onUpdateStock) {
      console.error('❌ onUpdateStock function not provided');
      return;
    }
    
    const currentStock = typeof item.stock === 'number' 
      ? item.stock 
      : item.stock?.quantity || 0;
    
    const newStock = Math.max(0, currentStock + change);
    
    console.log(`🔄 تحديث المخزون لـ ${item.name}: ${currentStock} → ${newStock}`);
    
    try {
      await onUpdateStock(itemId, newStock);
      console.log(`✅ تم تحديث المخزون بنجاح`);
    } catch (error) {
      console.error('❌ خطأ في تحديث المخزون:', error);
    }
  };

  const handleToggleAvailability = async (itemId: string) => {
    console.log('🚀 handleToggleAvailability called with:', { itemId, onToggleAvailability: !!onToggleAvailability });
    
    const item = menuItems.find(i => i._id === itemId);
    if (!item) {
      console.error('❌ Item not found:', itemId);
      return;
    }
    
    if (!onToggleAvailability) {
      console.error('❌ onToggleAvailability function not provided');
      return;
    }
    
    const currentAvailability = item.isAvailable !== undefined ? item.isAvailable : item.available;
    const newAvailability = !currentAvailability;
    
    console.log(`🔄 تحديث الإتاحة لـ ${item.name}: ${currentAvailability ? 'متاح' : 'غير متاح'} → ${newAvailability ? 'متاح' : 'غير متاح'}`);
    
    try {
      await onToggleAvailability(itemId, newAvailability);
      console.log(`✅ تم تحديث الإتاحة بنجاح`);
    } catch (error) {
      console.error('❌ خطأ في تحديث الإتاحة:', error);
    }
  };

  const renderStockControls = (item: InventoryItem) => {
    console.log('🎨 renderStockControls called for item:', item.name, 'hasOnUpdateStock:', !!onUpdateStock);
    
    if (!onUpdateStock) {
      console.log('❌ onUpdateStock not available, returning null');
      return null;
    }

    const stockQuantity = typeof item.stock === 'number' 
      ? item.stock 
      : item.stock?.quantity || 0;
    
    const isUpdating = updatingItems.has(item._id);
    
    console.log('📦 Stock quantity for', item.name, ':', stockQuantity, 'isUpdating:', isUpdating);

    return (
      <>
        <div className="stock-controls">
          <div className="stock-controls-label">
            <i className="fas fa-sliders-h"></i>
            <span>تحكم في المخزون</span>
          </div>
          <div className="stock-buttons">
            <button
              className="stock-btn decrease"
              onClick={(e) => {
                e.preventDefault();
                e.stopPropagation();
                console.log('🖱️ Button clicked: -1 for item', item._id);
                handleStockChange(item._id, -1);
              }}
              title="تقليل قطعة واحدة"
              disabled={stockQuantity <= 0 || isUpdating}
            >
              <i className={isUpdating ? "fas fa-spinner fa-spin" : "fas fa-minus"}></i>
              <span>-1</span>
            </button>
            <button
              className="stock-btn decrease-five"
              onClick={(e) => {
                e.preventDefault();
                e.stopPropagation();
                console.log('🖱️ Button clicked: -5 for item', item._id);
                handleStockChange(item._id, -5);
              }}
              title="تقليل 5 قطع"
              disabled={stockQuantity < 5 || isUpdating}
            >
              <i className={isUpdating ? "fas fa-spinner fa-spin" : "fas fa-minus-circle"}></i>
              <span>-5</span>
            </button>
            <button
              className="stock-btn increase-five"
              onClick={(e) => {
                e.preventDefault();
                e.stopPropagation();
                console.log('🖱️ Button clicked: +5 for item', item._id);
                handleStockChange(item._id, 5);
              }}
              title="إضافة 5 قطع"
              disabled={isUpdating}
            >
              <i className={isUpdating ? "fas fa-spinner fa-spin" : "fas fa-plus-circle"}></i>
              <span>+5</span>
            </button>
            <button
              className="stock-btn increase"
              onClick={(e) => {
                e.preventDefault();
                e.stopPropagation();
                console.log('🖱️ Button clicked: +1 for item', item._id);
                handleStockChange(item._id, 1);
              }}
              title="إضافة قطعة واحدة"
              disabled={isUpdating}
            >
              <i className={isUpdating ? "fas fa-spinner fa-spin" : "fas fa-plus"}></i>
              <span>+1</span>
            </button>
          </div>
        </div>

        {/* Availability Toggle Button */}
        {onToggleAvailability && (
          <div className="availability-controls">
            <div className="availability-controls-label">
              <i className="fas fa-toggle-on"></i>
              <span>حالة التوفر</span>
            </div>
            <button
              className={`availability-btn ${(item.isAvailable !== undefined ? item.isAvailable : item.available) ? 'available' : 'unavailable'}`}
              onClick={(e) => {
                e.preventDefault();
                e.stopPropagation();
                console.log('🖱️ Availability toggle clicked for item', item._id);
                handleToggleAvailability(item._id);
              }}
              title={`تغيير إلى ${(item.isAvailable !== undefined ? item.isAvailable : item.available) ? 'غير متاح' : 'متاح'}`}
              disabled={isUpdating}
            >
              <i className={isUpdating ? 'fas fa-spinner fa-spin' : `fas ${(item.isAvailable !== undefined ? item.isAvailable : item.available) ? 'fa-toggle-on' : 'fa-toggle-off'}`}></i>
              <span>{(item.isAvailable !== undefined ? item.isAvailable : item.available) ? 'متاح' : 'غير متاح'}</span>
            </button>
          </div>
        )}
      </>
    );
  };

  const renderInventoryItem = (item: InventoryItem) => {
    const stockQuantity = typeof item.stock === 'number' 
      ? item.stock 
      : item.stock?.quantity || 0;
    const isLowStock = stockQuantity < 10;
    const isAvailable = item.isAvailable !== undefined ? item.isAvailable : item.available;
    const isUpdating = updatingItems.has(item._id);

    return (
      <div
        key={item._id}
        className={`card inventory-card ${!isAvailable ? 'unavailable' : ''} ${isLowStock ? 'low-stock' : ''} ${isUpdating ? 'updating' : ''}`}
      >
        {/* Status indicator */}
        <div className={`badge status-badge ${isAvailable ? 'available' : 'unavailable'}`}>
          <i className={`fas ${isAvailable ? 'fa-check-circle' : 'fa-times-circle'} me-1`}></i>
          {isAvailable ? 'متوفر' : 'غير متوفر'}
        </div>

        {/* Stock indicator */}
        <div className={`stock-indicator ${isLowStock ? 'low' : ''}`}>
          <i className={`fas fa-cubes ${isLowStock ? 'text-warning' : 'text-info'}`}></i>
          {stockQuantity}
        </div>

        <div className="card-body inventory-card-body">
          {/* Header with item name and status */}
          <div className="inventory-card-header">
            <div>
              <h5 className="inventory-card-title">{item.name}</h5>
              {item.description && (
                <p className="inventory-card-description">
                  <i className="fas fa-info-circle"></i>
                  {item.description}
                </p>
              )}
            </div>
          </div>

          {/* Details section */}
          <div className="inventory-details">
            <div className="detail-item">
              <div>
                <i className="fas fa-tag price-icon"></i>
                <div className="detail-value">{item.price} ج.م</div>
                <div className="detail-label">السعر</div>
              </div>
            </div>
            <div className="detail-item">
              <div>
                <i className={`fas fa-cubes stock-icon ${stockQuantity < 5 ? 'critical' : isLowStock ? 'low' : ''}`}></i>
                <div className="detail-value">{stockQuantity}</div>
                <div className="detail-label">المخزون</div>
              </div>
            </div>
          </div>

          {/* Controls */}
          <div>
            {renderStockControls(item)}
          </div>
        </div>
      </div>
    );
  };

  return (
    <div className="container-fluid inventory-container">
      {/* Header */}
      <div className="row mb-4">
        <div className="col-12">
          <div className="inventory-header">
            <h1 className="display-6 mb-2">
              <i className="fas fa-boxes"></i>
              إدارة المخزون
            </h1>
            <p className="mb-0">مراقبة وإدارة المخزون بكفاءة</p>
          </div>
        </div>
      </div>

      {/* Content */}
      <div className="row">
        <div className="col-12">
          {loadingStates.inventory ? (
            <div className="loading-container">
              <div className="loading-spinner"></div>
              <h5 className="loading-text">جاري تحميل بيانات المخزون...</h5>
            </div>
          ) : menuItems.length === 0 ? (
            <div className="empty-state">
              <i className="fas fa-boxes empty-icon"></i>
              <h3>لا توجد عناصر في المخزون</h3>
              <p>يرجى إضافة عناصر إلى القائمة أولاً</p>
            </div>
          ) : (
            <ResponsiveGrid
              cols={{ xs: 1, sm: 1, md: 2, lg: 3, xl: 4, xxl: 5 }}
              gap={3}
            >
              {menuItems.map(renderInventoryItem)}
            </ResponsiveGrid>
          )}
        </div>
      </div>
    </div>
  );
};

export default InventoryManagerScreenBootstrap;

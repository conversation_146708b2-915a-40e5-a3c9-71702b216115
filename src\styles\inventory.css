/* ملف تنسيقات شاشة المخزون وكارت المخزون */

/* تنسيقات الحاوي الرئيسي */
.inventory-container {
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
  min-height: 100vh;
  padding: 2rem 0;
}

/* تنسيقات الهيدر */
.inventory-header {
  background: linear-gradient(135deg, #6f42c1 0%, #5a32a3 100%);
  color: white;
  border-radius: 15px;
  padding: 2rem;
  margin-bottom: 2rem;
  box-shadow: 0 8px 25px rgba(111, 66, 193, 0.3);
  position: relative;
  overflow: hidden;
}

.inventory-header::before {
  content: '';
  position: absolute;
  top: -50%;
  right: -50%;
  width: 100%;
  height: 100%;
  background: radial-gradient(circle, rgba(255,255,255,0.1) 0%, transparent 70%);
  transform: rotate(45deg);
}

.inventory-header h1 {
  font-size: 2.5rem;
  font-weight: 700;
  margin-bottom: 0.5rem;
  text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
}

.inventory-header p {
  font-size: 1.1rem;
  opacity: 0.9;
  margin-bottom: 0;
}

.inventory-header .fas {
  margin-left: 1rem;
  font-size: 2rem;
}

/* تنسيقات كارت المخزون */
.inventory-card {
  background: white;
  border-radius: 20px;
  box-shadow: 0 10px 30px rgba(0,0,0,0.1);
  transition: all 0.3s ease;
  overflow: hidden;
  position: relative;
  border: none;
  margin-bottom: 1.5rem;
}

.inventory-card:hover {
  transform: translateY(-8px);
  box-shadow: 0 20px 40px rgba(0,0,0,0.15);
}

.inventory-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: linear-gradient(90deg, #28a745, #20c997, #17a2b8);
}

/* حالة عدم التوفر */
.inventory-card.unavailable {
  opacity: 0.7;
  background: #f8f9fa;
}

.inventory-card.unavailable::before {
  background: linear-gradient(90deg, #dc3545, #e74c3c);
}

/* حالة المخزون المنخفض */
.inventory-card.low-stock::before {
  background: linear-gradient(90deg, #ffc107, #fd7e14);
}

/* مؤشر الحالة */
.status-badge {
  position: absolute;
  top: 15px;
  right: 15px;
  padding: 0.4rem 0.8rem;
  border-radius: 20px;
  font-size: 0.8rem;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  z-index: 2;
}

.status-badge.available {
  background: linear-gradient(135deg, #28a745, #20c997);
  color: white;
  box-shadow: 0 4px 15px rgba(40, 167, 69, 0.3);
}

.status-badge.unavailable {
  background: linear-gradient(135deg, #dc3545, #e74c3c);
  color: white;
  box-shadow: 0 4px 15px rgba(220, 53, 69, 0.3);
}

/* مؤشر المخزون */
.stock-indicator {
  position: absolute;
  top: 15px;
  left: 15px;
  background: rgba(255, 255, 255, 0.95);
  padding: 0.4rem 0.8rem;
  border-radius: 15px;
  font-size: 0.9rem;
  font-weight: 600;
  color: #495057;
  box-shadow: 0 4px 15px rgba(0,0,0,0.1);
  z-index: 2;
}

.stock-indicator.low {
  background: linear-gradient(135deg, #fff3cd, #ffeaa7);
  color: #856404;
}

.stock-indicator .fas {
  margin-left: 0.5rem;
  color: #17a2b8;
}

/* محتوى الكارت */
.inventory-card-body {
  padding: 2rem;
  padding-top: 3.5rem;
}

.inventory-card-header {
  margin-bottom: 1.5rem;
}

.inventory-card-title {
  font-size: 1.4rem;
  font-weight: 700;
  color: #2c3e50;
  margin-bottom: 0.5rem;
  line-height: 1.3;
}

.inventory-card-description {
  color: #6c757d;
  font-size: 0.95rem;
  line-height: 1.5;
  margin-bottom: 0;
}

.inventory-card-description .fas {
  margin-left: 0.5rem;
  color: #17a2b8;
}

/* تفاصيل السعر والمخزون */
.inventory-details {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 1rem;
  margin-bottom: 1.5rem;
}

.detail-item {
  background: #f8f9fa;
  padding: 1rem;
  border-radius: 12px;
  text-align: center;
  transition: all 0.3s ease;
}

.detail-item:hover {
  background: #e9ecef;
  transform: scale(1.02);
}

.detail-item .fas {
  font-size: 1.2rem;
  margin-bottom: 0.5rem;
  display: block;
}

.detail-item .price-icon {
  color: #28a745;
}

.detail-item .stock-icon {
  color: #17a2b8;
}

.detail-item .stock-icon.low {
  color: #ffc107;
}

.detail-item .stock-icon.critical {
  color: #dc3545;
}

.detail-value {
  font-size: 1.3rem;
  font-weight: 700;
  color: #2c3e50;
  margin-bottom: 0.2rem;
}

.detail-label {
  font-size: 0.85rem;
  color: #6c757d;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  font-weight: 600;
}

/* أزرار التحكم في المخزون */
.stock-controls {
  background: #f8f9fa;
  border-radius: 15px;
  padding: 1.5rem;
  margin-bottom: 1rem;
}

.stock-controls-label {
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 1rem;
  font-weight: 600;
  color: #495057;
  font-size: 0.9rem;
  white-space: nowrap;
}

.stock-controls-label .fas {
  margin-left: 0.5rem;
  color: #6f42c1;
}

.stock-buttons {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 0.5rem;
}

.stock-btn {
  background: white;
  border: 2px solid #e9ecef;
  border-radius: 10px;
  padding: 0.8rem 0.5rem;
  font-size: 0.85rem;
  font-weight: 600;
  transition: all 0.3s ease;
  cursor: pointer;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 0.3rem;
}

.stock-btn:hover:not(:disabled) {
  transform: translateY(-2px);
  box-shadow: 0 8px 20px rgba(0,0,0,0.1);
}

.stock-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.stock-btn.decrease,
.stock-btn.decrease-five {
  border-color: #dc3545;
  color: #dc3545;
}

.stock-btn.decrease:hover:not(:disabled),
.stock-btn.decrease-five:hover:not(:disabled) {
  background: #dc3545;
  color: white;
}

.stock-btn.increase,
.stock-btn.increase-five {
  border-color: #28a745;
  color: #28a745;
}

.stock-btn.increase:hover:not(:disabled),
.stock-btn.increase-five:hover:not(:disabled) {
  background: #28a745;
  color: white;
}

/* أزرار التحكم في التوفر */
.availability-controls {
  background: #f8f9fa;
  border-radius: 15px;
  padding: 1.5rem;
}

.availability-controls-label {
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 1rem;
  font-weight: 600;
  color: #495057;
  font-size: 0.9rem;
  white-space: nowrap;
}

.availability-controls-label .fas {
  margin-left: 0.5rem;
  color: #6f42c1;
}

.availability-btn {
  width: 100%;
  padding: 1rem;
  border: none;
  border-radius: 12px;
  font-size: 1rem;
  font-weight: 600;
  transition: all 0.3s ease;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
}

.availability-btn.available {
  background: linear-gradient(135deg, #28a745, #20c997);
  color: white;
  box-shadow: 0 6px 20px rgba(40, 167, 69, 0.3);
}

.availability-btn.unavailable {
  background: linear-gradient(135deg, #6c757d, #495057);
  color: white;
  box-shadow: 0 6px 20px rgba(108, 117, 125, 0.3);
}

.availability-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(0,0,0,0.2);
}

/* حالة التحميل */
.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 4rem 2rem;
  text-align: center;
}

.loading-spinner {
  width: 4rem;
  height: 4rem;
  border: 4px solid #e9ecef;
  border-top: 4px solid #6f42c1;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 1.5rem;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.loading-text {
  color: #6c757d;
  font-size: 1.1rem;
  font-weight: 500;
}

/* حالة فارغة */
.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 4rem 2rem;
  text-align: center;
}

.empty-icon {
  font-size: 4rem;
  color: #dee2e6;
  margin-bottom: 1.5rem;
}

.empty-state h3 {
  color: #6c757d;
  font-size: 1.5rem;
  margin-bottom: 1rem;
}

.empty-state p {
  color: #adb5bd;
  font-size: 1rem;
  max-width: 400px;
}

/* تجاوبية الشاشات */
@media (max-width: 768px) {
  .inventory-header {
    padding: 1.5rem;
    margin-bottom: 1.5rem;
  }
  
  .inventory-header h1 {
    font-size: 2rem;
  }
  
  .inventory-card-body {
    padding: 1.5rem;
    padding-top: 3rem;
  }
  
  .inventory-details {
    grid-template-columns: 1fr;
    gap: 0.8rem;
  }
  
  .stock-buttons {
    grid-template-columns: repeat(2, 1fr);
    gap: 0.8rem;
  }
  
  .stock-btn {
    padding: 1rem;
    font-size: 0.9rem;
  }
}

@media (max-width: 576px) {
  .inventory-container {
    padding: 1rem 0;
  }
  
  .inventory-header {
    padding: 1rem;
    margin-bottom: 1rem;
  }
  
  .inventory-header h1 {
    font-size: 1.8rem;
  }
  
  .inventory-card {
    margin-bottom: 1rem;
  }
  
  .inventory-card-body {
    padding: 1rem;
    padding-top: 2.5rem;
  }
  
  .status-badge,
  .stock-indicator {
    top: 10px;
    padding: 0.3rem 0.6rem;
    font-size: 0.75rem;
  }
  
  .status-badge {
    right: 10px;
  }
  
  .stock-indicator {
    left: 10px;
  }
}

/* تنسيقات حالة التحديث */
.inventory-card.updating {
  opacity: 0.8;
  position: relative;
  overflow: hidden;
}

.inventory-card.updating::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(111, 66, 193, 0.1), transparent);
  animation: shimmer 1.5s infinite;
  z-index: 1;
}

@keyframes shimmer {
  0% {
    left: -100%;
  }
  100% {
    left: 100%;
  }
}

.inventory-card.updating .stock-btn:disabled,
.inventory-card.updating .availability-btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.inventory-card.updating .fa-spinner {
  color: #6f42c1;
}
/* Tables Screen Variables - متغيرات شاشة الطاولات */
:root {
  /* ألوان شاشة الطاولات */
  --tables-primary-color: #2c3e50;
  --tables-secondary-color: #3498db;
  --tables-bg-primary: #ffffff;
  --tables-bg-secondary: #f8f9fa;
  --tables-text-primary: #000000;
  --tables-text-secondary: #6c757d;
  --tables-border-color: #dee2e6;
  --tables-border-light: #f1f3f4;
  
  /* ألوان الحالة لشاشة الطاولات */
  --tables-success-color: #27ae60;
  --tables-success-light: rgba(39, 174, 96, 0.1);
  --tables-warning-color: #f39c12;
  --tables-warning-light: rgba(243, 156, 18, 0.1);
  --tables-error-color: #e74c3c;
  --tables-error-light: rgba(231, 76, 60, 0.1);
  --tables-info-color: #17a2b8;
  --tables-info-light: rgba(23, 162, 184, 0.1);
  
  /* متغيرات الخط لشاشة الطاولات */
  --tables-font-size-xs: 12px;
  --tables-font-size-sm: 14px;
  --tables-font-size-md: 16px;
  --tables-font-size-lg: 18px;
  --tables-font-size-xl: 20px;
  --tables-font-size-xxl: 24px;
  
  /* متغيرات المسافة لشاشة الطاولات */
  --tables-spacing-xs: 0.25rem;
  --tables-spacing-sm: 0.5rem;
  --tables-spacing-md: 1rem;
  --tables-spacing-lg: 1.5rem;
  --tables-spacing-xl: 2rem;
  --tables-spacing-xxl: 3rem;
  
  /* متغيرات الحدود لشاشة الطاولات */
  --tables-border-radius: 8px;
  --tables-border-radius-sm: 4px;
  --tables-border-radius-lg: 12px;
  --tables-border-radius-xl: 16px;
  
  /* متغيرات الظلال لشاشة الطاولات */
  --tables-shadow-sm: 0 1px 3px rgba(0, 0, 0, 0.05);
  --tables-shadow-md: 0 2px 8px rgba(0, 0, 0, 0.1);
  --tables-shadow-lg: 0 4px 16px rgba(0, 0, 0, 0.15);
  
  /* متغيرات الانتقال لشاشة الطاولات */
  --tables-transition-fast: 0.2s ease;
  --tables-transition-medium: 0.3s ease;
  --tables-transition-slow: 0.5s ease;
  
  /* متغيرات خاصة بشاشة الطاولات */
  --tables-card-min-width: 300px;
  --tables-grid-gap: 1.5rem;
  --tables-primary-hover: #34495e;
  --tables-primary-light: rgba(44, 62, 80, 0.1);
  --tables-active-border: var(--tables-success-color);
  --tables-closed-border: var(--tables-error-color);
}




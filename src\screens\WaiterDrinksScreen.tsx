import React from 'react';
// استيراد ملف CSS الخاص بشاشة المشروبات
import '../styles/waiter/WaiterDrinksScreen.css';
import { smartSearchAndSort } from '../utils/arabicSearch';

interface MenuItem {
  _id: string;
  name: string;
  price: number;
  description?: string;
  categories?: string[];
  categoryDetails?: Category[];
  available: boolean;
  notes?: string;
}

interface Category {
  _id: string;
  name: string;
  color: string;
  icon?: string;
}

interface WaiterDrinksScreenProps {
  menuItems: MenuItem[];
  categories: Category[];
  selectedCategory: string;
  searchTerm: string;
  loadingMenuItems: boolean;
  onCategoryChange: (categoryId: string) => void;
  onSearchChange: (term: string) => void;
  onAddToCart: (item: MenuItem) => void;
  onOpenNotesModal: (item: MenuItem) => void;
  onRefreshMenuItems: () => void;
  onRefreshCategories: () => void;
}

const getCategoryIcon = (categoryName: string): string => {
  const iconMap: { [key: string]: string } = {
    'قهوة': 'fa-coffee',
    'شاي': 'fa-leaf',
    'عصائر': 'fa-glass-whiskey',
    'مشروبات باردة': 'fa-snowflake',
    'مشروبات ساخنة': 'fa-fire',
    'حلويات': 'fa-cookie-bite',
    'مقبلات': 'fa-utensils',
    'وجبات': 'fa-hamburger'
  };
  return iconMap[categoryName] || 'fa-coffee';
};

export default function WaiterDrinksScreen({
  menuItems,
  categories,
  selectedCategory,
  searchTerm,
  loadingMenuItems,
  onCategoryChange,
  onSearchChange,
  onAddToCart,
  onOpenNotesModal,
  onRefreshMenuItems,
  onRefreshCategories
}: WaiterDrinksScreenProps) {
  // فلترة العناصر حسب الفئة أولاً
  let filteredItems = menuItems.filter(item => {
    // إذا كان "جميع المشروبات" محدد، أظهر كل المنتجات
    if (selectedCategory === 'all') {
      return true;
    }
    
    // التحقق من وجود الفئة في categories (مصفوفة الـ IDs)
    const matchesCategory = item.categories && 
      Array.isArray(item.categories) && 
      item.categories.includes(selectedCategory);
    
    // التحقق من وجود الفئة في categoryDetails (مصفوفة الكائنات الكاملة) كبديل
    const matchesCategoryDetails = item.categoryDetails && 
      Array.isArray(item.categoryDetails) && 
      item.categoryDetails.some(cat => cat._id === selectedCategory);
    
    return matchesCategory || matchesCategoryDetails;
  });

  // تطبيق البحث الذكي مع الترتيب الذكي
  if (searchTerm && searchTerm.trim() !== '') {
    filteredItems = smartSearchAndSort(
      filteredItems,
      searchTerm,
      (item) => item.name || '',
      (item) => item.description || ''
    );
  }

  return (
    <div className="content-container waiter-drinks-screen">
      <div className="screen-header">
        <div className="waiter-header-flex">
          <div>
            <h1 className="screen-title">
              <i className="fas fa-coffee"></i>
              قائمة المشروبات
            </h1>
            <p className="screen-subtitle">اختر المشروبات المطلوبة وأضفها إلى السلة</p>
          </div>
          <div className="flex-gap-8">
            <button
              className={`btn-refresh ${loadingMenuItems ? 'loading' : ''}`}
              title="تحديث فوري من الخادم (بدون cache)"
              disabled={loadingMenuItems}
              onClick={() => {
                // تحديث فوري للبيانات من الخادم (بدون cache)
                onRefreshMenuItems();
                onRefreshCategories();
              }}
            >
              <i className={`fas ${loadingMenuItems ? 'fa-spinner fa-spin' : 'fa-sync-alt'}`}></i>
              <span className="btn-text">{loadingMenuItems ? 'جاري التحديث...' : 'تحديث'}</span>
            </button>
          </div>
        </div>
      </div>

      <div className="drinks-filters">
        <div className="search-section">
          <div className="filter-header">
            <h3 className="filter-title">
              <i className="fas fa-filter"></i>
              البحث والتصفية
            </h3>
            <div className="results-counter">
              <i className="fas fa-list-ul"></i>
              <span>{filteredItems.length} من {menuItems.length} مشروب</span>
            </div>
          </div>
          <div className="search-container">
            <i className="fas fa-search search-icon"></i>
            <input
              type="text"
              placeholder="ابحث عن المشروب المطلوب..."
              value={searchTerm}
              onChange={(e) => onSearchChange(e.target.value)}
              className="search-input"
            />
            {searchTerm && (
              <button
                className="clear-search"
                onClick={() => onSearchChange('')}
                title="مسح البحث"
              >
                <i className="fas fa-times"></i>
              </button>
            )}
          </div>
        </div>

        <div className="categories-section">
          <div className="categories-header">
            <h4 className="categories-title">
              <i className="fas fa-tags"></i>
              الفئات
            </h4>
          </div>
          <div className="category-filter">
            <button
              className={`category-btn ${selectedCategory === 'all' ? 'active' : ''}`}
              onClick={() => onCategoryChange('all')}
            >
              <i className="fas fa-th"></i>
              جميع المشروبات
            </button>
            {categories.map(category => (
              <button
                key={category._id}
                className={`category-btn ${selectedCategory === category._id ? 'active' : ''}`}
                onClick={() => onCategoryChange(category._id)}
                data-border-color={category.color}
              >
                <i className={`fas ${getCategoryIcon(category.name)}`}></i>
                {category.name}
              </button>
            ))}
          </div>
        </div>
      </div>

      <div className="menu-grid">
        {loadingMenuItems ? (
          <div className="loading-state">
            <div className="loading-spinner"></div>
            <h3>جاري تحميل المشروبات...</h3>
            <p>يرجى الانتظار</p>
          </div>
        ) : filteredItems.length === 0 && menuItems.length === 0 ? (
          <div className="empty-state">
            <i className="fas fa-coffee empty-icon"></i>
            <h3>لا توجد مشروبات</h3>
            <p>لم يتم تحميل أي مشروبات بعد</p>
            <button 
              className="retry-btn"
              onClick={() => onRefreshMenuItems()}
            >
              <i className="fas fa-refresh"></i> إعادة المحاولة
            </button>
          </div>
        ) : filteredItems.map(item => (
          <div key={item._id} className="menu-item-card">
            <div className="menu-item-header">
              <h3 className="menu-item-name">{item.name}</h3>
              <span className="menu-item-price">{item.price} جنيه</span>
            </div>
            
            {item.description && (
              <p className="menu-item-description">{item.description}</p>
            )}
            
            <div className="menu-item-categories">
              {item.categoryDetails?.map(cat => (
                <span 
                  key={cat._id} 
                  className="category-tag"
                  data-bg-color={cat.color}
                >
                  <i className={`fas ${getCategoryIcon(cat.name)}`}></i>
                  {cat.name}
                </span>
              ))}
            </div>
            
            <div className="item-actions">
              <button
                className="add-to-cart-btn"
                onClick={() => onAddToCart(item)}
              >
                <i className="fas fa-plus"></i>
                إضافة للسلة
              </button>

              <button
                className="add-with-notes-btn"
                onClick={() => onOpenNotesModal(item)}
                title="إضافة مع ملاحظات"
              >
                <i className="fas fa-sticky-note"></i>
                ملاحظات
              </button>
            </div>
          </div>
        ))}
      </div>

      {filteredItems.length === 0 && menuItems.length > 0 && (
        <div className="empty-state">
          <i className="fas fa-coffee"></i>
          <h3>لا توجد مشروبات متاحة</h3>
          <p>لا توجد مشروبات في هذه الفئة حالياً</p>
        </div>
      )}
    </div>
  );
}

console.log('اختبار Node.js');

const mongoose = require('mongoose');

mongoose.connect('mongodb+srv://besomustafa:<EMAIL>/deshacoffee?retryWrites=true&w=majority&appName=MyCoffeChop')
  .then(() => {
    console.log('تم الاتصال بقاعدة البيانات');
    
    return mongoose.connection.db.collection('orders').countDocuments();
  })
  .then(count => {
    console.log('عدد الطلبات:', count);
    
    return mongoose.connection.db.collection('orders').find({}).limit(3).toArray();
  })
  .then(orders => {
    console.log('الطلبات:');
    orders.forEach((order, i) => {
      console.log((i+1) + ':', order.status, '|', order.total, '|', order.discount);
    });
    
    return mongoose.disconnect();
  })
  .then(() => {
    console.log('تم قطع الاتصال');
  })
  .catch(error => {
    console.error('خطأ:', error.message);
  });

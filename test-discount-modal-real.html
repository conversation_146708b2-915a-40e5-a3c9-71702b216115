<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار مودال تفاصيل الخصم - البنية الفعلية</title>
    
    <!-- استيراد ملفات CSS -->
    <link rel="stylesheet" href="src/ManagerDashboard.css">
    <link rel="stylesheet" href="src/ManagerDashboard-fix.css">
    <link rel="stylesheet" href="src/OrderDetailsModal.css">
    <link rel="stylesheet" href="src/DiscountRequestsScreen.css">
    <link rel="stylesheet" href="src/popular-products.css">
    <link rel="stylesheet" href="src/ManagerDashboard-additional.css">
    <link rel="stylesheet" href="src/DiscountDetailsModal.css">
    
    <!-- Font Awesome for icons -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    
    <style>
        body {
            margin: 0;
            padding: 20px;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: #f5f5f5;
            direction: rtl;
        }
        
        .test-container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            padding: 2rem;
            border-radius: 12px;
            box-shadow: 0 4px 12px rgba(0,0,0,0.1);
        }
        
        .test-header {
            text-align: center;
            margin-bottom: 2rem;
            color: #2c3e50;
        }
        
        .test-btn {
            background: #8e44ad;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 8px;
            cursor: pointer;
            font-size: 16px;
            font-weight: 600;
            transition: all 0.3s ease;
            display: block;
            margin: 20px auto;
        }
        
        .test-btn:hover {
            background: #9b59b6;
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(142, 68, 173, 0.3);
        }
        
        .features-list {
            background: #f8f9fa;
            padding: 1.5rem;
            border-radius: 8px;
            margin: 20px 0;
            border-left: 4px solid #8e44ad;
        }
        
        .features-list h3 {
            color: #8e44ad;
            margin-top: 0;
        }
        
        .features-list ul {
            margin: 0;
            padding-right: 20px;
        }
        
        .features-list li {
            margin: 8px 0;
            color: #495057;
        }
        
        .note {
            background: #d4edda;
            border: 1px solid #c3e6cb;
            color: #155724;
            padding: 12px;
            border-radius: 6px;
            margin: 15px 0;
            font-size: 14px;
        }

        /* تنسيقات المودال الأساسية */
        .modal-overlay {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0, 0, 0, 0.6);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 999999;
            padding: 1rem;
            backdrop-filter: blur(5px);
        }

        .modal-content {
            background: white;
            border-radius: 12px;
            box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
            position: relative;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <div class="test-header">
            <h1>🧪 اختبار مودال تفاصيل الخصم - البنية الفعلية</h1>
            <p>اختبار مطابق لبنية المودال الحقيقية في النظام</p>
        </div>
        
        <div class="features-list">
            <h3>✅ التحسينات المطبقة:</h3>
            <ul>
                <li>إنشاء ملف CSS جديد مطابق للبنية الفعلية</li>
                <li>تخطيط أفقي محسن (معلومات أساسية + تفاصيل مالية)</li>
                <li>قسم أصناف الطلب بعرض كامل</li>
                <li>تصميم مالي تفاعلي مع أيقونات</li>
                <li>نسبة الخصم مع تدرج لوني</li>
                <li>تصميم متجاوب للشاشات المختلفة</li>
                <li>استخدام البنية الفعلية من ManagerDashboard.tsx</li>
            </ul>
        </div>
        
        <div class="note">
            <strong>ملاحظة:</strong> هذا الاختبار يعكس البنية الفعلية للمودال في النظام مع جميع التحسينات المطلوبة.
        </div>
        
        <button class="test-btn" onclick="openDiscountModal()">
            🔍 اختبار مودال تفاصيل الخصم
        </button>
    </div>

    <!-- Modal Overlay - البنية الفعلية -->
    <div class="modal-overlay" id="discountModalOverlay" style="display: none;">
        <div class="modal-content discount-details-modal" onclick="event.stopPropagation()">
            <div class="modal-header">
                <h3>
                    <i class="fas fa-percentage"></i>
                    تفاصيل طلب الخصم
                </h3>
                <button class="close-btn" onclick="closeDiscountModal()" type="button">
                    ×
                </button>
            </div>

            <div class="modal-body">
                <div class="discount-request-details">
                    <!-- المعلومات الأساسية -->
                    <div class="basic-info-section">
                        <h4 class="section-title">
                            <i class="fas fa-info-circle"></i>
                            معلومات عامة
                        </h4>
                        <div class="info-grid">
                            <div class="info-item">
                                <span class="label">رقم الطلب:</span>
                                <span class="value">#ORD-2024-001</span>
                            </div>
                            <div class="info-item">
                                <span class="label">النادل:</span>
                                <span class="value">سارة أحمد</span>
                            </div>
                            <div class="info-item">
                                <span class="label">سبب الخصم:</span>
                                <span class="value">خدمة متأخرة</span>
                            </div>
                            <div class="info-item">
                                <span class="label">حالة الطلب:</span>
                                <span class="value status approved">مقبول</span>
                            </div>
                            <div class="info-item">
                                <span class="label">تاريخ الطلب:</span>
                                <span class="value">2024-01-15 14:30</span>
                            </div>
                        </div>
                    </div>

                    <!-- التفاصيل المالية -->
                    <div class="discount-financial-section">
                        <h4 class="section-title">
                            <i class="fas fa-calculator"></i>
                            التفاصيل المالية
                        </h4>
                        <div class="financial-breakdown">
                            <div class="financial-details">
                                <div class="financial-row original-amount">
                                    <div class="row-icon">
                                        <i class="fas fa-receipt"></i>
                                    </div>
                                    <div class="row-content">
                                        <span class="row-label">قيمة الطلب قبل الخصم المطبق</span>
                                        <span class="row-description">المبلغ الأصلي قبل تطبيق الخصم</span>
                                    </div>
                                    <div class="row-value">
                                        <span class="amount original">97.75 ج.م</span>
                                    </div>
                                </div>

                                <div class="financial-row discount-amount">
                                    <div class="row-icon">
                                        <i class="fas fa-minus-circle"></i>
                                    </div>
                                    <div class="row-content">
                                        <span class="row-label">قيمة الخصم المطلوبة</span>
                                        <span class="row-description">تم تطبيق الخصم</span>
                                    </div>
                                    <div class="row-value">
                                        <span class="amount discount">- 14.66 ج.م</span>
                                    </div>
                                </div>

                                <div class="financial-separator">
                                    <div class="separator-line"></div>
                                    <div class="separator-icon">
                                        <i class="fas fa-equals"></i>
                                    </div>
                                    <div class="separator-line"></div>
                                </div>

                                <div class="financial-row final-amount">
                                    <div class="row-icon">
                                        <i class="fas fa-money-bill-wave"></i>
                                    </div>
                                    <div class="row-content">
                                        <span class="row-label">قيمة الطلب بعد الخصم</span>
                                        <span class="row-description">المبلغ النهائي المدفوع</span>
                                    </div>
                                    <div class="row-value">
                                        <span class="amount final">83.09 ج.م</span>
                                    </div>
                                </div>

                                <!-- نسبة الخصم -->
                                <div class="discount-percentage">
                                    <div class="percentage-info">
                                        <i class="fas fa-percentage"></i>
                                        <span>نسبة الخصم: 15.0%</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- أصناف الطلب -->
                    <div class="order-items-section">
                        <h4 class="section-title">
                            <i class="fas fa-list"></i>
                            أصناف الطلب (4)
                        </h4>
                        <div class="items-list">
                            <div class="item-row">
                                <div class="item-info">
                                    <div class="item-name">قهوة أمريكية</div>
                                    <div class="item-details">
                                        <span class="quantity">الكمية: 2</span>
                                        <span class="unit-price">سعر الوحدة: 15.00 ج.م</span>
                                    </div>
                                </div>
                                <div class="item-total">30.00 ج.م</div>
                            </div>
                            
                            <div class="item-row">
                                <div class="item-info">
                                    <div class="item-name">كابتشينو</div>
                                    <div class="item-details">
                                        <span class="quantity">الكمية: 1</span>
                                        <span class="unit-price">سعر الوحدة: 18.00 ج.م</span>
                                    </div>
                                </div>
                                <div class="item-total">18.00 ج.م</div>
                            </div>
                            
                            <div class="item-row">
                                <div class="item-info">
                                    <div class="item-name">كرواسون جبن</div>
                                    <div class="item-details">
                                        <span class="quantity">الكمية: 2</span>
                                        <span class="unit-price">سعر الوحدة: 12.00 ج.م</span>
                                    </div>
                                </div>
                                <div class="item-total">24.00 ج.م</div>
                            </div>
                            
                            <div class="item-row">
                                <div class="item-info">
                                    <div class="item-name">عصير برتقال طازج</div>
                                    <div class="item-details">
                                        <span class="quantity">الكمية: 1</span>
                                        <span class="unit-price">سعر الوحدة: 13.00 ج.م</span>
                                    </div>
                                </div>
                                <div class="item-total">13.00 ج.م</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="modal-footer">
                <button class="close-modal-btn" onclick="closeDiscountModal()">
                    <i class="fas fa-times"></i>
                    إغلاق
                </button>
            </div>
        </div>
    </div>

    <script>
        function openDiscountModal() {
            document.getElementById('discountModalOverlay').style.display = 'flex';
            document.body.style.overflow = 'hidden';
            
            // إضافة animation للمودال
            const modal = document.querySelector('.discount-details-modal');
            modal.style.transform = 'scale(0.8)';
            modal.style.opacity = '0';
            
            setTimeout(() => {
                modal.style.transition = 'all 0.3s ease';
                modal.style.transform = 'scale(1)';
                modal.style.opacity = '1';
            }, 10);
        }

        function closeDiscountModal() {
            const modal = document.querySelector('.discount-details-modal');
            modal.style.transform = 'scale(0.8)';
            modal.style.opacity = '0';
            
            setTimeout(() => {
                document.getElementById('discountModalOverlay').style.display = 'none';
                document.body.style.overflow = 'auto';
            }, 300);
        }

        // إغلاق المودال عند النقر خارجه
        document.getElementById('discountModalOverlay').addEventListener('click', function(e) {
            if (e.target === this) {
                closeDiscountModal();
            }
        });

        // إغلاق المودال بالضغط على ESC
        document.addEventListener('keydown', function(e) {
            if (e.key === 'Escape') {
                closeDiscountModal();
            }
        });

        // عرض معلومات التحميل
        window.addEventListener('load', function() {
            console.log('✅ تم تحميل مودال تفاصيل الخصم بالبنية الفعلية');
            console.log('📋 الملفات المحملة:');
            console.log('- ManagerDashboard.css');
            console.log('- ManagerDashboard-fix.css'); 
            console.log('- OrderDetailsModal.css');
            console.log('- DiscountRequestsScreen.css');
            console.log('- popular-products.css');
            console.log('- ManagerDashboard-additional.css');
            console.log('- DiscountDetailsModal.css (الجديد)');
        });
    </script>
</body>
</html>

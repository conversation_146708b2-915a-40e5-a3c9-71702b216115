import React from 'react';

interface TableCardProps {
  tableNumber: number;
  status: 'available' | 'occupied' | 'reserved';
  customerName?: string;
  orderTotal?: number;
  timeOccupied?: string;
  onClick?: () => void;
}

const TableCard: React.FC<TableCardProps> = ({
  tableNumber,
  status,
  customerName,
  orderTotal,
  timeOccupied,
  onClick
}) => {
  const getStatusInfo = () => {
    switch (status) {
      case 'available':
        return {
          icon: 'fas fa-check-circle',
          text: 'متاحة',
          bgClass: 'bg-success',
          textClass: 'text-success'
        };
      case 'occupied':
        return {
          icon: 'fas fa-user-friends',
          text: 'مشغولة',
          bgClass: 'bg-danger',
          textClass: 'text-danger'
        };
      case 'reserved':
        return {
          icon: 'fas fa-bookmark',
          text: 'محجوزة',
          bgClass: 'bg-warning',
          textClass: 'text-warning'
        };
      default:
        return {
          icon: 'fas fa-question-circle',
          text: 'غير معروف',
          bgClass: 'bg-secondary',
          textClass: 'text-secondary'
        };
    }
  };

  const statusInfo = getStatusInfo();

  return (
    <div
      className={`table-card table-card-enhanced ${status} h-100 cursor-pointer`}
      onClick={onClick}
      role="button"
      tabIndex={0}
    >
      {/* Status indicator bar */}
      <div className={`table-status-bar ${status}`}></div>

      {/* رقم الطاولة */}
      <div className="table-card-header text-center mb-3">
        <div className="table-icon-wrapper mb-2">
          <i className={`fas fa-table table-icon ${statusInfo.textClass}`}></i>
        </div>
        <h4 className="table-number mb-1">طاولة {tableNumber}</h4>

        {/* حالة الطاولة */}
        <div className="table-status-wrapper">
          <span className={`table-status-badge ${status}`}>
            <i className={`${statusInfo.icon} me-1`}></i>
            {statusInfo.text}
          </span>
        </div>
      </div>

      {/* تفاصيل إضافية للطاولات */}
      {(customerName || orderTotal || timeOccupied) && (
        <div className="table-details-section">
          {customerName && (
            <div className="table-detail-item">
              <div className="detail-icon customer">
                <i className="fas fa-user"></i>
              </div>
              <div className="detail-content">
                <span className="detail-label">العميل</span>
                <span className="detail-value">{customerName}</span>
              </div>
            </div>
          )}

          {orderTotal && (
            <div className="table-detail-item">
              <div className="detail-icon money">
                <i className="fas fa-money-bill-wave"></i>
              </div>
              <div className="detail-content">
                <span className="detail-label">المبلغ</span>
                <span className="detail-value amount">{orderTotal} ج.م</span>
              </div>
            </div>
          )}

          {timeOccupied && (
            <div className="table-detail-item">
              <div className="detail-icon time">
                <i className="fas fa-clock"></i>
              </div>
              <div className="detail-content">
                <span className="detail-label">المدة</span>
                <span className="detail-value">{timeOccupied}</span>
              </div>
            </div>
          )}
        </div>
      )}
    </div>
  );
};

interface StatusCardProps {
  title: string;
  count: number;
  icon: string;
  color: 'success' | 'danger' | 'warning' | 'info';
  onClick?: () => void;
}

const StatusCard: React.FC<StatusCardProps> = ({
  title,
  count,
  icon,
  color,
  onClick
}) => {
  const getColorClasses = () => {
    switch (color) {
      case 'success':
        return {
          bg: 'bg-success',
          text: 'text-success',
          border: 'border-success'
        };
      case 'danger':
        return {
          bg: 'bg-danger',
          text: 'text-danger',
          border: 'border-danger'
        };
      case 'warning':
        return {
          bg: 'bg-warning',
          text: 'text-warning',
          border: 'border-warning'
        };
      case 'info':
        return {
          bg: 'bg-info',
          text: 'text-info',
          border: 'border-info'
        };
      default:
        return {
          bg: 'bg-primary',
          text: 'text-primary',
          border: 'border-primary'
        };
    }
  };

  const colorClasses = getColorClasses();

  return (
    <div 
      className={`status-card h-100 cursor-pointer ${onClick ? 'clickable' : ''}`}
      onClick={onClick}
      {...(onClick && { role: 'button', tabIndex: 0 })}
    >
      <div className="text-center">
        <div className={`${colorClasses.text} mb-3`}>
          <i className={`${icon} fs-1`}></i>
        </div>
        
        <h3 className={`fw-bold mb-2 ${colorClasses.text}`}>
          {count}
        </h3>
        
        <p className="mb-0 text-muted fw-medium">
          {title}
        </p>
      </div>
    </div>
  );
};

interface ResponsiveGridProps {
  children: React.ReactNode;
  itemMinWidth?: number;
  gap?: number;
  className?: string;
}

const ResponsiveGrid: React.FC<ResponsiveGridProps> = ({
  children,
  itemMinWidth = 280,
  gap = 3,
  className = ''
}) => {
  // حساب عدد الأعمدة بناءً على حجم الشاشة
  const getColClass = () => {
    if (itemMinWidth <= 200) return 'col-6 col-sm-4 col-md-3 col-lg-2 col-xl-2';
    if (itemMinWidth <= 280) return 'col-12 col-sm-6 col-md-4 col-lg-3 col-xl-3';
    if (itemMinWidth <= 350) return 'col-12 col-sm-6 col-md-6 col-lg-4 col-xl-4';
    return 'col-12 col-md-6 col-lg-6 col-xl-4';
  };

  const colClass = getColClass();

  return (
    <div className={`row g-${gap} ${className}`}>
      {React.Children.map(children, (child, index) => (
        <div key={index} className={colClass}>
          {child}
        </div>
      ))}
    </div>
  );
};

export { TableCard, StatusCard, ResponsiveGrid };

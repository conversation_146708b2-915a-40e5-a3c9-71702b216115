/* ====================================
   CSS منفصل لشاشة الموظفين
   Employees Screen - Isolated CSS
   ==================================== */

/* استيراد المتغيرات المميزة لشاشة الموظفين */
@import '../variables/employees-variables.css';

/* تم إزالة المتغيرات المحلية - سيتم استخدام المتغيرات من ملف employees-variables.css فقط */

/* Employees Screen Container - معزول تماماً */
.employees-screen-container {
  width: 100%;
  min-height: calc(100vh - 80px);
  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
  padding: 0;
  margin: 0;
}

/* Header للموظفين */
.employees-screen-header {
  background: linear-gradient(135deg, var(--employees-primary) 0%, var(--employees-secondary) 100%);
  padding: 2.5rem;
  color: var(--employees-white);
  position: relative;
  overflow: hidden;
}

.employees-screen-header::before {
  content: '';
  position: absolute;
  top: 0;
  right: 0;
  width: 100%;
  height: 100%;
  background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><circle cx="80" cy="20" r="2" fill="white" opacity="0.1"/><circle cx="20" cy="80" r="1.5" fill="white" opacity="0.1"/><circle cx="90" cy="70" r="1" fill="white" opacity="0.1"/></svg>');
  pointer-events: none;
}

.employees-screen-title {
  font-size: 2.75rem;
  font-weight: 800;
  margin: 0 0 0.75rem 0;
  display: flex;
  align-items: center;
  gap: 1rem;
  position: relative;
  z-index: 1;
}

.employees-screen-title-icon {
  color: var(--employees-warning);
  text-shadow: 0 0 20px rgba(253, 203, 110, 0.6);
}

.employees-screen-subtitle {
  font-size: 1.3rem;
  opacity: 0.9;
  margin: 0;
  position: relative;
  z-index: 1;
}

/* Controls Section */
.employees-screen-controls {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 1.5rem;
  flex-wrap: wrap;
  gap: 1rem;
  position: relative;
  z-index: 1;
}

.employees-screen-add-btn {
  background: var(--employees-white);
  color: var(--employees-primary);
  border: none;
  padding: 1rem 2rem;
  border-radius: var(--employees-border-radius);
  font-weight: 700;
  font-size: 1.1rem;
  cursor: pointer;
  transition: var(--employees-transition);
  display: flex;
  align-items: center;
  gap: 0.75rem;
  box-shadow: 0 4px 15px rgba(255, 255, 255, 0.3);
}

.employees-screen-add-btn:hover {
  transform: translateY(-3px);
  box-shadow: 0 8px 25px rgba(255, 255, 255, 0.4);
  background: #f8f9fa;
}

.employees-screen-stats {
  display: flex;
  gap: 2rem;
  color: var(--employees-white);
}

.employees-screen-stat-item {
  text-align: center;
}

.employees-screen-stat-number {
  font-size: 2rem;
  font-weight: 700;
  margin: 0;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

.employees-screen-stat-label {
  font-size: 0.9rem;
  opacity: 0.9;
  margin: 0.25rem 0 0 0;
}

/* Content Section */
.employees-screen-content {
  padding: 2rem;
}

/* Employee Cards Grid */
.employees-screen-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
  gap: 2rem;
  margin-top: 2rem;
}

.employees-screen-card {
  background: var(--employees-white);
  border-radius: var(--employees-border-radius);
  padding: 2rem;
  box-shadow: var(--employees-card-shadow);
  transition: var(--employees-transition);
  position: relative;
  overflow: visible;
  word-break: break-word;
  border: 1px solid rgba(108, 92, 231, 0.1);
}

.employees-screen-card:hover {
  transform: translateY(-8px);
  box-shadow: 0 12px 40px rgba(108, 92, 231, 0.25);
}

.employees-screen-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 6px;
  background: linear-gradient(90deg, var(--employees-primary), var(--employees-secondary));
  border-radius: var(--employees-border-radius) var(--employees-border-radius) 0 0;
}

/* Employee Avatar */
.employees-screen-avatar {
  width: 80px;
  height: 80px;
  border-radius: 50%;
  background: linear-gradient(135deg, var(--employees-primary), var(--employees-secondary));
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--employees-white);
  font-size: 2rem;
  font-weight: 700;
  margin: 0 auto 1.5rem auto;
  box-shadow: 0 6px 20px rgba(108, 92, 231, 0.3);
  border: 4px solid rgba(255, 255, 255, 0.9);
  transition: var(--employees-transition);
}

.employees-screen-card:hover .employees-screen-avatar {
  transform: scale(1.1);
  box-shadow: 0 8px 30px rgba(108, 92, 231, 0.4);
}

/* Employee Info */
.employees-screen-info {
  text-align: center;
  margin-bottom: 1.5rem;
}

.employees-screen-name {
  font-size: 1.4rem;
  font-weight: 700;
  color: var(--employees-dark);
  margin: 0 0 0.5rem 0;
}

.employees-screen-role {
  display: inline-block;
  background: linear-gradient(135deg, var(--employees-primary), var(--employees-secondary));
  color: var(--employees-white);
  padding: 0.5rem 1rem;
  border-radius: 25px;
  font-size: 0.9rem;
  font-weight: 600;
  margin-bottom: 0.75rem;
}

.employees-screen-username {
  color: var(--employees-info);
  font-size: 1rem;
  font-weight: 500;
  background: rgba(0, 206, 201, 0.1);
  padding: 0.25rem 0.75rem;
  border-radius: 15px;
  display: inline-block;
}

/* Employee Stats */
.employees-screen-employee-stats {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 1rem;
  margin-bottom: 1.5rem;
}

.employees-screen-stat-box {
  background: rgba(108, 92, 231, 0.05);
  padding: 1rem;
  border-radius: 12px;
  text-align: center;
  border: 1px solid rgba(108, 92, 231, 0.1);
}

.employees-screen-stat-box-number {
  font-size: 1.5rem;
  font-weight: 700;
  color: var(--employees-primary);
  margin: 0;
}

.employees-screen-stat-box-label {
  font-size: 0.85rem;
  color: var(--employees-dark);
  margin: 0.25rem 0 0 0;
  opacity: 0.8;
}

/* Action Buttons */
.employees-screen-actions {
  display: flex;
  gap: 0.75rem;
  justify-content: center;
}

.employees-screen-btn {
  padding: 0.75rem 1.25rem;
  border: none;
  border-radius: 10px;
  font-weight: 600;
  font-size: 0.9rem;
  cursor: pointer;
  transition: var(--employees-transition);
  display: flex;
  align-items: center;
  gap: 0.5rem;
  min-width: 100px;
  justify-content: center;
}

.employees-screen-btn.edit {
  background: var(--employees-warning);
  color: var(--employees-white);
  box-shadow: 0 4px 15px rgba(253, 203, 110, 0.3);
}

.employees-screen-btn.edit:hover {
  background: #e1b12c;
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(253, 203, 110, 0.4);
}

.employees-screen-btn.delete {
  background: var(--employees-danger);
  color: var(--employees-white);
  box-shadow: 0 4px 15px rgba(225, 112, 85, 0.3);
}

.employees-screen-btn.delete:hover {
  background: #c44536;
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(225, 112, 85, 0.4);
}

/* Empty State */
.employees-screen-empty {
  text-align: center;
  padding: 4rem 2rem;
  color: var(--employees-dark);
}

.employees-screen-empty-icon {
  font-size: 4rem;
  color: var(--employees-primary);
  opacity: 0.5;
  margin-bottom: 1.5rem;
}

.employees-screen-empty-title {
  font-size: 1.5rem;
  font-weight: 600;
  margin: 0 0 1rem 0;
  color: var(--employees-dark);
}

.employees-screen-empty-text {
  font-size: 1.1rem;
  opacity: 0.7;
  margin: 0;
}

/* Loading State */
.employees-screen-loading {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 4rem;
  color: var(--employees-primary);
}

.employees-screen-loading-icon {
  font-size: 3rem;
  margin-bottom: 1rem;
  animation: employees-screen-spin 1s linear infinite;
}

.employees-screen-loading-text {
  font-size: 1.2rem;
  font-weight: 500;
}

@keyframes employees-screen-spin {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

/* Responsive Design */
@media (max-width: 1200px) {
  .employees-screen-grid {
    grid-template-columns: repeat(auto-fill, minmax(320px, 1fr));
    gap: 1.5rem;
  }
}

@media (max-width: 768px) {
  .employees-screen-header {
    padding: 2rem 1.5rem;
  }
  
  .employees-screen-title {
    font-size: 2.25rem;
  }
  
  .employees-screen-content {
    padding: 1.5rem;
  }
  
  .employees-screen-grid {
    grid-template-columns: 1fr;
    gap: 1.5rem;
  }
  
  .employees-screen-controls {
    flex-direction: column;
    align-items: stretch;
  }
  
  .employees-screen-stats {
    justify-content: space-around;
    gap: 1rem;
  }
}

@media (max-width: 480px) {
  .employees-screen-header {
    padding: 1.5rem 1rem;
  }
  
  .employees-screen-title {
    font-size: 1.75rem;
    flex-direction: column;
    gap: 0.5rem;
  }
  
  .employees-screen-content {
    padding: 1rem;
  }
  
  .employees-screen-card {
    padding: 1.5rem;
  }
  
  .employees-screen-actions {
    flex-direction: column;
  }
  
  .employees-screen-employee-stats {
    grid-template-columns: 1fr;
  }
}




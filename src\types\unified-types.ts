/**
 * TypeScript Types for Coffee Management System
 * نماذج البيانات الموحدة بين Frontend و Backend
 */

// ===== BASE TYPES =====

export interface User {
  _id: string;
  username: string;
  name: string;
  email: string;
  role: 'admin' | 'manager' | 'employee' | 'waiter' | 'chef';
  status: 'active' | 'inactive' | 'suspended';
  phone?: string;
  lastLogin?: Date;
  createdAt: Date;
  updatedAt: Date;
}

export interface Product {
  _id: string;
  name: string;
  description?: string;
  price: number;
  category: string;
  image?: string;
  available: boolean;
  featured: boolean;
  preparationTime?: number;
  rating?: {
    average: number;
    count: number;
  };
  stock?: {
    quantity: number;
    unit: string;
    lowStockAlert: number;
  };
  createdAt: Date;
  updatedAt: Date;
}

// ===== ORDER TYPES =====

export interface OrderItem {
  product: string; // Backend format (ObjectId)
  productId?: string; // Frontend format (for compatibility)
  productName: string;
  quantity: number;
  price: number;
  subtotal: number;
  notes?: string;
}

export interface Customer {
  name: string;
  phone?: string;
  email?: string;
  address?: string;
}

export interface OrderTotals {
  subtotal: number;
  tax: number;
  discount: number;
  total: number;
  deliveryFee?: number;
}

export interface OrderTable {
  number: number;
  section?: string;
}

export interface OrderStaff {
  waiter?: User | string;
  chef?: User | string;
  cashier?: User | string;
}

export interface OrderTiming {
  orderTime: Date;
  confirmedAt?: Date;
  preparingAt?: Date;
  readyAt?: Date;
  servedAt?: Date;
  completedAt?: Date;
  estimatedPreparationTime?: number;
}

export interface Order {
  _id: string;
  orderNumber: string;
  customer: Customer;
  items: OrderItem[];
  totals: OrderTotals;
  table?: OrderTable;
  orderType: 'dine-in' | 'takeaway' | 'delivery';
  status: 'pending' | 'confirmed' | 'preparing' | 'ready' | 'served' | 'completed' | 'cancelled';
  staff: OrderStaff;
  timing: OrderTiming;
  payment: {
    method: 'cash' | 'card' | 'online' | 'wallet';
    status: 'pending' | 'paid' | 'failed' | 'refunded';
    transactionId?: string;
    paidAt?: Date;
  };
  notes?: {
    customer?: string;
    staff?: string;
    kitchen?: string;
  };
  // حقول الخصم
  discountApplied?: number;
  discountReason?: string;
  discountStatus?: 'none' | 'pending' | 'approved' | 'rejected';
  discountAmount?: number;
  totalPrice?: number; // للتوافق مع الواجهات الأمامية
  createdAt: Date;
  updatedAt: Date;
}

// ===== FRONTEND COMPATIBILITY TYPES =====

/**
 * Frontend Order Format (WaiterDashboard)
 */
export interface FrontendOrder {
  _id: string;
  orderNumber: string;
  customerName: string; // Mapped from customer.name
  waiterName?: string; // Mapped from staff.waiter.name
  chefName?: string; // Mapped from staff.chef.name
  tableNumber?: string; // Mapped from table.number
  totalPrice: number; // Mapped from totals.total
  status: string;
  orderType: string;
  items: OrderItem[];
  timing: OrderTiming;
  // حقول الخصم
  discountApplied?: number;
  discountReason?: string;
  discountStatus?: 'none' | 'pending' | 'approved' | 'rejected';
  discountAmount?: number;
  createdAt: Date;
}

/**
 * Frontend Order Creation Request
 */
export interface CreateOrderRequest {
  customer: Customer;
  items: Array<{
    productId: string; // Frontend sends productId
    productName?: string;
    name?: string; // Alternative name field
    quantity: number;
    price?: number; // Optional, will be fetched from product
    notes?: string;
  }>;
  orderType?: 'dine-in' | 'takeaway' | 'delivery';
  table?: {
    tableNumber?: number; // Frontend format
    number?: number; // Backend format
    section?: string;
  };
  totals?: OrderTotals;
  waiterName?: string; // Frontend format
  notes?: string;
}

// ===== CHEF DASHBOARD TYPES =====

export interface ChefOrder {
  _id: string;
  orderNumber: string;
  customerName: string;
  waiterName?: string;
  chefName?: string;
  tableNumber?: string;
  totalPrice: number;
  status: string;
  items: Array<{
    productName: string;
    quantity: number;
    price: number;
    notes?: string;
  }>;
  timing: {
    orderTime: Date;
    preparingAt?: Date;
    readyAt?: Date;
    estimatedPreparationTime?: number;
  };
  priority?: 'low' | 'normal' | 'high' | 'urgent';
}

// ===== TABLE TYPES =====

export interface TableFeatures {
  hasWindow: boolean;
  hasTV: boolean;
  isVIP: boolean;
  hasOutlet: boolean;
  isSmokingAllowed: boolean;
}

export interface TableStats {
  totalOrders: number;
  totalRevenue: number;
  averageOrderValue: number;
  lastUsed?: Date;
}

export interface Table {
  _id: string;
  number: number;
  section: string;
  capacity: number;
  status: 'available' | 'occupied' | 'reserved' | 'cleaning' | 'maintenance';
  currentOrder?: string | Order;
  assignedWaiter?: string | User;
  features: TableFeatures;
  stats: TableStats;
  isActive: boolean;
  createdAt: Date;
  updatedAt: Date;
}

/**
 * Frontend Table Account Format
 */
export interface TableAccount {
  _id: string;
  tableNumber: string; // Mapped from number
  waiterName?: string; // Mapped from assignedWaiter.name
  section: string;
  status: 'active' | 'closed';
  customer?: {
    name: string;
    phone?: string;
  };
  orders: Order[];
  totalAmount: number; // Mapped from stats.totalRevenue
  duration?: number; // Occupation time in minutes
  openTime?: Date;
  closeTime?: Date;
  isVIP?: boolean; // Mapped from features.isVIP
  createdAt: Date;
}

// ===== API RESPONSE TYPES =====

export interface ApiResponse<T> {
  success: boolean;
  message?: string;
  data?: T;
  error?: string;
  pagination?: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
  };
}

export interface HealthCheckResponse {
  status: 'OK' | 'ERROR';
  message: string;
  timestamp: string;
  version: string;
  database: {
    connected: boolean;
    message: string;
    userCount?: number;
  };
  environment: 'development' | 'production';
}

// ===== COMPATIBILITY MIDDLEWARE TYPES =====

export interface OrderCompatibilityConfig {
  convertProductId: boolean;
  convertCustomerName: boolean;
  convertTableNumber: boolean;
  validateRequiredFields: boolean;
  logConversions: boolean;
}

export interface ResponseMappingConfig {
  mapWaiterName: boolean;
  mapChefName: boolean;
  mapTotalPrice: boolean;
  mapTableNumber: boolean;
  mapCustomerName: boolean;
}

// ===== UTILITY TYPES =====

export type OrderStatus = Order['status'];
export type UserRole = User['role'];
export type TableStatus = Table['status'];
export type PaymentMethod = Order['payment']['method'];
export type OrderType = Order['orderType'];

// ===== VALIDATION SCHEMAS =====

export interface ValidationError {
  field: string;
  message: string;
  value?: any;
}

export interface OrderValidationResult {
  isValid: boolean;
  errors: ValidationError[];
  warnings: string[];
  converted: boolean;
}

// ===== EVENT TYPES =====

export interface OrderEvent {
  type: 'created' | 'updated' | 'status_changed' | 'deleted';
  orderId: string;
  timestamp: Date;
  userId?: string;
  changes?: Record<string, any>;
}

export interface TableEvent {
  type: 'occupied' | 'released' | 'reserved' | 'status_changed';
  tableId: string;
  tableNumber: number;
  timestamp: Date;
  userId?: string;
  orderId?: string;
}

// ===== EXPORT ALL =====

export * from './Order';

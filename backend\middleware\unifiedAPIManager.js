// Unified API Router Manager
// نظام موحد لإدارة جميع routes

const express = require('express');
const { applyUnifiedMiddleware } = require('../middleware/unifiedRouteHandler');
const { sendError } = require('../middleware/unifiedResponse');

class UnifiedAPIManager {
  constructor() {
    this.router = express.Router();
    this.routes = new Map();
    this.middleware = [];
    this.setupGlobalMiddleware();
  }

  setupGlobalMiddleware() {
    // Apply unified middleware to all routes
    applyUnifiedMiddleware(this.router);

    // Global error handler
    this.router.use((error, req, res, next) => {
      console.error('🚨 Global API Error:', {
        path: req.path,
        method: req.method,
        error: error.message,
        stack: process.env.NODE_ENV === 'development' ? error.stack : undefined,
        deviceType: req.deviceInfo?.type
      });

      if (error.name === 'ValidationError') {
        return sendError(res, 'خطأ في التحقق من البيانات', 400, 'VALIDATION_ERROR', error.message);
      } else if (error.name === 'CastError') {
        return sendError(res, 'معرف غير صحيح', 400, 'INVALID_ID');
      } else if (error.code === 11000) {
        return sendError(res, 'البيانات موجودة بالفعل', 409, 'DUPLICATE_DATA');
      } else if (error.message.includes('not found')) {
        return sendError(res, 'العنصر غير موجود', 404, 'NOT_FOUND');
      } else {
        return sendError(res, 'خطأ داخلي في الخادم', 500, 'INTERNAL_ERROR');
      }
    });
  }

  // Register a route with the unified system
  registerRoute(path, routeHandler, options = {}) {
    const { version = 'v1', deprecated = false, rateLimit = null } = options;

    // Add route to registry
    this.routes.set(path, {
      handler: routeHandler,
      version,
      deprecated,
      rateLimit,
      registeredAt: new Date()
    });

    // Apply version prefix
    const versionedPath = `/api/${version}${path}`;

    // Add deprecation warning if needed
    if (deprecated) {
      this.router.use(versionedPath, (req, res, next) => {
        res.header('X-API-Deprecated', 'true');
        res.header('X-API-Deprecation-Date', options.deprecationDate || '');
        console.warn(`⚠️ Deprecated API used: ${req.method} ${req.path} by ${req.deviceInfo?.type}`);
        next();
      });
    }

    // Apply rate limiting if specified
    if (rateLimit) {
      this.router.use(versionedPath, rateLimit);
    }

    // Register the route
    this.router.use(versionedPath, routeHandler);

    console.log(`✅ Route registered: ${versionedPath} (${version})`);
  }

  // Get route information
  getRouteInfo() {
    const routeInfo = [];
    for (const [path, info] of this.routes) {
      routeInfo.push({
        path: `/api/${info.version}${path}`,
        version: info.version,
        deprecated: info.deprecated,
        registeredAt: info.registeredAt
      });
    }
    return routeInfo;
  }
  // Health check endpoint
  setupHealthCheck() {
    this.router.get('/health', async (req, res) => {
      const mongoose = require('mongoose');
      
      // فحص حالة قاعدة البيانات
      const dbStatus = mongoose.connection.readyState;
      const isDbConnected = dbStatus === 1; // 1 = connected
      
      const healthInfo = {
        status: 'healthy',
        timestamp: new Date().toISOString(),
        uptime: process.uptime(),
        memory: process.memoryUsage(),
        routes: this.routes.size,
        version: process.env.npm_package_version || '1.0.0',
        environment: process.env.NODE_ENV || 'development',
        database: {
          connected: isDbConnected,
          status: isDbConnected ? 'connected' : 'disconnected',
          host: mongoose.connection.host || 'unknown',
          name: mongoose.connection.name || 'unknown'
        }
      };

      res.json(healthInfo);
    });

    this.router.get('/api/health', (req, res) => {
      res.redirect('/health');
    });
  }

  // API documentation endpoint
  setupApiDocs() {
    this.router.get('/api/docs', (req, res) => {
      const docs = {
        title: 'Coffee Shop Management System API',
        version: '1.0.0',
        description: 'Unified API for Coffee Shop Management System',
        baseUrl: `${req.protocol}://${req.get('host')}/api`,
        routes: this.getRouteInfo(),
        authentication: {
          type: 'Bearer Token',
          header: 'Authorization'
        },
        responseFormat: {
          success: {
            success: true,
            message: 'Success message',
            data: 'Response data',
            timestamp: 'ISO timestamp'
          },
          error: {
            success: false,
            message: 'Error message',
            error: 'Error code',
            timestamp: 'ISO timestamp'
          }
        }
      };

      res.json(docs);
    });
  }

  // Get the configured router
  getRouter() {
    this.setupHealthCheck();
    this.setupApiDocs();
    return this.router;
  }
}

// Create singleton instance
const apiManager = new UnifiedAPIManager();

module.exports = apiManager;

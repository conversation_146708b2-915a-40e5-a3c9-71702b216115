/* CategoriesManagerScreen.css - Scoped styles for Categories Manager Screen */

/* ??????? ????????? ??????? */
@import '../variables/categories-variables.css';

/* Main container */
.categoriesManagerScreen {
  padding: 2rem;
  direction: rtl;
  font-family: var(--categories-fontFamily);
  min-height: 100vh;
  background: var(--categories-background);
}

/* Header section */
.categoriesManagerScreen__header {
  margin-bottom: 2rem;
  text-align: center;
}

.categoriesManagerScreen__header-title {
  color: var(--categories-textColorLight);
  font-size: 2.5rem;
  font-weight: 700;
  margin: 0 0 0.5rem 0;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 1rem;
}

.categoriesManagerScreen__header-icon {
  font-size: 2rem;
}

.categoriesManagerScreen__header-subtitle {
  color: rgba(255, 255, 255, 0.9);
  font-size: 1.1rem;
  margin: 0;
  font-weight: 400;
}

/* Search and filters section */
.categoriesManagerScreen__controls {
  background: var(--categories-cardBackground);
  border-radius: var(--categories-borderRadius);
  padding: var(--categories-spacing);
  margin-bottom: 2rem;
  box-shadow: 0 8px 25px var(--categories-shadowColor);
  display: flex;
  gap: 1rem;
  flex-wrap: wrap;
  align-items: center;
}

.categoriesManagerScreen__search-group {
  flex: 1;
  min-width: 250px;
}

.categoriesManagerScreen__search-input {
  width: 100%;
  padding: 0.75rem 1rem;
  border: 2px solid #e5e7eb;
  border-radius: 10px;
  font-size: 1rem;
  transition: border-color 0.2s ease;
}

.categoriesManagerScreen__search-input:focus {
  outline: none;
  border-color: var(--categories-primaryColor);
}

.categoriesManagerScreen__filter-group {
  display: flex;
  gap: 0.5rem;
}

.categoriesManagerScreen__filter-button {
  padding: 0.5rem 1rem;
  border: 2px solid var(--categories-primaryColor);
  background: transparent;
  color: var(--categories-primaryColor);
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.2s ease;
  font-weight: 500;
}

.categoriesManagerScreen__filter-button:hover,
.categoriesManagerScreen__filter-button--active {
  background: var(--categories-primaryColor);
  color: white;
}

/* Categories grid */
.categoriesManagerScreen__grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: var(--categories-spacing);
  max-width: 1400px;
  margin: 0 auto;
}

/* Individual category card */
.categoriesManagerScreen__category-card {
  background: var(--categories-cardBackground);
  border-radius: var(--categories-borderRadius);
  padding: var(--categories-spacing);
  box-shadow: 0 8px 25px var(--categories-shadowColor);
  border: 2px solid transparent;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.categoriesManagerScreen__category-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: var(--categories-primaryColor);
  transform: scaleX(0);
  transition: transform 0.3s ease;
}

.categoriesManagerScreen__category-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 15px 35px rgba(0, 0, 0, 0.15);
  border-color: var(--categories-primaryColor);
}

.categoriesManagerScreen__category-card:hover::before {
  transform: scaleX(1);
}

/* Category card header */
.categoriesManagerScreen__category-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 1rem;
}

.categoriesManagerScreen__category-info {
  flex: 1;
}

.categoriesManagerScreen__category-name {
  font-size: 1.3rem;
  font-weight: 600;
  color: var(--categories-textColor);
  margin: 0 0 0.5rem 0;
}

.categoriesManagerScreen__category-description {
  color: var(--categories-textColorMuted);
  font-size: 0.9rem;
  line-height: 1.4;
  margin: 0;
}

.categoriesManagerScreen__category-icon {
  width: 50px;
  height: 50px;
  background: var(--categories-primaryColor);
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 1.5rem;
  margin-left: 1rem;
}

/* Category stats */
.categoriesManagerScreen__category-stats {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 1rem;
  margin: 1rem 0;
  padding: 1rem;
  background: #f9fafb;
  border-radius: 10px;
}

.categoriesManagerScreen__stat {
  text-align: center;
}

.categoriesManagerScreen__stat-value {
  font-size: 1.5rem;
  font-weight: 700;
  color: var(--categories-primaryColor);
  margin-bottom: 0.25rem;
}

.categoriesManagerScreen__stat-label {
  font-size: 0.85rem;
  color: var(--categories-textColorMuted);
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

/* Category status */
.categoriesManagerScreen__category-status {
  position: absolute;
  top: 1rem;
  right: 1rem;
  padding: 0.25rem 0.75rem;
  border-radius: 20px;
  font-size: 0.8rem;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.categoriesManagerScreen__category-status--active {
  background: #d1fae5;
  color: #065f46;
}

.categoriesManagerScreen__category-status--inactive {
  background: #fee2e2;
  color: #991b1b;
}

/* Action buttons */
.categoriesManagerScreen__category-actions {
  display: flex;
  gap: 0.5rem;
  margin-top: 1rem;
}

.categoriesManagerScreen__button {
  flex: 1;
  padding: 0.5rem 1rem;
  border: none;
  border-radius: 8px;
  font-size: 0.9rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  text-decoration: none;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
}

.categoriesManagerScreen__button--primary {
  background: var(--categories-primaryColor);
  color: white;
}

.categoriesManagerScreen__button--primary:hover {
  background: var(--categories-secondaryColor);
  transform: translateY(-1px);
}

.categoriesManagerScreen__button--secondary {
  background: #6b7280;
  color: white;
}

.categoriesManagerScreen__button--secondary:hover {
  background: #4b5563;
  transform: translateY(-1px);
}

.categoriesManagerScreen__button--success {
  background: var(--categories-successColor);
  color: white;
}

.categoriesManagerScreen__button--success:hover {
  background: #059669;
  transform: translateY(-1px);
}

.categoriesManagerScreen__button--danger {
  background: var(--categories-dangerColor);
  color: white;
}

.categoriesManagerScreen__button--danger:hover {
  background: #dc2626;
  transform: translateY(-1px);
}

/* Add category button */
.categoriesManagerScreen__add-button {
  position: fixed;
  bottom: 2rem;
  right: 2rem;
  width: 60px;
  height: 60px;
  border-radius: 50%;
  background: var(--categories-primaryColor);
  color: white;
  border: none;
  font-size: 1.5rem;
  cursor: pointer;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
  transition: all 0.3s ease;
  z-index: 100;
}

.categoriesManagerScreen__add-button:hover {
  transform: scale(1.1);
  box-shadow: 0 6px 20px rgba(0, 0, 0, 0.3);
}

/* Responsive design */
@media (max-width: 768px) {
  .categoriesManagerScreen {
    padding: 1rem;
  }
  
  .categoriesManagerScreen__controls {
    flex-direction: column;
    gap: 1rem;
  }
  
  .categoriesManagerScreen__search-group {
    min-width: 100%;
  }
  
  .categoriesManagerScreen__filter-group {
    flex-wrap: wrap;
    justify-content: center;
  }
  
  .categoriesManagerScreen__grid {
    grid-template-columns: 1fr;
    gap: 1rem;
  }
  
  .categoriesManagerScreen__header-title {
    font-size: 2rem;
    flex-direction: column;
    gap: 0.5rem;
  }
  
  .categoriesManagerScreen__category-header {
    flex-direction: column;
    text-align: center;
  }
  
  .categoriesManagerScreen__category-icon {
    margin: 0 auto 1rem;
  }
  
  .categoriesManagerScreen__category-stats {
    grid-template-columns: 1fr;
    gap: 0.5rem;
  }
  
  .categoriesManagerScreen__category-actions {
    flex-direction: column;
  }
}

/* Loading state */
.categoriesManagerScreen__loading {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 50vh;
  color: var(--categories-textColorLight);
  font-size: 1.2rem;
}

.categoriesManagerScreen__spinner {
  width: 40px;
  height: 40px;
  border: 4px solid rgba(255, 255, 255, 0.3);
  border-top: 4px solid white;
  border-radius: 50%;
  animation: categoriesManagerScreen-spin 1s linear infinite;
  margin-right: 1rem;
}

@keyframes categoriesManagerScreen-spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Empty state */
.categoriesManagerScreen__empty {
  text-align: center;
  color: var(--categories-textColorLight);
  padding: 3rem;
}

.categoriesManagerScreen__empty-icon {
  font-size: 4rem;
  margin-bottom: 1rem;
  opacity: 0.7;
}

.categoriesManagerScreen__empty-message {
  font-size: 1.2rem;
  margin-bottom: 1rem;
}

.categoriesManagerScreen__empty-submessage {
  font-size: 1rem;
  opacity: 0.8;
}


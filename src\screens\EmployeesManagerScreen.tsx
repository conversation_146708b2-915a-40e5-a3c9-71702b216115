import React, { useState, useEffect } from 'react';
import { authenticatedGet, authenticatedPost, authenticatedPut, authenticatedDelete } from '../utils/apiHelpers';
import { useToast } from '../hooks/useToast';
// استدعاء ملف CSS الجديد المنظم
import '../styles/manager/EmployeesManagerScreen.css';

interface Employee {
  _id: string;
  username: string;
  name: string;
  email?: string;
  phone?: string;
  role: 'waiter' | 'chef' | 'manager';
  status: 'active' | 'inactive';
  isActive: boolean;
  currentShift?: any;
}

interface EmployeesManagerScreenProps {
  employees: Employee[];
  onEmployeesUpdate: (employees: Employee[]) => void;
  loading: boolean;
}

const EmployeesManagerScreen: React.FC<EmployeesManagerScreenProps> = ({
  employees,
  onEmployeesUpdate,
  loading
}) => {
  const [employeesScreenSearchTerm, setEmployeesScreenSearchTerm] = useState('');
  const [employeesScreenRoleFilter, setEmployeesScreenRoleFilter] = useState<'all' | 'waiter' | 'chef' | 'manager'>('all');
  const [employeesScreenStatusFilter, setEmployeesScreenStatusFilter] = useState<'all' | 'active' | 'inactive'>('all');
  const [employeesScreenShowModal, setEmployeesScreenShowModal] = useState(false);
  const [employeesScreenEditingEmployee, setEmployeesScreenEditingEmployee] = useState<Employee | null>(null);
  const [sortConfig, setSortConfig] = useState<{key: string, direction: 'asc' | 'desc'} | null>(null);
  const [employeesScreenFormData, setEmployeesScreenFormData] = useState({
    username: '',
    password: '',
    name: '',
    email: '',
    phone: '',
    role: 'waiter' as 'waiter' | 'chef' | 'manager'
  });

  const toast = useToast();

  // دالة الترتيب
  const handleSort = (key: string) => {
    let direction: 'asc' | 'desc' = 'asc';
    if (sortConfig && sortConfig.key === key && sortConfig.direction === 'asc') {
      direction = 'desc';
    }
    setSortConfig({ key, direction });
  };

  // Filter and sort employees
  const filteredAndSortedEmployees = (() => {
    let filtered = employees.filter(employee => {
      const matchesSearch = employee.name?.toLowerCase().includes(employeesScreenSearchTerm.toLowerCase()) ||
                           employee.username?.toLowerCase().includes(employeesScreenSearchTerm.toLowerCase());
      const matchesRole = employeesScreenRoleFilter === 'all' || employee.role === employeesScreenRoleFilter;
      const matchesStatus = employeesScreenStatusFilter === 'all' || 
                           (employeesScreenStatusFilter === 'active' && employee.isActive) ||
                           (employeesScreenStatusFilter === 'inactive' && !employee.isActive);
      
      return matchesSearch && matchesRole && matchesStatus;
    });

    // Apply sorting
    if (sortConfig) {
      filtered.sort((a, b) => {
        let aValue: any = a[sortConfig.key as keyof Employee];
        let bValue: any = b[sortConfig.key as keyof Employee];

        // Handle special cases
        if (sortConfig.key === 'status') {
          aValue = a.isActive ? 'active' : 'inactive';
          bValue = b.isActive ? 'active' : 'inactive';
        }

        if (aValue < bValue) {
          return sortConfig.direction === 'asc' ? -1 : 1;
        }
        if (aValue > bValue) {
          return sortConfig.direction === 'asc' ? 1 : -1;
        }
        return 0;
      });
    }

    return filtered;
  })();

  const handleEmployeesScreenSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    try {
      if (employeesScreenEditingEmployee) {
        // Update employee
        const response = await authenticatedPut(`/api/v1/employees/${employeesScreenEditingEmployee._id}`, {
          ...employeesScreenFormData,
          ...(employeesScreenFormData.password ? { password: employeesScreenFormData.password } : {})
        });
        
        if (response.success) {
          const updatedEmployees = employees.map(emp => 
            emp._id === employeesScreenEditingEmployee._id ? response.data : emp
          );
          onEmployeesUpdate(updatedEmployees);
          toast.showSuccess('تم تحديث بيانات الموظف بنجاح');
        }
      } else {
        // Create new employee
        const response = await authenticatedPost('/api/v1/employees', employeesScreenFormData);
        
        if (response.success) {
          onEmployeesUpdate([...employees, response.data]);
          toast.showSuccess('تم إضافة الموظف بنجاح');
        }
      }
      
      handleEmployeesScreenCloseModal();
    } catch (error) {
      toast.showError('حدث خطأ أثناء حفظ بيانات الموظف');
    }
  };

  const handleEmployeesScreenEdit = (employee: Employee) => {
    setEmployeesScreenEditingEmployee(employee);
    setEmployeesScreenFormData({
      username: employee.username,
      password: '',
      name: employee.name,
      email: employee.email || '',
      phone: employee.phone || '',
      role: employee.role
    });
    setEmployeesScreenShowModal(true);
  };

  const handleEmployeesScreenDelete = async (employeeId: string) => {
    if (!window.confirm('هل أنت متأكد من حذف هذا الموظف؟')) return;
    
    try {
      const response = await authenticatedDelete(`/api/v1/employees/${employeeId}`);
      if (response.success) {
        onEmployeesUpdate(employees.filter(emp => emp._id !== employeeId));
        toast.showSuccess('تم حذف الموظف بنجاح');
      }
    } catch (error) {
      toast.showError('حدث خطأ أثناء حذف الموظف');
    }
  };

  const handleEmployeesScreenToggleStatus = async (employee: Employee) => {
    try {
      const response = await authenticatedPut(`/api/v1/employees/${employee._id}`, {
        isActive: !employee.isActive
      });
      
      if (response.success) {
        const updatedEmployees = employees.map(emp => 
          emp._id === employee._id ? { ...emp, isActive: !emp.isActive } : emp
        );
        onEmployeesUpdate(updatedEmployees);
        toast.showSuccess(`تم ${!employee.isActive ? 'تفعيل' : 'إلغاء تفعيل'} الموظف`);
      }
    } catch (error) {
      toast.showError('حدث خطأ أثناء تغيير حالة الموظف');
    }
  };

  const handleEmployeesScreenCloseModal = () => {
    setEmployeesScreenShowModal(false);
    setEmployeesScreenEditingEmployee(null);
    setEmployeesScreenFormData({
      username: '',
      password: '',
      name: '',
      email: '',
      phone: '',
      role: 'waiter'
    });
  };

  const getRoleDisplayName = (role: string) => {
    switch (role) {
      case 'waiter': return 'نادل';
      case 'chef': return 'طباخ';
      case 'manager': return 'مدير';
      default: return role;
    }
  };

  return (
    <div className="employeesManagerScreen">
      <div className="employeesManagerScreen__header">
        <div className="employeesManagerScreen__title">
          <h2>إدارة الموظفين</h2>
          <p>إدارة حسابات وبيانات الموظفين</p>
        </div>
        <button 
          className="employeesManagerScreen__add-btn"
          onClick={() => setEmployeesScreenShowModal(true)}
        >
          <i className="fas fa-plus"></i>
          إضافة موظف جديد
        </button>
      </div>

      {/* Filters and Search */}
      <div className="employees-manager-screen-filters">
        <div className="employees-manager-screen-search">
          <i className="fas fa-search"></i>
          <input
            type="text"
            placeholder="البحث عن موظف..."
            value={employeesScreenSearchTerm}
            onChange={(e) => setEmployeesScreenSearchTerm(e.target.value)}
          />
        </div>
        
        <div className="employees-manager-screen-filter-group">
          <select
            value={employeesScreenRoleFilter}
            onChange={(e) => setEmployeesScreenRoleFilter(e.target.value as any)}
            title="تصفية حسب الدور"
          >
            <option value="all">جميع الأدوار</option>
            <option value="waiter">نادل</option>
            <option value="chef">طباخ</option>
            <option value="manager">مدير</option>
          </select>
          
          <select
            value={employeesScreenStatusFilter}
            onChange={(e) => setEmployeesScreenStatusFilter(e.target.value as any)}
            title="تصفية حسب الحالة"
          >
            <option value="all">جميع الحالات</option>
            <option value="active">نشط</option>
            <option value="inactive">غير نشط</option>
          </select>
        </div>
      </div>

      {/* Statistics Cards */}
      <div className="employees-manager-screen-stats">
        <div className="employees-manager-screen-stat-card total">
          <div className="employees-manager-screen-stat-icon">
            <i className="fas fa-users"></i>
          </div>
          <div className="employees-manager-screen-stat-content">
            <h3>{employees.length}</h3>
            <p>إجمالي الموظفين</p>
          </div>
        </div>
        
        <div className="employees-manager-screen-stat-card active">
          <div className="employees-manager-screen-stat-icon">
            <i className="fas fa-user-check"></i>
          </div>
          <div className="employees-manager-screen-stat-content">
            <h3>{employees.filter(emp => emp.isActive).length}</h3>
            <p>الموظفون النشطون</p>
          </div>
        </div>
        
        <div className="employees-manager-screen-stat-card waiters">
          <div className="employees-manager-screen-stat-icon">
            <i className="fas fa-concierge-bell"></i>
          </div>
          <div className="employees-manager-screen-stat-content">
            <h3>{employees.filter(emp => emp.role === 'waiter').length}</h3>
            <p>النُدُل</p>
          </div>
        </div>
        
        <div className="employees-manager-screen-stat-card chefs">
          <div className="employees-manager-screen-stat-icon">
            <i className="fas fa-chef-hat"></i>
          </div>
          <div className="employees-manager-screen-stat-content">
            <h3>{employees.filter(emp => emp.role === 'chef').length}</h3>
            <p>الطباخين</p>
          </div>
        </div>
      </div>

      {/* Employees Table */}
      <div className="employees-manager-screen-table-container">
        {loading ? (
          <div className="employees-manager-screen-loading">
            <i className="fas fa-spinner fa-spin"></i>
            <p>جاري تحميل بيانات الموظفين...</p>
          </div>
        ) : (
          <table className="employees-manager-screen-table">
            <thead>
              <tr>
                <th 
                  className="sortable"
                  onClick={() => handleSort('name')}
                  title="ترتيب حسب الاسم"
                >
                  الاسم
                  {sortConfig?.key === 'name' && (
                    <i className={`fas fa-sort-${sortConfig.direction === 'asc' ? 'up' : 'down'}`}></i>
                  )}
                </th>
                <th 
                  className="sortable"
                  onClick={() => handleSort('username')}
                  title="ترتيب حسب اسم المستخدم"
                >
                  اسم المستخدم
                  {sortConfig?.key === 'username' && (
                    <i className={`fas fa-sort-${sortConfig.direction === 'asc' ? 'up' : 'down'}`}></i>
                  )}
                </th>
                <th 
                  className="sortable"
                  onClick={() => handleSort('role')}
                  title="ترتيب حسب الدور"
                >
                  الدور
                  {sortConfig?.key === 'role' && (
                    <i className={`fas fa-sort-${sortConfig.direction === 'asc' ? 'up' : 'down'}`}></i>
                  )}
                </th>
                <th 
                  className="sortable"
                  onClick={() => handleSort('status')}
                  title="ترتيب حسب الحالة"
                >
                  الحالة
                  {sortConfig?.key === 'status' && (
                    <i className={`fas fa-sort-${sortConfig.direction === 'asc' ? 'up' : 'down'}`}></i>
                  )}
                </th>
                <th>البريد الإلكتروني</th>
                <th>الهاتف</th>
                <th>الإجراءات</th>
              </tr>
            </thead>
            <tbody>
              {filteredAndSortedEmployees.map(employee => (
                <tr key={employee._id}>
                  <td>
                    <div className="employees-manager-screen-employee-info">
                      <div className="employees-manager-screen-employee-avatar">
                        {employee.name?.charAt(0) || employee.username?.charAt(0)}
                      </div>
                      <span>{employee.name}</span>
                    </div>
                  </td>
                  <td>{employee.username}</td>
                  <td>
                    <span className={`employees-manager-screen-role-badge ${employee.role}`}>
                      {getRoleDisplayName(employee.role)}
                    </span>
                  </td>
                  <td>
                    <span className={`employees-manager-screen-status-badge ${employee.isActive ? 'active' : 'inactive'}`}>
                      <i className={`fas ${employee.isActive ? 'fa-check-circle' : 'fa-times-circle'}`}></i>
                      {employee.isActive ? 'نشط' : 'غير نشط'}
                    </span>
                  </td>
                  <td>{employee.email || '-'}</td>
                  <td>{employee.phone || '-'}</td>
                  <td>
                    <div className="employees-manager-screen-actions">
                      <button
                        className="employees-manager-screen-action-btn edit"
                        onClick={() => handleEmployeesScreenEdit(employee)}
                        title="تعديل"
                      >
                        <i className="fas fa-edit"></i>
                      </button>
                      <button
                        className="employees-manager-screen-action-btn toggle"
                        onClick={() => handleEmployeesScreenToggleStatus(employee)}
                        title={employee.isActive ? 'إلغاء تفعيل' : 'تفعيل'}
                      >
                        <i className={`fas ${employee.isActive ? 'fa-ban' : 'fa-check'}`}></i>
                      </button>
                      <button
                        className="employees-manager-screen-action-btn delete"
                        onClick={() => handleEmployeesScreenDelete(employee._id)}
                        title="حذف"
                      >
                        <i className="fas fa-trash"></i>
                      </button>
                    </div>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        )}
        
        {!loading && filteredAndSortedEmployees.length === 0 && (
          <div className="employees-manager-screen-no-data">
            <i className="fas fa-users"></i>
            <p>لا توجد موظفين مطابقين للبحث</p>
          </div>
        )}
      </div>

      {/* Add/Edit Employee Modal */}
      {employeesScreenShowModal && (
        <div className="employees-manager-screen-modal-overlay">
          <div className="employees-manager-screen-modal">
            <div className="employees-manager-screen-modal-header">
              <h3>{employeesScreenEditingEmployee ? 'تعديل موظف' : 'إضافة موظف جديد'}</h3>
              <button 
                className="employees-manager-screen-modal-close"
                onClick={handleEmployeesScreenCloseModal}
                title="إغلاق النافذة"
              >
                <i className="fas fa-times"></i>
              </button>
            </div>
            
            <form onSubmit={handleEmployeesScreenSubmit} className="employees-manager-screen-form">
              <div className="employees-manager-screen-form-group">
                <label>اسم المستخدم</label>
                <input
                  type="text"
                  value={employeesScreenFormData.username}
                  onChange={(e) => setEmployeesScreenFormData(prev => ({...prev, username: e.target.value}))}
                  required
                  title="اسم المستخدم"
                  placeholder="أدخل اسم المستخدم"
                />
              </div>
              
              <div className="employees-manager-screen-form-group">
                <label>كلمة المرور {employeesScreenEditingEmployee && '(اتركها فارغة للاحتفاظ بالحالية)'}</label>
                <input
                  type="password"
                  value={employeesScreenFormData.password}
                  onChange={(e) => setEmployeesScreenFormData(prev => ({...prev, password: e.target.value}))}
                  required={!employeesScreenEditingEmployee}
                  title="كلمة المرور"
                  placeholder="أدخل كلمة المرور"
                />
              </div>
              
              <div className="employees-manager-screen-form-group">
                <label>الاسم الكامل</label>
                <input
                  type="text"
                  value={employeesScreenFormData.name}
                  onChange={(e) => setEmployeesScreenFormData(prev => ({...prev, name: e.target.value}))}
                  required
                  title="الاسم الكامل"
                  placeholder="أدخل الاسم الكامل"
                />
              </div>
              
              <div className="employees-manager-screen-form-group">
                <label>البريد الإلكتروني</label>
                <input
                  type="email"
                  value={employeesScreenFormData.email}
                  onChange={(e) => setEmployeesScreenFormData(prev => ({...prev, email: e.target.value}))}
                  title="البريد الإلكتروني"
                  placeholder="أدخل البريد الإلكتروني"
                />
              </div>
              
              <div className="employees-manager-screen-form-group">
                <label>رقم الهاتف</label>
                <input
                  type="tel"
                  value={employeesScreenFormData.phone}
                  onChange={(e) => setEmployeesScreenFormData(prev => ({...prev, phone: e.target.value}))}
                  title="رقم الهاتف"
                  placeholder="أدخل رقم الهاتف"
                />
              </div>
              
              <div className="employees-manager-screen-form-group">
                <label>الدور</label>
                <select
                  value={employeesScreenFormData.role}
                  onChange={(e) => setEmployeesScreenFormData(prev => ({...prev, role: e.target.value as any}))}
                  required
                  title="اختر دور الموظف"
                >
                  <option value="waiter">نادل</option>
                  <option value="chef">طباخ</option>
                  <option value="manager">مدير</option>
                </select>
              </div>
              
              <div className="employees-manager-screen-form-actions">
                <button type="button" onClick={handleEmployeesScreenCloseModal}>
                  إلغاء
                </button>
                <button type="submit">
                  {employeesScreenEditingEmployee ? 'تحديث' : 'إضافة'}
                </button>
              </div>
            </form>
          </div>
        </div>
      )}
    </div>
  );
};

export default EmployeesManagerScreen;

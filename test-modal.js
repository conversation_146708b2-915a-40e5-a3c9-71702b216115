// Script للاختبار السريع لـ Modal في المتصفح
// يمكن تشغيله في Developer Console

console.log('Testing Modal functionality...');

// البحث عن أزرار التفاصيل
const detailsButtons = document.querySelectorAll('.details-btn');
console.log('Found details buttons:', detailsButtons.length);

// البحث عن Modal
const modal = document.querySelector('.modal-overlay');
console.log('Modal found:', modal ? 'Yes' : 'No');

// التحقق من حالة CSS
const modalStyles = modal ? window.getComputedStyle(modal) : null;
if (modalStyles) {
  console.log('Modal z-index:', modalStyles.zIndex);
  console.log('Modal display:', modalStyles.display);
  console.log('Modal position:', modalStyles.position);
}

// اختبار click على زر
if (detailsButtons.length > 0) {
  console.log('Clicking first details button...');
  detailsButtons[0].click();
  
  setTimeout(() => {
    const modalAfterClick = document.querySelector('.modal-overlay');
    console.log('Modal after click:', modalAfterClick ? 'Visible' : 'Hidden');
  }, 100);
}

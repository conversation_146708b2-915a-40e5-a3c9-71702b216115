# Dependencies
node_modules
.npm
.pnpm-debug.log*

# Build artifacts
.vite
.tsbuildinfo

# Development files
.env.local
.env.development.local
.env.test.local
.env.production.local

# IDE files
.vscode
.idea
*.swp
*.swo

# OS files
.DS_Store
Thumbs.db

# Git
.git
.gitignore

# Backend directory (if separate deployment)
backend/

# Test files
coverage/
*.test.*
*.spec.*
__tests__/

# Documentation
docs/
*.md
!README.md

# Scripts
*.sh
*.bat
*.ps1

# Logs
logs
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*

/* ====================================
   CSS منفصل لشاشة الإعدادات
   Settings Screen - Isolated CSS
   ==================================== */

/* استيراد المتغيرات المميزة لشاشة الإعدادات */
@import '../variables/settings-variables.css';

/* تم إزالة المتغيرات المحلية - سيتم استخدام المتغيرات من ملف settings-variables.css فقط */

/* Settings Screen Container - معزول تماماً */
.settings-screen-container {
  width: 100%;
  min-height: calc(100vh - 80px);
  background: linear-gradient(135deg, #6c5ce7 0%, #a29bfe 100%);
  padding: 0;
  margin: 0;
  position: relative;
  overflow: hidden;
}

.settings-screen-container::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="settings-pattern" width="20" height="20" patternUnits="userSpaceOnUse"><circle cx="10" cy="10" r="1" fill="white" opacity="0.1"/><path d="M0,20 L20,0" stroke="white" stroke-width="0.5" opacity="0.05"/></pattern></defs><rect width="100" height="100" fill="url(%23settings-pattern)"/></svg>');
  pointer-events: none;
}

/* Header للإعدادات */
.settings-screen-header {
  padding: 3rem 2rem;
  color: var(--settings-white);
  text-align: center;
  position: relative;
  z-index: 1;
}

.settings-screen-title {
  font-size: 3.5rem;
  font-weight: 900;
  margin: 0 0 1rem 0;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 1rem;
  text-shadow: 0 6px 12px rgba(0, 0, 0, 0.3);
}

.settings-screen-title-icon {
  color: var(--settings-warning);
  text-shadow: 0 0 40px rgba(225, 112, 85, 0.8);
  animation: settings-screen-gear-rotate 8s linear infinite;
}

@keyframes settings-screen-gear-rotate {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

.settings-screen-subtitle {
  font-size: 1.4rem;
  opacity: 0.9;
  margin: 0 0 2rem 0;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

.settings-screen-warning {
  background: rgba(225, 112, 85, 0.2);
  color: var(--settings-white);
  padding: 1rem 2rem;
  border-radius: var(--settings-border-radius);
  border: 2px solid rgba(225, 112, 85, 0.4);
  font-weight: 600;
  font-size: 1.1rem;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.75rem;
  backdrop-filter: blur(10px);
}

/* Content Section */
.settings-screen-content {
  padding: 2rem;
  position: relative;
  z-index: 1;
}

/* Settings Sections */
.settings-screen-section {
  margin-bottom: 3rem;
}

.settings-screen-section-title {
  color: var(--settings-white);
  font-size: 2rem;
  font-weight: 700;
  margin-bottom: 2rem;
  text-align: center;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.75rem;
}

/* System Actions Grid */
.settings-screen-actions-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
  gap: 2rem;
}

.settings-screen-action-card {
  background: var(--settings-white);
  border-radius: var(--settings-border-radius);
  padding: 2.5rem;
  box-shadow: var(--settings-card-shadow);
  transition: var(--settings-transition);
  position: relative;
  overflow: visible;
  word-break: break-word;
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.settings-screen-action-card:hover {
  transform: translateY(-10px);
  box-shadow: 0 20px 60px rgba(108, 92, 231, 0.25);
}

.settings-screen-action-card.danger {
  border-left: 6px solid var(--settings-danger);
}

.settings-screen-action-card.warning {
  border-left: 6px solid var(--settings-warning);
}

.settings-screen-action-card.success {
  border-left: 6px solid var(--settings-success);
}

/* Action Card Icon */
.settings-screen-action-icon {
  width: 70px;
  height: 70px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 2rem;
  color: var(--settings-white);
  margin: 0 auto 1.5rem auto;
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.2);
  transition: var(--settings-transition);
}

.settings-screen-action-card.danger .settings-screen-action-icon {
  background: var(--settings-gradient-danger);
}

.settings-screen-action-card.warning .settings-screen-action-icon {
  background: linear-gradient(135deg, var(--settings-warning), var(--settings-warning));
}

.settings-screen-action-card.success .settings-screen-action-icon {
  background: var(--settings-gradient-success);
}

.settings-screen-action-card:hover .settings-screen-action-icon {
  transform: scale(1.1);
  animation: settings-screen-pulse 2s ease-in-out infinite;
}

@keyframes settings-screen-pulse {
  0%, 100% { box-shadow: 0 8px 25px rgba(0, 0, 0, 0.2); }
  50% { box-shadow: 0 12px 35px rgba(0, 0, 0, 0.3); }
}

/* Action Card Content */
.settings-screen-action-title {
  font-size: 1.4rem;
  font-weight: 700;
  color: var(--settings-dark);
  margin: 0 0 1rem 0;
  text-align: center;
}

.settings-screen-action-description {
  color: var(--settings-dark);
  opacity: 0.8;
  font-size: 1rem;
  line-height: 1.6;
  margin: 0 0 1.5rem 0;
  text-align: center;
}

.settings-screen-action-warning {
  background: rgba(214, 48, 49, 0.1);
  color: var(--settings-danger);
  padding: 1rem;
  border-radius: 12px;
  font-size: 0.9rem;
  font-weight: 600;
  margin-bottom: 1.5rem;
  border: 1px solid rgba(214, 48, 49, 0.2);
  text-align: center;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
}

/* Action Button */
.settings-screen-action-btn {
  width: 100%;
  padding: 1rem 2rem;
  border: none;
  border-radius: 12px;
  font-weight: 700;
  font-size: 1rem;
  cursor: pointer;
  transition: var(--settings-transition);
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.75rem;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.settings-screen-action-btn.danger {
  background: var(--settings-gradient-danger);
  color: var(--settings-white);
  box-shadow: 0 6px 20px rgba(214, 48, 49, 0.3);
}

.settings-screen-action-btn.danger:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 30px rgba(214, 48, 49, 0.4);
}

.settings-screen-action-btn.warning {
  background: linear-gradient(135deg, var(--settings-warning), #fd79a8);
  color: var(--settings-white);
  box-shadow: 0 6px 20px rgba(225, 112, 85, 0.3);
}

.settings-screen-action-btn.warning:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 30px rgba(225, 112, 85, 0.4);
}

.settings-screen-action-btn.success {
  background: var(--settings-gradient-success);
  color: var(--settings-white);
  box-shadow: 0 6px 20px rgba(0, 184, 148, 0.3);
}

.settings-screen-action-btn.success:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 30px rgba(0, 184, 148, 0.4);
}

.settings-screen-action-btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  transform: none !important;
}

/* Coming Soon Section */
.settings-screen-coming-soon {
  margin-top: 4rem;
}

.settings-screen-coming-soon-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 1.5rem;
}

.settings-screen-coming-soon-card {
  background: rgba(255, 255, 255, 0.1);
  border-radius: var(--settings-border-radius);
  padding: 2rem;
  color: var(--settings-white);
  text-align: center;
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  transition: var(--settings-transition);
}

.settings-screen-coming-soon-card:hover {
  background: rgba(255, 255, 255, 0.15);
  transform: translateY(-5px);
}

.settings-screen-coming-soon-icon {
  width: 60px;
  height: 60px;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.2);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.5rem;
  margin: 0 auto 1rem auto;
  transition: var(--settings-transition);
}

.settings-screen-coming-soon-card:hover .settings-screen-coming-soon-icon {
  background: rgba(255, 255, 255, 0.3);
  transform: scale(1.1);
}

.settings-screen-coming-soon-title {
  font-size: 1.2rem;
  font-weight: 600;
  margin: 0 0 0.75rem 0;
}

.settings-screen-coming-soon-description {
  font-size: 0.9rem;
  opacity: 0.8;
  margin: 0;
  line-height: 1.5;
}

.settings-screen-coming-soon-badge {
  display: inline-block;
  background: rgba(255, 255, 255, 0.2);
  color: var(--settings-white);
  padding: 0.25rem 0.75rem;
  border-radius: 15px;
  font-size: 0.8rem;
  font-weight: 600;
  margin-top: 1rem;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

/* Loading State */
.settings-screen-loading {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 4rem;
  color: var(--settings-white);
}

.settings-screen-loading-icon {
  font-size: 3rem;
  margin-bottom: 1rem;
  animation: settings-screen-spin 2s linear infinite;
}

.settings-screen-loading-text {
  font-size: 1.3rem;
  font-weight: 500;
}

@keyframes settings-screen-spin {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

/* Alert Messages */
.settings-screen-alert {
  padding: 1.5rem;
  border-radius: var(--settings-border-radius);
  margin-bottom: 2rem;
  font-weight: 600;
  display: flex;
  align-items: center;
  gap: 1rem;
  backdrop-filter: blur(10px);
}

.settings-screen-alert.success {
  background: rgba(0, 184, 148, 0.2);
  color: var(--settings-white);
  border: 2px solid rgba(0, 184, 148, 0.4);
}

.settings-screen-alert.error {
  background: rgba(214, 48, 49, 0.2);
  color: var(--settings-white);
  border: 2px solid rgba(214, 48, 49, 0.4);
}

.settings-screen-alert.warning {
  background: rgba(225, 112, 85, 0.2);
  color: var(--settings-white);
  border: 2px solid rgba(225, 112, 85, 0.4);
}

/* Responsive Design */
@media (max-width: 1200px) {
  .settings-screen-actions-grid {
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  }
  
  .settings-screen-coming-soon-grid {
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  }
}

@media (max-width: 768px) {
  .settings-screen-header {
    padding: 2rem 1.5rem;
  }
  
  .settings-screen-title {
    font-size: 2.5rem;
  }
  
  .settings-screen-content {
    padding: 1.5rem;
  }
  
  .settings-screen-actions-grid {
    grid-template-columns: 1fr;
    gap: 1.5rem;
  }
  
  .settings-screen-coming-soon-grid {
    grid-template-columns: repeat(auto-fit, minmax(220px, 1fr));
  }
}

@media (max-width: 480px) {
  .settings-screen-header {
    padding: 1.5rem 1rem;
  }
  
  .settings-screen-title {
    font-size: 2rem;
    flex-direction: column;
    gap: 0.5rem;
  }
  
  .settings-screen-content {
    padding: 1rem;
  }
  
  .settings-screen-action-card {
    padding: 2rem 1.5rem;
  }
  
  .settings-screen-coming-soon-grid {
    grid-template-columns: 1fr;
  }
  
  .settings-screen-warning {
    padding: 1rem;
    font-size: 1rem;
  }
}




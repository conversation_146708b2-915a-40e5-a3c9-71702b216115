/* Manager Dashboard Styles - Clean Version */

/* تحسينات عامة للتخطيط */
.manager-dashboard {
  display: flex;
  flex-direction: column;
  min-height: 100vh;
  background: var(--light-bg, #f8f9fa);
  font-family: 'Se<PERSON>e UI', Tahoma, Geneva, Verdana, sans-serif;
  direction: rtl;
  overflow-x: hidden;
}

/* Header */
.manager-header {
  background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
  color: white;
  padding: 1rem 2rem;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  position: sticky;
  top: 0;
  z-index: 1000;
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.header-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  max-width: 1400px;
  margin: 0 auto;
}

.header-left {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.sidebar-toggle {
  background: none;
  border: none;
  color: white;
  font-size: 1.5rem;
  cursor: pointer;
  padding: 0.5rem;
  border-radius: 8px;
  transition: background-color 0.3s ease;
}

.sidebar-toggle:hover {
  background-color: rgba(255, 255, 255, 0.1);
}

.logout-button {
  background: none;
  border: 2px solid rgba(255, 255, 255, 0.2);
  color: white;
  padding: 0.5rem 1rem;
  border-radius: 8px;
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  transition: all 0.3s ease;
}

.logout-button:hover {
  background-color: rgba(255, 255, 255, 0.1);
  border-color: rgba(255, 255, 255, 0.3);
}

/* Sidebar */
.sidebar-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  z-index: 998;
  opacity: 0;
  visibility: hidden;
  transition: all 0.3s ease;
}

.sidebar-overlay.active {
  opacity: 1;
  visibility: visible;
}

.manager-sidebar {
  position: fixed;
  top: 80px;
  right: 0;
  width: 280px;
  background: rgba(255, 255, 255, 0.95);
  -webkit-backdrop-filter: blur(2px);
  backdrop-filter: blur(2px);
  border-left: 1px solid #e2e8f0;
  box-shadow: 2px 0 10px rgba(0, 0, 0, 0.1);
  height: calc(100vh - 80px);
  z-index: 500;
  overflow: hidden;
  transition: all 0.3s ease;
  /* Webkit scrollbar styling for better browser support */
}

.manager-sidebar::-webkit-scrollbar {
  width: 6px;
}

.manager-sidebar::-webkit-scrollbar-thumb {
  background: #cbd5e0;
  border-radius: 3px;
}

.manager-sidebar::-webkit-scrollbar-track {
  background: #f7fafc;
}

/* Main Content */
.main-content {
  flex: 1;
  background: #f8f9fa;
  transition: margin-right 0.3s ease;
  max-width: 100%;
  overflow-x: hidden;
}

.main-content.sidebar-open {
  margin-right: 280px;
}

/* ============================== */
/* تنسيقات العناصر الديناميكية */
/* ============================== */

/* أشرطة الكمية */
.quantity-bar {
  background: #ecf0f1;
  border-radius: 6px;
  height: 8px;
  overflow: hidden;
  position: relative;
}

.quantity-bar [data-width] {
  height: 100%;
  background: linear-gradient(90deg, #3498db, #2980b9);
  border-radius: 6px;
  transition: width 0.6s ease;
  width: 0%; /* القيمة الافتراضية */
}

.quantity-fill[data-width] {
  height: 100%;
  border-radius: 6px;
  transition: width 0.6s ease;
  width: 0%; /* القيمة الافتراضية */
}

/* رؤوس الفئات */
.category-header[data-color] {
  background-color: #8B4513; /* اللون الافتراضي */
  color: white;
  padding: 1.5rem;
  border-radius: 12px 12px 0 0;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

/* رموز الألوان */
.color-code[data-color] {
  background-color: #8B4513; /* اللون الافتراضي */
  color: white;
  padding: 0.2rem 0.5rem;
  border-radius: 4px;
  font-size: 0.8rem;
  font-family: monospace;
}

.color-preview[data-color] {
  background-color: #8B4513; /* اللون الافتراضي */
  width: 30px;
  height: 30px;
  border-radius: 50%;
  border: 2px solid #ecf0f1;
  display: inline-block;
  vertical-align: middle;
}

/* Navigation Styles */
.nav-item {
  width: 100%;
  padding: 1rem 1.5rem;
  background: none;
  border: none;
  text-align: right;
  cursor: pointer;
  transition: all 0.3s ease;
  color: #4a5568;
  display: flex;
  align-items: center;
  justify-content: flex-end;
  gap: 0.75rem;
  font-size: 1rem;
}

.nav-item:hover {
  background-color: #f7fafc;
  color: #2d3748;
}

.nav-item.active {
  background-color: #ebf8ff;
  color: #3182ce;
  border-right: 4px solid #3182ce;
}

.nav-item i {
  font-size: 1.1rem;
  min-width: 20px;
  text-align: center;
}

/* Responsive Design */
@media (max-width: 768px) {
  .manager-sidebar {
    width: 100%;
    transform: translateX(100%);
  }
  
  .manager-sidebar.active {
    transform: translateX(0);
  }
  
  .main-content.sidebar-open {
    margin-right: 0;
  }
  
  .header-content {
    padding: 0 1rem;
  }
}

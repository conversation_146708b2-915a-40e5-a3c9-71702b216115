// Quick System Test for Enhanced Coffee Shop Management System
console.log('🧪 Starting Enhanced System Test...');

// Test frontend connection
async function testFrontend() {
    try {
        console.log('🌐 Testing frontend connection...');
        const response = await fetch('http://localhost:5190');
        if (response.ok) {
            console.log('✅ Frontend is running on http://localhost:5190');
            return true;
        } else {
            console.log('❌ Frontend responded with status:', response.status);
            return false;
        }
    } catch (error) {
        console.log('❌ Frontend connection failed:', error.message);
        return false;
    }
}

// Test backend health via production
async function testBackend() {
    try {
        console.log('🔧 Testing backend connection via production...');
        const response = await fetch('https://deshacoffee-production.up.railway.app/api/v1/health');
        if (response.ok) {
            const data = await response.json();
            console.log('✅ Backend is healthy:', data);
            return true;
        } else {
            console.log('❌ Backend health check failed with status:', response.status);
            return false;
        }
    } catch (error) {
        console.log('❌ Backend connection failed:', error.message);
        return false;
    }
}

// Run all tests
async function runTests() {
    console.log('🚀 Enhanced Coffee Shop Management System - Quick Test');
    console.log('=' .repeat(60));
    
    const frontendOk = await testFrontend();
    const backendOk = await testBackend();
    
    console.log('\n📊 Test Results:');
    console.log(`Frontend Server: ${frontendOk ? '✅ RUNNING' : '❌ FAILED'}`);
    console.log(`Backend Server: ${backendOk ? '✅ RUNNING' : '❌ FAILED'}`);
    
    if (frontendOk && backendOk) {
        console.log('\n🎉 System is ready for testing!');
        console.log('🌐 Open http://localhost:5190 in your browser');
        console.log('📋 Follow the COMPREHENSIVE_FEATURE_TEST_PLAN.md for detailed testing');
        console.log('\n🔍 Key Features to Test:');
        console.log('  1. Connection Status Display (top-right corner)');
        console.log('  2. Enhanced Order Details Modal (Orders → View Details)');
        console.log('  3. New Discount Requests Screen (Sidebar → طلبات الخصم)');
        console.log('  4. Modal Functions (ESC key, click outside, X button)');
        console.log('  5. Responsive Design on different screen sizes');
    } else {
        console.log('\n❌ System has issues that need to be resolved');
    }
}

// Execute tests
if (typeof window === 'undefined') {
    // Node.js environment
    const fetch = require('node-fetch');
    runTests();
} else {
    // Browser environment
    runTests();
}

const mongoose = require('mongoose');
require('./backend/config/database');

async function checkOrdersStructure() {
  try {
    console.log('🔍 البحث عن الطلبات في قاعدة البيانات...');
    
    const Order = require('./backend/models/Order');
    const orders = await Order.find({}).limit(5).lean();
    
    console.log(`📊 تم العثور على ${orders.length} طلبات`);
    
    orders.forEach((order, index) => {
      console.log(`\n--- الطلب ${index + 1} ---`);
      console.log('- ID:', order._id);
      console.log('- رقم الطلب:', order.orderNumber);
      console.log('- table (ObjectId):', order.table);
      console.log('- tableNumber:', order.tableNumber);
      console.log('- pricing:', order.pricing);
      console.log('- totalPrice:', order.totalPrice);
      console.log('- الحالة:', order.status);
      console.log('- المبلغ الإجمالي:', order.totalAmount);
      console.log('- تاريخ الإنشاء:', order.createdAt);
    });
    
    console.log('\n🔍 البحث عن الطاولات...');
    const Table = require('./backend/models/Table');
    const tables = await Table.find({}).limit(3).lean();
    
    console.log(`📊 تم العثور على ${tables.length} طاولة`);
    tables.forEach((table, index) => {
      console.log(`\n--- الطاولة ${index + 1} ---`);
      console.log('- ID:', table._id);
      console.log('- رقم الطاولة:', table.number);
      console.log('- الحالة:', table.status);
      console.log('- مشغولة:', table.isOccupied);
      console.log('- النادل المخصص:', table.assignedWaiter);
    });
    
    process.exit(0);
  } catch (error) {
    console.error('❌ خطأ في فحص البيانات:', error);
    process.exit(1);
  }
}

checkOrdersStructure();

// تحليل سريع للتباين في المبيعات
const { MongoClient } = require('mongodb');
require('dotenv').config();

async function quickSalesAnalysis() {
    const client = new MongoClient(process.env.MONGODB_URI);
    
    try {
        await client.connect();
        const db = client.db('coffee_shop');
        
        console.log('🔍 تحليل سريع للمبيعات...');
        
        // إجمالي المبيعات
        const [totalResult] = await db.collection('orders').aggregate([
            { $match: { status: 'completed' } },
            { $group: { _id: null, total: { $sum: '$total' }, count: { $sum: 1 } } }
        ]).toArray();
        
        console.log(`💰 إجمالي المبيعات: ${totalResult?.total || 0} جنيه`);
        console.log(`📦 إجمالي الطلبات: ${totalResult?.count || 0}`);
        
        // مبيعات النُدُل
        const waiterSales = await db.collection('orders').aggregate([
            { $match: { status: 'completed', waiter: { $exists: true, $ne: null, $ne: '' } } },
            { $group: { _id: '$waiter', sales: { $sum: '$total' }, orders: { $sum: 1 } } },
            { $sort: { sales: -1 } }
        ]).toArray();
        
        const totalWaiterSales = waiterSales.reduce((sum, w) => sum + w.sales, 0);
        const totalWaiterOrders = waiterSales.reduce((sum, w) => sum + w.orders, 0);
        
        console.log(`👥 مجموع مبيعات النُدُل: ${totalWaiterSales} جنيه`);
        console.log(`📋 مجموع طلبات النُدُل: ${totalWaiterOrders}`);
        
        // التباين
        const salesDiscrepancy = (totalResult?.total || 0) - totalWaiterSales;
        const ordersDiscrepancy = (totalResult?.count || 0) - totalWaiterOrders;
        
        console.log(`\n🚨 التباين في المبيعات: ${salesDiscrepancy} جنيه`);
        console.log(`📊 التباين في الطلبات: ${ordersDiscrepancy}`);
        
        // الطلبات بدون نادل
        const orphanedOrders = await db.collection('orders').countDocuments({
            status: 'completed',
            $or: [{ waiter: { $exists: false } }, { waiter: null }, { waiter: '' }]
        });
        
        console.log(`👻 طلبات بدون نادل: ${orphanedOrders}`);
        
        // تفصيل النُدُل
        console.log('\n👥 تفصيل مبيعات النُدُل:');
        waiterSales.forEach(w => {
            console.log(`  ${w._id}: ${w.sales} جنيه (${w.orders} طلب)`);
        });
        
        if (Math.abs(salesDiscrepancy) > 1) {
            console.log('\n⚠️  يوجد تباين كبير يحتاج إصلاح!');
            return true; // يحتاج إصلاح
        } else {
            console.log('\n✅ التباين مقبول');
            return false; // لا يحتاج إصلاح
        }
        
    } catch (error) {
        console.error('❌ خطأ:', error.message);
        return false;
    } finally {
        await client.close();
    }
}

quickSalesAnalysis().then(needsFix => {
    if (needsFix) {
        console.log('\n🔧 تشغيل الإصلاح الآن...');
        // يمكن تشغيل الإصلاح هنا
    }
}).catch(console.error);

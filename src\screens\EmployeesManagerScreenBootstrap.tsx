import React, { useState, useEffect, useMemo } from 'react';
import { authenticatedGet, authenticatedPost, authenticatedPut, authenticatedDelete } from '../utils/apiHelpers';
import { useToast } from '../hooks/useToast';
import socket from '../socket';
import '../styles/screens/EmployeesScreenIsolated.css';

interface Employee {
  _id: string;
  username: string;
  name: string;
  email?: string;
  phone?: string;
  role: 'waiter' | 'chef' | 'manager';
  status: 'active' | 'inactive';
  isActive: boolean;
  currentShift?: any;
}

interface EmployeesManagerScreenProps {
  employees: Employee[];
  onEmployeesUpdate: (employees: Employee[]) => void;
  loading: boolean;
}

const EmployeesManagerScreenBootstrap: React.FC<EmployeesManagerScreenProps> = ({
  employees,
  onEmployeesUpdate,
  loading
}) => {
  const [employeesScreenSearchTerm, setEmployeesScreenSearchTerm] = useState('');
  const [employeesScreenRoleFilter, setEmployeesScreenRoleFilter] = useState<'all' | 'waiter' | 'chef' | 'manager'>('all');
  const [employeesScreenStatusFilter, setEmployeesScreenStatusFilter] = useState<'all' | 'active' | 'inactive'>('all');
  const [employeesScreenShowModal, setEmployeesScreenShowModal] = useState(false);
  const [employeesScreenEditingEmployee, setEmployeesScreenEditingEmployee] = useState<Employee | null>(null);
  const [sortConfig, setSortConfig] = useState<{key: string, direction: 'asc' | 'desc'} | null>(null);
  const [viewMode, setViewMode] = useState<'grid' | 'table'>('grid');
  const [employeesScreenFormData, setEmployeesScreenFormData] = useState({
    username: '',
    password: '',
    name: '',
    email: '',
    phone: '',
    role: 'waiter' as 'waiter' | 'chef' | 'manager'
  });

  const toast = useToast();

  // Socket.IO event listeners for real-time updates
  useEffect(() => {
    const handleEmployeeUpdate = () => {
      console.log('📡 Real-time employee update received');
      // Trigger parent component to refresh employees
      window.location.reload();
    };

    // Add event listeners
    socket.on('employeeAdded', handleEmployeeUpdate);
    socket.on('employeeUpdated', handleEmployeeUpdate);
    socket.on('employeeDeleted', handleEmployeeUpdate);
    socket.on('employeeStatusChanged', handleEmployeeUpdate);

    // Cleanup function
    return () => {
      socket.off('employeeAdded', handleEmployeeUpdate);
      socket.off('employeeUpdated', handleEmployeeUpdate);
      socket.off('employeeDeleted', handleEmployeeUpdate);
      socket.off('employeeStatusChanged', handleEmployeeUpdate);
    };
  }, []);

  const handleSort = (key: string) => {
    let direction: 'asc' | 'desc' = 'asc';
    if (sortConfig && sortConfig.key === key && sortConfig.direction === 'asc') {
      direction = 'desc';
    }
    setSortConfig({ key, direction });
  };

  // Filter and sort employees
  const filteredAndSortedEmployees = useMemo(() => {
    let filtered = employees.filter(employee => {
      const matchesSearch = employee.name?.toLowerCase().includes(employeesScreenSearchTerm.toLowerCase()) ||
                           employee.username?.toLowerCase().includes(employeesScreenSearchTerm.toLowerCase());
      const matchesRole = employeesScreenRoleFilter === 'all' || employee.role === employeesScreenRoleFilter;
      const matchesStatus = employeesScreenStatusFilter === 'all' || 
                           (employeesScreenStatusFilter === 'active' && employee.isActive) ||
                           (employeesScreenStatusFilter === 'inactive' && !employee.isActive);
      
      return matchesSearch && matchesRole && matchesStatus;
    });

    if (sortConfig) {
      filtered.sort((a, b) => {
        let aValue: any = a[sortConfig.key as keyof Employee];
        let bValue: any = b[sortConfig.key as keyof Employee];

        if (sortConfig.key === 'status') {
          aValue = a.isActive ? 'active' : 'inactive';
          bValue = b.isActive ? 'active' : 'inactive';
        }

        if (aValue < bValue) {
          return sortConfig.direction === 'asc' ? -1 : 1;
        }
        if (aValue > bValue) {
          return sortConfig.direction === 'asc' ? 1 : -1;
        }
        return 0;
      });
    }

    return filtered;
  }, [employees, employeesScreenSearchTerm, employeesScreenRoleFilter, employeesScreenStatusFilter, sortConfig]);

  const handleEmployeesScreenSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    try {
      if (employeesScreenEditingEmployee) {
        const response = await authenticatedPut(`/api/v1/employees/${employeesScreenEditingEmployee._id}`, {
          ...employeesScreenFormData,
          password: employeesScreenFormData.password || undefined
        });
        
        if (response.success) {
          toast.showSuccess('تم تحديث بيانات الموظف بنجاح');
          handleEmployeesScreenCloseModal();
          await refreshEmployees();
        } else {
          toast.showError(response.message || 'فشل في تحديث بيانات الموظف');
        }
      } else {
        const response = await authenticatedPost('/api/v1/employees', employeesScreenFormData);
        
        if (response.success) {
          toast.showSuccess('تم إضافة الموظف بنجاح');
          handleEmployeesScreenCloseModal();
          await refreshEmployees();
        } else {
          toast.showError(response.message || 'فشل في إضافة الموظف');
        }
      }
    } catch (error) {
      console.error('Error submitting employee:', error);
      toast.showError('حدث خطأ أثناء معالجة البيانات');
    }
  };

  const refreshEmployees = async () => {
    try {
      const response = await authenticatedGet('/api/v1/employees');
      if (response.success && response.data) {
        onEmployeesUpdate(response.data);
      }
    } catch (error) {
      console.error('Error refreshing employees:', error);
    }
  };

  const handleEmployeesScreenDeleteEmployee = async (employeeId: string) => {
    if (!window.confirm('هل أنت متأكد من حذف هذا الموظف؟')) return;
    
    try {
      const response = await authenticatedDelete(`/api/v1/employees/${employeeId}`);
      
      if (response.success) {
        toast.showSuccess('تم حذف الموظف بنجاح');
        await refreshEmployees();
      } else {
        toast.showError(response.message || 'فشل في حذف الموظف');
      }
    } catch (error) {
      console.error('Error deleting employee:', error);
      toast.showError('حدث خطأ أثناء حذف الموظف');
    }
  };

  const handleEmployeesScreenToggleStatus = async (employee: Employee) => {
    try {
      const newStatus = !employee.isActive;
      const response = await authenticatedPut(`/api/v1/employees/${employee._id}`, {
        isActive: newStatus
      });
      
      if (response.success) {
        toast.showSuccess(`تم ${newStatus ? 'تفعيل' : 'إلغاء تفعيل'} الموظف بنجاح`);
        await refreshEmployees();
      } else {
        toast.showError(response.message || 'فشل في تغيير حالة الموظف');
      }
    } catch (error) {
      console.error('Error toggling employee status:', error);
      toast.showError('حدث خطأ أثناء تغيير حالة الموظف');
    }
  };

  const handleEmployeesScreenEditEmployee = (employee: Employee) => {
    setEmployeesScreenEditingEmployee(employee);
    setEmployeesScreenFormData({
      username: employee.username,
      password: '',
      name: employee.name,
      email: employee.email || '',
      phone: employee.phone || '',
      role: employee.role
    });
    setEmployeesScreenShowModal(true);
  };

  const handleEmployeesScreenAddEmployee = () => {
    setEmployeesScreenEditingEmployee(null);
    setEmployeesScreenFormData({
      username: '',
      password: '',
      name: '',
      email: '',
      phone: '',
      role: 'waiter'
    });
    setEmployeesScreenShowModal(true);
  };

  const handleEmployeesScreenCloseModal = () => {
    setEmployeesScreenShowModal(false);
    setEmployeesScreenEditingEmployee(null);
    setEmployeesScreenFormData({
      username: '',
      password: '',
      name: '',
      email: '',
      phone: '',
      role: 'waiter'
    });
  };

  const getRoleText = (role: string) => {
    switch (role) {
      case 'waiter': return 'نادل';
      case 'chef': return 'طباخ';
      case 'manager': return 'مدير';
      default: return role;
    }
  };

  const getRoleColor = (role: string) => {
    switch (role) {
      case 'waiter': return 'primary';
      case 'chef': return 'warning';
      case 'manager': return 'danger';
      default: return 'secondary';
    }
  };

  const getRoleIcon = (role: string) => {
    switch (role) {
      case 'waiter': return 'fa-user-tie';
      case 'chef': return 'fa-utensils';
      case 'manager': return 'fa-user-cog';
      default: return 'fa-user';
    }
  };

  // Statistics
  const stats = useMemo(() => {
    return {
      total: employees.length,
      active: employees.filter(emp => emp.isActive).length,
      inactive: employees.filter(emp => !emp.isActive).length,
      waiters: employees.filter(emp => emp.role === 'waiter').length,
      chefs: employees.filter(emp => emp.role === 'chef').length,
      managers: employees.filter(emp => emp.role === 'manager').length
    };
  }, [employees]);

  return (
    <div className="container-fluid p-4" dir="rtl">
      {/* Header Section */}
      <div className="row mb-4">
        <div className="col-12">
          <div className="card border-0 shadow-sm bg-gradient">
            <div className="card-body p-4">
              <div className="row align-items-center">
                <div className="col-lg-6">
                  <h2 className="h3 fw-bold text-dark mb-2">إدارة الموظفين</h2>
                  <p className="text-muted mb-0">إدارة حسابات ومعلومات الموظفين</p>
                </div>
                <div className="col-lg-6">
                  <div className="d-flex justify-content-lg-end justify-content-start">
                    <button
                      className="btn btn-primary btn-lg"
                      onClick={handleEmployeesScreenAddEmployee}
                    >
                      <i className="fas fa-plus me-2"></i>
                      إضافة موظف جديد
                    </button>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Statistics Cards */}
      <div className="row g-4 mb-4">
        <div className="col-xl-2 col-lg-4 col-md-6">
          <div className="card border-0 shadow-sm h-100 bg-primary bg-gradient text-white">
            <div className="card-body text-center">
              <i className="fas fa-users fa-2x mb-2"></i>
              <h4 className="fw-bold">{stats.total}</h4>
              <p className="mb-0 small">إجمالي الموظفين</p>
            </div>
          </div>
        </div>
        
        <div className="col-xl-2 col-lg-4 col-md-6">
          <div className="card border-0 shadow-sm h-100 bg-success bg-gradient text-white">
            <div className="card-body text-center">
              <i className="fas fa-user-check fa-2x mb-2"></i>
              <h4 className="fw-bold">{stats.active}</h4>
              <p className="mb-0 small">موظفين نشطين</p>
            </div>
          </div>
        </div>
        
        <div className="col-xl-2 col-lg-4 col-md-6">
          <div className="card border-0 shadow-sm h-100 bg-secondary bg-gradient text-white">
            <div className="card-body text-center">
              <i className="fas fa-user-times fa-2x mb-2"></i>
              <h4 className="fw-bold">{stats.inactive}</h4>
              <p className="mb-0 small">موظفين غير نشطين</p>
            </div>
          </div>
        </div>
        
        <div className="col-xl-2 col-lg-4 col-md-6">
          <div className="card border-0 shadow-sm h-100 bg-info bg-gradient text-white">
            <div className="card-body text-center">
              <i className="fas fa-user-tie fa-2x mb-2"></i>
              <h4 className="fw-bold">{stats.waiters}</h4>
              <p className="mb-0 small">نُدُل</p>
            </div>
          </div>
        </div>

        <div className="col-xl-2 col-lg-4 col-md-6">
          <div className="card border-0 shadow-sm h-100 bg-warning bg-gradient text-white">
            <div className="card-body text-center">
              <i className="fas fa-utensils fa-2x mb-2"></i>
              <h4 className="fw-bold">{stats.chefs}</h4>
              <p className="mb-0 small">طباخين</p>
            </div>
          </div>
        </div>

        <div className="col-xl-2 col-lg-4 col-md-6">
          <div className="card border-0 shadow-sm h-100 bg-danger bg-gradient text-white">
            <div className="card-body text-center">
              <i className="fas fa-user-cog fa-2x mb-2"></i>
              <h4 className="fw-bold">{stats.managers}</h4>
              <p className="mb-0 small">مديرين</p>
            </div>
          </div>
        </div>
      </div>

      {/* Filters and Controls */}
      <div className="row mb-4">
        <div className="col-12">
          <div className="card border-0 shadow-sm">
            <div className="card-body p-4">
              <div className="row g-3 align-items-center">
                {/* Search */}
                <div className="col-lg-4">
                  <div className="position-relative">
                    <input
                      type="text"
                      className="form-control form-control-lg pe-5"
                      placeholder="البحث عن موظف..."
                      value={employeesScreenSearchTerm}
                      onChange={(e) => setEmployeesScreenSearchTerm(e.target.value)}
                    />
                    <i className="fas fa-search position-absolute top-50 end-0 translate-middle-y me-3 text-muted"></i>
                    {employeesScreenSearchTerm && (
                      <button
                        className="btn btn-link position-absolute top-50 start-0 translate-middle-y text-danger"
                        onClick={() => setEmployeesScreenSearchTerm('')}
                        title="مسح البحث"
                      >
                        <i className="fas fa-times"></i>
                      </button>
                    )}
                  </div>
                </div>

                {/* Role Filter */}
                <div className="col-lg-2 col-md-4">
                  <select
                    className="form-select form-select-lg"
                    value={employeesScreenRoleFilter}
                    onChange={(e) => setEmployeesScreenRoleFilter(e.target.value as any)}
                    title="تصفية حسب الوظيفة"
                    aria-label="تصفية حسب الوظيفة"
                  >
                    <option value="all">جميع الوظائف</option>
                    <option value="waiter">نُدُل</option>
                    <option value="chef">طباخين</option>
                    <option value="manager">مديرين</option>
                  </select>
                </div>

                {/* Status Filter */}
                <div className="col-lg-2 col-md-4">
                  <select
                    className="form-select form-select-lg"
                    value={employeesScreenStatusFilter}
                    onChange={(e) => setEmployeesScreenStatusFilter(e.target.value as any)}
                    title="تصفية حسب الحالة"
                    aria-label="تصفية حسب الحالة"
                  >
                    <option value="all">جميع الحالات</option>
                    <option value="active">نشطين</option>
                    <option value="inactive">غير نشطين</option>
                  </select>
                </div>

                {/* View Mode */}
                <div className="col-lg-2 col-md-4">
                  <div className="btn-group w-100" role="group">
                    <button
                      className={`btn ${viewMode === 'grid' ? 'btn-primary' : 'btn-outline-primary'}`}
                      onClick={() => setViewMode('grid')}
                      title="عرض شبكي"
                    >
                      <i className="fas fa-th"></i>
                    </button>
                    <button
                      className={`btn ${viewMode === 'table' ? 'btn-primary' : 'btn-outline-primary'}`}
                      onClick={() => setViewMode('table')}
                      title="عرض جدول"
                    >
                      <i className="fas fa-list"></i>
                    </button>
                  </div>
                </div>

                {/* Actions */}
                <div className="col-lg-2">
                  <button
                    className="btn btn-outline-info w-100"
                    onClick={() => window.location.reload()}
                    title="تحديث البيانات"
                  >
                    <i className="fas fa-refresh me-1"></i>
                    تحديث
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Employees Content */}
      <div className="row">
        <div className="col-12">
          {loading ? (
            <div className="text-center py-5">
              <div className="spinner-border text-primary" role="status">
                <span className="visually-hidden">جاري التحميل...</span>
              </div>
              <p className="mt-3 text-muted">جاري تحميل بيانات الموظفين...</p>
            </div>
          ) : filteredAndSortedEmployees.length === 0 ? (
            <div className="text-center py-5">
              <i className="fas fa-users fa-4x text-muted mb-3"></i>
              <h5 className="text-muted">لا يوجد موظفين</h5>
              <p className="text-muted">لا يوجد موظفين مطابقين لمعايير البحث والفلترة الحالية</p>
              <button
                className="btn btn-primary"
                onClick={handleEmployeesScreenAddEmployee}
              >
                <i className="fas fa-plus me-2"></i>
                إضافة موظف جديد
              </button>
            </div>
          ) : (
            <>
              {viewMode === 'grid' ? (
                <div className="row g-4">
                  {filteredAndSortedEmployees.map(employee => (
                    <div key={employee._id} className="col-xl-4 col-lg-6 col-md-12">
                      <div className={`card border-0 shadow-sm h-100 ${employee.isActive ? 'border-start border-success border-4' : 'border-start border-secondary border-4'}`}>
                        <div className="card-header bg-transparent border-0 pb-0">
                          <div className="d-flex justify-content-between align-items-start">
                            <div className="d-flex align-items-center">
                              <i className={`fas ${getRoleIcon(employee.role)} text-${getRoleColor(employee.role)} me-2 fa-lg`}></i>
                              <div>
                                <h6 className="card-title mb-0">{employee.name}</h6>
                                <small className="text-muted">@{employee.username}</small>
                              </div>
                            </div>
                            <div className="d-flex align-items-center gap-2">
                              <span className={`badge bg-${getRoleColor(employee.role)}`}>
                                {getRoleText(employee.role)}
                              </span>
                              <span className={`badge ${employee.isActive ? 'bg-success' : 'bg-secondary'}`}>
                                <i className={`fas ${employee.isActive ? 'fa-check' : 'fa-times'} me-1`}></i>
                                {employee.isActive ? 'نشط' : 'غير نشط'}
                              </span>
                            </div>
                          </div>
                        </div>

                        <div className="card-body">
                          <div className="row g-2">
                            {employee.email && (
                              <div className="col-12">
                                <div className="d-flex align-items-center">
                                  <i className="fas fa-envelope text-muted me-2"></i>
                                  <small className="text-truncate">{employee.email}</small>
                                </div>
                              </div>
                            )}
                            {employee.phone && (
                              <div className="col-12">
                                <div className="d-flex align-items-center">
                                  <i className="fas fa-phone text-muted me-2"></i>
                                  <small>{employee.phone}</small>
                                </div>
                              </div>
                            )}
                            {employee.currentShift && (
                              <div className="col-12">
                                <div className="d-flex align-items-center">
                                  <i className="fas fa-clock text-muted me-2"></i>
                                  <small className="text-success">في نوبة عمل</small>
                                </div>
                              </div>
                            )}
                          </div>
                        </div>

                        <div className="card-footer bg-transparent border-0">
                          <div className="btn-group w-100" role="group">
                            <button
                              className="btn btn-outline-primary btn-sm"
                              onClick={() => handleEmployeesScreenEditEmployee(employee)}
                              title="تعديل البيانات"
                            >
                              <i className="fas fa-edit"></i>
                            </button>
                            
                            <button
                              className={`btn btn-sm ${employee.isActive ? 'btn-outline-warning' : 'btn-outline-success'}`}
                              onClick={() => handleEmployeesScreenToggleStatus(employee)}
                              title={employee.isActive ? 'إلغاء التفعيل' : 'تفعيل'}
                            >
                              <i className={`fas ${employee.isActive ? 'fa-pause' : 'fa-play'}`}></i>
                            </button>
                            
                            <button
                              className="btn btn-outline-danger btn-sm"
                              onClick={() => handleEmployeesScreenDeleteEmployee(employee._id)}
                              title="حذف الموظف"
                            >
                              <i className="fas fa-trash"></i>
                            </button>
                          </div>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              ) : (
                <div className="card border-0 shadow-sm">
                  <div className="card-header bg-transparent">
                    <h5 className="card-title mb-0">قائمة الموظفين</h5>
                  </div>
                  <div className="card-body p-0">
                    <div className="table-responsive">
                      <table className="table table-hover mb-0">
                        <thead className="table-light">
                          <tr>
                            <th scope="col" className="text-center">
                              <button
                                className="btn btn-link text-decoration-none p-0"
                                onClick={() => handleSort('name')}
                              >
                                الاسم
                                {sortConfig?.key === 'name' && (
                                  <i className={`fas fa-sort-${sortConfig.direction === 'asc' ? 'up' : 'down'} ms-1`}></i>
                                )}
                              </button>
                            </th>
                            <th scope="col" className="text-center">اسم المستخدم</th>
                            <th scope="col" className="text-center">
                              <button
                                className="btn btn-link text-decoration-none p-0"
                                onClick={() => handleSort('role')}
                              >
                                الوظيفة
                                {sortConfig?.key === 'role' && (
                                  <i className={`fas fa-sort-${sortConfig.direction === 'asc' ? 'up' : 'down'} ms-1`}></i>
                                )}
                              </button>
                            </th>
                            <th scope="col" className="text-center">البريد الإلكتروني</th>
                            <th scope="col" className="text-center">الهاتف</th>
                            <th scope="col" className="text-center">
                              <button
                                className="btn btn-link text-decoration-none p-0"
                                onClick={() => handleSort('status')}
                              >
                                الحالة
                                {sortConfig?.key === 'status' && (
                                  <i className={`fas fa-sort-${sortConfig.direction === 'asc' ? 'up' : 'down'} ms-1`}></i>
                                )}
                              </button>
                            </th>
                            <th scope="col" className="text-center">الإجراءات</th>
                          </tr>
                        </thead>
                        <tbody>
                          {filteredAndSortedEmployees.map(employee => (
                            <tr key={employee._id}>
                              <td className="text-center">
                                <div className="d-flex align-items-center justify-content-center">
                                  <i className={`fas ${getRoleIcon(employee.role)} text-${getRoleColor(employee.role)} me-2`}></i>
                                  <span className="fw-bold">{employee.name}</span>
                                </div>
                              </td>
                              <td className="text-center">
                                <span className="text-muted">@{employee.username}</span>
                              </td>
                              <td className="text-center">
                                <span className={`badge bg-${getRoleColor(employee.role)}`}>
                                  {getRoleText(employee.role)}
                                </span>
                              </td>
                              <td className="text-center">
                                {employee.email ? (
                                  <a href={`mailto:${employee.email}`} className="text-decoration-none">
                                    {employee.email}
                                  </a>
                                ) : (
                                  <span className="text-muted">-</span>
                                )}
                              </td>
                              <td className="text-center">
                                {employee.phone ? (
                                  <a href={`tel:${employee.phone}`} className="text-decoration-none">
                                    {employee.phone}
                                  </a>
                                ) : (
                                  <span className="text-muted">-</span>
                                )}
                              </td>
                              <td className="text-center">
                                <span className={`badge ${employee.isActive ? 'bg-success' : 'bg-secondary'}`}>
                                  <i className={`fas ${employee.isActive ? 'fa-check' : 'fa-times'} me-1`}></i>
                                  {employee.isActive ? 'نشط' : 'غير نشط'}
                                </span>
                                {employee.currentShift && (
                                  <div className="mt-1">
                                    <span className="badge bg-info">
                                      <i className="fas fa-clock me-1"></i>
                                      في نوبة عمل
                                    </span>
                                  </div>
                                )}
                              </td>
                              <td className="text-center">
                                <div className="btn-group btn-group-sm" role="group">
                                  <button
                                    className="btn btn-outline-primary"
                                    onClick={() => handleEmployeesScreenEditEmployee(employee)}
                                    title="تعديل البيانات"
                                  >
                                    <i className="fas fa-edit"></i>
                                  </button>
                                  
                                  <button
                                    className={`btn ${employee.isActive ? 'btn-outline-warning' : 'btn-outline-success'}`}
                                    onClick={() => handleEmployeesScreenToggleStatus(employee)}
                                    title={employee.isActive ? 'إلغاء التفعيل' : 'تفعيل'}
                                  >
                                    <i className={`fas ${employee.isActive ? 'fa-pause' : 'fa-play'}`}></i>
                                  </button>
                                  
                                  <button
                                    className="btn btn-outline-danger"
                                    onClick={() => handleEmployeesScreenDeleteEmployee(employee._id)}
                                    title="حذف الموظف"
                                  >
                                    <i className="fas fa-trash"></i>
                                  </button>
                                </div>
                              </td>
                            </tr>
                          ))}
                        </tbody>
                      </table>
                    </div>
                  </div>
                </div>
              )}
            </>
          )}
        </div>
      </div>

      {/* Employee Modal */}
      {employeesScreenShowModal && (
        <div className="modal-backdrop">
          <div className="modal fade show d-block" tabIndex={-1}>
            <div className="modal-dialog modal-lg modal-dialog-centered">
              <div className="modal-content">
                <div className="modal-header">
                  <h5 className="modal-title">
                    {employeesScreenEditingEmployee ? 'تعديل بيانات الموظف' : 'إضافة موظف جديد'}
                  </h5>
                  <button
                    type="button"
                    className="btn-close"
                    onClick={handleEmployeesScreenCloseModal}
                    title="إغلاق النافذة"
                    aria-label="إغلاق النافذة"
                  ></button>
                </div>
                <form onSubmit={handleEmployeesScreenSubmit}>
                  <div className="modal-body">
                    <div className="row g-3">
                      <div className="col-md-6">
                        <label htmlFor="employeeName" className="form-label">الاسم الكامل</label>
                        <input
                          type="text"
                          className="form-control"
                          id="employeeName"
                          value={employeesScreenFormData.name}
                          onChange={(e) => setEmployeesScreenFormData({
                            ...employeesScreenFormData,
                            name: e.target.value
                          })}
                          required
                        />
                      </div>
                      
                      <div className="col-md-6">
                        <label htmlFor="employeeUsername" className="form-label">اسم المستخدم</label>
                        <input
                          type="text"
                          className="form-control"
                          id="employeeUsername"
                          value={employeesScreenFormData.username}
                          onChange={(e) => setEmployeesScreenFormData({
                            ...employeesScreenFormData,
                            username: e.target.value
                          })}
                          required
                        />
                      </div>
                      
                      <div className="col-md-6">
                        <label htmlFor="employeePassword" className="form-label">
                          {employeesScreenEditingEmployee ? 'كلمة المرور الجديدة (اختياري)' : 'كلمة المرور'}
                        </label>
                        <input
                          type="password"
                          className="form-control"
                          id="employeePassword"
                          value={employeesScreenFormData.password}
                          onChange={(e) => setEmployeesScreenFormData({
                            ...employeesScreenFormData,
                            password: e.target.value
                          })}
                          required={!employeesScreenEditingEmployee}
                        />
                      </div>
                      
                      <div className="col-md-6">
                        <label htmlFor="employeeRole" className="form-label">الوظيفة</label>
                        <select
                          className="form-select"
                          id="employeeRole"
                          value={employeesScreenFormData.role}
                          onChange={(e) => setEmployeesScreenFormData({
                            ...employeesScreenFormData,
                            role: e.target.value as any
                          })}
                          required
                        >
                          <option value="waiter">نادل</option>
                          <option value="chef">طباخ</option>
                          <option value="manager">مدير</option>
                        </select>
                      </div>
                      
                      <div className="col-md-6">
                        <label htmlFor="employeeEmail" className="form-label">البريد الإلكتروني (اختياري)</label>
                        <input
                          type="email"
                          className="form-control"
                          id="employeeEmail"
                          value={employeesScreenFormData.email}
                          onChange={(e) => setEmployeesScreenFormData({
                            ...employeesScreenFormData,
                            email: e.target.value
                          })}
                        />
                      </div>
                      
                      <div className="col-md-6">
                        <label htmlFor="employeePhone" className="form-label">رقم الهاتف (اختياري)</label>
                        <input
                          type="tel"
                          className="form-control"
                          id="employeePhone"
                          value={employeesScreenFormData.phone}
                          onChange={(e) => setEmployeesScreenFormData({
                            ...employeesScreenFormData,
                            phone: e.target.value
                          })}
                        />
                      </div>
                    </div>
                  </div>
                  <div className="modal-footer">
                    <button
                      type="button"
                      className="btn btn-secondary"
                      onClick={handleEmployeesScreenCloseModal}
                    >
                      إلغاء
                    </button>
                    <button
                      type="submit"
                      className="btn btn-primary"
                    >
                      {employeesScreenEditingEmployee ? 'تحديث البيانات' : 'إضافة الموظف'}
                    </button>
                  </div>
                </form>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default EmployeesManagerScreenBootstrap;

// اختبار محدد للنادلة Bosy والطاولات 1، 2، 29
const https = require('https');

async function testBosyTables() {
  console.log('🔍 فحص طاولات النادلة Bosy...');
  
  const baseUrl = 'https://deshacoffee-production.up.railway.app';
  
  // فحص الطلبات للنادلة Bosy
  console.log('\n📋 فحص طلبات النادلة Bosy:');
  try {
    const ordersResponse = await fetch(`${baseUrl}/api/v1/orders`);
    const ordersData = await ordersResponse.json();
    
    console.log('إجمالي الطلبات:', ordersData.length || ordersData.data?.length || 0);
    
    const orders = ordersData.data || ordersData || [];
    
    // فلترة طلبات Bosy
    const bosyOrders = orders.filter(order => 
      order.waiterName === 'Bosy' || 
      order.waiterName === 'بوسي' ||
      order.waiter?.name === 'Bo<PERSON>' ||
      order.waiter?.username === 'Bosy'
    );
    
    console.log('طلبات Bosy:', bosyOrders.length);
    
    // فحص طلبات كل طاولة
    [1, 2, 29].forEach(tableNum => {
      const tableOrders = bosyOrders.filter(order => 
        order.tableNumber == tableNum || 
        parseInt(order.tableNumber) === tableNum
      );
      
      console.log(`طاولة ${tableNum}: ${tableOrders.length} طلب`);
      if (tableOrders.length > 0) {
        tableOrders.forEach(order => {
          console.log(`  - طلب ${order._id?.slice(-6)}: ${order.totalPrice} جنيه، حالة: ${order.status}`);
        });
      }
    });
    
  } catch (error) {
    console.error('خطأ في جلب الطلبات:', error.message);
  }
  
  // فحص الطاولات
  console.log('\n🏓 فحص حسابات الطاولات:');
  try {
    const tablesResponse = await fetch(`${baseUrl}/api/v1/table-accounts`);
    const tablesData = await tablesResponse.json();
    
    console.log('إجمالي الطاولات:', tablesData.length || tablesData.data?.length || 0);
    
    const tables = tablesData.data || tablesData || [];
    
    // فلترة طاولات Bosy
    const bosyTables = tables.filter(table => 
      table.waiterName === 'Bosy' || 
      table.waiterName === 'بوسي' ||
      table.waiter?.name === 'Bosy' ||
      table.waiter?.username === 'Bosy'
    );
    
    console.log('طاولات Bosy:', bosyTables.length);
    
    // فحص الطاولات المحددة
    [1, 2, 29].forEach(tableNum => {
      const table = bosyTables.find(t => 
        t.tableNumber == tableNum || 
        parseInt(t.tableNumber) === tableNum
      );
      
      if (table) {
        console.log(`طاولة ${tableNum}: موجودة`);
        console.log(`  - المبلغ: ${table.totalAmount || 0} جنيه`);
        console.log(`  - الطلبات: ${table.orders?.length || 0}`);
        console.log(`  - الحالة: ${table.status}`);
        console.log(`  - مفتوحة: ${table.isOpen}`);
        console.log(`  - النادل: ${table.waiterName}`);
        console.log(`  - معرف النادل: ${table.waiterId}`);
      } else {
        console.log(`طاولة ${tableNum}: غير موجودة في حسابات الطاولات`);
      }
    });
    
  } catch (error) {
    console.error('خطأ في جلب الطاولات:', error.message);
  }
}

testBosyTables();

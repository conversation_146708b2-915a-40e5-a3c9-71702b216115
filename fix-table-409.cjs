const fetch = require('node-fetch');

const backEndURL = 'http://localhost:4010';
const productionURL = 'https://deshacoffee-production.up.railway.app';

async function fixTable409Issue() {
  console.log('🔧 إصلاح مشكلة الخطأ 409 - تحرير الطاولات المحجوزة...\n');

  try {
    // Determine which backend to use
    let baseURL = backEndURL;
    try {
      const healthCheck = await fetch(`${baseURL}/health`, { timeout: 5000 });
      console.log('✅ استخدام الخادم المحلي');
    } catch (error) {
      console.log('⚠️ التبديل إلى خادم الإنتاج...');
      baseURL = productionURL;
    }

    console.log(`🌐 الخادم: ${baseURL}\n`);

    // 1. Login as manager/admin to free tables
    console.log('🔐 تسجيل الدخول كمدير لتحرير الطاولات...');
    
    // Try different admin credentials
    const adminCredentials = [
      { username: 'بيسو', password: '253040' }, // Manager account
      { username: 'admin', password: 'admin' },
      { username: 'manager', password: 'manager' }
    ];

    let adminToken = null;
    let adminInfo = null;

    for (const creds of adminCredentials) {
      try {
        const loginResponse = await fetch(`${baseURL}/api/auth/login`, {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify(creds)
        });

        if (loginResponse.ok) {
          const loginData = await loginResponse.json();
          if (loginData.user.role === 'manager' || loginData.user.role === 'admin') {
            adminToken = loginData.token;
            adminInfo = loginData.user;
            console.log(`✅ تسجيل دخول ناجح: ${adminInfo.name || adminInfo.username} (${adminInfo.role})`);
            break;
          }
        }
      } catch (error) {
        // Continue to next credential
      }
    }

    if (!adminToken) {
      console.log('❌ فشل في تسجيل الدخول كمدير');
      console.log('💡 جرب تحرير الطاولات من واجهة المدير يدوياً');
      return;
    }

    // 2. Get all tables
    console.log('\n📋 جلب قائمة الطاولات...');
    const tablesResponse = await fetch(`${baseURL}/api/tables`, {
      headers: { 'Authorization': `Bearer ${adminToken}` }
    });

    if (!tablesResponse.ok) {
      console.log('❌ فشل في جلب قائمة الطاولات');
      return;
    }

    let tables = await tablesResponse.json();
    
    // Handle different response formats
    if (tables.data) tables = tables.data;
    if (!Array.isArray(tables)) {
      console.log('❌ تنسيق غير متوقع لقائمة الطاولات');
      return;
    }

    console.log(`📊 إجمالي الطاولات: ${tables.length}`);

    // 3. Find occupied tables
    const occupiedTables = tables.filter(t => t.status === 'occupied');
    console.log(`🔒 الطاولات المحجوزة: ${occupiedTables.length}`);

    if (occupiedTables.length === 0) {
      console.log('✅ جميع الطاولات متاحة - لا توجد مشكلة!');
      return;
    }

    // 4. Free occupied tables
    console.log('\n🔧 تحرير الطاولات المحجوزة...');
    
    for (const table of occupiedTables) {
      console.log(`\n📍 تحرير الطاولة ${table.number}:`);
      console.log(`  🔒 محجوزة من قبل: ${table.assignedWaiter || 'غير محدد'}`);
      
      try {
        const updateResponse = await fetch(`${baseURL}/api/tables/${table._id}`, {
          method: 'PUT',
          headers: {
            'Authorization': `Bearer ${adminToken}`,
            'Content-Type': 'application/json'
          },
          body: JSON.stringify({
            status: 'available',
            assignedWaiter: null,
            occupiedAt: null,
            lastCleaned: new Date()
          })
        });

        if (updateResponse.ok) {
          console.log(`  ✅ تم تحرير الطاولة ${table.number} بنجاح`);
        } else {
          const errorText = await updateResponse.text();
          console.log(`  ❌ فشل تحرير الطاولة ${table.number}: ${errorText}`);
        }
      } catch (error) {
        console.log(`  ❌ خطأ في تحرير الطاولة ${table.number}: ${error.message}`);
      }
    }

    // 5. Verify the fix
    console.log('\n🧪 التحقق من إصلاح المشكلة...');
    
    // Login as waiter again
    const waiterLoginResponse = await fetch(`${baseURL}/api/auth/login`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        username: 'Bosy',
        password: '253040'
      })
    });

    if (!waiterLoginResponse.ok) {
      console.log('❌ فشل في تسجيل دخول النادل للاختبار');
      return;
    }

    const waiterData = await waiterLoginResponse.json();
    const waiterToken = waiterData.token;

    // Try to create a test order
    const testOrder = {
      items: [
        {
          productId: '6123456789abcdef12345678', // Fake ID for testing
          quantity: 1
        }
      ],
      tableNumber: 1,
      customerName: 'عميل اختبار',
      orderType: 'dine-in'
    };

    const testOrderResponse = await fetch(`${baseURL}/api/orders`, {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${waiterToken}`,
        'Content-Type': 'application/json'
      },
      body: JSON.stringify(testOrder)
    });

    console.log(`📊 نتيجة اختبار الطلب: ${testOrderResponse.status} ${testOrderResponse.statusText}`);
    
    if (testOrderResponse.status === 409) {
      console.log('❌ المشكلة لا تزال موجودة - قد تحتاج لتدخل يدوي');
      const errorText = await testOrderResponse.text();
      console.log(`📝 الخطأ: ${errorText}`);
    } else if (testOrderResponse.status === 404) {
      console.log('⚠️ منتج غير موجود (متوقع) - لكن لا يوجد خطأ 409');
      console.log('✅ تم حل مشكلة الطاولة المحجوزة!');
    } else if (testOrderResponse.ok) {
      console.log('✅ تم إرسال الطلب بنجاح - المشكلة محلولة!');
    }

    console.log('\n🎉 انتهى الإصلاح');
    console.log('💡 يمكن الآن للنوادل إرسال الطلبات بدون خطأ 409');

  } catch (error) {
    console.error('❌ خطأ في عملية الإصلاح:', error.message);
    console.error('📊 التفاصيل:', error);
  }
}

// تشغيل الإصلاح
fixTable409Issue().then(() => {
  console.log('\n✅ انتهت عملية الإصلاح');
}).catch(error => {
  console.error('❌ فشل الإصلاح:', error);
});

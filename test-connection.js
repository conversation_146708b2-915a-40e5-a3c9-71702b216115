// اختبار بسيط للاتصال
console.log('بدء اختبار الاتصال...');

const mongoose = require('mongoose');

const mongoUri = 'mongodb+srv://besomustafa:<EMAIL>/deshacoffee?retryWrites=true&w=majority&appName=MyCoffeChop';

async function testConnection() {
  try {
    console.log('محاولة الاتصال بقاعدة البيانات...');
    await mongoose.connect(mongoUri);
    console.log('تم الاتصال بنجاح!');
    
    // البحث عن المجموعات
    const collections = await mongoose.connection.db.listCollections().toArray();
    console.log('المجموعات الموجودة:');
    collections.forEach(col => {
      console.log('- ' + col.name);
    });
    
    // البحث عن الطلبات
    const ordersCount = await mongoose.connection.db.collection('orders').countDocuments();
    console.log('عدد الطلبات: ' + ordersCount);
    
    // البحث عن المستخدمين
    const usersCount = await mongoose.connection.db.collection('users').countDocuments();
    console.log('عدد المستخدمين: ' + usersCount);
    
    if (ordersCount > 0) {
      console.log('جاري جلب عينة من الطلبات...');
      const sampleOrders = await mongoose.connection.db.collection('orders').find({}).limit(3).toArray();
      console.log('عينة من الطلبات:');
      sampleOrders.forEach((order, index) => {
        console.log((index + 1) + '. الحالة: ' + (order.status || 'غير محدد'));
        console.log('   الإجمالي: ' + (order.total || 0));
        console.log('   الخصم: ' + (order.discount || 0));
        console.log('   التاريخ: ' + order.createdAt);
      });
    }
    
    await mongoose.disconnect();
    console.log('تم قطع الاتصال');
    
  } catch (error) {
    console.error('خطأ في الاتصال: ' + error.message);
  }
}

testConnection();

// تحليل التباين في مبيعات النُدُل - Sales Discrepancy Analysis
const { MongoClient } = require('mongodb');
require('dotenv').config();

const uri = process.env.MONGODB_URI;

async function analyzeSalesDiscrepancy() {
    const client = new MongoClient(uri);
    
    try {
        await client.connect();
        console.log('🔍 تحليل التباين في مبيعات النُدُل...');
        console.log('🔗 متصل بقاعدة البيانات');
        
        const db = client.db('coffee_shop');
        const ordersCollection = db.collection('orders');
        const usersCollection = db.collection('users');
        
        // الحصول على جميع النُدُل
        const waiters = await usersCollection.find({ role: 'waiter' }).toArray();
        console.log(`\n👥 عدد النُدُل: ${waiters.length}`);
        
        // حساب إجمالي المبيعات
        const totalSalesResult = await ordersCollection.aggregate([
            {
                $match: {
                    status: 'completed',
                    total: { $exists: true, $ne: null }
                }
            },
            {
                $group: {
                    _id: null,
                    totalSales: { $sum: '$total' },
                    orderCount: { $sum: 1 }
                }
            }
        ]).toArray();
        
        const totalSales = totalSalesResult[0]?.totalSales || 0;
        const totalOrders = totalSalesResult[0]?.orderCount || 0;
        
        console.log(`\n💰 إجمالي المبيعات الفعلي: ${totalSales.toFixed(2)} جنيه`);
        console.log(`📊 إجمالي عدد الطلبات المكتملة: ${totalOrders}`);
        
        // حساب مبيعات كل نادل
        let waiterSalesTotal = 0;
        let waiterOrdersTotal = 0;
        const waiterSalesBreakdown = [];
        
        for (const waiter of waiters) {
            const waiterSales = await ordersCollection.aggregate([
                {
                    $match: {
                        waiter: waiter.username,
                        status: 'completed',
                        total: { $exists: true, $ne: null }
                    }
                },
                {
                    $group: {
                        _id: null,
                        sales: { $sum: '$total' },
                        orders: { $sum: 1 }
                    }
                }
            ]).toArray();
            
            const sales = waiterSales[0]?.sales || 0;
            const orders = waiterSales[0]?.orders || 0;
            
            waiterSalesTotal += sales;
            waiterOrdersTotal += orders;
            
            waiterSalesBreakdown.push({
                name: waiter.name || waiter.username,
                username: waiter.username,
                sales: sales,
                orders: orders
            });
        }
        
        console.log(`\n👨‍💼 مجموع مبيعات النُدُل: ${waiterSalesTotal.toFixed(2)} جنيه`);
        console.log(`📋 مجموع طلبات النُدُل: ${waiterOrdersTotal}`);
        
        // حساب التباين
        const discrepancy = totalSales - waiterSalesTotal;
        const orderDiscrepancy = totalOrders - waiterOrdersTotal;
        
        console.log(`\n🚨 التباين في المبيعات: ${discrepancy.toFixed(2)} جنيه`);
        console.log(`📊 التباين في عدد الطلبات: ${orderDiscrepancy}`);
        
        if (Math.abs(discrepancy) > 1) {
            console.log(`\n⚠️  هناك مشكلة! التباين كبير: ${discrepancy.toFixed(2)} جنيه`);
        } else {
            console.log(`\n✅ التباين طبيعي ومقبول: ${discrepancy.toFixed(2)} جنيه`);
        }
        
        // تفصيل مبيعات النُدُل
        console.log(`\n📈 تفصيل مبيعات النُدُل:`);
        console.log('=' .repeat(60));
        waiterSalesBreakdown.forEach(waiter => {
            console.log(`👤 ${waiter.name} (${waiter.username}): ${waiter.sales.toFixed(2)} جنيه - ${waiter.orders} طلب`);
        });
        
        // البحث عن الطلبات بدون نادل أو بنادل غير موجود
        console.log(`\n🔍 البحث عن الطلبات المفقودة...`);
        
        const ordersWithoutWaiter = await ordersCollection.find({
            status: 'completed',
            $or: [
                { waiter: { $exists: false } },
                { waiter: null },
                { waiter: '' }
            ]
        }).toArray();
        
        console.log(`📋 طلبات بدون نادل: ${ordersWithoutWaiter.length}`);
        
        if (ordersWithoutWaiter.length > 0) {
            let orphanedSales = 0;
            ordersWithoutWaiter.forEach(order => {
                orphanedSales += order.total || 0;
                console.log(`  📦 طلب ${order._id}: ${order.total} جنيه - تاريخ: ${order.createdAt}`);
            });
            console.log(`💰 مجموع مبيعات الطلبات بدون نادل: ${orphanedSales.toFixed(2)} جنيه`);
        }
        
        // البحث عن طلبات بنُدُل غير موجودين
        const waiterUsernames = waiters.map(w => w.username);
        const ordersWithInvalidWaiter = await ordersCollection.find({
            status: 'completed',
            waiter: { $exists: true, $ne: null, $ne: '' },
            waiter: { $nin: waiterUsernames }
        }).toArray();
        
        console.log(`👻 طلبات بنُدُل غير موجودين: ${ordersWithInvalidWaiter.length}`);
        
        if (ordersWithInvalidWaiter.length > 0) {
            let invalidWaiterSales = 0;
            const invalidWaiters = new Set();
            
            ordersWithInvalidWaiter.forEach(order => {
                invalidWaiterSales += order.total || 0;
                invalidWaiters.add(order.waiter);
                console.log(`  📦 طلب ${order._id}: ${order.total} جنيه - نادل: ${order.waiter}`);
            });
            
            console.log(`💰 مجموع مبيعات النُدُل غير الموجودين: ${invalidWaiterSales.toFixed(2)} جنيه`);
            console.log(`👤 النُدُل غير الموجودين: ${Array.from(invalidWaiters).join(', ')}`);
        }
        
        // تحليل الحالات المختلفة للطلبات
        console.log(`\n📊 تحليل حالات الطلبات:`);
        const statusAnalysis = await ordersCollection.aggregate([
            {
                $group: {
                    _id: '$status',
                    count: { $sum: 1 },
                    totalValue: { $sum: { $ifNull: ['$total', 0] } }
                }
            },
            { $sort: { count: -1 } }
        ]).toArray();
        
        statusAnalysis.forEach(status => {
            console.log(`  📋 ${status._id || 'غير محدد'}: ${status.count} طلب - ${status.totalValue.toFixed(2)} جنيه`);
        });
        
        // توصيات للإصلاح
        console.log(`\n🔧 توصيات الإصلاح:`);
        console.log('=' .repeat(60));
        
        if (ordersWithoutWaiter.length > 0) {
            console.log(`1. ✅ إصلاح ${ordersWithoutWaiter.length} طلب بدون نادل`);
        }
        
        if (ordersWithInvalidWaiter.length > 0) {
            console.log(`2. ✅ إصلاح ${ordersWithInvalidWaiter.length} طلب بنُدُل غير موجودين`);
        }
        
        if (Math.abs(discrepancy) > 1) {
            console.log(`3. ✅ مراجعة وإصلاح التباين في المبيعات`);
        }
        
        console.log(`4. ✅ إضافة تحقق من صحة بيانات النُدُل عند إنشاء الطلبات`);
        console.log(`5. ✅ إنشاء تقرير دوري لمراقبة التباين في المبيعات`);
        
    } catch (error) {
        console.error('❌ خطأ في تحليل التباين:', error);
    } finally {
        await client.close();
    }
}

analyzeSalesDiscrepancy();

const mongoose = require('mongoose');
require('dotenv').config();

const orderSchema = new mongoose.Schema({}, { strict: false });
const Order = mongoose.model('Order', orderSchema);

async function checkOrders() {
  try {
    await mongoose.connect(process.env.MONGODB_URI);
    console.log('🔗 اتصال بقاعدة البيانات...');
    
    const orders = await Order.find({}).limit(10).sort({ createdAt: -1 });
    console.log(`📋 عرض آخر 10 طلبات:`);
    
    orders.forEach((order, index) => {
      console.log(`${index + 1}. ID: ${order._id}`);
      console.log(`   Status: ${order.status}`);
      console.log(`   Waiter Name: ${order.waiterName || 'غير محدد'}`);
      console.log(`   Waiter ID: ${order.waiterId || 'غير محدد'}`);
      console.log(`   Total: ${order.totalPrice || order.totals?.total || 'غير محدد'}`);
      console.log(`   Created: ${order.createdAt}`);
      console.log('   ---');
    });
    
    process.exit(0);
  } catch (error) {
    console.error('❌ خطأ:', error);
    process.exit(1);
  }
}

checkOrders();

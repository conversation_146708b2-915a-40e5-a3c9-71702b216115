const fetch = require('node-fetch');
require('dotenv').config();

const backEndURL = process.env.BACKEND_URL || 'http://localhost:4010';

async function debugAuth() {
  try {
    // تسجيل الدخول
    console.log('🔐 تسجيل الدخول...');
    const loginResponse = await fetch(`${backEndURL}/api/auth/login`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        username: 'Be<PERSON>',
        password: 'admin123'  // أو جرب كلمة مرور أخرى
      })
    });

    console.log(`Login Status: ${loginResponse.status}`);
    const loginData = await loginResponse.json();
    console.log('Login Response:', JSON.stringify(loginData, null, 2));

    if (loginData.token) {
      console.log('✅ تم الحصول على Token');
      
      // جرب مع v1 API
      console.log('\n🔍 جرب مع /api/v1/users...');
      const usersV1Response = await fetch(`${backEndURL}/api/v1/users`, {
        headers: {
          'Authorization': `Bearer ${loginData.token}`,
          'Content-Type': 'application/json'
        }
      });

      console.log(`V1 Users Status: ${usersV1Response.status}`);
      
      if (usersV1Response.ok) {
        const usersData = await usersV1Response.json();
        console.log('👥 V1 بيانات النادلين:', JSON.stringify(usersData, null, 2));
      } else {
        const errorText = await usersV1Response.text();
        console.log('V1 Error:', errorText);
      }
    }

  } catch (error) {
    console.error('❌ خطأ:', error);
  }
}

debugAuth();

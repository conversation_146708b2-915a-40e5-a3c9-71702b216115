#root {
  width: 100%;
  margin: 0 auto;
  max-width: 100vw;
  overflow-x: hidden;
}

/* إصلاح المساحات الجانبية للهاتف المحمول */
@media (max-width: 768px) {
  #root {
    width: 100vw;
    max-width: 100vw;
    margin: 0;
    padding: 0;
  }
}

.app-logo {
  height: 80px;
  margin-bottom: var(--spacing-md);
}

.app-title {
  color: var(--primary);
  font-weight: var(--font-weight-bold);
  margin-bottom: var(--spacing-lg);
}

/* تصميم الحاويات */
.container {
  width: 100%;
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 var(--spacing-md);
}

.page-container {
  padding: var(--spacing-xl) 0;
}

/* إصلاح الحاويات للهاتف المحمول */
@media (max-width: 768px) {
  .container {
    width: 100vw;
    max-width: 100vw;
    margin: 0;
    padding: 0;
  }
  
  .page-container {
    padding: 0;
    width: 100vw;
    max-width: 100vw;
  }
}

/* تصميم الصفوف والأعمدة */
.row {
  display: flex;
  flex-wrap: wrap;
  margin: 0 -var(--spacing-sm);
}

.col {
  flex: 1;
  padding: 0 var(--spacing-sm);
}

/* تصميم البطاقات */
.card {
  background-color: var(--surface);
  border-radius: var(--border-radius-lg);
  box-shadow: var(--shadow-md);
  padding: var(--spacing-lg);
  margin-bottom: var(--spacing-lg);
  transition: transform 0.2s, box-shadow 0.2s;
}

.card:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-lg);
}

.login-container, .main-container {
  min-height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  background: var(--background);
}

.login-form {
  background: var(--surface);
  padding: var(--spacing-xl);
  border-radius: var(--border-radius-lg);
  box-shadow: var(--shadow-md);
  width: 100%;
  max-width: 400px;
  display: flex;
  flex-direction: column;
  gap: var(--spacing-lg);
}

.main-content {
  background: var(--surface);
  padding: var(--spacing-xl);
  border-radius: var(--border-radius-lg);
  box-shadow: var(--shadow-md);
  width: 100%;
  display: flex;
  flex-direction: column;
  gap: var(--spacing-lg);
}

/* تصميم شريط التنقل */
.navbar {
  background: var(--primary);
  color: white;
  padding: var(--spacing-md) var(--spacing-lg);
  display: flex;
  justify-content: space-between;
  align-items: center;
  box-shadow: var(--shadow-md);
  position: sticky;
  top: 0;
  z-index: 100;
  border-radius: 0 0 var(--border-radius-md) var(--border-radius-md);
}

.navbar-brand {
  font-size: var(--font-size-xl);
  font-weight: var(--font-weight-bold);
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
}

.navbar-brand img {
  height: 32px;
}

.navbar-nav {
  display: flex;
  gap: var(--spacing-lg);
  align-items: center;
}

.nav-link {
  color: white;
  text-decoration: none;
  font-size: var(--font-size-md);
  font-weight: var(--font-weight-medium);
  padding: var(--spacing-xs) var(--spacing-sm);
  border-radius: var(--border-radius-sm);
  transition: background-color 0.2s;
}

.nav-link:hover {
  background-color: rgba(255, 255, 255, 0.1);
}

.nav-link.active {
  background-color: rgba(255, 255, 255, 0.2);
}

.logout-btn {
  background-color: var(--accent);
  color: white;
  border: none;
  padding: var(--spacing-xs) var(--spacing-md);
  border-radius: var(--border-radius-md);
  font-weight: var(--font-weight-medium);
  transition: background-color 0.2s;
}

.logout-btn:hover {
  background-color: var(--accent-dark);
}

/* جداول الطلبات */
.table-container {
  overflow-x: auto;
  margin-bottom: var(--spacing-lg);
  border-radius: var(--border-radius-md);
  box-shadow: var(--shadow-sm);
}

table {
  width: 100%;
  background: var(--surface);
  text-align: right;
  border-collapse: collapse;
}

th, td {
  padding: var(--spacing-sm) var(--spacing-md);
  border-bottom: 1px solid var(--border);
  font-size: var(--font-size-md);
}

th {
  background: var(--primary-light);
  color: white;
  font-weight: var(--font-weight-medium);
}

tr:last-child td {
  border-bottom: none;
}

tr:hover {
  background-color: rgba(0, 0, 0, 0.02);
}

/* بلاطات لوحة المدير */
.dashboard-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
  gap: var(--spacing-lg);
  margin: var(--spacing-lg) 0;
}

.tile-btn {
  background: var(--primary);
  color: white;
  border: none;
  padding: var(--spacing-lg);
  border-radius: var(--border-radius-lg);
  font-size: var(--font-size-lg);
  font-weight: var(--font-weight-medium);
  cursor: pointer;
  text-align: center;
  box-shadow: var(--shadow-md);
  transition: all 0.2s;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  gap: var(--spacing-sm);
  min-height: 120px;
}

.tile-btn:hover {
  background: var(--primary-dark);
  transform: translateY(-3px);
  box-shadow: var(--shadow-lg);
}

.tile-btn:active {
  transform: translateY(0);
}

.tile-btn i {
  font-size: 2rem;
  margin-bottom: var(--spacing-sm);
}

/* تصميم متجاوب */
@media (max-width: 768px) {
  .navbar {
    flex-direction: column;
    padding: var(--spacing-sm);
  }

  .navbar-brand {
    margin-bottom: var(--spacing-sm);
  }

  .navbar-nav {
    flex-wrap: wrap;
    justify-content: center;
    gap: var(--spacing-sm);
  }

  .login-form {
    padding: var(--spacing-md);
    max-width: 90%;
    border-radius: var(--border-radius-md);
  }
  
  .main-content {
    padding-top: 0 !important;
    padding-bottom: var(--spacing-md);
    padding-left: var(--spacing-md);
    padding-right: var(--spacing-md);
    border-radius: var(--border-radius-md);
  }

  .dashboard-grid {
    grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));
    gap: var(--spacing-md);
  }

  .tile-btn {
    padding: var(--spacing-md);
    min-height: 100px;
  }

  th, td {
    padding: var(--spacing-xs) var(--spacing-sm);
    font-size: var(--font-size-sm);
  }

  .menu-section {
    display: none !important;
  }
}

/* Discount Requests Screen Variables - متغيرات شاشة طلبات الخصم */
:root {
  /* ألوان شاشة طلبات الخصم */
  --discount-primary-color: #2c3e50;
  --discount-secondary-color: #3498db;
  --discount-bg-primary: #ffffff;
  --discount-bg-secondary: #f8f9fa;
  --discount-text-primary: #000000;
  --discount-text-secondary: #6c757d;
  --discount-border-color: #dee2e6;
  --discount-border-light: #f1f3f4;
  
  /* ألوان الحالة لشاشة طلبات الخصم */
  --discount-success-color: #27ae60;
  --discount-success-light: rgba(39, 174, 96, 0.1);
  --discount-warning-color: #f39c12;
  --discount-warning-light: rgba(243, 156, 18, 0.1);
  --discount-error-color: #e74c3c;
  --discount-error-light: rgba(231, 76, 60, 0.1);
  --discount-info-color: #17a2b8;
  --discount-info-light: rgba(23, 162, 184, 0.1);
  
  /* متغيرات الخط لشاشة طلبات الخصم */
  --discount-font-size-xs: 12px;
  --discount-font-size-sm: 14px;
  --discount-font-size-md: 16px;
  --discount-font-size-lg: 18px;
  --discount-font-size-xl: 20px;
  --discount-font-size-xxl: 24px;
  
  /* متغيرات المسافة لشاشة طلبات الخصم */
  --discount-spacing-xs: 0.25rem;
  --discount-spacing-sm: 0.5rem;
  --discount-spacing-md: 1rem;
  --discount-spacing-lg: 1.5rem;
  --discount-spacing-xl: 2rem;
  --discount-spacing-xxl: 3rem;
  
  /* متغيرات الحدود لشاشة طلبات الخصم */
  --discount-border-radius: 8px;
  --discount-border-radius-sm: 4px;
  --discount-border-radius-lg: 12px;
  --discount-border-radius-xl: 16px;
  
  /* متغيرات الظلال لشاشة طلبات الخصم */
  --discount-shadow-sm: 0 1px 3px rgba(0, 0, 0, 0.05);
  --discount-shadow-md: 0 2px 8px rgba(0, 0, 0, 0.1);
  --discount-shadow-lg: 0 4px 16px rgba(0, 0, 0, 0.15);
  
  /* متغيرات الانتقال لشاشة طلبات الخصم */
  --discount-transition-fast: 0.2s ease;
  --discount-transition-medium: 0.3s ease;
  --discount-transition-slow: 0.5s ease;
  
  /* متغيرات خاصة بشاشة طلبات الخصم */
  --discount-card-min-width: 320px;
  --discount-grid-gap: 1.5rem;
  --discount-primary-hover: #34495e;
  --discount-primary-light: rgba(44, 62, 80, 0.1);
  --discount-pending-border: var(--discount-warning-color);
  --discount-approved-border: var(--discount-success-color);
  --discount-rejected-border: var(--discount-error-color);
  --discount-priority-high: var(--discount-error-color);
  --discount-priority-medium: var(--discount-warning-color);
  --discount-priority-low: var(--discount-success-color);
}




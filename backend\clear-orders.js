const mongoose = require('mongoose');
require('dotenv').config();

// Connect to MongoDB
const connectDB = async () => {
  try {
    await mongoose.connect(process.env.MONGODB_URI || 'mongodb+srv://besomustafa:<EMAIL>/deshacoffee?retryWrites=true&w=majority');
    console.log('✅ Connected to MongoDB');
  } catch (error) {
    console.error('❌ MongoDB connection error:', error);
    process.exit(1);
  }
};

// Order Schema
const orderSchema = new mongoose.Schema({
  orderNumber: String,
  waiterName: String,
  items: Array,
  totalAmount: Number,
  status: String,
  tableNumber: Number,
  notes: String,
  createdAt: Date,
  updatedAt: Date
});

const Order = mongoose.model('Order', orderSchema);

const clearOrders = async () => {
  try {
    await connectDB();
    
    const result = await Order.deleteMany({});
    console.log(`🗑️ Deleted ${result.deletedCount} existing orders`);
    
    await mongoose.connection.close();
    console.log('👋 Database connection closed');
  } catch (error) {
    console.error('❌ Error clearing orders:', error);
  }
};

clearOrders();

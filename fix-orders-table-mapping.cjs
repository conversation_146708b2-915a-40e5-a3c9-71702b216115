const fetch = require('node-fetch');

class OrdersTableMappingFixer {
  constructor() {
    this.baseURL = 'https://deshacoffee-production.up.railway.app';
    this.waiterId = '684c864e558dd1359d2380f7'; // Bosy
    this.waiterName = 'بوسي';
    this.targetTables = ['1', '2', '29'];
  }

  async diagnoseAndFix() {
    console.log('🔧 تشخيص وإصلاح مشكلة ربط الطلبات بالطاولات...\n');
    console.log('='.repeat(70));

    // 1. فحص Authentication واستخراج التوكن
    const authResult = await this.testAuthenticationFlow();
    if (!authResult.success) {
      console.log('❌ لا يمكن المتابعة بدون مصادقة صحيحة');
      return;
    }

    console.log('\n' + '-'.repeat(50) + '\n');

    // 2. جلب البيانات الصحيحة
    const ordersResult = await this.fetchOrdersWithAuth(authResult.token);
    const tablesResult = await this.fetchTablesWithAuth(authResult.token);

    console.log('\n' + '-'.repeat(50) + '\n');

    // 3. تحليل المشكلة
    await this.analyzeOrderTableMapping(ordersResult.orders, tablesResult.tables);

    console.log('\n' + '-'.repeat(50) + '\n');

    // 4. اقتراح الحلول
    await this.suggestFixes();
  }

  async testAuthenticationFlow() {
    console.log('🔐 اختبار تدفق المصادقة...');
    
    try {
      // محاولة تسجيل الدخول للحصول على توكن صحيح
      const loginData = {
        username: 'Bosy',
        password: '253040',
        role: 'نادل'
      };

      console.log('📡 محاولة تسجيل الدخول:', loginData);

      const response = await fetch(`${this.baseURL}/api/auth/login`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(loginData)
      });

      const result = await response.json();
      console.log('📊 نتيجة تسجيل الدخول:', response.status, result);

      if (response.ok && result.token) {
        console.log('✅ تم تسجيل الدخول بنجاح');
        console.log('👤 بيانات المستخدم:', {
          name: result.user?.name,
          username: result.user?.username,
          role: result.user?.role,
          id: result.user?._id
        });
        
        return {
          success: true,
          token: result.token,
          user: result.user
        };
      } else {
        console.log('❌ فشل في تسجيل الدخول:', result.message);
        return { success: false };
      }
    } catch (error) {
      console.log('❌ خطأ في تسجيل الدخول:', error.message);
      return { success: false };
    }
  }

  async fetchOrdersWithAuth(token) {
    console.log('📋 جلب الطلبات مع المصادقة...');
    
    try {
      const response = await fetch(`${this.baseURL}/api/v1/orders`, {
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        }
      });

      const result = await response.json();
      console.log(`📊 استجابة الطلبات ${response.status}:`, typeof result);

      let orders = [];
      if (Array.isArray(result)) {
        orders = result;
      } else if (result.data && Array.isArray(result.data)) {
        orders = result.data;
      }

      console.log(`📈 إجمالي الطلبات: ${orders.length}`);

      // فلترة طلبات بوسي
      const bosyOrders = orders.filter(order => {
        return order.waiterId === this.waiterId || 
               order.waiterName === this.waiterName ||
               order.waiterName === 'Bosy';
      });

      console.log(`👤 طلبات بوسي: ${bosyOrders.length}`);

      // فحص طلبات الطاولات المستهدفة
      const targetTableOrders = bosyOrders.filter(order => 
        this.targetTables.includes(String(order.tableNumber))
      );

      console.log(`🏓 طلبات الطاولات 1,2,29: ${targetTableOrders.length}`);

      // عرض تفاصيل الطلبات
      if (targetTableOrders.length > 0) {
        console.log('\n📝 تفاصيل طلبات الطاولات المستهدفة:');
        targetTableOrders.forEach(order => {
          console.log(`  - طاولة ${order.tableNumber}: طلب #${order.orderNumber || order._id?.slice(-6)}, حالة: ${order.status}, مبلغ: ${order.totalPrice} جنيه`);
        });
      }

      return {
        success: true,
        orders: bosyOrders,
        targetTableOrders
      };
    } catch (error) {
      console.log('❌ خطأ في جلب الطلبات:', error.message);
      return { success: false, orders: [] };
    }
  }

  async fetchTablesWithAuth(token) {
    console.log('🏓 جلب الطاولات مع المصادقة...');
    
    const endpoints = [
      `/api/v1/table-accounts?waiterId=${this.waiterId}`,
      `/api/v1/table-accounts`,
      `/api/v1/tables?waiterId=${this.waiterId}`,
      `/api/v1/tables`
    ];

    for (const endpoint of endpoints) {
      try {
        console.log(`📡 اختبار: ${endpoint}`);
        
        const response = await fetch(`${this.baseURL}${endpoint}`, {
          headers: {
            'Authorization': `Bearer ${token}`,
            'Content-Type': 'application/json'
          }
        });

        const result = await response.json();
        console.log(`  📊 استجابة ${response.status}:`, typeof result);

        if (response.ok) {
          let tables = [];
          if (Array.isArray(result)) {
            tables = result;
          } else if (result.data && Array.isArray(result.data)) {
            tables = result.data;
          }

          if (tables.length > 0) {
            const targetTables = tables.filter(t => 
              this.targetTables.includes(String(t.tableNumber))
            );
            
            console.log(`  ✅ وجد ${tables.length} طاولة، منها ${targetTables.length} طاولة مستهدفة`);
            
            return {
              success: true,
              tables: tables,
              targetTables
            };
          }
        }
      } catch (error) {
        console.log(`  ❌ خطأ: ${error.message}`);
      }
    }

    return { success: false, tables: [] };
  }

  async analyzeOrderTableMapping(orders, tables) {
    console.log('🔍 تحليل ربط الطلبات بالطاولات...');

    if (!orders || orders.length === 0) {
      console.log('❌ لا توجد طلبات للتحليل');
      return;
    }

    if (!tables || tables.length === 0) {
      console.log('⚠️ لا توجد طاولات من API - سيتم إنشاؤها من الطلبات');
    }

    // تحليل البيانات
    console.log('\n📊 تحليل البيانات:');
    console.log(`- إجمالي الطلبات: ${orders.length}`);
    console.log(`- إجمالي الطاولات: ${tables.length}`);

    // تجميع الطلبات حسب الطاولة
    const ordersByTable = {};
    orders.forEach(order => {
      const tableNum = String(order.tableNumber);
      if (!ordersByTable[tableNum]) {
        ordersByTable[tableNum] = [];
      }
      ordersByTable[tableNum].push(order);
    });

    console.log('\n🏓 الطلبات مجمعة حسب الطاولة:');
    Object.entries(ordersByTable).forEach(([tableNum, tableOrders]) => {
      const totalAmount = tableOrders.reduce((sum, order) => sum + (order.totalPrice || 0), 0);
      console.log(`  طاولة ${tableNum}: ${tableOrders.length} طلب، إجمالي ${totalAmount.toFixed(2)} جنيه`);
    });

    // فحص الطاولات المستهدفة
    console.log('\n🎯 فحص الطاولات المستهدفة:');
    this.targetTables.forEach(tableNum => {
      const tableOrders = ordersByTable[tableNum] || [];
      const existingTable = tables.find(t => String(t.tableNumber) === tableNum);
      
      console.log(`  طاولة ${tableNum}:`);
      console.log(`    - الطلبات: ${tableOrders.length}`);
      console.log(`    - موجودة في API: ${existingTable ? 'نعم' : 'لا'}`);
      
      if (tableOrders.length > 0) {
        const totalAmount = tableOrders.reduce((sum, order) => sum + (order.totalPrice || 0), 0);
        console.log(`    - إجمالي المبلغ: ${totalAmount.toFixed(2)} جنيه`);
        console.log(`    - تفاصيل الطلبات:`, tableOrders.map(o => ({
          id: o._id?.slice(-6),
          orderNumber: o.orderNumber,
          status: o.status,
          price: o.totalPrice
        })));
      }
    });

    // إنشاء الطاولات من الطلبات
    console.log('\n🔧 إنشاء طاولات محسنة من الطلبات:');
    const generatedTables = [];

    Object.entries(ordersByTable).forEach(([tableNum, tableOrders]) => {
      if (this.targetTables.includes(tableNum)) {
        const totalAmount = tableOrders.reduce((sum, order) => sum + (order.totalPrice || 0), 0);
        const lastActivity = Math.max(...tableOrders.map(o => new Date(o.createdAt).getTime()));
        
        const table = {
          _id: `generated-table-${tableNum}-${this.waiterId}`,
          tableNumber: parseInt(tableNum) || tableNum,
          waiterId: this.waiterId,
          waiterName: this.waiterName,
          status: 'active',
          isOpen: true,
          orders: tableOrders,
          totalAmount: totalAmount,
          ordersCount: tableOrders.length,
          createdAt: new Date(Math.min(...tableOrders.map(o => new Date(o.createdAt).getTime()))).toISOString(),
          updatedAt: new Date(lastActivity).toISOString(),
          customerName: tableOrders[0]?.customerName || 'عميل'
        };

        generatedTables.push(table);
        
        console.log(`  ✅ طاولة ${tableNum}: ${tableOrders.length} طلب، ${totalAmount.toFixed(2)} جنيه`);
      }
    });

    return generatedTables;
  }

  async suggestFixes() {
    console.log('💡 اقتراحات الإصلاح:');

    console.log('\n📋 المشاكل المكتشفة:');
    console.log('1. ❌ APIs تحتاج مصادقة صحيحة');
    console.log('2. ❌ قد لا توجد طاولات في table-accounts API');
    console.log('3. ❌ منطق ربط الطلبات بالطاولات قد يكون معطل');

    console.log('\n🔧 الحلول المقترحة:');
    console.log('1. ✅ تحسين منطق المصادقة في Frontend');
    console.log('2. ✅ إنشاء الطاولات من الطلبات عند عدم وجودها');
    console.log('3. ✅ تحسين منطق فلترة ومطابقة الطلبات');
    console.log('4. ✅ إضافة آلية fallback للبيانات');

    console.log('\n🚀 كود الإصلاح للـ Frontend:');
    console.log('سأقوم بإنشاء ملف إصلاح للـ WaiterDashboard...');
  }
}

// تشغيل التشخيص
async function main() {
  const fixer = new OrdersTableMappingFixer();
  await fixer.diagnoseAndFix();
}

main().catch(console.error);

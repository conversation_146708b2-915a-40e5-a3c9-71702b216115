/* ====================================
   TABLE DETAILS MODAL ENHANCEMENTS V2
   تحسينات نافذة تفاصيل الطاولة - الإصدار الثاني
   ==================================== */

/* Table Details Modal Container - Enhanced Modern Look */
.table-details-modal {
  max-width: 1400px !important;
  width: 95% !important;
  max-height: 90vh !important;
  overflow: hidden !important;
  background: linear-gradient(145deg, #ffffff 0%, #f8fafc 50%, #ffffff 100%) !important;
  border: none !important;
  border-radius: 24px !important;
  box-shadow: 
    0 32px 80px rgba(0, 0, 0, 0.12),
    0 8px 32px rgba(0, 0, 0, 0.08),
    inset 0 1px 0 rgba(255, 255, 255, 0.8) !important;
  -webkit-backdrop-filter: blur(20px) !important; /* Safari support */
  backdrop-filter: blur(20px) !important;
  position: relative;
  overflow: hidden;
}

.table-details-modal::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: 
    radial-gradient(circle at 20% 20%, rgba(59, 130, 246, 0.05) 0%, transparent 50%),
    radial-gradient(circle at 80% 80%, rgba(16, 185, 129, 0.05) 0%, transparent 50%),
    radial-gradient(circle at 40% 60%, rgba(245, 158, 11, 0.03) 0%, transparent 50%);
  pointer-events: none;
  z-index: 0;
}

/* Modal Header Enhancement - Modern Gradient Design */
.table-details-modal .modal-header {
  background: linear-gradient(135deg, #1e40af 0%, #3b82f6 50%, #1d4ed8 100%) !important;
  color: white !important;
  padding: 2.5rem 3rem 2rem !important;
  border-bottom: none !important;
  position: relative;
  overflow: hidden;
  border-top-left-radius: 24px !important;
  border-top-right-radius: 24px !important;
  z-index: 1;
}

.table-details-modal .modal-header::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: 
    linear-gradient(45deg, rgba(255, 255, 255, 0.1) 0%, transparent 50%),
    url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="table-pattern" width="20" height="20" patternUnits="userSpaceOnUse"><circle cx="10" cy="10" r="1.5" fill="white" opacity="0.1"/><circle cx="5" cy="5" r="0.8" fill="white" opacity="0.05"/><circle cx="15" cy="15" r="0.8" fill="white" opacity="0.05"/></pattern></defs><rect width="100" height="100" fill="url(%23table-pattern)"/></svg>');
  pointer-events: none;
  z-index: -1;
}

.table-details-modal .modal-header::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  height: 2px;
  background: linear-gradient(90deg, 
    #f59e0b 0%, 
    #eab308 25%, 
    #10b981 50%, 
    #06b6d4 75%, 
    #8b5cf6 100%);
  z-index: 1;
}

.table-details-modal .modal-header h3 {
  font-size: 2rem !important;
  font-weight: 800 !important;
  margin: 0 !important;
  text-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);
  position: relative;
  z-index: 2;
  letter-spacing: -0.5px;
  display: flex;
  align-items: center;
  gap: 1rem;
}

.table-details-modal .modal-header h3 i {
  color: #fbbf24 !important;
  margin-left: 0;
  font-size: 1.8rem;
  text-shadow: 0 2px 8px rgba(251, 191, 36, 0.4);
  filter: drop-shadow(0 0 8px rgba(251, 191, 36, 0.3));
}

.table-details-modal .modal-close {
  background: rgba(255, 255, 255, 0.15) !important;
  border: 1px solid rgba(255, 255, 255, 0.25) !important;
  color: white !important;
  width: 50px !important;
  height: 50px !important;
  border-radius: 50% !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  font-size: 1.3rem !important;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1) !important;
  position: relative;
  z-index: 2;
  -webkit-backdrop-filter: blur(10px); /* Safari support */
  backdrop-filter: blur(10px);
}

.table-details-modal .modal-close:hover {
  background: rgba(239, 68, 68, 0.9) !important;
  border-color: rgba(239, 68, 68, 1) !important;
  transform: scale(1.1) rotate(90deg) !important;
  box-shadow: 
    0 8px 25px rgba(239, 68, 68, 0.4),
    0 0 20px rgba(239, 68, 68, 0.3) !important;
}

/* Modal Body Enhancement - Modern Scrollable Design */
.table-details-modal .modal-body {
  padding: 3rem !important;
  background: #ffffff !important;
  overflow-y: auto !important;
  max-height: calc(90vh - 140px) !important;
  position: relative;
  z-index: 1;
  /* Custom Scrollbar */
  scrollbar-width: thin; /* Firefox */
  scrollbar-color: rgba(59, 130, 246, 0.3) transparent; /* Firefox */
}

/* Webkit Browsers Custom Scrollbar */
.table-details-modal .modal-body::-webkit-scrollbar {
  width: 8px;
}

.table-details-modal .modal-body::-webkit-scrollbar-track {
  background: rgba(0, 0, 0, 0.05);
  border-radius: 10px;
}

.table-details-modal .modal-body::-webkit-scrollbar-thumb {
  background: linear-gradient(180deg, #3b82f6, #1d4ed8);
  border-radius: 10px;
  border: 2px solid transparent;
  background-clip: content-box;
}

.table-details-modal .modal-body::-webkit-scrollbar-thumb:hover {
  background: linear-gradient(180deg, #2563eb, #1e40af);
  background-clip: content-box;
}

/* Table Header Card - Ultra Modern Design */
.table-header-card {
  background: linear-gradient(135deg, 
    #1e293b 0%, 
    #334155 25%, 
    #475569 50%, 
    #334155 75%, 
    #1e293b 100%);
  color: white;
  padding: 2.5rem;
  border-radius: 20px;
  margin-bottom: 3rem;
  box-shadow: 
    0 16px 40px rgba(30, 41, 59, 0.25),
    0 8px 20px rgba(30, 41, 59, 0.15),
    inset 0 1px 0 rgba(255, 255, 255, 0.1);
  position: relative;
  overflow: hidden;
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.table-header-card::before {
  content: '';
  position: absolute;
  top: 0;
  right: 0;
  width: 200px;
  height: 200px;
  background: 
    radial-gradient(circle, rgba(59, 130, 246, 0.15) 0%, transparent 70%),
    radial-gradient(circle, rgba(16, 185, 129, 0.1) 20%, transparent 60%);
  border-radius: 50%;
  transform: translate(80px, -80px);
  animation: float 6s ease-in-out infinite;
}

.table-header-card::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  width: 150px;
  height: 150px;
  background: radial-gradient(circle, rgba(245, 158, 11, 0.1) 0%, transparent 70%);
  border-radius: 50%;
  transform: translate(-50px, 50px);
  animation: float 8s ease-in-out infinite reverse;
}

.table-info-primary {
  position: relative;
  z-index: 2;
}

.table-number-display {
  display: flex;
  align-items: center;
  gap: 1.5rem;
  margin-bottom: 2rem;
  flex-wrap: wrap;
}

.table-number-display i {
  font-size: 3rem;
  color: #fbbf24;
  text-shadow: 
    0 0 20px rgba(251, 191, 36, 0.5),
    0 4px 8px rgba(0, 0, 0, 0.3);
  filter: drop-shadow(0 0 10px rgba(251, 191, 36, 0.4));
  animation: glow 3s ease-in-out infinite alternate;
}

.table-number {
  font-size: 2.2rem;
  font-weight: 800;
  text-shadow: 0 4px 8px rgba(0, 0, 0, 0.3);
  letter-spacing: -1px;
  background: linear-gradient(135deg, #ffffff, #f1f5f9);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.table-status-badge {
  padding: 1rem 2rem;
  border-radius: 30px;
  font-weight: 700;
  font-size: 1rem;
  display: flex;
  align-items: center;
  gap: 0.75rem;
  text-shadow: none;
  margin-right: auto;
  border: 2px solid rgba(255, 255, 255, 0.3);
  -webkit-backdrop-filter: blur(10px); /* Safari support */
  backdrop-filter: blur(10px);
  position: relative;
  overflow: hidden;
}

.table-status-badge.active {
  background: linear-gradient(135deg, #10b981 0%, #059669 50%, #047857 100%);
  color: white;
  box-shadow: 
    0 8px 25px rgba(16, 185, 129, 0.4),
    0 0 30px rgba(16, 185, 129, 0.2);
  animation: pulse-green 2s infinite;
}

.table-status-badge.active::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(45deg, rgba(255, 255, 255, 0.2) 0%, transparent 50%);
  animation: shimmer 3s infinite;
}

.table-status-badge.closed {
  background: linear-gradient(135deg, #ef4444 0%, #dc2626 50%, #b91c1c 100%);
  color: white;
  box-shadow: 
    0 8px 25px rgba(239, 68, 68, 0.4),
    0 0 30px rgba(239, 68, 68, 0.2);
}

.table-status-badge.closed::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(45deg, rgba(255, 255, 255, 0.1) 0%, transparent 50%);
}

.table-status-badge i {
  animation: pulse-icon 2s infinite;
  font-size: 1.1rem;
}

.table-summary {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(220px, 1fr));
  gap: 1.5rem;
  margin-top: 1rem;
}

.summary-item {
  display: flex;
  align-items: center;
  gap: 1rem;
  padding: 1.25rem;
  background: rgba(255, 255, 255, 0.12);
  border-radius: 16px;
  border: 1px solid rgba(255, 255, 255, 0.2);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  overflow: hidden;
}

.summary-item::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.1), transparent);
  transition: left 0.5s;
}

.summary-item:hover {
  background: rgba(255, 255, 255, 0.18);
  transform: translateY(-3px) scale(1.02);
  border-color: rgba(255, 255, 255, 0.3);
}

.summary-item:hover::before {
  left: 100%;
}

.summary-item i {
  color: #60a5fa;
  font-size: 1.4rem;
  min-width: 24px;
  text-shadow: 0 0 10px rgba(96, 165, 250, 0.5);
}

/* Financial Overview - Ultra Modern Cards */
.financial-overview {
  margin-bottom: 3rem;
}

.overview-cards {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(320px, 1fr));
  gap: 2rem;
}

.overview-card {
  background: linear-gradient(145deg, #ffffff 0%, #f8fafc 100%);
  border-radius: 24px;
  padding: 2.5rem;
  box-shadow: 
    0 12px 40px rgba(0, 0, 0, 0.08),
    0 4px 12px rgba(0, 0, 0, 0.04),
    inset 0 1px 0 rgba(255, 255, 255, 0.8);
  border: 1px solid rgba(226, 232, 240, 0.8);
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  overflow: hidden;
}

.overview-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 6px;
  background: linear-gradient(90deg, #3b82f6, #10b981, #f59e0b);
  transition: all 0.3s ease;
  border-radius: 24px 24px 0 0;
}

.overview-card::after {
  content: '';
  position: absolute;
  top: -50%;
  right: -50%;
  width: 100%;
  height: 100%;
  background: radial-gradient(circle, rgba(59, 130, 246, 0.05) 0%, transparent 70%);
  opacity: 0;
  transition: opacity 0.3s ease;
}

.overview-card:hover {
  transform: translateY(-8px) scale(1.02);
  box-shadow: 
    0 20px 60px rgba(0, 0, 0, 0.12),
    0 8px 20px rgba(0, 0, 0, 0.08),
    inset 0 1px 0 rgba(255, 255, 255, 0.9);
}

.overview-card:hover::before {
  height: 8px;
  box-shadow: 0 0 20px rgba(59, 130, 246, 0.4);
}

.overview-card:hover::after {
  opacity: 1;
}

.overview-card .card-icon {
  width: 80px;
  height: 80px;
  border-radius: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 2rem;
  font-size: 2.2rem;
  position: relative;
  overflow: hidden;
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

.overview-card .card-icon::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(45deg, rgba(255, 255, 255, 0.3) 0%, transparent 50%);
  animation: shimmer 3s infinite;
}

.current-amount .card-icon {
  background: linear-gradient(135deg, #f59e0b 0%, #d97706 50%, #92400e 100%);
  color: white;
  box-shadow: 0 8px 25px rgba(245, 158, 11, 0.4);
}

.total-sales .card-icon {
  background: linear-gradient(135deg, #10b981 0%, #059669 50%, #047857 100%);
  color: white;
  box-shadow: 0 8px 25px rgba(16, 185, 129, 0.4);
}

.orders-count .card-icon {
  background: linear-gradient(135deg, #3b82f6 0%, #2563eb 50%, #1d4ed8 100%);
  color: white;
  box-shadow: 0 8px 25px rgba(59, 130, 246, 0.4);
}

.card-content h4 {
  font-size: 1.2rem;
  color: #64748b;
  margin: 0 0 1rem 0;
  font-weight: 700;
  letter-spacing: -0.25px;
}

.card-content .amount,
.card-content .count {
  font-size: 2.5rem;
  font-weight: 900;
  color: #1e293b;
  margin: 0 0 1rem 0;
  line-height: 1;
  letter-spacing: -1px;
  background: linear-gradient(135deg, #1e293b 0%, #475569 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.card-content small {
  color: #94a3b8;
  font-size: 0.95rem;
  font-weight: 500;
  letter-spacing: 0.25px;
}

/* Detail Groups - Enhanced */
.detail-group {
  background: white;
  border-radius: 16px;
  padding: 2rem;
  margin-bottom: 2rem;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.06);
  border: 1px solid #f1f3f4;
  transition: all 0.3s ease;
}

.detail-group:hover {
  box-shadow: 0 8px 30px rgba(0, 0, 0, 0.1);
  transform: translateY(-2px);
}

.detail-group h4 {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  font-size: 1.3rem;
  color: #2c3e50;
  margin: 0 0 1.5rem 0;
  font-weight: 700;
  padding-bottom: 1rem;
  border-bottom: 2px solid #f8f9fa;
}

.detail-group h4 i {
  color: #3498db;
  font-size: 1.2rem;
}

/* Customer Information - Enhanced */
.customer-card {
  background: linear-gradient(135deg, #f8f9fa 0%, #ffffff 100%);
  border-radius: 12px;
  padding: 1.5rem;
  border: 1px solid #e9ecef;
}

.customer-details {
  display: grid;
  gap: 1rem;
}

.detail-row {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 1rem;
  background: white;
  border-radius: 10px;
  border: 1px solid #f1f3f4;
  transition: all 0.3s ease;
}

.detail-row:hover {
  background: #f8f9fa;
  border-color: #3498db;
}

.detail-row .label {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-weight: 600;
  color: #666;
  font-size: 0.95rem;
}

.detail-row .label i {
  color: #3498db;
  width: 16px;
}

.detail-row .value {
  font-weight: 500;
  color: #2c3e50;
  text-align: left;
}

.detail-row .value.waiter-name {
  background: linear-gradient(135deg, #3498db, #2980b9);
  color: white;
  padding: 0.5rem 1rem;
  border-radius: 20px;
  font-size: 0.9rem;
}

/* Orders Section - Enhanced */
.orders-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(320px, 1fr));
  gap: 1.5rem;
}

.order-card {
  background: white;
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.08);
  border: 1px solid #f1f3f4;
  transition: all 0.3s ease;
}

.order-card:hover {
  transform: translateY(-3px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.12);
}

.order-header {
  background: linear-gradient(135deg, #34495e 0%, #2c3e50 100%);
  color: white;
  padding: 1.5rem;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.order-number {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-weight: 600;
}

.order-status {
  padding: 0.5rem 1rem;
  border-radius: 20px;
  font-size: 0.85rem;
  font-weight: 600;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.order-status.status-pending {
  background: linear-gradient(135deg, #f39c12, #e67e22);
}

.order-status.status-preparing {
  background: linear-gradient(135deg, #e74c3c, #c0392b);
}

.order-status.status-ready {
  background: linear-gradient(135deg, #27ae60, #2ecc71);
}

.order-status.status-completed {
  background: linear-gradient(135deg, #95a5a6, #7f8c8d);
}

.order-body {
  padding: 1.5rem;
}

.order-info {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 1rem;
  margin-bottom: 1rem;
}

.info-item {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.75rem;
  background: #f8f9fa;
  border-radius: 8px;
  font-size: 0.9rem;
}

.info-item i {
  color: #3498db;
}

.order-amount {
  font-weight: 700;
  color: #27ae60;
}

.order-time {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-size: 0.85rem;
  color: #666;
  padding-top: 1rem;
  border-top: 1px solid #f1f3f4;
}

.order-time i {
  color: #3498db;
}

/* Timing Information - Enhanced */
.timing-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 1.5rem;
}

.timing-card {
  background: white;
  border: 1px solid #f1f3f4;
  border-radius: 12px;
  padding: 1.5rem;
  display: flex;
  align-items: center;
  gap: 1rem;
  transition: all 0.3s ease;
}

.timing-card:hover {
  border-color: #3498db;
  box-shadow: 0 4px 15px rgba(52, 152, 219, 0.1);
}

.timing-icon {
  width: 50px;
  height: 50px;
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.5rem;
  background: linear-gradient(135deg, #27ae60, #2ecc71);
  color: white;
}

.timing-icon.closed {
  background: linear-gradient(135deg, #e74c3c, #c0392b);
}

.timing-content h5 {
  margin: 0 0 0.5rem 0;
  font-size: 1rem;
  color: #666;
  font-weight: 600;
}

.timing-content p {
  margin: 0;
  font-weight: 500;
  color: #2c3e50;
}

/* Action Buttons - Modern Design */
.modal-actions {
  display: flex;
  gap: 1.5rem;
  padding: 2rem 0 1rem;
  border-top: 1px solid #e2e8f0;
  margin-top: 2rem;
  justify-content: center;
  flex-wrap: wrap;
}

.action-btn {
  padding: 1rem 2rem;
  border: none;
  border-radius: 16px;
  font-size: 1rem;
  font-weight: 600;
  display: flex;
  align-items: center;
  gap: 0.75rem;
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  overflow: hidden;
  letter-spacing: 0.25px;
  min-width: 180px;
  justify-content: center;
}

.action-btn::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left 0.5s;
}

.action-btn:hover::before {
  left: 100%;
}

.action-btn.close-table {
  background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%);
  color: white;
  box-shadow: 0 4px 15px rgba(239, 68, 68, 0.4);
}

.action-btn.close-table:hover {
  background: linear-gradient(135deg, #dc2626 0%, #b91c1c 100%);
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(239, 68, 68, 0.6);
}

.action-btn.refresh {
  background: linear-gradient(135deg, #3b82f6 0%, #2563eb 100%);
  color: white;
  box-shadow: 0 4px 15px rgba(59, 130, 246, 0.4);
}

.action-btn.refresh:hover {
  background: linear-gradient(135deg, #2563eb 0%, #1d4ed8 100%);
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(59, 130, 246, 0.6);
}

.action-btn:active {
  transform: translateY(0) scale(0.98);
}

.action-btn i {
  font-size: 1.1rem;
  transition: transform 0.3s ease;
}

.action-btn:hover i {
  transform: scale(1.1);
}

/* Improved Responsive for Action Buttons */
@media (max-width: 480px) {
  .modal-actions {
    flex-direction: column;
    align-items: center;
  }
  
  .action-btn {
    width: 100%;
    max-width: 280px;
  }
}

/* Responsive Design */
@media (max-width: 768px) {
  .table-details-modal {
    width: 98% !important;
    max-height: 95vh !important;
  }
  
  .table-details-modal .modal-header {
    padding: 1.5rem !important;
  }
  
  .table-details-modal .modal-body {
    padding: 1.5rem !important;
  }
  
  .table-header-card {
    padding: 1.5rem;
  }
  
  .table-number-display {
    flex-direction: column;
    align-items: flex-start;
  }
  
  .table-number {
    font-size: 1.5rem;
  }
  
  .overview-cards {
    grid-template-columns: 1fr;
  }
  
  .orders-grid {
    grid-template-columns: 1fr;
  }
  
  .timing-grid {
    grid-template-columns: 1fr;
  }
  
  .order-info {
    grid-template-columns: 1fr;
  }
}

@media (max-width: 480px) {
  .table-details-modal .modal-header h3 {
    font-size: 1.4rem !important;
  }
  
  .table-number {
    font-size: 1.3rem;
  }
  
  .detail-row {
    flex-direction: column;
    align-items: flex-start;
    gap: 0.5rem;
  }
  
  .order-header {
    flex-direction: column;
    gap: 1rem;
    text-align: center;
  }
}

/* Enhanced Animation Collection */
@keyframes pulse-icon {
  0%, 100% { 
    opacity: 1; 
    transform: scale(1);
  }
  50% { 
    opacity: 0.8; 
    transform: scale(1.1);
  }
}

@keyframes pulse-green {
  0%, 100% { 
    box-shadow: 
      0 8px 25px rgba(16, 185, 129, 0.4),
      0 0 30px rgba(16, 185, 129, 0.2);
  }
  50% { 
    box-shadow: 
      0 12px 35px rgba(16, 185, 129, 0.6),
      0 0 40px rgba(16, 185, 129, 0.4);
  }
}

@keyframes glow {
  0%, 100% {
    text-shadow: 
      0 0 20px rgba(251, 191, 36, 0.5),
      0 4px 8px rgba(0, 0, 0, 0.3);
    filter: drop-shadow(0 0 10px rgba(251, 191, 36, 0.4));
  }
  50% {
    text-shadow: 
      0 0 30px rgba(251, 191, 36, 0.8),
      0 4px 8px rgba(0, 0, 0, 0.3);
    filter: drop-shadow(0 0 20px rgba(251, 191, 36, 0.6));
  }
}

@keyframes shimmer {
  0% {
    transform: translateX(-100%) rotate(45deg);
  }
  100% {
    transform: translateX(200%) rotate(45deg);
  }
}

@keyframes float {
  0%, 100% {
    transform: translate(80px, -80px) rotate(0deg);
  }
  33% {
    transform: translate(90px, -70px) rotate(120deg);
  }
  66% {
    transform: translate(70px, -90px) rotate(240deg);
  }
}

@keyframes pulse {
  0%, 100% { opacity: 1; }
  50% { opacity: 0.7; }
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slideInRight {
  from {
    opacity: 0;
    transform: translateX(50px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes scaleIn {
  from {
    opacity: 0;
    transform: scale(0.8);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

.detail-group {
  animation: fadeInUp 0.8s ease-out;
  animation-fill-mode: backwards;
}

.detail-group:nth-child(1) { animation-delay: 0.1s; }
.detail-group:nth-child(2) { animation-delay: 0.2s; }
.detail-group:nth-child(3) { animation-delay: 0.3s; }
.detail-group:nth-child(4) { animation-delay: 0.4s; }
.detail-group:nth-child(5) { animation-delay: 0.5s; }

.overview-card {
  animation: scaleIn 0.8s cubic-bezier(0.4, 0, 0.2, 1);
  animation-fill-mode: backwards;
}

.overview-card:nth-child(1) { animation-delay: 0.1s; }
.overview-card:nth-child(2) { animation-delay: 0.2s; }
.overview-card:nth-child(3) { animation-delay: 0.3s; }

.order-card {
  animation: slideInRight 0.8s ease-out;
  animation-fill-mode: backwards;
}

.order-card:nth-child(odd) { animation-delay: 0.1s; }
.order-card:nth-child(even) { animation-delay: 0.2s; }

/* Enhanced Modal Entrance Animation */
.table-details-modal {
  animation: modalEnter 0.5s cubic-bezier(0.4, 0, 0.2, 1);
}

@keyframes modalEnter {
  from {
    opacity: 0;
    transform: scale(0.9) translateY(20px);
  }
  to {
    opacity: 1;
    transform: scale(1) translateY(0);
  }
}

/* Print Styles */
@media print {
  .table-details-modal .modal-close {
    display: none !important;
  }
  
  .table-details-modal {
    box-shadow: none !important;
    max-width: none !important;
    width: 100% !important;
  }
  
  .detail-group,
  .overview-card,
  .order-card {
    box-shadow: none !important;
    border: 1px solid #ddd !important;
  }
}

const express = require('express');
const { authenticateToken } = require('../middleware/auth');
const { 
  applyUnifiedMiddleware, 
  asyncHandler, 
  formatResponse, 
  formatError 
} = require('../middleware/unifiedRouteHandler');
const { 
  sendSuccess, 
  sendError, 
  sendNotFound, 
  handleDatabaseError 
} = require('../middleware/unifiedResponse');

const router = express.Router();

// Apply unified middleware
applyUnifiedMiddleware(router);

// Get settings placeholder
router.get('/', asyncHandler(async (req, res) => {
  const settings = {
    system: {
      name: 'Desha Coffee Management System',
      version: '1.0.1',
      language: 'ar'
    },
    business: {
      name: 'مقهى ديشا',
      currency: 'EGP',
      taxRate: 0.15
    }
  };

  return sendSuccess(res, settings, 'تم تحميل الإعدادات بنجاح');
}));

module.exports = router;

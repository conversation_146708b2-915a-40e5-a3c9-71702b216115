/* تنسيقات شاشة إدارة الفئات - تصميم محسن ومعاصر */

/* الشاشة الأساسية */
.categories-screen {
  padding: 2rem;
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
  min-height: 100vh;
  direction: rtl;
}

/* رأس القسم */
.categories-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 2rem;
  background: linear-gradient(135deg, #f39c12 0%, #e67e22 100%);
  color: white;
  padding: 2rem;
  border-radius: 20px;
  box-shadow: 0 10px 30px rgba(243, 156, 18, 0.3);
  border: none;
  position: relative;
  overflow: hidden;
}

.categories-header::before {
  content: '';
  position: absolute;
  top: -50%;
  left: -50%;
  width: 200%;
  height: 200%;
  background: linear-gradient(45deg, transparent, rgba(255,255,255,0.1), transparent);
  transform: rotate(45deg);
  animation: shimmer 3s infinite;
}

.categories-header h1 {
  color: white;
  margin: 0;
  font-size: 2.2rem;
  display: flex;
  align-items: center;
  gap: 1rem;
  font-weight: 700;
  text-shadow: 0 2px 4px rgba(0,0,0,0.3);
  position: relative;
  z-index: 1;
}

.categories-header h1 i {
  font-size: 2.5rem;
  background: linear-gradient(45deg, #ffffff, #ecf0f1);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  filter: drop-shadow(0 2px 4px rgba(0,0,0,0.3));
}

/* أزرار الإجراءات */
.categories-actions {
  display: flex;
  gap: 1rem;
  align-items: center;
}

.add-category-btn {
  background: linear-gradient(135deg, #f39c12 0%, #f1c40f 100%);
  color: white;
  border: none;
  padding: 1rem 1.5rem;
  border-radius: 10px;
  cursor: pointer;
  font-size: 1rem;
  font-weight: 600;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  transition: all 0.3s ease;
  box-shadow: 0 4px 15px rgba(243, 156, 18, 0.2);
}

.add-category-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(243, 156, 18, 0.3);
  background: linear-gradient(135deg, #e67e22 0%, #f39c12 100%);
}

/* شبكة الفئات */
.categories-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
  gap: 1.5rem;
  margin-bottom: 2rem;
}

/* بطاقة الفئة المحسنة */
.category-card {
  background: linear-gradient(145deg, #ffffff 0%, #f8f9fa 100%);
  border-radius: 20px;
  box-shadow: 0 10px 30px rgba(243, 156, 18, 0.15);
  transition: all 0.4s ease;
  border: none;
  overflow: hidden;
  position: relative;
  cursor: pointer;
}

.category-card::after {
  content: '';
  position: absolute;
  top: -2px;
  right: -2px;
  bottom: -2px;
  left: -2px;
  background: linear-gradient(45deg, #f39c12, #e67e22, #d35400);
  border-radius: 22px;
  z-index: -1;
  opacity: 0;
  transition: opacity 0.3s ease;
}

.category-card:hover {
  transform: translateY(-8px) scale(1.02);
  box-shadow: 0 20px 50px rgba(243, 156, 18, 0.3);
}

.category-card:hover::after {
  opacity: 1;
}

/* رأس الفئة المحسن */
.category-header {
  height: 120px;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
  background: linear-gradient(135deg, #f39c12, #e67e22);
  overflow: hidden;
}

.category-header::before {
  content: '';
  position: absolute;
  top: -50%;
  left: -50%;
  width: 200%;
  height: 200%;
  background: linear-gradient(45deg, transparent, rgba(255,255,255,0.2), transparent);
  transform: rotate(45deg);
  animation: categoryShimmer 3s infinite;
}

@keyframes categoryShimmer {
  0% { transform: translateX(-100%) translateY(-100%) rotate(45deg); }
  100% { transform: translateX(100%) translateY(100%) rotate(45deg); }
}

.category-icon {
  font-size: 3.5rem;
  color: white;
  filter: drop-shadow(0 4px 8px rgba(0,0,0,0.3));
  position: relative;
  z-index: 1;
}

.category-color-indicator {
  position: absolute;
  top: 1rem;
  right: 1rem;
  width: 20px;
  height: 20px;
  border-radius: 50%;
  border: 3px solid white;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
}

/* محتوى الفئة */
.category-content {
  padding: 1.5rem;
}

.category-name {
  color: #2c3e50;
  font-size: 1.3rem;
  font-weight: 600;
  margin-bottom: 0.5rem;
  text-align: center;
}

.category-description {
  color: #6c757d;
  font-size: 0.9rem;
  line-height: 1.4;
  margin-bottom: 1rem;
  text-align: center;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

/* إحصائيات الفئة */
.category-stats {
  display: flex;
  justify-content: space-around;
  margin-bottom: 1rem;
  padding: 1rem 0;
  background: #f8f9fa;
  border-radius: 8px;
}

.category-stat {
  text-align: center;
}

.stat-number {
  font-size: 1.5rem;
  font-weight: bold;
  color: #2c3e50;
}

.stat-label {
  font-size: 0.8rem;
  color: #6c757d;
}

/* حالة الفئة */
.category-status {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
  margin-bottom: 1rem;
}

.status-badge {
  padding: 0.3rem 0.8rem;
  border-radius: 15px;
  font-size: 0.8rem;
  font-weight: 600;
}

.status-badge.active {
  background: #d4edda;
  color: #155724;
  border: 1px solid #c3e6cb;
}

.status-badge.inactive {
  background: #f8d7da;
  color: #721c24;
  border: 1px solid #f5c6cb;
}

/* أزرار إجراءات الفئة */
.category-actions {
  display: flex;
  gap: 0.5rem;
}

.category-action-btn {
  flex: 1;
  padding: 0.6rem;
  border: none;
  border-radius: 6px;
  cursor: pointer;
  font-size: 0.8rem;
  font-weight: 600;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.3rem;
}

.edit-category-btn {
  background: #3498db;
  color: white;
}

.edit-category-btn:hover {
  background: #2980b9;
}

.delete-category-btn {
  background: #e74c3c;
  color: white;
}

.delete-category-btn:hover {
  background: #c0392b;
}

.toggle-category-btn {
  background: #95a5a6;
  color: white;
}

.toggle-category-btn:hover {
  background: #7f8c8d;
}

.toggle-category-btn.active {
  background: #27ae60;
}

.toggle-category-btn.active:hover {
  background: #229954;
}

/* إحصائيات الفئات العامة */
.categories-overview {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 1rem;
  margin-bottom: 2rem;
}

.overview-card {
  background: white;
  padding: 1.5rem;
  border-radius: 12px;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
  text-align: center;
  border-left: 4px solid #f39c12;
}

.overview-card.total {
  border-left-color: #3498db;
}

.overview-card.active {
  border-left-color: #27ae60;
}

.overview-card.inactive {
  border-left-color: #e74c3c;
}

.overview-card .overview-icon {
  font-size: 2.5rem;
  margin-bottom: 1rem;
  color: #f39c12;
}

.overview-card.total .overview-icon {
  color: #3498db;
}

.overview-card.active .overview-icon {
  color: #27ae60;
}

.overview-card.inactive .overview-icon {
  color: #e74c3c;
}

.overview-card .overview-number {
  font-size: 2rem;
  font-weight: bold;
  color: #2c3e50;
  margin-bottom: 0.5rem;
}

.overview-card .overview-label {
  color: #6c757d;
  font-size: 0.9rem;
}

/* مودال إضافة/تعديل فئة */
.category-modal .modal-header {
  background: linear-gradient(135deg, #f39c12, #f1c40f);
}

.category-modal .form-group {
  margin-bottom: 1.5rem;
}

.category-modal label {
  display: block;
  margin-bottom: 0.5rem;
  color: #495057;
  font-weight: 600;
}

.category-modal input,
.category-modal textarea,
.category-modal select {
  width: 100%;
  padding: 0.8rem;
  border: 2px solid #e9ecef;
  border-radius: 8px;
  font-size: 1rem;
  transition: border-color 0.3s ease;
  font-family: inherit;
}

.category-modal textarea {
  resize: vertical;
  min-height: 80px;
}

.category-modal input:focus,
.category-modal textarea:focus,
.category-modal select:focus {
  outline: none;
  border-color: #f39c12;
  box-shadow: 0 0 0 3px rgba(243, 156, 18, 0.1);
}

/* منتقي الألوان */
.color-picker-group {
  display: grid;
  grid-template-columns: repeat(8, 1fr);
  gap: 0.5rem;
  margin-top: 0.5rem;
}

.color-option {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  border: 3px solid transparent;
  cursor: pointer;
  transition: all 0.3s ease;
}

.color-option:hover {
  transform: scale(1.1);
  border-color: #2c3e50;
}

.color-option.selected {
  border-color: #2c3e50;
  transform: scale(1.1);
}

/* منتقي الأيقونات */
.icon-picker-group {
  display: grid;
  grid-template-columns: repeat(6, 1fr);
  gap: 0.5rem;
  margin-top: 0.5rem;
  max-height: 200px;
  overflow-y: auto;
}

.icon-option {
  width: 50px;
  height: 50px;
  border: 2px solid #e9ecef;
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.3s ease;
  font-size: 1.5rem;
  color: #6c757d;
}

.icon-option:hover {
  border-color: #f39c12;
  color: #f39c12;
  transform: scale(1.05);
}

.icon-option.selected {
  border-color: #f39c12;
  background: #fff9e6;
  color: #f39c12;
}

/* رسالة فارغة */
.empty-categories {
  text-align: center;
  padding: 4rem 2rem;
  color: #6c757d;
}

.empty-categories i {
  font-size: 4rem;
  margin-bottom: 1rem;
  color: #dee2e6;
}

.empty-categories h3 {
  color: #495057;
  margin-bottom: 1rem;
}

/* تنسيقات الإحصائيات العامة للفئات */
.categories-screen .stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 1.5rem;
  margin-bottom: 3rem;
  width: 100%;
  max-width: 100%;
  overflow-x: auto;
}

/* تنسيقات البحث للفئات */
.categories-screen .search-container {
  position: relative;
  display: flex;
  align-items: center;
  background: white;
  border: 2px solid #e9ecef;
  border-radius: 12px;
  overflow: hidden;
  transition: all 0.3s ease;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

.categories-screen .search-container:hover {
  border-color: #f39c12;
  box-shadow: 0 4px 15px rgba(243, 156, 18, 0.15);
}

.categories-screen .search-container:focus-within {
  border-color: #f39c12;
  box-shadow: 0 0 0 3px rgba(243, 156, 18, 0.1);
}

.categories-screen .search-icon {
  padding: 0.7rem 1rem;
  color: #6c757d;
  background: #f8f9fa;
  border-right: 1px solid #e9ecef;
}

.categories-screen .search-input {
  border: none;
  outline: none;
  padding: 0.7rem 1rem;
  font-size: 0.9rem;
  min-width: 200px;
  background: transparent;
}

.categories-screen .search-input::placeholder {
  color: #adb5bd;
  font-style: italic;
}

.categories-screen .clear-search {
  padding: 0.5rem;
  background: none;
  border: none;
  color: #6c757d;
  cursor: pointer;
  transition: color 0.2s ease;
}

.categories-screen .clear-search:hover {
  color: #e74c3c;
}

/* تنسيقات المرشحات للفئات */
.categories-screen .filter-select {
  padding: 0.7rem 1.2rem;
  border: 2px solid #e9ecef;
  border-radius: 12px;
  background: white;
  font-size: 0.9rem;
  font-weight: 500;
  min-width: 150px;
  color: #495057;
  transition: all 0.3s ease;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

.categories-screen .filter-select:hover {
  border-color: #f39c12;
  box-shadow: 0 4px 15px rgba(243, 156, 18, 0.15);
  transform: translateY(-1px);
}

.categories-screen .filter-select:focus {
  outline: none;
  border-color: #f39c12;
  box-shadow: 0 0 0 3px rgba(243, 156, 18, 0.1);
}

/* التصميم المتجاوب */
@media (max-width: 768px) {
  .categories-screen {
    padding: 1rem;
  }
  
  .categories-header {
    flex-direction: column;
    gap: 1rem;
    text-align: center;
  }
  
  .categories-header h1 {
    font-size: 1.5rem;
  }
  
  .categories-actions {
    flex-direction: column;
    width: 100%;
  }
  
  .add-category-btn {
    width: 100%;
    justify-content: center;
  }
  
  .categories-grid {
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 1rem;
  }
  
  .categories-overview {
    grid-template-columns: repeat(2, 1fr);
  }
  
  .color-picker-group {
    grid-template-columns: repeat(6, 1fr);
  }
  
  .icon-picker-group {
    grid-template-columns: repeat(4, 1fr);
  }
}

@media (max-width: 480px) {
  .categories-grid {
    grid-template-columns: 1fr;
  }
  
  .categories-overview {
    grid-template-columns: 1fr;
  }
  
  .category-actions {
    flex-direction: column;
    gap: 0.3rem;
  }
  
  .category-action-btn {
    width: 100%;
  }
  
  .color-picker-group {
    grid-template-columns: repeat(4, 1fr);
  }
  
  .icon-picker-group {
    grid-template-columns: repeat(3, 1fr);
  }
}

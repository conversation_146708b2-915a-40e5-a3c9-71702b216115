/* شاشة الإعدادات */
.settings-screen {
  padding: 2rem;
  max-width: 1200px;
  margin: 0 auto;
}

.settings-header {
  text-align: center;
  margin-bottom: 3rem;
}

.settings-header h1 {
  color: #2c3e50;
  font-size: 2.5rem;
  margin-bottom: 1rem;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 1rem;
}

.settings-header p {
  color: #7f8c8d;
  font-size: 1.1rem;
  max-width: 600px;
  margin: 0 auto;
}

/* أقسام الإعدادات */
.settings-section {
  background: white;
  border-radius: 12px;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
  margin-bottom: 2rem;
  overflow: hidden;
}

.section-header {
  background: linear-gradient(135deg, #3498db, #2980b9);
  color: white;
  padding: 1.5rem 2rem;
  border-bottom: 3px solid #2980b9;
}

.section-header h2 {
  margin: 0 0 0.5rem 0;
  font-size: 1.5rem;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.section-description {
  margin: 0;
  opacity: 0.9;
  font-size: 0.95rem;
}

/* بطاقات إعادة التهيئة */
.reset-controls {
  padding: 2rem;
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
  gap: 1.5rem;
}

.reset-card {
  border: 2px solid #ecf0f1;
  border-radius: 8px;
  padding: 1.5rem;
  background: #fafbfc;
  transition: all 0.3s ease;
  position: relative;
}

.reset-card:hover {
  border-color: #3498db;
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(52, 152, 219, 0.15);
}

.reset-card.danger {
  border-color: #e74c3c;
  background: #fdf2f2;
}

.reset-card.danger:hover {
  border-color: #c0392b;
  box-shadow: 0 4px 12px rgba(231, 76, 60, 0.2);
}

.reset-info h3 {
  margin: 0 0 0.75rem 0;
  color: #2c3e50;
  font-size: 1.2rem;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.reset-card.danger .reset-info h3 {
  color: #c0392b;
}

.reset-info p {
  margin: 0 0 1rem 0;
  color: #7f8c8d;
  line-height: 1.5;
}

.warning {
  background: #fff3cd;
  border: 1px solid #ffeaa7;
  border-radius: 4px;
  padding: 0.5rem 0.75rem;
  margin-bottom: 1rem;
  color: #856404;
  font-size: 0.85rem;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.danger-warning {
  background: #f8d7da;
  border: 1px solid #f5c6cb;
  border-radius: 4px;
  padding: 0.5rem 0.75rem;
  margin-bottom: 1rem;
  color: #721c24;
  font-size: 0.85rem;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-weight: 600;
}

/* أزرار إعادة التهيئة */
.reset-btn {
  width: 100%;
  padding: 0.75rem 1rem;
  border: none;
  border-radius: 6px;
  font-size: 1rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
  text-decoration: none;
}

.reset-orders-btn {
  background: linear-gradient(135deg, #3498db, #2980b9);
  color: white;
}

.reset-orders-btn:hover {
  background: linear-gradient(135deg, #2980b9, #1e6091);
  transform: translateY(-1px);
}

.reset-tables-btn {
  background: linear-gradient(135deg, #9b59b6, #8e44ad);
  color: white;
}

.reset-tables-btn:hover {
  background: linear-gradient(135deg, #8e44ad, #7d3c98);
  transform: translateY(-1px);
}

.reset-discounts-btn {
  background: linear-gradient(135deg, #f39c12, #e67e22);
  color: white;
}

.reset-discounts-btn:hover {
  background: linear-gradient(135deg, #e67e22, #d35400);
  transform: translateY(-1px);
}

.reset-all-btn {
  background: linear-gradient(135deg, #e74c3c, #c0392b);
  color: white;
  position: relative;
  overflow: hidden;
}

.reset-all-btn:hover {
  background: linear-gradient(135deg, #c0392b, #a93226);
  transform: translateY(-1px);
}

.reset-all-btn::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
  transition: left 0.5s ease;
}

.reset-all-btn:hover::before {
  left: 100%;
}

/* شبكة الإعدادات العامة */
.settings-grid {
  padding: 2rem;
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 1.5rem;
}

.setting-card {
  border: 2px solid #ecf0f1;
  border-radius: 8px;
  padding: 1.5rem;
  background: white;
  transition: all 0.3s ease;
  position: relative;
  opacity: 0.7;
}

.setting-card.coming-soon {
  background: #f8f9fa;
  border-color: #dee2e6;
}

.setting-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.setting-info h3 {
  margin: 0 0 0.75rem 0;
  color: #2c3e50;
  font-size: 1.1rem;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.setting-card.coming-soon .setting-info h3 {
  color: #6c757d;
}

.setting-info p {
  margin: 0;
  color: #7f8c8d;
  line-height: 1.5;
  font-size: 0.9rem;
}

.coming-soon-badge {
  position: absolute;
  top: 1rem;
  right: 1rem;
  background: #6c757d;
  color: white;
  padding: 0.25rem 0.5rem;
  border-radius: 12px;
  font-size: 0.75rem;
  font-weight: 600;
  display: flex;
  align-items: center;
  gap: 0.25rem;
}

/* تجاوب مع الشاشات الصغيرة */
@media (max-width: 768px) {
  .settings-screen {
    padding: 1rem;
  }
  
  .settings-header h1 {
    font-size: 2rem;
    flex-direction: column;
    gap: 0.5rem;
  }
  
  .reset-controls {
    grid-template-columns: 1fr;
    padding: 1rem;
  }
  
  .settings-grid {
    grid-template-columns: 1fr;
    padding: 1rem;
  }
  
  .section-header {
    padding: 1rem 1.5rem;
  }
}

@media (max-width: 480px) {
  .settings-screen {
    padding: 0.5rem;
  }
  
  .settings-header {
    margin-bottom: 2rem;
  }
  
  .settings-header h1 {
    font-size: 1.75rem;
  }
  
  .reset-card, .setting-card {
    padding: 1rem;
  }
}

import React from 'react';
// استيراد ملف CSS الخاص بشاشة السلة
import '../styles/waiter/WaiterCartScreen.css';

interface MenuItem extends CartItem {
  _id: string;
  name: string;
  price: number;
  description?: string;
  categories?: string[];
  categoryDetails?: any[];
  available: boolean;
  notes?: string;
}

interface CartItem {
  _id: string;
  name: string;
  price: number;
  quantity: number;
  notes?: string;
}

interface WaiterCartScreenProps {
  cart: CartItem[];
  tableNumber: string;
  customerName: string;
  loading: boolean;
  onTableNumberChange: (tableNumber: string) => void;
  onCustomerNameChange: (customerName: string) => void;
  onUpdateCartQuantity: (itemId: string, quantity: number) => void;
  onUpdateCartNotes: (itemIndex: number, notes: string) => void;
  onClearCart: () => void;
  onSubmitOrder: () => void;
  onChangeScreen: (screen: 'drinks' | 'orders' | 'tables' | 'cart' | 'discounts' | 'statistics') => void;
}

export default function WaiterCartScreen({
  cart,
  tableNumber,
  customerName,
  loading,
  onTableNumberChange,
  onCustomerNameChange,
  onUpdateCartQuantity,
  onUpdateCartNotes,
  onClearCart,
  onSubmitOrder,
  onChangeScreen
}: WaiterCartScreenProps) {
  return (
    <div className="content-container waiter-cart-screen">
      <div className="screen-header">
        <h1 className="screen-title">
          <i className="fas fa-shopping-cart"></i>
          سلة المشتريات ({cart.length})
        </h1>
        <p className="screen-subtitle">مراجعة وإدارة عناصر السلة</p>
      </div>

      {cart.length === 0 ? (
        <div className="empty-cart-state">
          <div className="empty-cart-icon">
            <i className="fas fa-shopping-cart"></i>
          </div>
          <h3>سلة المشتريات فارغة</h3>
          <p>لم تقم بإضافة أي عناصر إلى السلة بعد</p>
          <button 
            className="browse-menu-btn"
            onClick={() => onChangeScreen('drinks')}
          >
            <i className="fas fa-coffee"></i>
            تصفح المشروبات
          </button>
        </div>
      ) : (
        <div className="cart-content">
          <div className="cart-items">
            {cart.map((item, index) => (
              <div key={`${item._id}-${index}`} className="cart-item">
                <div className="cart-item-info">
                  <h4>{item.name}</h4>
                  <span className="cart-item-price">{item.price} جنيه</span>
                  
                  {/* ملاحظات للطباخ */}
                  <div className="cart-item-notes">
                    <label className="notes-label">
                      <i className="fas fa-sticky-note"></i>
                      ملاحظات للطباخ (اختياري):
                    </label>
                    <textarea
                      className="notes-input"
                      placeholder="أدخل ملاحظات خاصة للطباخ..."
                      value={item.notes || ''}
                      onChange={(e) => {
                        onUpdateCartNotes(index, e.target.value);
                      }}
                      rows={2}
                    />
                  </div>
                </div>
                
                <div className="cart-item-controls">
                  <div className="quantity-controls">
                    <button 
                      className="quantity-btn"
                      onClick={() => onUpdateCartQuantity(item._id, item.quantity - 1)}
                      aria-label="تقليل الكمية"
                    >
                      <i className="fas fa-minus"></i>
                    </button>
                    <span className="quantity">{item.quantity}</span>
                    <button 
                      className="quantity-btn"
                      onClick={() => onUpdateCartQuantity(item._id, item.quantity + 1)}
                      aria-label="زيادة الكمية"
                    >
                      <i className="fas fa-plus"></i>
                    </button>
                  </div>
                  
                  <button 
                    className="remove-item-btn"
                    onClick={() => onUpdateCartQuantity(item._id, 0)}
                    title="حذف العنصر"
                  >
                    <i className="fas fa-trash"></i>
                  </button>
                </div>
              </div>
            ))}
          </div>

          <div className="cart-form">
            <div className="form-group">
              <label>رقم الطاولة</label>
              <input 
                type="text" 
                value={tableNumber}
                onChange={(e) => onTableNumberChange(e.target.value)}
                placeholder="أدخل رقم الطاولة"
                required
              />
            </div>
            
            <div className="form-group">
              <label>اسم العميل (اختياري)</label>
              <input 
                type="text" 
                value={customerName}
                onChange={(e) => onCustomerNameChange(e.target.value)}
                placeholder="أدخل اسم العميل"
              />
            </div>
          </div>

          <div className="cart-total">
            <span>المجموع: {cart.reduce((sum, item) => sum + (item.price * item.quantity), 0)} جنيه</span>
          </div>

          <div className="cart-actions">
            <button className="clear-cart-btn" onClick={onClearCart} aria-label="مسح جميع عناصر السلة">
              <i className="fas fa-trash"></i>
              مسح السلة
            </button>
            <button 
              className="submit-order-btn"
              onClick={onSubmitOrder}
              disabled={loading || !tableNumber}
            >
              <i className="fas fa-paper-plane"></i>
              {loading ? 'جاري الإرسال...' : 'إرسال الطلب'}
            </button>
          </div>
        </div>
      )}
    </div>
  );
}

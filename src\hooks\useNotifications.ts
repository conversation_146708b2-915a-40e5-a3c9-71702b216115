import { useEffect, useCallback, useState } from 'react';
import socket from '../socket';
import { useToast } from './useToast';

export interface NotificationData {
  type: string;
  title: string;
  message: string;
  orderNumber?: string;
  waiterName?: string;
  chefName?: string;
  timestamp: string;
  data?: any;
}

export interface NotificationHookOptions {
  role: string;
  userName: string;
  onOrderUpdate?: () => void;
  onDiscountUpdate?: () => void;
}

export const useNotifications = (options: NotificationHookOptions) => {
  const { role, userName, onOrderUpdate, onDiscountUpdate } = options;
  const { showSuccess, showError, showWarning, showInfo } = useToast();
  const [isConnected, setIsConnected] = useState(false);
  const [notifications, setNotifications] = useState<NotificationData[]>([]);

  // تسجيل المستخدم في النظام
  const registerUser = useCallback(() => {
    const userData = { 
      userId: `${role}-${userName}`,
      role, 
      name: userName 
    };
    socket.emit('register-user', userData);
    console.log('Registering user with enhanced data:', userData);
  }, [role, userName]);

  // معالجة الإشعارات الواردة
  const handleNotification = useCallback((notification: NotificationData) => {
    console.log('Received notification:', notification);

    // إضافة الإشعار للقائمة
    setNotifications(prev => [notification, ...prev.slice(0, 49)]); // الاحتفاظ بآخر 50 إشعار

    // عرض التوست حسب نوع الإشعار
    switch (notification.type) {
      case 'new_order':
        showInfo(notification.message, 8000);
        if (onOrderUpdate) onOrderUpdate();
        break;

      case 'order_accepted':
        showSuccess(notification.message, 6000);
        if (onOrderUpdate) onOrderUpdate();
        break;

      case 'order_ready':
        showSuccess(notification.message, 10000);
        if (onOrderUpdate) onOrderUpdate();
        break;

      case 'discount_request':
        showWarning(notification.message, 8000);
        if (onDiscountUpdate) onDiscountUpdate();
        break;

      case 'discount_approved':
        showSuccess(notification.message, 6000);
        if (onDiscountUpdate) onDiscountUpdate();
        break;

      case 'discount_rejected':
        showError(notification.message, 6000);
        if (onDiscountUpdate) onDiscountUpdate();
        break;

      case 'order_status_update':
        showInfo(notification.message, 5000);
        if (onOrderUpdate) onOrderUpdate();
        break;

      case 'table_update':
        showInfo(notification.message, 4000);
        break;

      case 'table_conflict':
        showWarning(notification.message, 7000);
        break;

      case 'urgent':
        showError(notification.message, 10000);
        break;

      case 'role_specific':
        showInfo(notification.message, 6000);
        break;

      // User management notifications
      case 'user_login':
        showInfo(notification.message, 4000);
        break;

      case 'user_logout':
        showInfo(notification.message, 4000);
        break;

      case 'user_created':
        showSuccess(notification.message, 5000);
        break;

      case 'user_updated':
        showInfo(notification.message, 5000);
        break;

      case 'user_deleted':
        showWarning(notification.message, 5000);
        break;

      // Inventory management notifications
      case 'inventory_created':
        showSuccess(notification.message, 5000);
        break;

      case 'inventory_updated':
        showInfo(notification.message, 4000);
        break;

      case 'inventory_deleted':
        showWarning(notification.message, 5000);
        break;

      case 'stock_updated':
        showInfo(notification.message, 4000);
        break;

      case 'low_stock_alert':
        showWarning(notification.message, 8000);
        break;

      case 'out_of_stock_alert':
        showError(notification.message, 10000);
        break;

      // Category management notifications
      case 'category_created':
        showSuccess(notification.message, 5000);
        break;

      case 'category_updated':
        showInfo(notification.message, 4000);
        break;

      case 'category_deleted':
        showWarning(notification.message, 5000);
        break;

      // Product management notifications
      case 'product_created':
        showSuccess(notification.message, 5000);
        break;

      case 'product_updated':
        showInfo(notification.message, 4000);
        break;

      // Discount request notifications
      case 'discount_request_created':
        showWarning(notification.message, 8000);
        if (onDiscountUpdate) onDiscountUpdate();
        break;

      case 'discount_request_updated':
        showInfo(notification.message, 6000);
        if (onDiscountUpdate) onDiscountUpdate();
        break;

      case 'discount_request_processed':
        showSuccess(notification.message, 6000);
        if (onDiscountUpdate) onDiscountUpdate();
        break;

      default:
        showInfo(notification.message, 5000);
        break;
    }

    // تشغيل صوت الإشعار (اختياري)
    playNotificationSound(notification.type);
  }, [showSuccess, showError, showWarning, showInfo, onOrderUpdate, onDiscountUpdate]);

  // تشغيل صوت الإشعار
  const playNotificationSound = useCallback((type: string) => {
    try {
      // يمكن إضافة أصوات مختلفة حسب نوع الإشعار
      const audio = new Audio();

      switch (type) {
        case 'new_order':
          audio.src = '/sounds/new-order.mp3'; // يجب إضافة الملف الصوتي
          break;
        case 'order_ready':
          audio.src = '/sounds/order-ready.mp3';
          break;
        case 'discount_approved':
          audio.src = '/sounds/success.mp3';
          break;
        case 'discount_rejected':
          audio.src = '/sounds/error.mp3';
          break;
        default:
          audio.src = '/sounds/notification.mp3';
          break;
      }

      audio.volume = 0.5;
      audio.play().catch(() => {
        // تجاهل أخطاء تشغيل الصوت (قد تكون محظورة في المتصفح)
      });
    } catch (error) {
      // تجاهل أخطاء الصوت
    }
  }, []);

  // إعداد مستمعي الأحداث
  useEffect(() => {
    // التسجيل عند الاتصال
    registerUser();

    // مستمعي الاتصال
    socket.on('connect', () => {
      console.log('Socket connected');
      setIsConnected(true);
      registerUser();
    });

    socket.on('disconnect', () => {
      console.log('Socket disconnected');
      setIsConnected(false);
    });

    socket.on('registered', (data: any) => {
      console.log('User registered successfully:', data);
      setIsConnected(true);
    });

    // مستمعي الإشعارات الجديدة المحسنة
    socket.on('new-order-notification', (data: any) => {
      console.log('🔔 New order notification:', data);
      handleNotification({
        type: 'new_order',
        title: 'طلب جديد',
        message: data.message || `طلب جديد من الطاولة رقم ${data.tableNumber || 'غير محدد'} للعميل ${data.customer?.name || data.customer || 'غير محدد'}`,
        orderNumber: data.orderId,
        waiterName: data.waiterName,
        timestamp: data.timestamp,
        data: data
      });
    });

    socket.on('order-status-changed', (data: any) => {
      console.log('🔄 Order status changed:', data);
      const statusMessages = {
        'preparing': `بدأ تحضير الطلب من الطاولة رقم ${data.tableNumber || 'غير محدد'} للعميل ${data.customer?.name || data.customer || 'غير محدد'}`,
        'ready': `الطلب جاهز للتقديم من الطاولة رقم ${data.tableNumber || 'غير محدد'} للعميل ${data.customer?.name || data.customer || 'غير محدد'}`,
        'served': `تم تقديم الطلب من الطاولة رقم ${data.tableNumber || 'غير محدد'} للعميل ${data.customer?.name || data.customer || 'غير محدد'}`
      };
      handleNotification({
        type: 'order_status_update',
        title: 'تحديث حالة الطلب',
        message: data.message || statusMessages[data.newStatus] || `تحديث الطلب من الطاولة رقم ${data.tableNumber || 'غير محدد'} للعميل ${data.customer?.name || data.customer || 'غير محدد'}`,
        orderNumber: data.orderId,
        chefName: data.chefName,
        timestamp: data.timestamp,
        data: data
      });
    });

    socket.on('table-status-updated', (data: any) => {
      console.log('🪑 Table status updated:', data);
      handleNotification({
        type: 'table_update',
        title: 'تحديث حالة الطاولة',
        message: data.message || `تحديث الطاولة ${data.tableNumber}`,
        waiterName: data.waiterName,
        timestamp: data.timestamp,
        data: data
      });
    });

    socket.on('table-access-denied', (data: any) => {
      console.log('⚠️ Table access denied:', data);
      handleNotification({
        type: 'table_conflict',
        title: 'تعارض في الطاولة',
        message: data.message || `الطاولة ${data.tableNumber} مشغولة`,
        timestamp: data.timestamp,
        data: data
      });
    });

    socket.on('urgent-notification', (data: any) => {
      console.log('📢 Urgent notification:', data);
      handleNotification({
        type: 'urgent',
        title: 'إشعار عاجل',
        message: data.message,
        timestamp: data.timestamp,
        data: data
      });
    });

    socket.on('role-notification', (data: any) => {
      console.log('📢 Role notification:', data);
      
      // تحديد نوع الإشعار حسب البيانات
      let notificationType = 'info';
      let title = 'إشعار';
      
      switch (data.type) {
        case 'inventory-created':
          notificationType = 'inventory_created';
          title = 'صنف جديد';
          break;
        case 'inventory-updated':
          notificationType = 'inventory_updated';
          title = 'تحديث مخزون';
          break;
        case 'inventory-deleted':
          notificationType = 'inventory_deleted';
          title = 'حذف صنف';
          break;
        case 'stock-updated':
          notificationType = 'stock_updated';
          title = 'تحديث المخزون';
          break;
        case 'category-created':
          notificationType = 'category_created';
          title = 'فئة جديدة';
          break;
        case 'category-updated':
          notificationType = 'category_updated';
          title = 'تحديث فئة';
          break;
        case 'category-deleted':
          notificationType = 'category_deleted';
          title = 'حذف فئة';
          break;
        case 'product-created':
          notificationType = 'product_created';
          title = 'منتج جديد';
          break;
        case 'product-updated':
          notificationType = 'product_updated';
          title = 'تحديث منتج';
          break;
        case 'user-created':
          notificationType = 'user_created';
          title = 'مستخدم جديد';
          break;
        case 'user-updated':
          notificationType = 'user_updated';
          title = 'تحديث مستخدم';
          break;
        case 'user-deleted':
          notificationType = 'user_deleted';
          title = 'حذف مستخدم';
          break;
        case 'user-login':
          notificationType = 'user_login';
          title = 'تسجيل دخول';
          break;
        case 'user-logout':
          notificationType = 'user_logout';
          title = 'تسجيل خروج';
          break;
        case 'discount-request-created':
          notificationType = 'discount_request_created';
          title = 'طلب خصم جديد';
          break;
        case 'discount-request-updated':
          notificationType = 'discount_request_updated';
          title = 'تحديث طلب خصم';
          break;
        case 'discount-request-processed':
          notificationType = 'discount_request_processed';
          title = 'معالجة طلب خصم';
          break;
        default:
          notificationType = 'general';
          title = 'إشعار عام';
      }

      handleNotification({
        type: notificationType,
        title: title,
        message: data.message,
        timestamp: data.timestamp,
        data: data
      });
    });

    // إشعارات المخزون المنخفض والنافد
    socket.on('low-stock-alert', (data: any) => {
      console.log('⚠️ Low stock alert:', data);
      handleNotification({
        type: 'low_stock_alert',
        title: 'تحذير مخزون منخفض',
        message: data.message || `الصنف ${data.itemName} أصبح في المخزون المنخفض`,
        timestamp: data.timestamp,
        data: data
      });
    });

    socket.on('out-of-stock-alert', (data: any) => {
      console.log('🚫 Out of stock alert:', data);
      handleNotification({
        type: 'out_of_stock_alert',
        title: 'نفاد المخزون',
        message: data.message || `الصنف ${data.itemName} نفد من المخزون`,
        timestamp: data.timestamp,
        data: data
      });
    });

    // مستمعي الإشعارات القديمة (للتوافق)
    socket.on('newOrder', handleNotification);
    socket.on('orderAccepted', handleNotification);
    socket.on('orderReady', handleNotification);
    socket.on('newDiscountRequest', handleNotification);
    socket.on('discountRequestProcessed', handleNotification);

    // مستمعي التحديثات العامة (للتوافق مع النظام القديم)
    socket.on('orderUpdate', () => {
      if (onOrderUpdate) onOrderUpdate();
    });

    // تنظيف المستمعين عند إلغاء التحميل
    return () => {
      socket.off('connect');
      socket.off('disconnect');
      socket.off('registered');
      
      // إزالة المستمعين المحسنين
      socket.off('new-order-notification');
      socket.off('order-status-changed');
      socket.off('table-status-updated');
      socket.off('table-access-denied');
      socket.off('urgent-notification');
      socket.off('role-notification');
      
      // إزالة المستمعين القديمين
      socket.off('newOrder', handleNotification);
      socket.off('orderAccepted', handleNotification);
      socket.off('orderReady', handleNotification);
      socket.off('newDiscountRequest', handleNotification);
      socket.off('discountRequestProcessed', handleNotification);
      socket.off('orderUpdate');
    };
  }, [registerUser, handleNotification, onOrderUpdate]);

  // مسح الإشعارات
  const clearNotifications = useCallback(() => {
    setNotifications([]);
  }, []);

  // مسح إشعار محدد
  const removeNotification = useCallback((index: number) => {
    setNotifications(prev => prev.filter((_, i) => i !== index));
  }, []);

  // إحصائيات الإشعارات
  const notificationStats = {
    total: notifications.length,
    unread: notifications.length, // يمكن تحسينها لاحقاً لتتبع المقروءة
    byType: notifications.reduce((acc, notif) => {
      acc[notif.type] = (acc[notif.type] || 0) + 1;
      return acc;
    }, {} as Record<string, number>)
  };

  return {
    isConnected,
    notifications,
    notificationStats,
    clearNotifications,
    removeNotification,
    registerUser
  };
};

export default useNotifications;

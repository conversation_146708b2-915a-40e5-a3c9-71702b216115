@echo off
echo ===============================================
echo    DESHA COFFEE - LOCAL MONGODB SETUP
echo ===============================================
echo.

:: التحقق من وجود MongoDB
where mongod >nul 2>nul
if %errorlevel% neq 0 (
    echo ❌ MongoDB غير مثبت على النظام
    echo.
    echo 📥 لتثبيت MongoDB:
    echo 1. قم بتحميل MongoDB Community Server من:
    echo    https://www.mongodb.com/try/download/community
    echo.
    echo 2. أو استخدم Chocolatey:
    echo    choco install mongodb
    echo.
    echo 3. أو استخدم Winget:
    echo    winget install MongoDB.MongoDBCompass
    echo.
    pause
    exit /b 1
)

echo ✅ MongoDB مثبت على النظام

:: إنشاء مجلدات البيانات
if not exist "data" mkdir data
if not exist "data\db" mkdir data\db
if not exist "logs" mkdir logs

echo 📁 تم إنشاء مجلدات البيانات والسجلات

:: بدء تشغيل MongoDB
echo.
echo 🚀 بدء تشغيل MongoDB المحلي...
echo 📊 قاعدة البيانات: deshacoffee_local
echo 🌐 المنفذ: 27017
echo 📂 مجلد البيانات: %cd%\data\db
echo 📝 ملف السجل: %cd%\logs\mongodb.log
echo.
echo ⚠️  ملاحظة: اتركي هذه النافذة مفتوحة أثناء التطوير
echo.

:: تشغيل MongoDB مع الإعدادات المحلية
mongod --dbpath "%cd%\data\db" --logpath "%cd%\logs\mongodb.log" --port 27017 --bind_ip 127.0.0.1 --journal

echo.
echo ⛔ تم إيقاف MongoDB
pause

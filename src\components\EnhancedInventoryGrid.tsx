import React from 'react';


interface EnhancedInventoryGridProps {
  children: React.ReactNode;
  className?: string;
  minCardWidth?: number;
  maxColumns?: number;
  gap?: string;
  loading?: boolean;
  empty?: boolean;
  emptyMessage?: string;
}

const EnhancedInventoryGrid: React.FC<EnhancedInventoryGridProps> = ({
  children,
  className = '',
  minCardWidth = 300,
  maxColumns = 6,
  gap = '1.5rem',
  loading = false,
  empty = false,
  emptyMessage = 'لا توجد عناصر في المخزون'
}) => {
  if (loading) {
    return (
      <div className={`d-flex justify-content-center align-items-center min-vh-50 ${className}`}>
        <div className="loading-container">
          <div className="loading-spinner">
            <i className="fas fa-spinner fa-spin" aria-hidden="true"></i>
          </div>
          <p className="loading-text">جاري تحميل المخزون...</p>
        </div>
      </div>
    );
  }

  if (empty) {
    return (
      <div className={`d-flex justify-content-center align-items-center min-vh-75 ${className}`}>
        <div className="empty-container">
          <div className="empty-icon">
            <i className="fas fa-boxes" aria-hidden="true"></i>
          </div>
          <h3 className="empty-title">المخزون فارغ</h3>
          <p className="empty-message">{emptyMessage}</p>
          <button className="empty-action-btn" onClick={() => window.location.reload()}>
            <i className="fas fa-refresh" aria-hidden="true"></i>
            إعادة تحميل
          </button>
        </div>
      </div>
    );
  }

  return (
    <div 
      className={`row g-3 ${className}`}
      role="grid"
      aria-label="شبكة عناصر المخزون"
    >
      {React.Children.map(children, (child, index) => (
        <div 
          key={index} 
          className="col-12 col-sm-6 col-md-4 col-lg-3"
          role="gridcell"
          tabIndex={0}
        >
          {child}
        </div>
      ))}
    </div>
  );
};

export default EnhancedInventoryGrid;
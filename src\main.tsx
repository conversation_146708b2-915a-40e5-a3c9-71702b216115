// منع المكتبات الخارجية من إضافة Mutation Event Listeners
const originalAddEventListener = Element.prototype.addEventListener;
Element.prototype.addEventListener = function(type: string, listener: EventListenerOrEventListenerObject, options?: boolean | AddEventListenerOptions) {
  // قائمة Mutation Events المهجورة
  const deprecatedEvents = [
    'DOMNodeInserted',
    'DOMNodeRemoved',
    'DOMCharacterDataModified',
    'DOMSubtreeModified',
    'DOMNodeInsertedIntoDocument',
    'DOMNodeRemovedFromDocument',
    'DOMAttrModified'
  ];
  
  if (deprecatedEvents.includes(type)) {
    // تجاهل Mutation Events وعدم إضافتها
    console.debug(`🚫 تم منع إضافة Mutation Event: ${type}`);
    return;
  }
  
  // إضافة Events الأخرى بشكل طبيعي
  return originalAddEventListener.call(this, type, listener, options);
};

// منع إضافة Mutation Events على Document و Window أيضاً
const originalDocumentAddEventListener = Document.prototype.addEventListener;
const originalWindowAddEventListener = Window.prototype.addEventListener;

Document.prototype.addEventListener = function(type: string, listener: EventListenerOrEventListenerObject, options?: boolean | AddEventListenerOptions) {
  const deprecatedEvents = ['DOMNodeInserted', 'DOMNodeRemoved', 'DOMCharacterDataModified', 'DOMSubtreeModified', 'DOMNodeInsertedIntoDocument', 'DOMNodeRemovedFromDocument', 'DOMAttrModified'];
  if (deprecatedEvents.includes(type)) {
    console.debug(`🚫 تم منع Document Mutation Event: ${type}`);
    return;
  }
  return originalDocumentAddEventListener.call(this, type, listener, options);
};

Window.prototype.addEventListener = function(type: string, listener: EventListenerOrEventListenerObject, options?: boolean | AddEventListenerOptions) {
  const deprecatedEvents = ['DOMNodeInserted', 'DOMNodeRemoved', 'DOMCharacterDataModified', 'DOMSubtreeModified', 'DOMNodeInsertedIntoDocument', 'DOMNodeRemovedFromDocument', 'DOMAttrModified'];
  if (deprecatedEvents.includes(type)) {
    console.debug(`🚫 تم منع Window Mutation Event: ${type}`);
    return;
  }
  return originalWindowAddEventListener.call(this, type, listener, options);
};

import { StrictMode } from 'react';
import { createRoot } from 'react-dom/client';

// استيراد Font Awesome CSS محلياً كبديل
import '@fortawesome/fontawesome-free/css/all.min.css';
// استيراد Bootstrap
import 'bootstrap/dist/css/bootstrap.min.css';
import 'bootstrap/dist/js/bootstrap.bundle.min.js';

import App from './App';
import './theme.css'; // Import our theme first
import './index.css';

// ORGANIZED CSS ARCHITECTURE - النظام المنظم الجديد
// Essential component styles
import './components/Button.css';
import './components/Toast.css';
import './components/ThemeToggle.css';
import './components/SalesDiscrepancyFixer.css';
import './components/Modal.css';
import './components/Loading.css';
import './components/ErrorBoundary.css';
import './components/ConnectionStatus.css';
import './components/OrderDetails.css';
import './styles/components/IconFallback.css';


// Additional screen styles
import './styles/screens/LoginScreen.css';
import './styles/manager/HomeScreen.css';
import './styles/waiter/WaiterDashboardScreen.css';
import './styles/manager/EmployeesManagerScreen.css';
import './styles/manager/TablesManagerScreen.css';
import './styles/manager/ReportsManagerScreen.css';
import './styles/manager/MenuManagerScreen.css';
import './styles/manager/OrdersManagerScreen.css';
import './styles/manager/CategoriesManagerScreen.css';
import './styles/manager/SettingsManagerScreen.css';
import './styles/manager/DiscountRequestsManagerScreen.css';

import './styles/layout/ManagerDashboard.css';
import './styles/layout/NoHeaderLayout.css';
import './styles/components/ModalComponents.css';
import './styles/components/NavigationBarComponent.css';

// Layout styles (imported by components as needed)
// Screen styles (imported by components as needed)
// Modal and navigation styles (imported by components as needed)

// إخفاء تحذيرات DOM Mutation Events المهجورة والتحذيرات الأخرى
const originalConsoleWarn = console.warn;
const originalConsoleError = console.error;
const originalConsoleLog = console.log;

// قائمة الكلمات المفتاحية للتحذيرات المراد إخفاؤها
const suppressedWarnings = [
  'DOMNodeInserted',
  'DOMNodeRemoved', 
  'DOMCharacterDataModified',
  'DOMSubtreeModified',
  'mutation event',
  'Deprecation',
  'Support for this event type has been removed',
  'Listener added for a',
  'chromestatus.com/feature',
  'MutationEvent'
];

const shouldSuppressMessage = (message: string) => {
  return suppressedWarnings.some(warning => 
    message.toLowerCase().includes(warning.toLowerCase())
  );
};

console.warn = (...args: any[]) => {
  const message = args.join(' ');
  if (shouldSuppressMessage(message)) {
    return; // إخفاء هذه التحذيرات
  }
  originalConsoleWarn.apply(console, args);
};

console.error = (...args: any[]) => {
  const message = args.join(' ');
  if (shouldSuppressMessage(message)) {
    return; // إخفاء هذه الأخطاء
  }
  originalConsoleError.apply(console, args);
};

console.log = (...args: any[]) => {
  const message = args.join(' ');
  if (shouldSuppressMessage(message)) {
    return; // إخفاء هذه الرسائل
  }
  originalConsoleLog.apply(console, args);
};

// إلغاء تسجيل جميع Service Workers لحل مشكلة التداخل
if ('serviceWorker' in navigator) {
  window.addEventListener('load', () => {
    // إلغاء تسجيل جميع Service Workers الموجودة
    navigator.serviceWorker.getRegistrations().then(function(registrations) {
      for(let registration of registrations) {
        registration.unregister().then(() => {
          console.log('Service Worker unregistered successfully');
        });
      }
    });

    // مسح جميع الكاش
    if ('caches' in window) {
      caches.keys().then(function(names) {
        for (let name of names) {
          caches.delete(name);
        }
      });
    }
  });
}

createRoot(document.getElementById('root')!).render(
  <StrictMode>
    <App />
  </StrictMode>
);

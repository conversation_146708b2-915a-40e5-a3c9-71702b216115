import React from 'react';
// استيراد ملف CSS الخاص بشاشة الطلبات
import '../styles/waiter/WaiterOrdersScreen.css';

interface OrderItem {
  product: string;
  name: string;
  quantity: number;
  price: number;
  notes?: string;
  id?: string;
}

interface Order {
  _id: string;
  orderNumber: string;
  waiterName: string;
  waiterId?: string;
  chefName?: string;
  chefId?: string;
  items: OrderItem[];
  totalPrice: number;
  tableNumber: string;
  customerName?: string;
  status: 'pending' | 'preparing' | 'ready' | 'delivered' | 'cancelled';
  createdAt: string;
  tableAccountId?: string;
  discountStatus?: 'pending' | 'approved' | 'rejected';
  discountAmount?: number;
  discountApplied?: number;
  discountReason?: string;
}

interface WaiterOrdersScreenProps {
  orders: Order[];
  orderStatusFilter: 'all' | 'pending' | 'preparing' | 'ready' | 'delivered';
  orderSearchTerm: string;
  currentWaiterId: string;
  onStatusFilterChange: (status: 'all' | 'pending' | 'preparing' | 'ready' | 'delivered') => void;
  onSearchTermChange: (term: string) => void;
  onOrderDetails: (order: Order) => void;
  onMarkOrderDelivered: (orderId: string) => void;
  onRequestDiscount: (order: Order) => void;
  onRefreshOrders: () => void;
}

const getStatusIcon = (status: string): string => {
  const iconMap: { [key: string]: string } = {
    'pending': 'fa-clock',
    'preparing': 'fa-fire',
    'ready': 'fa-check-circle',
    'delivered': 'fa-box',
    'cancelled': 'fa-times-circle',
    'all': 'fa-list'
  };
  return iconMap[status] || 'fa-clock';
};

const getStatusText = (status: string): string => {
  const textMap: { [key: string]: string } = {
    'pending': 'في الانتظار',
    'preparing': 'قيد التحضير',
    'ready': 'جاهز',
    'delivered': 'تم التسليم',
    'cancelled': 'ملغي',
    'all': 'الكل'
  };
  return textMap[status] || status;
};

const formatDate = (dateString: string): string => {
  const date = new Date(dateString);
  return date.toLocaleString('ar-EG', {
    year: 'numeric',
    month: 'short',
    day: 'numeric',
    hour: '2-digit',
    minute: '2-digit'
  });
};

export default function WaiterOrdersScreen({
  orders,
  orderStatusFilter,
  orderSearchTerm,
  currentWaiterId,
  onStatusFilterChange,
  onSearchTermChange,
  onOrderDetails,
  onMarkOrderDelivered,
  onRequestDiscount,
  onRefreshOrders
}: WaiterOrdersScreenProps) {
  console.log('🔍 تصفية الطلبات في العرض:', {
    totalOrders: orders.length,
    currentWaiterId: currentWaiterId,
    orderStatusFilter: orderStatusFilter
  });

  // تصفية الطلبات للنادل الحالي فقط باستخدام waiterId
  const waiterOrders = orders.filter(order => {
    const isMatch = order.waiterId === currentWaiterId;
    return isMatch;
  });

  console.log('📋 طلبات النادل الحالي:', {
    waiterOrdersCount: waiterOrders.length,
    orderStatusFilter: orderStatusFilter
  });

  // تصفية الطلبات حسب الحالة
  const filteredByStatus = orderStatusFilter === 'all' 
    ? waiterOrders 
    : waiterOrders.filter(order => order.status === orderStatusFilter);

  // تطبيق البحث
  let filteredOrders = filteredByStatus;
  if (orderSearchTerm && orderSearchTerm.trim() !== '') {
    const searchTerm = orderSearchTerm.toLowerCase().trim();
    filteredOrders = filteredByStatus.filter(order => 
      order.orderNumber?.toLowerCase().includes(searchTerm) ||
      order.tableNumber?.toLowerCase().includes(searchTerm) ||
      order.customerName?.toLowerCase().includes(searchTerm) ||
      order._id.toLowerCase().includes(searchTerm)
    );
  }

  console.log('📋 النتيجة النهائية للتصفية:', {
    waiterOrdersCount: waiterOrders.length,
    orderStatusFilter: orderStatusFilter,
    filteredOrdersCount: filteredOrders.length
  });

  return (
    <div className="content-container waiter-orders-screen">
      <div className="screen-header">
        <div className="action-buttons-flex">
          <h1 className="screen-title">
            <i className="fas fa-receipt"></i>
            إدارة الطلبات
          </h1>
          <button
            className="btn-refresh action-btn-primary"
            title="تحديث الطلبات"
            onClick={() => onRefreshOrders()}
          >
            <i className="fas fa-sync-alt"></i> تحديث
          </button>
        </div>
        <p className="screen-subtitle">تتبع ومتابعة جميع الطلبات</p>
      </div>

      <div className="filter-section">
        <div className="search-section">
          <div className="search-container">
            <i className="fas fa-search search-icon"></i>
            <input
              type="text"
              placeholder="البحث برقم الطلب..."
              value={orderSearchTerm}
              onChange={(e) => onSearchTermChange(e.target.value)}
              className="search-input"
            />
            {orderSearchTerm && (
              <button
                className="clear-search"
                onClick={() => onSearchTermChange('')}
                title="مسح البحث"
              >
                <i className="fas fa-times"></i>
              </button>
            )}
          </div>
        </div>
        
        <div className="status-filters">
          {['all', 'pending', 'preparing', 'ready', 'delivered'].map(status => (
            <button
              key={status}
              className={`status-filter ${orderStatusFilter === status ? 'active' : ''}`}
              onClick={() => onStatusFilterChange(status as any)}
            >
              <i className={`fas ${getStatusIcon(status)}`}></i>
              <span>{getStatusText(status)}</span>
              <span className="count">({status === 'all' ? waiterOrders.length : waiterOrders.filter(o => o.status === status).length})</span>
            </button>
          ))}
        </div>
      </div>

      <div className="orders-list">
        {filteredOrders.length === 0 ? (
          <div className="empty-state">
            <i className="fas fa-receipt empty-icon"></i>
            <h3>لا توجد طلبات</h3>
            <p>لم يتم العثور على طلبات مطابقة للفلتر المحدد</p>
          </div>
        ) : (
          <div className="orders-grid">
            {filteredOrders.map(order => (
              <div key={order._id} className="order-card">
                <div className="order-header">
                  <div className="order-number">
                    <i className="fas fa-receipt"></i>
                    طلب #{order.orderNumber || order._id?.slice(-6) || 'غير محدد'}
                  </div>
                  <div className={`order-status ${order.status}`}>
                    <i className={`fas ${getStatusIcon(order.status)}`}></i>
                    {getStatusText(order.status)}
                  </div>
                </div>

                <div className="order-info">
                  <div className="info-row">
                    <span className="label">
                      <i className="fas fa-table"></i>
                      الطاولة:
                    </span>
                    <span className="value">{order.tableNumber}</span>
                  </div>
                  <div className="info-row">
                    <span className="label">
                      <i className="fas fa-user"></i>
                      العميل:
                    </span>
                    <span className="value">{order.customerName || 'غير محدد'}</span>
                  </div>
                  
                  {order.chefName && (
                    <div className="info-row chef-info">
                      <span className="label">
                        <i className="fas fa-user-tie"></i>
                        الطباخ:
                      </span>
                      <span className="value chef-name-card">{order.chefName}</span>
                    </div>
                  )}
                  
                  <div className="info-row">
                    <span className="label">
                      <i className="fas fa-money-bill-wave"></i>
                      المبلغ:
                    </span>
                    <span className="value">{order.totalPrice} جنيه</span>
                  </div>
                  
                  <div className="info-row">
                    <span className="label">
                      <i className="fas fa-clock"></i>
                      الوقت:
                    </span>
                    <span className="value">{formatDate(order.createdAt)}</span>
                  </div>
                  
                  {/* عرض تفاصيل الخصم إذا كان موجوداً */}
                  {order.discountStatus && (
                    <div className="discount-info">
                      <div className="info-row">
                        <span className="label">
                          <i className="fas fa-percentage"></i>
                          حالة الخصم:
                        </span>
                        <span className={`discount-status-badge ${order.discountStatus}`}>
                          {order.discountStatus === 'pending' && 'في الانتظار'}
                          {order.discountStatus === 'approved' && 'تم الموافقة'}
                          {order.discountStatus === 'rejected' && 'تم الرفض'}
                        </span>
                      </div>
                      
                      {order.discountAmount && (
                        <div className="info-row">
                          <span className="label">
                            <i className="fas fa-money-bill-wave"></i>
                            مبلغ الخصم:
                          </span>
                          <span className="value discount-amount">{order.discountAmount} جنيه</span>
                        </div>
                      )}
                      
                      {order.discountReason && (
                        <div className="info-row discount-reason">
                          <span className="label">
                            <i className="fas fa-comment"></i>
                            سبب الخصم:
                          </span>
                          <span className="value">{order.discountReason}</span>
                        </div>
                      )}
                    </div>
                  )}
                </div>

                <div className="order-actions">
                  <button 
                    className="btn-details"
                    onClick={() => onOrderDetails(order)}
                    title="عرض تفاصيل الطلب"
                  >
                    <i className="fas fa-eye"></i>
                    التفاصيل
                  </button>
                  
                  {order.status === 'ready' && (
                    <button 
                      className="btn-deliver"
                      onClick={() => onMarkOrderDelivered(order._id)}
                      title="تأكيد التسليم"
                    >
                      <i className="fas fa-check"></i>
                      تم التسليم
                    </button>
                  )}
                  
                  {!order.discountStatus && ['pending', 'preparing', 'ready'].includes(order.status) && (
                    <button 
                      className="btn-discount"
                      onClick={() => onRequestDiscount(order)}
                      title="طلب خصم"
                    >
                      <i className="fas fa-percentage"></i>
                      طلب خصم
                    </button>
                  )}
                </div>
              </div>
            ))}
          </div>
        )}
      </div>
    </div>
  );
}

# Unified Coffee Shop Backend Deployment Script for Windows
# سكريبت نشر النظام الموحد لإدارة المقهى - ويندوز

Write-Host "🚀 بدء تطبيق النظام الموحد..." -ForegroundColor Green
Write-Host "🚀 Starting Unified Coffee Shop System..." -ForegroundColor Green

# Function to print colored output
function Write-Success {
    param($Message)
    Write-Host "✅ $Message" -ForegroundColor Green
}

function Write-Warning {
    param($Message)  
    Write-Host "⚠️ $Message" -ForegroundColor Yellow
}

function Write-Error {
    param($Message)
    Write-Host "❌ $Message" -ForegroundColor Red
}

function Write-Info {
    param($Message)
    Write-Host "ℹ️ $Message" -ForegroundColor Blue
}

# Check if we're in the right directory
if (!(Test-Path "package.json")) {
    Write-Error "package.json not found. Please run this script from the backend directory."
    exit 1
}

Write-Info "النظام الموحد لإدارة المقهى | Unified Coffee Shop Management System"
Write-Info "الإصدار 1.0.1 | Version 1.0.1"
Write-Host ""

# Step 1: Install dependencies
Write-Info "تثبيت التبعيات المطلوبة | Installing required dependencies..."

try {
    npm install joi multer sharp winston node-cron --save
    if ($LASTEXITCODE -eq 0) {
        Write-Success "تم تثبيت التبعيات بنجاح | Dependencies installed successfully"
    } else {
        throw "npm install failed"
    }
} catch {
    Write-Error "فشل في تثبيت التبعيات | Failed to install dependencies"
    exit 1
}

# Step 2: Backup original files
Write-Info "إنشاء نسخ احتياطية | Creating backups..."

if (Test-Path "server.js") {
    Copy-Item "server.js" "server_original_backup.js"
    Write-Success "تم إنشاء نسخة احتياطية من server.js | Backed up server.js"
}

# Create backup directory for routes
if (!(Test-Path "routes_backup")) {
    New-Item -ItemType Directory -Path "routes_backup"
}

if (Test-Path "routes") {
    Copy-Item "routes\*" "routes_backup\" -Recurse -Force
    Write-Success "تم إنشاء نسخة احتياطية من المسارات | Backed up routes"
}

# Step 3: Replace server.js with unified version
Write-Info "تطبيق النظام الموحد | Applying unified system..."

if (Test-Path "server_unified.js") {
    if (Test-Path "server.js") {
        Copy-Item "server.js" "server_legacy.js"
    }
    Copy-Item "server_unified.js" "server.js"
    Write-Success "تم تطبيق الخادم الموحد | Applied unified server"
} else {
    Write-Error "ملف server_unified.js غير موجود | server_unified.js not found"
    exit 1
}

# Step 4: Update routes
Write-Info "تحديث المسارات | Updating routes..."

# Replace products route
if (Test-Path "routes\products_unified.js") {
    if (Test-Path "routes\products.js") {
        Copy-Item "routes\products.js" "routes\products_legacy.js"
    }
    Copy-Item "routes\products_unified.js" "routes\products.js"
    Write-Success "تم تحديث مسار المنتجات | Updated products route"
}

# Replace orders route
if (Test-Path "routes\orders_unified.js") {
    if (Test-Path "routes\orders.js") {
        Copy-Item "routes\orders.js" "routes\orders_legacy.js"
    }
    Copy-Item "routes\orders_unified.js" "routes\orders.js"
    Write-Success "تم تحديث مسار الطلبات | Updated orders route"
}

# Replace categories route
if (Test-Path "routes\categories_unified.js") {
    if (Test-Path "routes\categories.js") {
        Copy-Item "routes\categories.js" "routes\categories_legacy.js"
    }
    Copy-Item "routes\categories_unified.js" "routes\categories.js"
    Write-Success "تم تحديث مسار التصنيفات | Updated categories route"
}

# Step 5: Test the unified system
Write-Info "اختبار النظام الموحد | Testing unified system..."

# Check if Node.js is available
try {
    $nodeVersion = node --version
    Write-Success "Node.js متاح | Node.js available: $nodeVersion"
} catch {
    Write-Error "Node.js غير مثبت | Node.js not installed"
    exit 1
}

# Test syntax
try {
    node -c server.js
    Write-Success "تم التحقق من صحة الكود | Code syntax validated"
} catch {
    Write-Error "خطأ في صيغة الكود | Code syntax error"
    exit 1
}

# Step 6: Create startup scripts
Write-Info "إنشاء سكريبتات التشغيل | Creating startup scripts..."

# Create start script
$startScript = @'
@echo off
echo 🚀 تشغيل النظام الموحد | Starting Unified System...
echo 📱 محسن للهاتف المحمول | Mobile Optimized
echo 🌐 منافذ الوصول | Access Points:
echo    - Main: http://localhost:3001
echo    - Health: http://localhost:3001/health
echo    - API Docs: http://localhost:3001/api/docs
echo    - Products: http://localhost:3001/api/v1/products
echo.
node server.js
'@

$startScript | Out-File -FilePath "start_unified.bat" -Encoding ASCII

# Create development script
$devScript = @'
@echo off
echo 🛠️ تشغيل النظام في وضع التطوير | Starting in Development Mode...
echo 📱 محسن للهاتف المحمول | Mobile Optimized
echo 🔄 إعادة التشغيل التلقائي | Auto-restart enabled
echo.
where nodemon >nul 2>nul
if %ERRORLEVEL% EQU 0 (
    nodemon server.js
) else (
    echo ⚠️ nodemon غير مثبت، التشغيل العادي | nodemon not installed, using normal node
    node server.js
)
'@

$devScript | Out-File -FilePath "dev_unified.bat" -Encoding ASCII

Write-Success "تم إنشاء سكريبتات التشغيل | Created startup scripts"

# Step 7: Final checks
Write-Info "الفحوصات النهائية | Final checks..."

$requiredFiles = @(
    "server.js",
    "middleware\unifiedRouteHandler.js",
    "middleware\enhancedMobileCompatibility.js",
    "middleware\unifiedAPIManager.js", 
    "config\unifiedConfig.js",
    "routes\products.js",
    "routes\orders.js",
    "routes\categories.js"
)

$allFilesExist = $true
foreach ($file in $requiredFiles) {
    if (Test-Path $file) {
        Write-Success "✓ $file موجود | exists"
    } else {
        Write-Error "✗ $file مفقود | missing"
        $allFilesExist = $false
    }
}

if ($allFilesExist) {
    Write-Success "جميع الملفات المطلوبة موجودة | All required files exist"
} else {
    Write-Error "بعض الملفات مفقودة | Some files are missing"
    exit 1
}

# Step 8: Success message and instructions
Write-Host ""
Write-Host "🎉================================🎉"
Write-Success "تم تطبيق النظام الموحد بنجاح!"
Write-Success "Unified System Applied Successfully!"
Write-Host "🎉================================🎉"
Write-Host ""

Write-Info "طرق التشغيل | How to run:"
Write-Host "  📱 للتطوير | Development:    .\dev_unified.bat"
Write-Host "  🚀 للإنتاج | Production:     .\start_unified.bat"
Write-Host "  📦 NPM:                      npm run dev:unified"
Write-Host ""

Write-Info "نقاط الوصول | Access Points:"
Write-Host "  🌐 الخادم الرئيسي | Main Server:     http://localhost:3001"
Write-Host "  ❤️ فحص الصحة | Health Check:        http://localhost:3001/health"
Write-Host "  📚 وثائق API | API Documentation:    http://localhost:3001/api/docs"
Write-Host "  🛍️ المنتجات | Products API:         http://localhost:3001/api/v1/products"
Write-Host "  📋 الطلبات | Orders API:            http://localhost:3001/api/v1/orders"
Write-Host ""

Write-Info "الميزات الجديدة | New Features:"
Write-Host "  📱 محسن للهاتف المحمول | Mobile Optimized"
Write-Host "  🔄 نظام API موحد | Unified API System"
Write-Host "  🛡️ معالجة أخطاء محسنة | Enhanced Error Handling"
Write-Host "  📊 تتبع الطلبات | Request Tracking"
Write-Host "  🌍 دعم متعدد الأجهزة | Multi-device Support"
Write-Host ""

Write-Warning "ملاحظات مهمة | Important Notes:"
Write-Host "  - تم إنشاء نسخ احتياطية من الملفات الأصلية"
Write-Host "  - Original files backed up with _legacy suffix"
Write-Host "  - يرجى اختبار النظام قبل النشر في الإنتاج"
Write-Host "  - Please test the system before production deployment"
Write-Host ""

Write-Info "للحصول على المساعدة | For help:"
Write-Host "  📖 راجع ملف UNIFIED_BACKEND_IMPLEMENTATION_GUIDE.md"
Write-Host "  📖 Check UNIFIED_BACKEND_IMPLEMENTATION_GUIDE.md"
Write-Host ""

Write-Success "النظام جاهز للتشغيل! | System ready to run!"
Write-Host "🚀 .\dev_unified.bat للبدء | Run .\dev_unified.bat to start" -ForegroundColor Green

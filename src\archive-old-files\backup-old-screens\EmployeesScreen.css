/* تنسيقات شاشة إدارة الموظفين - تصميم محسن ومعاصر */

/* الشاشة الأساسية */
.employees-screen {
  padding: 2rem;
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
  min-height: 100vh;
  direction: rtl;
}

/* رأس القسم */
.employees-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 2rem;
  background: linear-gradient(135deg, #3498db 0%, #2980b9 100%);
  color: white;
  padding: 2rem;
  border-radius: 20px;
  box-shadow: 0 10px 30px rgba(52, 152, 219, 0.3);
  border: none;
  position: relative;
  overflow: hidden;
}

.employees-header::before {
  content: '';
  position: absolute;
  top: -50%;
  left: -50%;
  width: 200%;
  height: 200%;
  background: linear-gradient(45deg, transparent, rgba(255,255,255,0.1), transparent);
  transform: rotate(45deg);
  animation: shimmer 3s infinite;
}

@keyframes shimmer {
  0% { transform: translateX(-100%) translateY(-100%) rotate(45deg); }
  100% { transform: translateX(100%) translateY(100%) rotate(45deg); }
}

.employees-header h1 {
  color: white;
  margin: 0;
  font-size: 2.2rem;
  display: flex;
  align-items: center;
  gap: 1rem;
  font-weight: 700;
  text-shadow: 0 2px 4px rgba(0,0,0,0.3);
  position: relative;
  z-index: 1;
}

.employees-header h1 i {
  font-size: 2.5rem;
  background: linear-gradient(45deg, #ffffff, #ecf0f1);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  filter: drop-shadow(0 2px 4px rgba(0,0,0,0.3));
}

/* زر إضافة موظف محسن */
.add-employee-btn {
  background: linear-gradient(135deg, #2ecc71 0%, #27ae60 100%);
  color: white;
  border: none;
  border-radius: 15px;
  padding: 1rem 2rem;
  font-size: 1rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: 0 8px 25px rgba(46, 204, 113, 0.4);
  display: flex;
  align-items: center;
  gap: 0.8rem;
  position: relative;
  overflow: hidden;
  z-index: 1;
}

.add-employee-btn::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
  transition: left 0.5s ease;
}

.add-employee-btn:hover::before {
  left: 100%;
}

.add-employee-btn:hover {
  background: linear-gradient(135deg, #27ae60 0%, #2ecc71 100%);
  transform: translateY(-3px);
  box-shadow: 0 12px 35px rgba(46, 204, 113, 0.6);
}

.add-employee-btn i {
  font-size: 1.1rem;
}

/* شبكة الموظفين */
.employees-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(320px, 1fr));
  gap: 1.5rem;
  margin-bottom: 2rem;
}

/* بطاقة الموظف المحسنة */
.employee-card {
  background: linear-gradient(145deg, #ffffff 0%, #f8f9fa 100%);
  border-radius: 20px;
  padding: 2rem;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
  transition: all 0.4s ease;
  border: none;
  position: relative;
  overflow: hidden;
  -webkit-backdrop-filter: blur(10px);
  backdrop-filter: blur(10px);
}

.employee-card::before {
  content: '';
  position: absolute;
  top: 0;
  right: 0;
  left: 0;
  height: 5px;
  background: linear-gradient(90deg, #3498db 0%, #2ecc71 25%, #f39c12 50%, #e74c3c 75%, #9b59b6 100%);
  border-radius: 20px 20px 0 0;
}

.employee-card::after {
  content: '';
  position: absolute;
  top: -2px;
  right: -2px;
  bottom: -2px;
  left: -2px;
  background: linear-gradient(45deg, #3498db, #2ecc71, #f39c12, #e74c3c);
  border-radius: 22px;
  z-index: -1;
  opacity: 0;
  transition: opacity 0.3s ease;
}

.employee-card:hover {
  transform: translateY(-8px) scale(1.02);
  box-shadow: 0 20px 50px rgba(52, 152, 219, 0.3);
}

.employee-card:hover::after {
  opacity: 1;
}

/* رأس بطاقة الموظف */
.employee-header {
  display: flex;
  align-items: center;
  gap: 1rem;
  margin-bottom: 1rem;
}

/* أفاتار الموظف المحسن */
.employee-avatar {
  width: 80px;
  height: 80px;
  border-radius: 50%;
  background: linear-gradient(135deg, #3498db 0%, #2ecc71 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 2rem;
  font-weight: bold;
  flex-shrink: 0;
  box-shadow: 0 8px 25px rgba(52, 152, 219, 0.4);
  position: relative;
  overflow: hidden;
}

.employee-avatar::before {
  content: '';
  position: absolute;
  top: -50%;
  left: -50%;
  width: 200%;
  height: 200%;
  background: linear-gradient(45deg, transparent, rgba(255,255,255,0.3), transparent);
  transform: rotate(45deg);
  animation: avatarShimmer 2s infinite;
}

@keyframes avatarShimmer {
  0% { transform: translateX(-100%) translateY(-100%) rotate(45deg); }
  100% { transform: translateX(100%) translateY(100%) rotate(45deg); }
}

.employee-info h3 {
  margin: 0 0 0.25rem 0;
  color: #2c3e50;
  font-size: 1.2rem;
}

.employee-role {
  color: #6c757d;
  font-size: 0.9rem;
  background: #f8f9fa;
  padding: 0.25rem 0.5rem;
  border-radius: 6px;
  display: inline-block;
}

/* معلومات الموظف */
.employee-details {
  margin-bottom: 1rem;
}

.employee-detail {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0.5rem 0;
  border-bottom: 1px solid #f1f3f4;
  font-size: 0.9rem;
}

.employee-detail:last-child {
  border-bottom: none;
}

.employee-detail .label {
  color: #6c757d;
  font-weight: 600;
}

.employee-detail .value {
  color: #495057;
}

/* حالة المناوبة */
.shift-status {
  padding: 0.4rem 0.8rem;
  border-radius: 20px;
  font-size: 0.8rem;
  font-weight: 600;
  text-align: center;
}

.shift-status.active {
  background: #d4edda;
  color: #155724;
  border: 1px solid #c3e6cb;
}

.shift-status.inactive {
  background: #f8d7da;
  color: #721c24;
  border: 1px solid #f5c6cb;
}

/* أزرار الإجراءات */
.employee-actions {
  display: flex;
  gap: 0.5rem;
  margin-top: 1rem;
}

.shift-btn {
  flex: 1;
  padding: 0.7rem;
  border: none;
  border-radius: 8px;
  cursor: pointer;
  font-size: 0.85rem;
  font-weight: 600;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.4rem;
}

.start-shift-btn {
  background: linear-gradient(135deg, #27ae60, #2ecc71);
  color: white;
}

.start-shift-btn:hover:not(:disabled) {
  background: linear-gradient(135deg, #229954, #27ae60);
  transform: translateY(-1px);
}

.end-shift-btn {
  background: linear-gradient(135deg, #e74c3c, #c0392b);
  color: white;
}

.end-shift-btn:hover:not(:disabled) {
  background: linear-gradient(135deg, #c0392b, #a93226);
  transform: translateY(-1px);
}

.shift-btn:disabled {
  background: #95a5a6;
  cursor: not-allowed;
  transform: none;
  opacity: 0.7;
}

/* إحصائيات الموظفين */
.employees-stats {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 1rem;
  margin-bottom: 2rem;
}

.stat-card {
  background: white;
  padding: 1.5rem;
  border-radius: 12px;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
  text-align: center;
  border-left: 4px solid #3498db;
}

.stat-card.total {
  border-left-color: #3498db;
}

.stat-card.active {
  border-left-color: #27ae60;
}

.stat-card.inactive {
  border-left-color: #e74c3c;
}

.stat-card .stat-icon {
  font-size: 2.5rem;
  margin-bottom: 1rem;
  color: #3498db;
}

.stat-card.active .stat-icon {
  color: #27ae60;
}

.stat-card.inactive .stat-icon {
  color: #e74c3c;
}

.stat-card .stat-number {
  font-size: 2rem;
  font-weight: bold;
  color: #2c3e50;
  margin-bottom: 0.5rem;
}

.stat-card .stat-label {
  color: #6c757d;
  font-size: 0.9rem;
}

/* مودال إضافة موظف */
.add-employee-modal .modal-header {
  background: linear-gradient(135deg, #3498db, #2ecc71);
}

.add-employee-modal .form-group {
  margin-bottom: 1.5rem;
}

.add-employee-modal label {
  display: block;
  margin-bottom: 0.5rem;
  color: #495057;
  font-weight: 600;
}

.add-employee-modal input,
.add-employee-modal select {
  width: 100%;
  padding: 0.8rem;
  border: 2px solid #e9ecef;
  border-radius: 8px;
  font-size: 1rem;
  transition: border-color 0.3s ease;
}

.add-employee-modal input:focus,
.add-employee-modal select:focus {
  outline: none;
  border-color: #3498db;
  box-shadow: 0 0 0 3px rgba(52, 152, 219, 0.1);
}

.add-employee-modal .form-actions {
  display: flex;
  gap: 1rem;
  justify-content: flex-end;
  margin-top: 2rem;
}

.add-employee-modal .submit-btn {
  background: linear-gradient(135deg, #3498db, #2ecc71);
  color: white;
  border: none;
  padding: 0.8rem 1.5rem;
  border-radius: 8px;
  cursor: pointer;
  font-weight: 600;
  transition: all 0.3s ease;
}

.add-employee-modal .submit-btn:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 15px rgba(52, 152, 219, 0.3);
}

.add-employee-modal .cancel-btn {
  background: #6c757d;
  color: white;
  border: none;
  padding: 0.8rem 1.5rem;
  border-radius: 8px;
  cursor: pointer;
  font-weight: 600;
  transition: all 0.3s ease;
}

.add-employee-modal .cancel-btn:hover {
  background: #5a6268;
}

/* تنسيقات الإحصائيات العامة للموظفين */
.employees-screen .stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 1.5rem;
  margin-bottom: 3rem;
  width: 100%;
  max-width: 100%;
  overflow-x: auto;
}

/* تنسيقات البحث للموظفين */
.employees-screen .search-container {
  position: relative;
  display: flex;
  align-items: center;
  background: white;
  border: 2px solid #e9ecef;
  border-radius: 12px;
  overflow: hidden;
  transition: all 0.3s ease;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

.employees-screen .search-container:hover {
  border-color: #3498db;
  box-shadow: 0 4px 15px rgba(52, 152, 219, 0.15);
}

.employees-screen .search-container:focus-within {
  border-color: #3498db;
  box-shadow: 0 0 0 3px rgba(52, 152, 219, 0.1);
}

.employees-screen .search-icon {
  padding: 0.7rem 1rem;
  color: #6c757d;
  background: #f8f9fa;
  border-right: 1px solid #e9ecef;
}

.employees-screen .search-input {
  border: none;
  outline: none;
  padding: 0.7rem 1rem;
  font-size: 0.9rem;
  min-width: 200px;
  background: transparent;
}

.employees-screen .search-input::placeholder {
  color: #adb5bd;
  font-style: italic;
}

.employees-screen .clear-search {
  padding: 0.5rem;
  background: none;
  border: none;
  color: #6c757d;
  cursor: pointer;
  transition: color 0.2s ease;
}

.employees-screen .clear-search:hover {
  color: #e74c3c;
}

/* تنسيقات المرشحات للموظفين */
.employees-screen .filter-select {
  padding: 0.7rem 1.2rem;
  border: 2px solid #e9ecef;
  border-radius: 12px;
  background: white;
  font-size: 0.9rem;
  font-weight: 500;
  min-width: 150px;
  color: #495057;
  transition: all 0.3s ease;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

.employees-screen .filter-select:hover {
  border-color: #3498db;
  box-shadow: 0 4px 15px rgba(52, 152, 219, 0.15);
  transform: translateY(-1px);
}

.employees-screen .filter-select:focus {
  outline: none;
  border-color: #3498db;
  box-shadow: 0 0 0 3px rgba(52, 152, 219, 0.1);
}

/* تنسيقات التحميل للموظفين */
.employees-screen .loading {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 60vh;
  gap: 1rem;
  color: #2c3e50;
}

.employees-screen .loading i {
  font-size: 3rem;
  color: #3498db;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* شارة الدور للموظفين */
.employees-screen .role-badge {
  background: linear-gradient(135deg, #3498db, #2980b9);
  color: white;
  padding: 0.5rem 1rem;
  border-radius: 20px;
  font-size: 0.9rem;
  font-weight: 500;
}

/* رسالة قريباً للموظفين */
.employees-screen .coming-soon {
  text-align: center;
  padding: 4rem 2rem;
  color: #7f8c8d;
  font-size: 1.2rem;
  background: linear-gradient(135deg, #ecf0f1, #bdc3c7);
  border-radius: 15px;
  margin: 2rem 0;
}

/* التصميم المتجاوب */
@media (max-width: 768px) {
  .employees-screen {
    padding: 1rem;
  }
  
  .employees-header {
    flex-direction: column;
    gap: 1rem;
    text-align: center;
  }
  
  .employees-header h1 {
    font-size: 1.5rem;
  }
  
  .employees-grid {
    grid-template-columns: 1fr;
    gap: 1rem;
  }
  
  .employee-card {
    padding: 1rem;
  }
  
  .employee-actions {
    flex-direction: column;
  }
  
  .employees-stats {
    grid-template-columns: repeat(2, 1fr);
  }
}

@media (max-width: 480px) {
  .employees-header h1 {
    font-size: 1.2rem;
  }
  
  .employee-header {
    flex-direction: column;
    text-align: center;
    gap: 0.5rem;
  }
  
  .employee-avatar {
    width: 50px;
    height: 50px;
    font-size: 1.2rem;
  }
  
  .employees-stats {
    grid-template-columns: 1fr;
  }
}

const axios = require('axios');

/**
 * سكريپت اختبار سريع للنظام المحلي
 * يتحقق من عمل جميع الخدمات الأساسية
 */
async function testLocalSystem() {
  console.log('🧪 بدء اختبار النظام المحلي...');
  console.log('=' + '='.repeat(50));
  
  const tests = [];
  
  // اختبار الباك اند API
  try {
    console.log('🔍 اختبار الباك اند API...');
    const response = await axios.get('http://localhost:5000/api/v1/health', {
      timeout: 5000
    });
    
    if (response.status === 200) {
      console.log('✅ الباك اند API يعمل بشكل صحيح');
      tests.push({ name: 'Backend API', status: 'pass' });
    }
  } catch (error) {
    console.log('❌ الباك اند API لا يعمل');
    console.log(`   السبب: ${error.message}`);
    tests.push({ name: 'Backend API', status: 'fail', error: error.message });
  }
  
  // اختبار قاعدة البيانات
  try {
    console.log('🔍 اختبار الاتصال بقاعدة البيانات...');
    const response = await axios.get('http://localhost:5000/api/v1/users', {
      timeout: 5000
    });
    
    if (response.status === 200) {
      console.log('✅ قاعدة البيانات متصلة ومتاحة');
      console.log(`   عدد المستخدمين: ${response.data.length || 0}`);
      tests.push({ name: 'Database Connection', status: 'pass' });
    }
  } catch (error) {
    console.log('❌ مشكلة في الاتصال بقاعدة البيانات');
    console.log(`   السبب: ${error.message}`);
    tests.push({ name: 'Database Connection', status: 'fail', error: error.message });
  }
  
  // اختبار تسجيل الدخول
  try {
    console.log('🔍 اختبار تسجيل الدخول...');
    const loginResponse = await axios.post('http://localhost:5000/api/v1/auth/login', {
      username: 'admin',
      password: 'admin123'
    }, {
      timeout: 5000
    });
    
    if (loginResponse.status === 200 && loginResponse.data.token) {
      console.log('✅ تسجيل الدخول يعمل بشكل صحيح');
      console.log(`   المستخدم: ${loginResponse.data.user?.username}`);
      console.log(`   الدور: ${loginResponse.data.user?.role}`);
      tests.push({ name: 'Login System', status: 'pass' });
    }
  } catch (error) {
    console.log('❌ مشكلة في تسجيل الدخول');
    console.log(`   السبب: ${error.message}`);
    tests.push({ name: 'Login System', status: 'fail', error: error.message });
  }
  
  // اختبار المنتجات
  try {
    console.log('🔍 اختبار تحميل المنتجات...');
    const response = await axios.get('http://localhost:5000/api/v1/products', {
      timeout: 5000
    });
    
    if (response.status === 200) {
      console.log('✅ تحميل المنتجات يعمل بشكل صحيح');
      console.log(`   عدد المنتجات: ${response.data.length || 0}`);
      tests.push({ name: 'Products API', status: 'pass' });
    }
  } catch (error) {
    console.log('❌ مشكلة في تحميل المنتجات');
    console.log(`   السبب: ${error.message}`);
    tests.push({ name: 'Products API', status: 'fail', error: error.message });
  }
  
  // اختبار الفرونت اند
  try {
    console.log('🔍 اختبار الفرونت اند...');
    const response = await axios.get('http://localhost:3000', {
      timeout: 5000
    });
    
    if (response.status === 200) {
      console.log('✅ الفرونت اند يعمل بشكل صحيح');
      tests.push({ name: 'Frontend', status: 'pass' });
    }
  } catch (error) {
    console.log('❌ الفرونت اند لا يعمل');
    console.log(`   السبب: ${error.message}`);
    tests.push({ name: 'Frontend', status: 'fail', error: error.message });
  }
  
  // تقرير النتائج
  console.log('\n' + '=' + '='.repeat(50));
  console.log('📊 تقرير نتائج الاختبار');
  console.log('=' + '='.repeat(50));
  
  const passedTests = tests.filter(test => test.status === 'pass').length;
  const failedTests = tests.filter(test => test.status === 'fail').length;
  
  console.log(`\n✅ اختبارات نجحت: ${passedTests}`);
  console.log(`❌ اختبارات فشلت: ${failedTests}`);
  console.log(`📊 إجمالي الاختبارات: ${tests.length}`);
  
  if (failedTests > 0) {
    console.log('\n🔍 تفاصيل الأخطاء:');
    tests.filter(test => test.status === 'fail').forEach(test => {
      console.log(`   ❌ ${test.name}: ${test.error}`);
    });
    
    console.log('\n💡 اقتراحات لحل المشاكل:');
    
    if (tests.find(t => t.name === 'Backend API' && t.status === 'fail')) {
      console.log('   🔧 تأكد من تشغيل الباك اند: npm run backend:dev:local');
    }
    
    if (tests.find(t => t.name === 'Database Connection' && t.status === 'fail')) {
      console.log('   🔧 تأكد من تشغيل MongoDB: start-mongodb-local.bat');
      console.log('   🔧 تأكد من تهيئة قاعدة البيانات: node init-local-database.cjs');
    }
    
    if (tests.find(t => t.name === 'Frontend' && t.status === 'fail')) {
      console.log('   🔧 تأكد من تشغيل الفرونت اند: npm run dev:local');
    }
  } else {
    console.log('\n🎉 جميع الاختبارات نجحت! النظام يعمل بشكل مثالي');
    console.log('\n🚀 يمكنك الآن:');
    console.log('   🌐 زيارة http://localhost:3000');
    console.log('   👤 تسجيل الدخول باستخدام: admin / admin123');
    console.log('   💻 بدء التطوير');
  }
  
  // معلومات إضافية
  console.log('\n📋 معلومات النظام:');
  console.log(`   الفرونت اند: http://localhost:3000`);
  console.log(`   الباك اند: http://localhost:5000`);
  console.log(`   Socket.IO: http://localhost:5000`);
  console.log(`   قاعدة البيانات: MongoDB Atlas (السحابية)`);
  
  console.log('\n⚙️  للتحكم في النظام:');
  console.log(`   إيقاف: Ctrl+C في نوافذ الطرفية`);
  console.log(`   إعادة التشغيل: start-local-development.bat`);
  console.log(`   إعادة تهيئة قاعدة البيانات: node init-local-database.cjs`);
}

// معالج الأخطاء
process.on('unhandledRejection', (error) => {
  console.error('❌ خطأ غير متوقع:', error.message);
});

// تشغيل الاختبار
testLocalSystem().catch(error => {
  console.error('❌ فشل في تشغيل الاختبار:', error.message);
});

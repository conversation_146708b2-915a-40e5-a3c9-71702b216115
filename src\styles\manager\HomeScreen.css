/* ============================== */
/* HomeScreen - Manager Dashboard Home */
/* ============================== */

/* ??????? ????????? ??????? */
@import '../variables/home-variables.css';

/* ================ */
/* Home Screen Container */
/* ================ */

.homeScreen {
  max-width: 1200px;
  margin: 0 auto;
  padding: 2rem;
  direction: rtl;
}

.homeScreen__header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 2rem;
  flex-wrap: wrap;
  gap: 1rem;
}

.homeScreen__welcome-section {
  text-align: center;
  margin-bottom: 2rem;
}

.homeScreen__title {
  margin: 0 0 0.5rem 0;
  color: var(--homeScreen-primary-color);
  font-size: 2.5rem;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 1rem;
}

.homeScreen__title-icon {
  color: var(--homeScreen-secondary-color);
  font-size: 2rem;
}

.homeScreen__role-badge {
  background: linear-gradient(135deg, var(--homeScreen-secondary-color), #2980b9);
  color: var(--homeScreen-white);
  padding: 0.5rem 1rem;
  border-radius: 20px;
  font-size: 0.9rem;
  font-weight: 500;
  display: inline-block;
}

/* ================ */
/* Control Buttons */
/* ================ */

.homeScreen__control-buttons {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 1rem;
  flex-wrap: wrap;
}

.homeScreen__refresh-btn {
  background: linear-gradient(135deg, var(--homeScreen-secondary-color), #2980b9);
  color: var(--homeScreen-white);
  border: none;
  padding: 1rem 2rem;
  border-radius: var(--homeScreen-border-radius);
  font-size: 1rem;
  font-weight: 600;
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  transition: var(--homeScreen-transition);
  box-shadow: var(--homeScreen-box-shadow);
}

.homeScreen__refresh-btn:hover {
  background: linear-gradient(135deg, #2980b9, #1f4e79);
  transform: translateY(-2px);
  box-shadow: 0 4px 15px rgba(52, 152, 219, 0.3);
}

.homeScreen__refresh-btn--loading {
  position: relative;
  overflow: hidden;
}

.homeScreen__refresh-btn--loading::after {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
  animation: loading-shimmer 1.5s infinite;
}

@keyframes loading-shimmer {
  0% { left: -100%; }
  100% { left: 100%; }
}

/* ================ */
/* Connection Status */
/* ================ */

.homeScreen__connection-status {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.5rem 1rem;
  border-radius: 20px;
  font-size: 0.9rem;
  font-weight: 500;
}

.homeScreen__connection-status--online {
  background: rgba(39, 174, 96, 0.1);
  color: var(--homeScreen-success-color);
  border: 1px solid rgba(39, 174, 96, 0.3);
}

.homeScreen__connection-status--offline {
  background: rgba(231, 76, 60, 0.1);
  color: var(--homeScreen-danger-color);
  border: 1px solid rgba(231, 76, 60, 0.3);
}

.homeScreen__connection-icon {
  font-size: 1rem;
}

/* ================ */
/* Last Update Info */
/* ================ */

.homeScreen__last-update {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  color: #7f8c8d;
  font-size: 0.9rem;
}

.homeScreen__update-icon {
  color: var(--homeScreen-secondary-color);
}

/* ================ */
/* Stats Grid */
/* ================ */

.homeScreen__stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 1.5rem;
  margin-bottom: 3rem;
}

.homeScreen__stat-card {
  background: var(--homeScreen-white);
  padding: 2rem;
  border-radius: 16px;
  box-shadow: var(--homeScreen-box-shadow);
  border: 1px solid #f1f2f6;
  transition: var(--homeScreen-transition);
  position: relative;
  overflow: hidden;
}

.homeScreen__stat-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

.homeScreen__stat-card--orders {
  border-left: 4px solid var(--homeScreen-secondary-color);
}

.homeScreen__stat-card--sales {
  border-left: 4px solid var(--homeScreen-success-color);
}

.homeScreen__stat-card--employees {
  border-left: 4px solid var(--homeScreen-warning-color);
}

.homeScreen__stat-card--tables {
  border-left: 4px solid #9b59b6;
}

.homeScreen__stat-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 60px;
  height: 60px;
  border-radius: 50%;
  margin-bottom: 1rem;
  font-size: 1.5rem;
  color: var(--homeScreen-white);
}

.homeScreen__stat-card--orders .homeScreen__stat-icon {
  background: linear-gradient(135deg, var(--homeScreen-secondary-color), #2980b9);
}

.homeScreen__stat-card--sales .homeScreen__stat-icon {
  background: linear-gradient(135deg, var(--homeScreen-success-color), #1e8449);
}

.homeScreen__stat-card--employees .homeScreen__stat-icon {
  background: linear-gradient(135deg, var(--homeScreen-warning-color), #d68910);
}

.homeScreen__stat-card--tables .homeScreen__stat-icon {
  background: linear-gradient(135deg, #9b59b6, #8e44ad);
}

.homeScreen__stat-content {
  text-align: center;
}

.homeScreen__stat-number {
  font-size: 2.5rem;
  font-weight: 700;
  color: var(--homeScreen-primary-color);
  margin: 0;
  line-height: 1;
}

.homeScreen__stat-label {
  color: #7f8c8d;
  font-size: 1rem;
  margin: 0.5rem 0 0 0;
  font-weight: 500;
}

/* ================ */
/* Orders Statistics */
/* ================ */

.homeScreen__orders-stats {
  background: var(--homeScreen-white);
  padding: 2rem;
  border-radius: 16px;
  box-shadow: var(--homeScreen-box-shadow);
  margin-bottom: 2rem;
  border: 1px solid #f1f2f6;
}

.homeScreen__orders-stats-title {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  margin-bottom: 1.5rem;
  color: var(--homeScreen-primary-color);
  font-size: 1.5rem;
  font-weight: 700;
  position: relative;
  padding-bottom: 0.5rem;
  border-bottom: 2px solid #e9ecef;
}

.orders-total-badge {
  display: inline-block;
  background: linear-gradient(135deg, var(--homeScreen-secondary-color), #2980b9);
  color: var(--homeScreen-white);
  padding: 0.3rem 0.8rem;
  border-radius: 20px;
  font-size: 0.8rem;
  font-weight: 600;
  margin-right: 1rem;
  box-shadow: 0 2px 8px rgba(52, 152, 219, 0.3);
}

.homeScreen__orders-stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 1rem;
}

.homeScreen__order-stat {
  background: var(--homeScreen-light-bg);
  padding: 1.5rem;
  border-radius: var(--homeScreen-border-radius);
  text-align: center;
  cursor: pointer;
  transition: var(--homeScreen-transition);
  border: 2px solid transparent;
  position: relative;
}

.homeScreen__order-stat:hover {
  transform: translateY(-5px) scale(1.02);
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.15);
}

.homeScreen__order-stat--pending {
  border-color: var(--homeScreen-warning-color);
  background: linear-gradient(135deg, #fff3cd, #fef2c0);
  position: relative;
  border-left: 4px solid var(--homeScreen-warning-color);
}

.homeScreen__order-stat--preparing {
  border-color: var(--homeScreen-secondary-color);
  background: linear-gradient(135deg, #d1ecf1, #b8daff);
  position: relative;
  border-left: 4px solid var(--homeScreen-secondary-color);
}

.homeScreen__order-stat--ready {
  border-color: var(--homeScreen-success-color);
  background: linear-gradient(135deg, #d4edda, #c3e6cb);
  position: relative;
  border-left: 4px solid var(--homeScreen-success-color);
  animation: subtle-glow 3s infinite;
}

@keyframes subtle-glow {
  0%, 100% { box-shadow: 0 0 5px rgba(39, 174, 96, 0.3); }
  50% { box-shadow: 0 0 15px rgba(39, 174, 96, 0.5); }
}

.homeScreen__order-stat--completed {
  border-color: #6c757d;
  background: linear-gradient(135deg, #f8f9fa, #e9ecef);
  position: relative;
  border-left: 4px solid #6c757d;
}

/* ================ */
/* Enhanced Order Status Alerts */
/* ================ */

.homeScreen__order-stat-alert,
.homeScreen__order-stat-working,
.homeScreen__order-stat-ready,
.homeScreen__order-stat-completed {
  display: flex;
  align-items: center;
  gap: 0.3rem;
  font-size: 0.8rem;
  font-weight: 600;
  margin-top: 0.5rem;
  padding: 0.3rem 0.6rem;
  border-radius: 12px;
  animation: pulse 2s infinite;
}

.homeScreen__order-stat-alert {
  background: rgba(231, 76, 60, 0.1);
  color: var(--homeScreen-danger-color);
  border: 1px solid rgba(231, 76, 60, 0.3);
}

.homeScreen__order-stat-working {
  background: rgba(52, 152, 219, 0.1);
  color: var(--homeScreen-secondary-color);
  border: 1px solid rgba(52, 152, 219, 0.3);
}

.homeScreen__order-stat-ready {
  background: rgba(39, 174, 96, 0.1);
  color: var(--homeScreen-success-color);
  border: 1px solid rgba(39, 174, 96, 0.3);
  animation: blink 1.5s infinite;
}

.homeScreen__order-stat-completed {
  background: rgba(108, 117, 125, 0.1);
  color: #6c757d;
  border: 1px solid rgba(108, 117, 125, 0.3);
}

@keyframes pulse {
  0%, 100% { opacity: 1; }
  50% { opacity: 0.7; }
}

@keyframes blink {
  0%, 100% { background: rgba(39, 174, 96, 0.1); }
  50% { background: rgba(39, 174, 96, 0.2); }
}

/* ================ */
/* Enhanced Summary Cards */
/* ================ */

.homeScreen__summary-card {
  position: relative;
  overflow: hidden;
}

.homeScreen__summary-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 3px;
  background: linear-gradient(90deg, var(--homeScreen-secondary-color), var(--homeScreen-success-color));
  border-radius: var(--homeScreen-border-radius) var(--homeScreen-border-radius) 0 0;
}

.homeScreen__summary-card:nth-child(3)::before {
  background: linear-gradient(90deg, var(--homeScreen-warning-color), #e67e22);
}

.homeScreen__summary-card:nth-child(4)::before {
  background: linear-gradient(90deg, #e74c3c, #c0392b);
}

/* ================ */
/* Status Priority Indicators */
/* ================ */

.homeScreen__order-stat--pending {
  position: relative;
  border-left: 4px solid var(--homeScreen-warning-color);
}

.homeScreen__order-stat--preparing {
  position: relative;
  border-left: 4px solid var(--homeScreen-secondary-color);
}

.homeScreen__order-stat--ready {
  position: relative;
  border-left: 4px solid var(--homeScreen-success-color);
  animation: subtle-glow 3s infinite;
}

@keyframes subtle-glow {
  0%, 100% { box-shadow: 0 0 5px rgba(39, 174, 96, 0.3); }
  50% { box-shadow: 0 0 15px rgba(39, 174, 96, 0.5); }
}

.homeScreen__order-stat--completed {
  position: relative;
  border-left: 4px solid #6c757d;
}

/* ================ */
/* Popular Products Section Enhancement */
/* ================ */

.homeScreen__popular-products {
  background: var(--homeScreen-white);
  padding: 2rem;
  border-radius: 16px;
  box-shadow: var(--homeScreen-box-shadow);
  margin-bottom: 2rem;
  border: 1px solid #f1f2f6;
}

.homeScreen__popular-products-title {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  margin-bottom: 1.5rem;
  color: var(--homeScreen-primary-color);
  font-size: 1.5rem;
  font-weight: 700;
}

.homeScreen__popular-products-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 2rem; /* زيادة المسافة لتجنب تداخل الـ badges */
  padding: 1rem 0; /* إضافة padding علوي وسفلي */
}

.homeScreen__popular-product-card {
  background: var(--homeScreen-light-bg);
  padding: 1.5rem;
  border-radius: var(--homeScreen-border-radius);
  border: 2px solid transparent;
  transition: var(--homeScreen-transition);
  position: relative;
  overflow: visible; /* تغيير من hidden لـ visible لضمان ظهور الـ badge */
  margin: 15px 5px; /* إضافة margin لتجنب تداخل الـ badges */
}

.homeScreen__popular-product-card:hover {
  transform: translateY(-3px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
  border-color: var(--homeScreen-secondary-color);
}

.homeScreen__product-rank-badge {
  position: absolute;
  top: -10px;
  right: -10px;
  background: linear-gradient(135deg, var(--homeScreen-secondary-color), #2980b9);
  color: var(--homeScreen-white);
  border-radius: 50%;
  width: 45px;
  height: 45px;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  font-weight: 700;
  box-shadow: 0 2px 10px rgba(52, 152, 219, 0.3);
  z-index: 10;
  font-size: 0.75rem;
  line-height: 1;
}

.homeScreen__product-rank-badge .rank-number {
  display: block;
  font-size: 0.8rem;
  font-weight: 700;
  margin-bottom: 1px;
}

.homeScreen__product-rank-badge .rank-icon {
  display: block;
  line-height: 1;
}

.homeScreen__product-rank-badge .rank-icon i {
  font-size: 0.7rem;
}

/* Special styles for top 3 rankings */
.homeScreen__product-rank-badge .rank-icon i.gold {
  color: #FFD700;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
}

.homeScreen__product-rank-badge .rank-icon i.silver {
  color: #C0C0C0;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
}

.homeScreen__product-rank-badge .rank-icon i.bronze {
  color: #CD7F32;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
}

/* Alternative: Show only icon for top 3, only number for others */
.homeScreen__popular-product-card:nth-child(1) .homeScreen__product-rank-badge .rank-number,
.homeScreen__popular-product-card:nth-child(2) .homeScreen__product-rank-badge .rank-number,
.homeScreen__popular-product-card:nth-child(3) .homeScreen__product-rank-badge .rank-number {
  display: none;
}

.homeScreen__popular-product-card:nth-child(1) .homeScreen__product-rank-badge .rank-icon,
.homeScreen__popular-product-card:nth-child(2) .homeScreen__product-rank-badge .rank-icon,
.homeScreen__popular-product-card:nth-child(3) .homeScreen__product-rank-badge .rank-icon {
  font-size: 1.2rem;
}

.homeScreen__popular-product-card:nth-child(n+4) .homeScreen__product-rank-badge .rank-icon {
  display: none;
}

.homeScreen__popular-product-card:nth-child(n+4) .homeScreen__product-rank-badge .rank-number {
  font-size: 1rem;
}

/* Enhanced badge colors for different ranks */
.homeScreen__popular-product-card:nth-child(1) .homeScreen__product-rank-badge {
  background: linear-gradient(135deg, #FFD700, #FFA500);
  box-shadow: 0 2px 10px rgba(255, 215, 0, 0.4);
}

.homeScreen__popular-product-card:nth-child(2) .homeScreen__product-rank-badge {
  background: linear-gradient(135deg, #C0C0C0, #A0A0A0);
  box-shadow: 0 2px 10px rgba(192, 192, 192, 0.4);
}

.homeScreen__popular-product-card:nth-child(3) .homeScreen__product-rank-badge {
  background: linear-gradient(135deg, #CD7F32, #B87333);
  box-shadow: 0 2px 10px rgba(205, 127, 50, 0.4);
}

.homeScreen__product-name {
  color: var(--homeScreen-primary-color);
  font-size: 1.1rem;
  font-weight: 600;
  margin-bottom: 1rem;
  line-height: 1.3;
}

.homeScreen__product-stats {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(80px, 1fr));
  gap: 0.8rem;
  margin-bottom: 1.5rem;
}

.homeScreen__stat-item {
  text-align: center;
  padding: 0.8rem;
  border-radius: 8px;
  background: var(--homeScreen-white);
  border: 1px solid #e9ecef;
}

.homeScreen__stat-item i {
  display: block;
  font-size: 1.2rem;
  margin-bottom: 0.3rem;
  color: var(--homeScreen-secondary-color);
}

.homeScreen__stat-value {
  display: block;
  font-size: 1.3rem;
  font-weight: 700;
  color: var(--homeScreen-primary-color);
  margin-bottom: 0.2rem;
}

.homeScreen__stat-label {
  font-size: 0.8rem;
  color: #7f8c8d;
  font-weight: 500;
}

/* ================ */
/* Enhanced Order Status Indicators */
/* ================ */

.homeScreen__orders-stats-title {
  position: relative;
  padding-bottom: 0.5rem;
  border-bottom: 2px solid #e9ecef;
}

.orders-total-badge {
  display: inline-block;
  background: linear-gradient(135deg, var(--homeScreen-secondary-color), #2980b9);
  color: var(--homeScreen-white);
  padding: 0.3rem 0.8rem;
  border-radius: 20px;
  font-size: 0.8rem;
  font-weight: 600;
  margin-right: 1rem;
  box-shadow: 0 2px 8px rgba(52, 152, 219, 0.3);
}

.urgent-indicator,
.active-indicator,
.ready-indicator,
.success-indicator {
  position: absolute;
  top: -5px;
  left: -5px;
  width: 20px;
  height: 20px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 0.7rem;
  animation: pulse-indicator 2s infinite;
}

.urgent-indicator {
  background: var(--homeScreen-danger-color);
  color: var(--homeScreen-white);
}

.active-indicator {
  background: var(--homeScreen-secondary-color);
  color: var(--homeScreen-white);
}

.ready-indicator {
  background: var(--homeScreen-success-color);
  color: var(--homeScreen-white);
  animation: blink-indicator 1.5s infinite;
}

.success-indicator {
  background: #6c757d;
  color: var(--homeScreen-white);
}

@keyframes pulse-indicator {
  0%, 100% { 
    transform: scale(1);
    opacity: 1;
  }
  50% { 
    transform: scale(1.1);
    opacity: 0.8;
  }
}

@keyframes blink-indicator {
  0%, 100% { 
    background: var(--homeScreen-success-color);
    box-shadow: 0 0 5px var(--homeScreen-success-color);
  }
  50% { 
    background: #27ae60;
    box-shadow: 0 0 15px var(--homeScreen-success-color);
  }
}

/* ================ */
/* Enhanced Alert Messages */
/* ================ */

.homeScreen__order-stat-alert {
  font-size: 0.75rem;
}

.homeScreen__order-stat-working {
  font-size: 0.75rem;
}

.homeScreen__order-stat-ready {
  font-size: 0.75rem;
}

.homeScreen__order-stat-completed {
  font-size: 0.75rem;
}

/* ================ */
/* Order Status Icon Enhancements */
/* ================ */

.homeScreen__order-stat-icon-wrapper {
  position: relative;
}

.homeScreen__order-stat--pending .homeScreen__order-stat-icon-wrapper:hover {
  animation: shake 0.5s ease-in-out;
}

.homeScreen__order-stat--ready .homeScreen__order-stat-icon-wrapper {
  animation: gentle-bounce 2s infinite;
}

@keyframes shake {
  0%, 100% { transform: translateX(0); }
  25% { transform: translateX(-2px); }
  75% { transform: translateX(2px); }
}

@keyframes gentle-bounce {
  0%, 100% { transform: translateY(0); }
  50% { transform: translateY(-3px); }
}

/* ================ */
/* Priority-Based Visual Enhancements */
/* ================ */

.homeScreen__order-stat--pending[data-count="0"] {
  opacity: 0.6;
  transform: scale(0.95);
}

.homeScreen__order-stat--pending:not([data-count="0"]) {
  box-shadow: 0 0 20px rgba(243, 156, 18, 0.4);
}

.homeScreen__order-stat--ready:not([data-count="0"]) {
  box-shadow: 0 0 20px rgba(39, 174, 96, 0.4);
  animation: ready-pulse 3s infinite;
}

@keyframes ready-pulse {
  0%, 100% { 
    box-shadow: 0 0 20px rgba(39, 174, 96, 0.4);
  }
  50% { 
    box-shadow: 0 0 30px rgba(39, 174, 96, 0.6);
  }
}

/* ================ */
/* Daily Orders Summary - Enhanced Design */
/* ================ */

.homeScreen__daily-orders-summary {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 1.5rem;
  margin: 2rem 0;
  padding: 1.5rem;
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
  border-radius: 15px;
  border: 1px solid #dee2e6;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
}

.homeScreen__summary-card {
  background: var(--homeScreen-white);
  padding: 1.5rem;
  border-radius: 12px;
  display: flex;
  align-items: center;
  gap: 1rem;
  box-shadow: 0 2px 15px rgba(0, 0, 0, 0.1);
  border: 1px solid #e9ecef;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.homeScreen__summary-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: linear-gradient(135deg, var(--homeScreen-secondary-color), #2980b9);
  transform: translateX(-100%);
  transition: transform 0.3s ease;
}

.homeScreen__summary-card:hover {
  transform: translateY(-3px);
  box-shadow: 0 8px 30px rgba(0, 0, 0, 0.15);
  border-color: var(--homeScreen-secondary-color);
}

.homeScreen__summary-card:hover::before {
  transform: translateX(0);
}

.homeScreen__summary-card-icon {
  width: 60px;
  height: 60px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.5rem;
  color: var(--homeScreen-white);
  background: linear-gradient(135deg, var(--homeScreen-secondary-color), #2980b9);
  box-shadow: 0 4px 15px rgba(52, 152, 219, 0.3);
  flex-shrink: 0;
}

.homeScreen__summary-card:nth-child(1) .homeScreen__summary-card-icon {
  background: linear-gradient(135deg, #e74c3c, #c0392b);
  box-shadow: 0 4px 15px rgba(231, 76, 60, 0.3);
}

.homeScreen__summary-card:nth-child(2) .homeScreen__summary-card-icon {
  background: linear-gradient(135deg, #27ae60, #229954);
  box-shadow: 0 4px 15px rgba(39, 174, 96, 0.3);
}

.homeScreen__summary-card:nth-child(3) .homeScreen__summary-card-icon {
  background: linear-gradient(135deg, #f39c12, #e67e22);
  box-shadow: 0 4px 15px rgba(243, 156, 18, 0.3);
}

.homeScreen__summary-card:nth-child(4) .homeScreen__summary-card-icon {
  background: linear-gradient(135deg, #9b59b6, #8e44ad);
  box-shadow: 0 4px 15px rgba(155, 89, 182, 0.3);
}

.homeScreen__summary-count {
  display: block;
  font-size: 2rem;
  font-weight: 700;
  color: var(--homeScreen-primary-color);
  margin-bottom: 0.3rem;
  line-height: 1;
}

.homeScreen__summary-label {
  display: block;
  font-size: 0.9rem;
  color: #7f8c8d;
  font-weight: 500;
  line-height: 1.2;
}

/* تأثيرات نابضة للبطاقات النشطة */
.homeScreen__summary-card:nth-child(4) {
  animation: active-card-pulse 3s infinite;
}

@keyframes active-card-pulse {
  0%, 100% {
    box-shadow: 0 2px 15px rgba(0, 0, 0, 0.1);
  }
  50% {
    box-shadow: 0 2px 20px rgba(155, 89, 182, 0.2);
  }
}

/* إضافة شارات للأرقام المهمة */
.homeScreen__summary-card[data-alert="true"] {
  position: relative;
}

.homeScreen__summary-card[data-alert="true"]::after {
  content: '!';
  position: absolute;
  top: 10px;
  right: 10px;
  width: 20px;
  height: 20px;
  background: var(--homeScreen-danger-color);
  color: var(--homeScreen-white);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 0.7rem;
  font-weight: bold;
  animation: warning-blink 1.5s infinite;
}

@keyframes warning-blink {
  0%, 100% { opacity: 1; }
  50% { opacity: 0.5; }
}

/* ================ */
/* Orders Status Stats - إحصائيات حالة الطلبات */
/* ================ */

.homeScreen__orders-stats {
  margin: 2rem 0;
}

.homeScreen__orders-stats-title {
  color: var(--homeScreen-primary-color);
  font-size: 1.5rem;
  font-weight: 600;
  margin-bottom: 1.5rem;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  position: relative;
}

.homeScreen__orders-stats-title i {
  color: var(--homeScreen-secondary-color);
  font-size: 1.3rem;
}

.orders-total-badge {
  display: inline-block;
  background: linear-gradient(135deg, var(--homeScreen-secondary-color), #2980b9);
  color: var(--homeScreen-white);
  padding: 0.3rem 0.8rem;
  border-radius: 20px;
  font-size: 0.8rem;
  font-weight: 600;
  margin-right: 1rem;
  box-shadow: 0 2px 8px rgba(52, 152, 219, 0.3);
}

.homeScreen__orders-stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 1.5rem;
}

.homeScreen__order-stat {
  background: var(--homeScreen-white);
  padding: 1.5rem;
  border-radius: 12px;
  border: 2px solid #e9ecef;
  box-shadow: 0 2px 15px rgba(0, 0, 0, 0.1);
  transition: var(--homeScreen-transition);
  cursor: pointer;
  position: relative;
  overflow: hidden;
}

.homeScreen__order-stat::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  transition: var(--homeScreen-transition);
}

.homeScreen__order-stat:hover {
  transform: translateY(-3px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

/* Status-specific colors and styles */
.homeScreen__order-stat--pending {
  border-color: var(--homeScreen-warning-color);
}

.homeScreen__order-stat--pending::before {
  background: linear-gradient(135deg, var(--homeScreen-warning-color), #e67e22);
}

.homeScreen__order-stat--pending:hover {
  border-color: var(--homeScreen-warning-color);
  box-shadow: 0 8px 25px rgba(243, 156, 18, 0.3);
}

.homeScreen__order-stat--preparing {
  border-color: #e74c3c;
}

.homeScreen__order-stat--preparing::before {
  background: linear-gradient(135deg, #e74c3c, #c0392b);
}

.homeScreen__order-stat--preparing:hover {
  border-color: #e74c3c;
  box-shadow: 0 8px 25px rgba(231, 76, 60, 0.3);
}

.homeScreen__order-stat--ready {
  border-color: var(--homeScreen-success-color);
}

.homeScreen__order-stat--ready::before {
  background: linear-gradient(135deg, var(--homeScreen-success-color), #229954);
}

.homeScreen__order-stat--ready:hover {
  border-color: var(--homeScreen-success-color);
  box-shadow: 0 8px 25px rgba(39, 174, 96, 0.3);
}

.homeScreen__order-stat--completed {
  border-color: var(--homeScreen-secondary-color);
}

.homeScreen__order-stat--completed::before {
  background: linear-gradient(135deg, var(--homeScreen-secondary-color), #2980b9);
}

.homeScreen__order-stat--completed:hover {
  border-color: var(--homeScreen-secondary-color);
  box-shadow: 0 8px 25px rgba(52, 152, 219, 0.3);
}

.homeScreen__order-stat-icon-wrapper {
  position: relative;
  display: inline-block;
  margin-bottom: 1rem;
  text-align: center;
}

.homeScreen__order-stat-icon-wrapper i {
  font-size: 2.5rem;
  color: var(--homeScreen-primary-color);
  transition: var(--homeScreen-transition);
  display: block;
}

.homeScreen__order-stat--pending .homeScreen__order-stat-icon-wrapper i {
  color: var(--homeScreen-warning-color);
}

.homeScreen__order-stat--preparing .homeScreen__order-stat-icon-wrapper i {
  color: #e74c3c;
}

.homeScreen__order-stat--ready .homeScreen__order-stat-icon-wrapper i {
  color: var(--homeScreen-success-color);
}

.homeScreen__order-stat--completed .homeScreen__order-stat-icon-wrapper i {
  color: var(--homeScreen-secondary-color);
}

.homeScreen__order-stat-details {
  text-align: center;
}

.homeScreen__order-stat-count {
  display: block;
  font-size: 2.5rem;
  font-weight: 700;
  color: var(--homeScreen-primary-color);
  margin-bottom: 0.5rem;
  line-height: 1;
}

.homeScreen__order-stat-label {
  display: block;
  font-size: 1rem;
  color: #7f8c8d;
  font-weight: 500;
  margin-bottom: 1rem;
}

.homeScreen__progress-bar {
  width: 100%;
  height: 6px;
  background: #e9ecef;
  border-radius: 3px;
  overflow: hidden;
  margin-bottom: 1rem;
}

.homeScreen__progress-fill {
  height: 100%;
  width: 100%;
  border-radius: 3px;
  transition: var(--homeScreen-transition);
}

.homeScreen__progress-fill--pending {
  background: linear-gradient(135deg, var(--homeScreen-warning-color), #e67e22);
}

.homeScreen__progress-fill--preparing {
  background: linear-gradient(135deg, #e74c3c, #c0392b);
}

.homeScreen__progress-fill--ready {
  background: linear-gradient(135deg, var(--homeScreen-success-color), #229954);
}

.homeScreen__progress-fill--completed {
  background: linear-gradient(135deg, var(--homeScreen-secondary-color), #2980b9);
}

/* Status indicators */
.urgent-indicator,
.active-indicator,
.ready-indicator,
.success-indicator {
  position: absolute;
  top: -8px;
  right: -8px;
  width: 22px;
  height: 22px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 0.65rem;
  color: var(--homeScreen-white);
  animation: pulse 2s infinite;
  border: 2px solid var(--homeScreen-white);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
  z-index: 5;
}

.urgent-indicator {
  background: var(--homeScreen-danger-color);
}

.active-indicator {
  background: #e74c3c;
}

.ready-indicator {
  background: var(--homeScreen-success-color);
}

.success-indicator {
  background: var(--homeScreen-secondary-color);
}

.urgent-indicator i,
.active-indicator i,
.ready-indicator i,
.success-indicator i {
  font-size: 0.65rem;
  color: var(--homeScreen-white);
  line-height: 1;
}

@keyframes pulse {
  0%, 100% {
    transform: scale(1);
    opacity: 1;
  }
  50% {
    transform: scale(1.1);
    opacity: 0.8;
  }
}

/* Status messages */
.homeScreen__order-stat-alert,
.homeScreen__order-stat-working,
.homeScreen__order-stat-ready,
.homeScreen__order-stat-completed {
  font-size: 0.8rem;
  padding: 0.5rem;
  border-radius: 6px;
  display: flex;
  align-items: center;
  gap: 0.3rem;
  margin-top: 0.5rem;
}

.homeScreen__order-stat-alert {
  background: rgba(243, 156, 18, 0.1);
  color: var(--homeScreen-warning-color);
  border: 1px solid rgba(243, 156, 18, 0.2);
}

.homeScreen__order-stat-alert i {
  color: var(--homeScreen-warning-color);
  flex-shrink: 0;
}

.homeScreen__order-stat-working {
  background: rgba(231, 76, 60, 0.1);
  color: #e74c3c;
  border: 1px solid rgba(231, 76, 60, 0.2);
}

.homeScreen__order-stat-working i {
  color: #e74c3c;
  flex-shrink: 0;
}

.homeScreen__order-stat-ready {
  background: rgba(39, 174, 96, 0.1);
  color: var(--homeScreen-success-color);
  border: 1px solid rgba(39, 174, 96, 0.2);
}

.homeScreen__order-stat-ready i {
  color: var(--homeScreen-success-color);
  flex-shrink: 0;
}

.homeScreen__order-stat-completed {
  background: rgba(52, 152, 219, 0.1);
  color: var(--homeScreen-secondary-color);
  border: 1px solid rgba(52, 152, 219, 0.2);
}

.homeScreen__order-stat-completed i {
  color: var(--homeScreen-secondary-color);
  flex-shrink: 0;
}

/* Enhanced hover effects for order stats */
.homeScreen__order-stat:hover .homeScreen__order-stat-icon-wrapper i {
  transform: scale(1.1);
}

.homeScreen__order-stat--pending:hover .homeScreen__order-stat-icon-wrapper {
  animation: shake 0.5s ease-in-out;
}

.homeScreen__order-stat--ready:hover .homeScreen__order-stat-icon-wrapper {
  animation: gentle-bounce 2s infinite;
}

@keyframes shake {
  0%, 100% { transform: translateX(0); }
  25% { transform: translateX(-2px); }
  75% { transform: translateX(2px); }
}

@keyframes gentle-bounce {
  0%, 100% { transform: translateY(0); }
  50% { transform: translateY(-3px); }
}

/* ================ */
/* Section Headers and Actions */
/* ================ */

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex-wrap: wrap;
  gap: 1rem;
  margin-bottom: 1.5rem;
}

.homeScreen__popular-products-title {
  color: var(--homeScreen-primary-color);
  font-size: 1.5rem;
  font-weight: 600;
  margin: 0;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.homeScreen__popular-products-title i {
  color: var(--homeScreen-warning-color);
  font-size: 1.3rem;
}

.header-actions {
  display: flex;
  gap: 0.5rem;
  align-items: center;
}

.view-toggle {
  display: flex;
  align-items: center;
  gap: 0.4rem;
  padding: 0.5rem 1rem;
  background: var(--homeScreen-white);
  border: 2px solid #e9ecef;
  color: var(--homeScreen-primary-color);
  border-radius: 8px;
  font-size: 0.85rem;
  font-weight: 500;
  cursor: pointer;
  transition: var(--homeScreen-transition);
}

.view-toggle:hover {
  border-color: var(--homeScreen-secondary-color);
  color: var(--homeScreen-secondary-color);
  transform: translateY(-1px);
  box-shadow: 0 2px 8px rgba(52, 152, 219, 0.2);
}

.view-toggle.active {
  background: linear-gradient(135deg, var(--homeScreen-success-color), #229954);
  border-color: var(--homeScreen-success-color);
  color: var(--homeScreen-white);
  box-shadow: 0 4px 12px rgba(39, 174, 96, 0.3);
}

.view-toggle.active:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 16px rgba(39, 174, 96, 0.4);
}

/* ================ */
/* Pagination Controls - Enhanced Design */
/* ================ */

.products-pagination-controls {
  margin-top: 1.5rem;
}

.pagination-info {
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex-wrap: wrap;
  gap: 1rem;
  margin-bottom: 1rem;
}

.total-products-count {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-size: 0.9rem;
  color: var(--homeScreen-primary-color);
  font-weight: 500;
}

.total-products-count i {
  color: var(--homeScreen-secondary-color);
}

.view-options {
  display: flex;
  gap: 0.5rem;
}

.view-option-btn {
  display: flex;
  align-items: center;
  gap: 0.4rem;
  padding: 0.5rem 1rem;
  background: var(--homeScreen-white);
  border: 2px solid #e9ecef;
  color: var(--homeScreen-primary-color);
  border-radius: 8px;
  font-size: 0.85rem;
  font-weight: 500;
  cursor: pointer;
  transition: var(--homeScreen-transition);
}

.view-option-btn:hover {
  border-color: var(--homeScreen-secondary-color);
  color: var(--homeScreen-secondary-color);
  transform: translateY(-1px);
  box-shadow: 0 2px 8px rgba(52, 152, 219, 0.2);
}

.view-option-btn.active {
  background: linear-gradient(135deg, var(--homeScreen-secondary-color), #2980b9);
  border-color: var(--homeScreen-secondary-color);
  color: var(--homeScreen-white);
  box-shadow: 0 4px 12px rgba(52, 152, 219, 0.3);
}

.view-option-btn.active:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 16px rgba(52, 152, 219, 0.4);
}

.pagination-controls {
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex-wrap: wrap;
  gap: 1rem;
  margin-top: 2rem;
  padding: 1.5rem;
  background: var(--homeScreen-white);
  border-radius: 12px;
  border: 1px solid #e9ecef;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.08);
}

.items-per-page {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.items-per-page label {
  font-size: 0.9rem;
  font-weight: 500;
  color: var(--homeScreen-primary-color);
}

.per-page-select {
  padding: 0.5rem 0.75rem;
  border: 2px solid #e9ecef;
  border-radius: 8px;
  background: var(--homeScreen-white);
  color: var(--homeScreen-primary-color);
  font-size: 0.9rem;
  font-weight: 500;
  cursor: pointer;
  transition: var(--homeScreen-transition);
  min-width: 60px;
}

.per-page-select:focus {
  outline: none;
  border-color: var(--homeScreen-secondary-color);
  box-shadow: 0 0 0 3px rgba(52, 152, 219, 0.1);
}

.per-page-select:hover {
  border-color: var(--homeScreen-secondary-color);
}

.pagination-buttons {
  flex: 1;
  display: flex;
  justify-content: center;
}

.page-navigation {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  flex-wrap: wrap;
}

.page-btn {
  display: flex;
  align-items: center;
  gap: 0.3rem;
  padding: 0.6rem 1rem;
  background: var(--homeScreen-white);
  border: 2px solid var(--homeScreen-secondary-color);
  color: var(--homeScreen-secondary-color);
  border-radius: 8px;
  font-size: 0.9rem;
  font-weight: 500;
  cursor: pointer;
  transition: var(--homeScreen-transition);
  text-decoration: none;
}

.page-btn:hover:not(:disabled) {
  background: var(--homeScreen-secondary-color);
  color: var(--homeScreen-white);
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(52, 152, 219, 0.3);
}

.page-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
  background: #f8f9fa;
  border-color: #dee2e6;
  color: #6c757d;
}

.page-btn:disabled:hover {
  transform: none;
  box-shadow: none;
}

.page-numbers {
  display: flex;
  gap: 0.25rem;
}

.page-number {
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: var(--homeScreen-white);
  border: 2px solid #e9ecef;
  color: var(--homeScreen-primary-color);
  border-radius: 8px;
  font-size: 0.9rem;
  font-weight: 600;
  cursor: pointer;
  transition: var(--homeScreen-transition);
}

.page-number:hover {
  border-color: var(--homeScreen-secondary-color);
  color: var(--homeScreen-secondary-color);
  transform: translateY(-1px);
  box-shadow: 0 2px 8px rgba(52, 152, 219, 0.2);
}

.page-number.active {
  background: linear-gradient(135deg, var(--homeScreen-secondary-color), #2980b9);
  border-color: var(--homeScreen-secondary-color);
  color: var(--homeScreen-white);
  box-shadow: 0 4px 12px rgba(52, 152, 219, 0.3);
}

.page-number.active:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 16px rgba(52, 152, 219, 0.4);
}

.page-info {
  font-size: 0.85rem;
  color: #7f8c8d;
  font-weight: 500;
  padding: 0.5rem 0.75rem;
  background: var(--homeScreen-light-bg);
  border-radius: 6px;
  white-space: nowrap;
}

/* Enhanced hover effects for pagination */
.prev-btn i,
.next-btn i {
  transition: transform 0.2s ease;
}

.prev-btn:hover:not(:disabled) i {
  transform: translateX(2px);
}

.next-btn:hover:not(:disabled) i {
  transform: translateX(-2px);
}

/* ================ */
/* Responsive Design */
/* ================ */

@media (max-width: 768px) {
  .homeScreen {
    padding: 1rem;
  }
  
  .homeScreen__header {
    flex-direction: column;
    text-align: center;
  }
  
  .homeScreen__title {
    font-size: 2rem;
  }
  
  .homeScreen__stats-grid {
    grid-template-columns: 1fr;
    gap: 1rem;
  }
  
  .homeScreen__orders-stats-grid {
    grid-template-columns: repeat(2, 1fr);
  }
  
  .homeScreen__order-stat {
    padding: 1.2rem;
  }
  
  .homeScreen__order-stat-icon-wrapper i {
    font-size: 2rem;
  }
  
  .homeScreen__order-stat-count {
    font-size: 2rem;
  }
  
  .homeScreen__order-stat-label {
    font-size: 0.9rem;
  }
  
  .orders-total-badge {
    font-size: 0.75rem;
    padding: 0.25rem 0.6rem;
  }
  
  .homeScreen__orders-stats-title {
    font-size: 1.3rem;
    flex-wrap: wrap;
    justify-content: center;
    text-align: center;
  }
  
  .homeScreen__control-buttons {
    width: 100%;
    justify-content: center;
  }
  
  .homeScreen__refresh-btn {
    width: 100%;
    justify-content: center;
  }
  
  .homeScreen__popular-products-grid {
    grid-template-columns: 1fr;
  }
  
  .homeScreen__product-stats {
    grid-template-columns: repeat(2, 1fr);
  }
  
  .homeScreen__popularity-text {
    font-size: 0.8rem;
    padding: 0.15rem 0.4rem;
    min-width: 70px;
  }
  
  .homeScreen__popular-product-card {
    padding: 1rem;
  }
  
  /* Product rank badge responsive styles */
  .homeScreen__product-rank-badge {
    width: 40px;
    height: 40px;
    top: -8px;
    right: -8px;
  }
  
  .homeScreen__product-rank-badge .rank-number {
    font-size: 0.75rem;
  }
  
  .homeScreen__product-rank-badge .rank-icon i {
    font-size: 0.6rem;
  }
  
  .homeScreen__popular-product-card:nth-child(1) .homeScreen__product-rank-badge .rank-icon,
  .homeScreen__popular-product-card:nth-child(2) .homeScreen__product-rank-badge .rank-icon,
  .homeScreen__popular-product-card:nth-child(3) .homeScreen__product-rank-badge .rank-icon {
    font-size: 1rem;
  }
  
  .homeScreen__popular-product-card:nth-child(n+4) .homeScreen__product-rank-badge .rank-number {
    font-size: 0.9rem;
  }
  
  /* Section header responsive styles */
  .section-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 1rem;
  }
  
  .homeScreen__popular-products-title {
    font-size: 1.3rem;
    width: 100%;
    justify-content: center;
  }
  
  .header-actions {
    width: 100%;
    justify-content: center;
  }
  
  .view-toggle {
    flex: 1;
    justify-content: center;
    min-width: 140px;
  }
  
  /* Pagination responsive styles */
  .pagination-info {
    flex-direction: column;
    align-items: flex-start;
    gap: 0.75rem;
  }
  
  .view-options {
    width: 100%;
    justify-content: center;
  }
  
  .view-option-btn {
    flex: 1;
    justify-content: center;
    min-width: 120px;
  }
  
  .pagination-controls {
    flex-direction: column;
    gap: 1rem;
    padding: 1rem;
  }
  
  .page-navigation {
    justify-content: center;
    gap: 0.3rem;
  }
  
  .page-btn {
    padding: 0.5rem 0.75rem;
    font-size: 0.8rem;
  }
  
  .page-number {
    width: 35px;
    height: 35px;
    font-size: 0.8rem;
  }
  
  .page-info {
    font-size: 0.8rem;
    text-align: center;
  }
}

@media (max-width: 480px) {
  .homeScreen__orders-stats-grid {
    grid-template-columns: 1fr;
  }
  
  .homeScreen__order-stat {
    padding: 1rem;
  }
  
  .homeScreen__order-stat-icon-wrapper i {
    font-size: 1.8rem;
  }
  
  .homeScreen__order-stat-count {
    font-size: 1.8rem;
  }
  
  .homeScreen__order-stat-label {
    font-size: 0.85rem;
  }
  
  .orders-total-badge {
    font-size: 0.7rem;
    padding: 0.2rem 0.5rem;
    display: block;
    text-align: center;
    margin: 0.5rem 0 0 0;
  }
  
  .homeScreen__orders-stats-title {
    font-size: 1.2rem;
    flex-direction: column;
    gap: 0.5rem;
  }
  
  .homeScreen__order-stat-alert,
  .homeScreen__order-stat-working,
  .homeScreen__order-stat-ready,
  .homeScreen__order-stat-completed {
    font-size: 0.75rem;
    padding: 0.4rem;
  }
  
  .urgent-indicator,
  .active-indicator,
  .ready-indicator,
  .success-indicator {
    width: 18px;
    height: 18px;
    font-size: 0.55rem;
  }
  
  .homeScreen__daily-orders-summary {
    grid-template-columns: 1fr;
  }
  
  .homeScreen__product-stats {
    grid-template-columns: 1fr;
  }
  
  .homeScreen__popularity-bar {
    height: 35px;
  }
  
  .homeScreen__popularity-text {
    font-size: 0.75rem;
    min-width: 60px;
  }
  
  /* Section header mobile styles */
  .homeScreen__popular-products-title {
    font-size: 1.2rem;
    text-align: center;
  }
  
  .view-toggle {
    width: 100%;
    padding: 0.6rem;
    font-size: 0.8rem;
  }
  
  /* Product rank badge mobile styles */
  .homeScreen__product-rank-badge {
    width: 35px;
    height: 35px;
    top: -6px;
    right: -6px;
  }
  
  .homeScreen__product-rank-badge .rank-number {
    font-size: 0.7rem;
  }
  
  .homeScreen__product-rank-badge .rank-icon i {
    font-size: 0.55rem;
  }
  
  .homeScreen__popular-product-card:nth-child(1) .homeScreen__product-rank-badge .rank-icon,
  .homeScreen__popular-product-card:nth-child(2) .homeScreen__product-rank-badge .rank-icon,
  .homeScreen__popular-product-card:nth-child(3) .homeScreen__product-rank-badge .rank-icon {
    font-size: 0.9rem;
  }
  
  .homeScreen__popular-product-card:nth-child(n+4) .homeScreen__product-rank-badge .rank-number {
    font-size: 0.8rem;
  }
  
  /* Mobile pagination styles */
  .pagination-info {
    text-align: center;
  }
  
  .total-products-count {
    justify-content: center;
    font-size: 0.8rem;
  }
  
  .view-options {
    flex-direction: column;
    gap: 0.5rem;
  }
  
  .view-option-btn {
    width: 100%;
    font-size: 0.8rem;
    padding: 0.6rem;
  }
  
  .pagination-controls {
    padding: 0.75rem;
  }
  
  .items-per-page {
    flex-direction: column;
    align-items: flex-start;
    gap: 0.25rem;
  }
  
  .page-navigation {
    flex-direction: column;
    gap: 0.5rem;
  }
  
  .page-numbers {
    order: 2;
    justify-content: center;
  }
  
  .prev-btn {
    order: 1;
    width: 100%;
    justify-content: center;
  }
  
  .next-btn {
    order: 3;
    width: 100%;
    justify-content: center;
  }
  
  .page-info {
    order: 4;
    margin-top: 0.5rem;
  }
  
  .page-btn {
    padding: 0.6rem 1rem;
    font-size: 0.85rem;
  }
  
  .page-number {
    width: 32px;
    height: 32px;
    font-size: 0.75rem;
  }
}


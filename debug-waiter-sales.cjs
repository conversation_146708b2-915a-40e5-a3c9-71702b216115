const fetch = require('node-fetch');

const backEndURL = 'https://deshacoffee-production.up.railway.app';

async function debugWaiterSales() {
  try {
    console.log('🔐 تسجيل الدخول...');
    
    // تسجيل الدخول
    const loginResponse = await fetch(`${backEndURL}/api/v1/auth/login`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ username: '<PERSON><PERSON>', password: 'MOHAMEDmostafa123' })
    });
    
    const loginResult = await loginResponse.json();
    const token = loginResult.token;
    console.log('✅ تم تسجيل الدخول بنجاح');
    
    // جلب جميع الطلبات
    console.log('📋 جلب جميع الطلبات...');
    const ordersResponse = await fetch(`${backEndURL}/api/v1/orders?limit=999999`, {
      headers: { 'Authorization': `Bear<PERSON> ${token}` }
    });
    
    const ordersData = await ordersResponse.json();
    let allOrders = [];
    
    if (Array.isArray(ordersData)) {
      allOrders = ordersData;
    } else if (ordersData.success && Array.isArray(ordersData.data)) {
      allOrders = ordersData.data;
    } else if (ordersData.orders) {
      allOrders = ordersData.orders;
    }
    
    console.log(`📊 إجمالي الطلبات في النظام: ${allOrders.length}`);
    
    // فحص النادلة sara
    console.log('\n🔍 فحص طلبات النادلة sara:');
    
    // أولاً البحث عن user sara
    const usersResponse = await fetch(backEndURL + '/api/users', {
      headers: {
        'Authorization': `Bearer ${token}`,
        'Content-Type': 'application/json'
      }
    });
    
    let saraUser = null;
    if (usersResponse.ok) {
      const usersData = await usersResponse.json();
      const users = usersData.users || usersData.data || usersData;
      if (Array.isArray(users)) {
        saraUser = users.find(user => 
          user.username === 'sara' || 
          user.name === 'sara' ||
          user.username === 'Sara' || 
          user.name === 'Sara'
        );
      }
    }
    
    console.log('👤 معلومات sara:', saraUser ? `ID: ${saraUser._id}, Name: ${saraUser.name || saraUser.username}` : 'لم يتم العثور عليها');
    
    const saraOrders = allOrders.filter(order => {
      if (!saraUser) return false;
      
      return (
        order.waiterName === 'sara' || 
        order.waiterName === 'Sara' ||
        order.waiterName === 'SARA' ||
        order.waiterId === saraUser._id ||
        (order.staff && order.staff.waiter === saraUser._id)
      );
    });
    
    console.log(`📋 عدد طلبات sara: ${saraOrders.length}`);
    
    if (saraOrders.length > 0) {
      console.log('\n📋 تفاصيل طلبات sara:');
      let totalAmount = 0;
      
      saraOrders.forEach((order, index) => {
        const amount = order.totals?.total || order.totalPrice || order.totalAmount || 0;
        totalAmount += amount;
        
        console.log(`${index + 1}. طلب #${order.orderNumber || order._id}`);
        console.log(`   - المبلغ: ${amount} جنيه`);
        console.log(`   - الحالة: ${order.status}`);
        console.log(`   - التاريخ: ${new Date(order.createdAt).toLocaleDateString('ar-EG')}`);
        console.log(`   - البيانات الخام:`, {
          totals: order.totals,
          totalPrice: order.totalPrice,
          totalAmount: order.totalAmount,
          finalAmount: order.finalAmount
        });
        console.log('');
      });
      
      console.log(`💰 إجمالي مبيعات sara (حساب يدوي): ${totalAmount.toFixed(2)} جنيه`);
    }
    
    // جلب النتائج من API الإحصائيات
    console.log('\n🔍 جلب النتائج من API الإحصائيات...');
    const statsResponse = await fetch(`${backEndURL}/api/v1/waiter-stats`, {
      headers: { 'Authorization': `Bearer ${token}` }
    });
    
    const statsData = await statsResponse.json();
    
    if (statsData.success) {
      const saraStats = statsData.data.waiters.find(w => 
        w.waiterName === 'sara' || 
        w.waiterName === 'Sara' ||
        w.waiterName === 'SARA'
      );
      
      if (saraStats) {
        console.log(`💰 إجمالي مبيعات sara (من API): ${saraStats.totalAmount} جنيه`);
        console.log(`📋 عدد طلبات sara (من API): ${saraStats.totalOrders}`);
      } else {
        console.log('❌ لم يتم العثور على sara في نتائج API');
      }
    }
    
    // فحص جميع النادلين من خلال الـ Users API
    console.log('\n👥 فحص جميع النادلين:');
    
    // جلب جميع النادلين من API
    let allUsers = [];
    if (usersResponse.ok) {
      const usersData = await usersResponse.json();
      allUsers = usersData.users || usersData.data || usersData;
    }
    
    const waiters = Array.isArray(allUsers) ? allUsers.filter(user => user.role === 'waiter') : [];
    
    console.log(`👥 وجد ${waiters.length} نادل في النظام`);
    
    const waiterStats = {};
    
    waiters.forEach(waiter => {
      const waiterOrders = allOrders.filter(order => {
        return (
          order.waiterName === waiter.username || 
          order.waiterName === waiter.name ||
          order.waiterId === waiter._id ||
          (order.staff && order.staff.waiter === waiter._id)
        );
      });
      
      const totalAmount = waiterOrders.reduce((sum, order) => {
        return sum + (order.totals?.total || order.totalPrice || order.totalAmount || 0);
      }, 0);
      
      waiterStats[waiter.name || waiter.username] = {
        orders: waiterOrders.length,
        totalAmount: totalAmount,
        waiterInfo: waiter
      };
    });

    console.log('\n📊 ملخص جميع النادلين:');
    Object.keys(waiterStats).forEach(waiterName => {
      const data = waiterStats[waiterName];
      console.log(`👤 ${waiterName}: ${data.orders} طلب، ${data.totalAmount.toFixed(2)} جنيه`);
    });
    
  } catch (error) {
    console.error('❌ خطأ في فحص المبيعات:', error);
  }
}

debugWaiterSales();

/* Enhanced Menu Item Card Styles */
/* استيراد المتغيرات المميزة لمكونات القائمة */
@import '../variables/menu-variables.css';

.menu-item-card-enhanced {
  background: linear-gradient(145deg, #ffffff 0%, #f8f9fa 100%);
  border-radius: 20px;
  box-shadow: 0 15px 50px rgba(0, 0, 0, 0.15);
  transition: all 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
  border: none;
  position: relative;
  overflow: hidden;
  min-height: 300px;
  width: 100%;
  height: 100%;
  cursor: pointer;
  margin: 0;
  display: flex;
  flex-direction: column;
}

.menu-item-card-enhanced::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.1), rgba(255, 255, 255, 0.05));
  pointer-events: none;
  transition: opacity 0.3s ease;
}

.menu-item-card-enhanced:hover {
  transform: translateY(-8px) scale(1.02);
  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.15);
}

.menu-item-card-enhanced:hover::before {
  opacity: 0.8;
}

.menu-item-card-enhanced.unavailable {
  opacity: 0.7;
  filter: grayscale(0.3);
}

/* Status Bar */
.menu-status-bar {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  border-radius: 16px 16px 0 0;
}

.menu-status-bar.available {
  background: linear-gradient(90deg, #27ae60, #2ecc71);
}

.menu-status-bar.unavailable {
  background: linear-gradient(90deg, #e74c3c, #c0392b);
}

/* Floating Price Indicator */
.floating-price-indicator {
  position: absolute;
  top: 15px;
  right: 15px;
  min-width: 50px;
  height: 50px;
  border-radius: 25px;
  background: linear-gradient(135deg, #27ae60, #2ecc71);
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-weight: 700;
  z-index: 3;
  box-shadow: 0 8px 25px rgba(39, 174, 96, 0.3);
  transition: all 0.3s ease;
  padding: 0 0.75rem;
}

.menu-item-card-enhanced:hover .floating-price-indicator {
  transform: scale(1.1);
  box-shadow: 0 12px 35px rgba(39, 174, 96, 0.4);
}

.floating-price-indicator.unavailable {
  background: linear-gradient(135deg, #e74c3c, #c0392b);
  box-shadow: 0 8px 25px rgba(231, 76, 60, 0.3);
}

.floating-price-indicator.unavailable:hover {
  box-shadow: 0 12px 35px rgba(231, 76, 60, 0.4);
}

.floating-price-indicator span {
  font-size: 0.8rem;
  font-weight: 800;
  white-space: nowrap;
}

/* Header */
.menu-card-header {
  text-align: center;
  padding: 1.25rem 1rem 0.75rem;
}

.menu-item-icon {
  width: 55px;
  height: 55px;
  margin: 0 auto 0.8rem;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, #667eea, #764ba2);
  transition: all 0.3s ease;
  font-size: 1.6rem;
  color: white;
  box-shadow: 0 8px 25px rgba(102, 126, 234, 0.3);
}

.menu-item-card-enhanced:hover .menu-item-icon {
  transform: scale(1.1) rotate(5deg);
  box-shadow: 0 12px 35px rgba(102, 126, 234, 0.4);
}

.menu-item-name {
  font-size: 1.1rem;
  font-weight: 700;
  color: #2c3e50;
  margin-bottom: 0.8rem;
  line-height: 1.3;
}

.menu-category-badge {
  display: inline-block;
  padding: 0.3rem 0.8rem;
  background: linear-gradient(135deg, rgba(102, 126, 234, 0.1), rgba(118, 75, 162, 0.05));
  color: #667eea;
  border-radius: 16px;
  font-size: 0.8rem;
  font-weight: 600;
  border: 1px solid rgba(102, 126, 234, 0.2);
  transition: all 0.3s ease;
}

.menu-category-badge:hover {
  background: linear-gradient(135deg, rgba(102, 126, 234, 0.15), rgba(118, 75, 162, 0.08));
  transform: translateY(-1px);
}

/* Body */
.menu-card-body {
  padding: 0 1.5rem 1.5rem;
  display: flex;
  flex-direction: column;
  flex: 1;
  justify-content: space-between;
  min-height: 0;
}

/* Status Section */
.menu-status-section {
  display: flex;
  justify-content: center;
  margin-bottom: 1rem;
}

.availability-badge {
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.6rem 1.2rem;
  border-radius: 20px;
  font-weight: 600;
  font-size: 0.9rem;
  transition: all 0.3s ease;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
}

.availability-badge.available {
  background: linear-gradient(135deg, #d4edda, #c3e6cb);
  color: #155724;
  border: 1px solid #c3e6cb;
}

.availability-badge.unavailable {
  background: linear-gradient(135deg, #f8d7da, #f5c6cb);
  color: #721c24;
  border: 1px solid #f5c6cb;
}

.availability-badge:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(0, 0, 0, 0.15);
}

/* Description */
.menu-description {
  margin-bottom: 1rem;
}

.menu-description p {
  font-size: 0.9rem;
  color: #6c757d;
  line-height: 1.4;
  margin: 0;
  font-style: italic;
}

/* Details Grid */
.menu-details-grid {
  display: flex;
  gap: 0.75rem;
  margin-bottom: 1rem;
}

.menu-detail-item {
  display: flex;
  align-items: center;
  flex: 1;
  padding: 0.5rem;
  background: rgba(248, 249, 250, 0.5);
  border-radius: 8px;
  transition: all 0.3s ease;
}

.menu-detail-item:hover {
  background: rgba(248, 249, 250, 0.8);
  transform: translateY(-2px);
}

.menu-detail-item .detail-icon {
  width: 28px;
  height: 28px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-left: 0.5rem;
  font-size: 0.8rem;
  color: white;
  flex-shrink: 0;
}

.menu-detail-item .detail-icon.time {
  background: linear-gradient(135deg, #f39c12, #e67e22);
}

.menu-detail-item .detail-icon.calories {
  background: linear-gradient(135deg, #e74c3c, #c0392b);
}

.menu-detail-item .detail-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 0.1rem;
}

.menu-detail-item .detail-label {
  font-size: 0.7rem;
  color: #6c757d;
  font-weight: 500;
}

.menu-detail-item .detail-value {
  font-size: 0.8rem;
  color: #2c3e50;
  font-weight: 600;
}

/* Ingredients */
.menu-ingredients {
  margin-bottom: 1rem;
}

.ingredients-header {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  margin-bottom: 0.5rem;
  color: #6c757d;
  font-weight: 600;
  font-size: 0.8rem;
}

.ingredients-list {
  display: flex;
  flex-wrap: wrap;
  gap: 0.25rem;
}

.ingredient-tag {
  display: inline-block;
  padding: 0.2rem 0.5rem;
  background: linear-gradient(135deg, rgba(52, 152, 219, 0.1), rgba(52, 152, 219, 0.05));
  color: #3498db;
  border-radius: 8px;
  font-size: 0.7rem;
  font-weight: 500;
  border: 1px solid rgba(52, 152, 219, 0.2);
}

.ingredient-tag.more {
  background: linear-gradient(135deg, rgba(108, 117, 125, 0.1), rgba(108, 117, 125, 0.05));
  color: #6c757d;
  border-color: rgba(108, 117, 125, 0.2);
}

/* Actions */
.menu-actions {
  display: flex;
  gap: 0.5rem;
  margin-top: auto;
  padding-top: 1rem;
  border-top: 1px solid rgba(0, 0, 0, 0.05);
}

.menu-action-btn {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 0.25rem;
  padding: 0.75rem 0.5rem;
  border: 2px solid transparent;
  border-radius: 12px;
  background: transparent;
  transition: all 0.3s ease;
  cursor: pointer;
  font-weight: 600;
  font-size: 0.8rem;
}

.menu-action-btn:hover {
  transform: translateY(-2px);
}

.menu-action-btn.edit {
  color: #3498db;
  border-color: rgba(52, 152, 219, 0.3);
  background: rgba(52, 152, 219, 0.05);
}

.menu-action-btn.edit:hover {
  background: rgba(52, 152, 219, 0.1);
  border-color: #3498db;
}

.menu-action-btn.toggle.hide {
  color: #f39c12;
  border-color: rgba(243, 156, 18, 0.3);
  background: rgba(243, 156, 18, 0.05);
}

.menu-action-btn.toggle.hide:hover {
  background: rgba(243, 156, 18, 0.1);
  border-color: #f39c12;
}

.menu-action-btn.toggle.show {
  color: #27ae60;
  border-color: rgba(39, 174, 96, 0.3);
  background: rgba(39, 174, 96, 0.05);
}

.menu-action-btn.toggle.show:hover {
  background: rgba(39, 174, 96, 0.1);
  border-color: #27ae60;
}

.menu-action-btn.delete {
  color: #e74c3c;
  border-color: rgba(231, 76, 60, 0.3);
  background: rgba(231, 76, 60, 0.05);
}

.menu-action-btn.delete:hover {
  background: rgba(231, 76, 60, 0.1);
  border-color: #e74c3c;
}

.menu-action-btn i {
  font-size: 1rem;
}

.menu-action-btn span {
  font-size: 0.75rem;
}

/* Responsive Design */
@media (max-width: 768px) {
  .menu-item-card-enhanced {
    min-height: 300px;
    min-width: 260px;
    max-width: 320px;
    margin: 0.75rem;
  }

  .floating-price-indicator {
    min-width: 50px;
    height: 45px;
    top: 12px;
    right: 12px;
    padding: 0 0.7rem;
  }

  .floating-price-indicator span {
    font-size: 0.8rem;
  }

  .menu-card-header {
    padding: 1rem 1.2rem 0.75rem;
  }

  .menu-item-icon {
    width: 70px;
    height: 70px;
    font-size: 2rem;
  }

  .menu-item-name {
    font-size: 1.3rem;
  }

  .menu-card-body {
    padding: 0 1.5rem 1.5rem;
  }

  .menu-category-badge {
    padding: 0.4rem 1rem;
    font-size: 0.85rem;
  }

  .availability-badge {
    padding: 0.6rem 1.2rem;
    font-size: 0.9rem;
  }

  .menu-details-grid {
    flex-direction: column;
    gap: 0.5rem;
  }

  .menu-action-btn {
    padding: 0.7rem 0.4rem;
    font-size: 0.8rem;
  }

  .menu-action-btn i {
    font-size: 1rem;
  }

  .menu-action-btn span {
    font-size: 0.75rem;
  }
}

@media (max-width: 480px) {
  .menu-item-card-enhanced {
    min-height: 280px;
    min-width: 240px;
    max-width: 280px;
    margin: 0.5rem;
  }

  .floating-price-indicator {
    min-width: 45px;
    height: 40px;
    padding: 0 0.6rem;
  }

  .floating-price-indicator span {
    font-size: 0.75rem;
  }

  .menu-card-header {
    padding: 0.75rem 1rem 0.5rem;
  }

  .menu-item-icon {
    width: 60px;
    height: 60px;
    font-size: 1.8rem;
  }

  .menu-item-name {
    font-size: 1.2rem;
  }

  .menu-card-body {
    padding: 0 1.2rem 1.2rem;
  }

  .menu-category-badge {
    padding: 0.3rem 0.8rem;
    font-size: 0.8rem;
  }

  .availability-badge {
    padding: 0.5rem 1rem;
    font-size: 0.85rem;
  }

  .menu-action-btn {
    padding: 0.6rem 0.3rem;
    font-size: 0.75rem;
  }

  .menu-action-btn i {
    font-size: 0.9rem;
  }

  .menu-action-btn span {
    font-size: 0.7rem;
  }
}

/* Grid Layout Fixes for Desktop */
@media (min-width: 992px) {
  .menu-item-card-enhanced {
    min-height: 320px;
    max-height: 400px;
  }
}

@media (min-width: 1200px) {
  .menu-item-card-enhanced {
    min-height: 340px;
    max-height: 420px;
  }
}

@media (min-width: 1400px) {
  .menu-item-card-enhanced {
    min-height: 350px;
    max-height: 450px;
  }
}

/* Ensure proper spacing in grid */
.row .col-12,
.row .col-6,
.row .col-4,
.row .col-3,
.row .col-2 {
  margin-bottom: 1.5rem;
}

/* Fix for overlapping cards */
.menu-item-card-enhanced {
  box-sizing: border-box;
  align-self: stretch;
}

/* Enhanced card body to prevent overflow */
.menu-card-body {
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}

/* Desktop Grid Stability */
@media (min-width: 768px) {
  .container-fluid .row {
    margin-left: -12px;
    margin-right: -12px;
  }
  
  .container-fluid .row > [class*="col-"] {
    padding-left: 12px;
    padding-right: 12px;
  }
  
  .menu-item-card-enhanced {
    margin-bottom: 1.5rem;
  }
}

/* Prevent card expansion beyond container */
.menu-item-card-enhanced {
  max-width: 100%;
  word-wrap: break-word;
  overflow-wrap: break-word;
}

/* Ensure consistent card heights in rows */
.row {
  display: flex;
  flex-wrap: wrap;
  align-items: stretch;
}

.row > [class*="col-"] {
  display: flex;
  flex-direction: column;
}

/* Fix for floating elements */
.menu-item-card-enhanced * {
  box-sizing: border-box;
}

/* Prevent content overflow */
.menu-item-card-enhanced {
  contain: layout style;
}


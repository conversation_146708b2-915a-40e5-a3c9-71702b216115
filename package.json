{"name": "desha-coffee", "private": false, "version": "1.0.2", "description": "نظام إدارة المقهى المتكامل - تطبيق ويب حديث لإدارة المقاهي والمطاعم", "type": "module", "keywords": ["coffee", "restaurant", "management", "pos", "react", "nodejs", "mongodb", "<PERSON><PERSON>", "cafe"], "author": "MediaFuture Team <<EMAIL>>", "license": "MIT", "repository": {"type": "git", "url": "https://github.com/MediaFuture/DeshaCoffee.git"}, "bugs": {"url": "https://github.com/MediaFuture/DeshaCoffee/issues"}, "homepage": "https://github.com/MediaFuture/DeshaCoffee#readme", "scripts": {"dev": "vite", "dev:local": "vite --mode development --host 0.0.0.0 --port 3000", "dev:production": "vite --mode production", "build": "vite build", "build:check": "tsc -b && vite build", "build:vercel": "vite build --mode production", "build:simple": "vite build", "lint": "eslint .", "preview": "vite preview", "preview:production": "vite preview --mode production", "clean": "<PERSON><PERSON><PERSON> dist", "build:clean": "npm run clean && npm run build", "start": "npm run preview", "backend:dev": "cd backend && npm run dev", "backend:dev:local": "cd backend && npm run dev:local", "backend:start": "cd backend && npm start", "backend:install": "cd backend && npm install", "install:all": "npm install --legacy-peer-deps && npm run backend:install", "dev:all": "concurrently \"npm run dev\" \"npm run backend:dev\"", "dev:all:local": "concurrently \"npm run dev:local\" \"npm run backend:dev:local\"", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "test:backend": "cd backend && npm test", "test:all": "npm run test && npm run test:backend", "test:workflow": "node test-workflow.js", "format": "prettier --write .", "format:check": "prettier --check .", "fix-deps": "rm -rf node_modules package-lock.json && npm install --legacy-peer-deps", "postinstall": "echo 'Dependencies installed successfully!'", "mcp:install": "npm install -g @upstash/context7-mcp", "mcp:start": "npx @upstash/context7-mcp", "mcp:dev": "npx -y @upstash/context7-mcp --dev", "seed:tables": "node backend/scripts/seedTables.js"}, "dependencies": {"@fortawesome/fontawesome-free": "^6.7.2", "@popperjs/core": "^2.11.8", "axios": "^1.9.0", "bcrypt": "^6.0.0", "bcryptjs": "^3.0.2", "bootstrap": "^5.3.7", "colors": "^1.4.0", "mongodb": "^6.16.0", "mongoose": "^8.15.2", "node-fetch": "^2.7.0", "react": "^19.1.0", "react-dom": "^19.1.0", "react-router-dom": "^7.6.0", "socket.io": "^4.8.1", "socket.io-client": "^4.8.1"}, "devDependencies": {"@eslint/js": "^9.25.0", "@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^16.0.1", "@testing-library/user-event": "^14.5.2", "@types/node": "^22.15.29", "@types/react": "^19.1.2", "@types/react-dom": "^19.1.2", "@types/socket.io-client": "^1.4.36", "@upstash/context7-mcp": "latest", "@vitejs/plugin-react": "^4.4.1", "concurrently": "^8.2.2", "eslint": "^9.25.0", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-react-refresh": "^0.4.19", "globals": "^16.0.0", "identity-obj-proxy": "^3.0.0", "jest": "^29.7.0", "jest-environment-jsdom": "^29.7.0", "jest-transform-stub": "^2.0.0", "prettier": "^3.1.0", "rimraf": "^5.0.5", "ts-jest": "^29.1.1", "typescript": "~5.8.3", "typescript-eslint": "^8.30.1", "vercel": "^42.2.0", "vite": "^6.3.5"}, "overrides": {"@testing-library/react": {"react": "^19.1.0", "react-dom": "^19.1.0"}}, "peerDependenciesMeta": {"react": {"optional": false}, "react-dom": {"optional": false}}}
export const APP_CONFIG = {
  // إعدادات الخادم
  API: {
    BASE_URL: import.meta.env.VITE_API_URL || 'https://deshacoffee-production.up.railway.app',
    TIMEOUT: parseInt(import.meta.env.VITE_API_TIMEOUT) || 30000,
    RETRY_ATTEMPTS: parseInt(import.meta.env.VITE_API_RETRY_ATTEMPTS) || 3,
    RETRY_DELAY: parseInt(import.meta.env.VITE_API_RETRY_DELAY) || 1000
  },

  // إعدادات Socket.IO
  SOCKET: {
    URL: import.meta.env.VITE_SOCKET_URL || 'https://deshacoffee-production.up.railway.app',
    OPTIONS: {
      transports: ['websocket', 'polling'],
      timeout: parseInt(import.meta.env.VITE_SOCKET_TIMEOUT) || 20000,
      reconnection: true,
      reconnectionAttempts: parseInt(import.meta.env.VITE_SOCKET_RECONNECTION_ATTEMPTS) || 5,
      reconnectionDelay: parseInt(import.meta.env.VITE_SOCKET_RECONNECTION_DELAY) || 1000
    }
  },

  // إعدادات التخزين المؤقت
  CACHE: {
    DURATION: parseInt(import.meta.env.VITE_CACHE_DURATION) || 300000, // 5 دقائق
    MAX_SIZE: parseInt(import.meta.env.VITE_CACHE_MAX_SIZE) || 100
  },

  // إعدادات المصادقة
  AUTH: {
    TOKEN_VALIDATION_INTERVAL: parseInt(import.meta.env.VITE_TOKEN_CHECK_INTERVAL) || 300000, // 5 دقائق
    MAX_VALIDATION_ERRORS: parseInt(import.meta.env.VITE_MAX_VALIDATION_ERRORS) || 3
  },

  // الأدوار المتاحة
  ROLES: {
    MANAGER: 'مدير',
    COOK: 'طباخ',
    WAITER: 'نادل'
  },

  // إعدادات التطبيق
  APP: {
    NAME: 'نظام إدارة المقهى',
    VERSION: import.meta.env.VITE_APP_VERSION || '1.0.0',
    ENVIRONMENT: import.meta.env.MODE || 'production',
    LANGUAGE: 'ar',
    DIRECTION: 'rtl'
  },

  // إعدادات الإشعارات
  NOTIFICATIONS: {
    ENABLED: import.meta.env.VITE_NOTIFICATIONS_ENABLED !== 'false',
    DURATION: parseInt(import.meta.env.VITE_NOTIFICATION_DURATION) || 5000,
    POSITION: import.meta.env.VITE_NOTIFICATION_POSITION || 'top-right',
    MAX_NOTIFICATIONS: parseInt(import.meta.env.VITE_MAX_NOTIFICATIONS) || 5
  },

  // إعدادات المراقبة
  MONITORING: {
    ENABLED: import.meta.env.VITE_MONITORING_ENABLED === 'true',
    ENDPOINT: import.meta.env.VITE_MONITORING_ENDPOINT,
    PERFORMANCE_ENABLED: import.meta.env.VITE_PERFORMANCE_MONITORING === 'true'
  },

  // حدود التطبيق
  LIMITS: {
    MAX_FILE_SIZE: parseInt(import.meta.env.VITE_MAX_FILE_SIZE) || 5242880, // 5MB
    MAX_SEARCH_RESULTS: parseInt(import.meta.env.VITE_MAX_SEARCH_RESULTS) || 50,
    MAX_ORDER_ITEMS: parseInt(import.meta.env.VITE_MAX_ORDER_ITEMS) || 20
  }
};

// دالة للتحقق من صحة التكوين
export const validateConfig = (): { valid: boolean; errors: string[] } => {
  const errors: string[] = [];

  // التحقق من URL الخادم
  try {
    new URL(APP_CONFIG.API.BASE_URL);
  } catch {
    errors.push('رابط الخادم غير صالح');
  }

  // التحقق من القيم الرقمية
  if (APP_CONFIG.API.TIMEOUT < 1000) {
    errors.push('مهلة الاتصال قصيرة جداً (أقل من ثانية واحدة)');
  }

  if (APP_CONFIG.CACHE.DURATION < 60000) {
    errors.push('مدة التخزين المؤقت قصيرة جداً (أقل من دقيقة)');
  }

  return {
    valid: errors.length === 0,
    errors
  };
};

// دوال مساعدة
export const getApiUrl = (endpoint: string): string => {
  return `${APP_CONFIG.API.BASE_URL}${endpoint.startsWith('/') ? endpoint : '/' + endpoint}`;
};

export const getSocketUrl = (): string => {
  return APP_CONFIG.SOCKET.URL;
};

export const isDevelopment = (): boolean => {
  return import.meta.env.MODE === 'development';
};

export const isProduction = (): boolean => {
  return import.meta.env.MODE === 'production';
};

export default APP_CONFIG;

const fetch = require('node-fetch');

const backEndURL = 'http://localhost:4010'; // Local backend URL
const productionURL = 'https://deshacoffee-production.up.railway.app';

async function diagnose409Error() {
  console.log('🔍 تشخيص مشكلة الخطأ 409 عند إرسال الطلبات...\n');

  try {
    // Try local first, then production
    let baseURL = backEndURL;
    
    // Test connection to local backend
    try {
      const healthCheck = await fetch(`${baseURL}/health`, { timeout: 5000 });
      console.log('✅ الخادم المحلي متاح');
    } catch (error) {
      console.log('⚠️ الخادم المحلي غير متاح، التبديل إلى الإنتاج...');
      baseURL = productionURL;
    }

    console.log(`🌐 استخدام: ${baseURL}\n`);

    // 1. Login as waiter
    console.log('🔐 تسجيل الدخول كنادل...');
    const loginResponse = await fetch(`${baseURL}/api/auth/login`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        username: 'Bosy',
        password: '253040'
      })
    });

    if (!loginResponse.ok) {
      console.log('❌ فشل تسجيل الدخول:', loginResponse.status);
      return;
    }

    const loginData = await loginResponse.json();
    const token = loginData.token;
    const waiterInfo = loginData.user;
    
    console.log('✅ تم تسجيل الدخول بنجاح');
    console.log('👤 النادل:', waiterInfo.name || waiterInfo.username);
    console.log('🆔 معرف النادل:', waiterInfo.id || waiterInfo._id);

    // 2. Get tables status
    console.log('\n📋 فحص حالة الطاولات...');
    try {
      const tablesResponse = await fetch(`${baseURL}/api/tables`, {
        headers: { 'Authorization': `Bearer ${token}` }
      });
      
      if (tablesResponse.ok) {
        const tables = await tablesResponse.json();
        console.log('📊 إجمالي الطاولات:', tables.length);
        
        // Show occupied tables
        const occupiedTables = tables.filter(t => t.status === 'occupied');
        console.log('🔒 الطاولات المحجوزة:', occupiedTables.length);
        
        occupiedTables.forEach(table => {
          console.log(`  📍 طاولة ${table.number}: محجوزة من قبل النادل ID ${table.assignedWaiter}`);
        });
      }
    } catch (error) {
      console.log('⚠️ لا يمكن الحصول على حالة الطاولات:', error.message);
    }

    // 3. Try to create an order for different tables
    console.log('\n🧪 اختبار إرسال طلبات لطاولات مختلفة...');
    
    const testTables = [1, 2, 3, 5, 10];
    
    for (const tableNum of testTables) {
      console.log(`\n📋 اختبار الطاولة ${tableNum}:`);
      
      const testOrder = {
        items: [
          {
            productId: '6123456789abcdef12345678', // Fake product ID for testing
            quantity: 1,
            specialRequests: 'طلب تجريبي'
          }
        ],
        tableNumber: tableNum,
        customerName: 'عميل تجريبي',
        orderType: 'dine-in',
        specialInstructions: `اختبار الطاولة ${tableNum}`
      };

      try {
        const orderResponse = await fetch(`${baseURL}/api/orders`, {
          method: 'POST',
          headers: {
            'Authorization': `Bearer ${token}`,
            'Content-Type': 'application/json'
          },
          body: JSON.stringify(testOrder)
        });

        console.log(`  📊 النتيجة: ${orderResponse.status} ${orderResponse.statusText}`);
        
        const responseText = await orderResponse.text();
        
        if (orderResponse.status === 409) {
          console.log(`  ❌ خطأ 409: ${responseText}`);
          
          try {
            const errorData = JSON.parse(responseText);
            console.log(`  💬 الرسالة: ${errorData.message || errorData.error}`);
            console.log(`  🔍 الكود: ${errorData.code}`);
          } catch (e) {
            // Ignore JSON parse error
          }
        } else if (orderResponse.status === 404) {
          console.log(`  ⚠️ الطاولة ${tableNum} غير موجودة`);
        } else if (orderResponse.ok) {
          console.log(`  ✅ تم إرسال الطلب بنجاح للطاولة ${tableNum}`);
        } else {
          console.log(`  ❌ خطأ آخر: ${responseText}`);
        }
        
      } catch (error) {
        console.log(`  ❌ خطأ في الاتصال: ${error.message}`);
      }
    }

    console.log('\n📋 ملخص التشخيص:');
    console.log('- الخطأ 409 يحدث عندما تكون الطاولة محجوزة من قبل نادل آخر');
    console.log('- يجب التأكد من أن النادل يستخدم طاولة غير محجوزة أو محجوزة له شخصياً');
    console.log('- يمكن حل المشكلة بتحرير الطاولة أو استخدام طاولة مختلفة');

  } catch (error) {
    console.error('❌ خطأ في التشخيص:', error.message);
    console.error('📊 التفاصيل:', error);
  }
}

// تشغيل التشخيص
diagnose409Error().then(() => {
  console.log('\n✅ انتهى التشخيص');
}).catch(error => {
  console.error('❌ فشل التشخيص:', error);
});

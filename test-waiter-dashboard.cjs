const fetch = require('node-fetch');

const backEndURL = 'https://deshacoffee-production.up.railway.app';

async function testWaiterDashboard() {
  try {
    console.log('🔐 تسجيل الدخول كنادلة sara...');
    
    // تسجيل الدخول بحساب النادلة sara
    const loginResponse = await fetch(`${backEndURL}/api/v1/auth/login`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ username: 'sara', password: '253040' })
    });
    
    const loginResult = await loginResponse.json();
    if (!loginResult.success) {
      console.error('❌ فشل تسجيل الدخول:', loginResult.message);
      return;
    }
    
    const token = loginResult.token;
    const user = loginResult.user;
    console.log('✅ تم تسجيل الدخول بنجاح كـ:', user.name);
    console.log('👤 معرف النادلة:', user._id);
    
    // جلب جميع الطلبات (كما يفعل WaiterDashboard)
    console.log('\n📋 جلب جميع الطلبات...');
    const ordersResponse = await fetch(`${backEndURL}/api/v1/orders?limit=999999`, {
      headers: { 'Authorization': `Bearer ${token}` }
    });
    
    const ordersData = await ordersResponse.json();
    let allOrders = [];
    if (Array.isArray(ordersData)) {
      allOrders = ordersData;
    } else if (ordersData.success && Array.isArray(ordersData.data)) {
      allOrders = ordersData.data;
    } else if (ordersData.data) {
      allOrders = ordersData.data;
    }
    
    console.log(`📊 إجمالي الطلبات في النظام: ${allOrders.length}`);
    
    // فلترة طلبات النادلة sara (كما يفعل WaiterDashboard)
    const waiterId = user.id || user._id; // أخذ الحقل الصحيح
    const waiterName = user.username || user.name;
    
    console.log('🔍 بيانات الفلترة:', {
      waiterId,
      waiterName,
      username: user.username,
      name: user.name
    });
    
    const waiterOrders = allOrders.filter(order => {
      const isCurrentWaiterByid = order.waiterId === waiterId;
      const isCurrentWaiterByName = order.waiterName === waiterName;
      const isCurrentWaiterByUsername = order.waiterName === user.username;
      const isCurrentWaiterByStaff = order.staff && order.staff.waiter === waiterId;
      
      const isCurrentWaiterOrder = isCurrentWaiterByid || isCurrentWaiterByName || isCurrentWaiterByUsername || isCurrentWaiterByStaff;
      
      if (isCurrentWaiterOrder) {
        console.log('✅ طلب للنادلة sara:', {
          orderId: order._id,
          orderNumber: order.orderNumber,
          orderWaiterId: order.waiterId,
          orderWaiterName: order.waiterName,
          orderStaff: order.staff,
          matchedBy: {
            byId: isCurrentWaiterByid,
            byName: isCurrentWaiterByName,
            byUsername: isCurrentWaiterByUsername,
            byStaff: isCurrentWaiterByStaff
          }
        });
      }
      
      return isCurrentWaiterOrder;
    });
    
    console.log(`\n📊 طلبات النادلة sara: ${waiterOrders.length} طلب`);
    
    if (waiterOrders.length > 0) {
      // حساب إحصائيات النادلة
      const totalSales = waiterOrders.reduce((sum, order) => {
        return sum + (order.totals?.total || order.totalPrice || order.totalAmount || 0);
      }, 0);
      
      const pendingOrders = waiterOrders.filter(order => order.status === 'pending').length;
      const preparingOrders = waiterOrders.filter(order => order.status === 'preparing').length;
      const readyOrders = waiterOrders.filter(order => order.status === 'ready').length;
      const completedOrders = waiterOrders.filter(order => 
        order.status === 'completed' || order.status === 'delivered'
      ).length;
      
      console.log('\n📈 إحصائيات النادلة sara:');
      console.log(`💰 إجمالي المبيعات: ${totalSales.toFixed(2)} جنيه`);
      console.log(`📋 إجمالي الطلبات: ${waiterOrders.length}`);
      console.log(`⏳ معلقة: ${pendingOrders}`);
      console.log(`🔄 قيد التحضير: ${preparingOrders}`);
      console.log(`✅ جاهزة: ${readyOrders}`);
      console.log(`🎯 مكتملة: ${completedOrders}`);
      
      console.log('\n📋 تفاصيل الطلبات:');
      waiterOrders.forEach((order, index) => {
        const amount = order.totals?.total || order.totalPrice || order.totalAmount || 0;
        console.log(`${index + 1}. ${order.orderNumber} - ${amount} جنيه - ${order.status}`);
      });
    } else {
      console.log('❌ لم يتم العثور على طلبات للنادلة sara');
      
      // عرض عينة من الطلبات للتشخيص
      console.log('\n🔍 عينة من الطلبات للتشخيص:');
      allOrders.slice(0, 5).forEach(order => {
        console.log(`- طلب ${order.orderNumber}:`);
        console.log(`  waiterId: ${order.waiterId}`);
        console.log(`  waiterName: ${order.waiterName}`);
        console.log(`  staff: ${JSON.stringify(order.staff)}`);
      });
    }
    
  } catch (error) {
    console.error('❌ خطأ:', error);
  }
}

testWaiterDashboard();

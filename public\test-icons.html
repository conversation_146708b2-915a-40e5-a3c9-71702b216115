<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار الأيقونات - شاشة الطاولات</title>
    
    <!-- Font Awesome CDN -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.6.0/css/all.min.css" integrity="sha512-Kc323vGBEqzTmouAECnVceyQqyqdsSiqLQISBL29aUW4U/M7pSPA/gEUZQqv1cwx4OnYxTxve5UMg5GT6L4JJg==" crossorigin="anonymous" referrerpolicy="no-referrer" />
    
    <!-- Backup CDN -->
    <link rel="stylesheet" href="https://use.fontawesome.com/releases/v6.6.0/css/all.css" crossorigin="anonymous" />
    
    <style>
        body {
            font-family: Arial, sans-serif;
            padding: 2rem;
            direction: rtl;
            background: #f8f9fa;
        }
        .test-container {
            background: white;
            padding: 2rem;
            border-radius: 10px;
            box-shadow: 0 4px 6px rgba(0,0,0,0.1);
            margin-bottom: 2rem;
        }
        .icon-test {
            display: inline-flex;
            align-items: center;
            margin: 1rem;
            padding: 0.5rem 1rem;
            background: #e3f2fd;
            border-radius: 5px;
            border: 1px solid #2196f3;
        }
        .icon-test i {
            margin-left: 0.5rem;
            font-size: 1.2rem;
            color: #1976d2;
        }
        .status {
            padding: 1rem;
            margin: 1rem 0;
            border-radius: 5px;
        }
        .status.success {
            background: #d4edda;
            border: 1px solid #28a745;
            color: #155724;
        }
        .status.error {
            background: #f8d7da;
            border: 1px solid #dc3545;
            color: #721c24;
        }
        .fa-spin {
            animation: fa-spin 2s infinite linear;
        }
        @keyframes fa-spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>اختبار الأيقونات - شاشة الطاولات</h1>
        <p>تحقق من عرض الأيقونات المستخدمة في شاشة إدارة الطاولات:</p>
        
        <div class="icon-test">
            <i class="fas fa-search"></i>
            أيقونة البحث
        </div>
        
        <div class="icon-test">
            <i class="fas fa-times"></i>
            أيقونة الإغلاق
        </div>
        
        <div class="icon-test">
            <i class="fas fa-th"></i>
            العرض الشبكي
        </div>
        
        <div class="icon-test">
            <i class="fas fa-list"></i>
            عرض القائمة
        </div>
        
        <div class="icon-test">
            <i class="fas fa-sync-alt fa-spin"></i>
            التحديث التلقائي
        </div>
        
        <div class="icon-test">
            <i class="fas fa-download"></i>
            تصدير البيانات
        </div>
        
        <div class="icon-test">
            <i class="fas fa-table"></i>
            الطاولات
        </div>
        
        <div class="icon-test">
            <i class="fas fa-door-open"></i>
            طاولة مفتوحة
        </div>
        
        <div class="icon-test">
            <i class="fas fa-door-closed"></i>
            طاولة مغلقة
        </div>
        
        <div class="icon-test">
            <i class="fas fa-money-bill-wave"></i>
            المبلغ الإجمالي
        </div>
        
        <div class="icon-test">
            <i class="fas fa-receipt"></i>
            عدد الطلبات
        </div>
        
        <div class="icon-test">
            <i class="fas fa-users"></i>
            النادل
        </div>
        
        <div class="icon-test">
            <i class="fas fa-eye"></i>
            عرض التفاصيل
        </div>
        
        <div class="icon-test">
            <i class="fas fa-sort-up"></i>
            ترتيب تصاعدي
        </div>
        
        <div class="icon-test">
            <i class="fas fa-sort-down"></i>
            ترتيب تنازلي
        </div>
        
        <div class="icon-test">
            <i class="fas fa-exclamation-triangle"></i>
            تحذير/أولوية
        </div>
    </div>
    
    <div class="test-container">
        <h2>اختبار تحميل Font Awesome</h2>
        <div id="fontawesome-status" class="status">
            <i class="fas fa-spinner fa-spin"></i>
            جاري التحقق من تحميل Font Awesome...
        </div>
    </div>
    
    <script>
        // اختبار تحميل Font Awesome
        function checkFontAwesome() {
            const statusDiv = document.getElementById('fontawesome-status');
            
            // التحقق من وجود Font Awesome في الصفحة
            const faElement = document.createElement('i');
            faElement.className = 'fas fa-heart';
            faElement.style.visibility = 'hidden';
            faElement.style.position = 'absolute';
            document.body.appendChild(faElement);
            
            // التحقق من الخط المستخدم
            const computedStyle = window.getComputedStyle(faElement);
            const fontFamily = computedStyle.fontFamily;
            
            document.body.removeChild(faElement);
            
            if (fontFamily.includes('Font Awesome')) {
                statusDiv.className = 'status success';
                statusDiv.innerHTML = '<i class="fas fa-check-circle"></i> تم تحميل Font Awesome بنجاح! ✅';
            } else {
                statusDiv.className = 'status error';
                statusDiv.innerHTML = '<i class="fas fa-times-circle"></i> لم يتم تحميل Font Awesome بشكل صحيح ❌';
            }
        }
        
        // تشغيل الاختبار بعد تحميل الصفحة
        window.addEventListener('load', function() {
            setTimeout(checkFontAwesome, 1000);
        });
        
        // اختبار إضافي للتحقق من CDN
        const testLink = document.createElement('link');
        testLink.rel = 'stylesheet';
        testLink.href = 'https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.6.0/css/all.min.css';
        testLink.onload = function() {
            console.log('✅ Font Awesome CDN محمل بنجاح');
        };
        testLink.onerror = function() {
            console.log('❌ خطأ في تحميل Font Awesome CDN');
        };
    </script>
</body>
</html>

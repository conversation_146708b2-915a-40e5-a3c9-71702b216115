/* ====================================
   WaiterDiscountsScreen Component Styles
   ==================================== */

/* استيراد المتغيرات المميزة لشاشة طلبات الخصم */
@import '../variables/discount-variables.css';

/* متغيرات CSS خاصة بشاشة طلبات الخصم */


/* Header شاشة طلبات الخصم */
.waiter-discounts-screen .screen-header {
  background: linear-gradient(135deg, var(--discount-primary-color), var(--discount-secondary-color));
  padding: var(--discount-spacing-lg);
  border-radius: var(--discount-border-radius);
  margin-bottom: var(--discount-spacing-lg);
  color: white;
  box-shadow: var(--discount-shadow);
}

.waiter-discounts-screen .action-buttons-flex {
  display: flex;
  justify-content: space-between;
  align-items: center;
  gap: var(--discount-spacing-md);
  margin-bottom: var(--discount-spacing-sm);
}

.waiter-discounts-screen .screen-title {
  font-size: 1.75rem;
  font-weight: 700;
  margin: 0;
  display: flex;
  align-items: center;
  gap: var(--discount-spacing-sm);
}

.waiter-discounts-screen .screen-subtitle {
  margin: 0;
  opacity: 0.9;
  font-size: 0.95rem;
}

.waiter-discounts-screen .btn-refresh {
  background: rgba(255, 255, 255, 0.2);
  color: white;
  border: 1px solid rgba(255, 255, 255, 0.3);
  padding: var(--discount-spacing-sm) var(--discount-spacing-md);
  border-radius: var(--discount-border-radius);
  cursor: pointer;
  transition: all 0.3s ease;
  font-size: 0.9rem;
  display: flex;
  align-items: center;
  gap: var(--discount-spacing-xs);
}

.waiter-discounts-screen .btn-refresh:hover {
  background: rgba(255, 255, 255, 0.3);
  transform: translateY(-1px);
}

/* حاوية الفلاتر */
.waiter-discounts-screen .filters-container {
  background: var(--discount-bg-primary);
  padding: var(--discount-spacing-lg);
  border-radius: var(--discount-border-radius);
  margin-bottom: var(--discount-spacing-lg);
  box-shadow: var(--discount-shadow);
  border: 1px solid var(--discount-border-color);
}

.waiter-discounts-screen .filter-group {
  display: flex;
  align-items: center;
  gap: var(--discount-spacing-md);
}

.waiter-discounts-screen .filter-label {
  color: var(--discount-text-primary);
  font-weight: 600;
  display: flex;
  align-items: center;
  gap: var(--discount-spacing-xs);
  white-space: nowrap;
}

.waiter-discounts-screen .filter-select {
  padding: var(--discount-spacing-sm) var(--discount-spacing-md);
  border: 2px solid var(--discount-border-color);
  border-radius: var(--discount-border-radius);
  background: var(--discount-bg-primary);
  color: var(--discount-text-primary);
  font-size: 0.9rem;
  cursor: pointer;
  transition: all 0.3s ease;
  min-width: 150px;
}

.waiter-discounts-screen .filter-select:focus {
  outline: none;
  border-color: var(--discount-primary-color);
  box-shadow: 0 0 0 3px rgba(232, 62, 140, 0.1);
}

/* شبكة الإحصائيات */
.waiter-discounts-screen .stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: var(--discount-spacing-lg);
  margin-bottom: var(--discount-spacing-lg);
}

.waiter-discounts-screen .stat-card {
  background: var(--discount-bg-primary);
  border: 1px solid var(--discount-border-color);
  border-radius: var(--discount-border-radius);
  padding: var(--discount-spacing-lg);
  display: flex;
  align-items: center;
  gap: var(--discount-spacing-md);
  box-shadow: var(--discount-shadow);
  transition: all 0.3s ease;
  cursor: pointer;
}

.waiter-discounts-screen .stat-card:hover {
  transform: translateY(-2px);
  box-shadow: var(--discount-shadow-hover);
}

.waiter-discounts-screen .stat-card.clickable:hover {
  border-color: var(--discount-primary-color);
}

.waiter-discounts-screen .stat-card.active {
  background: linear-gradient(135deg, var(--discount-primary-color), var(--discount-secondary-color));
  color: white;
  border-color: var(--discount-primary-color);
}

.waiter-discounts-screen .stat-icon {
  width: 50px;
  height: 50px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.25rem;
  color: white;
}

.waiter-discounts-screen .stat-card:not(.active) .stat-icon {
  background: var(--discount-primary-color);
}

.waiter-discounts-screen .stat-card.active .stat-icon {
  background: rgba(255, 255, 255, 0.2);
}

.waiter-discounts-screen .stat-icon.pending {
  background: var(--discount-warning-color);
}

.waiter-discounts-screen .stat-icon.approved {
  background: var(--discount-success-color);
}

.waiter-discounts-screen .stat-icon.rejected {
  background: var(--discount-danger-color);
}

.waiter-discounts-screen .stat-content {
  flex: 1;
}

.waiter-discounts-screen .stat-number {
  font-size: 1.75rem;
  font-weight: 700;
  margin-bottom: var(--discount-spacing-xs);
}

.waiter-discounts-screen .stat-card:not(.active) .stat-number {
  color: var(--discount-primary-color);
}

.waiter-discounts-screen .stat-label {
  font-size: 0.9rem;
  font-weight: 500;
  opacity: 0.9;
}

/* حالة التحميل */
.waiter-discounts-screen .loading-container {
  text-align: center;
  padding: var(--discount-spacing-xl);
  background: var(--discount-bg-primary);
  border-radius: var(--discount-border-radius);
  box-shadow: var(--discount-shadow);
}

.waiter-discounts-screen .loading-spinner {
  width: 40px;
  height: 40px;
  border: 4px solid var(--discount-bg-tertiary);
  border-top: 4px solid var(--discount-primary-color);
  border-radius: 50%;
  animation: waiter-discounts-spin 1s linear infinite;
  margin: 0 auto var(--discount-spacing-md);
}

@keyframes waiter-discounts-spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.waiter-discounts-screen .loading-container p {
  color: var(--discount-text-secondary);
  margin: 0;
}

/* قائمة طلبات الخصم */
.waiter-discounts-screen .discount-requests-list {
  margin-bottom: var(--discount-spacing-xl);
}

.waiter-discounts-screen .discount-requests-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
  gap: var(--discount-spacing-lg);
}

.waiter-discounts-screen .discount-request-card {
  background: var(--discount-bg-primary);
  border: 1px solid var(--discount-border-color);
  border-radius: var(--discount-border-radius);
  padding: var(--discount-spacing-lg);
  transition: all 0.3s ease;
  box-shadow: var(--discount-shadow);
  position: relative;
  overflow: hidden;
}

.waiter-discounts-screen .discount-request-card:hover {
  transform: translateY(-4px);
  box-shadow: var(--discount-shadow-hover);
  border-color: var(--discount-primary-color);
}

.waiter-discounts-screen .request-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: var(--discount-spacing-md);
  gap: var(--discount-spacing-sm);
}

.waiter-discounts-screen .request-number {
  font-weight: 600;
  color: var(--discount-text-primary);
  display: flex;
  align-items: center;
  gap: var(--discount-spacing-xs);
  font-size: 1.1rem;
}

.waiter-discounts-screen .request-status {
  padding: var(--discount-spacing-xs) var(--discount-spacing-sm);
  border-radius: 12px;
  font-size: 0.8rem;
  font-weight: 600;
  display: flex;
  align-items: center;
  gap: var(--discount-spacing-xs);
  white-space: nowrap;
}

.waiter-discounts-screen .request-status.pending {
  background: #fff3cd;
  color: #856404;
  border: 1px solid #ffeaa7;
}

.waiter-discounts-screen .request-status.approved {
  background: #d4edda;
  color: #155724;
  border: 1px solid #c3e6cb;
}

.waiter-discounts-screen .request-status.rejected {
  background: #f8d7da;
  color: #721c24;
  border: 1px solid #f5c6cb;
}

/* معلومات الطلب */
.waiter-discounts-screen .request-info {
  margin-bottom: var(--discount-spacing-lg);
}

.waiter-discounts-screen .info-row {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: var(--discount-spacing-sm);
  gap: var(--discount-spacing-sm);
}

.waiter-discounts-screen .info-row .label {
  display: flex;
  align-items: center;
  gap: var(--discount-spacing-xs);
  color: var(--discount-text-secondary);
  font-size: 0.9rem;
  font-weight: 500;
  min-width: 0;
  flex-shrink: 0;
}

.waiter-discounts-screen .info-row .value {
  color: var(--discount-text-primary);
  font-weight: 600;
  text-align: left;
  word-break: break-word;
  flex: 1;
}

.waiter-discounts-screen .info-row .value.discount-amount {
  color: var(--discount-primary-color);
  font-size: 1.1rem;
}

.waiter-discounts-screen .info-row .value.reason {
  font-style: italic;
  font-weight: 500;
  line-height: 1.4;
}

/* تذييل الطلب */
.waiter-discounts-screen .request-footer {
  border-top: 1px solid var(--discount-border-color);
  padding-top: var(--discount-spacing-md);
  background: var(--discount-bg-secondary);
  margin: var(--discount-spacing-md) calc(-1 * var(--discount-spacing-lg)) calc(-1 * var(--discount-spacing-lg));
  padding: var(--discount-spacing-md) var(--discount-spacing-lg);
  border-radius: 0 0 var(--discount-border-radius) var(--discount-border-radius);
}

.waiter-discounts-screen .final-amount {
  display: flex;
  justify-content: space-between;
  align-items: center;
  gap: var(--discount-spacing-sm);
}

.waiter-discounts-screen .final-amount .label {
  color: var(--discount-text-secondary);
  font-weight: 600;
  font-size: 0.95rem;
}

.waiter-discounts-screen .final-amount .amount {
  color: var(--discount-success-color);
  font-weight: 700;
  font-size: 1.1rem;
}

/* حالات الفراغ */
.waiter-discounts-screen .empty-state {
  text-align: center;
  padding: var(--discount-spacing-xl);
  grid-column: 1 / -1;
  background: var(--discount-bg-primary);
  border-radius: var(--discount-border-radius);
  box-shadow: var(--discount-shadow);
}

.waiter-discounts-screen .empty-icon {
  font-size: 3rem;
  color: var(--discount-text-muted);
  margin-bottom: var(--discount-spacing-md);
}

.waiter-discounts-screen .empty-state h3 {
  color: var(--discount-text-primary);
  margin-bottom: var(--discount-spacing-sm);
}

.waiter-discounts-screen .empty-state p {
  color: var(--discount-text-secondary);
  margin-bottom: var(--discount-spacing-lg);
  line-height: 1.5;
}

/* تحسين بطاقات طلبات الخصم */
.waiter-discounts-screen .discount-request-card.pending {
  border-left: 4px solid var(--discount-warning-color);
}

.waiter-discounts-screen .discount-request-card.approved {
  border-left: 4px solid var(--discount-success-color);
}

.waiter-discounts-screen .discount-request-card.rejected {
  border-left: 4px solid var(--discount-danger-color);
}

/* Responsive Design */
@media (max-width: 768px) {
  .waiter-discounts-screen .action-buttons-flex {
    flex-direction: column;
    align-items: stretch;
  }
  
  .waiter-discounts-screen .stats-grid {
    grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
    gap: var(--discount-spacing-md);
  }
  
  .waiter-discounts-screen .discount-requests-grid {
    grid-template-columns: 1fr;
    gap: var(--discount-spacing-md);
  }
  
  .waiter-discounts-screen .filter-group {
    flex-direction: column;
    align-items: stretch;
  }
  
  .waiter-discounts-screen .filter-select {
    min-width: auto;
  }
  
  .waiter-discounts-screen .request-header {
    flex-direction: column;
    align-items: stretch;
    gap: var(--discount-spacing-sm);
  }
  
  .waiter-discounts-screen .request-status {
    align-self: flex-start;
  }
  
  .waiter-discounts-screen .stat-card {
    padding: var(--discount-spacing-md);
  }
  
  .waiter-discounts-screen .stat-icon {
    width: 40px;
    height: 40px;
    font-size: 1rem;
  }
  
  .waiter-discounts-screen .stat-number {
    font-size: 1.5rem;
  }
}

@media (max-width: 480px) {
  .waiter-discounts-screen .screen-header {
    padding: var(--discount-spacing-md);
  }
  
  .waiter-discounts-screen .screen-title {
    font-size: 1.5rem;
  }
  
  .waiter-discounts-screen .discount-request-card {
    padding: var(--discount-spacing-md);
  }
  
  .waiter-discounts-screen .filters-container {
    padding: var(--discount-spacing-md);
  }
  
  .waiter-discounts-screen .info-row {
    flex-direction: column;
    align-items: stretch;
  }
  
  .waiter-discounts-screen .info-row .value {
    text-align: right;
    margin-top: var(--discount-spacing-xs);
  }
  
  .waiter-discounts-screen .final-amount {
    flex-direction: column;
    align-items: stretch;
    text-align: center;
  }
  
  .waiter-discounts-screen .stats-grid {
    grid-template-columns: 1fr;
  }
}


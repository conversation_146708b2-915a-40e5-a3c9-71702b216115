const mongoose = require('mongoose');

async function quickCheck() {
  try {
    console.log('🔗 محاولة الاتصال بقاعدة البيانات...');
    
    const mongoUri = 'mongodb+srv://besomustafa:<EMAIL>/deshacoffee?retryWrites=true&w=majority&appName=MyCoffeChop';
    
    await mongoose.connect(mongoUri, {
      serverSelectionTimeoutMS: 5000,
      connectTimeoutMS: 10000,
    });
    
    console.log('✅ تم الاتصال بنجاح');
    
    // فحص المجموعات الموجودة
    const collections = await mongoose.connection.db.listCollections().toArray();
    console.log('\n📋 المجموعات الموجودة:');
    collections.forEach(collection => {
      console.log(`   - ${collection.name}`);
    });
    
    // إحصائيات سريعة
    const ordersCount = await mongoose.connection.db.collection('orders').countDocuments();
    const usersCount = await mongoose.connection.db.collection('users').countDocuments();
    
    console.log('\n📊 إحصائيات سريعة:');
    console.log(`   الطلبات: ${ordersCount}`);
    console.log(`   المستخدمين: ${usersCount}`);
    
    if (ordersCount > 0) {
      // عينة من الطلبات
      const sampleOrders = await mongoose.connection.db.collection('orders').find({}).limit(3).toArray();
      console.log('\n📝 عينة من الطلبات:');
      sampleOrders.forEach((order, index) => {
        console.log(`   ${index + 1}. طلب ${order.orderNumber || order._id}:`);
        console.log(`      - الحالة: ${order.status}`);
        console.log(`      - النادل: ${order.waiterName || 'غير محدد'}`);
        console.log(`      - المبلغ: ${order.totalPrice || order.totalAmount || order.totals?.total || 'غير محدد'}`);
      });
    }
    
    if (usersCount > 0) {
      const waiters = await mongoose.connection.db.collection('users').find({ role: 'waiter' }).toArray();
      console.log('\n👥 النُدل في النظام:');
      waiters.forEach((waiter, index) => {
        console.log(`   ${index + 1}. ${waiter.username} (${waiter.name || 'بدون اسم'}) - ${waiter.isActive ? 'نشط' : 'غير نشط'}`);
      });
    }
    
  } catch (error) {
    console.error('❌ خطأ:', error.message);
  } finally {
    await mongoose.disconnect();
    console.log('\n🔌 تم قطع الاتصال');
  }
}

quickCheck();

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار شاشة الطلبات - لوحة المدير</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 20px;
            background: #f8f9fa;
            direction: rtl;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            padding: 2rem;
            border-radius: 12px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
        }
        .test-section {
            margin: 2rem 0;
            padding: 1rem;
            border: 1px solid #ddd;
            border-radius: 8px;
            background: #f8f9fa;
        }
        .success { color: #28a745; }
        .error { color: #dc3545; }
        .warning { color: #ffc107; }
        .info { color: #17a2b8; }
        pre {
            background: #f1f3f4;
            padding: 1rem;
            border-radius: 4px;
            overflow-x: auto;
            font-size: 0.9rem;
        }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 0.5rem 1rem;
            border-radius: 4px;
            cursor: pointer;
            margin: 0.5rem;
        }
        button:hover {
            background: #0056b3;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🧪 اختبار شاشة الطلبات - لوحة المدير</h1>
        <p class="info">هذه الصفحة لاختبار وتشخيص مشاكل شاشة الطلبات في لوحة المدير</p>

        <div class="test-section">
            <h2>📊 اختبار جلب الطلبات</h2>
            <button onclick="testOrdersFetch()">جلب الطلبات</button>
            <div id="orders-result"></div>
        </div>

        <div class="test-section">
            <h2>👥 اختبار جلب الموظفين</h2>
            <button onclick="testEmployeesFetch()">جلب الموظفين</button>
            <div id="employees-result"></div>
        </div>

        <div class="test-section">
            <h2>🔍 اختبار فلترة الطلبات حسب النادل</h2>
            <button onclick="testWaiterFilter()">اختبار الفلترة</button>
            <div id="filter-result"></div>
        </div>

        <div class="test-section">
            <h2>📈 اختبار حساب الإحصائيات</h2>
            <button onclick="testStatsCalculation()">حساب الإحصائيات</button>
            <div id="stats-result"></div>
        </div>

        <div class="test-section">
            <h2>⏰ اختبار فلترة حسب التاريخ</h2>
            <select id="date-filter">
                <option value="today">اليوم</option>
                <option value="week">هذا الأسبوع</option>
                <option value="month">هذا الشهر</option>
                <option value="all">جميع الأوقات</option>
            </select>
            <button onclick="testDateFilter()">اختبار الفلترة الزمنية</button>
            <div id="date-result"></div>
        </div>
    </div>

    <script>
        const API_BASE = 'http://localhost:5000';
        let ordersData = [];
        let employeesData = [];

        // دالة لتسجيل الدخول كمدير
        async function loginAsManager() {
            try {
                const response = await fetch(`${API_BASE}/api/v1/auth/login`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        username: 'admin',
                        password: 'admin123'
                    })
                });

                const data = await response.json();
                if (data.success) {
                    localStorage.setItem('token', data.token);
                    return data.token;
                }
                return null;
            } catch (error) {
                console.error('Login failed:', error);
                return null;
            }
        }

        // دالة مساعدة للطلبات المصادق عليها
        async function authenticatedFetch(endpoint, options = {}) {
            let token = localStorage.getItem('token');
            if (!token) {
                token = await loginAsManager();
            }

            return fetch(`${API_BASE}${endpoint}`, {
                ...options,
                headers: {
                    'Authorization': `Bearer ${token}`,
                    'Content-Type': 'application/json',
                    ...options.headers
                }
            });
        }

        async function testOrdersFetch() {
            const resultDiv = document.getElementById('orders-result');
            resultDiv.innerHTML = '<p class="info">جاري جلب الطلبات...</p>';

            try {
                const response = await authenticatedFetch('/api/v1/orders?limit=999999');
                const data = await response.json();

                let orders = [];
                if (Array.isArray(data)) {
                    orders = data;
                } else if (data.success && Array.isArray(data.data)) {
                    orders = data.data;
                } else if (data.data && Array.isArray(data.data)) {
                    orders = data.data;
                }

                ordersData = orders;

                if (orders.length > 0) {
                    resultDiv.innerHTML = `
                        <p class="success">✅ تم جلب ${orders.length} طلب بنجاح</p>
                        <pre>${JSON.stringify({
                            totalOrders: orders.length,
                            sampleOrder: {
                                id: orders[0]._id,
                                orderNumber: orders[0].orderNumber,
                                waiterName: orders[0].waiterName,
                                status: orders[0].status,
                                totalAmount: orders[0].totalAmount,
                                totalPrice: orders[0].totalPrice,
                                createdAt: orders[0].createdAt
                            },
                            waiterNames: [...new Set(orders.map(o => o.waiterName).filter(Boolean))]
                        }, null, 2)}</pre>
                    `;
                } else {
                    resultDiv.innerHTML = '<p class="warning">⚠️ لا توجد طلبات</p>';
                }
            } catch (error) {
                resultDiv.innerHTML = `<p class="error">❌ خطأ في جلب الطلبات: ${error.message}</p>`;
            }
        }

        async function testEmployeesFetch() {
            const resultDiv = document.getElementById('employees-result');
            resultDiv.innerHTML = '<p class="info">جاري جلب الموظفين...</p>';

            try {
                const response = await authenticatedFetch('/api/v1/employees');
                const data = await response.json();

                let employees = [];
                if (Array.isArray(data)) {
                    employees = data;
                } else if (data.success && Array.isArray(data.data)) {
                    employees = data.data;
                } else if (data.data && Array.isArray(data.data)) {
                    employees = data.data;
                }

                employeesData = employees;
                const waiters = employees.filter(emp => emp.role === 'waiter');

                resultDiv.innerHTML = `
                    <p class="success">✅ تم جلب ${employees.length} موظف بنجاح</p>
                    <p class="info">عدد النُدل: ${waiters.length}</p>
                    <pre>${JSON.stringify({
                        totalEmployees: employees.length,
                        waiters: waiters.map(w => ({
                            id: w._id,
                            name: w.name,
                            username: w.username,
                            role: w.role
                        }))
                    }, null, 2)}</pre>
                `;
            } catch (error) {
                resultDiv.innerHTML = `<p class="error">❌ خطأ في جلب الموظفين: ${error.message}</p>`;
            }
        }

        function testWaiterFilter() {
            const resultDiv = document.getElementById('filter-result');

            if (ordersData.length === 0 || employeesData.length === 0) {
                resultDiv.innerHTML = '<p class="warning">⚠️ يجب جلب الطلبات والموظفين أولاً</p>';
                return;
            }

            const waiters = employeesData.filter(emp => emp.role === 'waiter');
            if (waiters.length === 0) {
                resultDiv.innerHTML = '<p class="warning">⚠️ لا يوجد نُدل في النظام</p>';
                return;
            }

            const testWaiter = waiters[0];
            const waiterOrders = ordersData.filter(order => {
                return order.waiterName === testWaiter.name ||
                       order.waiterName?.toLowerCase() === testWaiter.username?.toLowerCase() ||
                       order.waiterName?.toLowerCase() === testWaiter.name?.toLowerCase();
            });

            resultDiv.innerHTML = `
                <p class="info">اختبار فلترة النادل: ${testWaiter.name || testWaiter.username}</p>
                <p class="success">✅ عدد الطلبات للنادل: ${waiterOrders.length}</p>
                <pre>${JSON.stringify({
                    waiterInfo: {
                        name: testWaiter.name,
                        username: testWaiter.username
                    },
                    orderMatches: waiterOrders.map(o => ({
                        id: o._id,
                        orderNumber: o.orderNumber,
                        waiterName: o.waiterName,
                        matchedWith: testWaiter.name === o.waiterName ? 'name' : 'username'
                    }))
                }, null, 2)}</pre>
            `;
        }

        function testStatsCalculation() {
            const resultDiv = document.getElementById('stats-result');

            if (ordersData.length === 0 || employeesData.length === 0) {
                resultDiv.innerHTML = '<p class="warning">⚠️ يجب جلب الطلبات والموظفين أولاً</p>';
                return;
            }

            const waiters = employeesData.filter(emp => emp.role === 'waiter');
            const waiterStats = waiters.map(waiter => {
                const waiterOrders = ordersData.filter(order => {
                    return order.waiterName === waiter.name ||
                           order.waiterName?.toLowerCase() === waiter.username?.toLowerCase() ||
                           order.waiterName?.toLowerCase() === waiter.name?.toLowerCase();
                });

                const totalSales = waiterOrders.reduce((sum, order) => {
                    let orderTotal = 0;
                    if (order.totalAmount && typeof order.totalAmount === 'number') {
                        orderTotal = order.totalAmount;
                    } else if (order.totalPrice && typeof order.totalPrice === 'number') {
                        orderTotal = order.totalPrice;
                    } else if (order.totals && order.totals.total && typeof order.totals.total === 'number') {
                        orderTotal = order.totals.total;
                    }
                    return sum + orderTotal;
                }, 0);

                return {
                    name: waiter.name || waiter.username,
                    ordersCount: waiterOrders.length,
                    sales: totalSales,
                    pendingOrders: waiterOrders.filter(o => o.status === 'pending').length,
                    completedOrders: waiterOrders.filter(o => o.status === 'completed' || o.status === 'delivered').length
                };
            });

            resultDiv.innerHTML = `
                <p class="success">✅ تم حساب الإحصائيات لـ ${waiterStats.length} نادل</p>
                <pre>${JSON.stringify(waiterStats, null, 2)}</pre>
            `;
        }

        function testDateFilter() {
            const resultDiv = document.getElementById('date-result');
            const dateFilter = document.getElementById('date-filter').value;

            if (ordersData.length === 0) {
                resultDiv.innerHTML = '<p class="warning">⚠️ يجب جلب الطلبات أولاً</p>';
                return;
            }

            const filteredOrders = ordersData.filter(order => {
                if (dateFilter === 'all') return true;

                const orderDate = new Date(order.createdAt);
                const today = new Date();

                if (dateFilter === 'today') {
                    return today.toDateString() === orderDate.toDateString();
                } else if (dateFilter === 'week') {
                    const weekStart = new Date(today);
                    weekStart.setDate(today.getDate() - today.getDay());
                    weekStart.setHours(0, 0, 0, 0);
                    return orderDate >= weekStart;
                } else if (dateFilter === 'month') {
                    const monthStart = new Date(today.getFullYear(), today.getMonth(), 1);
                    monthStart.setHours(0, 0, 0, 0);
                    return orderDate >= monthStart;
                }
                return true;
            });

            resultDiv.innerHTML = `
                <p class="success">✅ فلترة ${dateFilter}: ${filteredOrders.length} من أصل ${ordersData.length} طلب</p>
                <pre>${JSON.stringify({
                    filter: dateFilter,
                    totalOrders: ordersData.length,
                    filteredOrders: filteredOrders.length,
                    percentage: ((filteredOrders.length / ordersData.length) * 100).toFixed(1) + '%'
                }, null, 2)}</pre>
            `;
        }

        // تشغيل اختبارات أولية عند تحميل الصفحة
        window.onload = async function() {
            await testOrdersFetch();
            await testEmployeesFetch();
        };
    </script>
</body>
</html>

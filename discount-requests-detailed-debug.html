<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>تشخيص طلبات الخصم - تفصيلي</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
            direction: rtl;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .section {
            margin-bottom: 30px;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 8px;
            background: #f9f9f9;
        }
        .section h3 {
            margin-top: 0;
            color: #333;
            border-bottom: 2px solid #4CAF50;
            padding-bottom: 10px;
        }
        button {
            background: #4CAF50;
            color: white;
            border: none;
            padding: 10px 20px;
            margin: 5px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
        }
        button:hover {
            background: #45a049;
        }
        button.danger {
            background: #f44336;
        }
        button.danger:hover {
            background: #da190b;
        }
        .result {
            background: #f0f8ff;
            border: 1px solid #4CAF50;
            padding: 15px;
            margin-top: 10px;
            border-radius: 4px;
            white-space: pre-wrap;
            font-family: monospace;
            direction: ltr;
            text-align: left;
        }
        .error {
            background: #ffebee;
            border: 1px solid #f44336;
            color: #c62828;
        }
        .warning {
            background: #fff3e0;
            border: 1px solid #ff9800;
            color: #ef6c00;
        }
        .grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
        }
        .discount-card {
            border: 1px solid #ddd;
            border-radius: 8px;
            padding: 15px;
            background: white;
            margin-bottom: 15px;
        }
        .discount-card h4 {
            margin-top: 0;
            color: #2196F3;
            border-bottom: 1px solid #eee;
            padding-bottom: 5px;
        }
        .field {
            margin: 8px 0;
            padding: 5px;
            background: #f8f8f8;
            border-radius: 4px;
        }
        .field strong {
            color: #333;
        }
        .missing {
            color: #f44336;
            font-weight: bold;
        }
        .present {
            color: #4CAF50;
            font-weight: bold;
        }
        .loading {
            text-align: center;
            padding: 20px;
            color: #666;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔍 تشخيص طلبات الخصم - تفصيلي</h1>
        
        <div class="section">
            <h3>🎯 اختبار APIs</h3>
            <button onclick="testDiscountRequestsAPI()">📋 جلب طلبات الخصم</button>
            <button onclick="testOrdersAPI()">🧾 جلب الطلبات</button>
            <button onclick="testRelationship()">🔗 فحص العلاقات</button>
            <button onclick="createTestDiscountRequest()">🧪 إنشاء طلب خصم تجريبي</button>
            <div id="api-results"></div>
        </div>

        <div class="section">
            <h3>📊 بيانات طلبات الخصم</h3>
            <div id="discount-requests-container">
                <div class="loading">⏳ انقر على "جلب طلبات الخصم" لعرض البيانات...</div>
            </div>
        </div>

        <div class="section">
            <h3>🧾 بيانات الطلبات المرتبطة</h3>
            <div id="orders-container">
                <div class="loading">⏳ انقر على "جلب الطلبات" لعرض البيانات...</div>
            </div>
        </div>

        <div class="section">
            <h3>🔗 تحليل العلاقات</h3>
            <div id="relationship-analysis">
                <div class="loading">⏳ انقر على "فحص العلاقات" لتحليل الربط بين طلبات الخصم والطلبات...</div>
            </div>
        </div>
    </div>

    <script>
        const API_BASE = 'http://localhost:3001/api/v1';
        
        let authToken = localStorage.getItem('authToken');
        if (!authToken) {
            authToken = prompt('يرجى إدخال Auth Token:');
            if (authToken) {
                localStorage.setItem('authToken', authToken);
            }
        }

        const headers = {
            'Content-Type': 'application/json',
            'Authorization': `Bearer ${authToken}`
        };

        let discountRequestsData = [];
        let ordersData = [];

        async function testDiscountRequestsAPI() {
            const resultDiv = document.getElementById('api-results');
            const containerDiv = document.getElementById('discount-requests-container');
            
            try {
                resultDiv.innerHTML = '<div class="loading">⏳ جاري جلب طلبات الخصم...</div>';
                
                const response = await fetch(`${API_BASE}/discount-requests`, { headers });
                const data = await response.json();
                
                discountRequestsData = data.data || [];
                
                resultDiv.innerHTML = `<div class="result">
✅ نتيجة API طلبات الخصم:
الحالة: ${response.status}
النجاح: ${data.success}
العدد: ${discountRequestsData.length}
الرسالة: ${data.message}

عينة من البيانات:
${JSON.stringify(discountRequestsData.slice(0, 2), null, 2)}
                </div>`;

                // عرض تفصيلي لكل طلب خصم
                displayDiscountRequestsDetails(discountRequestsData);
                
            } catch (error) {
                resultDiv.innerHTML = `<div class="result error">❌ خطأ في جلب طلبات الخصم: ${error.message}</div>`;
                console.error('Error:', error);
            }
        }

        async function testOrdersAPI() {
            const containerDiv = document.getElementById('orders-container');
            
            try {
                containerDiv.innerHTML = '<div class="loading">⏳ جاري جلب الطلبات...</div>';
                
                const response = await fetch(`${API_BASE}/orders`, { headers });
                const data = await response.json();
                
                ordersData = data.data || [];
                
                // عرض تفصيلي للطلبات
                displayOrdersDetails(ordersData);
                
            } catch (error) {
                containerDiv.innerHTML = `<div class="result error">❌ خطأ في جلب الطلبات: ${error.message}</div>`;
                console.error('Error:', error);
            }
        }

        function displayDiscountRequestsDetails(requests) {
            const container = document.getElementById('discount-requests-container');
            
            if (!requests || requests.length === 0) {
                container.innerHTML = '<div class="warning">⚠️ لا توجد طلبات خصم</div>';
                return;
            }

            let html = `<h4>📋 طلبات الخصم (${requests.length})</h4>`;
            
            requests.forEach((request, index) => {
                html += `
                <div class="discount-card">
                    <h4>طلب خصم #${index + 1} - ${request.orderNumber || request._id}</h4>
                    
                    <div class="field">
                        <strong>ID:</strong> ${request._id || 'غير محدد'}
                    </div>
                    
                    <div class="field">
                        <strong>Order ID:</strong> 
                        <span class="${request.orderId ? 'present' : 'missing'}">
                            ${request.orderId || 'غير محدد'}
                        </span>
                    </div>
                    
                    <div class="field">
                        <strong>رقم الطلب:</strong> 
                        <span class="${request.orderNumber ? 'present' : 'missing'}">
                            ${request.orderNumber || 'غير محدد'}
                        </span>
                    </div>
                    
                    <div class="field">
                        <strong>رقم الطاولة:</strong> 
                        <span class="${request.tableNumber ? 'present' : 'missing'}">
                            ${request.tableNumber || 'غير محدد'}
                        </span>
                    </div>
                    
                    <div class="field">
                        <strong>اسم العميل:</strong> 
                        <span class="${request.customerName ? 'present' : 'missing'}">
                            ${request.customerName || 'غير محدد'}
                        </span>
                    </div>
                    
                    <div class="field">
                        <strong>النادل:</strong> 
                        <span class="${request.waiterName ? 'present' : 'missing'}">
                            ${request.waiterName || request.requestedBy || 'غير محدد'}
                        </span>
                    </div>
                    
                    <div class="field">
                        <strong>المبلغ الأصلي:</strong> 
                        <span class="${request.originalAmount !== undefined ? 'present' : 'missing'}">
                            ${request.originalAmount !== undefined ? request.originalAmount + ' ج.م' : 'غير محدد'}
                        </span>
                    </div>
                    
                    <div class="field">
                        <strong>مبلغ الخصم المطلوب:</strong> 
                        <span class="${request.requestedDiscount !== undefined ? 'present' : 'missing'}">
                            ${request.requestedDiscount !== undefined ? request.requestedDiscount + ' ج.م' : 'غير محدد'}
                        </span>
                    </div>
                    
                    <div class="field">
                        <strong>المبلغ (amount):</strong> 
                        <span class="${request.amount !== undefined ? 'present' : 'missing'}">
                            ${request.amount !== undefined ? request.amount + ' ج.م' : 'غير محدد'}
                        </span>
                    </div>
                    
                    <div class="field">
                        <strong>السبب:</strong> 
                        <span class="${request.reason ? 'present' : 'missing'}">
                            ${request.reason || 'غير محدد'}
                        </span>
                    </div>
                    
                    <div class="field">
                        <strong>الحالة:</strong> 
                        <span class="${request.status ? 'present' : 'missing'}">
                            ${request.status || 'غير محدد'}
                        </span>
                    </div>
                    
                    <div class="field">
                        <strong>تاريخ الإنشاء:</strong> ${new Date(request.createdAt).toLocaleString('ar-SA')}
                    </div>
                    
                    <div class="field">
                        <strong>بيانات الطلب المرتبط:</strong> 
                        <span class="${request.order ? 'present' : 'missing'}">
                            ${request.order ? 'موجود' : 'غير موجود'}
                        </span>
                    </div>
                    
                    ${request.order ? `
                    <div style="margin-right: 20px; padding: 10px; background: #e8f5e8; border-radius: 4px;">
                        <strong>تفاصيل الطلب المرتبط:</strong><br>
                        رقم الطاولة: ${request.order.tableNumber || 'غير محدد'}<br>
                        المبلغ الإجمالي: ${request.order.totalAmount || request.order.totals?.total || 'غير محدد'}<br>
                        الحالة: ${request.order.status || 'غير محدد'}
                    </div>
                    ` : ''}
                </div>`;
            });
            
            container.innerHTML = html;
        }

        function displayOrdersDetails(orders) {
            const container = document.getElementById('orders-container');
            
            if (!orders || orders.length === 0) {
                container.innerHTML = '<div class="warning">⚠️ لا توجد طلبات</div>';
                return;
            }

            // عرض أول 5 طلبات فقط للاختبار
            const sampleOrders = orders.slice(0, 5);
            let html = `<h4>🧾 عينة من الطلبات (${sampleOrders.length} من ${orders.length})</h4>`;
            
            sampleOrders.forEach((order, index) => {
                html += `
                <div class="discount-card">
                    <h4>طلب #${index + 1} - ${order.orderNumber || order._id}</h4>
                    
                    <div class="field">
                        <strong>ID:</strong> ${order._id}
                    </div>
                    
                    <div class="field">
                        <strong>رقم الطلب:</strong> ${order.orderNumber || 'غير محدد'}
                    </div>
                    
                    <div class="field">
                        <strong>رقم الطاولة:</strong> 
                        <span class="${order.tableNumber ? 'present' : 'missing'}">
                            ${order.tableNumber || 'غير محدد'}
                        </span>
                    </div>
                    
                    <div class="field">
                        <strong>المبلغ الإجمالي:</strong> 
                        <span class="${order.totalAmount !== undefined ? 'present' : 'missing'}">
                            ${order.totalAmount !== undefined ? order.totalAmount + ' ج.م' : 'غير محدد'}
                        </span>
                    </div>
                    
                    <div class="field">
                        <strong>المجاميع (totals):</strong> 
                        ${order.totals ? JSON.stringify(order.totals, null, 2) : 'غير محدد'}
                    </div>
                    
                    <div class="field">
                        <strong>الحالة:</strong> ${order.status || 'غير محدد'}
                    </div>
                    
                    <div class="field">
                        <strong>النادل:</strong> ${order.waiterName || order.staff?.waiter || 'غير محدد'}
                    </div>
                </div>`;
            });
            
            container.innerHTML = html;
        }

        async function testRelationship() {
            const container = document.getElementById('relationship-analysis');
            
            if (discountRequestsData.length === 0) {
                container.innerHTML = '<div class="warning">⚠️ يجب جلب طلبات الخصم أولاً</div>';
                return;
            }
            
            if (ordersData.length === 0) {
                container.innerHTML = '<div class="warning">⚠️ يجب جلب الطلبات أولاً</div>';
                return;
            }

            let analysis = `<h4>🔗 تحليل العلاقات</h4>`;
            
            analysis += `<div class="field">
                <strong>عدد طلبات الخصم:</strong> ${discountRequestsData.length}
            </div>`;
            
            analysis += `<div class="field">
                <strong>عدد الطلبات:</strong> ${ordersData.length}
            </div>`;

            let matchedCount = 0;
            let unmatchedDiscountRequests = [];

            discountRequestsData.forEach(discountRequest => {
                const matchingOrder = ordersData.find(order => 
                    order._id === discountRequest.orderId || 
                    order.orderNumber === discountRequest.orderNumber
                );
                
                if (matchingOrder) {
                    matchedCount++;
                } else {
                    unmatchedDiscountRequests.push(discountRequest);
                }
            });

            analysis += `<div class="field">
                <strong>طلبات الخصم المرتبطة بطلبات موجودة:</strong> 
                <span class="${matchedCount > 0 ? 'present' : 'missing'}">
                    ${matchedCount} من ${discountRequestsData.length}
                </span>
            </div>`;

            if (unmatchedDiscountRequests.length > 0) {
                analysis += `<div class="field">
                    <strong>طلبات الخصم غير المرتبطة:</strong>
                    <span class="missing">${unmatchedDiscountRequests.length}</span>
                </div>`;
                
                analysis += `<div style="margin-right: 20px;">`;
                unmatchedDiscountRequests.forEach(req => {
                    analysis += `<div class="field" style="background: #ffebee;">
                        طلب خصم: ${req.orderNumber || req._id}<br>
                        Order ID المطلوب: ${req.orderId}<br>
                        السبب المحتمل: الطلب محذوف أو Order ID خاطئ
                    </div>`;
                });
                analysis += `</div>`;
            }

            container.innerHTML = analysis;
        }

        async function createTestDiscountRequest() {
            const resultDiv = document.getElementById('api-results');
            
            try {
                resultDiv.innerHTML = '<div class="loading">⏳ جاري إنشاء طلب خصم تجريبي...</div>';
                
                const testData = {
                    orderId: 'TEST-ORDER-' + Date.now(),
                    orderNumber: 'TEST-' + Date.now(),
                    customerName: 'عميل تجريبي للاختبار',
                    originalAmount: 150.00,
                    requestedDiscount: 15.00,
                    reason: 'طلب خصم تجريبي لفحص النظام',
                    waiterName: 'نادل تجريبي',
                    tableNumber: '999'
                };
                
                const response = await fetch(`${API_BASE}/discount-requests`, {
                    method: 'POST',
                    headers,
                    body: JSON.stringify(testData)
                });
                
                const data = await response.json();
                
                resultDiv.innerHTML = `<div class="result">
✅ نتيجة إنشاء طلب خصم تجريبي:
الحالة: ${response.status}
النجاح: ${data.success}
الرسالة: ${data.message}

البيانات المنشأة:
${JSON.stringify(data.data, null, 2)}
                </div>`;

                // إعادة جلب طلبات الخصم لإظهار الطلب الجديد
                setTimeout(() => {
                    testDiscountRequestsAPI();
                }, 1000);
                
            } catch (error) {
                resultDiv.innerHTML = `<div class="result error">❌ خطأ في إنشاء طلب خصم تجريبي: ${error.message}</div>`;
                console.error('Error:', error);
            }
        }

        // تشغيل تلقائي عند تحميل الصفحة
        window.addEventListener('load', () => {
            if (authToken) {
                console.log('🔧 تم تحميل صفحة التشخيص التفصيلي');
            } else {
                alert('⚠️ يجب إدخال Auth Token صحيح للمتابعة');
            }
        });
    </script>
</body>
</html>

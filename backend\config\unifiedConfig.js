// Unified Application Configuration
// إعدادات التطبيق الموحدة

const path = require('path');

const config = {  // Server Configuration
  server: {
    port: process.env.PORT || 4010,
    host: process.env.HOST || '0.0.0.0',
    environment: process.env.NODE_ENV || 'production',
    timezone: process.env.TZ || 'Asia/Riyadh',
    
    // Railway specific settings
    railway: {
      projectId: process.env.RAILWAY_PROJECT_ID,
      serviceId: process.env.RAILWAY_SERVICE_ID,
      environmentId: process.env.RAILWAY_ENVIRONMENT_ID,
      publicDomain: process.env.RAILWAY_PUBLIC_DOMAIN || process.env.BACKEND_URL
    }
  },

  // Database Configuration
  database: {
    uri: process.env.MONGODB_URI || 'mongodb+srv://besomustafa:<EMAIL>/deshacoffee?retryWrites=true&w=majority&appName=MyCoffeChop',
    options: {
      maxPoolSize: 10,
      serverSelectionTimeoutMS: 5000,
      heartbeatFrequencyMS: 10000,
      retryWrites: true,
      writeConcern: {
        w: 'majority'
      }
    }
  },
  // Frontend Configuration
  frontend: {
    allowedOrigins: [
      // Production URLs
      'https://desha-coffee.vercel.app',
      'https://coffee-eta-murex.vercel.app',
      
      // Development URLs
      'http://localhost:3000',
      'http://localhost:5173',
      'http://localhost:5190',
      
      // Mobile app support
      'capacitor://localhost',
      'ionic://localhost',
      'http://localhost',
      'https://localhost',
      
      // Dynamic Railway and Vercel domains
      /\.up\.railway\.app$/,
      /\.vercel\.app$/,
      /localhost:\d+$/,
      
      // Environment specific
      process.env.FRONTEND_URL,
      process.env.CORS_ORIGIN
    ].filter(Boolean)
  },

  // Mobile Configuration
  mobile: {
    // Headers specific to mobile devices
    headers: {
      cacheControl: 'no-cache, no-store, must-revalidate, private',
      pragma: 'no-cache',
      expires: '0',
      contentTypeOptions: 'nosniff',
      frameOptions: 'SAMEORIGIN'
    },
    
    // Timeout settings for mobile requests
    timeouts: {
      request: 30000, // 30 seconds
      connection: 10000, // 10 seconds
      response: 25000 // 25 seconds
    },
    
    // Rate limiting for mobile devices
    rateLimiting: {
      windowMs: 15 * 60 * 1000, // 15 minutes
      maxRequests: 200, // More lenient for mobile
      skipSuccessfulRequests: true
    }
  },

  // API Configuration
  api: {
    version: 'v1',
    basePath: '/api',
    
    // Response format configuration
    response: {
      format: {
        success: {
          success: true,
          message: 'string',
          data: 'any',
          timestamp: 'ISO string',
          meta: 'object (optional)'
        },
        error: {
          success: false,
          message: 'string',
          error: 'string (error code)',
          timestamp: 'ISO string',
          details: 'object (development only)'
        }
      }
    },

    // Rate limiting configuration
    rateLimiting: {
      general: {
        windowMs: 15 * 60 * 1000, // 15 minutes
        max: 100 // requests per window
      },
      auth: {
        windowMs: 15 * 60 * 1000, // 15 minutes
        max: 5 // login attempts per window
      },
      strict: {
        windowMs: 15 * 60 * 1000, // 15 minutes
        max: 50 // sensitive operations per window
      }
    }
  },

  // Security Configuration
  security: {
    helmet: {
      crossOriginEmbedderPolicy: false,
      contentSecurityPolicy: {
        directives: {
          defaultSrc: ["'self'"],
          styleSrc: ["'self'", "'unsafe-inline'"],
          scriptSrc: ["'self'"],
          imgSrc: ["'self'", "data:", "https:"],
          connectSrc: ["'self'", "ws:", "wss:"]
        }
      }
    },
      cors: {
      credentials: true,
      methods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS', 'PATCH'],
      allowedHeaders: [
        'Origin',
        'X-Requested-With',
        'Content-Type',
        'Accept',
        'Authorization',
        'x-device-type',
        'x-request-id',
        'x-app-version',
        'Cache-Control',
        'Pragma',
        'Expires',
        'X-Forwarded-For',
        'X-Forwarded-Proto',
        'X-Vercel-Id',
        'X-Railway-Request-Id'
      ],
      exposedHeaders: [
        'x-total-count',
        'x-page-count',
        'x-current-page',
        'x-api-version',
        'x-request-id',
        'x-response-time',
        'X-Railway-Request-Id'
      ],
      maxAge: 86400, // 24 hours
      
      // Railway and Vercel specific settings
      trustProxy: true,
      origin: function(origin, callback) {
        // Allow requests with no origin (mobile apps, Postman, etc.)
        if (!origin) return callback(null, true);
        
        const allowedOrigins = config.frontend.allowedOrigins;
        const isAllowed = allowedOrigins.some(allowedOrigin => {
          if (typeof allowedOrigin === 'string') {
            return origin === allowedOrigin;
          } else if (allowedOrigin instanceof RegExp) {
            return allowedOrigin.test(origin);
          }
          return false;
        });
        
        callback(null, isAllowed);
      }
    }
  },

  // Logging Configuration
  logging: {
    level: process.env.LOG_LEVEL || 'info',
    format: process.env.LOG_FORMAT || 'combined',
    
    // What to log
    requests: {
      enabled: true,
      includeMobile: true,
      includeDesktop: process.env.NODE_ENV === 'development',
      sensitive: false // Don't log sensitive data
    },
    
    errors: {
      enabled: true,
      includeStack: process.env.NODE_ENV === 'development',
      notifyOnCritical: process.env.NODE_ENV === 'production'
    }
  },

  // Monitoring Configuration
  monitoring: {
    enabled: true,
    healthCheck: {
      path: '/health',
      interval: 30000 // 30 seconds
    },
    
    metrics: {
      enabled: true,
      collectInterval: 60000 // 1 minute
    }
  },

  // Socket.IO Configuration
  socketio: {
    cors: {
      origin: [
        'http://localhost:3000',
        'http://localhost:5173',
        'https://coffee-eta-murex.vercel.app',
        process.env.FRONTEND_URL
      ].filter(Boolean),
      credentials: true
    },
    
    // Events configuration
    events: {
      newOrder: 'new-order',
      orderStatusUpdated: 'order-status-updated',
      orderUpdated: 'order-updated',
      stockAlert: 'stock-alert',
      systemNotification: 'system-notification'
    }
  },

  // File Upload Configuration
  upload: {
    maxSize: 10 * 1024 * 1024, // 10MB
    allowedTypes: ['image/jpeg', 'image/png', 'image/gif', 'image/webp'],
    destination: path.join(process.cwd(), 'uploads'),
    
    // Mobile-specific settings
    mobile: {
      maxSize: 5 * 1024 * 1024, // 5MB for mobile
      quality: 0.8, // Compress images for mobile
      maxWidth: 1200,
      maxHeight: 1200
    }
  },
  // Application Features
  features: {
    // Feature flags
    unifiedAPI: true,
    mobileOptimization: true,
    realTimeUpdates: true,
    stockMonitoring: true,
    analyticsTracking: process.env.NODE_ENV === 'production',
    
    // Production specific features
    railwayDeployment: !!process.env.RAILWAY_PROJECT_ID,
    vercelFrontend: !!process.env.VERCEL,
    mongoAtlas: process.env.MONGODB_URI?.includes('mongodb.net'),
    
    // Module-specific features
    orders: {
      autoAssignChef: true,
      realTimeStatus: true,
      estimatedPrepTime: true
    },
    
    inventory: {
      lowStockAlerts: true,
      autoReorder: false, // Future feature
      expirationTracking: true
    },
    
    reports: {
      realTimeGeneration: true,
      exportFormats: ['json', 'csv'],
      scheduledReports: false // Future feature
    }
  },

  // Business Logic Configuration
  business: {
    // Order configuration
    orders: {
      defaultPrepTime: 5, // minutes per item
      maxItemsPerOrder: 999999, // إزالة قيد الـ 50 عنصر لكل طلب
      maxOrdersPerTable: 999999, // إزالة قيد الـ 5 طلبات لكل طاولة
      autoConfirmTimeout: 300000, // 5 minutes
      
      // Status transitions
      statusFlow: [
        'pending',
        'preparing', 
        'ready',
        'served'
      ],
      
      // Cancellation rules
      cancellation: {
        allowedStatuses: ['pending', 'preparing'],
        requireReason: true,
        refundPolicy: 'full' // full, partial, none
      }
    },

    // Pricing configuration
    pricing: {
      currency: 'EGP',
      taxRate: 0.15, // 15% VAT
      serviceFeeRate: 0.10, // 10% service fee
      roundingMethod: 'round', // round, ceil, floor
      
      // Discount rules
      discounts: {
        maxPercentage: 50,
        maxAmount: 1000,
        requireManagerApproval: true
      }
    },

    // Table management
    tables: {
      autoReleaseTimeout: 3600000, // 1 hour
      maxReservationTime: 7200000, // 2 hours
      occupancyStates: ['available', 'occupied', 'reserved', 'maintenance']
    }
  },

  // Error Messages (Arabic)
  messages: {
    errors: {
      validation: 'خطأ في التحقق من البيانات',
      notFound: 'العنصر غير موجود',
      unauthorized: 'غير مصرح لك بالوصول',
      forbidden: 'ممنوع الوصول',
      serverError: 'خطأ داخلي في الخادم',
      timeout: 'انتهت مهلة الطلب',
      rateLimit: 'تم تجاوز الحد الأقصى للطلبات'
    },
    
    success: {
      created: 'تم الإنشاء بنجاح',
      updated: 'تم التحديث بنجاح',
      deleted: 'تم الحذف بنجاح',
      retrieved: 'تم التحميل بنجاح'
    }
  }
};

// Environment-specific overrides
if (config.server.environment === 'production') {
  // Production overrides
  config.logging.requests.includeDesktop = false;
  config.security.helmet.contentSecurityPolicy.directives.defaultSrc = ["'self'"];
  config.api.rateLimiting.general.max = 999999; // إزالة حدود الطلبات في الإنتاج
} else if (config.server.environment === 'development') {
  // Development overrides
  config.api.rateLimiting.general.max = 999999; // إزالة حدود الطلبات في التطوير
  config.mobile.rateLimiting.maxRequests = 999999; // إزالة حدود الطلبات للموبايل
  config.logging.level = 'debug';
}

module.exports = config;

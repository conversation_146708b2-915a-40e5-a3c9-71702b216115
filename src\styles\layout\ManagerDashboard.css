/* ManagerDashboard.css - Scoped styles for Manager Dashboard Layout */

/* CSS Custom Properties (Variables) */
/* ?? ????? ????????? ??????? ?????? ??? ????? */

/* Manager Dashboard Main Container */
.manager-dashboard {
  font-family: 'Cairo', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
  background-color: #f8f9fa;
  min-height: 100vh;
  display: flex;
  flex-direction: column;
}

/* Header Bar */
.app-header {
  background: white;
  border-bottom: 1px solid #dee2e6;
  padding: 1rem;
  position: sticky;
  top: 0;
  z-index: 1000;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.125);
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.header-left {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.sidebar-toggle {
  background: transparent;
  border: 1px solid #dee2e6;
  border-radius: 12px;
  padding: 0.5rem;
  cursor: pointer;
  color: #2c3e50;
  transition: all 0.3s cubic-bezier(0.25, 0.8, 0.25, 1);
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  user-select: none;
  -webkit-tap-highlight-color: transparent;
  touch-action: manipulation;
}

.sidebar-toggle:hover {
  background: #2c3e50;
  color: white;
  border-color: #2c3e50;
}

.app-title {
  color: #2c3e50;
  font-size: 1.5rem;
  font-weight: 600;
  margin: 0;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.app-title i {
  color: #2c3e50;
}

.header-right {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.logout-button {
  background: #e74c3c;
  color: white;
  border: none;
  border-radius: 12px;
  padding: 0.5rem 1rem;
  cursor: pointer;
  font-weight: 500;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.logout-button:hover {
  background: #c82333;
  transform: translateY(-1px);
}

/* Dashboard Content Container */
.dashboard-content {
  display: flex;
  flex: 1;
  position: relative;
}

/* Sidebar Overlay for Mobile */
.sidebar-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.5);
  z-index: 1050;
  opacity: 0;
  visibility: hidden;
  transition: all 0.3s cubic-bezier(0.25, 0.8, 0.25, 1);
  -webkit-backdrop-filter: blur(2px);
  backdrop-filter: blur(2px);
  touch-action: none;
}

.sidebar-overlay.active {
  opacity: 1;
  visibility: visible;
}

/* Manager Sidebar */
.manager-sidebar {
  width: 300px;
  background: linear-gradient(180deg, #2c3e50 0%, #34495e 100%);
  box-shadow: 4px 0 20px rgba(44, 62, 80, 0.15);
  transition: all 0.4s cubic-bezier(0.25, 0.8, 0.25, 1);
  -webkit-transition: all 0.4s cubic-bezier(0.25, 0.8, 0.25, 1);
  -moz-transition: all 0.4s cubic-bezier(0.25, 0.8, 0.25, 1);
  -ms-transition: all 0.4s cubic-bezier(0.25, 0.8, 0.25, 1);
  -o-transition: all 0.4s cubic-bezier(0.25, 0.8, 0.25, 1);
  position: relative;
  z-index: 1051;
  height: 100vh; /* استخدام كامل ارتفاع الشاشة */
  overflow: hidden;
  display: flex;
  flex-direction: column;
  will-change: transform, width;
  transform: translateZ(0); /* تفعيل تسريع الأجهزة */
  -webkit-transform: translateZ(0);
  -moz-transform: translateZ(0);
  -ms-transform: translateZ(0);
  -o-transform: translateZ(0);
}

.manager-sidebar::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="20" cy="20" r="1" fill="white" opacity="0.03"/><circle cx="80" cy="40" r="1" fill="white" opacity="0.02"/><circle cx="40" cy="80" r="1" fill="white" opacity="0.03"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
  pointer-events: none;
}

.manager-sidebar.closed {
  width: 70px;
}

.sidebar-content {
  padding: 1.5rem 1rem;
  flex: 1; /* استخدام كامل المساحة المتاحة */
  display: flex;
  flex-direction: column;
  position: relative;
  z-index: 1;
  overflow: hidden; /* إزالة scroll من المحتوى الرئيسي */
  min-height: 0; /* للسماح بـ flex shrinking */
}

.sidebar-content::-webkit-scrollbar {
  width: 6px;
}

.sidebar-content::-webkit-scrollbar-track {
  background: transparent;
}

.sidebar-content::-webkit-scrollbar-thumb {
  background: rgba(255, 255, 255, 0.3);
  border-radius: 3px;
}

.sidebar-content::-webkit-scrollbar-thumb:hover {
  background: rgba(255, 255, 255, 0.5);
}

.sidebar-header {
  border-bottom: 1px solid rgba(255, 255, 255, 0.15);
  padding-bottom: 1.5rem;
  margin-bottom: 2rem;
  display: flex;
  justify-content: space-between;
  align-items: center;
  background: rgba(255, 255, 255, 0.05);
  margin: 0 -1rem 2rem -1rem;
  padding: 1.5rem 1rem;
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px); /* دعم Safari */
}

.manager-profile {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.manager-avatar {
  width: 50px;
  height: 50px;
  background: linear-gradient(135deg, #3498db, #27ae60);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 1.4rem;
  box-shadow: 0 4px 15px rgba(52, 152, 219, 0.3);
  border: 3px solid rgba(255, 255, 255, 0.2);
  transition: all 0.3s ease;
}

.manager-avatar:hover {
  transform: scale(1.05);
  box-shadow: 0 6px 20px rgba(52, 152, 219, 0.4);
}

.manager-info h3 {
  margin: 0;
  font-size: 1.1rem;
  color: #ecf0f1;
  font-weight: 700;
  font-family: 'Amiri', 'Cairo', serif;
  text-shadow: 0 1px 3px rgba(0, 0, 0, 0.3);
}

.manager-info .role {
  font-size: 0.85rem;
  color: #bdc3c7;
  font-weight: 500;
  background: rgba(255, 255, 255, 0.1);
  padding: 0.25rem 0.75rem;
  border-radius: 15px;
  margin-top: 0.25rem;
  display: inline-block;
  font-family: 'Cairo', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
}

.sidebar-close-btn {
  background: rgba(255, 255, 255, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
  cursor: pointer;
  color: #bdc3c7;
  padding: 0.6rem;
  border-radius: 10px;
  transition: all 0.3s ease;
  width: 36px;
  height: 36px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 0.9rem;
}

.sidebar-close-btn:hover {
  background: rgba(231, 76, 60, 0.8);
  color: white;
  border-color: #e74c3c;
  transform: scale(1.05);
  box-shadow: 0 4px 12px rgba(231, 76, 60, 0.3);
}

/* Sidebar Navigation */
.sidebar-nav {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
  overflow-y: auto; /* إضافة scroll للملاحة فقط عند الحاجة */
  min-height: 0; /* للسماح بـ flex shrinking */
  padding-bottom: 1rem; /* مساحة إضافية في الأسفل */
  
  /* Firefox scrollbar styling */
  scrollbar-width: thin;
  scrollbar-color: rgba(255, 255, 255, 0.3) transparent;
}

/* Webkit scrollbar للملاحة */
.sidebar-nav::-webkit-scrollbar {
  width: 6px;
}

.sidebar-nav::-webkit-scrollbar-track {
  background: transparent;
}

.sidebar-nav::-webkit-scrollbar-thumb {
  background: rgba(255, 255, 255, 0.3);
  border-radius: 3px;
}

.sidebar-nav::-webkit-scrollbar-thumb:hover {
  background: rgba(255, 255, 255, 0.5);
}

.nav-btn {
  display: flex;
  align-items: center;
  gap: 1rem;
  padding: 1rem 1.25rem;
  border: none;
  background: rgba(255, 255, 255, 0.08);
  color: #ecf0f1;
  text-align: right;
  border-radius: 12px;
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.25, 0.8, 0.25, 1);
  position: relative;
  font-weight: 600;
  width: 100%;
  direction: rtl;
  font-family: 'Cairo', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
  font-size: 0.95rem;
  border: 1px solid transparent;
  -webkit-backdrop-filter: blur(5px);
  backdrop-filter: blur(5px);
}

.nav-btn::before {
  content: '';
  position: absolute;
  left: 0;
  top: 0;
  bottom: 0;
  width: 4px;
  background: transparent;
  border-radius: 0 4px 4px 0;
  transition: all 0.3s ease;
}

.nav-btn:hover {
  background: rgba(255, 255, 255, 0.15);
  color: #ecf0f1;
  transform: translateX(-3px);
  border-color: rgba(255, 255, 255, 0.2);
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
}

.nav-btn:hover::before {
  background: #3498db;
}

.nav-btn.active {
  background: linear-gradient(135deg, #3498db, #27ae60);
  color: white;
  transform: translateX(-5px);
  border-color: rgba(255, 255, 255, 0.3);
  box-shadow: 0 6px 20px rgba(52, 152, 219, 0.3);
}

.nav-btn.active::before {
  background: white;
  width: 6px;
}

.nav-btn i {
  font-size: 1.2rem;
  width: 24px;
  text-align: center;
  transition: all 0.3s ease;
}

.nav-btn.active i {
  transform: scale(1.1);
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

.nav-btn span {
  flex: 1;
  text-align: right;
  font-weight: 600;
  letter-spacing: 0.3px;
}

.nav-btn .badge {
  background: #e74c3c;
  color: white;
  font-size: 0.7rem;
  padding: 0.3rem 0.6rem;
  border-radius: 15px;
  font-weight: 700;
  min-width: 22px;
  text-align: center;
  box-shadow: 0 2px 8px rgba(231, 76, 60, 0.3);
  animation: pulse 2s infinite;
}

.nav-btn .badge.success {
  background: #27ae60;
  box-shadow: 0 2px 8px rgba(39, 174, 96, 0.3);
}

.nav-btn.active .badge {
  background: rgba(255, 255, 255, 0.9);
  color: #2c3e50;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

@keyframes pulse {
  0%, 100% { transform: scale(1); }
  50% { transform: scale(1.05); }
}

/* Main Content Area */
.main-content {
  flex: 1;
  background: #f8f9fa;
  overflow-y: auto;
  padding: 1rem;
}

/* HomeScreen Styles in Manager Dashboard */
.homeScreen {
  max-width: 1400px;
  margin: 0 auto;
  padding: 1rem;
}

.homeScreen__header {
  background: white;
  border-radius: 12px;
  padding: 2rem;
  margin-bottom: 2rem;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.125);
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex-wrap: wrap;
  gap: 1rem;
}

.homeScreen__welcome-section {
  flex: 1;
}

.homeScreen__title {
  color: #2c3e50;
  font-size: 2rem;
  font-weight: 700;
  margin: 0 0 0.5rem 0;
  display: flex;
  align-items: center;
  gap: 0.75rem;
}

.homeScreen__title-icon {
  color: #2c3e50;
  font-size: 1.8rem;
}

.homeScreen__role-badge {
  background: linear-gradient(135deg, #2c3e50, #17a2b8);
  color: white;
  padding: 0.5rem 1rem;
  border-radius: 20px;
  font-weight: 600;
  font-size: 0.875rem;
  display: inline-block;
  margin: 0;
}

.homeScreen__control-buttons {
  display: flex;
  align-items: center;
  gap: 1rem;
  flex-wrap: wrap;
}

.homeScreen__refresh-btn {
  background: #2c3e50;
  color: white;
  border: none;
  border-radius: 12px;
  padding: 0.75rem 1.5rem;
  cursor: pointer;
  font-weight: 500;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.homeScreen__refresh-btn:hover {
  background: #0056b3;
  transform: translateY(-2px);
}

.homeScreen__refresh-btn--loading {
  opacity: 0.7;
  cursor: not-allowed;
}

.homeScreen__refresh-btn--loading i {
  animation: spin 1s linear infinite;
}

.homeScreen__connection-status {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.5rem 1rem;
  border-radius: 12px;
  font-weight: 500;
  font-size: 0.875rem;
}

.homeScreen__connection-status--online {
  background: rgba(40, 167, 69, 0.1);
  color: #27ae60;
  border: 1px solid rgba(40, 167, 69, 0.3);
}

.homeScreen__connection-status--offline {
  background: rgba(220, 53, 69, 0.1);
  color: #e74c3c;
  border: 1px solid rgba(220, 53, 69, 0.3);
}

.homeScreen__connection-icon {
  font-size: 1rem;
}

.homeScreen__last-update {
  color: #7f8c8d;
  font-size: 0.875rem;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.homeScreen__update-icon {
  color: #17a2b8;
}

/* Stats Grid */
.homeScreen__stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 1.5rem;
  margin-bottom: 2rem;
}

.homeScreen__stat-card {
  background: white;
  border-radius: 12px;
  padding: 1.5rem;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.125);
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  gap: 1rem;
}

.homeScreen__stat-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.125);
}

.homeScreen__stat-icon {
  width: 60px;
  height: 60px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.5rem;
  color: white;
}

.homeScreen__stat-card--orders .homeScreen__stat-icon {
  background: linear-gradient(135deg, #2c3e50, #17a2b8);
}

.homeScreen__stat-card--sales .homeScreen__stat-icon {
  background: linear-gradient(135deg, #27ae60, #20c997);
}

.homeScreen__stat-card--employees .homeScreen__stat-icon {
  background: linear-gradient(135deg, #f39c12, #fd7e14);
}

.homeScreen__stat-card--tables .homeScreen__stat-icon {
  background: linear-gradient(135deg, #17a2b8, #2c3e50);
}

.homeScreen__stat-content {
  flex: 1;
  text-align: right;
}

.homeScreen__stat-number {
  font-size: 2rem;
  font-weight: 700;
  margin: 0 0 0.25rem 0;
  color: #2c3e50;
}

.homeScreen__stat-label {
  color: #7f8c8d;
  font-size: 0.875rem;
  margin: 0;
  font-weight: 500;
}

/* Orders Stats Section */
.homeScreen__orders-stats {
  background: white;
  border-radius: 12px;
  padding: 2rem;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.125);
  margin-bottom: 2rem;
}

.homeScreen__orders-stats-title {
  color: #2c3e50;
  font-size: 1.5rem;
  font-weight: 600;
  margin: 0 0 1.5rem 0;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.homeScreen__orders-stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 1rem;
}

.homeScreen__order-stat {
  background: #ecf0f1;
  border-radius: 12px;
  padding: 1.5rem;
  transition: all 0.3s ease;
  cursor: pointer;
  border: 2px solid transparent;
  text-align: center;
}

.homeScreen__order-stat:hover {
  border-color: #2c3e50;
  transform: translateY(-2px);
}

.homeScreen__order-stat--pending {
  border-left: 4px solid #f39c12;
}

.homeScreen__order-stat--preparing {
  border-left: 4px solid #17a2b8;
}

.homeScreen__order-stat--ready {
  border-left: 4px solid #27ae60;
}

.homeScreen__order-stat--completed {
  border-left: 4px solid #2c3e50;
}

.homeScreen__order-stat-icon-wrapper {
  margin-bottom: 1rem;
}

.homeScreen__order-stat-details {
  text-align: center;
}

.homeScreen__order-stat-count {
  display: block;
  font-size: 2rem;
  font-weight: 700;
  color: #2c3e50;
  margin-bottom: 0.25rem;
}

.homeScreen__order-stat-label {
  color: #7f8c8d;
  font-size: 0.875rem;
  font-weight: 500;
}

/* ====================================
   RESPONSIVE DESIGN & SPACING FIXES
   إصلاحات التصميم المتجاوب والمساحات
   ==================================== */

/* Large Desktop Screens (1400px+) */
@media (min-width: 1400px) {
  .manager-sidebar {
    width: 320px;
  }
  
  .main-content {
    padding: 2rem;
  }
  
  .homeScreen {
    max-width: 1600px;
    padding: 2rem;
  }
  
  .homeScreen__header {
    padding: 2.5rem;
  }
}

/* Desktop Screens (1200px - 1399px) */
@media (min-width: 1200px) and (max-width: 1399px) {
  .manager-sidebar {
    width: 300px;
  }
  
  .main-content {
    padding: 1.5rem;
  }
  
  .homeScreen {
    max-width: 1200px;
    padding: 1.5rem;
  }
}

/* Laptop Screens (992px - 1199px) */
@media (min-width: 992px) and (max-width: 1199px) {
  .manager-sidebar {
    width: 280px;
  }
  
  .main-content {
    padding: 1rem;
  }
  
  .homeScreen {
    max-width: 100%;
    padding: 1rem;
  }
  
  .homeScreen__header {
    padding: 1.5rem;
    flex-direction: column;
    text-align: center;
  }
  
  .homeScreen__control-buttons {
    justify-content: center;
    width: 100%;
  }
}

/* Tablet Screens (768px - 991px) */
@media (min-width: 768px) and (max-width: 991px) {
  .manager-sidebar {
    width: 260px;
    position: fixed;
    left: -260px;
    height: 100vh;
    top: 0;
    z-index: 1001;
    transition: left 0.3s ease;
  }
  
  .manager-sidebar.open {
    left: 0;
  }
  
  .dashboard-content {
    position: relative;
  }
  
  .main-content {
    width: 100%;
    padding: 1rem;
    margin-left: 0;
  }
  
  .sidebar-toggle {
    display: block;
  }
  
  .homeScreen {
    padding: 1rem;
  }
  
  .homeScreen__header {
    padding: 1.5rem;
    flex-direction: column;
    gap: 1.5rem;
  }
  
  .homeScreen__title {
    font-size: 1.75rem;
    text-align: center;
  }
}

/* Mobile Screens (up to 767px) */
@media (max-width: 767px) {
  .app-header {
    padding: 0.75rem;
  }
  
  .app-title {
    font-size: 1.25rem;
  }
  
  .manager-sidebar {
    width: 100%;
    position: fixed;
    right: -100%;
    height: 100vh;
    top: 0;
    z-index: 1001;
    transition: right 0.3s ease;
  }
  
  .manager-sidebar.open {
    right: 0;
  }
  
  .sidebar-overlay.active {
    display: block;
  }
  
  .main-content {
    width: 100%;
    padding: 0.75rem;
    margin-left: 0;
  }
  
  .homeScreen {
    padding: 0.75rem;
  }
  
  .homeScreen__header {
    padding: 1.25rem;
    flex-direction: column;
    gap: 1rem;
    text-align: center;
  }
  
  .homeScreen__title {
    font-size: 1.5rem;
  }
  
  .homeScreen__control-buttons {
    flex-direction: column;
    width: 100%;
  }
  
  .homeScreen__refresh-btn {
    width: 100%;
    justify-content: center;
  }
  
  /* تقليل padding للعناصر الداخلية */
  .sidebar-content {
    padding: 1rem 0.75rem;
  }
  
  .nav-btn {
    padding: 0.75rem 1rem;
    font-size: 0.9rem;
  }
  
  .nav-btn i {
    font-size: 1.1rem;
  }
}

/* Extra Small Mobile (up to 480px) */
@media (max-width: 480px) {
  .app-header {
    padding: 0.5rem;
  }
  
  .app-title {
    font-size: 1.1rem;
  }
  
  .main-content {
    padding: 0.5rem;
  }
  
  .homeScreen {
    padding: 0.5rem;
  }
  
  .homeScreen__header {
    padding: 1rem;
  }
  
  .homeScreen__title {
    font-size: 1.25rem;
  }
  
  .sidebar-content {
    padding: 0.75rem 0.5rem;
  }
  
  .nav-btn {
    padding: 0.6rem 0.75rem;
    font-size: 0.85rem;
  }
  
  .manager-info h3 {
    font-size: 1rem;
  }
  
  .manager-info .role {
    font-size: 0.8rem;
  }
}

/* Landscape orientation fixes for mobile */
@media (max-width: 767px) and (orientation: landscape) {
  .manager-sidebar {
    width: 80%;
    max-width: 320px;
  }
  
  .homeScreen__header {
    flex-direction: row;
    flex-wrap: wrap;
  }
}

/* Fix for very wide screens */
@media (min-width: 1920px) {
  .homeScreen {
    max-width: 1800px;
  }
  
  .main-content {
    padding: 2.5rem;
  }
}

/* Print styles */
@media print {
  .manager-sidebar,
  .app-header,
  .sidebar-overlay {
    display: none !important;
  }
  
  .main-content {
    margin: 0 !important;
    padding: 0 !important;
    width: 100% !important;
  }
}

/* ====================================
   CONTENT AREA SPACING IMPROVEMENTS
   تحسينات مساحات منطقة المحتوى
   ==================================== */

/* تحسين المساحات للمحتوى الرئيسي */
.main-content {
  transition: all 0.3s ease;
  min-height: calc(100vh - 70px);
  position: relative;
}

/* إضافة مساحة محسوبة للمحتوى عندما تكون القائمة مفتوحة */
.dashboard-content:not(.sidebar-closed) .main-content {
  margin-left: 0;
  width: calc(100% - 300px);
}

.dashboard-content.sidebar-closed .main-content {
  margin-left: 0;
  width: calc(100% - 70px);
}

/* تحسين المساحات للشاشات الصغيرة */
@media (max-width: 991px) {
  .dashboard-content .main-content {
    width: 100% !important;
    margin-left: 0 !important;
  }
}

/* منع التداخل في المحتوى */
.main-content > * {
  position: relative;
  z-index: 1;
}

/* تحسين المساحات الداخلية للكروت */
.homeScreen .row {
  margin: 0 -0.75rem;
}

.homeScreen .col-md-3,
.homeScreen .col-md-4,
.homeScreen .col-md-6,
.homeScreen .col-lg-3,
.homeScreen .col-lg-4,
.homeScreen .col-lg-6 {
  padding: 0 0.75rem;
  margin-bottom: 1.5rem;
}

/* ====================================
   SIDEBAR TOGGLE IMPROVEMENTS
   تحسينات زر تبديل القائمة الجانبية
   ==================================== */

.sidebar-toggle {
  position: relative;
  z-index: 1002;
}

/* إخفاء زر التبديل في الشاشات الكبيرة */
@media (min-width: 992px) {
  .sidebar-toggle {
    display: none;
  }
}

/* إظهار زر التبديل في الشاشات الصغيرة */
@media (max-width: 991px) {
  .sidebar-toggle {
    display: flex !important;
  }
}

/* ====================================
   SCROLLBAR IMPROVEMENTS
   تحسينات شريط التمرير
   ==================================== */

.main-content {
  /* Fallback for older browsers */
  overflow-y: auto;
  /* Firefox scrollbar styling */
  scrollbar-width: thin; /* حديث - Firefox فقط */
  scrollbar-color: rgba(44, 62, 80, 0.3) transparent; /* حديث - Firefox فقط */
}

.main-content::-webkit-scrollbar {
  width: 8px;
}

.main-content::-webkit-scrollbar-track {
  background: transparent;
}

.main-content::-webkit-scrollbar-thumb {
  background: rgba(44, 62, 80, 0.3);
  border-radius: 4px;
}

.main-content::-webkit-scrollbar-thumb:hover {
  background: rgba(44, 62, 80, 0.5);
}

/* Advanced Sidebar Enhancements */

/* Closed sidebar styles */
.manager-sidebar.closed .manager-profile {
  justify-content: center;
}

.manager-sidebar.closed .manager-info {
  display: none;
}

.manager-sidebar.closed .nav-btn span {
  display: none;
}

.manager-sidebar.closed .nav-btn .badge {
  position: absolute;
  top: -8px;
  right: -8px;
  transform: scale(0.8);
}

.manager-sidebar.closed .nav-btn {
  justify-content: center;
  padding: 1rem 0.5rem;
}

.manager-sidebar.closed .sidebar-header {
  padding: 1rem 0.5rem;
  margin: -1.5rem -1rem 1.5rem -1rem;
}

/* Smooth transitions for all states */
.manager-sidebar * {
  transition: all 0.3s cubic-bezier(0.25, 0.8, 0.25, 1);
}

/* Custom scrollbar for sidebar */
.sidebar-content {
  /* Firefox */
  scrollbar-width: thin;
  scrollbar-color: rgba(255, 255, 255, 0.3) transparent;
}

/* Loading states */
.nav-btn.loading {
  opacity: 0.7;
  pointer-events: none;
}

.nav-btn.loading i {
  animation: spin 1s linear infinite;
}

/* Notification indicators */
.nav-btn .notification-dot {
  position: absolute;
  top: 8px;
  right: 8px;
  width: 8px;
  height: 8px;
  background: #e74c3c;
  border-radius: 50%;
  border: 2px solid white;
  animation: pulse 2s infinite;
}

/* Section dividers */
.nav-section {
  margin: 1.5rem 0 0.5rem 0;
  padding-top: 1rem;
  border-top: 1px solid rgba(255, 255, 255, 0.1);
}

.nav-section:first-child {
  margin-top: 0;
  padding-top: 0;
  border-top: none;
}

.nav-section-title {
  color: #bdc3c7;
  font-size: 0.75rem;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 1px;
  margin-bottom: 0.75rem;
  padding: 0 1.25rem;
  opacity: 0.8;
}

/* Enhanced hover effects */
.nav-btn:hover {
  background: rgba(255, 255, 255, 0.15);
  color: #ecf0f1;
  transform: translateX(-3px);
  border-color: rgba(255, 255, 255, 0.2);
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
}

.nav-btn:active {
  transform: translateX(-2px) scale(0.98);
}

/* Dark mode support */
@media (prefers-color-scheme: dark) {
  /* ?? ????? ????????? ??????? ?????? ??? ????? */
}

/* High contrast mode */
@media (prefers-contrast: high) {
  .nav-btn {
    border: 2px solid rgba(255, 255, 255, 0.3);
  }
  
  .nav-btn.active {
    border-color: white;
  }
}

/* Reduce motion for accessibility */
@media (prefers-reduced-motion: reduce) {
  .manager-sidebar,
  .nav-btn,
  .manager-avatar,
  .sidebar-content * {
    transition: none !important;
    animation: none !important;
  }
}

@keyframes spin {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

/* RTL support enhancements */
[dir="rtl"] .nav-btn {
  direction: rtl;
}

[dir="rtl"] .nav-btn::before {
  left: auto;
  right: 0;
  border-radius: 4px 0 0 4px;
}

[dir="rtl"] .nav-btn:hover,
[dir="rtl"] .nav-btn.active {
  transform: translateX(3px);
}

/* ====================================
   MAIN CONTENT CONSISTENCY FIXES
   إصلاحات اتساق المحتوى الرئيسي
   ==================================== */

/* تنسيق موحد للمحتوى الرئيسي */
.manager-main {
  flex: 1;
  background: #f8f9fa;
  min-height: calc(100vh - 70px);
  position: relative;
  overflow-y: auto;
  transition: all 0.3s ease;
  
  /* تطبيق نفس إعدادات main-content */
  padding: 1rem;
  width: 100%;
  margin-left: 0;
  
  /* Firefox scrollbar styling */
  scrollbar-width: thin; /* حديث - Firefox فقط */
  scrollbar-color: rgba(44, 62, 80, 0.3) transparent; /* حديث - Firefox فقط */
}

/* Webkit scrollbar styling للمحتوى الرئيسي */
.manager-main::-webkit-scrollbar {
  width: 8px;
}

.manager-main::-webkit-scrollbar-track {
  background: transparent;
}

.manager-main::-webkit-scrollbar-thumb {
  background: rgba(44, 62, 80, 0.3);
  border-radius: 4px;
}

.manager-main::-webkit-scrollbar-thumb:hover {
  background: rgba(44, 62, 80, 0.5);
}

/* تحديد عرض المحتوى حسب حالة القائمة الجانبية */
@media (min-width: 992px) {
  .manager-main {
    margin-left: 0;
  }
  
  .dashboard-content:not(.sidebar-closed) .manager-main {
    width: calc(100% - 300px);
    margin-left: 0;
  }
  
  .dashboard-content.sidebar-closed .manager-main {
    width: calc(100% - 70px);
    margin-left: 0;
  }
}

/* للشاشات الصغيرة - المحتوى يأخذ العرض الكامل */
@media (max-width: 991px) {
  .manager-main {
    width: 100% !important;
    margin-left: 0 !important;
    padding: 1rem;
  }
}

@media (max-width: 767px) {
  .manager-main {
    padding: 0.75rem;
  }
}

@media (max-width: 480px) {
  .manager-main {
    padding: 0.5rem;
  }
}

/* تنسيق المحتوى الداخلي للشاشات */
.screen-content {
  width: 100%;
  max-width: 100%;
  margin: 0;
  padding: 0;
  position: relative;
  z-index: 1;
}

/* ====================================
   BOOTSTRAP CONTAINER CONSISTENCY
   اتساق حاويات Bootstrap
   ==================================== */

/* توحيد سلوك container-fluid في جميع الشاشات */
.manager-main .container-fluid {
  width: 100%;
  max-width: 100%;
  margin: 0;
  padding-left: 0;
  padding-right: 0;
}

/* تعديل padding للشاشات المختلفة */
.manager-main .container-fluid.p-4 {
  padding: 1.5rem !important;
}

.manager-main .container-fluid.py-4 {
  padding-top: 1.5rem !important;
  padding-bottom: 1.5rem !important;
  padding-left: 0 !important;
  padding-right: 0 !important;
}

/* للشاشات الصغيرة */
@media (max-width: 991px) {
  .manager-main .container-fluid.p-4 {
    padding: 1rem !important;
  }
  
  .manager-main .container-fluid.py-4 {
    padding-top: 1rem !important;
    padding-bottom: 1rem !important;
  }
}

@media (max-width: 767px) {
  .manager-main .container-fluid.p-4 {
    padding: 0.75rem !important;
  }
  
  .manager-main .container-fluid.py-4 {
    padding-top: 0.75rem !important;
    padding-bottom: 0.75rem !important;
  }
}

@media (max-width: 480px) {
  .manager-main .container-fluid.p-4 {
    padding: 0.5rem !important;
  }
  
  .manager-main .container-fluid.py-4 {
    padding-top: 0.5rem !important;
    padding-bottom: 0.5rem !important;
  }
}

/* ====================================
   SPECIFIC SCREEN CONTAINERS
   حاويات الشاشات المحددة
   ==================================== */

/* توحيد جميع الحاويات المخصصة للشاشات */
.manager-main .categories-bootstrap-container,
.manager-main .discount-requests-bootstrap-container,
.manager-main .inventory-bootstrap-container,
.manager-main .reports-bootstrap-container,
.manager-main .settings-bootstrap-container {
  width: 100%;
  max-width: 100%;
  margin: 0;
  padding-left: 0;
  padding-right: 0;
}

/* ====================================
   HOME SCREEN CONSISTENCY
   اتساق الشاشة الرئيسية
   ==================================== */

/* توحيد تنسيق الشاشة الرئيسية */
.manager-main .homeScreen {
  width: 100%;
  max-width: 100%;
  margin: 0;
  padding: 0;
}

/* ====================================
   LAYOUT OVERFLOW FIXES
   إصلاحات تسرب التخطيط
   ==================================== */

/* منع التسرب الأفقي */
.manager-main,
.manager-main > *,
.manager-main .container-fluid {
  overflow-x: hidden;
}

/* ضمان عدم تجاوز العرض */
.manager-main .row {
  margin-left: 0;
  margin-right: 0;
}

.manager-main .col-*,
.manager-main [class*="col-"] {
  padding-left: 0.75rem;
  padding-right: 0.75rem;
}

/* ====================================
   DEBUG HELPERS (للتطوير فقط)
   ==================================== */

/* يمكن إزالة هذا القسم في الإنتاج */
.debug-spacing .manager-main {
  border: 2px solid red;
}

.debug-spacing .manager-main .container-fluid {
  border: 2px solid blue;
}

.debug-spacing .manager-main .homeScreen {
  border: 2px solid green;
}

/* ====================================
   SIDEBAR HEIGHT OPTIMIZATION
   تحسين ارتفاع القائمة الجانبية
   ==================================== */

/* ضمان استخدام كامل ارتفاع الشاشة */
.dashboard-content {
  height: 100vh;
  overflow: hidden;
}

/* تحسين ارتفاع القائمة الجانبية للشاشات المختلفة */
@media (min-width: 992px) {
  .manager-sidebar {
    height: 100vh; /* كامل ارتفاع الشاشة */
    position: fixed;
    top: 0;
    right: 0;
    z-index: 1000;
  }
  
  /* تعديل المحتوى الرئيسي ليبدأ من اليسار */
  .dashboard-content:not(.sidebar-closed) .manager-main {
    margin-right: 300px;
    width: calc(100% - 300px);
  }
  
  .dashboard-content.sidebar-closed .manager-main {
    margin-right: 70px;
    width: calc(100% - 70px);
  }
}

/* للشاشات المتوسطة والصغيرة */
@media (max-width: 991px) {
  .manager-sidebar {
    height: 100vh;
    position: fixed;
    top: 0;
    right: -100%;
    z-index: 1051;
    width: 280px;
    transition: right 0.3s cubic-bezier(0.25, 0.8, 0.25, 1);
    will-change: transform;
    transform: translateZ(0);
  }
  
  .manager-sidebar.open {
    right: 0;
  }
  
  .dashboard-content .manager-main {
    margin-right: 0 !important;
    width: 100% !important;
  }
}

/* للهواتف المحمولة */
@media (max-width: 767px) {
  .manager-sidebar {
    width: 100%;
    right: -100%;
  }
  
  .manager-sidebar.open {
    right: 0;
  }
}

/* ====================================
   SIDEBAR HEADER HEIGHT FIX
   إصلاح ارتفاع رأس القائمة الجانبية
   ==================================== */

.sidebar-header {
  flex-shrink: 0; /* منع تقليص الرأس */
  border-bottom: 1px solid rgba(255, 255, 255, 0.15);
  padding-bottom: 1.5rem;
  margin-bottom: 2rem;
  background: rgba(255, 255, 255, 0.05);
  margin: 0 -1rem 2rem -1rem;
  padding: 1.5rem 1rem;
  -webkit-backdrop-filter: blur(10px); /* دعم Safari */
  backdrop-filter: blur(10px);
}

/* ====================================
   SIDEBAR FOOTER OPTIMIZATION
   تحسين تذييل القائمة الجانبية
   ==================================== */

.sidebar-footer {
  flex-shrink: 0; /* منع تقليص التذييل */
  margin-top: auto; /* دفع التذييل للأسفل */
  padding-top: 1rem;
  border-top: 1px solid rgba(255, 255, 255, 0.15);
}

.connection-status {
  padding: 0.75rem;
  background: rgba(255, 255, 255, 0.05);
  border-radius: 12px;
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.socket-indicator {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-size: 0.85rem;
  color: #bdc3c7;
}

.socket-dot {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background: #e74c3c;
  animation: pulse 2s infinite;
}

.socket-indicator.connected .socket-dot {
  background: #27ae60;
  animation: none;
}

/* ====================================
   RESPONSIVE HEIGHT ADJUSTMENTS
   تعديلات الارتفاع المتجاوبة
   ==================================== */

/* للشاشات القصيرة */
@media (max-height: 600px) {
  .sidebar-content {
    padding: 1rem 1rem;
  }
  
  .sidebar-header {
    padding: 1rem 1rem;
    margin-bottom: 1rem;
  }
  
  .nav-btn {
    padding: 0.75rem 1rem;
  }
  
  .sidebar-nav {
    gap: 0.5rem;
  }
}

/* للشاشات الطويلة */
@media (min-height: 900px) {
  .sidebar-content {
    padding: 2rem 1rem;
  }
  
  .sidebar-header {
    padding: 2rem 1rem;
    margin-bottom: 2.5rem;
  }
  
  .nav-btn {
    padding: 1.25rem 1.5rem;
  }
  
  .sidebar-nav {
    gap: 1rem;
  }
}

/* ====================================
   PERFORMANCE OPTIMIZATIONS
   تحسينات الأداء
   ==================================== */

/* استخدام hardware acceleration للقائمة الجانبية */
.manager-sidebar {
  will-change: transform;
  transform: translateZ(0);
}

/* تحسين الانتقالات */
.manager-sidebar,
.manager-main {
  transition: all 0.3s cubic-bezier(0.25, 0.8, 0.25, 1);
}

/* منع layout thrashing */
.sidebar-content,
.sidebar-nav {
  contain: layout style;
}


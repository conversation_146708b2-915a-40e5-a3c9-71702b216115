// API Helper functions for authenticated requests - NO RATE LIMITING
// دوال مساعدة لطلبات API مع المصادقة - بدون قيود المعدل

import { getApiUrl } from '../config/app.config';

// Get auth token from localStorage
export const getAuthToken = (): string | null => {
  return localStorage.getItem('token') || localStorage.getItem('authToken');
};

// Get current user from localStorage  
export const getCurrentUser = (): any | null => {
  try {
    const userStr = localStorage.getItem('user');
    if (!userStr) return null;
    
    const user = JSON.parse(userStr);
    // تأكد من أن البيانات المرجعة ليست Proxy
    return user ? { ...user } : null;
  } catch (error) {
    console.error('Error parsing user data:', error);
    return null;
  }
};

// Get auth headers with token - CORS COMPATIBLE: Standard and custom headers
export const getAuthHeaders = (): HeadersInit => {
  const token = getAuthToken();
  const headers: HeadersInit = {
    'Content-Type': 'application/json',
    'Accept': 'application/json',
  };

  if (token) {
    headers['Authorization'] = `Bearer ${token}`;
  }

  // Add minimal user info using only ASCII characters - CORS COMPATIBLE
  try {
    const user = getCurrentUser();
    if (user && typeof user === 'object') {
      // Use only ASCII characters and basic IDs - no Arabic text in headers
      const userId = String(user._id || user.id || user.userId || '').substring(0, 50);
      const userRole = String(user.role || 'user').replace(/[^\x00-\x7F]/g, '');
      
      if (userId) headers['X-User-ID'] = userId;
      if (userRole) headers['X-User-Role'] = userRole;
      
      // For chefs, use simple identifier without Arabic characters
      if (user.role === 'chef') {
        const chefId = String(user._id || user.id || 'chef').substring(0, 20);
        headers['X-Chef-ID'] = chefId;
        headers['X-Request-Type'] = 'chef-ops';
      }
    }
  } catch (error) {
    // Silently continue without user headers if there's any issue
    console.debug('Skipping user headers due to error:', error);
  }

  return headers;
};

// Make authenticated API request
export const makeAuthenticatedRequest = async (
  endpoint: string,
  options: RequestInit = {}
): Promise<Response> => {
  const url = getApiUrl(endpoint);
  const headers = {
    ...getAuthHeaders(),
    ...options.headers,
  };

  return fetch(url, {
    ...options,
    headers,
  });
};

// GET request with authentication - NO RATE LIMITING WHATSOEVER
export const authenticatedGet = async (endpoint: string): Promise<any> => {
  try {
    console.log(`🚀 Making direct request to ${endpoint} (No rate limiting)`);
    const response = await makeAuthenticatedRequest(endpoint);

    if (!response.ok) {
      // Handle specific error codes
      if (response.status === 429) {
        console.warn(`🚫 Server returned 429 for ${endpoint} - server issue, not client rate limiting`);
        throw new Error(`HTTP 429: Server overloaded - Please try again in a moment`);
      }

      if (response.status === 401) {
        console.error('❌ Authentication failed - Token may be expired');
        localStorage.removeItem('token');
        localStorage.removeItem('authToken');
        throw new Error('Authentication required');
      }

      throw new Error(`HTTP ${response.status}: ${response.statusText}`);
    }

    console.log(`✅ Request successful for ${endpoint}`);
    return await response.json();
  } catch (error) {
    console.error(`❌ Request failed for ${endpoint}:`, error);
    throw error;
  }
};

// POST request with authentication - NO RATE LIMITING WHATSOEVER
export const authenticatedPost = async (
  endpoint: string,
  data: any
): Promise<any> => {
  try {
    console.log(`🚀 Making POST request to ${endpoint} (No rate limiting)`);
    
    const response = await makeAuthenticatedRequest(endpoint, {
      method: 'POST',
      body: JSON.stringify(data),
    });
    
    if (!response.ok) {
      // Try to get error details from response
      let errorData: any = null;
      try {
        errorData = await response.json();
      } catch (parseError) {
        console.warn('Could not parse error response:', parseError);
      }
      
      // Handle specific error codes
      if (response.status === 409) {
        // Handle table conflict errors specially
        const errorMessage = errorData?.message || 'يوجد تضارب في البيانات';
        const error = new Error(errorMessage) as any;
        error.status = 409;
        error.response = { status: 409, data: errorData };
        console.error(`⚠️ Conflict error (409) for ${endpoint}:`, errorData);
        throw error;
      }
      
      if (response.status === 429) {
        console.error(`🚫 Server returned 429 for ${endpoint} - server issue, not client rate limiting`);
        throw new Error(`HTTP 429: Server overloaded - Please try again in a moment`);
      }

      if (response.status === 401) {
        console.error('❌ Authentication failed - Token may be expired');
        localStorage.removeItem('token');
        localStorage.removeItem('authToken');
        throw new Error('Authentication required');
      }
      
      const errorMessage = errorData?.message || response.statusText;
      const error = new Error(`HTTP ${response.status}: ${errorMessage}`) as any;
      error.status = response.status;
      error.response = { status: response.status, data: errorData };
      throw error;
    }
    
    return await response.json();
  } catch (error) {
    console.error('POST request failed:', error);
    throw error;
  }
};

// PUT request with authentication - NO RATE LIMITING WHATSOEVER
export const authenticatedPut = async (
  endpoint: string,
  data: any
): Promise<any> => {
  try {
    console.log(`🚀 Making PUT request to ${endpoint} (No rate limiting)`);
    
    const response = await makeAuthenticatedRequest(endpoint, {
      method: 'PUT',
      body: JSON.stringify(data),
    });
    
    if (response.status === 401) {
      console.error('❌ Authentication failed - Token may be expired');
      // Clear invalid token
      localStorage.removeItem('token');
      localStorage.removeItem('authToken');
      // Redirect to login
      window.location.href = '/login';
      throw new Error('Authentication required - redirecting to login');
    }
    
    if (!response.ok) {
      // Handle specific error codes
      if (response.status === 429) {
        console.error(`🚫 Server returned 429 for ${endpoint} - server issue, not client rate limiting`);
        throw new Error(`HTTP 429: Server overloaded - Please try again in a moment`);
      }
      
      const errorText = await response.text();
      console.error(`❌ HTTP ${response.status} error:`, errorText);
      throw new Error(`HTTP ${response.status}: ${errorText || response.statusText}`);
    }
    
    return await response.json();
  } catch (error) {
    console.error('PUT request failed:', error);
    throw error;
  }
};

// DELETE request with authentication
export const authenticatedDelete = async (endpoint: string): Promise<any> => {
  try {
    console.log(`🗑️ Making DELETE request to: ${endpoint}`);
    
    const response = await makeAuthenticatedRequest(endpoint, {
      method: 'DELETE',
    });
    
    console.log(`📡 DELETE response status: ${response.status} ${response.statusText}`);
    
    if (!response.ok) {
      // Try to get error details from response
      let errorDetails = '';
      try {
        const errorData = await response.json();
        errorDetails = errorData.message || errorData.error || response.statusText;
        console.error(`❌ DELETE request failed with details:`, errorData);
      } catch (parseError) {
        errorDetails = response.statusText;
        console.error(`❌ DELETE request failed, could not parse error response:`, parseError);
      }
      
      throw new Error(`HTTP ${response.status}: ${errorDetails}`);
    }
    
    const result = await response.json();
    console.log(`✅ DELETE request successful:`, result);
    return result;
  } catch (error) {
    console.error('❌ Authenticated DELETE request failed:', error);
    throw error;
  }
};

// Check if user is authenticated
export const isAuthenticated = (): boolean => {
  const token = getAuthToken();
  const user = localStorage.getItem('user');
  return !!(token && user);
};

// Logout user (clear all auth data)
export const logoutUser = (): void => {
  localStorage.removeItem('token');
  localStorage.removeItem('authToken');
  localStorage.removeItem('user');
  localStorage.removeItem('username');
  localStorage.removeItem('userRole');
};

// Helper function to check if a request is a chef critical operation
export const isChefCriticalOperation = (endpoint: string): boolean => {
  const user = getCurrentUser();
  if (user?.role !== 'chef') return false;
  
  // All chef operations are considered critical and bypass any rate limiting
  return endpoint.includes('/orders') || 
         endpoint.includes('/order-items') ||
         endpoint.includes('/menu-items') ||
         endpoint.includes('/categories');
};

// Helper function to get user role safely
export const getUserRole = (): string => {
  try {
    const user = getCurrentUser();
    return user?.role || 'user';
  } catch {
    return 'user';
  }
};

// Helper function to get chef ID safely
export const getChefId = (): string => {
  try {
    const user = getCurrentUser();
    if (user?.role === 'chef') {
      return user._id || user.id || 'chef-user';
    }
    return '';
  } catch {
    return '';
  }
};

// Helper function to get chef name safely (ASCII only to prevent header encoding issues)
export const getChefName = (): string => {
  try {
    const user = getCurrentUser();
    if (user?.role === 'chef') {
      const name = user.username || user.name || localStorage.getItem('username') || 'chef';
      // Convert to ASCII only to avoid header encoding issues that caused the Unicode error
      return name.replace(/[^\x00-\x7F]/g, 'chef').substring(0, 20);
    }
    return 'chef';
  } catch {
    return 'chef';
  }
};

// ***** NOTE: RATE LIMITING IS COMPLETELY DISABLED *****
// These functions are kept for compatibility but do nothing
export const canMakeRequest = (endpoint: string): boolean => {
  console.log(`🧑‍🍳 Rate limiting DISABLED - allowing all requests to ${endpoint}`);
  return true;
};

export const waitForRateLimit = async (endpoint: string): Promise<void> => {
  // Do nothing - no rate limiting
  return;
};

export const rateLimiter = {
  canMakeRequest: () => true,
  getWaitTime: () => 0,
  getRemainingRequests: () => 999,
  getNextResetTime: () => 0
};

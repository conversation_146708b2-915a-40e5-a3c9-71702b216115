const https = require('https');

// معلومات الاتصال
const backEndURL = 'https://deshacoffee-production.up.railway.app';

// بيانات الحسابات
const accounts = {
  waiter: {
    username: '<PERSON><PERSON>',
    password: '253040',
    role: 'نادل'
  }
};

// دالة مساعدة لإرسال طلبات HTTP
function makeRequest(url, options, data = null) {
  return new Promise((resolve, reject) => {
    const req = https.request(url, options, (res) => {
      let body = '';
      res.on('data', chunk => body += chunk);
      res.on('end', () => {
        try {
          const jsonData = JSON.parse(body);
          resolve({ status: res.statusCode, data: jsonData, headers: res.headers });
        } catch (e) {
          resolve({ status: res.statusCode, data: body, headers: res.headers });
        }
      });
    });

    req.on('error', reject);
    
    if (data) {
      req.write(JSON.stringify(data));
    }
    
    req.end();
  });
}

// دالة تسجيل الدخول
async function login(account) {
  console.log(`🔐 محاولة تسجيل الدخول: ${account.username} (${account.role})`);
  
  const options = {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json'
    }
  };

  try {
    const response = await makeRequest(`${backEndURL}/api/auth/login`, options, {
      username: account.username,
      password: account.password
    });

    if (response.status === 200 && response.data.token) {
      console.log(`✅ تم تسجيل الدخول بنجاح: ${account.username}`);
      return {
        token: response.data.token,
        user: response.data.user
      };
    } else {
      console.log(`❌ فشل تسجيل الدخول: ${account.username}`, response.data);
      return null;
    }
  } catch (error) {
    console.error(`❌ خطأ في تسجيل الدخول: ${account.username}`, error.message);
    return null;
  }
}

// دالة جلب المنتجات للحصول على ID حقيقي
async function getProducts(token) {
  console.log('📦 جلب قائمة المنتجات...');
  
  const options = {
    method: 'GET',
    headers: {
      'Authorization': `Bearer ${token}`,
      'Content-Type': 'application/json'
    }
  };

  try {
    const response = await makeRequest(`${backEndURL}/api/products`, options);
    
    if (response.status === 200) {
      const products = Array.isArray(response.data) ? response.data : response.data.data || [];
      console.log(`📦 تم جلب ${products.length} منتج`);
      return products;
    } else {
      console.log('❌ فشل في جلب المنتجات:', response.data);
      return [];
    }
  } catch (error) {
    console.error('❌ خطأ في جلب المنتجات:', error.message);
    return [];
  }
}

// دالة اختبار إرسال طلب للطاولة محجوزة
async function testTableOccupiedMessage(token, user, products) {
  console.log(`🧪 اختبار رسالة الطاولة المحجوزة...`);
  
  // استخدام طاولة محجوزة (رقم 1 محجوزة للنادل azza)
  const occupiedTableNumber = 1;
  
  // استخدام منتج حقيقي من قاعدة البيانات
  const testProduct = products[0];
  if (!testProduct) {
    console.log('❌ لا توجد منتجات متاحة للاختبار');
    return false;
  }
  
  const testOrder = {
    items: [
      {
        product: testProduct._id,
        productId: testProduct._id,
        name: testProduct.name,
        productName: testProduct.name,
        quantity: 1,
        price: testProduct.price || 15,
        subtotal: testProduct.price || 15,
        notes: '',
        specialRequests: '',
        modifications: []
      }
    ],
    customer: {
      name: 'عميل تجريبي',
      phone: '',
      email: ''
    },
    customerName: 'عميل تجريبي',
    table: {
      number: occupiedTableNumber,
      section: 'القسم الرئيسي'
    },
    tableNumber: occupiedTableNumber.toString(),
    waiterName: user.username,
    waiterId: user._id,
    waiterUsername: user.username,
    totalPrice: testProduct.price || 15,
    totals: {
      subtotal: testProduct.price || 15,
      tax: 0,
      discount: 0,
      total: testProduct.price || 15
    },
    orderType: 'dine-in',
    status: 'pending',
    payment: {
      method: 'cash',
      status: 'pending'
    }
  };

  const options = {
    method: 'POST',
    headers: {
      'Authorization': `Bearer ${token}`,
      'Content-Type': 'application/json'
    }
  };

  try {
    console.log(`📋 محاولة إرسال طلب للطاولة المحجوزة ${occupiedTableNumber}...`);
    console.log('📦 بيانات الطلب:', {
      tableNumber: occupiedTableNumber,
      customerName: testOrder.customerName,
      waiterName: user.username,
      items: testOrder.items.map(item => ({
        name: item.name,
        quantity: item.quantity,
        price: item.price
      }))
    });
    
    const response = await makeRequest(`${backEndURL}/api/orders`, options, testOrder);
    
    console.log(`📊 رمز الاستجابة: ${response.status}`);
    
    if (response.status === 409) {
      console.log('✅ تم الحصول على الخطأ 409 كما هو متوقع');
      console.log('📝 محتوى الرسالة المحسنة:');
      console.log('─'.repeat(60));
      console.log(response.data.message);
      console.log('─'.repeat(60));
      
      // عرض البيانات الإضافية
      if (response.data.tableNumber) {
        console.log(`📍 رقم الطاولة: ${response.data.tableNumber}`);
      }
      if (response.data.occupiedByWaiter) {
        console.log(`👤 النادل المسؤول: ${response.data.occupiedByWaiter}`);
      }
      if (response.data.currentWaiter) {
        console.log(`👤 النادل الحالي: ${response.data.currentWaiter}`);
      }
      if (response.data.availableTables) {
        console.log(`📋 الطاولات المتاحة: ${response.data.availableTables.join(', ')}`);
      }
      
      console.log('\n🎉 الرسالة المحسنة تعمل بنجاح!');
      return true;
      
    } else if (response.status >= 200 && response.status < 300) {
      console.log('⚠️ تم إرسال الطلب بنجاح - ربما الطاولة لم تعد محجوزة');
      console.log('📦 بيانات الطلب:', response.data);
      return false;
    } else {
      console.log(`❌ خطأ غير متوقع: ${response.status}`);
      console.log('📝 تفاصيل الخطأ:', response.data);
      return false;
    }
  } catch (error) {
    console.error('❌ خطأ في اختبار الطلب:', error.message);
    return false;
  }
}

// الدالة الرئيسية
async function main() {
  console.log('🚀 بدء اختبار الإشعار المحسن للطاولة المحجوزة...\n');
  
  // 1. تسجيل الدخول كنادل
  console.log('=== المرحلة 1: تسجيل الدخول ===');
  const waiterAuth = await login(accounts.waiter);
  
  if (!waiterAuth) {
    console.log('❌ فشل في تسجيل الدخول. لا يمكن المتابعة.');
    return;
  }
  
  // 2. جلب المنتجات
  console.log('\n=== المرحلة 2: جلب المنتجات ===');
  const products = await getProducts(waiterAuth.token);
  
  if (products.length === 0) {
    console.log('❌ لا توجد منتجات متاحة. لا يمكن المتابعة.');
    return;
  }
  
  // 3. اختبار رسالة الطاولة المحجوزة
  console.log('\n=== المرحلة 3: اختبار الرسالة المحسنة ===');
  const testResult = await testTableOccupiedMessage(waiterAuth.token, waiterAuth.user, products);
  
  if (testResult) {
    console.log('\n🎉 تم اختبار الإشعار المحسن بنجاح!');
    console.log('\n📋 ملخص التحسينات:');
    console.log('✅ رسالة واضحة ومفيدة بدلاً من الخطأ 409');
    console.log('✅ معلومات عن النادل المسؤول');
    console.log('✅ قائمة بالطاولات المتاحة');
    console.log('✅ إرشادات واضحة للنادل');
  } else {
    console.log('\n⚠️ لم يتم اختبار الرسالة كما هو متوقع.');
  }
  
  console.log('\n✅ انتهى الاختبار.');
}

// تشغيل البرنامج
main().catch(error => {
  console.error('❌ خطأ عام في البرنامج:', error);
});

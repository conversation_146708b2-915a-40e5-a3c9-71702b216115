// routes/reports.js
const express = require('express');
const router = express.Router();
const Order = require('../models/Order');
const User = require('../models/User');
const { authenticateToken } = require('../middleware/auth');

/**
 * @route   GET /api/reports/daily-sales
 * @desc    Get daily sales report
 * @access  Private
 */
router.get('/daily-sales', authenticateToken, async (req, res) => {
  try {
    const { startDate, endDate } = req.query;
    
    // تحديد نطاق التاريخ (افتراضياً: آخر 30 يوم)
    const end = endDate ? new Date(endDate) : new Date();
    const start = startDate ? new Date(startDate) : new Date(Date.now() - 30 * 24 * 60 * 60 * 1000);
      // جلب الطلبات المكتملة في النطاق المحدد - توحيد جميع حالات الطلبات المكتملة
    const orders = await Order.find({
      'timing.orderTime': { $gte: start, $lte: end },
      status: { $in: ['served', 'delivered', 'completed'] } // تضمين جميع حالات الإكمال
    }).populate('items.product', 'name price');

    console.log('📊 Daily sales report query:', {
      dateRange: { start, end },
      ordersFound: orders.length,
      sampleOrder: orders[0] ? {
        id: orders[0]._id,
        total: orders[0].pricing?.total || orders[0].totals?.total,
        status: orders[0].status,
        date: orders[0].timing?.orderTime
      } : null
    });

    // تجميع البيانات حسب التاريخ
    const dailySales = {};
    
    orders.forEach(order => {
      const date = order.timing.orderTime.toISOString().split('T')[0];
      
      if (!dailySales[date]) {
        dailySales[date] = {
          date,
          totalSales: 0,
          ordersCount: 0,
          averageOrderValue: 0
        };
      }
      
      // استخدام الحقل الصحيح للمجموع
      const orderTotal = order.pricing?.total || order.totals?.total || 
        order.items.reduce((sum, item) => sum + (item.price * item.quantity), 0);
      
      dailySales[date].totalSales += orderTotal;
      dailySales[date].ordersCount += 1;
    });

    // حساب متوسط قيمة الطلب
    Object.values(dailySales).forEach(day => {
      day.averageOrderValue = day.ordersCount > 0 ? day.totalSales / day.ordersCount : 0;
    });

    const result = Object.values(dailySales).sort((a, b) => new Date(a.date) - new Date(b.date));

    res.json({
      success: true,
      data: result
    });
  } catch (error) {
    console.error('خطأ في تقرير المبيعات اليومية:', error);
    res.status(500).json({
      success: false,
      message: 'خطأ في جلب تقرير المبيعات اليومية',
      error: error.message
    });
  }
});

/**
 * @route   GET /api/reports/weekly-sales
 * @desc    Get weekly sales report
 * @access  Private
 */
router.get('/weekly-sales', authenticateToken, async (req, res) => {
  try {
    const { weeks = 4 } = req.query;
    
    // حساب بداية الفترة (عدد الأسابيع المطلوب)
    const endDate = new Date();
    const startDate = new Date(Date.now() - weeks * 7 * 24 * 60 * 60 * 1000);
      const orders = await Order.find({
      'timing.orderTime': { $gte: startDate, $lte: endDate },
      status: { $in: ['served', 'delivered', 'completed'] } // تضمين جميع حالات الإكمال
    }).populate('items.product', 'name price');

    console.log('📊 Weekly sales report query:', {
      dateRange: { startDate, endDate },
      ordersFound: orders.length
    });

    // تجميع البيانات حسب الأسبوع
    const weeklySales = {};
    
    orders.forEach(order => {
      const orderDate = new Date(order.timing.orderTime);
      const weekStart = new Date(orderDate);
      weekStart.setDate(orderDate.getDate() - orderDate.getDay());
      const weekKey = weekStart.toISOString().split('T')[0];
      
      if (!weeklySales[weekKey]) {
        weeklySales[weekKey] = {
          week: `أسبوع ${weekStart.toLocaleDateString('ar-EG')}`,
          totalSales: 0,
          ordersCount: 0,
          averageOrderValue: 0
        };
      }
      
      const orderTotal = order.total || order.items.reduce((sum, item) => 
        sum + (item.price * item.quantity), 0);
      
      weeklySales[weekKey].totalSales += orderTotal;
      weeklySales[weekKey].ordersCount += 1;
    });

    // حساب متوسط قيمة الطلب
    Object.values(weeklySales).forEach(week => {
      week.averageOrderValue = week.ordersCount > 0 ? week.totalSales / week.ordersCount : 0;
    });

    const result = Object.values(weeklySales).sort((a, b) => new Date(a.week) - new Date(b.week));

    res.json({
      success: true,
      data: result
    });
  } catch (error) {
    console.error('خطأ في تقرير المبيعات الأسبوعية:', error);
    res.status(500).json({
      success: false,
      message: 'خطأ في جلب تقرير المبيعات الأسبوعية',
      error: error.message
    });
  }
});

/**
 * @route   GET /api/reports/waiter-stats
 * @desc    Get waiter statistics
 * @access  Private
 */
router.get('/waiter-stats', authenticateToken, async (req, res) => {
  try {
    const { period = '30' } = req.query; // افتراضياً: آخر 30 يوم
    
    const startDate = new Date(Date.now() - parseInt(period) * 24 * 60 * 60 * 1000);
    const endDate = new Date();
    
    // جلب النُدل
    const waiters = await User.find({ role: 'waiter', isActive: true });
    
    // جلب الطلبات في الفترة المحددة - توحيد جميع حالات الطلبات المكتملة
    const orders = await Order.find({
      'timing.orderTime': { $gte: startDate, $lte: endDate },
      status: { $in: ['served', 'delivered', 'completed'] } // تضمين جميع حالات الإكمال
    }).populate('staff.waiter', 'name username');

    const waiterStats = waiters.map(waiter => {
      const waiterOrders = orders.filter(order => 
        order.staff.waiter && 
        (order.staff.waiter._id.toString() === waiter._id.toString() ||
         order.waiterName === waiter.username ||
         order.waiterName === waiter.name)
      );
        const totalSales = waiterOrders.reduce((sum, order) => {
        const orderTotal = order.pricing?.total || order.totals?.total || 
                          order.total || 
                          order.items.reduce((itemSum, item) => 
                            itemSum + (item.price * item.quantity), 0);
        return sum + orderTotal;
      }, 0);
      
      const averageOrderValue = waiterOrders.length > 0 ? totalSales / waiterOrders.length : 0;
      
      // تقدير ساعات العمل (افتراضياً 8 ساعات/يوم × أيام العمل)
      const workingDays = Math.min(parseInt(period), 30);
      const workingHours = workingDays * 8;
      
      return {
        waiterName: waiter.name || waiter.username,
        ordersCount: waiterOrders.length,
        totalSales: parseFloat(totalSales.toFixed(2)),
        averageOrderValue: parseFloat(averageOrderValue.toFixed(2)),
        workingHours
      };
    });

    // ترتيب حسب المبيعات
    waiterStats.sort((a, b) => b.totalSales - a.totalSales);

    res.json({
      success: true,
      data: waiterStats
    });
  } catch (error) {
    console.error('خطأ في إحصائيات النُدل:', error);
    res.status(500).json({
      success: false,
      message: 'خطأ في جلب إحصائيات النُدل',
      error: error.message
    });
  }
});

/**
 * @route   GET /api/reports/chef-stats
 * @desc    Get chef statistics
 * @access  Private
 */
router.get('/chef-stats', authenticateToken, async (req, res) => {
  try {
    const { period = '30' } = req.query;
    
    const startDate = new Date(Date.now() - parseInt(period) * 24 * 60 * 60 * 1000);
    const endDate = new Date();
    
    // جلب الطباخين
    const chefs = await User.find({ role: 'chef', isActive: true });
    
    // جلب الطلبات في الفترة المحددة - توحيد جميع حالات الطلبات المكتملة
    const orders = await Order.find({
      'timing.orderTime': { $gte: startDate, $lte: endDate },
      status: { $in: ['served', 'delivered', 'completed'] } // تضمين جميع حالات الإكمال
    }).populate('staff.chef', 'name username');

    const chefStats = chefs.map(chef => {
      const chefOrders = orders.filter(order => 
        order.staff.chef && 
        (order.staff.chef._id.toString() === chef._id.toString() ||
         order.chefName === chef.username ||
         order.chefName === chef.name)
      );
      
      // حساب متوسط وقت التحضير
      const ordersWithPrepTime = chefOrders.filter(order => 
        order.timing.preparingAt && order.timing.readyAt
      );
      
      const totalPrepTime = ordersWithPrepTime.reduce((sum, order) => {
        const prepTime = (new Date(order.timing.readyAt) - new Date(order.timing.preparingAt)) / (1000 * 60); // بالدقائق
        return sum + prepTime;
      }, 0);
      
      const averagePreparationTime = ordersWithPrepTime.length > 0 ? 
        totalPrepTime / ordersWithPrepTime.length : 0;
      
      // تقدير ساعات العمل
      const workingDays = Math.min(parseInt(period), 30);
      const workingHours = workingDays * 8;
      
      return {
        chefName: chef.name || chef.username,
        ordersProcessed: chefOrders.length,
        averagePreparationTime: parseFloat(averagePreparationTime.toFixed(1)),
        workingHours
      };
    });

    // ترتيب حسب عدد الطلبات المُعالجة
    chefStats.sort((a, b) => b.ordersProcessed - a.ordersProcessed);

    res.json({
      success: true,
      data: chefStats
    });
  } catch (error) {
    console.error('خطأ في إحصائيات الطباخين:', error);
    res.status(500).json({
      success: false,
      message: 'خطأ في جلب إحصائيات الطباخين',
      error: error.message
    });
  }
});

/**
 * @route   GET /api/reports/top-items
 * @desc    Get top selling items
 * @access  Private
 */
router.get('/top-items', authenticateToken, async (req, res) => {
  try {
    const { period = '30', limit = '999999' } = req.query; // إزالة قيد الـ 10 عناصر
    
    const startDate = new Date(Date.now() - parseInt(period) * 24 * 60 * 60 * 1000);
    const endDate = new Date();
    
    // جلب الطلبات المكتملة - توحيد جميع حالات الطلبات المكتملة
    const orders = await Order.find({
      'timing.orderTime': { $gte: startDate, $lte: endDate },
      status: { $in: ['served', 'delivered', 'completed'] } // تضمين جميع حالات الإكمال
    }).populate('items.product', 'name price');

    // تجميع الأصناف
    const itemStats = {};
    
    orders.forEach(order => {
      order.items.forEach(item => {
        const itemName = item.product ? item.product.name : item.name || 'غير محدد';
        const itemPrice = item.price || (item.product ? item.product.price : 0);
        
        if (!itemStats[itemName]) {
          itemStats[itemName] = {
            itemName,
            quantitySold: 0,
            totalRevenue: 0
          };
        }
        
        itemStats[itemName].quantitySold += item.quantity;
        itemStats[itemName].totalRevenue += itemPrice * item.quantity;
      });
    });

    // تحويل إلى مصفوفة وترتيب حسب الكمية المباعة
    const result = Object.values(itemStats)
      .sort((a, b) => b.quantitySold - a.quantitySold)
      .slice(0, parseInt(limit))
      .map(item => ({
        ...item,
        totalRevenue: parseFloat(item.totalRevenue.toFixed(2))
      }));

    res.json({
      success: true,
      data: result
    });
  } catch (error) {
    console.error('خطأ في أفضل الأصناف مبيعاً:', error);
    res.status(500).json({
      success: false,
      message: 'خطأ في جلب أفضل الأصناف مبيعاً',
      error: error.message
    });
  }
});

/**
 * @route   GET /api/reports/summary
 * @desc    Get general summary report
 * @access  Private
 */
router.get('/summary', authenticateToken, async (req, res) => {
  try {
    const { period = '30' } = req.query;
    
    const startDate = new Date(Date.now() - parseInt(period) * 24 * 60 * 60 * 1000);
    const endDate = new Date();
    
    // جلب جميع الطلبات في الفترة
    const allOrders = await Order.find({
      'timing.orderTime': { $gte: startDate, $lte: endDate }
    });
    
    // حساب جميع الطلبات بدون فلتر لمنع فقدان أي مبيعات
    const completedOrders = allOrders;
    
    const totalSales = completedOrders.reduce((sum, order) => {
      const orderTotal = order.total || order.items.reduce((itemSum, item) => 
        itemSum + (item.price * item.quantity), 0);
      return sum + orderTotal;
    }, 0);
    
    const averageOrderValue = completedOrders.length > 0 ? totalSales / completedOrders.length : 0;
    
    // إحصائيات اليوم
    const today = new Date();
    today.setHours(0, 0, 0, 0);
    const todayOrders = allOrders.filter(order => 
      new Date(order.timing.orderTime) >= today
    );
    
    const todayCompleted = todayOrders; // جميع طلبات اليوم بدون فلتر
    
    const todaySales = todayCompleted.reduce((sum, order) => {
      const orderTotal = order.total || order.items.reduce((itemSum, item) => 
        itemSum + (item.price * item.quantity), 0);
      return sum + orderTotal;
    }, 0);

    res.json({
      success: true,
      data: {
        period: `آخر ${period} يوم`,
        totalOrders: allOrders.length,
        completedOrders: completedOrders.length,
        pendingOrders: allOrders.filter(o => o.status === 'pending').length,
        preparingOrders: allOrders.filter(o => o.status === 'preparing').length,
        totalSales: parseFloat(totalSales.toFixed(2)),
        averageOrderValue: parseFloat(averageOrderValue.toFixed(2)),
        today: {
          orders: todayOrders.length,
          completed: todayCompleted.length,
          sales: parseFloat(todaySales.toFixed(2))
        }
      }
    });
  } catch (error) {
    console.error('خطأ في تقرير الملخص:', error);
    res.status(500).json({      success: false,
      message: 'خطأ في جلب تقرير الملخص',
      error: error.message
    });
  }
});

/**
 * @route   GET /api/reports/dashboard
 * @desc    Get dashboard overview statistics
 * @access  Private
 */
router.get('/dashboard', authenticateToken, async (req, res) => {
  try {
    const { period = 'today' } = req.query;
    
    // تحديد النطاق الزمني
    let startDate, endDate = new Date();
    
    switch (period) {
      case 'today':
        startDate = new Date();
        startDate.setHours(0, 0, 0, 0);
        break;
      case 'week':
        startDate = new Date(Date.now() - 7 * 24 * 60 * 60 * 1000);
        break;
      case 'month':
        startDate = new Date(Date.now() - 30 * 24 * 60 * 60 * 1000);
        break;
      default:
        startDate = new Date();
        startDate.setHours(0, 0, 0, 0);
    }

    console.log('📊 Dashboard report query:', {
      period,
      dateRange: { startDate, endDate }
    });

    // جلب جميع الطلبات في الفترة المحددة
    const allOrders = await Order.find({
      'timing.orderTime': { $gte: startDate, $lte: endDate }
    }).populate('staff.waiter', 'name username')
      .populate('items.product', 'name price');

    // جلب الطلبات المكتملة فقط للمبيعات
    const completedOrders = allOrders.filter(order => 
      ['served', 'delivered'].includes(order.status)
    );

    // حساب الإحصائيات
    const totalOrders = allOrders.length;
    const completedOrdersCount = completedOrders.length;
    const pendingOrders = allOrders.filter(order => order.status === 'pending').length;
    const preparingOrders = allOrders.filter(order => order.status === 'preparing').length;
    const readyOrders = allOrders.filter(order => order.status === 'ready').length;

    // حساب إجمالي المبيعات
    const totalRevenue = completedOrders.reduce((sum, order) => {
      const orderTotal = order.pricing?.total || order.totals?.total || 
        order.items.reduce((itemSum, item) => itemSum + (item.price * item.quantity), 0);
      return sum + orderTotal;
    }, 0);

    // حساب متوسط قيمة الطلب
    const averageOrderValue = completedOrdersCount > 0 ? totalRevenue / completedOrdersCount : 0;

    // إحصائيات المنتجات
    const itemStats = {};
    completedOrders.forEach(order => {
      order.items.forEach(item => {
        const productName = item.product?.name || item.productName || 'Unknown';
        if (!itemStats[productName]) {
          itemStats[productName] = 0;
        }
        itemStats[productName] += item.quantity;
      });
    });

    // جميع المنتجات الشائعة (إزالة قيد الـ 5)
    const topProducts = Object.entries(itemStats)
      .sort(([,a], [,b]) => b - a)
      .map(([name, quantity]) => ({ name, quantity })); // عرض جميع المنتجات

    // إحصائيات النداء
    const waiterStats = {};
    allOrders.forEach(order => {
      const waiterName = order.staff?.waiter?.name || order.staff?.waiter?.username || 'غير محدد';
      if (!waiterStats[waiterName]) {
        waiterStats[waiterName] = {
          totalOrders: 0,
          completedOrders: 0,
          revenue: 0
        };
      }
      waiterStats[waiterName].totalOrders += 1;
      if (['served', 'delivered'].includes(order.status)) {
        waiterStats[waiterName].completedOrders += 1;
        const orderTotal = order.pricing?.total || order.totals?.total || 
          order.items.reduce((sum, item) => sum + (item.price * item.quantity), 0);
        waiterStats[waiterName].revenue += orderTotal;
      }
    });

    // جلب إحصائيات الطاولات
    const Table = require('../models/Table');
    const tables = await Table.find({ isActive: true });
    const occupiedTables = tables.filter(table => table.status === 'occupied').length;
    const availableTables = tables.filter(table => table.status === 'available').length;

    const dashboardData = {
      orders: {
        total: totalOrders,
        completed: completedOrdersCount,
        pending: pendingOrders,
        preparing: preparingOrders,
        ready: readyOrders
      },
      revenue: {
        total: Math.round(totalRevenue * 100) / 100,
        average: Math.round(averageOrderValue * 100) / 100,
        currency: 'EGP'
      },
      tables: {
        total: tables.length,
        occupied: occupiedTables,
        available: availableTables,
        occupancyRate: tables.length > 0 ? Math.round((occupiedTables / tables.length) * 100) : 0
      },
      topProducts: topProducts,
      waiters: Object.entries(waiterStats).map(([name, stats]) => ({
        name,
        ...stats,
        revenue: Math.round(stats.revenue * 100) / 100
      })),
      period: {
        type: period,
        startDate: startDate.toISOString(),
        endDate: endDate.toISOString()
      }
    };

    console.log('📊 Dashboard data prepared:', {
      totalOrders,
      totalRevenue,
      period,
      topProductsCount: topProducts.length,
      waitersCount: Object.keys(waiterStats).length
    });

    res.json({
      success: true,
      message: 'تم جلب إحصائيات لوحة التحكم بنجاح',
      data: dashboardData
    });

  } catch (error) {
    console.error('خطأ في تقرير لوحة التحكم:', error);
    res.status(500).json({
      success: false,
      message: 'خطأ في جلب إحصائيات لوحة التحكم',
      error: error.message
    });
  }
});

/**
 * @route   GET /api/reports/detailed
 * @desc    Get detailed reports based on period filter
 * @access  Private
 */
router.get('/detailed', authenticateToken, async (req, res) => {
  try {
    const { period = 'today' } = req.query;
    
    // تحديد نطاق التاريخ حسب الفترة المطلوبة
    const now = new Date();
    let startDate, endDate = new Date();
    
    switch (period) {
      case 'today':
        startDate = new Date(now.getFullYear(), now.getMonth(), now.getDate());
        break;
      case 'week':
        startDate = new Date(now);
        startDate.setDate(now.getDate() - now.getDay()); // بداية الأسبوع
        startDate.setHours(0, 0, 0, 0);
        break;
      case 'month':
        startDate = new Date(now.getFullYear(), now.getMonth(), 1);
        break;
      case 'all':
      default:
        startDate = new Date(0); // بداية الزمن للحصول على جميع البيانات
    }

    // جلب الطلبات في النطاق المحدد
    const orders = await Order.find({
      'timing.orderTime': { $gte: startDate, $lte: endDate }
    }).populate('items.product', 'name price category')
      .populate('staff.waiter', 'name')
      .populate('table', 'number');

    // حساب الإحصائيات التفصيلية
    const totalRevenue = orders.reduce((sum, order) => {
      return sum + (order.pricing?.total || order.totals?.total || 0);
    }, 0);

    const totalOrders = orders.length;
    const avgOrderValue = totalOrders > 0 ? totalRevenue / totalOrders : 0;

    // إحصائيات حسب الحالة
    const ordersByStatus = {
      pending: orders.filter(o => o.status === 'pending').length,
      preparing: orders.filter(o => o.status === 'preparing').length,
      ready: orders.filter(o => o.status === 'ready').length,
      served: orders.filter(o => o.status === 'served').length,
      completed: orders.filter(o => o.status === 'completed').length,
      cancelled: orders.filter(o => o.status === 'cancelled').length
    };

    // إحصائيات ساعية
    const hourlyStats = {};
    for (let i = 0; i < 24; i++) {
      hourlyStats[i] = 0;
    }
    
    orders.forEach(order => {
      const hour = new Date(order.timing.orderTime).getHours();
      hourlyStats[hour] += 1;
    });

    // أفضل المنتجات
    const productStats = {};
    orders.forEach(order => {
      order.items.forEach(item => {
        const productName = item.product?.name || 'غير محدد';
        if (!productStats[productName]) {
          productStats[productName] = {
            name: productName,
            quantity: 0,
            revenue: 0
          };
        }
        productStats[productName].quantity += item.quantity;
        productStats[productName].revenue += item.quantity * (item.product?.price || 0);
      });
    });

    const topProducts = Object.values(productStats)
      .sort((a, b) => b.quantity - a.quantity)
      .slice(0, 5);

    // أداء النوادل
    const waiterStats = {};
    orders.forEach(order => {
      const waiterName = order.staff?.waiter?.name || order.waiterName || 'غير محدد';
      if (!waiterStats[waiterName]) {
        waiterStats[waiterName] = {
          name: waiterName,
          orders: 0,
          revenue: 0
        };
      }
      waiterStats[waiterName].orders += 1;
      waiterStats[waiterName].revenue += (order.pricing?.total || order.totals?.total || 0);
    });

    const waiterPerformance = Object.values(waiterStats)
      .sort((a, b) => b.revenue - a.revenue)
      .slice(0, 5);

    res.json({
      success: true,
      data: {
        period,
        summary: {
          totalRevenue,
          totalOrders,
          avgOrderValue,
          ordersByStatus
        },
        hourlyStats: Object.entries(hourlyStats).map(([hour, count]) => ({
          hour: parseInt(hour),
          count
        })),
        topProducts,
        waiterPerformance,
        dateRange: {
          startDate,
          endDate
        }
      }
    });

  } catch (error) {
    console.error('خطأ في التقرير التفصيلي:', error);
    res.status(500).json({
      success: false,
      message: 'خطأ في جلب التقرير التفصيلي',
      error: error.message
    });
  }
});

module.exports = router;

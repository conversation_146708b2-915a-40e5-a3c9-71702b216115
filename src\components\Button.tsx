import type { ReactNode } from 'react';
import './Button.css';

interface ButtonProps {
  children: ReactNode;
  variant?: 'primary' | 'secondary' | 'accent' | 'success' | 'warning' | 'error' | 'info';
  size?: 'sm' | 'md' | 'lg';
  icon?: string;
  iconPosition?: 'start' | 'end';
  fullWidth?: boolean;
  disabled?: boolean;
  loading?: boolean;
  onClick?: () => void;
  type?: 'button' | 'submit' | 'reset';
  className?: string;
  style?: React.CSSProperties;
}

const Button: React.FC<ButtonProps> = ({
  children,
  variant = 'primary',
  size = 'md',
  icon,
  iconPosition = 'start',
  fullWidth = false,
  disabled = false,
  loading = false,
  onClick,
  type = 'button',
  className = ''
}) => {
  return (
    <button
      type={type}
      className={`
        btn 
        btn-${variant} 
        btn-${size} 
        ${fullWidth ? 'btn-full-width' : ''} 
        ${loading ? 'btn-loading' : ''} 
        ${className}
      `}
      disabled={disabled || loading}
      onClick={onClick}
    >
      {loading && <span className="btn-spinner"></span>}
      
      {icon && iconPosition === 'start' && !loading && (
        <i className={`btn-icon ${icon}`}></i>
      )}
      
      <span className="btn-text">{children}</span>
      
      {icon && iconPosition === 'end' && !loading && (
        <i className={`btn-icon ${icon}`}></i>
      )}
    </button>
  );
};

export default Button;

// ملف socket.ts: تهيئة اتصال socket.io للواجهة الأمامية
import io from 'socket.io-client';
import { getSocketUrl, APP_CONFIG } from './config/app.config';

// إنشاء اتصال Socket.IO مع معالجة الأخطاء
let socket: any = null;

try {
  // التحقق من توفر io function
  if (typeof io === 'function') {
    const socketUrl = getSocketUrl();
    console.log('🔗 محاولة الاتصال بـ Socket.IO:', socketUrl);

    socket = io(socketUrl, {
      ...APP_CONFIG.SOCKET.OPTIONS,
      autoConnect: true,
      forceNew: false,
      // إعدادات إضافية لتحسين الاتصال
      upgrade: true,
      rememberUpgrade: true,
      // تجربة polling أولاً ثم websocket
      transports: ['polling', 'websocket'],
      // زيادة timeout للاتصال الأولي
      timeout: 30000,
      // إعدادات إعادة الاتصال
      reconnection: true,
      reconnectionAttempts: 10,
      reconnectionDelay: 2000,
      reconnectionDelayMax: 10000,
      maxReconnectionAttempts: 10,
      // إعدادات إضافية
      randomizationFactor: 0.5,
      forceBase64: false
    });

    // إضافة event listeners للتشخيص
    socket.on('connect', () => {
      console.log('✅ Socket.IO متصل بنجاح!');
    });

    socket.on('disconnect', (reason: string) => {
      console.log('❌ Socket.IO منقطع:', reason);
    });

    socket.on('connect_error', (error: any) => {
      console.error('❌ خطأ في اتصال Socket.IO:', error);
    });

    socket.on('reconnect', (attemptNumber: number) => {
      console.log('🔄 Socket.IO أعيد الاتصال، المحاولة:', attemptNumber);
    });

    socket.on('reconnect_error', (error: any) => {
      console.error('❌ خطأ في إعادة اتصال Socket.IO:', error);
    });

    console.log('🔗 Socket.IO initialized successfully');
  } else {
    throw new Error('io function not available');
  }
} catch (error) {
  console.error('❌ Failed to initialize Socket.IO:', error);
  console.warn('⚠️ Creating mock socket for fallback');

  // إنشاء mock socket للتجنب الأخطاء
  socket = {
    on: (event: string, callback: Function) => {
      console.log(`Mock socket: listening for ${event}`);
      // محاكاة الاتصال بعد 3 ثواني
      if (event === 'connect') {
        setTimeout(() => {
          console.log('Mock socket: simulating connection');
          callback();
        }, 3000);
      }
    },
    off: (event: string) => {
      console.log(`Mock socket: removing listener for ${event}`);
    },
    emit: (event: string, data?: any) => {
      console.log(`Mock socket: emitting ${event}`, data);
    },
    connect: () => {
      console.log('Mock socket: connect called');
    },
    disconnect: () => {
      console.log('Mock socket: disconnect called');
    },
    connected: false
  };
}

export default socket;

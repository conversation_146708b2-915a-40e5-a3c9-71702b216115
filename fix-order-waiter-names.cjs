const mongoose = require('mongoose');

// اتصال بقاعدة البيانات (MongoDB Atlas Production)
const mongoUri = 'mongodb+srv://besomustafa:<EMAIL>/deshacoffee?retryWrites=true&w=majority&appName=MyCoffeChop';

mongoose.connect(mongoUri, {
  serverSelectionTimeoutMS: 10000,
  socketTimeoutMS: 45000,
  family: 4
});

// تعريف النماذج
const orderSchema = new mongoose.Schema({
  // ... باقي الحقول
  waiterName: String,
  waiterId: { type: mongoose.Schema.Types.ObjectId, ref: 'User' },
  staff: {
    waiter: { type: mongoose.Schema.Types.ObjectId, ref: 'User' }
  }
}, { strict: false });

const userSchema = new mongoose.Schema({
  name: String,
  username: String,
  // ... باقي الحقول
}, { strict: false });

const Order = mongoose.model('Order', orderSchema);
const User = mongoose.model('User', userSchema);

async function fixOrderWaiterNames() {
  try {
    console.log('🔧 بدء تصحيح أسماء النادلين في الطلبات...');

    // جلب جميع الطلبات التي قد تحتوي على معرفات بدلاً من أسماء
    const orders = await Order.find({
      $or: [
        { waiterName: { $regex: /^[0-9a-fA-F]{24}$/ } }, // معرف MongoDB
        { waiterName: { $regex: /_/ } }, // يحتوي على underscore (اسم مستخدم)
        { waiterName: { $not: { $regex: / / } } }, // لا يحتوي على مسافة (قد يكون اسم مستخدم)
        { waiterName: null },
        { waiterName: '' }
      ]
    }).populate('staff.waiter', 'name username').populate('waiterId', 'name username');

    console.log(`📊 تم العثور على ${orders.length} طلب يحتاج إلى تصحيح`);

    let updatedCount = 0;
    let errors = 0;

    for (const order of orders) {
      try {
        let newWaiterName = null;

        // محاولة الحصول على الاسم من staff.waiter أولاً
        if (order.staff?.waiter?.name) {
          newWaiterName = order.staff.waiter.name;
        } else if (order.waiterId?.name) {
          // محاولة الحصول على الاسم من waiterId
          newWaiterName = order.waiterId.name;
        } else if (order.waiterName && order.waiterName.length === 24) {
          // إذا كان waiterName يبدو كمعرف، حاول جلب المستخدم
          const user = await User.findById(order.waiterName);
          if (user) {
            newWaiterName = user.name;
          }
        } else if (order.waiterName && order.waiterName.includes('_')) {
          // إذا كان waiterName يبدو كاسم مستخدم
          const user = await User.findOne({ username: order.waiterName });
          if (user) {
            newWaiterName = user.name;
          }
        }

        // تحديث الطلب إذا تم العثور على اسم صحيح
        if (newWaiterName && newWaiterName !== order.waiterName) {
          await Order.findByIdAndUpdate(order._id, {
            waiterName: newWaiterName
          });
          
          console.log(`✅ تم تحديث الطلب ${order.orderNumber || order._id}:`);
          console.log(`   من: "${order.waiterName}" إلى: "${newWaiterName}"`);
          updatedCount++;
        }

      } catch (error) {
        console.error(`❌ خطأ في تحديث الطلب ${order._id}:`, error.message);
        errors++;
      }
    }

    console.log('\n📈 نتائج التصحيح:');
    console.log(`✅ تم تحديث: ${updatedCount} طلب`);
    console.log(`❌ أخطاء: ${errors} طلب`);
    console.log(`📊 المجموع: ${orders.length} طلب تم فحصه`);

  } catch (error) {
    console.error('❌ خطأ عام:', error);
  } finally {
    await mongoose.disconnect();
    console.log('📝 تم قطع الاتصال بقاعدة البيانات');
  }
}

// تشغيل الدالة
fixOrderWaiterNames().catch(console.error);

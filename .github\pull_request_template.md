## 📋 وصف التغييرات

وصف مختصر للتغييرات التي تم إجراؤها في هذا PR.

## 🔗 Issues ذات الصلة

- Fixes #(رقم issue)
- Closes #(رقم issue)
- Related to #(رقم issue)

## 🎯 نوع التغيير

- [ ] 🐛 إصلاح خطأ (تغيير لا يكسر الوظائف الموجودة ويصلح مشكلة)
- [ ] ✨ ميزة جديدة (تغيير لا يكسر الوظائف الموجودة ويضيف وظيفة جديدة)
- [ ] 💥 تغيير كبير (إصلاح أو ميزة تؤثر على الوظائف الموجودة)
- [ ] 📚 تحديث التوثيق
- [ ] 🎨 تحسين الكود (تنسيق، إعادة هيكلة، إلخ)
- [ ] ⚡ تحسين الأداء
- [ ] 🧪 إضافة اختبارات
- [ ] 🔧 تحديث أدوات البناء أو التبعيات

## 🧪 كيف تم اختبار التغييرات؟

وصف الاختبارات التي أجريتها للتحقق من تغييراتك.

- [ ] اختبار وحدة
- [ ] اختبار تكامل
- [ ] اختبار يدوي
- [ ] اختبار على متصفحات مختلفة
- [ ] اختبار على أجهزة مختلفة

### 🖥️ بيئة الاختبار
- OS: 
- Browser: 
- Node.js version: 
- Database: 

## 📸 لقطات الشاشة (إذا كان مناسباً)

أضف لقطات شاشة للتغييرات البصرية.

| قبل | بعد |
|-----|-----|
| ![قبل](url) | ![بعد](url) |

## ✅ قائمة التحقق

### 📝 عام
- [ ] الكود يتبع معايير المشروع
- [ ] أجريت مراجعة ذاتية للكود
- [ ] علقت على الأجزاء المعقدة من الكود
- [ ] أضفت اختبارات للتغييرات الجديدة
- [ ] جميع الاختبارات الجديدة والموجودة تمر
- [ ] لا توجد تحذيرات من linter

### 📚 التوثيق
- [ ] حدثت التوثيق المناسب
- [ ] أضفت تعليقات للكود الجديد
- [ ] حدثت CHANGELOG.md إذا لزم الأمر

### 🔒 الأمان
- [ ] لا توجد معلومات حساسة في الكود
- [ ] تم التحقق من صحة المدخلات
- [ ] لا توجد ثغرات أمنية جديدة

### 🎨 Frontend (إذا كان مناسباً)
- [ ] التصميم متجاوب
- [ ] يعمل على متصفحات مختلفة
- [ ] لا توجد أخطاء في console
- [ ] تم تحسين الأداء

### 🖥️ Backend (إذا كان مناسباً)
- [ ] APIs موثقة
- [ ] تم التعامل مع الأخطاء بشكل صحيح
- [ ] تم التحقق من الصلاحيات
- [ ] تم تحسين استعلامات قاعدة البيانات

## 📝 ملاحظات للمراجعين

أي معلومات إضافية تريد أن يعرفها المراجعون.

## 🚀 خطوات النشر

إذا كانت هناك خطوات خاصة مطلوبة للنشر:

1. 
2. 
3. 

## 📊 تأثير الأداء

- [ ] لا يوجد تأثير على الأداء
- [ ] تحسين في الأداء
- [ ] تأثير سلبي طفيف (مبرر)
- [ ] تأثير سلبي كبير (يحتاج مناقشة)

## 🔄 Breaking Changes

إذا كان هذا PR يحتوي على breaking changes، يرجى وصفها:

- 
- 
- 

## 📞 جهات الاتصال

- المطور: @username
- المراجع المطلوب: @username
- فريق التصميم: @team (إذا لزم الأمر)

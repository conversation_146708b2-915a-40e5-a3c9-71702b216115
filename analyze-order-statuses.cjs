const mongoose = require('mongoose');

async function analyzeOrderStatuses() {
  try {
    console.log('🔗 الاتصال بقاعدة البيانات...');
    
    const mongoUri = 'mongodb+srv://besomustafa:<EMAIL>/deshacoffee?retryWrites=true&w=majority&appName=MyCoffeChop';
    
    await mongoose.connect(mongoUri, {
      serverSelectionTimeoutMS: 5000,
      connectTimeoutMS: 10000,
    });
    
    console.log('✅ تم الاتصال بنجاح');
    
    console.log('\n' + '='.repeat(80));
    console.log('📊 تحليل حالات الطلبات وتأثيرها على حساب المبيعات');
    console.log('='.repeat(80));
    
    // جلب جميع الطلبات
    const orders = await mongoose.connection.db.collection('orders').find({}).toArray();
    console.log(`\n📋 إجمالي الطلبات: ${orders.length}`);
    
    // تحليل الحالات
    const statusAnalysis = {};
    let totalSalesAllOrders = 0;
    let totalSalesCompletedOnly = 0;
    let totalSalesReadyIncluded = 0;
    
    orders.forEach(order => {
      const status = order.status || 'غير محدد';
      const amount = order.totals?.total || order.totalPrice || order.totalAmount || order.pricing?.total || 0;
      
      if (!statusAnalysis[status]) {
        statusAnalysis[status] = {
          count: 0,
          totalAmount: 0,
          avgAmount: 0,
          examples: []
        };
      }
      
      statusAnalysis[status].count++;
      statusAnalysis[status].totalAmount += amount;
      
      if (statusAnalysis[status].examples.length < 3) {
        statusAnalysis[status].examples.push({
          orderNumber: order.orderNumber,
          amount: amount,
          createdAt: order.createdAt
        });
      }
      
      // حساب المبيعات بطرق مختلفة
      totalSalesAllOrders += amount;
      
      // الطريقة الحالية: completed, delivered, served فقط
      if (status === 'completed' || status === 'delivered' || status === 'served') {
        totalSalesCompletedOnly += amount;
      }
      
      // طريقة بديلة: شامل ready أيضاً
      if (status === 'completed' || status === 'delivered' || status === 'served' || status === 'ready') {
        totalSalesReadyIncluded += amount;
      }
    });
    
    // حساب المتوسطات
    Object.keys(statusAnalysis).forEach(status => {
      statusAnalysis[status].avgAmount = statusAnalysis[status].totalAmount / statusAnalysis[status].count;
    });
    
    console.log('\n📊 تحليل تفصيلي لحالات الطلبات:');
    console.log('=' + '='.repeat(60));
    
    Object.entries(statusAnalysis)
      .sort((a, b) => b[1].count - a[1].count)
      .forEach(([status, data]) => {
        const percentage = ((data.count / orders.length) * 100).toFixed(1);
        console.log(`\n🔸 حالة "${status}":`);
        console.log(`   عدد الطلبات: ${data.count} (${percentage}%)`);
        console.log(`   إجمالي المبيعات: ${data.totalAmount.toFixed(2)} جنيه`);
        console.log(`   متوسط قيمة الطلب: ${data.avgAmount.toFixed(2)} جنيه`);
        console.log(`   عينة من الطلبات:`);
        data.examples.forEach((example, index) => {
          console.log(`     ${index + 1}. ${example.orderNumber}: ${example.amount} جنيه`);
        });
      });
    
    console.log('\n' + '='.repeat(80));
    console.log('💰 مقارنة طرق حساب المبيعات');
    console.log('='.repeat(80));
    
    console.log(`\n1️⃣ حساب جميع الطلبات (بدون فلتر):`);
    console.log(`   إجمالي المبيعات: ${totalSalesAllOrders.toFixed(2)} جنيه`);
    console.log(`   النسبة: 100%`);
    
    console.log(`\n2️⃣ الطريقة الحالية (completed, delivered, served فقط):`);
    console.log(`   إجمالي المبيعات: ${totalSalesCompletedOnly.toFixed(2)} جنيه`);
    console.log(`   النسبة: ${((totalSalesCompletedOnly / totalSalesAllOrders) * 100).toFixed(1)}%`);
    console.log(`   المبيعات المفقودة: ${(totalSalesAllOrders - totalSalesCompletedOnly).toFixed(2)} جنيه`);
    
    console.log(`\n3️⃣ طريقة بديلة (+ ready):`);
    console.log(`   إجمالي المبيعات: ${totalSalesReadyIncluded.toFixed(2)} جنيه`);
    console.log(`   النسبة: ${((totalSalesReadyIncluded / totalSalesAllOrders) * 100).toFixed(1)}%`);
    console.log(`   المبيعات المفقودة: ${(totalSalesAllOrders - totalSalesReadyIncluded).toFixed(2)} جنيه`);
    
    console.log('\n' + '='.repeat(80));
    console.log('🎯 التحليل والتوصيات');
    console.log('='.repeat(80));
    
    const readyOrders = statusAnalysis['ready'] || { count: 0, totalAmount: 0 };
    const pendingOrders = statusAnalysis['pending'] || { count: 0, totalAmount: 0 };
    const preparingOrders = statusAnalysis['preparing'] || { count: 0, totalAmount: 0 };
    
    console.log(`\n🔍 الطلبات التي قد تفقد من المبيعات:`);
    if (readyOrders.count > 0) {
      console.log(`   📦 طلبات "ready": ${readyOrders.count} طلب بقيمة ${readyOrders.totalAmount.toFixed(2)} جنيه`);
      console.log(`      ➡️ هذه طلبات جاهزة للتسليم - يجب احتسابها في المبيعات`);
    }
    
    if (pendingOrders.count > 0) {
      console.log(`   ⏳ طلبات "pending": ${pendingOrders.count} طلب بقيمة ${pendingOrders.totalAmount.toFixed(2)} جنيه`);
      console.log(`      ➡️ طلبات في انتظار المعالجة - لا يجب احتسابها حتى تكتمل`);
    }
    
    if (preparingOrders.count > 0) {
      console.log(`   👨‍🍳 طلبات "preparing": ${preparingOrders.count} طلب بقيمة ${preparingOrders.totalAmount.toFixed(2)} جنيه`);
      console.log(`      ➡️ طلبات قيد التحضير - قد نحتاج احتسابها حسب سياسة العمل`);
    }
    
    console.log(`\n💡 التوصيات:`);
    
    if (readyOrders.count > 0) {
      console.log(`\n🟢 يُنصح بشدة بإضافة حالة "ready" للفلتر:`);
      console.log(`   const completedStatuses = ['served', 'delivered', 'completed', 'ready'];`);
      console.log(`   السبب: الطلبات الجاهزة تم دفع ثمنها وهي جزء من المبيعات الفعلية`);
      console.log(`   المكسب: ${readyOrders.totalAmount.toFixed(2)} جنيه إضافية في المبيعات`);
    }
    
    if (pendingOrders.count > 0 || preparingOrders.count > 0) {
      console.log(`\n🟡 بالنسبة للطلبات قيد المعالجة:`);
      console.log(`   - يُفضل عدم احتسابها حتى تصل لحالة "ready" على الأقل`);
      console.log(`   - يمكن إنشاء تقرير منفصل للطلبات قيد المعالجة`);
    }
    
    console.log(`\n🎯 الحل الأمثل:`);
    console.log(`   استخدام فلتر محدد بدلاً من "جميع الطلبات" لضمان دقة المبيعات:`);
    console.log(`   \`\`\`javascript`);
    console.log(`   const COMPLETED_STATUSES = ['served', 'delivered', 'completed', 'ready'];`);
    console.log(`   const completedOrders = orders.filter(order => `);
    console.log(`     COMPLETED_STATUSES.includes(order.status)`);
    console.log(`   );`);
    console.log(`   \`\`\``);
    
    if (readyOrders.count === 0 && pendingOrders.count === 0 && preparingOrders.count === 0) {
      console.log(`\n✅ جميع الطلبات في حالات مكتملة - الفلتر الحالي مناسب`);
    }
    
  } catch (error) {
    console.error('❌ خطأ في التحليل:', error.message);
  } finally {
    await mongoose.disconnect();
    console.log('\n🔌 تم قطع الاتصال بقاعدة البيانات');
  }
}

analyzeOrderStatuses();

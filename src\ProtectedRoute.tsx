import { Navigate } from 'react-router-dom';
import React from 'react';

interface ProtectedRouteProps {
  allowedRoles: string[];
  children: React.ReactNode;
}

export default function ProtectedRoute({ allowedRoles, children }: ProtectedRouteProps) {
  const user = JSON.parse(localStorage.getItem('user') || 'null');

  console.log('ProtectedRoute - User from localStorage:', user ? JSON.stringify(user) : 'null');
  console.log('ProtectedRoute - Required roles:', allowedRoles);

  if (!user) {
    console.log('ProtectedRoute - No user found, redirecting to login');
    return <Navigate to="/" replace />;
  }

  // التحقق من الدور - دعم الأدوار بالعربية والإنجليزية
  const userRole = user.role;

  // تطبيع الدور إذا كان بالعربية (للتوافق مع البيانات القديمة)
  const normalizeRole = (role: string): string => {
    const roleMapping: Record<string, string> = {
      'مدير': 'manager',
      'نادل': 'waiter',
      'طباخ': 'chef',
      'manager': 'manager',
      'waiter': 'waiter',
      'chef': 'chef',
      'admin': 'manager'
    };
    return roleMapping[role] || role;
  };

  const normalizedUserRole = normalizeRole(userRole);
  const hasAccess = allowedRoles.includes(normalizedUserRole);

  if (!hasAccess) {
    console.log('Access denied - User Role:', userRole);
    console.log('Access denied - Normalized Role:', normalizedUserRole);
    console.log('Access denied - Allowed Roles:', allowedRoles);
    console.log('Access denied - User Object:', JSON.stringify(user));
    return <Navigate to="/" replace />;
  }

  return <>{children}</>;
}

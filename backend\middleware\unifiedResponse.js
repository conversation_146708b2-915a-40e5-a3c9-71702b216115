// Unified Response Middleware
// ميدلوير موحد للاستجابات والأخطاء

/**
 * Unified success response
 */
const sendSuccess = (res, data, message = 'نجح', statusCode = 200) => {
  return res.status(statusCode).json({
    success: true,
    message: message,
    data: data,
    timestamp: new Date().toISOString()
  });
};

/**
 * Unified error response
 */
const sendError = (res, message, statusCode = 500, errorCode = null, details = null) => {
  const response = {
    success: false,
    message: message,
    timestamp: new Date().toISOString()
  };

  if (errorCode) {
    response.error = errorCode;
  }

  if (details && process.env.NODE_ENV === 'development') {
    response.details = details;
  }

  console.error(`❌ API Error [${statusCode}]:`, {
    message,
    errorCode,
    details: details?.message || details
  });

  return res.status(statusCode).json(response);
};

/**
 * Unified validation error
 */
const sendValidationError = (res, field, value, expectedType = null) => {
  const message = `خطأ في ${field}: القيمة غير صحيحة`;
  const details = {
    field,
    receivedValue: value,
    receivedType: typeof value,
    expectedType: expectedType
  };

  console.error('❌ Validation Error:', details);

  return res.status(400).json({
    success: false,
    message: message,
    error: 'VALIDATION_ERROR',
    details: details,
    timestamp: new Date().toISOString()
  });
};

/**
 * Unified not found error
 */
const sendNotFound = (res, resource = 'العنصر') => {
  return res.status(404).json({
    success: false,
    message: `${resource} غير موجود`,
    error: 'NOT_FOUND',
    timestamp: new Date().toISOString()
  });
};

/**
 * Unified database error handler
 */
const handleDatabaseError = (res, error, operation = 'العملية') => {
  console.error(`❌ Database Error in ${operation}:`, {
    name: error.name,
    message: error.message,
    code: error.code,
    stack: process.env.NODE_ENV === 'development' ? error.stack : undefined
  });

  // MongoDB Validation Error
  if (error.name === 'ValidationError') {
    const validationErrors = Object.values(error.errors).map(err => ({
      field: err.path,
      message: err.message,
      value: err.value
    }));

    return res.status(400).json({
      success: false,
      message: 'خطأ في التحقق من البيانات',
      error: 'VALIDATION_ERROR',
      details: validationErrors,
      timestamp: new Date().toISOString()
    });
  }

  // MongoDB Duplicate Key Error
  if (error.code === 11000) {
    const field = Object.keys(error.keyValue)[0];
    const value = error.keyValue[field];

    return res.status(400).json({
      success: false,
      message: `${field} موجود بالفعل: ${value}`,
      error: 'DUPLICATE_ENTRY',
      details: { field, value },
      timestamp: new Date().toISOString()
    });
  }

  // MongoDB Cast Error (Invalid ObjectId)
  if (error.name === 'CastError') {
    return res.status(400).json({
      success: false,
      message: 'معرف غير صحيح',
      error: 'INVALID_ID',
      details: { field: error.path, value: error.value },
      timestamp: new Date().toISOString()
    });
  }

  // Generic server error
  return res.status(500).json({
    success: false,
    message: `خطأ في ${operation}`,
    error: 'SERVER_ERROR',
    details: process.env.NODE_ENV === 'development' ? error.message : undefined,
    timestamp: new Date().toISOString()
  });
};

/**
 * Mobile-friendly device detection middleware
 */
const deviceDetection = (req, res, next) => {
  const userAgent = req.headers['user-agent'] || '';
  const origin = req.headers.origin || req.headers.referer || 'Unknown';
  
  // Detect device type
  const isMobile = /Mobile|Android|iPhone|iPad|iPod|BlackBerry|Opera Mini|IEMobile/i.test(userAgent);
  const isTablet = /iPad|Android(?!.*Mobile)/i.test(userAgent);
  const isDesktop = !isMobile && !isTablet;
  
  // Add device info to request
  req.deviceInfo = {
    isMobile,
    isTablet,
    isDesktop,
    userAgent,
    origin,
    type: isMobile ? 'mobile' : isTablet ? 'tablet' : 'desktop'
  };

  // Log mobile requests for debugging
  if (isMobile) {
    console.log(`📱 Mobile Request [${req.method}] ${req.path}:`, {
      userAgent: userAgent.substring(0, 100) + (userAgent.length > 100 ? '...' : ''),
      origin,
      contentType: req.headers['content-type'],
      hasAuth: !!req.headers.authorization
    });
  }

  next();
};

/**
 * Unified CORS headers for all routes
 */
const setCorsHeaders = (req, res, next) => {
  // Set CORS headers for all devices
  res.header('Access-Control-Allow-Origin', '*');
  res.header('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE, OPTIONS, PATCH');
  res.header('Access-Control-Allow-Headers', 'Origin, X-Requested-With, Content-Type, Accept, Authorization, Cache-Control');
  res.header('Access-Control-Allow-Credentials', 'true');
  res.header('Access-Control-Max-Age', '86400'); // 24 hours

  // Handle preflight requests
  if (req.method === 'OPTIONS') {
    return res.status(200).end();
  }

  next();
};

/**
 * Request logging middleware
 */
const requestLogger = (req, res, next) => {
  const startTime = Date.now();
  const { method, path, ip } = req;
  const userAgent = req.headers['user-agent'] || 'Unknown';
  
  console.log(`🌐 [${method}] ${path} from ${ip} (${req.deviceInfo?.type || 'unknown'})`);

  // Log response time
  res.on('finish', () => {
    const duration = Date.now() - startTime;
    const status = res.statusCode;
    const statusIcon = status < 400 ? '✅' : status < 500 ? '⚠️' : '❌';
    
    console.log(`${statusIcon} [${method}] ${path} - ${status} (${duration}ms)`);
  });

  next();
};

module.exports = {
  sendSuccess,
  sendError,
  sendValidationError,
  sendNotFound,
  handleDatabaseError,
  deviceDetection,
  setCorsHeaders,
  requestLogger
};

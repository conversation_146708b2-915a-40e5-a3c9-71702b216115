const mongoose = require('mongoose');
require('dotenv').config();

async function examineCurrentOrders() {
  try {
    console.log('🔌 Connecting to MongoDB Atlas...');
    await mongoose.connect(process.env.MONGODB_URI);
    console.log('✅ Connected successfully!');

    const ordersCollection = mongoose.connection.db.collection('orders');
    const totalOrders = await ordersCollection.countDocuments();
    console.log(`\n📦 Total Orders: ${totalOrders}`);

    if (totalOrders > 0) {
      console.log('\n📋 Examining current order structure:');
      const sampleOrders = await ordersCollection.find().limit(5).toArray();
      
      sampleOrders.forEach((order, index) => {
        console.log(`\n--- Order ${index + 1} ---`);
        console.log(`Order Number: ${order.orderNumber || 'undefined'}`);
        console.log(`Waiter Name: ${order.waiterName || 'undefined'}`);
        console.log(`Table Number: ${order.tableNumber || 'undefined'}`);
        console.log(`Total Amount: ${order.totalAmount || 'undefined'}`);
        console.log(`Status: ${order.status || 'undefined'}`);
        console.log(`Items:`, order.items ? order.items.length : 'undefined');
        if (order.items && order.items.length > 0) {
          console.log(`First item:`, order.items[0]);
        }
        console.log(`Order Date: ${order.orderDate || order.createdAt || 'undefined'}`);
        console.log('Full object keys:', Object.keys(order));
      });

      // Check the exact schema structure
      console.log('\n🔍 Schema Analysis:');
      const firstOrder = sampleOrders[0];
      if (firstOrder) {
        Object.keys(firstOrder).forEach(key => {
          console.log(`- ${key}: ${typeof firstOrder[key]} = ${firstOrder[key]}`);
        });
      }
    }

  } catch (error) {
    console.error('❌ Error:', error.message);
  } finally {
    await mongoose.connection.close();
    console.log('\n🔐 Database connection closed.');
  }
}

examineCurrentOrders();

const express = require('express');
const User = require('../models/User');
const { authenticateToken } = require('../middleware/auth');
const router = express.Router();

// Get all employees (users with role 'waiter', 'chef', or 'manager')
router.get('/', authenticateToken, async (req, res) => {
  try {
    const { role, status } = req.query;
    let query = {};

    // Only include employee roles
    const employeeRoles = ['waiter', 'chef', 'manager'];
    
    if (role && employeeRoles.includes(role)) {
      query.role = role;
    } else {
      query.role = { $in: employeeRoles };
    }

    if (status) {
      query.status = status;
    }

    const employees = await User.find(query).select('-password').sort({ createdAt: -1 });

    res.json({
      success: true,
      data: employees,
      total: employees.length
    });
  } catch (error) {
    console.error('Get employees error:', error);
    res.status(500).json({
      success: false,
      message: 'خطأ في جلب الموظفين'
    });
  }
});

// Get employee by ID
router.get('/:id', authenticateToken, async (req, res) => {
  try {
    const employee = await User.findById(req.params.id).select('-password');

    if (!employee) {
      return res.status(404).json({
        success: false,
        message: 'الموظف غير موجود'
      });
    }

    // Check if it's an employee role
    const employeeRoles = ['waiter', 'chef', 'manager'];
    if (!employeeRoles.includes(employee.role)) {
      return res.status(404).json({
        success: false,
        message: 'المستخدم ليس موظف'
      });
    }

    res.json({
      success: true,
      data: employee
    });
  } catch (error) {
    console.error('Get employee error:', error);
    res.status(500).json({
      success: false,
      message: 'خطأ في جلب بيانات الموظف'
    });
  }
});

// Create new employee
router.post('/', authenticateToken, async (req, res) => {
  try {
    const { username, password, name, role, phone, email } = req.body;

    // Validate required fields
    if (!username || !password || !name || !role || !email) {
      return res.status(400).json({
        success: false,
        message: 'جميع البيانات الأساسية مطلوبة (اسم المستخدم، كلمة المرور، الاسم، الدور، البريد الإلكتروني)'
      });
    }

    // Validate employee role
    const employeeRoles = ['waiter', 'chef', 'manager'];
    if (!employeeRoles.includes(role)) {
      return res.status(400).json({
        success: false,
        message: 'نوع الموظف غير صحيح'
      });
    }

    // Check if username already exists
    const existingUser = await User.findOne({ username });
    if (existingUser) {
      return res.status(400).json({
        success: false,
        message: 'اسم المستخدم موجود مسبقاً'
      });
    }

    // Check if email already exists
    const existingEmail = await User.findOne({ email });
    if (existingEmail) {
      return res.status(400).json({
        success: false,
        message: 'البريد الإلكتروني موجود مسبقاً'
      });
    }

    const employee = new User({
      username,
      password,
      name, // Use 'name' field directly
      role,
      phone,
      email,
      status: 'active'
    });

    await employee.save();

    // Return employee without password
    const employeeData = await User.findById(employee._id).select('-password');

    res.status(201).json({
      success: true,
      data: employeeData,
      message: 'تم إنشاء الموظف بنجاح'
    });
  } catch (error) {
    console.error('Create employee error:', error);
    
    if (error.code === 11000) {
      return res.status(400).json({
        success: false,
        message: 'اسم المستخدم موجود مسبقاً'
      });
    }

    res.status(500).json({
      success: false,
      message: 'خطأ في إنشاء الموظف'
    });
  }
});

// Update employee
router.put('/:id', authenticateToken, async (req, res) => {
  try {
    const { name, role, phone, email, status } = req.body;
    
    // Validate employee role if provided
    const employeeRoles = ['waiter', 'chef', 'manager'];
    if (role && !employeeRoles.includes(role)) {
      return res.status(400).json({
        success: false,
        message: 'نوع الموظف غير صحيح'
      });
    }

    const employee = await User.findById(req.params.id);
    if (!employee) {
      return res.status(404).json({
        success: false,
        message: 'الموظف غير موجود'
      });
    }

    // Check if it's an employee role
    if (!employeeRoles.includes(employee.role)) {
      return res.status(404).json({
        success: false,
        message: 'المستخدم ليس موظف'
      });
    }

    // Update fields
    if (name) employee.name = name; // Use 'name' field directly
    if (role) employee.role = role;
    if (phone) employee.phone = phone;
    if (email) employee.email = email;
    if (status) employee.status = status;

    await employee.save();

    // Return updated employee without password
    const updatedEmployee = await User.findById(employee._id).select('-password');

    res.json({
      success: true,
      data: updatedEmployee,
      message: 'تم تحديث بيانات الموظف بنجاح'
    });
  } catch (error) {
    console.error('Update employee error:', error);
    res.status(500).json({
      success: false,
      message: 'خطأ في تحديث بيانات الموظف'
    });
  }
});

// Delete employee
router.delete('/:id', authenticateToken, async (req, res) => {
  try {
    const employee = await User.findById(req.params.id);
    if (!employee) {
      return res.status(404).json({
        success: false,
        message: 'الموظف غير موجود'
      });
    }

    // Check if it's an employee role
    const employeeRoles = ['waiter', 'chef', 'manager'];
    if (!employeeRoles.includes(employee.role)) {
      return res.status(404).json({
        success: false,
        message: 'المستخدم ليس موظف'
      });
    }

    await User.findByIdAndDelete(req.params.id);

    res.json({
      success: true,
      message: 'تم حذف الموظف بنجاح'
    });
  } catch (error) {
    console.error('Delete employee error:', error);
    res.status(500).json({
      success: false,
      message: 'خطأ في حذف الموظف'
    });
  }
});

// Update employee status
router.patch('/:id/status', authenticateToken, async (req, res) => {
  try {
    const { status } = req.body;
    
    if (!['active', 'inactive'].includes(status)) {
      return res.status(400).json({
        success: false,
        message: 'حالة الموظف غير صحيحة'
      });
    }

    const employee = await User.findById(req.params.id);
    if (!employee) {
      return res.status(404).json({
        success: false,
        message: 'الموظف غير موجود'
      });
    }

    // Check if it's an employee role
    const employeeRoles = ['waiter', 'chef', 'manager'];
    if (!employeeRoles.includes(employee.role)) {
      return res.status(404).json({
        success: false,
        message: 'المستخدم ليس موظف'
      });
    }

    employee.status = status;
    await employee.save();

    const updatedEmployee = await User.findById(employee._id).select('-password');

    res.json({
      success: true,
      data: updatedEmployee,
      message: `تم ${status === 'active' ? 'تفعيل' : 'إلغاء تفعيل'} الموظف بنجاح`
    });
  } catch (error) {
    console.error('Update employee status error:', error);
    res.status(500).json({
      success: false,
      message: 'خطأ في تحديث حالة الموظف'
    });
  }
});

module.exports = router;

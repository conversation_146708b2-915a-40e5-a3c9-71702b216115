/* Categories Screen Variables - متغيرات شاشة الفئات */
:root {
  /* ألوان شاشة الفئات */
  --categories-primary-color: #2c3e50;
  --categories-secondary-color: #3498db;
  --categories-bg-primary: #ffffff;
  --categories-bg-secondary: #f8f9fa;
  --categories-text-primary: #000000;
  --categories-text-secondary: #6c757d;
  --categories-border-color: #dee2e6;
  --categories-border-light: #f1f3f4;
  
  /* ألوان الحالة لشاشة الفئات */
  --categories-success-color: #27ae60;
  --categories-success-light: rgba(39, 174, 96, 0.1);
  --categories-warning-color: #f39c12;
  --categories-warning-light: rgba(243, 156, 18, 0.1);
  --categories-error-color: #e74c3c;
  --categories-error-light: rgba(231, 76, 60, 0.1);
  --categories-info-color: #17a2b8;
  --categories-info-light: rgba(23, 162, 184, 0.1);
  
  /* متغيرات الخط لشاشة الفئات */
  --categories-font-size-xs: 12px;
  --categories-font-size-sm: 14px;
  --categories-font-size-md: 16px;
  --categories-font-size-lg: 18px;
  --categories-font-size-xl: 20px;
  --categories-font-size-xxl: 24px;
  
  /* متغيرات المسافة لشاشة الفئات */
  --categories-spacing-xs: 0.25rem;
  --categories-spacing-sm: 0.5rem;
  --categories-spacing-md: 1rem;
  --categories-spacing-lg: 1.5rem;
  --categories-spacing-xl: 2rem;
  --categories-spacing-xxl: 3rem;
  
  /* متغيرات الحدود لشاشة الفئات */
  --categories-border-radius: 8px;
  --categories-border-radius-sm: 4px;
  --categories-border-radius-lg: 12px;
  --categories-border-radius-xl: 16px;
  
  /* متغيرات الظلال لشاشة الفئات */
  --categories-shadow-sm: 0 1px 3px rgba(0, 0, 0, 0.05);
  --categories-shadow-md: 0 2px 8px rgba(0, 0, 0, 0.1);
  --categories-shadow-lg: 0 4px 16px rgba(0, 0, 0, 0.15);
  
  /* متغيرات الانتقال لشاشة الفئات */
  --categories-transition-fast: 0.2s ease;
  --categories-transition-medium: 0.3s ease;
  --categories-transition-slow: 0.5s ease;
  
  /* متغيرات خاصة بشاشة الفئات */
  --categories-card-min-width: 250px;
  --categories-grid-gap: 1.5rem;
  --categories-primary-hover: #34495e;
  --categories-primary-light: rgba(44, 62, 80, 0.1);
  --categories-active-border: var(--categories-success-color);
  --categories-inactive-border: var(--categories-error-color);
  --categories-color-indicator-size: 20px;
}




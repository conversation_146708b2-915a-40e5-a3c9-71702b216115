/* نظام ألوان المقهى */
:root {
  /* ألوان أساسية */
  --primary: #795548;
  --primary-dark: #5d4037;
  --primary-light: #a1887f;
  --accent: #d84315;
  --accent-dark: #bf360c;
  --accent-light: #ff7043;

  /* ألوان محايدة */
  --background: #f7f7f7;
  --surface: #ffffff;
  --text-primary: #212121;
  --text-secondary: #757575;
  --border: #e0e0e0;

  /* ألوان الحالة */
  --success: #4caf50;
  --warning: #ff9800;
  --error: #f44336;
  --info: #2196f3;

  /* قياسات */
  --border-radius-sm: 4px;
  --border-radius-md: 8px;
  --border-radius-lg: 12px;
  --spacing-xs: 0.25rem;
  --spacing-sm: 0.5rem;
  --spacing-md: 1rem;
  --spacing-lg: 1.5rem;
  --spacing-xl: 2rem;

  /* ظلال */
  --shadow-sm: 0 1px 3px rgba(0, 0, 0, 0.1);
  --shadow-md: 0 2px 8px rgba(0, 0, 0, 0.1);
  --shadow-lg: 0 4px 16px rgba(0, 0, 0, 0.1);

  /* خطوط */
  --font-family: 'Cairo', Tahoma, Arial, sans-serif;
  --font-size-sm: 0.875rem;
  --font-size-md: 1rem;
  --font-size-lg: 1.125rem;
  --font-size-xl: 1.25rem;
  --font-size-2xl: 1.5rem;
  --font-weight-normal: 400;
  --font-weight-medium: 500;
  --font-weight-bold: 700;

  /* متغيرات إضافية */
  --transition-speed: 0.3s;
}

/* الوضع المظلم */
[data-theme="dark"] {
  /* ألوان أساسية - نسخة داكنة */
  --primary: #6d4c41;
  --primary-dark: #4e342e;
  --primary-light: #8d6e63;

  /* ألوان محايدة - نسخة داكنة */
  --background: #121212;
  --surface: #1e1e1e;
  --text-primary: #e0e0e0;
  --text-secondary: #a0a0a0;
  --border: #333333;

  /* ظلال - نسخة داكنة */
  --shadow-sm: 0 1px 3px rgba(0, 0, 0, 0.3);
  --shadow-md: 0 2px 8px rgba(0, 0, 0, 0.4);
  --shadow-lg: 0 4px 16px rgba(0, 0, 0, 0.5);
}

/* إعدادات عامة */
body, html {
  margin: 0;
  padding: 0;
  font-family: var(--font-family);
  background: var(--background);
  color: var(--text-primary);
  line-height: 1.5;
  direction: rtl;
  width: 100%;
  max-width: 100vw;
  overflow-x: hidden;
}

/* إصلاح شامل للمساحات الجانبية في الهاتف المحمول */
@media (max-width: 768px) {
  body, html {
    width: 100vw;
    max-width: 100vw;
    margin: 0;
    padding: 0;
    box-sizing: border-box;
  }
  
  * {
    max-width: 100vw;
    box-sizing: border-box;
  }
}

/* تحسينات عامة للعناصر */
button {
  cursor: pointer;
  font-family: var(--font-family);
  transition: all 0.2s ease;
}

input, select, textarea {
  font-family: var(--font-family);
}

/* تصميم الأزرار */
.btn {
  display: inline-block;
  padding: var(--spacing-sm) var(--spacing-lg);
  border-radius: var(--border-radius-md);
  border: none;
  font-weight: var(--font-weight-medium);
  text-align: center;
  transition: background-color 0.2s, transform 0.1s;
}

.btn:hover {
  transform: translateY(-1px);
}

.btn:active {
  transform: translateY(1px);
}

.btn-primary {
  background-color: var(--primary);
  color: white;
}

.btn-primary:hover {
  background-color: var(--primary-dark);
}

.btn-accent {
  background-color: var(--accent);
  color: white;
}

.btn-accent:hover {
  background-color: var(--accent-dark);
}

/* تصميم البطاقات */
.card {
  background-color: var(--surface);
  border-radius: var(--border-radius-lg);
  box-shadow: var(--shadow-md);
  padding: var(--spacing-lg);
}

/* تصميم الجداول */
.table {
  width: 100%;
  border-collapse: collapse;
  border-radius: var(--border-radius-md);
  overflow: hidden;
  box-shadow: var(--shadow-sm);
}

.table th {
  background-color: var(--primary-light);
  color: white;
  font-weight: var(--font-weight-medium);
  text-align: right;
  padding: var(--spacing-sm) var(--spacing-md);
}

.table td {
  padding: var(--spacing-sm) var(--spacing-md);
  border-bottom: 1px solid var(--border);
}

.table tr:last-child td {
  border-bottom: none;
}

.table tr:nth-child(even) {
  background-color: rgba(0, 0, 0, 0.02);
}

/* تصميم النماذج */
.form-group {
  margin-bottom: var(--spacing-md);
}

.form-label {
  display: block;
  margin-bottom: var(--spacing-xs);
  font-weight: var(--font-weight-medium);
}

.form-control {
  width: 100%;
  padding: var(--spacing-sm);
  border: 1px solid var(--border);
  border-radius: var(--border-radius-md);
  font-size: var(--font-size-md);
  transition: border-color 0.2s;
}

.form-control:focus {
  outline: none;
  border-color: var(--primary);
  box-shadow: 0 0 0 2px rgba(121, 85, 72, 0.2);
}

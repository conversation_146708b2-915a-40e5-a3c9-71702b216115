const mongoose = require('mongoose');

const discountRequestSchema = new mongoose.Schema({
  orderId: {
    type: String,
    required: true
  },
  orderNumber: {
    type: String,
    required: false
  },
  customerName: {
    type: String,
    required: true
  },
  originalAmount: {
    type: Number,
    required: true
  },
  requestedDiscount: {
    type: Number,
    required: true
  },
  amount: {
    type: Number,
    required: false,
    default: function() {
      return this.requestedDiscount;
    }
  },
  reason: {
    type: String,
    required: true
  },
  status: {
    type: String,
    enum: ['pending', 'approved', 'rejected'],
    default: 'pending'
  },
  requestedBy: {
    type: String,
    required: true
  },
  waiterName: {
    type: String,
    required: false
  },
  tableNumber: {
    type: String,
    required: false
  },
  approvedBy: {
    type: String
  },
  approvedByName: {
    type: String
  },
  rejectedBy: {
    type: String
  },
  rejectedByName: {
    type: String
  }
}, {
  timestamps: true
});

module.exports = mongoose.model('DiscountRequest', discountRequestSchema);

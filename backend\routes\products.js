const express = require('express');
const Product = require('../models/Product');
const Category = require('../models/Category');
const { authenticateToken } = require('../middleware/auth');
const { 
  applyUnifiedMiddleware, 
  asyncHandler, 
  formatResponse, 
  formatError 
} = require('../middleware/unifiedRouteHandler');
const { 
  sendSuccess, 
  sendError, 
  sendNotFound, 
  handleDatabaseError 
} = require('../middleware/unifiedResponse');

const router = express.Router();

// Apply unified middleware
applyUnifiedMiddleware(router);

// Get all products (alias for /api/menu)
router.get('/', asyncHandler(async (req, res) => {
  const { category, available, featured, search } = req.query;
  let query = { status: 'active' };

  // Build query filters
  if (category) {
    query.category = category;
  }

  if (available !== undefined) {
    query.available = available === 'true';
  }

  if (featured !== undefined) {
    query.featured = featured === 'true';
  }

  let products;
  if (search) {
    products = await Product.search(search);
  } else {
    products = await Product.find(query)
      .populate('category', 'name icon color')
      .sort({ createdAt: -1 });
  }

  console.log(`📋 Products Query Result:`, {
    query,
    count: products.length,
    deviceType: req.deviceInfo?.type || 'unknown',
    search: search || 'none'
  });

  return sendSuccess(res, products, 'تم تحميل المنتجات بنجاح');
}));

// Get product by ID
router.get('/:id', asyncHandler(async (req, res) => {
  const product = await Product.findById(req.params.id)
    .populate('category', 'name icon color');

  if (!product) {
    return sendNotFound(res, 'المنتج');
  }

  return sendSuccess(res, product, 'تم تحميل المنتج بنجاح');
}));

// Create new product
router.post('/', authenticateToken, asyncHandler(async (req, res) => {
  console.log('📱 Product Creation Request:', {
    deviceType: req.deviceInfo?.type,
    userAgent: req.headers['user-agent'],
    contentType: req.headers['content-type'],
    body: req.body,
    user: req.user?.name || 'Unknown'
  });
  console.log('🔍 Detailed request body analysis:', {
    name: { value: req.body.name, type: typeof req.body.name, length: req.body.name?.length },
    description: { value: req.body.description, type: typeof req.body.description, length: req.body.description?.length },
    price: { value: req.body.price, type: typeof req.body.price },
    category: { value: req.body.category, type: typeof req.body.category },
    categoryName: { value: req.body.categoryName, type: typeof req.body.categoryName, length: req.body.categoryName?.length },
    available: { value: req.body.available, type: typeof req.body.available }
  });

  const { name, description, price, category, categoryName, image } = req.body;

  // Enhanced validation with mobile-friendly error messages
  const trimmedName = typeof name === 'string' ? name.trim() : '';
  if (!trimmedName) {
    return sendError(res, 'اسم المنتج مطلوب', 400, 'MISSING_NAME');
  }
  const trimmedDescription = typeof description === 'string' ? description.trim() : '';
  // إذا كان الوصف فارغ، استخدم وصف افتراضي
  const productDescription = trimmedDescription || `${trimmedName} - منتج لذيذ من مقهى ديشا`;

  const numericPrice = parseFloat(price);
  if (!numericPrice || numericPrice <= 0) {
    return sendError(res, 'سعر المنتج يجب أن يكون رقماً موجباً', 400, 'INVALID_PRICE');
  }

  // Handle category creation or selection
  let productCategory;
  if (category) {
    productCategory = await Category.findById(category);
    if (!productCategory) {
      return sendNotFound(res, 'التصنيف');
    }
  } else if (categoryName && categoryName.trim()) {
    const trimmedCategoryName = categoryName.trim();
    productCategory = await Category.findOne({ 
      name: { $regex: new RegExp(`^${trimmedCategoryName}$`, 'i') } 
    });
    
    if (!productCategory) {
      productCategory = new Category({
        name: trimmedCategoryName,
        icon: 'category',
        color: '#6366f1'
      });
      
      await productCategory.save();
      console.log('✅ New category created:', productCategory.name);
    }
  } else {
    return sendError(res, 'التصنيف مطلوب', 400, 'MISSING_CATEGORY');
  }

  // Check for duplicate product names
  const existingProduct = await Product.findOne({ 
    name: { $regex: new RegExp(`^${trimmedName}$`, 'i') },
    status: 'active'
  });
  
  if (existingProduct) {
    return sendError(res, 'منتج بهذا الاسم موجود بالفعل', 409, 'DUPLICATE_NAME');
  }  // Create the product
  const newProduct = new Product({
    name: trimmedName,
    description: productDescription,
    price: numericPrice,
    category: productCategory._id,
    categoryName: productCategory.name, // إضافة اسم الفئة
    image: image || null,
    available: true,
    featured: false,
    status: 'active'
  });

  const savedProduct = await newProduct.save();
  const populatedProduct = await Product.findById(savedProduct._id)
    .populate('category', 'name icon color');

  console.log('✅ Product created successfully:', {
    id: populatedProduct._id,
    name: populatedProduct.name,
    price: populatedProduct.price,
    category: populatedProduct.category?.name,
    deviceType: req.deviceInfo?.type
  });

  return sendSuccess(res, populatedProduct, 'تم إنشاء المنتج بنجاح', 201);
}));

// Update product
router.put('/:id', authenticateToken, asyncHandler(async (req, res) => {
  const product = await Product.findById(req.params.id);
  
  if (!product) {
    return sendNotFound(res, 'المنتج');
  }

  const { name, description, price, category, available, featured, image, stock } = req.body;

  // Validate and update fields
  if (name !== undefined) {
    const trimmedName = typeof name === 'string' ? name.trim() : '';
    if (!trimmedName) {
      return sendError(res, 'اسم المنتج لا يمكن أن يكون فارغاً', 400, 'INVALID_NAME');
    }
    
    // Check for duplicate names (excluding current product)
    const existingProduct = await Product.findOne({
      _id: { $ne: req.params.id },
      name: { $regex: new RegExp(`^${trimmedName}$`, 'i') },
      status: 'active'
    });
    
    if (existingProduct) {
      return sendError(res, 'منتج بهذا الاسم موجود بالفعل', 409, 'DUPLICATE_NAME');
    }
    
    product.name = trimmedName;
  }

  if (description !== undefined) {
    const trimmedDescription = typeof description === 'string' ? description.trim() : '';
    if (!trimmedDescription) {
      return sendError(res, 'وصف المنتج لا يمكن أن يكون فارغاً', 400, 'INVALID_DESCRIPTION');
    }
    product.description = trimmedDescription;
  }

  if (price !== undefined) {
    const numericPrice = parseFloat(price);
    if (!numericPrice || numericPrice <= 0) {
      return sendError(res, 'سعر المنتج يجب أن يكون رقماً موجباً', 400, 'INVALID_PRICE');
    }
    product.price = numericPrice;
  }

  if (category !== undefined) {
    if (!category) {
      return sendError(res, 'التصنيف مطلوب', 400, 'MISSING_CATEGORY');
    }
    
    const categoryExists = await Category.findById(category);
    if (!categoryExists) {
      return sendNotFound(res, 'التصنيف');
    }
    
    product.category = category;
  }

  if (available !== undefined) {
    product.available = Boolean(available);
  }

  if (featured !== undefined) {
    product.featured = Boolean(featured);
  }

  if (image !== undefined) {
    product.image = image;
  }

  // Handle stock updates
  if (stock !== undefined) {
    if (typeof stock === 'object' && stock.quantity !== undefined) {
      // Update stock object
      const quantity = parseInt(stock.quantity);
      if (isNaN(quantity) || quantity < 0) {
        return sendError(res, 'كمية المخزون يجب أن تكون رقماً غير سالب', 400, 'INVALID_STOCK');
      }
      product.stock.quantity = quantity;
      
      // Update other stock fields if provided
      if (stock.unit !== undefined) {
        product.stock.unit = stock.unit;
      }
      if (stock.lowStockAlert !== undefined) {
        const lowStockAlert = parseInt(stock.lowStockAlert);
        if (!isNaN(lowStockAlert) && lowStockAlert >= 0) {
          product.stock.lowStockAlert = lowStockAlert;
        }
      }
    } else if (typeof stock === 'number') {
      // Handle legacy format where stock is just a number
      const quantity = parseInt(stock);
      if (isNaN(quantity) || quantity < 0) {
        return sendError(res, 'كمية المخزون يجب أن تكون رقماً غير سالب', 400, 'INVALID_STOCK');
      }
      product.stock.quantity = quantity;
    }
    
    console.log('📦 Stock updated:', {
      productId: product._id,
      newQuantity: product.stock.quantity,
      unit: product.stock.unit,
      lowStockAlert: product.stock.lowStockAlert
    });
  }

  product.updatedAt = new Date();
  const updatedProduct = await product.save();
  
  const populatedProduct = await Product.findById(updatedProduct._id)
    .populate('category', 'name icon color');

  console.log('✅ Product updated successfully:', {
    id: populatedProduct._id,
    name: populatedProduct.name,
    deviceType: req.deviceInfo?.type
  });

  return sendSuccess(res, populatedProduct, 'تم تحديث المنتج بنجاح');
}));

// Delete product (soft delete)
router.delete('/:id', authenticateToken, asyncHandler(async (req, res) => {
  const product = await Product.findById(req.params.id);
  
  if (!product) {
    return sendNotFound(res, 'المنتج');
  }

  // Soft delete by changing status
  product.status = 'deleted';
  product.updatedAt = new Date();
  await product.save();

  console.log('✅ Product deleted successfully:', {
    id: product._id,
    name: product.name,
    deviceType: req.deviceInfo?.type
  });

  return sendSuccess(res, { id: product._id }, 'تم حذف المنتج بنجاح');
}));

// Toggle product availability
router.patch('/:id/toggle-availability', authenticateToken, asyncHandler(async (req, res) => {
  const product = await Product.findById(req.params.id);
  
  if (!product) {
    return sendNotFound(res, 'المنتج');
  }

  product.available = !product.available;
  product.updatedAt = new Date();
  
  const updatedProduct = await product.save();
  const populatedProduct = await Product.findById(updatedProduct._id)
    .populate('category', 'name icon color');

  const status = product.available ? 'متاح' : 'غير متاح';
  
  console.log('✅ Product availability toggled:', {
    id: product._id,
    name: product.name,
    available: product.available,
    deviceType: req.deviceInfo?.type
  });

  return sendSuccess(res, populatedProduct, `تم تغيير حالة المنتج إلى ${status}`);
}));

// Toggle product featured status
router.patch('/:id/toggle-featured', authenticateToken, asyncHandler(async (req, res) => {
  const product = await Product.findById(req.params.id);
  
  if (!product) {
    return sendNotFound(res, 'المنتج');
  }

  product.featured = !product.featured;
  product.updatedAt = new Date();
  
  const updatedProduct = await product.save();
  const populatedProduct = await Product.findById(updatedProduct._id)
    .populate('category', 'name icon color');

  const status = product.featured ? 'مميز' : 'عادي';
  
  console.log('✅ Product featured status toggled:', {
    id: product._id,
    name: product.name,
    featured: product.featured,
    deviceType: req.deviceInfo?.type
  });

  return sendSuccess(res, populatedProduct, `تم تغيير حالة المنتج إلى ${status}`);
}));

// Get products by category
router.get('/category/:categoryId', asyncHandler(async (req, res) => {
  const { categoryId } = req.params;
  const { available, featured } = req.query;
  
  let query = { 
    category: categoryId, 
    status: 'active' 
  };

  if (available !== undefined) {
    query.available = available === 'true';
  }

  if (featured !== undefined) {
    query.featured = featured === 'true';
  }

  const products = await Product.find(query)
    .populate('category', 'name icon color')
    .sort({ createdAt: -1 });

  console.log('📋 Products by category:', {
    categoryId,
    query,
    count: products.length,
    deviceType: req.deviceInfo?.type
  });

  return sendSuccess(res, products, 'تم تحميل منتجات التصنيف بنجاح');
}));

// Search products
router.get('/search/:term', asyncHandler(async (req, res) => {
  const { term } = req.params;
  const { available, category } = req.query;
  
  let query = {
    status: 'active',
    $or: [
      { name: { $regex: term, $options: 'i' } },
      { description: { $regex: term, $options: 'i' } }
    ]
  };

  if (available !== undefined) {
    query.available = available === 'true';
  }

  if (category) {
    query.category = category;
  }

  const products = await Product.find(query)
    .populate('category', 'name icon color')
    .sort({ createdAt: -1 });

  console.log('🔍 Product search:', {
    term,
    query,
    count: products.length,
    deviceType: req.deviceInfo?.type
  });

  return sendSuccess(res, products, `تم العثور على ${products.length} منتج`);
}));

module.exports = router;

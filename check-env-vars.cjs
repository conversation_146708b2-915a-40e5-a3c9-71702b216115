// اختبار متغيرات البيئة للفرونت إند

console.log('🔍 فحص متغيرات البيئة...\n');

// قراءة ملف .env.local
const fs = require('fs');
const path = require('path');

try {
  const envLocalPath = path.join(__dirname, '.env.local');
  const envContent = fs.readFileSync(envLocalPath, 'utf8');
  
  console.log('📁 محتوى .env.local:');
  console.log('=====================================');
  console.log(envContent);
  
  // استخراج متغيرات VITE
  const viteVars = envContent.split('\n')
    .filter(line => line.startsWith('VITE_'))
    .filter(line => !line.startsWith('#'));
    
  console.log('\n🎯 متغيرات VITE المفعلة:');
  console.log('=====================================');
  viteVars.forEach(line => {
    console.log(line);
  });
  
  // التحقق من القيم المحددة
  const apiUrl = viteVars.find(line => line.startsWith('VITE_API_URL='));
  const socketUrl = viteVars.find(line => line.startsWith('VITE_SOCKET_URL='));
  
  console.log('\n✅ التحقق من الإعدادات المهمة:');
  console.log('=====================================');
  console.log('VITE_API_URL:', apiUrl ? apiUrl.split('=')[1] : 'غير محدد');
  console.log('VITE_SOCKET_URL:', socketUrl ? socketUrl.split('=')[1] : 'غير محدد');
  
  // التحقق من صحة الـ URLs
  const expectedApiUrl = 'http://localhost:5001';
  const currentApiUrl = apiUrl ? apiUrl.split('=')[1] : '';
  
  console.log('\n🎯 النتيجة:');
  console.log('=====================================');
  if (currentApiUrl === expectedApiUrl) {
    console.log('✅ VITE_API_URL صحيح:', currentApiUrl);
  } else {
    console.log('❌ VITE_API_URL خطأ:', currentApiUrl, '(المتوقع:', expectedApiUrl, ')');
  }
  
} catch (error) {
  console.error('❌ خطأ في قراءة ملف البيئة:', error.message);
}

console.log('\n📋 خطوات حل المشكلة:');
console.log('=====================================');
console.log('1. تأكد من أن VITE_API_URL = http://localhost:5001');
console.log('2. أعد تشغيل الفرونت إند: npm run dev');
console.log('3. تحقق من الكونسول في المتصفح');
console.log('4. ابحث عن رسائل: "🔍 Starting health check..."');

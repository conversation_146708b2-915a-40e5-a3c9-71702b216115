const express = require('express');
const router = express.Router();
const { authenticateToken } = require('../middleware/auth');
const Order = require('../models/Order');
const User = require('../models/User');

// GET /api/v1/waiter-stats - إحصائيات النادلين
router.get('/', authenticateToken, async (req, res) => {
  try {
    console.log('🔍 جلب إحصائيات النادلين...');
    
    // جلب جميع النادلين النشطين
    const waiters = await User.find({ 
      role: 'waiter',
      isActive: true 
    }).select('username name');
    
    console.log(`📊 وجد ${waiters.length} نادل نشط`);
    
    // إنشاء قائمة لحفظ إحصائيات كل نادل
    const waiterStats = [];
    
    for (const waiter of waiters) {
      console.log(`🔍 حساب إحصائيات النادل: ${waiter.name || waiter.username}`);
      
      // جلب جميع الطلبات للنادل (البحث بأسماء مختلفة)
      const orders = await Order.find({ 
        $or: [
          { waiterName: waiter.username },
          { waiterName: waiter.name },
          { waiterId: waiter._id },
          { 'staff.waiter': waiter._id }
        ]
      });
      
      console.log(`📝 وجد ${orders.length} طلب للنادل ${waiter.username}`);
      
      // حساب الإحصائيات
      let totalOrders = orders.length;
      let totalAmount = 0;
      let completedOrders = 0;
      let pendingOrders = 0;
      let cancelledOrders = 0;
      
      orders.forEach(order => {
        // حساب المبلغ الإجمالي - استخدام أفضل قيمة متاحة
        let amount = 0;
        if (order.totals && order.totals.total) {
          amount = order.totals.total;
        } else if (order.totalPrice) {
          amount = order.totalPrice;
        } else if (order.totalAmount) {
          amount = order.totalAmount;
        } else if (order.finalAmount) {
          amount = order.finalAmount;
        }
        
        // حساب جميع الطلبات بدون فلتر حالة لمنع فقدان المبيعات
        totalAmount += amount;
        
        // تصنيف الطلبات حسب الحالة للإحصائيات فقط (ليس للمبيعات)
        if (order.status === 'completed' || order.status === 'delivered' || order.status === 'served') {
          completedOrders++;
        } else if (order.status === 'cancelled') {
          cancelledOrders++;
        } else {
          pendingOrders++;
        }
      });
      
      // إضافة إحصائيات النادل
      waiterStats.push({
        waiterId: waiter._id,
        waiterName: waiter.username,
        waiterFullName: waiter.name || waiter.username,
        totalOrders,
        totalAmount: Math.round(totalAmount * 100) / 100, // تقريب لرقمين عشريين
        completedOrders,
        pendingOrders,
        cancelledOrders,
        averageOrderValue: totalOrders > 0 ? Math.round((totalAmount / totalOrders) * 100) / 100 : 0
      });
      
      console.log(`✅ ${waiter.username}: ${totalOrders} طلب، ${totalAmount} جنيه`);
    }
    
    // ترتيب النادلين حسب إجمالي المبيعات
    waiterStats.sort((a, b) => b.totalAmount - a.totalAmount);
    
    // حساب الإحصائيات العامة
    const totalStats = {
      totalWaiters: waiterStats.length,
      totalOrdersAll: waiterStats.reduce((sum, waiter) => sum + waiter.totalOrders, 0),
      totalAmountAll: Math.round(waiterStats.reduce((sum, waiter) => sum + waiter.totalAmount, 0) * 100) / 100,
      totalCompletedAll: waiterStats.reduce((sum, waiter) => sum + waiter.completedOrders, 0),
      totalPendingAll: waiterStats.reduce((sum, waiter) => sum + waiter.pendingOrders, 0),
      totalCancelledAll: waiterStats.reduce((sum, waiter) => sum + waiter.cancelledOrders, 0)
    };
    
    console.log('📊 إحصائيات عامة:', totalStats);
    console.log('✅ تم حساب إحصائيات النادلين بنجاح');
    
    res.json({
      success: true,
      data: {
        waiters: waiterStats,
        totals: totalStats
      }
    });
    
  } catch (error) {
    console.error('❌ خطأ في حساب إحصائيات النادلين:', error);
    res.status(500).json({
      success: false,
      message: 'حدث خطأ في حساب الإحصائيات',
      error: error.message
    });
  }
});

// GET /api/v1/waiter-stats/:waiterId - إحصائيات نادل محدد
router.get('/:waiterId', authenticateToken, async (req, res) => {
  try {
    const { waiterId } = req.params;
    console.log(`🔍 جلب إحصائيات النادل: ${waiterId}`);
    
    // جلب بيانات النادل
    const waiter = await User.findById(waiterId);
    if (!waiter) {
      return res.status(404).json({
        success: false,
        message: 'النادل غير موجود'
      });
    }
    
    // جلب طلبات النادل
    const orders = await Order.find({ 
      $or: [
        { waiterName: waiter.username },
        { waiterName: waiter.name },
        { waiterId: waiter._id }
      ]
    }).sort({ createdAt: -1 });
    
    // حساب الإحصائيات التفصيلية
    let dailyStats = {};
    let monthlyStats = {};
    let totalAmount = 0;
    
    orders.forEach(order => {
      // حساب المبلغ
      let amount = 0;
      if (order.totals && order.totals.total) {
        amount = order.totals.total;
      } else if (order.totalPrice) {
        amount = order.totalPrice;
      } else if (order.totalAmount) {
        amount = order.totalAmount;
      } else if (order.finalAmount) {
        amount = order.finalAmount;
      }
      
      totalAmount += amount;
      
      const date = new Date(order.createdAt);
      const dayKey = date.toISOString().split('T')[0]; // YYYY-MM-DD
      const monthKey = `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}`; // YYYY-MM
      
      // إحصائيات يومية
      if (!dailyStats[dayKey]) {
        dailyStats[dayKey] = { orders: 0, amount: 0 };
      }
      dailyStats[dayKey].orders++;
      dailyStats[dayKey].amount += amount;
      
      // إحصائيات شهرية
      if (!monthlyStats[monthKey]) {
        monthlyStats[monthKey] = { orders: 0, amount: 0 };
      }
      monthlyStats[monthKey].orders++;
      monthlyStats[monthKey].amount += amount;
    });
    
    res.json({
      success: true,
      data: {
        waiter: {
          id: waiter._id,
          username: waiter.username,
          name: waiter.name
        },
        orders: orders, // إزالة قيد الـ 50 طلب - عرض جميع الطلبات
        dailyStats,
        monthlyStats,
        totalOrders: orders.length,
        totalAmount: Math.round(totalAmount * 100) / 100
      }
    });
    
  } catch (error) {
    console.error('❌ خطأ في جلب إحصائيات النادل:', error);
    res.status(500).json({
      success: false,
      message: 'حدث خطأ في جلب الإحصائيات',
      error: error.message
    });
  }
});

module.exports = router;

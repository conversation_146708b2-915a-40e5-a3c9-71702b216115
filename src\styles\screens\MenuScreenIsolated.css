﻿/* Menu Screen Isolated Styles */
@import '../variables/menu-variables.css';

/* Ø<PERSON>Ø®Ø·ÙŠØ· Ø§Ù„Ù‚Ø§Ø¦Ù…Ø© */
.menu-screen {
  padding: var(--menu-spacing-lg);
  max-width: 1400px;
  margin: 0 auto;
  overflow-x: hidden;
}

/* Ø±Ø£Ø³ Ø§Ù„Ø´Ø§Ø´Ø© */
.menu-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: var(--menu-spacing-lg);
  flex-wrap: wrap;
  gap: var(--menu-spacing-md);
}

.menu-title {
  font-size: var(--menu-font-size-xl);
  font-weight: 700;
  color: var(--menu-text-primary);
  margin: 0;
}

/* Ø£Ø¯ÙˆØ§Øª Ø§Ù„ØªØ­ÙƒÙ… ÙÙŠ Ø§Ù„Ù‚Ø§Ø¦Ù…Ø© */
.menu-controls {
  display: flex;
  gap: var(--menu-spacing-md);
  align-items: center;
  flex-wrap: wrap;
}

.menu-search {
  padding: var(--menu-spacing-sm) var(--menu-spacing-md);
  border: 1px solid var(--menu-border-color);
  border-radius: var(--menu-border-radius);
  font-size: var(--menu-font-size-sm);
  background: var(--menu-bg-primary);
  color: var(--menu-text-primary);
  min-width: 200px;
}

.menu-category-filter {
  padding: var(--menu-spacing-sm) var(--menu-spacing-md);
  border: 1px solid var(--menu-border-color);
  border-radius: var(--menu-border-radius);
  font-size: var(--menu-font-size-sm);
  background: var(--menu-bg-primary);
  color: var(--menu-text-primary);
  min-width: 120px;
}

/* Ø²Ø± Ø¥Ø¶Ø§ÙØ© Ù…Ù†ØªØ¬ */
.add-menu-item-btn {
  padding: var(--menu-spacing-sm) var(--menu-spacing-lg);
  background: var(--menu-primary-color);
  color: white;
  border: none;
  border-radius: var(--menu-border-radius);
  cursor: pointer;
  font-weight: 500;
  transition: all 0.3s ease;
  white-space: nowrap;
}

.add-menu-item-btn:hover {
  background: var(--menu-primary-hover);
  transform: translateY(-1px);
}

/* Ø§Ù„Ø¥Ø­ØµØ§Ø¦ÙŠØ§Øª Ø§Ù„Ø³Ø±ÙŠØ¹Ø© */
.menu-stats {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: var(--menu-spacing-md);
  margin-bottom: var(--menu-spacing-lg);
}

.menu-stat-card {
  background: var(--menu-bg-primary);
  border: 1px solid var(--menu-border-color);
  border-radius: var(--menu-border-radius);
  padding: var(--menu-spacing-md);
  text-align: center;
}

.menu-stat-number {
  font-size: var(--menu-font-size-xl);
  font-weight: 700;
  color: var(--menu-primary-color);
  margin-bottom: var(--menu-spacing-xs);
}

.menu-stat-label {
  font-size: var(--menu-font-size-sm);
  color: var(--menu-text-secondary);
}

/* Ø¨Ø·Ø§Ù‚Ø§Øª Ø§Ù„Ù‚Ø§Ø¦Ù…Ø© */
.menu-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
  gap: var(--menu-spacing-lg);
  margin-top: var(--menu-spacing-lg);
}

.menu-item-card {
  background: var(--menu-bg-primary);
  border: 1px solid var(--menu-border-color);
  border-radius: var(--menu-border-radius-lg);
  overflow: visible;
  word-break: break-word;
  position: relative;
  transition: all 0.3s ease;
  box-shadow: var(--menu-shadow-sm);
}

.menu-item-card:hover {
  transform: translateY(-2px);
  box-shadow: var(--menu-shadow-md);
}

.menu-item-card.unavailable {
  opacity: 0.6;
  border-color: var(--menu-error-color);
}

/* ØµÙˆØ±Ø© Ø§Ù„Ù…Ù†ØªØ¬ */
.menu-item-image {
  width: 100%;
  height: 150px;
  object-fit: cover;
  background: var(--menu-bg-secondary);
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--menu-text-secondary);
  font-size: var(--menu-font-size-sm);
}

/* Ù…Ø­ØªÙˆÙ‰ Ø§Ù„Ø¨Ø·Ø§Ù‚Ø© */
.menu-item-content {
  padding: var(--menu-spacing-md);
}

/* Ø±Ø£Ø³ Ø§Ù„Ø¨Ø·Ø§Ù‚Ø© */
.menu-item-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: var(--menu-spacing-sm);
}

.menu-item-name {
  font-size: var(--menu-font-size-md);
  font-weight: 600;
  color: var(--menu-text-primary);
  margin: 0;
  flex: 1;
  line-height: 1.3;
}

.menu-item-availability {
  padding: 2px 6px;
  border-radius: 8px;
  font-size: var(--menu-font-size-xs);
  font-weight: 500;
  text-transform: uppercase;
  margin-left: var(--menu-spacing-xs);
}

.menu-item-availability.available {
  background: var(--menu-success-light);
  color: var(--menu-success-color);
}

.menu-item-availability.unavailable {
  background: var(--menu-error-light);
  color: var(--menu-error-color);
}

/* ØªÙØ§ØµÙŠÙ„ Ø§Ù„Ù…Ù†ØªØ¬ */
.menu-item-details {
  margin-bottom: var(--menu-spacing-md);
}

.menu-item-category {
  font-size: var(--menu-font-size-xs);
  color: var(--menu-text-secondary);
  margin-bottom: var(--menu-spacing-xs);
  background: var(--menu-bg-secondary);
  padding: 2px 6px;
  border-radius: 4px;
  display: inline-block;
}

.menu-item-description {
  font-size: var(--menu-font-size-sm);
  color: var(--menu-text-secondary);
  line-height: 1.4;
  margin-bottom: var(--menu-spacing-sm);
}

.menu-item-price {
  font-size: var(--menu-font-size-lg);
  font-weight: 700;
  color: var(--menu-primary-color);
  margin-bottom: var(--menu-spacing-sm);
}

/* Ù…Ø¹Ù„ÙˆÙ…Ø§Øª Ø¥Ø¶Ø§ÙÙŠØ© */
.menu-item-meta {
  display: flex;
  gap: var(--menu-spacing-md);
  margin-bottom: var(--menu-spacing-md);
  font-size: var(--menu-font-size-xs);
  color: var(--menu-text-secondary);
}

.menu-item-meta-item {
  display: flex;
  align-items: center;
  gap: var(--menu-spacing-xs);
}

/* Ø£Ø²Ø±Ø§Ø± Ø§Ù„ØªØ­ÙƒÙ… */
.menu-item-actions {
  display: flex;
  gap: var(--menu-spacing-sm);
  margin-top: var(--menu-spacing-md);
}

.menu-action-btn {
  padding: var(--menu-spacing-xs) var(--menu-spacing-sm);
  border: none;
  border-radius: var(--menu-border-radius);
  cursor: pointer;
  font-size: var(--menu-font-size-xs);
  font-weight: 500;
  transition: all 0.3s ease;
  flex: 1;
}

.menu-action-btn.edit {
  background: var(--menu-warning-light);
  color: var(--menu-warning-color);
}

.menu-action-btn.edit:hover {
  background: var(--menu-warning-color);
  color: white;
}

.menu-action-btn.delete {
  background: var(--menu-error-light);
  color: var(--menu-error-color);
}

.menu-action-btn.delete:hover {
  background: var(--menu-error-color);
  color: white;
}

.menu-action-btn.toggle {
  background: var(--menu-info-light);
  color: var(--menu-info-color);
}

.menu-action-btn.toggle:hover {
  background: var(--menu-info-color);
  color: white;
}

/* Ø­Ø§Ù„Ø© Ø§Ù„ØªØ­Ù…ÙŠÙ„ */
.menu-loading {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 300px;
  font-size: var(--menu-font-size-lg);
  color: var(--menu-text-secondary);
}

/* Ø±Ø³Ø§Ù„Ø© Ø¹Ø¯Ù… ÙˆØ¬ÙˆØ¯ Ù…Ù†ØªØ¬Ø§Øª */
.no-menu-items {
  text-align: center;
  padding: var(--menu-spacing-xl);
  color: var(--menu-text-secondary);
}

.no-menu-items h3 {
  margin-bottom: var(--menu-spacing-md);
  color: var(--menu-text-primary);
}

/* ØªØ­Ø³ÙŠÙ†Ø§Øª Ø§Ù„Ù‚Ø§Ø¦Ù…Ø© Ø§Ù„Ù…Ø·ÙˆØ±Ø© */
.enhanced-menu-card {
  position: relative;
  overflow: hidden;
}

.enhanced-menu-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 3px;
  background: linear-gradient(90deg, var(--menu-primary-color), var(--menu-primary-light));
  opacity: 0;
  transition: opacity 0.3s ease;
}

.enhanced-menu-card:hover::before {
  opacity: 1;
}

/* Ø´Ø§Ø±Ø© Ø§Ù„Ø´Ø¹Ø¨ÙŠØ© */
.popularity-badge {
  position: absolute;
  top: var(--menu-spacing-sm);
  right: var(--menu-spacing-sm);
  background: var(--menu-primary-color);
  color: white;
  padding: 2px 6px;
  border-radius: 12px;
  font-size: var(--menu-font-size-xs);
  font-weight: 500;
}

/* ØªØ¬Ø§ÙˆØ¨ Ø§Ù„Ø´Ø§Ø´Ø© */
@media (max-width: 768px) {
  .menu-screen {
    padding: var(--menu-spacing-md);
  }
  
  .menu-header {
    flex-direction: column;
    align-items: stretch;
  }
  
  .menu-controls {
    flex-direction: column;
  }
  
  .menu-search,
  .menu-category-filter {
    width: 100%;
  }
  
  .menu-grid {
    grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
    gap: var(--menu-spacing-md);
  }
  
  .menu-stats {
    grid-template-columns: repeat(2, 1fr);
  }
}

@media (max-width: 480px) {
  .menu-grid {
    grid-template-columns: 1fr;
  }
  
  .menu-item-actions {
    flex-direction: column;
  }
  
  .menu-action-btn {
    flex: none;
  }
  
  .menu-stats {
    grid-template-columns: 1fr;
  }
  
  .menu-item-meta {
    flex-direction: column;
    gap: var(--menu-spacing-xs);
  }
}




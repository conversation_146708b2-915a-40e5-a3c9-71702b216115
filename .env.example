# ==================================================
# DESHA COFFEE - ENVIRONMENT CONFIGURATION EXAMPLE
# ==================================================
# انسخ هذا الملف إلى .env وأدخل القيم الحقيقية

# ==================================================
# FRONTEND API CONFIGURATION
# ==================================================
VITE_API_URL=https://your-backend-domain.com
VITE_API_TIMEOUT=30000
VITE_API_RETRY_ATTEMPTS=3
VITE_API_RETRY_DELAY=1000

# ==================================================
# SOCKET.IO CONFIGURATION
# ==================================================
VITE_SOCKET_URL=https://your-backend-domain.com
VITE_SOCKET_TIMEOUT=20000
VITE_SOCKET_RECONNECTION_ATTEMPTS=5
VITE_SOCKET_RECONNECTION_DELAY=1000

# ==================================================
# SERVER CONFIGURATION
# ==================================================
PORT=4003
NODE_ENV=development
BACKEND_PORT=4003
VITE_FRONTEND_PORT=5176

# ==================================================
# DATABASE CONFIGURATION
# ==================================================
# MongoDB Atlas Connection String
# استبدل username وpassword بالقيم الحقيقية
MONGODB_URI=mongodb+srv://username:<EMAIL>/deshacoffee?retryWrites=true&w=majority

# Database Pool Settings
DB_MAX_POOL_SIZE=10
DB_MIN_POOL_SIZE=1
DB_MAX_IDLE_TIME_MS=30000

# ==================================================
# AUTHENTICATION & SECURITY
# ==================================================
# JWT Configuration - اجعل هذا المفتاح معقدًا وطويلًا
JWT_SECRET=your-super-secret-jwt-key-here-make-it-very-long-and-random
JWT_EXPIRES_IN=7d

# Password Hashing
BCRYPT_SALT_ROUNDS=12

# Session Security
SESSION_SECRET=your-session-secret-key-here

# ==================================================
# CORS & API CONFIGURATION
# ==================================================
FRONTEND_URL=https://your-frontend-domain.com
BACKEND_URL=https://your-backend-domain.com
CORS_ORIGIN=https://your-frontend-domain.com
CORS_CREDENTIALS=true

# ==================================================
# SECURITY & RATE LIMITING
# ==================================================
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=100
HOST=0.0.0.0

# ==================================================
# APPLICATION SETTINGS
# ==================================================
VITE_APP_VERSION=1.0.0
VITE_APP_NAME=نظام إدارة المقهى
VITE_DEV_MODE=true

# ==================================================
# NOTIFICATIONS SETTINGS
# ==================================================
VITE_NOTIFICATIONS_ENABLED=true
VITE_NOTIFICATION_DURATION=5000
VITE_NOTIFICATION_POSITION=top-right
VITE_MAX_NOTIFICATIONS=5

# ==================================================
# AUTO REFRESH SETTINGS
# ==================================================
VITE_AUTO_REFRESH_ENABLED=true
VITE_ORDERS_REFRESH_INTERVAL=3000
VITE_TABLES_REFRESH_INTERVAL=5000
VITE_INVENTORY_REFRESH_INTERVAL=10000

# ==================================================
# SECURITY SETTINGS
# ==================================================
VITE_SESSION_TIMEOUT=86400000
VITE_AUTO_LOGOUT_WARNING=300000
VITE_MAX_LOGIN_ATTEMPTS=3

# ==================================================
# EMAIL CONFIGURATION (Optional)
# ==================================================
EMAIL_HOST=smtp.gmail.com
EMAIL_PORT=587
EMAIL_USER=<EMAIL>
EMAIL_PASS=your-app-password
EMAIL_FROM=<EMAIL>

# ==================================================
# FILE UPLOAD CONFIGURATION
# ==================================================
MAX_FILE_SIZE=10485760
UPLOAD_PATH=uploads/
ALLOWED_FILE_TYPES=jpg,jpeg,png,pdf

# ==================================================
# LOGGING CONFIGURATION
# ==================================================
LOG_LEVEL=info
DEBUG=false

# ==================================================
# FEATURES & INTEGRATIONS
# ==================================================
# Context7 Integration
VITE_CONTEXT7_ENABLED=false
VITE_CONTEXT7_URL=ws://localhost:3001
VITE_CONTEXT7_API_KEY=your_context7_api_key_here

# ==================================================
# DEVELOPMENT FLAGS
# ==================================================
ENABLE_SEED_DATA=false
ENABLE_TEST_ROUTES=false

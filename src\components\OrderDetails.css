.order-details {
  direction: rtl;
}

.order-header {
  margin-bottom: var(--spacing-lg);
  padding-bottom: var(--spacing-lg);
  border-bottom: 2px solid var(--border);
}

.order-info-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: var(--spacing-md);
}

.info-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: var(--spacing-sm) var(--spacing-md);
  background: rgba(0, 0, 0, 0.02);
  border-radius: var(--border-radius-md);
  border-right: 4px solid var(--primary);
}

.info-label {
  font-weight: var(--font-weight-medium);
  color: var(--text-secondary);
  font-size: var(--font-size-sm);
}

.info-value {
  font-weight: var(--font-weight-bold);
  color: var(--text-primary);
}

.order-number {
  color: var(--primary);
  font-size: var(--font-size-lg);
}

.status-badge {
  color: white !important;
  padding: var(--spacing-xs) var(--spacing-sm);
  border-radius: var(--border-radius-sm);
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-bold);
}

.total-price {
  color: var(--success);
  font-size: var(--font-size-lg);
}

/* تفاصيل الأصناف */
.order-items {
  margin-bottom: var(--spacing-lg);
}

.order-items h3 {
  color: var(--text-primary);
  margin-bottom: var(--spacing-md);
  font-size: var(--font-size-xl);
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
}

.order-items h3::before {
  content: "🍽️";
  font-size: var(--font-size-lg);
}

.items-list {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-md);
}

.item-card {
  background: var(--surface);
  border: 1px solid var(--border);
  border-radius: var(--border-radius-md);
  padding: var(--spacing-md);
  box-shadow: var(--shadow-sm);
  transition: all 0.2s;
}

.item-card:hover {
  box-shadow: var(--shadow-md);
  transform: translateY(-2px);
}

.item-name {
  font-size: var(--font-size-lg);
  font-weight: var(--font-weight-bold);
  color: var(--text-primary);
  margin-bottom: var(--spacing-sm);
}

.item-details {
  display: flex;
  gap: var(--spacing-lg);
  margin-bottom: var(--spacing-sm);
  flex-wrap: wrap;
}

.item-quantity,
.item-price,
.item-total {
  font-size: var(--font-size-sm);
  color: var(--text-secondary);
  background: rgba(0, 0, 0, 0.05);
  padding: var(--spacing-xs) var(--spacing-sm);
  border-radius: var(--border-radius-sm);
}

.item-total {
  color: var(--success);
  font-weight: var(--font-weight-medium);
}

.item-notes {
  background: rgba(255, 193, 7, 0.1);
  border: 1px solid rgba(255, 193, 7, 0.3);
  border-radius: var(--border-radius-sm);
  padding: var(--spacing-sm);
  font-size: var(--font-size-sm);
  color: var(--warning);
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
}

/* ملاحظات الطلب */
.order-notes {
  margin-bottom: var(--spacing-lg);
}

.order-notes h3 {
  color: var(--text-primary);
  margin-bottom: var(--spacing-md);
  font-size: var(--font-size-xl);
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
}

.notes-content {
  background: rgba(33, 150, 243, 0.1);
  border: 1px solid rgba(33, 150, 243, 0.3);
  border-radius: var(--border-radius-md);
  padding: var(--spacing-md);
  color: var(--info);
  display: flex;
  align-items: flex-start;
  gap: var(--spacing-sm);
  line-height: 1.6;
}

/* أزرار تغيير الحالة */
.status-actions h3 {
  color: var(--text-primary);
  margin-bottom: var(--spacing-md);
  font-size: var(--font-size-xl);
}

.status-buttons {
  display: flex;
  gap: var(--spacing-md);
  flex-wrap: wrap;
}

/* تنسيق خاص للوضع المظلم */
[data-theme="dark"] .info-item {
  background: rgba(255, 255, 255, 0.05);
}

[data-theme="dark"] .item-card {
  background: var(--surface);
  border-color: rgba(255, 255, 255, 0.1);
}

[data-theme="dark"] .item-quantity,
[data-theme="dark"] .item-price,
[data-theme="dark"] .item-total {
  background: rgba(255, 255, 255, 0.05);
}

[data-theme="dark"] .item-notes {
  background: rgba(255, 193, 7, 0.15);
  border-color: rgba(255, 193, 7, 0.4);
}

[data-theme="dark"] .notes-content {
  background: rgba(33, 150, 243, 0.15);
  border-color: rgba(33, 150, 243, 0.4);
}

/* تصميم متجاوب */
@media (max-width: 768px) {
  .order-info-grid {
    grid-template-columns: 1fr;
  }
  
  .info-item {
    flex-direction: column;
    align-items: flex-start;
    gap: var(--spacing-xs);
  }
  
  .item-details {
    flex-direction: column;
    gap: var(--spacing-sm);
  }
  
  .status-buttons {
    flex-direction: column;
  }
  
  .status-buttons .btn {
    width: 100%;
  }
}

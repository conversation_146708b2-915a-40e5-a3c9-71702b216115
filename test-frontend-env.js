// Test script to check frontend environment configuration
const APP_CONFIG = {
  API: {
    BASE_URL: process.env.VITE_API_URL || 'https://deshacoffee-production.up.railway.app',
  }
};

console.log('🔍 Frontend Environment Test:');
console.log('📄 VITE_API_URL:', process.env.VITE_API_URL);
console.log('🌐 APP_CONFIG.API.BASE_URL:', APP_CONFIG.API.BASE_URL);
console.log('🎯 Expected:', 'http://localhost:5001');

// Test URL construction
const getApiUrl = (endpoint) => {
  return `${APP_CONFIG.API.BASE_URL}${endpoint.startsWith('/') ? endpoint : '/' + endpoint}`;
};

console.log('\n🔧 URL Construction Test:');
console.log('Login URL:', getApiUrl('/api/v1/auth/login'));
console.log('Health URL:', getApiUrl('/health'));

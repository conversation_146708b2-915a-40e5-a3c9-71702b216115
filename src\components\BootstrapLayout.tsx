import React, { useState, useEffect } from 'react';
import <PERSON><PERSON><PERSON>Header from './BootstrapHeader';
import BootstrapSidebar from './BootstrapSidebar';

interface BootstrapLayoutProps {
  user?: any;
  onLogout?: () => void;
  children: React.ReactNode;
  activeScreen: string;
  onNavigate: (screen: string) => void;
}

const BootstrapLayout: React.FC<BootstrapLayoutProps> = ({
  user,
  onLogout,
  children,
  activeScreen,
  onNavigate
}) => {
  const [sidebarOpen, setSidebarOpen] = useState(false);
  const [isLargeScreen, setIsLargeScreen] = useState(window.innerWidth >= 992);

  // متابعة تغيير حجم الشاشة
  useEffect(() => {
    const handleResize = () => {
      const isLarge = window.innerWidth >= 992;
      setIsLargeScreen(isLarge);
      
      // في الشاشات الكبيرة، نُظهر القائمة الجانبية افتراضياً
      if (isLarge && !sidebarOpen) {
        setSidebarOpen(true);
      }
    };

    window.addEventListener('resize', handleResize);
    handleResize(); // تشغيل مرة واحدة في البداية

    return () => window.removeEventListener('resize', handleResize);
  }, [sidebarOpen]);

  const handleToggleSidebar = () => {
    setSidebarOpen(!sidebarOpen);
  };

  const handleCloseSidebar = () => {
    setSidebarOpen(false);
  };

  return (
    <div className="manager-dashboard">
      {/* Header */}
      <BootstrapHeader
        user={user}
        onLogout={onLogout}
        onToggleSidebar={handleToggleSidebar}
        sidebarOpen={sidebarOpen}
      />

      {/* Sidebar */}
      <BootstrapSidebar
        isOpen={sidebarOpen}
        onClose={handleCloseSidebar}
        activeScreen={activeScreen}
        onNavigate={onNavigate}
      />

      {/* Main Content */}
      <main 
        className={`main-content ${
          sidebarOpen && isLargeScreen ? 'sidebar-open' : ''
        }`}
      >
        <div className="container-fluid h-100">
          <div className="row h-100">
            <div className="col-12">
              {children}
            </div>
          </div>
        </div>
      </main>
    </div>
  );
};

export default BootstrapLayout;

// Service Worker للـ PWA مع دعم Push Notifications
const CACHE_NAME = 'coffee-shop-v1';
const urlsToCache = [
  '/',
  '/coffee-logo.svg',
  '/coffee-cup.svg',
  '/manifest.json',
  '/notification.wav'
];

// تثبيت Service Worker
self.addEventListener('install', (event) => {
  console.log('🔧 تثبيت Service Worker...');
  event.waitUntil(
    caches.open(CACHE_NAME)
      .then((cache) => {
        console.log('✅ فتح الكاش');
        return cache.addAll(urlsToCache);
      })
      .then(() => {
        console.log('✅ تم تثبيت Service Worker بنجاح');
        // التفعيل الفوري
        return self.skipWaiting();
      })
  );
});

// تحديث Service Worker
self.addEventListener('activate', (event) => {
  console.log('🚀 تفعيل Service Worker...');
  event.waitUntil(
    caches.keys().then((cacheNames) => {
      return Promise.all(
        cacheNames.map((cacheName) => {
          if (cacheName !== CACHE_NAME) {
            console.log('🗑️ حذف الكاش القديم:', cacheName);
            return caches.delete(cacheName);
          }
        })
      );
    }).then(() => {
      console.log('✅ تم تفعيل Service Worker بنجاح');
      // السيطرة على جميع العملاء
      return self.clients.claim();
    })
  );
});

// استقبال Push Notifications
self.addEventListener('push', (event) => {
  console.log('📨 استقبال Push Notification:', event);
  
  let notificationData = {
    title: 'إشعار من نظام القهوة',
    body: 'إشعار جديد',
    icon: '/coffee-logo.svg',
    badge: '/coffee-cup.svg',
    tag: 'coffee-notification',
    renotify: true,
    requireInteraction: true,
    silent: false,
    vibrate: [200, 100, 200],
    actions: [
      {
        action: 'view',
        title: 'عرض',
        icon: '/coffee-logo.svg'
      },
      {
        action: 'dismiss',
        title: 'إخفاء'
      }
    ]
  };

  // استخراج البيانات من Push message
  if (event.data) {
    try {
      const data = event.data.json();
      notificationData = { ...notificationData, ...data };
    } catch (error) {
      console.error('❌ خطأ في معالجة بيانات Push:', error);
      notificationData.body = event.data.text() || notificationData.body;
    }
  }

  console.log('📱 عرض الإشعار:', notificationData);

  event.waitUntil(
    self.registration.showNotification(notificationData.title, notificationData)
  );
});

// التعامل مع النقر على الإشعار
self.addEventListener('notificationclick', (event) => {
  console.log('👆 تم النقر على الإشعار:', event);
  
  event.notification.close();

  if (event.action === 'dismiss') {
    return;
  }

  // فتح التطبيق أو التركيز عليه
  event.waitUntil(
    self.clients.matchAll({ type: 'window' }).then((clients) => {
      // البحث عن نافذة مفتوحة
      for (const client of clients) {
        if (client.url.includes(self.location.origin) && 'focus' in client) {
          return client.focus();
        }
      }
      
      // فتح نافذة جديدة إذا لم توجد
      if (self.clients.openWindow) {
        return self.clients.openWindow('/');
      }
    })
  );
});

// التعامل مع إغلاق الإشعار
self.addEventListener('notificationclose', (event) => {
  console.log('❌ تم إغلاق الإشعار:', event);
});

// استقبال الرسائل من التطبيق الرئيسي
self.addEventListener('message', (event) => {
  console.log('💬 رسالة من التطبيق الرئيسي:', event.data);
  
  if (event.data && event.data.type === 'SHOW_NOTIFICATION') {
    const notificationData = {
      title: event.data.title || 'إشعار من نظام القهوة',
      body: event.data.body || 'إشعار جديد',
      icon: '/coffee-logo.svg',
      badge: '/coffee-cup.svg',
      tag: event.data.tag || 'coffee-notification',
      renotify: true,
      requireInteraction: true,
      silent: false,
      vibrate: [200, 100, 200],
      data: event.data.data || {}
    };

    self.registration.showNotification(notificationData.title, notificationData);
  }
  
  // معالجة طلب تشغيل الصوت
  if (event.data && event.data.type === 'PLAY_NOTIFICATION_SOUND') {
    // إرسال رسالة لجميع العملاء لتشغيل الصوت
    self.clients.matchAll({ type: 'window' }).then((clients) => {
      clients.forEach((client) => {
        client.postMessage({
          type: 'PLAY_SOUND_REQUEST',
          urgent: true
        });
      });
    });
  }
  
  // معالجة تفعيل/تعطيل وضع الخلفية
  if (event.data && event.data.type === 'ENABLE_BACKGROUND_MODE') {
    console.log('🌙 تفعيل وضع الخلفية في Service Worker');
    // يمكن إضافة منطق خاص بوضع الخلفية هنا
  }
  
  if (event.data && event.data.type === 'DISABLE_BACKGROUND_MODE') {
    console.log('☀️ تعطيل وضع الخلفية في Service Worker');
    // يمكن إضافة منطق خاص بوضع المقدمة هنا
  }
});

// جلب الملفات من الكاش
self.addEventListener('fetch', (event) => {
  const url = event.request.url;

  // تجاهل جميع ملفات JavaScript/CSS المُولدة من Vite
  if (url.includes('/assets/') ||
      url.includes('/@vite/') ||
      url.includes('/@react-refresh') ||
      url.includes('/src/') ||
      url.includes('.js') ||
      url.includes('.css') ||
      url.includes('.tsx') ||
      url.includes('.ts') ||
      url.includes('index-') ||
      url.includes('.map')) {
    // السماح للطلب بالمرور مباشرة بدون تدخل
    return;
  }

  // فقط cache الملفات الثابتة الأساسية
  if (url.includes('.svg') || url.includes('.png') || url.includes('.jpg') || url.includes('manifest.json')) {
    event.respondWith(
      caches.match(event.request)
        .then((response) => {
          return response || fetch(event.request);
        })
        .catch(() => fetch(event.request))
    );
  }
});

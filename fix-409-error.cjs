const https = require('https');

// معلومات الاتصال
const backEndURL = 'https://deshacoffee-production.up.railway.app';

// بيانات الحسابات
const accounts = {
  manager: {
    username: '<PERSON><PERSON>',
    password: 'MOHAMEDmostafa123',
    role: 'مدير'
  },
  waiter1: {
    username: 'azz',
    password: '253040',
    role: 'نادل'
  },
  waiter2: {
    username: '<PERSON><PERSON>',
    password: '253040',
    role: 'نادل'
  },
  chef: {
    username: 'khaled',
    password: '253040',
    role: 'طباخ'
  }
};

// دالة مساعدة لإرسال طلبات HTTP
function makeRequest(url, options, data = null) {
  return new Promise((resolve, reject) => {
    const req = https.request(url, options, (res) => {
      let body = '';
      res.on('data', chunk => body += chunk);
      res.on('end', () => {
        try {
          const jsonData = JSON.parse(body);
          resolve({ status: res.statusCode, data: jsonData, headers: res.headers });
        } catch (e) {
          resolve({ status: res.statusCode, data: body, headers: res.headers });
        }
      });
    });

    req.on('error', reject);
    
    if (data) {
      req.write(JSON.stringify(data));
    }
    
    req.end();
  });
}

// دالة تسجيل الدخول
async function login(account) {
  console.log(`🔐 محاولة تسجيل الدخول: ${account.username} (${account.role})`);
  
  const options = {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json'
    }
  };

  try {
    const response = await makeRequest(`${backEndURL}/api/auth/login`, options, {
      username: account.username,
      password: account.password
    });

    if (response.status === 200 && response.data.token) {
      console.log(`✅ تم تسجيل الدخول بنجاح: ${account.username}`);
      return {
        token: response.data.token,
        user: response.data.user
      };
    } else {
      console.log(`❌ فشل تسجيل الدخول: ${account.username}`, response.data);
      return null;
    }
  } catch (error) {
    console.error(`❌ خطأ في تسجيل الدخول: ${account.username}`, error.message);
    return null;
  }
}

// دالة فحص الطاولات
async function checkTables(token) {
  console.log('🔍 فحص حالة الطاولات...');
  
  const options = {
    method: 'GET',
    headers: {
      'Authorization': `Bearer ${token}`,
      'Content-Type': 'application/json'
    }
  };

  try {
    const response = await makeRequest(`${backEndURL}/api/tables`, options);
    
    if (response.status === 200) {
      console.log('📋 حالة الطاولات:');
      
      let tablesData = [];
      
      if (Array.isArray(response.data)) {
        tablesData = response.data;
      } else if (response.data && Array.isArray(response.data.data)) {
        tablesData = response.data.data;
      } else {
        console.log('⚠️ تنسيق غير متوقع لبيانات الطاولات');
        return [];
      }
      
      tablesData.forEach(table => {
        const isOccupied = table.assignedWaiter ? 'محجوزة' : 'متاحة';
        const waiterInfo = table.assignedWaiter ? 
          (table.assignedWaiter.username || table.assignedWaiter.name || table.assignedWaiter._id || 'غير محدد') : '';
        console.log(`📍 طاولة ${table.number}: ${isOccupied} ${waiterInfo ? `للنادل: ${waiterInfo}` : ''}`);
      });
      
      // عد الطاولات المحجوزة
      const occupiedTables = tablesData.filter(table => table.assignedWaiter);
      console.log(`📊 إجمالي الطاولات المحجوزة: ${occupiedTables.length}`);
      
      return tablesData;
    } else {
      console.log('❌ فشل في جلب بيانات الطاولات:', response.data);
      return [];
    }
  } catch (error) {
    console.error('❌ خطأ في فحص الطاولات:', error.message);
    return [];
  }
}

// دالة تحرير طاولة معينة
async function releaseTable(token, tableNumber) {
  console.log(`🔓 محاولة تحرير الطاولة ${tableNumber}...`);
  
  const options = {
    method: 'PUT',
    headers: {
      'Authorization': `Bearer ${token}`,
      'Content-Type': 'application/json'
    }
  };

  try {
    const response = await makeRequest(`${backEndURL}/api/tables/${tableNumber}/release`, options);
    
    if (response.status === 200) {
      console.log(`✅ تم تحرير الطاولة ${tableNumber} بنجاح`);
      return true;
    } else {
      console.log(`❌ فشل في تحرير الطاولة ${tableNumber}:`, response.data);
      return false;
    }
  } catch (error) {
    console.error(`❌ خطأ في تحرير الطاولة ${tableNumber}:`, error.message);
    return false;
  }
}

// دالة تحرير جميع الطاولات
async function releaseAllTables(token) {
  console.log('🔓 محاولة تحرير جميع الطاولات...');
  
  const options = {
    method: 'POST',
    headers: {
      'Authorization': `Bearer ${token}`,
      'Content-Type': 'application/json'
    }
  };

  try {
    const response = await makeRequest(`${backEndURL}/api/tables/release-all`, options);
    
    if (response.status === 200) {
      console.log('✅ تم تحرير جميع الطاولات بنجاح');
      return true;
    } else {
      console.log('❌ فشل في تحرير جميع الطاولات:', response.data);
      return false;
    }
  } catch (error) {
    console.error('❌ خطأ في تحرير جميع الطاولات:', error.message);
    return false;
  }
}

// دالة اختبار إرسال طلب
async function testOrderSubmission(token, user) {
  console.log(`🧪 اختبار إرسال طلب بواسطة: ${user.username}`);
  
  // أولاً، دعنا نحاول طاولة متاحة
  const availableTableNumbers = [2, 4, 5, 6, 7, 9, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 24, 25, 26, 27, 28];
  
  for (const tableNumber of availableTableNumbers) {
    console.log(`🧪 محاولة الطاولة ${tableNumber}...`);
    
    const testOrder = {
      items: [
        {
          name: 'قهوة عربية',
          price: 15,
          quantity: 1,
          category: 'مشروبات ساخنة'
        }
      ],
      customerName: 'عميل تجريبي',
      tableNumber: tableNumber,
      notes: 'طلب تجريبي للاختبار',
      waiterName: user.username,
      waiterId: user._id
    };

    const options = {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${token}`,
        'Content-Type': 'application/json'
      }
    };

    try {
      const response = await makeRequest(`${backEndURL}/api/orders`, options, testOrder);
      
      console.log(`📊 رمز الاستجابة للطاولة ${tableNumber}: ${response.status}`);
      
      if (response.status === 409) {
        console.log(`❌ الطاولة ${tableNumber} محجوزة: ${response.data.message}`);
        continue; // جرب الطاولة التالية
      } else if (response.status >= 200 && response.status < 300) {
        console.log(`✅ تم إرسال الطلب بنجاح للطاولة ${tableNumber}!`);
        console.log('📦 بيانات الطلب:', response.data);
        return true;
      } else {
        console.log(`❌ فشل إرسال الطلب للطاولة ${tableNumber}: ${response.status}`);
        console.log('📝 تفاصيل الخطأ:', response.data);
      }
    } catch (error) {
      console.error(`❌ خطأ في اختبار الطاولة ${tableNumber}:`, error.message);
    }
  }
  
  console.log('❌ فشل في إرسال طلب لجميع الطاولات المتاحة');
  return false;
}

// الدالة الرئيسية
async function main() {
  console.log('🚀 بدء تشخيص وحل مشكلة الخطأ 409...\n');
  
  // 1. تسجيل الدخول كمدير
  console.log('=== المرحلة 1: تسجيل الدخول كمدير ===');
  const managerAuth = await login(accounts.manager);
  
  if (!managerAuth) {
    console.log('❌ فشل في تسجيل الدخول كمدير. لا يمكن المتابعة.');
    return;
  }
  
  // 2. فحص حالة الطاولات
  console.log('\n=== المرحلة 2: فحص حالة الطاولات ===');
  const tables = await checkTables(managerAuth.token);
  
  // 3. تحرير الطاولات المحجوزة
  const occupiedTables = tables.filter(table => table.assignedWaiter);
  
  if (occupiedTables.length > 0) {
    console.log('\n=== المرحلة 3: تحرير الطاولات المحجوزة ===');
    
    // محاولة تحرير جميع الطاولات دفعة واحدة
    const allReleased = await releaseAllTables(managerAuth.token);
    
    if (!allReleased) {
      // إذا فشل التحرير الجماعي، حرر كل طاولة على حدة
      console.log('⚠️ فشل التحرير الجماعي، محاولة تحرير كل طاولة على حدة...');
      
      for (const table of occupiedTables) {
        await releaseTable(managerAuth.token, table.number);
      }
    }
    
    // إعادة فحص الطاولات
    console.log('\n📋 إعادة فحص حالة الطاولات بعد التحرير:');
    await checkTables(managerAuth.token);
  } else {
    console.log('✅ جميع الطاولات متاحة (غير محجوزة)');
  }
  
  // 4. اختبار إرسال طلب بواسطة النادل
  console.log('\n=== المرحلة 4: اختبار إرسال طلب ===');
  
  // تسجيل الدخول كنادل
  const waiterAuth = await login(accounts.waiter2); // Bosy
  
  if (waiterAuth) {
    const orderSuccess = await testOrderSubmission(waiterAuth.token, waiterAuth.user);
    
    if (orderSuccess) {
      console.log('\n🎉 تم حل المشكلة بنجاح! يمكن للنادل إرسال الطلبات الآن.');
    } else {
      console.log('\n⚠️ لا تزال هناك مشكلة في إرسال الطلبات.');
    }
  } else {
    console.log('\n❌ فشل في تسجيل الدخول كنادل للاختبار.');
  }
  
  console.log('\n✅ انتهى التشخيص والإصلاح.');
}

// تشغيل البرنامج
main().catch(error => {
  console.error('❌ خطأ عام في البرنامج:', error);
});

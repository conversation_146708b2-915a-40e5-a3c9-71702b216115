<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>تشخيص ConnectionStatus - نظام المقهى</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            padding: 20px;
            direction: rtl;
            text-align: right;
        }
        .result {
            margin: 10px 0;
            padding: 10px;
            border-radius: 3px;
            border: 1px solid #ddd;
        }
        .success {
            background-color: #d4edda;
            border-color: #c3e6cb;
        }
        .error {
            background-color: #f8d7da;
            border-color: #f5c6cb;
        }
        pre {
            background-color: #f8f9fa;
            padding: 10px;
            border-radius: 3px;
            overflow-x: auto;
            font-size: 12px;
        }
        button {
            padding: 10px 20px;
            margin: 5px;
            border: none;
            border-radius: 3px;
            cursor: pointer;
            background-color: #007bff;
            color: white;
        }
    </style>
</head>
<body>
    <h1>تشخيص ConnectionStatus</h1>
    
    <button onclick="testConnectionLogic()">اختبار منطق ConnectionStatus</button>
    <div id="result"></div>

    <script>
        // نسخ إعدادات الـ API من التطبيق الأصلي
        const API_BASE_URL = 'http://localhost:5000';
        const REQUEST_TIMEOUT = 30000;

        const getApiUrl = (endpoint) => {
            return `${API_BASE_URL}${endpoint.startsWith('/') ? endpoint : '/' + endpoint}`;
        };

        const makeRequest = async (endpoint, options = {}) => {
            const controller = new AbortController();
            const timeoutId = setTimeout(() => controller.abort(), REQUEST_TIMEOUT);

            try {
                const token = localStorage.getItem('token') || localStorage.getItem('authToken');
                const fullUrl = getApiUrl(endpoint);
                
                console.log('🌐 makeRequest called:');
                console.log('  - endpoint:', endpoint);
                console.log('  - API_BASE_URL:', API_BASE_URL);
                console.log('  - full URL:', fullUrl);

                const response = await fetch(fullUrl, {
                    ...options,
                    signal: controller.signal,
                    headers: {
                        'Content-Type': 'application/json',
                        'Accept': 'application/json',
                        'x-request-id': Math.random().toString(36).substring(7),
                        ...(token && { 'Authorization': `Bearer ${token}` }),
                        ...options.headers,
                    },
                });

                console.log('🌐 Response received:', {
                    status: response.status,
                    statusText: response.statusText,
                    ok: response.ok
                });

                clearTimeout(timeoutId);

                if (!response.ok) {
                    let errorMessage = `خطأ HTTP ${response.status}`;
                    try {
                        const errorData = await response.clone().json();
                        errorMessage = errorData.message || errorData.error || errorMessage;
                    } catch {
                        // استخدم الرسالة الافتراضية
                    }
                    throw new Error(errorMessage);
                }

                const data = await response.json();
                console.log('🌐 Response data:', data);
                return { success: true, data };
            } catch (error) {
                clearTimeout(timeoutId);
                console.error('❌ API Request Error:', error);
                return {
                    success: false,
                    error: error.message || 'حدث خطأ في الشبكة'
                };
            }
        };

        const checkServerHealth = async () => {
            console.log('🏥 checkServerHealth called with API_BASE_URL:', API_BASE_URL);
            return makeRequest('/health');
        };

        async function testConnectionLogic() {
            document.getElementById('result').innerHTML = '<div class="result">جاري الاختبار...</div>';
            
            try {
                console.log('🔍 Starting health check...');
                console.log('🔍 API URL being used:', API_BASE_URL);
                const response = await checkServerHealth();
                console.log('🔍 Health check response:', response);

                // تحقق من صحة الاستجابة - makeRequest يرجع { success: true, data: healthData }
                const healthData = response.data;
                console.log('📊 Health data:', healthData);
                console.log('📊 Response success:', response.success);
                console.log('📊 Health status:', healthData?.status);
                console.log('📊 Database connected:', healthData?.database?.connected);
                
                // التحقق من حالة الاتصال - نفس المنطق من ConnectionStatus.tsx
                const isServerHealthy = response.success === true && healthData?.status === 'healthy';
                const isDatabaseConnected = response.success === true && healthData?.database?.connected === true;

                console.log('✅ Server healthy:', isServerHealthy);
                console.log('✅ Database connected:', isDatabaseConnected);
                console.log('✅ Overall connection:', isServerHealthy && isDatabaseConnected);
                console.log('🔍 Response.success check:', response.success === true);
                console.log('🔍 HealthData.status check:', healthData?.status === 'healthy');
                console.log('🔍 Database.connected check:', healthData?.database?.connected === true);

                const isConnected = isServerHealthy && isDatabaseConnected;
                const serverStatus = isServerHealthy ? 'متصل' : 'غير متصل';
                const databaseStatus = isDatabaseConnected ? 'متصل' : 'غير متصل';

                const resultHtml = `
                    <div class="result ${isConnected ? 'success' : 'error'}">
                        <h3>نتائج الاختبار:</h3>
                        <p><strong>الحالة العامة:</strong> ${isConnected ? 'متصل' : 'غير متصل'}</p>
                        <p><strong>الخادم:</strong> ${serverStatus}</p>
                        <p><strong>قاعدة البيانات:</strong> ${databaseStatus}</p>
                        <h4>تفاصيل فنية:</h4>
                        <pre>${JSON.stringify({
                            response: response,
                            healthData: healthData,
                            isServerHealthy: isServerHealthy,
                            isDatabaseConnected: isDatabaseConnected,
                            checks: {
                                'response.success === true': response.success === true,
                                'healthData?.status === "healthy"': healthData?.status === 'healthy',
                                'healthData?.database?.connected === true': healthData?.database?.connected === true
                            }
                        }, null, 2)}</pre>
                    </div>
                `;
                
                document.getElementById('result').innerHTML = resultHtml;

            } catch (error) {
                console.error('❌ Connection check failed:', error);
                document.getElementById('result').innerHTML = `
                    <div class="result error">
                        <h3>خطأ في الاختبار:</h3>
                        <p>${error.message}</p>
                        <pre>${JSON.stringify(error, null, 2)}</pre>
                    </div>
                `;
            }
        }

        // تشغيل الاختبار عند تحميل الصفحة
        window.onload = () => {
            setTimeout(testConnectionLogic, 500);
        };
    </script>
</body>
</html>

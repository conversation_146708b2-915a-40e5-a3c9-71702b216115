import React, { useState, useEffect, useMemo } from 'react';
import { authenticatedPut, authenticatedDelete } from '../utils/apiHelpers';
import { updateOrderStatus, updateOrder } from '../utils/api';
import { useToast } from '../hooks/useToast';
import socket from '../socket';
import '../styles/screens/OrdersScreenIsolated.css';

interface Order {
  _id: string;
  orderNumber: string;
  items: any[];
  totalAmount?: number;
  totalPrice?: number;
  totals?: {
    subtotal: number;
    tax: number;
    discount: number;
    total: number;
  };
  status: 'pending' | 'preparing' | 'ready' | 'completed' | 'delivered';
  tableNumber?: number;
  customerName?: string;
  waiterName?: string;
  waiterId?: string;
  chefName?: string;
  createdAt: string;
}

interface Employee {
  _id: string;
  username: string;
  name: string;
  role: string;
}

interface OrdersManagerScreenProps {
  orders: Order[];
  employees: Employee[];
  onOrdersUpdate: () => void;
  loading: boolean;
}

const OrdersManagerScreenBootstrap: React.FC<OrdersManagerScreenProps> = ({
  orders,
  employees,
  onOrdersUpdate,
  loading
}) => {
  const { showSuccess, showError } = useToast();
  
  const [orderStatusFilter, setOrderStatusFilter] = useState<'all' | 'pending' | 'preparing' | 'ready' | 'completed'>('all');
  const [waiterFilter, setWaiterFilter] = useState('all');
  const [dateFilter, setDateFilter] = useState('today');
  const [orderSearchTerm, setOrderSearchTerm] = useState('');
  const [selectedOrder, setSelectedOrder] = useState<Order | null>(null);
  const [showOrderDetailsModal, setShowOrderDetailsModal] = useState(false);
  const [showStatusChangeModal, setShowStatusChangeModal] = useState(false);
  const [orderToUpdate, setOrderToUpdate] = useState<Order | null>(null);
  const [sortBy, setSortBy] = useState<'date' | 'amount' | 'status'>('date');
  const [sortOrder, setSortOrder] = useState<'asc' | 'desc'>('desc');

  // Socket.IO event listeners للتحديثات الفورية
  useEffect(() => {
    const handleNewOrder = (orderData: any) => {
      console.log('📦 طلب جديد وارد:', orderData);
      onOrdersUpdate();
    };

    const handleOrderStatusUpdate = (orderUpdate: any) => {
      console.log('🔄 تحديث حالة الطلب:', orderUpdate);
      onOrdersUpdate();
    };

    const handleOrderUpdate = (orderUpdate: any) => {
      console.log('✏️ تحديث الطلب:', orderUpdate);
      onOrdersUpdate();
    };

    const handleOrderReady = (readyNotification: any) => {
      console.log('✅ طلب جاهز:', readyNotification);
      onOrdersUpdate();
    };

    // إضافة event listeners
    socket.on('new-order-notification', handleNewOrder);
    socket.on('order-status-update', handleOrderStatusUpdate);
    socket.on('order-updated', handleOrderUpdate);
    socket.on('order-ready-notification', handleOrderReady);

    // تنظيف event listeners
    return () => {
      socket.off('new-order-notification', handleNewOrder);
      socket.off('order-status-update', handleOrderStatusUpdate);
      socket.off('order-updated', handleOrderUpdate);
      socket.off('order-ready-notification', handleOrderReady);
    };
  }, [onOrdersUpdate]);

  // دالة حساب إجمالي الطلب - تم نقلها هنا لتجنب خطأ hoisting
  const getOrderTotal = (order: Order) => {
    if (order.totalAmount && typeof order.totalAmount === 'number') {
      return order.totalAmount;
    } else if (order.totalPrice && typeof order.totalPrice === 'number') {
      return order.totalPrice;
    } else if (order.totals && order.totals.total && typeof order.totals.total === 'number') {
      return order.totals.total;
    } else if (order.items && Array.isArray(order.items)) {
      return order.items.reduce((sum, item) => {
        return sum + ((item.price || 0) * (item.quantity || 0));
      }, 0);
    }
    return 0;
  };

  // دالة حساب وقت التحضير
  const getPreparationTime = (order: Order) => {
    const createdAt = new Date(order.createdAt);
    const now = new Date();
    const diffMs = now.getTime() - createdAt.getTime();
    const diffMins = Math.floor(diffMs / 60000);
    
    if (diffMins < 60) {
      return `${diffMins} دقيقة`;
    } else {
      const hours = Math.floor(diffMins / 60);
      const remainingMins = diffMins % 60;
      return `${hours} ساعة و ${remainingMins} دقيقة`;
    }
  };

  // دالة لتحديد فئة CSS حسب قدم الطلب
  const getTimeDisplayClass = (order: Order) => {
    const createdAt = new Date(order.createdAt);
    const now = new Date();
    const diffMins = Math.floor((now.getTime() - createdAt.getTime()) / 60000);
    
    if (diffMins <= 15) {
      return 'ordersManagerScreen__time-display--recent';
    } else if (diffMins <= 45) {
      return 'ordersManagerScreen__time-display';
    } else if (diffMins <= 90) {
      return 'ordersManagerScreen__time-display--old';
    } else {
      return 'ordersManagerScreen__time-display--very-old';
    }
  };

  const handleDeleteOrder = async (orderId: string) => {
    if (!window.confirm('هل أنت متأكد من حذف هذا الطلب نهائياً؟ هذا الإجراء لا يمكن التراجع عنه.')) {
      return;
    }
    
    try {
      const response = await authenticatedDelete(`/api/v1/orders/${orderId}`);
      
      if (response.success) {
        showSuccess('تم حذف الطلب نهائياً');
        onOrdersUpdate();
      } else {
        showError(response.message || 'فشل في حذف الطلب');
      }
    } catch (error) {
      console.error('Error deleting order:', error);
      showError('حدث خطأ أثناء حذف الطلب');
    }
  };

  // دالة مساعدة لـ PATCH requests
  const authenticatedPatch = async (endpoint: string, data: any) => {
    try {
      const token = localStorage.getItem('token') || localStorage.getItem('authToken');
      const response = await fetch(endpoint, {
        method: 'PATCH',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`,
        },
        body: JSON.stringify(data),
      });
      
      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(`HTTP ${response.status}: ${JSON.stringify(errorData)}`);
      }
      
      return await response.json();
    } catch (error) {
      console.error('PATCH request failed:', error);
      throw error;
    }
  };

  const handleUpdateOrderStatus = async (orderId: string, newStatus: string) => {
    try {
      let response;
      
      // الطريقة الأولى: استخدام updateOrder مع الطلب الكامل
      if (orderToUpdate) {
        console.log('🔄 Trying updateOrder method...');
        try {
          response = await updateOrder(orderId, { ...orderToUpdate, status: newStatus });
          if (response.success) {
            showSuccess('تم تحديث حالة الطلب');
            onOrdersUpdate();
            setShowStatusChangeModal(false);
            setOrderToUpdate(null);
            return;
          }
        } catch (error) {
          console.log('❌ updateOrder failed:', error);
        }
      }
      
      // الطريقة الثانية: استخدام PATCH method
      console.log('🔄 Trying PATCH method...');
      try {
        response = await authenticatedPatch(`/api/v1/orders/${orderId}`, { status: newStatus });
        if (response.success) {
          showSuccess('تم تحديث حالة الطلب');
          onOrdersUpdate();
          setShowStatusChangeModal(false);
          setOrderToUpdate(null);
          return;
        }
      } catch (error) {
        console.log('❌ PATCH method failed:', error);
      }
      
      // الطريقة الثالثة: استخدام updateOrderStatus المخصص
      console.log('🔄 Trying updateOrderStatus method...');
      try {
        response = await updateOrderStatus(orderId, newStatus);
        if (response.success) {
          showSuccess('تم تحديث حالة الطلب');
          onOrdersUpdate();
          setShowStatusChangeModal(false);
          setOrderToUpdate(null);
          return;
        }
      } catch (error) {
        console.log('❌ updateOrderStatus failed:', error);
      }
      
      // الطريقة الرابعة: استخدام authenticatedPut مباشرة على المسار الأساسي
      console.log('🔄 Trying direct PUT method...');
      try {
        response = await authenticatedPut(`/api/v1/orders/${orderId}`, { status: newStatus });
        if (response.success) {
          showSuccess('تم تحديث حالة الطلب');
          onOrdersUpdate();
          setShowStatusChangeModal(false);
          setOrderToUpdate(null);
          return;
        }
      } catch (error) {
        console.log('❌ Direct PUT failed:', error);
      }
      
      // إذا فشلت جميع الطرق
      showError('فشل في تحديث حالة الطلب - جميع الطرق المتاحة لم تعمل');
      
    } catch (error) {
      console.error('❌ Error updating order status:', error);
      showError('حدث خطأ أثناء تحديث حالة الطلب - يرجى المحاولة مرة أخرى');
    }
  };

  const openStatusChangeModal = (order: Order) => {
    setOrderToUpdate(order);
    setShowStatusChangeModal(true);
  };

  // فلترة وترتيب الطلبات
  const filteredAndSortedOrders = useMemo(() => {
    let filtered = orders.filter(order => {
      let isValid = true;
      
      // فلتر الحالة
      if (orderStatusFilter !== 'all') {
        const orderStatus = order.status?.toLowerCase();
        const filterStatus = orderStatusFilter.toLowerCase();
        
        if (orderStatus !== filterStatus) {
          isValid = false;
        }
      }

      // فلتر النادل
      if (isValid && waiterFilter !== 'all') {
        const orderWaiter = order.waiterName?.toLowerCase() || '';
        
        const selectedWaiter = employees.find(emp => 
          emp.role === 'waiter' && emp.username === waiterFilter
        );
        
        const filterWaiterName = selectedWaiter?.name?.toLowerCase() || '';
        const filterWaiterUsername = selectedWaiter?.username?.toLowerCase() || '';
        const filterWaiterValue = waiterFilter.toLowerCase();
        
        const isWaiterMatch = (
          orderWaiter === filterWaiterName ||
          orderWaiter === filterWaiterUsername ||
          orderWaiter === filterWaiterValue
        );
        
        if (!isWaiterMatch) {
          isValid = false;
        }
      }

      // فلتر التاريخ
      if (isValid && dateFilter !== 'all') {
        const orderDate = new Date(order.createdAt);
        const today = new Date();
        let dateMatch = true;

        if (dateFilter === 'today') {
          dateMatch = today.toDateString() === orderDate.toDateString();
        } else if (dateFilter === 'week') {
          const weekStart = new Date(today);
          weekStart.setDate(today.getDate() - today.getDay());
          weekStart.setHours(0, 0, 0, 0);
          dateMatch = orderDate >= weekStart;
        } else if (dateFilter === 'month') {
          const monthStart = new Date(today.getFullYear(), today.getMonth(), 1);
          monthStart.setHours(0, 0, 0, 0);
          dateMatch = orderDate >= monthStart;
        }

        if (!dateMatch) {
          isValid = false;
        }
      }

      // فلتر البحث برقم الطلب
      if (isValid && orderSearchTerm.trim()) {
        const searchNumber = orderSearchTerm.trim().toLowerCase();
        const orderNumber = (order.orderNumber || order._id?.slice(-6) || '').toLowerCase();
        const orderIdMatch = order._id.toLowerCase().includes(searchNumber);
        const orderNumberMatch = orderNumber.includes(searchNumber);
        
        if (!orderIdMatch && !orderNumberMatch) {
          isValid = false;
        }
      }

      return isValid;
    });

    // ترتيب النتائج
    filtered.sort((a, b) => {
      let aValue, bValue;
      
      switch (sortBy) {
        case 'date':
          aValue = new Date(a.createdAt).getTime();
          bValue = new Date(b.createdAt).getTime();
          break;
        case 'amount':
          aValue = getOrderTotal(a);
          bValue = getOrderTotal(b);
          break;
        case 'status':
          aValue = a.status;
          bValue = b.status;
          break;
        default:
          aValue = new Date(a.createdAt).getTime();
          bValue = new Date(b.createdAt).getTime();
      }

      if (sortOrder === 'asc') {
        return aValue < bValue ? -1 : aValue > bValue ? 1 : 0;
      } else {
        return aValue > bValue ? -1 : aValue < bValue ? 1 : 0;
      }
    });

    return filtered;
  }, [orders, orderStatusFilter, waiterFilter, dateFilter, orderSearchTerm, employees, sortBy, sortOrder]);

  // إحصائيات النُدل
  const waiterStats = useMemo(() => {
    return employees.filter(emp => emp.role === 'waiter').map(waiter => {
      const waiterOrders = filteredAndSortedOrders.filter(order => {
        return order.waiterName === waiter.name ||
               order.waiterName?.toLowerCase() === waiter.username?.toLowerCase() ||
               order.waiterName?.toLowerCase() === waiter.name?.toLowerCase();
      });
      
      const waiterSales = waiterOrders.reduce((sum, order) => {
        return sum + getOrderTotal(order);
      }, 0);

      return {
        name: waiter.name || waiter.username,
        ordersCount: waiterOrders.length,
        sales: waiterSales
      };
    }).sort((a, b) => b.sales - a.sales);
  }, [employees, filteredAndSortedOrders]);

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'pending': return 'warning';
      case 'preparing': return 'info';
      case 'ready': return 'success';
      case 'completed': return 'primary';
      default: return 'secondary';
    }
  };

  const getStatusText = (status: string) => {
    switch (status) {
      case 'pending': return 'قيد الانتظار';
      case 'preparing': return 'قيد التحضير';
      case 'ready': return 'جاهز';
      case 'completed': return 'مكتمل';
      default: return status;
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'pending': return 'clock';
      case 'preparing': return 'utensils';
      case 'ready': return 'check-circle';
      case 'completed': return 'check-double';
      default: return 'question-circle';
    }
  };

  return (
    <div className="container-fluid p-4" dir="rtl">
      {/* Header Section */}
      <div className="row mb-4">
        <div className="col-12">
          <div className="card border-0 shadow-sm bg-gradient">
            <div className="card-body p-4">
              <div className="row align-items-center">
                <div className="col-lg-6">
                  <h2 className="h3 fw-bold text-dark mb-2">إدارة الطلبات</h2>
                  <p className="text-muted mb-0">متابعة وإدارة جميع الطلبات</p>
                </div>
                <div className="col-lg-6">
                  <div className="row g-2">
                    <div className="col-sm-6">
                      <div className="d-flex align-items-center">
                        <span className="me-2 text-muted">إجمالي المبيعات:</span>
                        <span className="badge bg-success fs-6">
                          {filteredAndSortedOrders.reduce((sum, order) => sum + getOrderTotal(order), 0).toFixed(2)} ج.م
                        </span>
                      </div>
                    </div>
                    <div className="col-sm-6">
                      <div className="d-flex align-items-center">
                        <span className="me-2 text-muted">متوسط الطلب:</span>
                        <span className="badge bg-info fs-6">
                          {filteredAndSortedOrders.length > 0 
                            ? (filteredAndSortedOrders.reduce((sum, order) => sum + getOrderTotal(order), 0) / filteredAndSortedOrders.length).toFixed(2)
                            : '0.00'
                          } ج.م
                        </span>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Statistics Cards */}
      <div className="row g-4 mb-4">
        <div className="col-xl-3 col-lg-6 col-md-6">
          <div className="card border-0 shadow-sm h-100 bg-primary bg-gradient text-white">
            <div className="card-body text-center">
              <i className="fas fa-shopping-cart fa-2x mb-2"></i>
              <h4 className="fw-bold">{filteredAndSortedOrders.length}</h4>
              <p className="mb-0 small">إجمالي الطلبات</p>
            </div>
          </div>
        </div>

        <div className="col-xl-3 col-lg-6 col-md-6">
          <div className="card border-0 shadow-sm h-100 bg-warning bg-gradient text-white">
            <div className="card-body text-center">
              <i className="fas fa-clock fa-2x mb-2"></i>
              <h4 className="fw-bold">{filteredAndSortedOrders.filter(o => o.status === 'pending').length}</h4>
              <p className="mb-0 small">قيد الانتظار</p>
            </div>
          </div>
        </div>

        <div className="col-xl-3 col-lg-6 col-md-6">
          <div className="card border-0 shadow-sm h-100 bg-info bg-gradient text-white">
            <div className="card-body text-center">
              <i className="fas fa-utensils fa-2x mb-2"></i>
              <h4 className="fw-bold">{filteredAndSortedOrders.filter(o => o.status === 'preparing').length}</h4>
              <p className="mb-0 small">قيد التحضير</p>
            </div>
          </div>
        </div>

        <div className="col-xl-3 col-lg-6 col-md-6">
          <div className="card border-0 shadow-sm h-100 bg-success bg-gradient text-white">
            <div className="card-body text-center">
              <i className="fas fa-check-circle fa-2x mb-2"></i>
              <h4 className="fw-bold">{filteredAndSortedOrders.filter(o => o.status === 'ready').length}</h4>
              <p className="mb-0 small">جاهز</p>
            </div>
          </div>
        </div>
      </div>

      {/* Filters and Controls */}
      <div className="row mb-4">
        <div className="col-12">
          <div className="card border-0 shadow-sm">
            <div className="card-body p-4">
              <div className="row g-3">
                {/* Search */}
                <div className="col-lg-3">
                  <div className="position-relative">
                    <input
                      type="text"
                      className="form-control form-control-lg pe-5"
                      placeholder="البحث برقم الطلب..."
                      value={orderSearchTerm}
                      onChange={(e) => setOrderSearchTerm(e.target.value)}
                    />
                    <i className="fas fa-search position-absolute top-50 end-0 translate-middle-y me-3 text-muted"></i>
                    {orderSearchTerm && (
                      <button
                        className="btn btn-link position-absolute top-50 start-0 translate-middle-y text-danger"
                        onClick={() => setOrderSearchTerm('')}
                        title="مسح البحث"
                      >
                        <i className="fas fa-times"></i>
                      </button>
                    )}
                  </div>
                </div>

                {/* Status Filter */}
                <div className="col-lg-2 col-md-4">
                  <select
                    className="form-select form-select-lg"
                    value={orderStatusFilter}
                    onChange={(e) => setOrderStatusFilter(e.target.value as any)}
                    title="تصفية حسب حالة الطلب"
                    aria-label="تصفية حسب حالة الطلب"
                  >
                    <option value="all">جميع الحالات</option>
                    <option value="pending">قيد الانتظار</option>
                    <option value="preparing">قيد التحضير</option>
                    <option value="ready">جاهز</option>
                    <option value="completed">مكتمل</option>
                  </select>
                </div>

                {/* Waiter Filter */}
                <div className="col-lg-2 col-md-4">
                  <select
                    className="form-select form-select-lg"
                    value={waiterFilter}
                    onChange={(e) => setWaiterFilter(e.target.value)}
                    title="تصفية حسب النادل"
                    aria-label="تصفية حسب النادل"
                  >
                    <option value="all">جميع النُدل</option>
                    {employees.filter(emp => emp.role === 'waiter').map(waiter => (
                      <option key={waiter._id} value={waiter.username}>
                        {waiter.name || waiter.username}
                      </option>
                    ))}
                  </select>
                </div>

                {/* Date Filter */}
                <div className="col-lg-2 col-md-4">
                  <select
                    className="form-select form-select-lg"
                    value={dateFilter}
                    onChange={(e) => setDateFilter(e.target.value)}
                    title="تصفية حسب التاريخ"
                    aria-label="تصفية حسب التاريخ"
                  >
                    <option value="all">جميع التواريخ</option>
                    <option value="today">اليوم</option>
                    <option value="week">هذا الأسبوع</option>
                    <option value="month">هذا الشهر</option>
                  </select>
                </div>

                {/* Sort By */}
                <div className="col-lg-2 col-md-6">
                  <select
                    className="form-select form-select-lg"
                    value={sortBy}
                    onChange={(e) => setSortBy(e.target.value as any)}
                    title="ترتيب حسب"
                    aria-label="ترتيب حسب"
                  >
                    <option value="date">التاريخ</option>
                    <option value="amount">المبلغ</option>
                    <option value="status">الحالة</option>
                  </select>
                </div>

                {/* Sort Order */}
                <div className="col-lg-1 col-md-6">
                  <div className="btn-group w-100" role="group">
                    <button
                      className={`btn btn-outline-primary ${sortOrder === 'desc' ? 'active' : ''}`}
                      onClick={() => setSortOrder('desc')}
                      title="تنازلي"
                    >
                      <i className="fas fa-sort-down"></i>
                    </button>
                    <button
                      className={`btn btn-outline-primary ${sortOrder === 'asc' ? 'active' : ''}`}
                      onClick={() => setSortOrder('asc')}
                      title="تصاعدي"
                    >
                      <i className="fas fa-sort-up"></i>
                    </button>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Orders Table */}
      <div className="row">
        <div className="col-12">
          <div className="card border-0 shadow-sm">
            <div className="card-header bg-transparent border-0 pb-0">
              <div className="d-flex justify-content-between align-items-center">
                <h5 className="card-title mb-0">قائمة الطلبات</h5>
                <span className="badge bg-primary fs-6">
                  {filteredAndSortedOrders.length} طلب
                </span>
              </div>
            </div>
            <div className="card-body p-0">
              {loading ? (
                <div className="text-center py-5">
                  <div className="spinner-border text-primary" role="status">
                    <span className="visually-hidden">جاري التحميل...</span>
                  </div>
                  <p className="mt-3 text-muted">جاري تحميل الطلبات...</p>
                </div>
              ) : filteredAndSortedOrders.length === 0 ? (
                <div className="text-center py-5">
                  <i className="fas fa-shopping-cart fa-4x text-muted mb-3"></i>
                  <h5 className="text-muted">لا توجد طلبات</h5>
                  <p className="text-muted">لا توجد طلبات مطابقة لمعايير البحث والفلترة الحالية</p>
                </div>
              ) : (
                <div className="ordersManagerScreen__table-container">
                  <table className="ordersManagerScreen__table table table-hover mb-0">
                    <thead>
                      <tr>
                        <th scope="col">رقم الطلب</th>
                        <th scope="col" className="d-none d-md-table-cell">الطاولة</th>
                        <th scope="col" className="d-none d-lg-table-cell">النادل</th>
                        <th scope="col">الحالة</th>
                        <th scope="col" className="d-none d-sm-table-cell">المبلغ</th>
                        <th scope="col" className="d-none d-lg-table-cell">الوقت</th>
                        <th scope="col">الإجراءات</th>
                      </tr>
                    </thead>
                    <tbody>
                      {filteredAndSortedOrders.map(order => (
                        <tr key={order._id} data-status={order.status}>
                          <td>
                            <div className="d-none d-sm-block">
                              <button
                                className="ordersManagerScreen__order-number-btn"
                                onClick={() => {
                                  setSelectedOrder(order);
                                  setShowOrderDetailsModal(true);
                                }}
                                title="اضغط لعرض تفاصيل الطلب"
                              >
                                #{order.orderNumber || order._id.slice(-6)}
                              </button>
                              {/* معلومات إضافية للشاشات الصغيرة */}
                              <div className="ordersManagerScreen__mobile-info d-md-none">
                                <small className="text-muted">
                                  {order.tableNumber && (
                                    <span className="ordersManagerScreen__table-badge me-1">
                                      <i className="fas fa-table"></i> طاولة {order.tableNumber}
                                    </span>
                                  )}
                                  <span className="text-muted d-block">{order.waiterName || 'غير محدد'}</span>
                                </small>
                                <small className="ordersManagerScreen__date-display">
                                  {new Date(order.createdAt).toLocaleTimeString('ar-EG', { 
                                    hour: '2-digit', 
                                    minute: '2-digit' 
                                  })}
                                </small>
                              </div>
                            </div>
                            
                            {/* Mobile Card Layout */}
                            <div className="d-sm-none">
                              <div className="ordersManagerScreen__mobile-order-header">
                                <button
                                  className="ordersManagerScreen__mobile-order-number"
                                  onClick={() => {
                                    setSelectedOrder(order);
                                    setShowOrderDetailsModal(true);
                                  }}
                                  title="اضغط لعرض تفاصيل الطلب"
                                >
                                  طلب #{order.orderNumber || order._id.slice(-6)}
                                </button>
                                <div className="ordersManagerScreen__mobile-order-time">
                                  {new Date(order.createdAt).toLocaleTimeString('ar-EG', { 
                                    hour: '2-digit', 
                                    minute: '2-digit' 
                                  })}
                                </div>
                              </div>
                              
                              <div className="ordersManagerScreen__mobile-order-info">
                                {order.tableNumber && (
                                  <div className="ordersManagerScreen__mobile-info-item">
                                    <span className="ordersManagerScreen__mobile-info-label">الطاولة</span>
                                    <span className="ordersManagerScreen__mobile-info-value">
                                      <i className="fas fa-table me-1"></i>
                                      {order.tableNumber}
                                    </span>
                                  </div>
                                )}
                                <div className="ordersManagerScreen__mobile-info-item">
                                  <span className="ordersManagerScreen__mobile-info-label">النادل</span>
                                  <span className="ordersManagerScreen__mobile-info-value">
                                    <i className="fas fa-user me-1"></i>
                                    {order.waiterName || 'غير محدد'}
                                  </span>
                                </div>
                              </div>
                              
                              <div className="ordersManagerScreen__mobile-status-amount">
                                <span className={`ordersManagerScreen__status-badge ordersManagerScreen__status-badge--${order.status}`}>
                                  <i className={`fas fa-${getStatusIcon(order.status)}`}></i>
                                  {getStatusText(order.status)}
                                </span>
                                <span className="ordersManagerScreen__amount">
                                  {getOrderTotal(order).toFixed(2)} ج.م
                                </span>
                              </div>
                            </div>
                          </td>
                          <td className="d-none d-md-table-cell">
                            {order.tableNumber ? (
                              <span className="ordersManagerScreen__table-badge">
                                <i className="fas fa-table me-1"></i>
                                {order.tableNumber}
                              </span>
                            ) : (
                              <span className="text-muted">-</span>
                            )}
                          </td>
                          <td className="d-none d-lg-table-cell">
                            <span className="text-dark">
                              {order.waiterName || 'غير محدد'}
                            </span>
                          </td>
                          <td className="d-none d-sm-table-cell">
                            <span className={`ordersManagerScreen__status-badge ordersManagerScreen__status-badge--${order.status}`}>
                              <i className={`fas fa-${getStatusIcon(order.status)}`}></i>
                              <span className="d-none d-sm-inline">{getStatusText(order.status)}</span>
                            </span>
                            {/* عرض المبلغ للشاشات الصغيرة */}
                            <div className="d-sm-none">
                              <small className="ordersManagerScreen__amount d-block">
                                {getOrderTotal(order).toFixed(2)} ج.م
                              </small>
                            </div>
                          </td>
                          <td className="d-none d-sm-table-cell">
                            <span className="ordersManagerScreen__amount">
                              {getOrderTotal(order).toFixed(2)} ج.م
                            </span>
                          </td>
                          <td className="d-none d-lg-table-cell">
                            <div className={`ordersManagerScreen__date-display ${getTimeDisplayClass(order)}`}>
                              {new Date(order.createdAt).toLocaleTimeString('ar-EG', { 
                                hour: '2-digit', 
                                minute: '2-digit' 
                              })}
                            </div>
                          </td>
                          <td>
                            {/* Desktop Actions */}
                            <div className="d-none d-sm-block">
                              <div className="ordersManagerScreen__actions-container">
                                <div className="btn-group-vertical btn-group-sm d-lg-none" role="group">
                                  <button
                                    className="btn btn-outline-primary btn-sm"
                                    onClick={() => {
                                      setSelectedOrder(order);
                                      setShowOrderDetailsModal(true);
                                    }}
                                    title="عرض التفاصيل"
                                  >
                                    <i className="fas fa-eye me-1"></i>
                                    تفاصيل
                                  </button>
                                  
                                  {order.status !== 'completed' && (
                                    <button
                                      className="btn btn-outline-success btn-sm"
                                      onClick={() => openStatusChangeModal(order)}
                                      title="تغيير الحالة"
                                    >
                                      <i className="fas fa-edit me-1"></i>
                                      حالة
                                    </button>
                                  )}
                                  
                                  <button
                                    className="btn btn-outline-danger btn-sm"
                                    onClick={() => handleDeleteOrder(order._id)}
                                    title="حذف الطلب"
                                  >
                                    <i className="fas fa-trash me-1"></i>
                                    حذف
                                  </button>
                                </div>

                                <div className="btn-group btn-group-sm d-none d-lg-flex" role="group">
                                  <button
                                    className="btn btn-outline-primary"
                                    onClick={() => {
                                      setSelectedOrder(order);
                                      setShowOrderDetailsModal(true);
                                    }}
                                    title="عرض التفاصيل"
                                  >
                                    <i className="fas fa-eye"></i>
                                  </button>
                                  
                                  {order.status !== 'completed' && (
                                    <button
                                      className="btn btn-outline-success"
                                      onClick={() => openStatusChangeModal(order)}
                                      title="تغيير الحالة"
                                    >
                                      <i className="fas fa-edit"></i>
                                    </button>
                                  )}
                                  
                                  <button
                                    className="btn btn-outline-danger"
                                    onClick={() => handleDeleteOrder(order._id)}
                                    title="حذف الطلب"
                                  >
                                    <i className="fas fa-trash"></i>
                                  </button>
                                </div>
                              </div>
                            </div>
                            
                            {/* Mobile Actions */}
                            <div className="d-sm-none">
                              <div className="ordersManagerScreen__mobile-actions">
                                <button
                                  className="btn btn-outline-primary btn-sm"
                                  onClick={() => {
                                    setSelectedOrder(order);
                                    setShowOrderDetailsModal(true);
                                  }}
                                  title="عرض التفاصيل"
                                >
                                  <i className="fas fa-eye me-1"></i>
                                  تفاصيل
                                </button>
                                
                                {order.status !== 'completed' && (
                                  <button
                                    className="btn btn-outline-success btn-sm"
                                    onClick={() => openStatusChangeModal(order)}
                                    title="تغيير الحالة"
                                  >
                                    <i className="fas fa-edit me-1"></i>
                                    حالة
                                  </button>
                                )}
                                
                                <button
                                  className="btn btn-outline-danger btn-sm"
                                  onClick={() => handleDeleteOrder(order._id)}
                                  title="حذف الطلب"
                                >
                                  <i className="fas fa-trash"></i>
                                </button>
                              </div>
                            </div>
                          </td>
                        </tr>
                      ))}
                    </tbody>
                  </table>
                </div>
              )}
            </div>
          </div>
        </div>
      </div>

      {/* Waiter Statistics */}
      {waiterStats.length > 0 && (
        <div className="row mt-4">
          <div className="col-12">
            <div className="card border-0 shadow-sm">
              <div className="card-header bg-transparent">
                <h5 className="card-title mb-0">إحصائيات النُدل</h5>
              </div>
              <div className="card-body">
                <div className="row g-3">
                  {waiterStats.slice(0, 6).map((waiter, index) => (
                    <div key={waiter.name} className="col-xl-2 col-lg-4 col-md-6">
                      <div className="card border h-100">
                        <div className="card-body text-center">
                          <div className="position-relative">
                            <i className="fas fa-user-tie fa-2x text-primary mb-2"></i>
                            {index < 3 && (
                              <span className={`position-absolute top-0 start-100 translate-middle badge rounded-pill ${
                                index === 0 ? 'bg-warning' : index === 1 ? 'bg-secondary' : 'bg-warning'
                              }`}>
                                {index + 1}
                              </span>
                            )}
                          </div>
                          <h6 className="card-title">{waiter.name}</h6>
                          <p className="card-text">
                            <small className="text-muted">{waiter.ordersCount} طلب</small><br />
                            <span className="fw-bold text-success">{waiter.sales.toFixed(2)} ج.م</span>
                          </p>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Order Details Modal */}
      {showOrderDetailsModal && selectedOrder && (
        <div className="modal-backdrop">
          <div className="modal fade show d-block ordersManagerScreen__order-details" tabIndex={-1}>
            <div className="modal-dialog modal-dialog-centered">
              <div className="modal-content">
                <div className="modal-header">
                  <h6 className="modal-title">
                    تفاصيل الطلب #{selectedOrder.orderNumber || selectedOrder._id.slice(-6)}
                  </h6>
                  <button
                    type="button"
                    className="btn-close"
                    onClick={() => setShowOrderDetailsModal(false)}
                    title="إغلاق النافذة"
                    aria-label="إغلاق النافذة"
                  ></button>
                </div>
                <div className="modal-body">
                  <div className="row g-1">
                    <div className="col-6">
                      <div className="info-item">
                        <span className="info-label">الطاولة:</span>
                        <span className="info-value">{selectedOrder.tableNumber || 'غير محدد'}</span>
                      </div>
                    </div>
                    <div className="col-6">
                      <div className="info-item">
                        <span className="info-label">النادل:</span>
                        <span className="info-value">{selectedOrder.waiterName || 'غير محدد'}</span>
                      </div>
                    </div>
                    <div className="col-6">
                      <div className="info-item">
                        <span className="info-label">الطباخ:</span>
                        <span className="info-value">{selectedOrder.chefName || 'غير محدد'}</span>
                      </div>
                    </div>
                    <div className="col-6">
                      <div className="info-item">
                        <span className="info-label">وقت التحضير:</span>
                        <span className="info-value prep-time">{getPreparationTime(selectedOrder)}</span>
                      </div>
                    </div>
                    <div className="col-6">
                      <div className="info-item">
                        <span className="info-label">الحالة:</span>
                        <span className={`status-badge status-${selectedOrder.status}`}>
                          <i className={`fas fa-${getStatusIcon(selectedOrder.status)}`}></i>
                          {getStatusText(selectedOrder.status)}
                        </span>
                      </div>
                    </div>
                    <div className="col-6">
                      <div className="info-item">
                        <span className="info-label">الوقت:</span>
                        <span className="info-value">{new Date(selectedOrder.createdAt).toLocaleTimeString('ar-EG', { hour: '2-digit', minute: '2-digit' })}</span>
                      </div>
                    </div>
                    <div className="col-12">
                      <div className="items-section">
                        <h6 className="items-title">الأصناف:</h6>
                        <div className="items-list">
                          {selectedOrder.items?.map((item, index) => (
                            <div key={index} className="item-row">
                              <span className="item-name">{item.name || 'صنف غير محدد'}</span>
                              <span className="item-calc">
                                {item.quantity || 1} × {(item.price || 0).toFixed(2)} = {((item.quantity || 1) * (item.price || 0)).toFixed(2)} ج.م
                              </span>
                            </div>
                          ))}
                        </div>
                      </div>
                    </div>
                    <div className="col-12">
                      <div className="total-section">
                        <span>المجموع الكلي:</span>
                        <span className="total-amount">{getOrderTotal(selectedOrder).toFixed(2)} ج.م</span>
                      </div>
                    </div>
                  </div>
                </div>
                <div className="modal-footer">
                  <button
                    type="button"
                    className="btn btn-sm btn-outline-secondary"
                    onClick={() => setShowOrderDetailsModal(false)}
                  >
                    إغلاق
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Status Change Modal */}
      {showStatusChangeModal && orderToUpdate && (
        <div className="modal-backdrop">
          <div className="modal fade show d-block ordersManagerScreen__status-modal" tabIndex={-1}>
            <div className="modal-dialog modal-dialog-centered modal-sm">
              <div className="modal-content">
                <div className="modal-header">
                  <h6 className="modal-title">
                    تغيير حالة الطلب #{orderToUpdate.orderNumber || orderToUpdate._id.slice(-6)}
                  </h6>
                  <button
                    type="button"
                    className="btn-close"
                    onClick={() => {
                      setShowStatusChangeModal(false);
                      setOrderToUpdate(null);
                    }}
                    title="إغلاق النافذة"
                    aria-label="إغلاق النافذة"
                  ></button>
                </div>
                <div className="modal-body">
                  <div className="current-status">
                    <span className="status-label">الحالة الحالية:</span>
                    <span className={`current-status-badge status-${orderToUpdate.status}`}>
                      <i className={`fas fa-${getStatusIcon(orderToUpdate.status)}`}></i>
                      {getStatusText(orderToUpdate.status)}
                    </span>
                  </div>
                  
                  <div className="status-options">
                    {orderToUpdate.status !== 'preparing' && (
                      <button
                        className="status-btn status-btn--preparing"
                        onClick={() => handleUpdateOrderStatus(orderToUpdate._id, 'preparing')}
                      >
                        <i className="fas fa-utensils"></i>
                        قيد التحضير
                      </button>
                    )}
                    
                    {orderToUpdate.status !== 'ready' && (
                      <button
                        className="status-btn status-btn--ready"
                        onClick={() => handleUpdateOrderStatus(orderToUpdate._id, 'ready')}
                      >
                        <i className="fas fa-check-circle"></i>
                        جاهز للتقديم
                      </button>
                    )}
                    
                    {orderToUpdate.status !== 'completed' && (
                      <button
                        className="status-btn status-btn--completed"
                        onClick={() => handleUpdateOrderStatus(orderToUpdate._id, 'completed')}
                      >
                        <i className="fas fa-check-double"></i>
                        مكتمل
                      </button>
                    )}
                  </div>
                </div>
                <div className="modal-footer">
                  <button
                    type="button"
                    className="btn btn-sm btn-outline-secondary"
                    onClick={() => {
                      setShowStatusChangeModal(false);
                      setOrderToUpdate(null);
                    }}
                  >
                    إلغاء
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default OrdersManagerScreenBootstrap;

import React, { useState, useEffect } from 'react';
import { authenticatedGet } from '../utils/apiHelpers';
// استدعاء ملف CSS المحسن الجديد
import '../styles/manager/ReportsManagerScreen.css';

interface DashboardStats {
  totalOrders: number;
  totalSales: number;
  activeEmployees: number;
  activeTables: number;
  pendingOrders: number;
  preparingOrders: number;
  readyOrders: number;
  completedOrders: number;
  popularProducts?: ProductStats[];
  waiterStats?: WaiterStats[];
}

interface ProductStats {
  productId: string;
  productName: string;
  totalQuantity: number;
  totalOrders: number;
  category?: string;
}

interface WaiterStats {
  waiterId: string;
  waiterName: string;
  totalOrders: number;
  totalSales: number;
  averageOrderValue: number;
  completedOrders: number;
  pendingOrders: number;
}

interface Order {
  _id: string;
  orderNumber: string;
  items: any[];
  totalAmount?: number;
  totalPrice?: number;
  totals?: {
    subtotal: number;
    tax: number;
    discount: number;
    total: number;
  };
  status: 'pending' | 'preparing' | 'ready' | 'completed' | 'delivered';
  tableNumber?: number;
  customerName?: string;
  waiterName?: string;
  waiterId?: string;
  chefName?: string;
  staff?: {
    waiter?: string;
    chef?: string;
  };
  createdAt: string;
  updatedAt: string;
}

interface ReportsManagerScreenProps {
  stats: DashboardStats;
  orders: Order[];
  loading: boolean;
}

const ReportsManagerScreen: React.FC<ReportsManagerScreenProps> = ({
  stats,
  orders,
  loading
}) => {
  const [reportsScreenDateFilter, setReportsScreenDateFilter] = useState<'today' | 'week' | 'month' | 'all'>('today');
  const [reportsScreenDetailedStats, setReportsScreenDetailedStats] = useState<any>(null);
  const [reportsScreenLoadingDetailed, setReportsScreenLoadingDetailed] = useState(false);

  useEffect(() => {
    loadDetailedReports();
  }, [reportsScreenDateFilter]);

  // تطبيق القيم الديناميكية على العناصر
  useEffect(() => {
    // تطبيق عرض أعمدة الويتر
    const waiterBars = document.querySelectorAll('.reports-manager-screen-waiter-bar[data-width]');
    waiterBars.forEach((bar) => {
      const width = bar.getAttribute('data-width');
      if (width) {
        (bar as HTMLElement).style.width = `${width}%`;
      }
    });

    // تطبيق ارتفاع الأعمدة الساعية
    const hourFills = document.querySelectorAll('.reports-manager-screen-hour-fill[data-height]');
    hourFills.forEach((fill) => {
      const height = fill.getAttribute('data-height');
      if (height) {
        (fill as HTMLElement).style.height = `${height}%`;
      }
    });
  }, [reportsScreenDetailedStats]);

  const loadDetailedReports = async () => {
    setReportsScreenLoadingDetailed(true);
    try {
      const response = await authenticatedGet(`/api/reports/detailed?period=${reportsScreenDateFilter}`);
      if (response.success) {
        setReportsScreenDetailedStats(response.data);
      }
    } catch (error) {
      console.error('Error loading detailed reports:', error);
    } finally {
      setReportsScreenLoadingDetailed(false);
    }
  };

  const getFilteredOrders = () => {
    const now = new Date();
    const startOfDay = new Date(now.getFullYear(), now.getMonth(), now.getDate());
    const startOfWeek = new Date(startOfDay);
    startOfWeek.setDate(startOfDay.getDate() - startOfDay.getDay());
    const startOfMonth = new Date(now.getFullYear(), now.getMonth(), 1);

    return orders.filter(order => {
      const orderDate = new Date(order.createdAt);
      switch (reportsScreenDateFilter) {
        case 'today':
          return orderDate >= startOfDay;
        case 'week':
          return orderDate >= startOfWeek;
        case 'month':
          return orderDate >= startOfMonth;
        case 'all':
        default:
          return true;
      }
    });
  };

  const filteredOrders = getFilteredOrders();
  const totalRevenue = filteredOrders.reduce((sum, order) => {
    const amount = order.totals?.total || order.totalPrice || order.totalAmount || 0;
    return sum + amount;
  }, 0);

  const avgOrderValue = filteredOrders.length > 0 ? totalRevenue / filteredOrders.length : 0;

  const getOrdersByStatus = (status: string) => {
    return filteredOrders.filter(order => order.status === status).length;
  };

  const getTopProducts = () => {
    const productStats: { [key: string]: { name: string; quantity: number; orders: number } } = {};
    
    filteredOrders.forEach(order => {
      order.items?.forEach((item: any) => {
        const productName = item.name || item.productName || 'منتج غير محدد';
        if (!productStats[productName]) {
          productStats[productName] = { name: productName, quantity: 0, orders: 0 };
        }
        productStats[productName].quantity += item.quantity || 1;
        productStats[productName].orders += 1;
      });
    });

    return Object.values(productStats)
      .sort((a, b) => b.quantity - a.quantity)
      .slice(0, 5);
  };

  const getWaiterPerformance = () => {
    const waiterStats: { [key: string]: { name: string; orders: number; sales: number } } = {};
    
    filteredOrders.forEach(order => {
      const waiterName = order.waiterName || order.staff?.waiter || 'غير محدد';
      if (!waiterStats[waiterName]) {
        waiterStats[waiterName] = { name: waiterName, orders: 0, sales: 0 };
      }
      waiterStats[waiterName].orders += 1;
      const amount = order.totals?.total || order.totalPrice || order.totalAmount || 0;
      waiterStats[waiterName].sales += amount;
    });

    return Object.values(waiterStats)
      .sort((a, b) => b.sales - a.sales)
      .slice(0, 5);
  };

  const getHourlyStats = () => {
    const hourlyData: { [key: number]: number } = {};
    
    for (let i = 0; i < 24; i++) {
      hourlyData[i] = 0;
    }
    
    filteredOrders.forEach(order => {
      const hour = new Date(order.createdAt).getHours();
      hourlyData[hour] += 1;
    });

    return Object.entries(hourlyData).map(([hour, count]) => ({
      hour: parseInt(hour),
      count
    }));
  };

  const getDateFilterLabel = () => {
    switch (reportsScreenDateFilter) {
      case 'today': return 'اليوم';
      case 'week': return 'هذا الأسبوع';
      case 'month': return 'هذا الشهر';
      case 'all': return 'جميع الفترات';
      default: return 'اليوم';
    }
  };

  const topProducts = getTopProducts();
  const waiterPerformance = getWaiterPerformance();
  const hourlyStats = getHourlyStats();

  return (
    <div className="reports-manager-screen">
      <div className="reports-manager-screen-header">
        <div className="reports-manager-screen-title">
          <h2>التقارير والإحصائيات</h2>
          <p>تحليل مفصل لأداء المقهى والمبيعات</p>
        </div>
        
        <div className="reports-manager-screen-filters">
          <select
            value={reportsScreenDateFilter}
            onChange={(e) => setReportsScreenDateFilter(e.target.value as any)}
            className="reports-manager-screen-period-filter"
            title="اختر فترة التقرير"
          >
            <option value="today">اليوم</option>
            <option value="week">هذا الأسبوع</option>
            <option value="month">هذا الشهر</option>
            <option value="all">جميع الفترات</option>
          </select>
        </div>
      </div>

      {/* Overview Statistics */}
      <div className="reports-manager-screen-overview">
        <h3>نظرة عامة - {getDateFilterLabel()}</h3>
        <div className="reports-manager-screen-overview-grid">
          <div className="reports-manager-screen-overview-card revenue">
            <div className="reports-manager-screen-overview-icon">
              <i className="fas fa-money-bill-wave"></i>
            </div>
            <div className="reports-manager-screen-overview-content">
              <h4>{totalRevenue.toFixed(2)} ج.م</h4>
              <p>إجمالي الإيرادات</p>
            </div>
          </div>
          
          <div className="reports-manager-screen-overview-card orders">
            <div className="reports-manager-screen-overview-icon">
              <i className="fas fa-shopping-cart"></i>
            </div>
            <div className="reports-manager-screen-overview-content">
              <h4>{filteredOrders.length}</h4>
              <p>إجمالي الطلبات</p>
            </div>
          </div>
          
          <div className="reports-manager-screen-overview-card average">
            <div className="reports-manager-screen-overview-icon">
              <i className="fas fa-chart-line"></i>
            </div>
            <div className="reports-manager-screen-overview-content">
              <h4>{avgOrderValue.toFixed(2)} ج.م</h4>
              <p>متوسط قيمة الطلب</p>
            </div>
          </div>
          
          <div className="reports-manager-screen-overview-card completed">
            <div className="reports-manager-screen-overview-icon">
              <i className="fas fa-check-circle"></i>
            </div>
            <div className="reports-manager-screen-overview-content">
              <h4>{getOrdersByStatus('completed')}</h4>
              <p>الطلبات المكتملة</p>
            </div>
          </div>
        </div>
      </div>

      {/* Order Status Breakdown */}
      <div className="reports-manager-screen-order-status">
        <h3>توزيع حالات الطلبات</h3>
        <div className="reports-manager-screen-status-grid">
          <div className="reports-manager-screen-status-card pending">
            <div className="reports-manager-screen-status-icon">
              <i className="fas fa-clock"></i>
            </div>
            <div className="reports-manager-screen-status-content">
              <h4>{getOrdersByStatus('pending')}</h4>
              <p>في الانتظار</p>
            </div>
          </div>
          
          <div className="reports-manager-screen-status-card preparing">
            <div className="reports-manager-screen-status-icon">
              <i className="fas fa-utensils"></i>
            </div>
            <div className="reports-manager-screen-status-content">
              <h4>{getOrdersByStatus('preparing')}</h4>
              <p>قيد التحضير</p>
            </div>
          </div>
          
          <div className="reports-manager-screen-status-card ready">
            <div className="reports-manager-screen-status-icon">
              <i className="fas fa-bell"></i>
            </div>
            <div className="reports-manager-screen-status-content">
              <h4>{getOrdersByStatus('ready')}</h4>
              <p>جاهز</p>
            </div>
          </div>
          
          <div className="reports-manager-screen-status-card completed">
            <div className="reports-manager-screen-status-icon">
              <i className="fas fa-check"></i>
            </div>
            <div className="reports-manager-screen-status-content">
              <h4>{getOrdersByStatus('completed')}</h4>
              <p>مكتمل</p>
            </div>
          </div>
        </div>
      </div>

      {/* Top Products */}
      <div className="reports-manager-screen-top-products">
        <h3>أكثر المنتجات مبيعاً</h3>
        <div className="reports-manager-screen-products-list">
          {topProducts.map((product, index) => (
            <div key={product.name} className="reports-manager-screen-product-item">
              <div className="reports-manager-screen-product-rank">#{index + 1}</div>
              <div className="reports-manager-screen-product-info">
                <h4>{product.name}</h4>
                <p>{product.quantity} قطعة - {product.orders} طلب</p>
              </div>
              <div className="reports-manager-screen-product-chart">
                <div 
                  className="reports-manager-screen-product-bar"
                  data-width={`${(product.quantity / (topProducts[0]?.quantity || 1)) * 100}%`}
                ></div>
              </div>
            </div>
          ))}
        </div>
      </div>

      {/* Waiter Performance */}
      <div className="reports-manager-screen-waiter-performance">
        <h3>أداء النُدُل</h3>
        <div className="reports-manager-screen-waiters-list">
          {waiterPerformance.map((waiter, index) => (
            <div key={waiter.name} className="reports-manager-screen-waiter-item">
              <div className="reports-manager-screen-waiter-rank">#{index + 1}</div>
              <div className="reports-manager-screen-waiter-info">
                <h4>{waiter.name}</h4>
                <p>{waiter.orders} طلب - {waiter.sales.toFixed(2)} ج.م</p>
              </div>
              <div className="reports-manager-screen-waiter-chart">
                <div 
                  className="reports-manager-screen-waiter-bar"
                  data-width={`${(waiter.sales / (waiterPerformance[0]?.sales || 1)) * 100}`}
                ></div>
              </div>
            </div>
          ))}
        </div>
      </div>

      {/* Hourly Distribution */}
      <div className="reports-manager-screen-hourly-stats">
        <h3>توزيع الطلبات على مدار اليوم</h3>
        <div className="reports-manager-screen-hourly-chart">
          {hourlyStats.map(({ hour, count }) => (
            <div key={hour} className="reports-manager-screen-hour-bar">
              <div 
                className="reports-manager-screen-hour-fill"
                data-height={`${count > 0 ? (count / Math.max(...hourlyStats.map(h => h.count)) * 100) : 0}`}
              ></div>
              <span className="reports-manager-screen-hour-label">{hour}:00</span>
            </div>
          ))}
        </div>
      </div>

      {loading && (
        <div className="reports-manager-screen-loading">
          <i className="fas fa-spinner fa-spin"></i>
          <p>جاري تحميل التقارير...</p>
        </div>
      )}
    </div>
  );
};

export default ReportsManagerScreen;

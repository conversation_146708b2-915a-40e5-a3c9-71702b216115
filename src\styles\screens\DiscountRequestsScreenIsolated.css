/* Discount Requests Screen Isolated Styles */
@import '../variables/discount-variables.css';

/* تخطيط طلبات الخصم */
.discount-requests-screen {
  padding: var(--discount-spacing-lg);
  max-width: 1400px;
  margin: 0 auto;
  overflow-x: hidden;
}

/* رأس الشاشة */
.discount-requests-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: var(--discount-spacing-lg);
  flex-wrap: wrap;
  gap: var(--discount-spacing-md);
}

.discount-requests-title {
  font-size: var(--discount-font-size-xl);
  font-weight: 700;
  color: var(--discount-text-primary);
  margin: 0;
}

/* أدوات التحكم في طلبات الخصم */
.discount-requests-controls {
  display: flex;
  gap: var(--discount-spacing-md);
  align-items: center;
  flex-wrap: wrap;
}

.discount-requests-search {
  padding: var(--discount-spacing-sm) var(--discount-spacing-md);
  border: 1px solid var(--discount-border-color);
  border-radius: var(--discount-border-radius);
  font-size: var(--discount-font-size-sm);
  background: var(--discount-bg-primary);
  color: var(--discount-text-primary);
  min-width: 200px;
}

.discount-requests-filter {
  padding: var(--discount-spacing-sm) var(--discount-spacing-md);
  border: 1px solid var(--discount-border-color);
  border-radius: var(--discount-border-radius);
  font-size: var(--discount-font-size-sm);
  background: var(--discount-bg-primary);
  color: var(--discount-text-primary);
  min-width: 120px;
}

/* الإحصائيات السريعة */
.discount-requests-stats {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: var(--discount-spacing-md);
  margin-bottom: var(--discount-spacing-lg);
}

.discount-stat-card {
  background: var(--discount-bg-primary);
  border: 1px solid var(--discount-border-color);
  border-radius: var(--discount-border-radius);
  padding: var(--discount-spacing-md);
  text-align: center;
}

.discount-stat-number {
  font-size: var(--discount-font-size-xl);
  font-weight: 700;
  margin-bottom: var(--discount-spacing-xs);
}

.discount-stat-number.pending {
  color: var(--discount-warning-color);
}

.discount-stat-number.approved {
  color: var(--discount-success-color);
}

.discount-stat-number.rejected {
  color: var(--discount-error-color);
}

.discount-stat-number.total {
  color: var(--discount-primary-color);
}

.discount-stat-label {
  font-size: var(--discount-font-size-sm);
  color: var(--discount-text-secondary);
}

/* بطاقات طلبات الخصم */
.discount-requests-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(320px, 1fr));
  gap: var(--discount-spacing-lg);
  margin-top: var(--discount-spacing-lg);
}

.discount-request-card {
  background: var(--discount-bg-primary);
  border: 1px solid var(--discount-border-color);
  border-radius: var(--discount-border-radius-lg);
  padding: var(--discount-spacing-lg);
  position: relative;
  transition: all 0.3s ease;
  box-shadow: var(--discount-shadow-sm);
  overflow: visible;
  word-break: break-word;
}

.discount-request-card:hover {
  transform: translateY(-2px);
  box-shadow: var(--discount-shadow-md);
}

.discount-request-card.pending {
  border-left: 4px solid var(--discount-warning-color);
}

.discount-request-card.approved {
  border-left: 4px solid var(--discount-success-color);
}

.discount-request-card.rejected {
  border-left: 4px solid var(--discount-error-color);
}

/* رأس بطاقة طلب الخصم */
.discount-request-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: var(--discount-spacing-md);
}

.discount-request-id {
  font-size: var(--discount-font-size-md);
  font-weight: 600;
  color: var(--discount-text-primary);
}

.discount-request-status {
  padding: 4px 8px;
  border-radius: 12px;
  font-size: var(--discount-font-size-xs);
  font-weight: 500;
  text-transform: uppercase;
}

.discount-request-status.pending {
  background: var(--discount-warning-light);
  color: var(--discount-warning-color);
}

.discount-request-status.approved {
  background: var(--discount-success-light);
  color: var(--discount-success-color);
}

.discount-request-status.rejected {
  background: var(--discount-error-light);
  color: var(--discount-error-color);
}

/* معلومات طلب الخصم */
.discount-request-info {
  margin-bottom: var(--discount-spacing-md);
}

.discount-info-item {
  display: flex;
  justify-content: space-between;
  margin-bottom: var(--discount-spacing-xs);
  font-size: var(--discount-font-size-sm);
}

.discount-info-label {
  color: var(--discount-text-secondary);
  font-weight: 500;
}

.discount-info-value {
  color: var(--discount-text-primary);
  font-weight: 600;
}

.discount-amount {
  color: var(--discount-primary-color);
  font-weight: 700;
  font-size: var(--discount-font-size-md);
}

/* تفاصيل الطلب */
.discount-order-details {
  background: var(--discount-bg-secondary);
  border-radius: var(--discount-border-radius);
  padding: var(--discount-spacing-md);
  margin-bottom: var(--discount-spacing-md);
}

.discount-order-title {
  font-size: var(--discount-font-size-sm);
  font-weight: 600;
  color: var(--discount-text-primary);
  margin-bottom: var(--discount-spacing-sm);
}

.discount-order-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: var(--discount-spacing-xs) 0;
  border-bottom: 1px solid var(--discount-border-light);
  font-size: var(--discount-font-size-xs);
}

.discount-order-item:last-child {
  border-bottom: none;
}

.discount-item-name {
  color: var(--discount-text-primary);
  flex: 1;
}

.discount-item-quantity {
  color: var(--discount-text-secondary);
  margin: 0 var(--discount-spacing-sm);
}

.discount-item-price {
  color: var(--discount-primary-color);
  font-weight: 500;
}

/* سبب طلب الخصم */
.discount-reason {
  background: var(--discount-info-light);
  border: 1px solid var(--discount-info-color);
  border-radius: var(--discount-border-radius);
  padding: var(--discount-spacing-sm);
  margin-bottom: var(--discount-spacing-md);
}

.discount-reason-title {
  font-size: var(--discount-font-size-xs);
  font-weight: 600;
  color: var(--discount-info-color);
  margin-bottom: var(--discount-spacing-xs);
}

.discount-reason-text {
  font-size: var(--discount-font-size-sm);
  color: var(--discount-text-primary);
  line-height: 1.4;
}

/* أزرار التحكم في طلب الخصم */
.discount-request-actions {
  display: flex;
  gap: var(--discount-spacing-sm);
  margin-top: var(--discount-spacing-md);
}

.discount-action-btn {
  padding: var(--discount-spacing-xs) var(--discount-spacing-sm);
  border: none;
  border-radius: var(--discount-border-radius);
  cursor: pointer;
  font-size: var(--discount-font-size-xs);
  font-weight: 500;
  transition: all 0.3s ease;
  flex: 1;
}

.discount-action-btn.approve {
  background: var(--discount-success-light);
  color: var(--discount-success-color);
}

.discount-action-btn.approve:hover {
  background: var(--discount-success-color);
  color: white;
}

.discount-action-btn.reject {
  background: var(--discount-error-light);
  color: var(--discount-error-color);
}

.discount-action-btn.reject:hover {
  background: var(--discount-error-color);
  color: white;
}

.discount-action-btn.view {
  background: var(--discount-info-light);
  color: var(--discount-info-color);
}

.discount-action-btn.view:hover {
  background: var(--discount-info-color);
  color: white;
}

.discount-action-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

/* حالة التحميل */
.discount-requests-loading {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 300px;
  font-size: var(--discount-font-size-lg);
  color: var(--discount-text-secondary);
}

/* رسالة عدم وجود طلبات */
.no-discount-requests {
  text-align: center;
  padding: var(--discount-spacing-xl);
  color: var(--discount-text-secondary);
}

.no-discount-requests h3 {
  margin-bottom: var(--discount-spacing-md);
  color: var(--discount-text-primary);
}

/* أولوية الطلب */
.discount-priority {
  position: absolute;
  top: var(--discount-spacing-sm);
  right: var(--discount-spacing-sm);
  width: 8px;
  height: 8px;
  border-radius: 50%;
}

.discount-priority.high {
  background: var(--discount-error-color);
  animation: pulse 2s infinite;
}

.discount-priority.medium {
  background: var(--discount-warning-color);
}

.discount-priority.low {
  background: var(--discount-success-color);
}

/* تنسيق أفضل للمسافات */
.discount-request-card {
  position: relative;
  overflow: visible;
  word-break: break-word;
}

.discount-enhanced-layout {
  display: flex;
  flex-direction: column;
  gap: var(--discount-spacing-md);
}

.discount-summary-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: var(--discount-spacing-sm);
  background: var(--discount-bg-secondary);
  border-radius: var(--discount-border-radius);
}

.discount-order-number {
  font-weight: 600;
  color: var(--discount-primary-color);
}

.discount-waiter-name {
  font-size: var(--discount-font-size-sm);
  color: var(--discount-text-secondary);
}

/* تجاوب الشاشة */
@media (max-width: 768px) {
  .discount-requests-screen {
    padding: var(--discount-spacing-md);
  }
  
  .discount-requests-header {
    flex-direction: column;
    align-items: stretch;
  }
  
  .discount-requests-controls {
    flex-direction: column;
  }
  
  .discount-requests-search,
  .discount-requests-filter {
    width: 100%;
  }
  
  .discount-requests-grid {
    grid-template-columns: 1fr;
    gap: var(--discount-spacing-md);
  }
  
  .discount-requests-stats {
    grid-template-columns: repeat(2, 1fr);
  }
}

@media (max-width: 480px) {
  .discount-request-actions {
    flex-direction: column;
  }
  
  .discount-action-btn {
    flex: none;
  }
  
  .discount-info-item {
    flex-direction: column;
    gap: var(--discount-spacing-xs);
  }
  
  .discount-requests-stats {
    grid-template-columns: 1fr;
  }
  
  .discount-summary-row {
    flex-direction: column;
    gap: var(--discount-spacing-xs);
    text-align: center;
  }
}




/* ====================================
   CSS منفصل للشاشة الرئيسية
   Home Screen - Isolated CSS
   ==================================== */

/* استيراد المتغيرات المميزة للشاشة الرئيسية */
@import '../variables/home-variables.css';

/* تم إزالة المتغيرات المحلية - سيتم استخدام المتغيرات من ملف home-variables.css فقط */

/* Home Screen Container - معزول تماماً */
.home-screen-container {
  width: 100%;
  min-height: calc(100vh - 80px);
  background: var(--home-light);
  padding: 0;
  margin: 0;
}

/* Header Section للشاشة الرئيسية */
.home-screen-header {
  background: linear-gradient(135deg, var(--home-primary) 0%, var(--home-secondary) 100%);
  padding: 2rem;
  border-radius: 0 0 var(--home-border-radius) var(--home-border-radius);
  color: var(--home-white);
  margin-bottom: 2rem;
  box-shadow: var(--home-card-shadow);
}

.home-screen-title {
  font-size: 2.5rem;
  font-weight: 700;
  margin: 0 0 0.5rem 0;
  display: flex;
  align-items: center;
  gap: 1rem;
}

.home-screen-title-icon {
  color: var(--home-warning);
  text-shadow: 0 0 20px rgba(243, 156, 18, 0.5);
}

.home-screen-subtitle {
  font-size: 1.2rem;
  opacity: 0.9;
  margin: 0;
}

/* Control Buttons للشاشة الرئيسية */
.home-screen-controls {
  display: flex;
  align-items: center;
  gap: 1rem;
  margin-top: 1rem;
  flex-wrap: wrap;
}

.home-screen-refresh-btn {
  background: var(--home-white);
  color: var(--home-primary);
  border: none;
  padding: 0.75rem 1.5rem;
  border-radius: var(--home-border-radius);
  font-weight: 600;
  cursor: pointer;
  transition: var(--home-transition);
  display: flex;
  align-items: center;
  gap: 0.5rem;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.home-screen-refresh-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.2);
}

.home-screen-refresh-btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  transform: none;
}

.home-screen-refresh-btn.loading i {
  animation: home-screen-spin 1s linear infinite;
}

@keyframes home-screen-spin {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

/* Connection Status للشاشة الرئيسية */
.home-screen-connection-status {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.5rem 1rem;
  border-radius: var(--home-border-radius);
  font-weight: 500;
  font-size: 0.9rem;
}

.home-screen-connection-status.online {
  background: rgba(39, 174, 96, 0.2);
  color: var(--home-success);
  border: 1px solid var(--home-success);
}

.home-screen-connection-status.offline {
  background: rgba(231, 76, 60, 0.2);
  color: var(--home-danger);
  border: 1px solid var(--home-danger);
}

/* Stats Grid للشاشة الرئيسية */
.home-screen-stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 1.5rem;
  margin-bottom: 2rem;
  padding: 0 2rem;
}

.home-screen-stat-card {
  background: var(--home-white);
  padding: 1.5rem;
  border-radius: var(--home-border-radius);
  box-shadow: var(--home-card-shadow);
  transition: var(--home-transition);
  display: flex;
  align-items: center;
  gap: 1rem;
  border: 1px solid rgba(0, 0, 0, 0.05);
}

.home-screen-stat-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 8px 30px rgba(0, 0, 0, 0.15);
}

.home-screen-stat-icon {
  width: 60px;
  height: 60px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.5rem;
  color: var(--home-white);
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
}

.home-screen-stat-card.orders .home-screen-stat-icon {
  background: linear-gradient(135deg, var(--home-primary), var(--home-secondary));
}

.home-screen-stat-card.sales .home-screen-stat-icon {
  background: linear-gradient(135deg, var(--home-success), #20c997);
}

.home-screen-stat-card.employees .home-screen-stat-icon {
  background: linear-gradient(135deg, var(--home-warning), #fd7e14);
}

.home-screen-stat-card.tables .home-screen-stat-icon {
  background: linear-gradient(135deg, var(--home-info), #6f42c1);
}

.home-screen-stat-content h3 {
  font-size: 2rem;
  font-weight: 700;
  margin: 0;
  color: var(--home-primary);
}

.home-screen-stat-content p {
  font-size: 1rem;
  color: var(--home-dark);
  margin: 0.25rem 0 0 0;
  opacity: 0.8;
}

/* Orders Stats للشاشة الرئيسية */
.home-screen-orders-section {
  padding: 0 2rem 2rem 2rem;
}

.home-screen-orders-title {
  font-size: 1.8rem;
  color: var(--home-primary);
  margin-bottom: 1.5rem;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.home-screen-orders-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 1rem;
}

.home-screen-order-status-card {
  background: var(--home-white);
  padding: 1.25rem;
  border-radius: var(--home-border-radius);
  box-shadow: var(--home-card-shadow);
  transition: var(--home-transition);
  cursor: pointer;
  border: 1px solid rgba(0, 0, 0, 0.05);
}

.home-screen-order-status-card:hover {
  transform: translateY(-3px);
  box-shadow: 0 6px 25px rgba(0, 0, 0, 0.15);
}

.home-screen-order-status-card.pending {
  border-left: 4px solid var(--home-warning);
}

.home-screen-order-status-card.preparing {
  border-left: 4px solid var(--home-primary);
}

.home-screen-order-status-card.ready {
  border-left: 4px solid var(--home-success);
}

.home-screen-order-status-card.completed {
  border-left: 4px solid var(--home-info);
}

.home-screen-order-status-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 0.75rem;
}

.home-screen-order-status-icon {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--home-white);
  font-size: 1.1rem;
}

.home-screen-order-status-card.pending .home-screen-order-status-icon {
  background: var(--home-warning);
}

.home-screen-order-status-card.preparing .home-screen-order-status-icon {
  background: var(--home-primary);
}

.home-screen-order-status-card.ready .home-screen-order-status-icon {
  background: var(--home-success);
}

.home-screen-order-status-card.completed .home-screen-order-status-icon {
  background: var(--home-info);
}

.home-screen-order-count {
  font-size: 1.5rem;
  font-weight: 700;
  color: var(--home-primary);
}

.home-screen-order-label {
  font-size: 0.9rem;
  color: var(--home-dark);
  opacity: 0.8;
  margin: 0.25rem 0 0 0;
}

/* Responsive Design للشاشة الرئيسية */
@media (max-width: 1200px) {
  .home-screen-stats-grid {
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    padding: 0 1.5rem;
  }
  
  .home-screen-orders-section {
    padding: 0 1.5rem 2rem 1.5rem;
  }
}

@media (max-width: 768px) {
  .home-screen-header {
    padding: 1.5rem;
  }
  
  .home-screen-title {
    font-size: 2rem;
  }
  
  .home-screen-stats-grid {
    grid-template-columns: 1fr;
    padding: 0 1rem;
    gap: 1rem;
  }
  
  .home-screen-orders-section {
    padding: 0 1rem 2rem 1rem;
  }
  
  .home-screen-orders-grid {
    grid-template-columns: 1fr;
  }
  
  .home-screen-controls {
    flex-direction: column;
    align-items: stretch;
  }
  
  .home-screen-refresh-btn {
    justify-content: center;
  }
}

@media (max-width: 480px) {
  .home-screen-header {
    padding: 1rem;
  }
  
  .home-screen-title {
    font-size: 1.75rem;
    flex-direction: column;
    text-align: center;
    gap: 0.5rem;
  }
  
  .home-screen-stat-card {
    flex-direction: column;
    text-align: center;
    gap: 0.75rem;
  }
  
  .home-screen-orders-title {
    font-size: 1.5rem;
  }
}



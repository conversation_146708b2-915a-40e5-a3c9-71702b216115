import { useEffect, useState, useCallback } from 'react';
import { checkServerHealth } from '../utils/api';
import './ConnectionStatus.css';

interface ConnectionState {
  isConnected: boolean;
  serverStatus: string;
  databaseStatus: string;
  lastChecked: Date | null;
  error: string | null;
}

const ConnectionStatus = () => {
  const [state, setState] = useState<ConnectionState>({
    isConnected: false,
    serverStatus: 'جاري الفحص...',
    databaseStatus: 'جاري الفحص...',
    lastChecked: null,
    error: null
  });
  const [isChecking, setIsChecking] = useState(false);
  const [retryCount, setRetryCount] = useState(0);

  const checkConnection = useCallback(async () => {
    if (isChecking) {
      console.log('🔄 Health check already in progress, skipping...');
      return;
    }

    setIsChecking(true);
    console.log('🔍 Starting health check...');
    // console.log('🔍 API URL being used:', import.meta.env.VITE_API_URL); // تقليل الرسائل
    
    try {
      const response = await checkServerHealth();
      // console.log('🔍 Health check response:', response); // تقليل الرسائل

      // التحقق الأساسي من وجود الاستجابة
      if (!response) {
        throw new Error('لا توجد استجابة من الخادم');
      }

      const healthData = response.data;
      // console.log('📊 Health data:', healthData); // تقليل الرسائل
      // console.log('📊 Response success:', response.success); // تقليل الرسائل
      
      // التحقق من حالة الاتصال مع معالجة أفضل للـ null/undefined
      const isServerHealthy = Boolean(response.success === true && healthData?.status === 'healthy');
      const isDatabaseConnected = Boolean(response.success === true && healthData?.database?.connected === true);

      // عرض رسائل النجاح فقط في حالة التشخيص أو المشاكل
      if (!isServerHealthy || !isDatabaseConnected) {
        console.log('✅ Server healthy:', isServerHealthy);
        console.log('✅ Database connected:', isDatabaseConnected);
        console.log('✅ Overall connection:', isServerHealthy && isDatabaseConnected);
      }
      
      // تحديث الحالة
      const newState: ConnectionState = {
        isConnected: isServerHealthy && isDatabaseConnected,
        serverStatus: isServerHealthy ? 'متصل' : 'غير متصل',
        databaseStatus: isDatabaseConnected ? 'متصل' : 'غير متصل',
        lastChecked: new Date(),
        error: null
      };

      // console.log('🔄 Updating state to:', newState); // تقليل الرسائل
      setState(newState);
      setRetryCount(0);
      
    } catch (error: any) {
      console.error('❌ Connection check failed:', error);
      const errorState: ConnectionState = {
        isConnected: false,
        serverStatus: 'غير متصل',
        databaseStatus: 'غير متصل',
        lastChecked: new Date(),
        error: error.message || 'فشل في الاتصال بالخادم'
      };
      console.log('🔄 Updating state to (error):', errorState);
      setState(errorState);
      
      // زيادة عداد المحاولات
      setRetryCount(prev => prev + 1);
    } finally {
      setIsChecking(false);
    }
  }, [isChecking]);

  // تشغيل الفحص الأولي
  useEffect(() => {
    console.log('🚀 ConnectionStatus component mounted, starting initial check...');
    const timer = setTimeout(() => {
      checkConnection();
    }, 1000); // انتظار ثانية واحدة للتأكد من جاهزية التطبيق

    return () => clearTimeout(timer);
  }, []); // يتم تشغيله مرة واحدة فقط عند mount

  // إعادة المحاولة عند الفشل
  useEffect(() => {
    if (retryCount > 0 && retryCount < 3 && !state.isConnected && !isChecking) {
      console.log(`🔄 Retry attempt ${retryCount}/3 in 5 seconds...`);
      const timer = setTimeout(() => {
        checkConnection();
      }, 5000);

      return () => clearTimeout(timer);
    }
  }, [retryCount, state.isConnected, isChecking, checkConnection]);

  // فحص دوري كل 30 ثانية
  useEffect(() => {
    const intervalId = setInterval(() => {
      // console.log('🔄 Periodic health check...'); // تقليل الرسائل - فحص صامت
      if (!isChecking) {
        checkConnection();
      }
    }, 30000);

    return () => clearInterval(intervalId);
  }, [checkConnection, isChecking]);

  // عرض حالة الاتصال فقط عند وجود مشاكل (غير متصل)
  // إذا كان الاتصال سليم، لا نعرض شيئاً لتقليل التشويش البصري
  if (state.isConnected) {
    return null; // إخفاء العنصر بالكامل عند الاتصال السليم
  }

  return (
    <div className={`connection-status ${state.isConnected ? 'connected' : 'error'}`}>
      <div className="connection-status-header">
        <span className={`status-indicator ${state.isConnected ? 'success' : 'error'}`}></span>
        <strong>
          {state.isConnected ? 'متصل بالخادم' : 'غير متصل بالخادم'}
        </strong>
      </div>
      <div className="connection-details">
        <span>الخادم: {state.serverStatus}</span><br/>
        <span>قاعدة البيانات: {state.databaseStatus}</span>
        {state.error && <div className="error-message">{state.error}</div>}
      </div>
      {!state.isConnected && (
        <div className="connection-actions">
          <button onClick={() => setIsChecking(true)} disabled={isChecking} className="retry-button">
            {isChecking ? 'جاري إعادة المحاولة...' : 'إعادة المحاولة'}
          </button>
        </div>
      )}
      <div className="last-checked">
        <small>
          {state.lastChecked
            ? `آخر فحص: ${state.lastChecked.toLocaleTimeString()}`
            : 'لم يتم الفحص بعد'}
        </small>
      </div>
    </div>
  );
};

export default ConnectionStatus;
/* ??????? ????????? ??????? */
@import '../variables/tables-variables.css';

/* Enhanced Table Card Styles */

.table-card-enhanced {
  background: linear-gradient(145deg, #ffffff 0%, #f8f9fa 100%);
  border-radius: 16px;
  padding: 1.5rem;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  transition: all 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
  border: none;
  position: relative;
  overflow: hidden;
  cursor: pointer;
  min-height: 200px;
}

.table-card-enhanced::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.1), rgba(255, 255, 255, 0.05));
  pointer-events: none;
  transition: opacity 0.3s ease;
}

.table-card-enhanced:hover {
  transform: translateY(-8px) scale(1.02);
  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.15);
}

.table-card-enhanced:hover::before {
  opacity: 0.8;
}

/* Status Bar */
.table-status-bar {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  border-radius: 16px 16px 0 0;
}

.table-status-bar.available {
  background: linear-gradient(90deg, #27ae60, #2ecc71);
}

.table-status-bar.occupied {
  background: linear-gradient(90deg, #e74c3c, #c0392b);
}

.table-status-bar.reserved {
  background: linear-gradient(90deg, #f39c12, #e67e22);
}

/* Header */
.table-card-header {
  margin-top: 0.5rem;
}

.table-icon-wrapper {
  width: 60px;
  height: 60px;
  margin: 0 auto;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, rgba(52, 152, 219, 0.1), rgba(52, 152, 219, 0.05));
  transition: all 0.3s ease;
}

.table-card-enhanced:hover .table-icon-wrapper {
  transform: scale(1.1);
  background: linear-gradient(135deg, rgba(52, 152, 219, 0.2), rgba(52, 152, 219, 0.1));
}

.table-icon {
  font-size: 1.8rem;
  transition: all 0.3s ease;
}

.table-icon.text-success {
  color: #27ae60 !important;
}

.table-icon.text-danger {
  color: #e74c3c !important;
}

.table-icon.text-warning {
  color: #f39c12 !important;
}

.table-number {
  font-size: 1.4rem;
  font-weight: 700;
  color: #2c3e50;
  margin: 0;
}

/* Status Badge */
.table-status-wrapper {
  margin-top: 0.5rem;
}

.table-status-badge {
  display: inline-flex;
  align-items: center;
  padding: 0.5rem 1rem;
  border-radius: 20px;
  font-weight: 600;
  font-size: 0.85rem;
  transition: all 0.3s ease;
}

.table-status-badge.available {
  background: linear-gradient(135deg, #d4edda, #c3e6cb);
  color: #155724;
  border: 1px solid #c3e6cb;
}

.table-status-badge.occupied {
  background: linear-gradient(135deg, #f8d7da, #f5c6cb);
  color: #721c24;
  border: 1px solid #f5c6cb;
}

.table-status-badge.reserved {
  background: linear-gradient(135deg, #fff3cd, #ffeaa7);
  color: #856404;
  border: 1px solid #ffeaa7;
}

/* Details Section */
.table-details-section {
  margin-top: 1rem;
  padding-top: 1rem;
  border-top: 1px solid #e9ecef;
}

.table-detail-item {
  display: flex;
  align-items: center;
  margin-bottom: 0.75rem;
  padding: 0.5rem;
  background: rgba(248, 249, 250, 0.5);
  border-radius: 8px;
  transition: all 0.3s ease;
}

.table-detail-item:last-child {
  margin-bottom: 0;
}

.table-detail-item:hover {
  background: rgba(248, 249, 250, 0.8);
  transform: translateX(4px);
}

.detail-icon {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-left: 0.75rem;
  font-size: 0.9rem;
  color: white;
  flex-shrink: 0;
}

.detail-icon.customer {
  background: linear-gradient(135deg, #3498db, #2980b9);
}

.detail-icon.money {
  background: linear-gradient(135deg, #27ae60, #229954);
}

.detail-icon.time {
  background: linear-gradient(135deg, #f39c12, #e67e22);
}

.detail-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
}

.detail-label {
  font-size: 0.75rem;
  color: #6c757d;
  font-weight: 500;
}

.detail-value {
  font-size: 0.9rem;
  color: #2c3e50;
  font-weight: 600;
}

.detail-value.amount {
  color: #27ae60;
  font-weight: 700;
}

/* Animations */
@keyframes tableCardPulse {
  0%, 100% {
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  }
  50% {
    box-shadow: 0 8px 32px rgba(52, 152, 219, 0.2);
  }
}

.table-card-enhanced.occupied {
  animation: tableCardPulse 3s infinite;
}

/* Responsive Design */
@media (max-width: 768px) {
  .table-card-enhanced {
    padding: 1rem;
    min-height: 180px;
  }
  
  .table-icon-wrapper {
    width: 50px;
    height: 50px;
  }
  
  .table-icon {
    font-size: 1.5rem;
  }
  
  .table-number {
    font-size: 1.2rem;
  }
  
  .table-status-badge {
    padding: 0.4rem 0.8rem;
    font-size: 0.8rem;
  }
  
  .detail-icon {
    width: 28px;
    height: 28px;
    font-size: 0.8rem;
  }
  
  .detail-content {
    gap: 0.2rem;
  }
  
  .detail-label {
    font-size: 0.7rem;
  }
  
  .detail-value {
    font-size: 0.85rem;
  }
}

@media (max-width: 480px) {
  .table-card-enhanced {
    padding: 0.75rem;
    min-height: 160px;
  }
  
  .table-icon-wrapper {
    width: 45px;
    height: 45px;
  }
  
  .table-icon {
    font-size: 1.3rem;
  }
  
  .table-number {
    font-size: 1.1rem;
  }
  
  .table-detail-item {
    padding: 0.4rem;
    margin-bottom: 0.5rem;
  }
  
  .detail-icon {
    width: 26px;
    height: 26px;
    font-size: 0.75rem;
    margin-left: 0.5rem;
  }
}


<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار Modal تفاصيل طلب الخصم</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
            direction: rtl;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .test-section {
            margin-bottom: 30px;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 8px;
            background: #f9f9f9;
        }
        .test-section h3 {
            margin-top: 0;
            color: #333;
            border-bottom: 2px solid #4CAF50;
            padding-bottom: 10px;
        }
        button {
            background: #4CAF50;
            color: white;
            border: none;
            padding: 10px 20px;
            margin: 5px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
        }
        button:hover {
            background: #45a049;
        }
        button.test {
            background: #2196F3;
        }
        button.test:hover {
            background: #1976D2;
        }
        .result {
            background: #f0f8ff;
            border: 1px solid #4CAF50;
            padding: 15px;
            margin-top: 10px;
            border-radius: 4px;
            white-space: pre-wrap;
            font-family: monospace;
            direction: ltr;
            text-align: left;
        }
        .error {
            background: #ffebee;
            border: 1px solid #f44336;
            color: #c62828;
        }
        .console-logs {
            background: #263238;
            color: #4fc3f7;
            max-height: 400px;
            overflow-y: auto;
            padding: 15px;
            border-radius: 6px;
            font-family: 'Courier New', monospace;
            font-size: 12px;
            margin-top: 15px;
        }
        .log-entry {
            margin-bottom: 5px;
            padding: 2px 0;
        }
        .log-time {
            color: #81c784;
        }
        .log-info {
            color: #4fc3f7;
        }
        .log-error {
            color: #f44336;
        }
        .log-success {
            color: #4caf50;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔧 اختبار Modal تفاصيل طلب الخصم</h1>
        
        <div class="test-section">
            <h3>🎯 اختبار الاتصال والبيانات</h3>
            <button onclick="testConnection()">🔗 اختبار الاتصال</button>
            <button onclick="testDiscountData()">📋 اختبار بيانات الخصم</button>
            <button onclick="testModalFunctions()">⚙️ اختبار دوال Modal</button>
            <button onclick="simulateModalOpen()">🪟 محاكاة فتح Modal</button>
            <div id="test-results"></div>
        </div>

        <div class="test-section">
            <h3>📊 حالة Console.log</h3>
            <button class="test" onclick="clearLogs()">🗑️ مسح اللوجات</button>
            <button class="test" onclick="toggleConsoleMonitor()">📺 تشغيل/إيقاف المراقبة</button>
            <div id="console-monitor" class="console-logs">
                <div class="log-entry log-info">🔧 جاري تشغيل مراقب Console...</div>
            </div>
        </div>

        <div class="test-section">
            <h3>🔍 فحص DOM</h3>
            <button onclick="checkForModal()">🔍 البحث عن Modal في DOM</button>
            <button onclick="checkReactDevTools()">⚛️ فحص React DevTools</button>
            <button onclick="checkElementsCount()">📊 عدد العناصر</button>
            <div id="dom-results"></div>
        </div>

        <div class="test-section">
            <h3>💡 التوصيات والحلول</h3>
            <div style="background: #fff3e0; padding: 15px; border-radius: 6px; border: 1px solid #ff9800;">
                <h4>🔧 خطوات استكشاف الأخطاء:</h4>
                <ol>
                    <li><strong>تحقق من Console:</strong> افتح F12 وراقب الأخطاء</li>
                    <li><strong>فحص Network:</strong> تأكد من وصول البيانات من API</li>
                    <li><strong>React State:</strong> راقب state المكونات في React DevTools</li>
                    <li><strong>CSS Visibility:</strong> تحقق من أن Modal ليس مخفي بـ CSS</li>
                    <li><strong>Z-index:</strong> تأكد من أن Modal أعلى من باقي العناصر</li>
                </ol>
            </div>
        </div>
    </div>

    <script>
        let consoleMonitorActive = true;
        let originalConsoleLog = console.log;
        let originalConsoleError = console.error;
        let originalConsoleWarn = console.warn;

        // اعتراض console.log
        function setupConsoleMonitor() {
            const monitor = document.getElementById('console-monitor');
            
            console.log = function(...args) {
                originalConsoleLog.apply(console, args);
                if (consoleMonitorActive) {
                    addLogEntry('info', args.join(' '));
                }
            };

            console.error = function(...args) {
                originalConsoleError.apply(console, args);
                if (consoleMonitorActive) {
                    addLogEntry('error', args.join(' '));
                }
            };

            console.warn = function(...args) {
                originalConsoleWarn.apply(console, args);
                if (consoleMonitorActive) {
                    addLogEntry('warn', args.join(' '));
                }
            };
        }

        function addLogEntry(type, message) {
            const monitor = document.getElementById('console-monitor');
            const time = new Date().toLocaleTimeString('ar-SA');
            const logClass = type === 'error' ? 'log-error' : (type === 'warn' ? 'log-warn' : 'log-info');
            
            const entry = document.createElement('div');
            entry.className = 'log-entry ' + logClass;
            entry.innerHTML = `<span class="log-time">[${time}]</span> ${message}`;
            
            monitor.appendChild(entry);
            monitor.scrollTop = monitor.scrollHeight;
            
            // إبقاء آخر 100 سجل فقط
            while (monitor.children.length > 100) {
                monitor.removeChild(monitor.firstChild);
            }
        }

        function clearLogs() {
            const monitor = document.getElementById('console-monitor');
            monitor.innerHTML = '<div class="log-entry log-info">🧹 تم مسح جميع اللوجات</div>';
        }

        function toggleConsoleMonitor() {
            consoleMonitorActive = !consoleMonitorActive;
            const status = consoleMonitorActive ? 'تم تشغيل' : 'تم إيقاف';
            addLogEntry('info', `📺 مراقب Console: ${status}`);
        }

        async function testConnection() {
            const resultDiv = document.getElementById('test-results');
            
            try {
                resultDiv.innerHTML = '<div class="result">⏳ جاري اختبار الاتصال...</div>';
                
                // اختبار الاتصال بالـ frontend
                const frontendTest = await fetch(window.location.origin);
                console.log('✅ Frontend متاح:', frontendTest.status);
                
                // اختبار الاتصال بالـ backend
                const backendTest = await fetch('http://localhost:3001/api/v1/health').catch(() => null);
                console.log('🔗 Backend status:', backendTest ? backendTest.status : 'غير متاح');
                
                resultDiv.innerHTML = `<div class="result">
✅ نتائج اختبار الاتصال:
Frontend: ${frontendTest.status === 200 ? 'متاح' : 'غير متاح'}
Backend: ${backendTest ? backendTest.status : 'غير متاح'}
URL الحالي: ${window.location.href}
                </div>`;
                
            } catch (error) {
                console.error('❌ خطأ في اختبار الاتصال:', error);
                resultDiv.innerHTML = `<div class="result error">❌ خطأ في الاتصال: ${error.message}</div>`;
            }
        }

        async function testDiscountData() {
            const resultDiv = document.getElementById('test-results');
            
            try {
                resultDiv.innerHTML = '<div class="result">⏳ جاري اختبار بيانات الخصم...</div>';
                
                // محاولة الوصول لبيانات React
                const reactRoot = document.getElementById('root');
                console.log('⚛️ React Root:', reactRoot);
                
                // البحث عن discount request cards
                const discountCards = document.querySelectorAll('.discount-request-card');
                console.log('📋 عدد كروت طلبات الخصم:', discountCards.length);
                
                // البحث عن أزرار التفاصيل
                const detailButtons = document.querySelectorAll('.details-btn');
                console.log('🔍 عدد أزرار التفاصيل:', detailButtons.length);
                
                resultDiv.innerHTML = `<div class="result">
📊 نتائج فحص بيانات الخصم:
React Root: ${reactRoot ? 'موجود' : 'غير موجود'}
كروت طلبات الخصم: ${discountCards.length}
أزرار التفاصيل: ${detailButtons.length}
                </div>`;
                
            } catch (error) {
                console.error('❌ خطأ في فحص البيانات:', error);
                resultDiv.innerHTML = `<div class="result error">❌ خطأ في فحص البيانات: ${error.message}</div>`;
            }
        }

        function testModalFunctions() {
            const resultDiv = document.getElementById('test-results');
            
            try {
                console.log('🔧 فحص دوال Modal...');
                
                // البحث عن modal في DOM
                const modalOverlay = document.querySelector('.modal-overlay');
                const modalBackdrop = document.querySelector('.modal-backdrop');
                const orderDetailsModal = document.querySelector('.order-details-modal');
                
                console.log('🔍 Modal elements:', {
                    modalOverlay: !!modalOverlay,
                    modalBackdrop: !!modalBackdrop,
                    orderDetailsModal: !!orderDetailsModal
                });
                
                // فحص CSS
                const modalCSS = Array.from(document.styleSheets).some(sheet => {
                    try {
                        return Array.from(sheet.cssRules).some(rule => 
                            rule.selectorText && rule.selectorText.includes('modal-overlay')
                        );
                    } catch (e) {
                        return false;
                    }
                });
                
                console.log('🎨 Modal CSS loaded:', modalCSS);
                
                resultDiv.innerHTML = `<div class="result">
🔧 نتائج فحص دوال Modal:
Modal Overlay في DOM: ${modalOverlay ? 'موجود' : 'غير موجود'}
Modal Backdrop في DOM: ${modalBackdrop ? 'موجود' : 'غير موجود'}
Order Details Modal: ${orderDetailsModal ? 'موجود' : 'غير موجود'}
Modal CSS محمل: ${modalCSS ? 'نعم' : 'لا'}
                </div>`;
                
            } catch (error) {
                console.error('❌ خطأ في فحص دوال Modal:', error);
                resultDiv.innerHTML = `<div class="result error">❌ خطأ في فحص دوال Modal: ${error.message}</div>`;
            }
        }

        function simulateModalOpen() {
            console.log('🚀 محاكاة فتح Modal...');
            
            // البحث عن زر تفاصيل وتجربة الضغط عليه
            const detailButtons = document.querySelectorAll('.details-btn');
            
            if (detailButtons.length > 0) {
                console.log(`🎯 وجد ${detailButtons.length} زر تفاصيل، سأجرب الضغط على الأول...`);
                detailButtons[0].click();
                
                // فحص بعد ثانية
                setTimeout(() => {
                    const modal = document.querySelector('.modal-overlay');
                    console.log('🔍 Modal بعد الضغط:', !!modal);
                    addLogEntry('info', `🎭 Modal ${modal ? 'ظهر' : 'لم يظهر'} بعد الضغط`);
                }, 1000);
            } else {
                console.warn('⚠️ لم يتم العثور على أزرار تفاصيل');
                addLogEntry('warn', '⚠️ لم يتم العثور على أزرار تفاصيل في الصفحة');
            }
        }

        function checkForModal() {
            const resultDiv = document.getElementById('dom-results');
            
            // فحص شامل للـ Modal
            const modalSelectors = [
                '.modal-overlay',
                '.modal-backdrop', 
                '.order-details-modal',
                '.modal-container',
                '.modal-header',
                '.modal-content'
            ];
            
            let results = '🔍 نتائج فحص Modal في DOM:\n\n';
            
            modalSelectors.forEach(selector => {
                const element = document.querySelector(selector);
                const count = document.querySelectorAll(selector).length;
                results += `${selector}: ${element ? '✅ موجود' : '❌ غير موجود'} (العدد: ${count})\n`;
                
                if (element) {
                    const styles = window.getComputedStyle(element);
                    results += `  - Display: ${styles.display}\n`;
                    results += `  - Visibility: ${styles.visibility}\n`;
                    results += `  - Z-index: ${styles.zIndex}\n`;
                    results += `  - Position: ${styles.position}\n\n`;
                }
            });
            
            console.log(results);
            resultDiv.innerHTML = `<div class="result">${results}</div>`;
        }

        function checkReactDevTools() {
            const resultDiv = document.getElementById('dom-results');
            
            // فحص React DevTools
            const hasReact = window.React !== undefined;
            const hasReactDOM = window.ReactDOM !== undefined;
            const reactVersion = hasReact ? React.version : 'غير متاح';
            
            console.log('⚛️ React info:', { hasReact, hasReactDOM, reactVersion });
            
            resultDiv.innerHTML = `<div class="result">
⚛️ معلومات React:
React متاح: ${hasReact ? 'نعم' : 'لا'}
ReactDOM متاح: ${hasReactDOM ? 'نعم' : 'لا'}
إصدار React: ${reactVersion}

💡 نصيحة: استخدم React Developer Tools لفحص state المكونات
            </div>`;
        }

        function checkElementsCount() {
            const resultDiv = document.getElementById('dom-results');
            
            const counts = {
                'إجمالي العناصر': document.querySelectorAll('*').length,
                'أزرار': document.querySelectorAll('button').length,
                'كروت الخصم': document.querySelectorAll('.discount-request-card').length,
                'أزرار التفاصيل': document.querySelectorAll('.details-btn').length,
                'Modal elements': document.querySelectorAll('[class*="modal"]').length
            };
            
            let results = '📊 إحصائيات العناصر:\n\n';
            for (const [key, value] of Object.entries(counts)) {
                results += `${key}: ${value}\n`;
            }
            
            console.log('📊 Elements count:', counts);
            resultDiv.innerHTML = `<div class="result">${results}</div>`;
        }

        // تشغيل مراقب Console عند التحميل
        setupConsoleMonitor();
        
        // رسالة ترحيب
        console.log('🔧 مرحباً! تم تحميل صفحة اختبار Modal تفاصيل طلب الخصم');
        console.log('💡 استخدم الأزرار أعلاه لاختبار مختلف جوانب النظام');
        
        // مراقبة تغيرات DOM
        const observer = new MutationObserver(function(mutations) {
            mutations.forEach(function(mutation) {
                if (mutation.type === 'childList') {
                    const addedModals = Array.from(mutation.addedNodes).filter(node => 
                        node.nodeType === 1 && (
                            node.classList.contains('modal-overlay') ||
                            node.querySelector && node.querySelector('.modal-overlay')
                        )
                    );
                    
                    if (addedModals.length > 0) {
                        console.log('🎭 تم إضافة Modal إلى DOM!', addedModals);
                        addLogEntry('success', '🎭 تم رصد إضافة Modal إلى DOM');
                    }
                }
            });
        });
        
        observer.observe(document.body, {
            childList: true,
            subtree: true
        });
    </script>
</body>
</html>
